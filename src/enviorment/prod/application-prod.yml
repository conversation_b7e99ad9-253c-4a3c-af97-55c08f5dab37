spring:
  kafka:
    properties:
      session:
        timeout:
          ms: 10000
      max:
        poll:
          interval:
            ms: 15000
    bootstrap-servers: 192.168.200.22:9092,192.168.200.23:9092,192.168.200.24:9092,192.168.200.25:9092,192.168.200.26:9092,192.168.200.27:9092,192.168.200.28:9092,192.168.200.29:9092,192.168.200.30:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      enable-auto-commit: false
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      auto-offset-reset: earliest
      max-poll-records: 50
    listener:
      type: batch
      concurrency: 1
      ack-mode: batch
    topic_id:
      "FaultDiagnosis"
    topic_id_tmr:
      "TrainMechanismRuleData"
    group_id:
      "Warning_SX2"
#    topic_id_cod:
#      "TracksideCheckData"
#    topic_id_coda:
#      "TracksideCheckAlarmData"
    external-bootstrap-servers: **************:9092,**************:9092,**************:9092
    topic_id_cod: "TracksideCheckData"
    topic_id_coda: "TracksideCheckAlarmData"
    group_id_nuoli: "Nuoli_Data"

  datasource:
    url: ******************************************************************
    username: phmdbadmin
    password: P9b8yHnM
    hikari:
      maximum-pool-size: 30
      minimum-idle: 10
  redis:
    host: **************
    port: 8000
    database: 0
    lettuce:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0
    timeout: 2000

cors:
  allowCredentials: false
  maxAge:
  allowedHeaders:
  allowedMethods:
  exposedHeaders:
    - "access-control-allow-headers"
    - "access-control-allow-methods"
    - "access-control-allow-origin"
    - "access-control-max-age"
    - "X-Frame-Options"

fastdfs:
  network_timeout_in_seconds: 60 #超时时间
  connect_timeout_in_seconds: 60 #连接超时时间
  charset: UTF-8
  tracker_servers: ***************:22122 #存储地址
  http_anti_steal_token: no
  http_secret_key: 123456
  #  http_tracker_http_port: 22122 # 访问端口
  thumb-image: # 缩略图
    width: 60
    height: 60

# 时序库相关配置 influx:1 opentsdb:2 iotdb:3
tsdbflag: 3
#influx:
#  url: http://************:8082
#  user:
#  password:
#  database: refactor_phm
#opentsdb:
#  url: http://************:4242
iotdb:
  driver-class-name: org.apache.iotdb.jdbc.IoTDBDriver
  master:
    url: ********************************/
    username: root
    password: root
  slave:
    url: ********************************/
    username: root
    password: root

kafkaTopicName: "SignalVersionNew"
#SignalVersion

#文件上传路径指定
server:
  tomcat:
    basedir: /data/web-server/backend

crrc:
  storage:
    s3:
      endpoint: http://192.168.64.75:9000
      accessKey: phm_object_data
      secretKey: TcUVmWVpJcjJ4bDEze
      defaultBucket: shaoxing2-nuoli
# 异步线程配置
async:
  executor:
    thread:
      # 配置核心线程数
      core_pool_size: 2
      # 配置最大线程数
      max_pool_size: 2
      # 配置队列大小
      queue_capacity: 99999
      # 配置线程池中的线程的名称前缀
      name:
        prefix: async-service-
