server:
  port: 8085
  servlet:
    contextPath: /api
spring:
  jpa:
    properties:
      hibernate:
        temp:
          use_jdbc_metadata_defaults: false
  profiles:
    active: prod
  mvc:
    throw-exception-if-no-handler-found: true
  resources:
    add-mappings: false
  application:
    name: manage-service
  security:
    user:
      name: "client"
      password: "client"
  servlet:
    multipart:
      max-request-size: 10MB
      max-file-size: 10MB
  liquibase:
    enabled: true
    change-log: classpath:liquibase-change-master.xml

mybatis:
  dialect: postgresql
  type-aliases-package: cc.crrc.manage.pojo
  mapper-locations: classpath:mapper/**/*.xml

swagger:
  base-package: cc.crrc

security:
  header: Authorization
  secret: mySecret
  expiration: 15552000
  sessionTimeout: 15552000
  ignoreUrl:
    - "/swagger-ui.html"
    - "/webjars/**"
    - "/swagger-resources/**"
    - "/MonitorWebsocket/**"
    - "/v2/**"
    - "/websocket"
    - "/ac/**"
    - "/tracksideOnlineMonitor/**"

login:
  bad:
    user:
      count: 5
      interval: 300
    ip:
      count: 5
      interval: 60