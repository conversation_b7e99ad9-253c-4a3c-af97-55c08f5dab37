package cc.crrc.manage.datascope;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.mapping.MappedStatement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;

/**
 * @FileName DataPermissionInterceptor
 * <AUTHOR> shuangquan
 * @Date 2020/08/11 9:11
 **/
public class DataScopeUtil {
    private static final Logger logger = LoggerFactory.getLogger(DataScopeUtil.class);

    public static DataScope getPermission(MappedStatement mappedStatement) {
        String id = mappedStatement.getId();
        if (StringUtils.isEmpty(id)) {
            return null;
        }
        String className = id.substring(0, id.lastIndexOf("."));
        String methodName = id.substring(id.lastIndexOf(".") + 1);
        try {
            final Class cls = Class.forName(className);
            final Method[] methods = cls.getMethods();
            for (Method method : methods) {
                if (method.getName().equals(methodName) && method.isAnnotationPresent(DataScope.class)) {
                    return method.getAnnotation(DataScope.class);
                }
            }
        } catch (ClassNotFoundException e) {
            logger.error("Class forname error {}", e.getException());
        }
        return null;
    }
}
