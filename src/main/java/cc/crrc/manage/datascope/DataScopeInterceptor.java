package cc.crrc.manage.datascope;


import cc.crrc.manage.common.utils.SpringBeanUtils;
import cc.crrc.manage.common.utils.UserUtils;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.DefaultReflectorFactory;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.factory.DefaultObjectFactory;
import org.apache.ibatis.reflection.wrapper.DefaultObjectWrapperFactory;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Properties;

/**
 * @FileName DataPermissionInterceptor
 * <AUTHOR> shuangquan
 * @Date 2020/08/11 9:11
 **/
@Intercepts({
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class,
                RowBounds.class, ResultHandler.class})
})
public class DataScopeInterceptor implements Interceptor {
    private static final String DATA_SCOPE_MARK = "$DATASCOPE$";
    private static final String TRUE_CONDITION = "1=1";
    private static final String USER_PERMISSION_CONDITION = "%s='%s'";
    private static final String CURRENT_ORGANIZATION_SQL = "%s in (select user_id from sys_organization_user where organization_id in(\n" +
            "SELECT organization_id FROM sys_organization_user where user_id = '%s'))";
    private static final String CURRENT_AND_CHILD_ORGANIZATION_SQL = "%s in (select user_id from sys_organization_user where organization_id in \n" +
            "(WITH RECURSIVE T ( ID,parent_id ) AS (\n" +
            "        SELECT ID,\n" +
            "        parent_id\n" +
            "        FROM\n" +
            "        sys_organization\n" +
            "        WHERE\n" +
            "        ID IN (\n" +
            "        SELECT\n" +
            "        organ_user.organization_id\n" +
            "        FROM\n" +
            "        sys_organization_user organ_user\n" +
            "        WHERE\n" +
            "        organ_user.user_id = '%s'\n" +
            "        )\n" +
            "        AND del_flag = '0' UNION ALL\n" +
            "        SELECT\n" +
            "        T1.ID,\n" +
            "        T1.parent_id\n" +
            "        FROM\n" +
            "        sys_organization T1\n" +
            "        JOIN T ON T1.parent_id = T.ID\n" +
            "        AND T1.del_flag = '0'\n" +
            "        )\n" +
            "SELECT ID\n" +
            "        FROM\n" +
            "        T))";
    private static final String MAX_DATA_SCOPE = "select max(sr.data_permission) from sys_role_user sru,sys_role sr where sru.user_id='%s' and sru.role_id=sr.Id";

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object[] args = invocation.getArgs();
        MappedStatement ms = (MappedStatement) args[0];
        Object parameter = args[1];
        DataScope dataScopeAnnotation = DataScopeUtil.getPermission(ms);
        if (dataScopeAnnotation != null) {
            JdbcTemplate jdbcTemplate = SpringBeanUtils.getBean(JdbcTemplate.class);
            Integer dataScope = jdbcTemplate.queryForObject(String.format(MAX_DATA_SCOPE, UserUtils.getUserId()), Integer.class);
            if (dataScope == null) {
                return invocation.proceed();
            }
            BoundSql boundSql = ms.getBoundSql(parameter);
            String sql = boundSql.getSql();
            String newSql;
            if (sql.indexOf(DATA_SCOPE_MARK) != -1) {
                newSql = replaceSql(sql, dataScope, dataScopeAnnotation.userIdColumn());
            } else {
                newSql = appendSql(sql, dataScope, dataScopeAnnotation.userIdColumn());
            }
            MappedStatement newStatement = newMappedStatement(ms, new BoundSqlSqlSource(boundSql));
            MetaObject msObject = MetaObject.forObject(newStatement, new DefaultObjectFactory(),
                    new DefaultObjectWrapperFactory(), new DefaultReflectorFactory());
            msObject.setValue("sqlSource.boundSql.sql", newSql);
            args[0] = newStatement;
        }
        return invocation.proceed();

    }

    private String replaceSql(String sql, int dataScope, String column) {
        switch (dataScope) {
            case 1://仅查看自己数据
                sql = sql.replace(DATA_SCOPE_MARK, String.format(USER_PERMISSION_CONDITION, column,
                        UserUtils.getUserId()));
                break;
            case 2://所属组织数据
                sql = sql.replace(DATA_SCOPE_MARK, String.format(CURRENT_ORGANIZATION_SQL,
                        column, UserUtils.getUserId()));
                break;
            case 3://所属组织以及下级组织数据
                sql = sql.replace(DATA_SCOPE_MARK, String.format(CURRENT_AND_CHILD_ORGANIZATION_SQL,
                        column, UserUtils.getUserId()));
                break;
            case 4://查看全部数据
                sql = sql.replace(DATA_SCOPE_MARK, TRUE_CONDITION);
                break;
            default:
                break;
        }
        return sql;
    }

    private String appendSql(String sql, int dataScope, String column) {
        StringBuilder buffer = new StringBuilder("SELECT temp.* FROM (");
        buffer.append(sql);
        buffer.append(") as temp");
        buffer.append(" WHERE ");
        switch (dataScope) {
            case 1://仅查看自己数据
                buffer.append(String.format(USER_PERMISSION_CONDITION, column,
                        UserUtils.getUserId()));
                break;
            case 2://所属组织数据
                buffer.append(String.format(CURRENT_ORGANIZATION_SQL,
                        column, UserUtils.getUserId()));
                break;
            case 3://所属组织以及下级组织数据
                buffer.append(String.format(CURRENT_AND_CHILD_ORGANIZATION_SQL,
                        column, UserUtils.getUserId()));
                break;
            case 4://查看全部数据
                buffer.setLength(0);
                buffer.append(sql);
                break;
            default:
                break;
        }
        return buffer.toString();
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
    }

    private MappedStatement newMappedStatement(MappedStatement ms, SqlSource newSqlSource) {
        MappedStatement.Builder builder =
                new MappedStatement.Builder(ms.getConfiguration(), ms.getId(), newSqlSource, ms.getSqlCommandType());
        builder.resource(ms.getResource());
        builder.fetchSize(ms.getFetchSize());
        builder.statementType(ms.getStatementType());
        builder.keyGenerator(ms.getKeyGenerator());
        if (ms.getKeyProperties() != null && ms.getKeyProperties().length != 0) {
            StringBuilder keyProperties = new StringBuilder();
            for (String keyProperty : ms.getKeyProperties()) {
                keyProperties.append(keyProperty).append(",");
            }
            keyProperties.delete(keyProperties.length() - 1, keyProperties.length());
            builder.keyProperty(keyProperties.toString());
        }
        builder.timeout(ms.getTimeout());
        builder.parameterMap(ms.getParameterMap());
        builder.resultMaps(ms.getResultMaps());
        builder.resultSetType(ms.getResultSetType());
        builder.cache(ms.getCache());
        builder.flushCacheRequired(ms.isFlushCacheRequired());
        builder.useCache(ms.isUseCache());
        return builder.build();
    }

    class BoundSqlSqlSource implements SqlSource {
        private BoundSql boundSql;

        public BoundSqlSqlSource(BoundSql boundSql) {
            this.boundSql = boundSql;
        }

        @Override
        public BoundSql getBoundSql(Object parameterObject) {
            return boundSql;
        }
    }
}
