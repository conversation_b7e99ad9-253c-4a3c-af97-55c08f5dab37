package cc.crrc.manage.controller.eva;


import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.eva.EvaHealthRuleDTO;
import cc.crrc.manage.pojo.line.LineDTO;
import cc.crrc.manage.service.eva.HealthyScopeService;
import cc.crrc.manage.service.line.LineService;
import cc.crrc.manage.service.mtr.MtrVehicleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 健康评分
 *
 * <AUTHOR>
 * 2020/02/17
 **/
@Api(tags = "健康评分")
@RestController
@RequestMapping("/healthyScope")
public class HealthyScopeController {
    @Autowired
    private HealthyScopeService healthyScopeService;
    @Autowired
    private MtrVehicleService mtrVehicleService;
    @Autowired
    private LineService lineService;

    @PostMapping(value = "/line/list")
    @SystemLog(optDesc = "查询线路信息", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询线路信息 ", notes = "查询线路信息")
    public Object getLines(@RequestBody LineDTO lineDTO) {
        return lineService.getLines(lineDTO);
    }

    @GetMapping(value = "/vehicle/list")
    @SystemLog(optDesc = "查询车辆列表（支持线路id查询)", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询车辆列表", notes = "支持按线路id查询。不传线路id，则全部查询。")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "metroLineId", value = "线路id", dataType = "String")
    })
    public Object listVehicle(@LogParam(description = "线路id") @RequestParam String metroLineId) {
        return mtrVehicleService.listVehicle(metroLineId);
    }


    @GetMapping(value = "/history/system")
    @SystemLog(optDesc = "查询历史评分记录", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询车辆和车辆子系统历史评分记录", notes = "按车辆编码和子系统名称进行查询。")
    public Object getSystemScopeByVehicleCodeAndSysName(@RequestParam String vehicleCode, @RequestParam List<String> sysNameArray
            , @RequestParam String startTime,@RequestParam String endTime) {
        return healthyScopeService.getSystemScopeByVehicleCodeAndSysName(vehicleCode, sysNameArray,startTime,endTime);
    }

    @GetMapping(value = "/rule/list")
    @SystemLog(optDesc = "查询健康评分规则并分组（按车型和分类精确查询)", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询健康评分规则并分组", notes = "按车型和分类精确查询")
    public Object listHealthRule(EvaHealthRuleDTO evaHealthRuleDTO) {
        return healthyScopeService.listHealthRule(evaHealthRuleDTO);
    }


    @PutMapping(value = "/updateHealthRule")
    @SystemLog(optDesc = "修改健康评分规则", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "修改健康评分规则新接口")
    public Object updateHealthRule(@RequestParam String evaHealthRule, @RequestParam String category) {
        return healthyScopeService.updateHealthRule(evaHealthRule, category);
    }

    @GetMapping(value = "/overView/getoverViewByLine")
    @SystemLog(optDesc = "查询健康评分总览", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询健康评分总览", notes = "支持按线路id查询。不传线路id，则全部查询。")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "metroLineId", value = "线路id", dataType = "String"),
            @ApiImplicitParam(name = "startTime", value = "startTime", dataType = "String"),
            @ApiImplicitParam(name = "endTime", value = "endTime", dataType = "String")
    })
    public Object getScopeListByLineId(@RequestParam String metroLineId, @RequestParam String startTime,@RequestParam String endTime) {
        return healthyScopeService.getScopeListByLineId(metroLineId,startTime,endTime);
    }

    @GetMapping(value = "/system/vehicleType/list")
    @SystemLog(optDesc = "查询车辆所有子系统列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询车辆所有子系统列表", notes = "按车辆编码进行查询。")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleCode", value = "车辆编码", required = true, dataType = "String")
    })
    public Object getAllSystemByVehicleCode(@LogParam(description = "车辆编码") @RequestParam String vehicleCode) {
        return healthyScopeService.getAllSystemByVehicleCode(vehicleCode);
    }

}
