package cc.crrc.manage.controller.eva;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.service.eva.HealthyScopeService;
import cc.crrc.manage.service.eva.RamsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName RamsController
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/7/8 10:17
 * @Version 1.0
 **/
@Api(tags = "健康评分")
@RestController
@Validated
public class RamsController {
    private static final Logger logger = LoggerFactory.getLogger(HealthyScopeService.class);

    @Autowired
    private RamsService ramsService;

    @GetMapping(value = "/RAMS/line/list")
    @ApiOperation(value = "获取RAMS线路列表")
    public Object listLine() {
        return ramsService.getRamsLineList();
    }

    @GetMapping(value = "/RAMS/vehicleType/list")
    @ApiOperation(value = "获取RAMS车型列表")
    public Object listVehicleType() {
        return ramsService.getVehicleTypeRams();
    }

    @GetMapping(value = "/healthyScopeRams/selectRams")
    @ApiOperation(value = "健康评分-RAMS-部件RAMS")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "productNumber", value = "productNumber", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "nameCn", value = "部件型号名称",  dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pageNumber", value = "分页页数", required = true, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "分页size", required = true, dataType = "int")
    })

    @ResponseBody
    public Object SelectRams(@RequestParam(value = "nameCn",required = false)String nameCn, @RequestParam(value = "productNumber",required = false) String productNumber
            , @RequestParam("pageNumber")int pageNumber, @RequestParam("pageSize")int pageSize){
        return ramsService.selectRams(nameCn, productNumber,pageNumber,pageSize);
    }

}

