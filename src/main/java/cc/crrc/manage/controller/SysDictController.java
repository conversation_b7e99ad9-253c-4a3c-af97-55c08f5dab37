package cc.crrc.manage.controller;

import cc.crrc.manage.cache.RefreshCache;
import cc.crrc.manage.common.annotation.LogParam;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.SysDictDTO;
import cc.crrc.manage.pojo.SysDictVO;
import cc.crrc.manage.service.SysDictService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @FileName SysDictController
 * <AUTHOR> yuxi
 * @Date 2019/6/14 13:35
 * @Version 1.0
 **/
@Api(tags = "数据字典")
@RestController
@RequestMapping("/dict")
@Validated
public class SysDictController {
    @Autowired
    private SysDictService dictService;

    /**
     * @return java.util.List<cc.crrc.manage.pojo.SysDictVO>
     * @Description 分页取得数据字典列表
     * <AUTHOR> yuxi
     * @Date 15:58 2019/6/14
     * @Param [dictVO]
     **/
    @SystemLog(optDesc = "查询字典列表分页", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "分页取得数据字典列表")
    @PostMapping("/dicts/page")
    public Object listDict(@RequestBody SysDictVO dictVO) {
        return dictService.listDict(dictVO);
    }

    /**
     * @return java.lang.Object
     * @Description 根据类型id筛选数据字典
     * <AUTHOR> yuxi
     * @Date 9:29 2019/10/22
     * @Param [typeId]
     * TODO：该接口暂时没有被使用
     **/
    @ApiOperation(value = "根据类型id筛选数据字典")
    @GetMapping("/dicts/typeId")
    public Object listByTypeId(@RequestParam("typeId") String typeId) {
        return dictService.listByTypeId(typeId);
    }

    /**
     * @return java.lang.Object
     * @Description 根据类型名称筛选数据字典
     * <AUTHOR> yuxi
     * @Date 9:29 2019/10/22
     * @Param [typeCode]
     **/
    @SystemLog(optDesc = "查询数据字典", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据类型名称筛选数据字典")
    @GetMapping("/dicts/type")
    public Object listByTypeCode(@LogParam(description = "类型名称") @RequestParam("typeCode") String typeCode) {
        return dictService.listByType(typeCode);
    }

    /**
     * @return int
     * @Description 新增数据字典
     * <AUTHOR> yuxi
     * @Date 16:04 2019/6/14
     * @Param [dictVO]
     **/
    @SystemLog(optDesc = "增加数据字典", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "新增数据字典")
    @PostMapping("/dict")
    @RefreshCache(values = "DICT")
    public Object addDict(@RequestBody @Valid SysDictDTO dictVO) {
        return dictService.addDict(dictVO);
    }

    /**
     * @return int
     * @Description 更新数据
     * <AUTHOR> yuxi
     * @Date 16:05 2019/6/14
     * @Param [dictVO]
     **/
    @SystemLog(optDesc = "修改数据字典", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "更新数据字典")
    @PutMapping("/dict")
    @RefreshCache(values = "DICT")
    public Object updateDict(@RequestBody @Valid SysDictDTO dictDTO) {
        return dictService.updateDict(dictDTO);
    }

    /**
     * @return void
     * @Description 批量删除数据字典
     * <AUTHOR> yuxi
     * @Date 16:06 2019/6/14
     * @Param [ids]
     **/
    @SystemLog(optDesc = "删除数据字典", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "删除数据字典")
    @DeleteMapping("/dict/id")
    @RefreshCache(values = "DICT")
    public Object deleteDict(@LogParam(description = "数据字典id") @RequestParam("id") String id) {
        return dictService.deleteDict(id);
    }

    /**
     * @return java.util.List<cc.crrc.manage.pojo.SysDictVO>
     * @Description 获取线路或者车型下的对应字典类型字典通用接口
     * <AUTHOR>
     * @Date 08:45 2021/4/26
     * @Param [dictVO]
     **/
    @SystemLog(optDesc = "查询字典通用接口", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询字典通用接口")
    @PostMapping("/dicts/listDictUniversal")
    public Object listDictUniversal(@RequestBody SysDictDTO sysDictDTO) {
        return dictService.listDictUniversal(sysDictDTO);
    }
}
