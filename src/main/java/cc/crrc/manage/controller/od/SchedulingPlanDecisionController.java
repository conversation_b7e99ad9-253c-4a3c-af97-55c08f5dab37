package cc.crrc.manage.controller.od;


import cc.crrc.manage.common.annotation.InsertValidated;
import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.od.SchedulingPlanDecisionPO;
import cc.crrc.manage.service.od.SchedulingPlanDecisionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;



/**
 * @FileName SchedulingPlanDecisionController
 * @Description 调度计划决策控制层
 * <AUTHOR>
 * @Date 2020/7/29 15:04
 **/
@Api(tags = "运维决策-决策配置-行车调度计划决策")
@RestController
@RequestMapping("/schedulingPlan")
public class SchedulingPlanDecisionController {
    @Autowired
    private SchedulingPlanDecisionService schedulingPlanDecisionService;

    @ApiOperation(value = "查询行车调度计划决策列表")
    @SystemLog(optDesc = "查询行车调度计划决策列表", optType = SystemLogEnum.SELECT)
    @GetMapping("/findSchedulingPlanPOList")
    @ApiImplicitParam(paramType = "query", name = "pushStatus", value = "推送状态", required = false, dataType = "String")
    public Object findSchedulingPlanPOList(@RequestParam Integer pageNumber,
                                           @RequestParam Integer pageSize,
                                           @RequestParam String pushStatus){
        return schedulingPlanDecisionService.findSchedulingPlanPOList(pageNumber,pageSize,pushStatus);
    }

    @ApiOperation(value = "增加行车调度计划决策配置和生成的相关数据")
    @SystemLog(optDesc = "增加行车调度计划决策配置和生成的相关数据", optType = SystemLogEnum.INSERT)
    @PostMapping("/findSchedulingPlanPOList")
    public Object saveSchedulingPlanPO(@InsertValidated @RequestBody SchedulingPlanDecisionPO schedulingPlanDecisionPO){
        return schedulingPlanDecisionService.saveSchedulingPlanPO(schedulingPlanDecisionPO);
    }

    @ApiOperation(value = "计算发车间隔和投运车辆数目")
    @SystemLog(optDesc = "计算发车间隔和投运车辆数目", optType = SystemLogEnum.SELECT)
    @PostMapping("/calculateCountTime")
    public Object calculateCountTime(@UpdateValidated @RequestBody SchedulingPlanDecisionPO schedulingPlanDecisionPO){
        return schedulingPlanDecisionService.formulaComputing(schedulingPlanDecisionPO);
    }

    @ApiOperation(value = "查询系统推荐数据")
    @SystemLog(optDesc = "查询系统推荐数据", optType = SystemLogEnum.SELECT)
    @GetMapping("/systemRecommended")
    public Object systemRecommended(@RequestParam String calculateParam){
        return schedulingPlanDecisionService.systemRecommended(calculateParam);
    }

    @ApiOperation(value = "修改行车调度计划决策和推送状态")
    @SystemLog(optDesc = "修改行车调度计划决策和推送状态修改", optType = SystemLogEnum.SELECT)
    @PostMapping("/updeteSchedulingPlanPO")
    public Object updeteSchedulingPlanPO(@UpdateValidated @RequestBody SchedulingPlanDecisionPO schedulingPlanDecisionPO){
        return schedulingPlanDecisionService.updeteSchedulingPlanPO(schedulingPlanDecisionPO);
    }

    @ApiOperation(value = "删除多个行车调度计划决策数据")
    @SystemLog(optDesc = "删除多个行车调度计划决策数据", optType = SystemLogEnum.SELECT)
    @DeleteMapping("/deleteSchedulingPlanPO")
    @ApiImplicitParam(paramType = "query", name = "ids", value = "多个决策id,用逗号分隔", required = true, dataType = "String")
    public Object deleteSchedulingPlanPO(@RequestParam String ids){
        return schedulingPlanDecisionService.deleteSchedulingPlanPO(ids);
    }

}
