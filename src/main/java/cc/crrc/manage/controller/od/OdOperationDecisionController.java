package cc.crrc.manage.controller.od;


import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.od.OdOperationDecisionPO;
import cc.crrc.manage.service.od.OdOperationDecisionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @FileName OdOperationDecisionController
 * @Description 运维决策controller
 * <AUTHOR> yuxi
 * @Date 2020/5/7 15:10
 **/
@Api(tags = "运维决策-决策总览")
@RestController
@RequestMapping("/operationDecision")
public class OdOperationDecisionController {
    @Autowired
    private OdOperationDecisionService operationDecisionService;


    @SystemLog(optDesc = "查询决策分页列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询决策分页列表")
    @PostMapping("/page/list")
    public Object listOperationDecision(@RequestBody OdOperationDecisionPO operationDecision) {
        return operationDecisionService.listOperationDecision(operationDecision);
    }

    @SystemLog(optDesc = "修改决策状态", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "修改决策状态")
    @PostMapping("/UpdateReconditionStatus")
    public Object UpdateReconditionStatus(@RequestBody OdOperationDecisionPO operationDecision){
        return operationDecisionService.UpdateReconditionStatus(operationDecision);
    }


}
