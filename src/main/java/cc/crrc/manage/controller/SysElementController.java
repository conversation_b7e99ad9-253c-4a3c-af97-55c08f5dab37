package cc.crrc.manage.controller;

import cc.crrc.manage.common.annotation.LogParam;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.Element;
import cc.crrc.manage.pojo.SysElementRole;
import cc.crrc.manage.service.SysElementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "用户资源")
@RestController
@RequestMapping(value = "/sysElement")
public class SysElementController {
    @Autowired
    private SysElementService sysElementService;

    @SystemLog(optDesc="增加资源", optType = SystemLogEnum.INSERT)
    @PostMapping(value = "/addSysElement")
    @ApiOperation(value = "新增资源", notes = "系统菜单新建")//需要menuId
    public Object addSysElement(@RequestBody Element element) {
        return sysElementService.addSysElement(element);
    }

    @SystemLog(optDesc="删除资源", optType = SystemLogEnum.DELETE)
    @DeleteMapping(value = "/deleteSysElementById")
    @ApiOperation(value = "删除 资源", notes = "删除资源")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "Id", required = true, dataType = "String")
    })
    public Object deleteSysElementById(@RequestBody Element element) {
        return sysElementService.deleteSysElementById(element);
    }

    @SystemLog(optDesc="修改资源", optType = SystemLogEnum.UPDATE)
    @PutMapping(value = "/updateSysElementById")
    @ApiOperation(value = "更新 资源", notes = "更新资源")
    public Object updateSysElementById(@RequestBody Element element) {
        return sysElementService.updateSysElementById(element);
    }

    @SystemLog(optDesc = "查询 资源", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/selectSysElement")
    @ApiOperation(value = "查询 资源", notes = "查询资源")//根据menuId查该菜单下的资源  根据输入的关键字查找“elementName”相关的资源
    public Object selectSysElement(@RequestBody Element element) {
    	 //return sysElementService.selectSysElement(element);  //原代码
    	//查询当前用户以及当前编辑的角色的资源 heshenglun 2020-8-26
    	return  sysElementService.selectSysElementByUser(element);
       
    }

    //为角色添加资源
    @SystemLog(optDesc="为角色增加资源", optType = SystemLogEnum.INSERT)
    @PostMapping(value = "/addElementByRoleId")
    @ApiOperation(value = "为角色 添加 资源", notes = "角色添加资源")
    public Object addElementByRoleId(@RequestBody SysElementRole sysElementRole) {
        return sysElementService.addElementByRoleId(sysElementRole);
    }

    //为角色删除资源
    @SystemLog(optDesc="为角色删除资源", optType = SystemLogEnum.DELETE)
    @DeleteMapping(value = "/removeElementByRoleId")
    @ApiOperation(value = "为角色 移除 资源", notes = "角色移除资源")
    public Object removeElementByRoleId(@LogParam(description = "资源id") @RequestParam String elementId, @LogParam(description = "角色id")@RequestParam String roleId) {
        return sysElementService.removeElementByRoleId(elementId, roleId);
    }

    @SystemLog(optDesc = "通过role_id查询资源", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/getAllElementByRoleId")
    @ApiOperation(value = "通过role_id查询资源", notes = "通过role_id查询资源")
    public Object getAllElementByRoleId(@LogParam(description = "角色id")@RequestParam String roleId) {
        return sysElementService.getAllElementByRoleId(roleId);
    }

}
