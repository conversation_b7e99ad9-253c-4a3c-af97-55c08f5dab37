package cc.crrc.manage.controller.analysis;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.analysis.SysAccessStatisticsVO;
import cc.crrc.manage.service.analysis.SysAccessStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



@Api(tags = "用户访问统计")
@RestController
@RequestMapping(value = "/sysAccessStatistics")
public class SysAccessStatisticsController {

    @Autowired
    private SysAccessStatisticsService sysAccessStatisticsService;

    @SystemLog(optDesc = "用户访问统计", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/accessCount")
    @ApiOperation(value = "用户访问计数", notes = "用户访问计数")
    @ApiImplicitParam(name = "menuCode", value = "菜单code", paramType = "query", dataType = "String", required = true)
    public Object accessCount(@RequestParam("menuCode") String menuCode){
        return sysAccessStatisticsService.accessCount(menuCode);
    }

    @SystemLog(optDesc = "访客统计", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/getVisitors")
    @ApiOperation(value = "访客统计", notes = "访客统计")
    public Object getVisitors(@RequestBody SysAccessStatisticsVO vo) {
        return sysAccessStatisticsService.getVisitors(vo);
    }

    @SystemLog(optDesc = "浏览器统计", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/getAgent")
    @ApiOperation(value = "浏览器统计", notes = "浏览器统计")
    public Object getAgent(@RequestParam String startTime, @RequestParam String endTime) {
        return sysAccessStatisticsService.getAgent(startTime,endTime);
    }

    @SystemLog(optDesc = "当日访问量统计", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/getCountByDate")
    @ApiOperation(value = "当日访问量统计 ", notes = "当日访问量统计")
    public Object getCountByDate(@RequestParam String startTime, @RequestParam String endTime) {
        return sysAccessStatisticsService.getCountByDate(startTime,endTime);
    }

    @SystemLog(optDesc = "用户访问统计", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/getParentMenuCountByDate")
    @ApiOperation(value = "用户访问统计 ", notes = "用户访问统计")
    public Object getParentMenuCount(@RequestParam String startTime, @RequestParam String endTime) {
        return sysAccessStatisticsService.getParentMenuCount(startTime,endTime);
    }

    @SystemLog(optDesc = "访问页面统计", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/getMenuCountByDate")
    @ApiOperation(value = "访问页面统计 ", notes = "访问页面统计")
    public Object getMenuCountByDate(@RequestParam String parentMenuCode,@RequestParam String startTime, @RequestParam String endTime) {
        return sysAccessStatisticsService.getMenuCountByDate(parentMenuCode,startTime,endTime);
    }

}
