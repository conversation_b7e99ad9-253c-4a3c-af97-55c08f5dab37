package cc.crrc.manage.controller.analysis;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.analysis.AnalysisParamVO;
import io.swagger.annotations.ApiImplicitParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import cc.crrc.manage.service.analysis.AnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Api(tags = "统计分析")
@RestController
@RequestMapping(value = "/analysis")
public class AnalysisController {
    @Autowired
    private AnalysisService analysisService;

    @SystemLog(optDesc = "车辆各项能耗统计", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/vehicle/energy")
    @ApiOperation(value = "车辆各项能耗统计", notes = "车辆各项能耗统计")
    public Object getEnergy(@RequestBody AnalysisParamVO param) {
        return analysisService.getEnergy(param);
    }

    @SystemLog(optDesc = "车辆里程趋势", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/vehicle/mileageTrend")
    @ApiOperation(value = "车辆里程趋势", notes = "车辆里程趋势")
    public Object totleMileageTrend(@RequestBody AnalysisParamVO param) {
        return analysisService.mileageTrend(param);
    }

    @SystemLog(optDesc = "车辆总能耗趋势", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/vehicle/totalEnergyTrend")
    @ApiOperation(value = "车辆总能耗趋势", notes = "车辆总能耗趋势")
    public Object totalEnergyTrend(@RequestBody AnalysisParamVO param) {
        return analysisService.totalEnergyTrend(param);
    }

    @SystemLog(optDesc = "车辆各能耗趋势", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/vehicle/traConTrend")
    @ApiOperation(value = "车辆各能耗趋势", notes = "车辆各能耗趋势")
    public Object traConTrend(@RequestBody AnalysisParamVO param) {
        return analysisService.traConTrend(param);
    }

    @SystemLog(optDesc = "线路数据统计-线路各列车数据量时间统计", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/line/collectionReportVehicleCount")
    @ApiOperation(value = "线路数据统计各列车数据量时间统计", notes = "线路数据统计各列车数据量时间统计")
    public Object collectionReportVehicleCount(@RequestBody AnalysisParamVO param) {
        return analysisService.collectionReportVehicleCount(param);
    }

    @SystemLog(optDesc = "线路数据统计-线路各列车数据量统计", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/line/collectionReportCount")
    @ApiOperation(value = "线路数据统计各列车数据量统计", notes = "线路数据统计各列车数据量统计")
    public Object collectionReportCount(@RequestBody AnalysisParamVO param) {
        return analysisService.collectionReportCount(param);
    }

    @SystemLog(optDesc = "线路各车辆运行总时间", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/line/totalRunTime")
    @ApiOperation(value = "线路各车辆运行总时间", notes = "线路各车辆运行总时间")
    @ApiImplicitParam(name = "lineId", value = "线路id", paramType = "query", dataType = "String", required = true)
    public Object totalRunTime(@RequestParam String lineId) {
        return analysisService.totalRunTime(lineId);
    }

    @SystemLog(optDesc = "线路各车辆运行总里程", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/line/totalRunDistance")
    @ApiOperation(value = "线路各车辆运行总里程", notes = "线路各车辆运行总里程")
    @ApiImplicitParam(name = "lineId", value = "线路id", paramType = "query", dataType = "String", required = true)
    public Object totalRunDistance(@RequestParam String lineId) {
        return analysisService.totalRunDistance(lineId);
    }

    @SystemLog(optDesc = "线路各车辆的各个能耗统计", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/line/totalEngyByVehicle")
    @ApiOperation(value = "线路各车辆的各个能耗统计", notes = "线路各车辆的各个能耗统计")
    @ApiImplicitParam(name = "lineId", value = "线路id", paramType = "query", dataType = "String", required = true)
    public Object totalEngyByVehicle(@RequestParam String lineId) {
        return analysisService.totalEngyByVehicle(lineId);
    }

    @SystemLog(optDesc = "线路的里程趋势", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/line/mileageTrend")
    @ApiOperation(value = "线路的里程趋势", notes = "线路的里程趋势")
    @ApiImplicitParam(name = "lineId", value = "线路id", paramType = "query", dataType = "String", required = true)
    public Object lineMileageTrend(@RequestParam String lineId) {
        return analysisService.lineMileageTrend(lineId);
    }

    /**
     * @throws
     * @Title: getLineEnergy
     * @Description: 根据线路获取所有信息
     * @param: [param]
     * @return: java.lang.Object
     * @date: 2020/11/5 19:10
     * @author: Heshenglun
     */
    @SystemLog(optDesc = "全网运行统计-根据线路获取所有信息", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/line/getLineEnergy")
    @ApiOperation(value = "全网统计根据线路获取所有信息", notes = "全网统计根据线路获取所有信息")
    public Object getLineEnergy(@RequestBody AnalysisParamVO param) {
        return analysisService.getLineEnergy(param);
    }

    @SystemLog(optDesc = "全网运行统计-根据线路获取所有信息(用于饼图)", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/line/getLineEnergyPic")
    @ApiOperation(value = "全网统计根据线路获取所有信息(饼图)", notes = "全网统计根据线路获取所有信息(饼图)")
    public Object getLineEnergyPic(@RequestBody AnalysisParamVO param) {
        return analysisService.getLineEnergyPic(param);
    }

    /**
     * @throws
     * @Title: getLineAll
     * @Description: 根据线路获取各个信息(时间 、 里程 、 能耗)
     * @param: [param]
     * @return: java.lang.Object
     * @date: 2020/11/6 9:29
     * @author: Heshenglun
     */
    @SystemLog(optDesc = "全网运行统计-根据线路获取各个信息(时间、里程、能耗)", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/line/getLineAll")
    @ApiOperation(value = "全网统计根据线路获取各个信息(时间、里程、能耗)", notes = "全网统计根据线路获取各个信息(时间、里程、能耗)")
    public Object getLineAll(@RequestBody AnalysisParamVO param) {
        return analysisService.getLineAll(param);
    }

    @SystemLog(optDesc = "车辆故障统计-根据车厢查询", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/fault/getFaultByLocation")
    @ApiOperation(value = "车辆故障统计-根据车厢查询", notes = "车辆故障统计-根据车厢查询")
    public Object getFaultByLocation(@RequestBody AnalysisParamVO param) {
        return analysisService.getFaultByLocation(param);
    }

    @SystemLog(optDesc = "车辆故障统计-根据子系统查询", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/fault/getFaultBySubsystem")
    @ApiOperation(value = "车辆故障统计-根据子系统查询", notes = "车辆故障统计-根据子系统查询")
    public Object getFaultBySubsystem(@RequestBody AnalysisParamVO param) {
        return analysisService.getFaultBySubsystem(param);
    }

    @SystemLog(optDesc = "车辆故障统计-根据故障等级查询", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/fault/getFaultByLevel")
    @ApiOperation(value = "车辆故障统计-根据故障等级查询", notes = "车辆故障统计-根据故障等级查询")
    public Object getFaultByLevel(@RequestBody AnalysisParamVO param) {
        return analysisService.getFaultByLevel(param);
    }

    @SystemLog(optDesc = "车辆故障统计-根据日期分类查询", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/fault/getFaultByDate")
    @ApiOperation(value = "车辆故障统计-根据日期分类查询", notes = "车辆故障统计-根据日期分类查询")
    public Object getFaultByDate(@RequestBody AnalysisParamVO param) {
        return analysisService.getFaultByDate(param);
    }

    @SystemLog(optDesc = "车辆故障统计-根据故障等级和子系统查询", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/fault/getSystemFaultByLevel")
    @ApiOperation(value = "车辆故障统计-根据故障等级和子系统查询", notes = "车辆故障统计-根据故障等级子系统查询")
    public Object getSystemFaultByLevel(@RequestBody AnalysisParamVO param) {
        return analysisService.getSystemFaultByLevel(param);
    }

}
