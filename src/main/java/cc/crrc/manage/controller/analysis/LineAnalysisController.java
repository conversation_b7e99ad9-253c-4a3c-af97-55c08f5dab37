package cc.crrc.manage.controller.analysis;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.StringUtils;
import cc.crrc.manage.service.analysis.LineAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/07/05/ 13:41
 */

@Api(tags = "线路数据统计-历史回溯")
@RestController
@RequestMapping(value = "/communicationHistory")
public class LineAnalysisController {

    @Autowired
    private LineAnalysisService lineAnalysisService;

    @SystemLog(optDesc = "线路数据统计-历史回溯", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "线路数据统计-历史回溯", notes = "多条件筛选")
    @PostMapping(value = "/dataStatistics")
    public Object dataStatistics(@RequestParam("time") String time, @RequestParam("vehicleCode") String vehicleCode){
        if (StringUtils.isEmpty(time)){
            throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,"请选择日期");
        }
        return lineAnalysisService.dataStatistics(time, vehicleCode);
    }
}
