package cc.crrc.manage.controller;



import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.common.utils.TreeUtil;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.pojo.Menu;
import cc.crrc.manage.pojo.MenuTree;
import cc.crrc.manage.pojo.User;
import cc.crrc.manage.service.EntitlementService;
import cc.crrc.manage.service.SysMenuService;
import cc.crrc.manage.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Api(tags = "用户信息接口")
@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private EntitlementService entitlementService;

    @Autowired
    private SysMenuService service;

    /**
     * 注册
     *
     * @param user
     * @return
     */
    @SystemLog(optDesc="用户注册", optType = SystemLogEnum.INSERT)
    @PostMapping("register")
    @ApiOperation(value = "用户注册")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "username", value = "用户名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "name", value = "姓名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "password", value = "密码", required = true, dataType = "String")
    })
    public Object register(User user) {
        return this.userService.register(user);
    }


    /**
     * 根据用户名和密码查询用户
     *
     * @param username
     * @param password
     * @return
     */
    @SystemLog(optDesc = "查询用户信息", optType = SystemLogEnum.SELECT)
    @GetMapping("query")
    @ApiOperation(value = "查询用户信息", notes = "根据用户名和密码查询用户信息是否正确")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "username", value = "用户名", required = true, dataType = "String"),
            @ApiImplicitParam(name = "password", value = "密码", required = true, dataType = "String")
    })
    public User queryUser(
            @RequestParam("username") String username,
            @RequestParam("password") String password
    ) {
        //创建返回对象
        User user = this.userService.queryUser(username, password);
        return user;
    }

    @SystemLog(optDesc = "查询所有用户信息", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询所有用户信息", notes = "查询所有的注册用户信息")
    @GetMapping("getAll")
    public Object getAll(User user) {
        return userService.getAll(user);
    }


    //--------------------------------------------------------20190514
    @SystemLog(optDesc = "查询用户菜单权限信息", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询用户菜单权限信息", notes = "通过验证查询用户菜单和权限信息")
    @GetMapping("getUserInfo")
    public Object getUserInfo() throws Exception {
        return entitlementService.getUserInfo();
    }

    @SystemLog(optDesc = "查询所有菜单权限信息", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询所有菜单权限信息", notes = "获取所有菜单")
    @GetMapping("getAllMenus")
    public Object getMenusByUsername() {
        return entitlementService.getMenusByUsername();
    }


    @SystemLog(optDesc = "查询菜单树目录", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询菜单树目录", notes = "查询菜单树目录")
    @RequestMapping(value = "/front/menus", method = RequestMethod.GET)
    public Object getMenusByUserId() {
        //获取当前登录用户id
        String userId = UserUtils.getUser().getId();
//        List<Menu> menus = entitlementService.getUserMenuByUserId("1");
        List<Menu> menus = entitlementService.getUserMenuByUserId(userId);//1为admin 的userId//董哥 获取用户id需要改
        List<MenuTree> menuTree = getMenuTree(menus, "-1");
        return menuTree;
    }

    private List<MenuTree> getMenuTree(List<Menu> menus, String root) {
        List<MenuTree> trees = new ArrayList<MenuTree>();
        MenuTree node = null;
        for (Menu menu : menus) {
            node = new MenuTree();
            node.setAuthority(menu.getCode());
            node.setRedirect(menu.getHref());

            BeanUtils.copyProperties(menu, node);
            trees.add(node);
        }
        List<MenuTree> bulid = TreeUtil.bulid(trees, root, new Comparator<MenuTree>() {
            @Override
            public int compare(MenuTree o1, MenuTree o2) {
                return o1.getSort().compareTo(o2.getSort());
            }
        });
        return bulid;
    }

    @SystemLog(optDesc = "查找系统菜单树list", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/selectSysMenuTreeList")
    @ApiOperation(value = "查找 系统菜单树list", notes = "系统菜单树list查找")
    public Object selectSysMenuTreeList() {
        return service.selectSysMenuTreeList();
    }

}
