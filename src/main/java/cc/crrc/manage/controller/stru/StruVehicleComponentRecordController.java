package cc.crrc.manage.controller.stru;


import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.service.stru.StruVehicleComponentRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 车辆构型-车辆履历 控制层
 * <AUTHOR>
 * 2021/1/14
 **/
@Api(tags = "车辆管理-履历管理")
@RestController
@RequestMapping("/vehicle/component/record")
public class StruVehicleComponentRecordController {

    @Autowired
    private StruVehicleComponentRecordService struVehicleComponentRecordService;

    @PostMapping(value = "/file")
    @SystemLog(optDesc = "上传文件", optType = SystemLogEnum.UPLOAD)
    @ApiOperation(value = "上传文件")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleCode", value = "车辆编号", required = true, dataType = "String")
    })
    public Object saveManufacturerFile(@RequestParam MultipartFile file, @RequestParam String vehicleCode) {
        struVehicleComponentRecordService.loadExcelData2DB(file,vehicleCode);
        return null;
    }


    @PostMapping(value = "/downloadExcel")
    @SystemLog(optDesc = "下载履历模板Excel", optType = SystemLogEnum.DOWNLOAD)
    @ApiOperation(value = "下载履历模板Excel")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleId", value = "车辆id", required = true, dataType = "String")
    })
    public void downloadExcel(@RequestParam("vehicleId") String vehicleId, HttpServletResponse response) {
        struVehicleComponentRecordService.downloadExcel(vehicleId,response);
    }


    @GetMapping(value = "/records")
    @SystemLog(optDesc = "查询该车辆构型下的所有部件履历", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询该车辆构型下的所有部件履历")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleId", value = "车辆id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "structureCode", value = "车辆构型", required = true, dataType = "String")
    })
    public Object getVehicleComponentRecordList(@RequestParam String vehicleId, @RequestParam String structureCode){
        return struVehicleComponentRecordService.getVehicleComponentRecordList(vehicleId, structureCode);
    }

}
