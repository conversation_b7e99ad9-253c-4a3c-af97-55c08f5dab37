package cc.crrc.manage.controller.stru;

import cc.crrc.manage.common.annotation.LogParam;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO;
import cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO;
import cc.crrc.manage.service.stru.DigitalVehicleStruService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 车辆构型-车辆履历 控制层
 * <AUTHOR>
 * 2021/11/15
 **/
@Api(tags = "数字车辆-构型")
@RestController
@RequestMapping(value = "/DigitalVehicleStru")
public class DigitalVehicleStruController {


    @Autowired
    private DigitalVehicleStruService service;



    @PostMapping(value = "/mechanismAlarmRuleGroupCount")
    @ApiOperation(value = "数字车辆-根据车辆获取各构型的 机理规则数量统计")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleCode", value = "车辆code", required = true, dataType = "String")
    })
    public Object mechanismAlarmRuleGroupCount(@RequestParam String vehicleCode){
        return service.mechanismAlarmRuleGroupCount(vehicleCode);
    }


    @PostMapping(value = "/mechanismAlarmRuleGroupList")
    @ApiOperation(value = "数字车辆-根据车辆code、三维模型code 获取机理规则list-按构型分组")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleCode", value = "车辆code", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "threedCode", value = "三维模型code", required = true, dataType = "String")
    })
    public Object mechanismAlarmRuleListByVehicleCodeAndThreedCode(@RequestParam String vehicleCode,@RequestParam String threedCode){
        return service.mechanismAlarmRuleListByVehicleCodeAndThreedCode(vehicleCode,threedCode);
    }






    @PostMapping(value = "/autoFaultGroupCount")
    @ApiOperation(value = "数字车辆-根据车辆获取各构型故障数量统计（不区分故障来源）-按构型分组")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleCode", value = "车辆code", required = true, dataType = "String")
    })
    public Object autoFaultGroupListByVehicleCode(@RequestParam String vehicleCode){
        return service.autoFaultGroupCountNumByVehicleCode(vehicleCode);
    }

    @PostMapping(value = "/autoFaultGroupList")
    @ApiOperation(value = "数字车辆-根据车辆、threedCode获取故障list（不区分故障来源、但进行来源标注）")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleCode", value = "车辆code", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "threedCode", value = "三维模型code", required = true, dataType = "String")
    })
    public Object autoFaultGroupListByVehicleCodeAndThreedCode(@RequestParam String vehicleCode,@RequestParam String threedCode){
        return service.autoFaultGroupListByVehicleCodeAndThreedCode(vehicleCode,threedCode);
    }


    @PostMapping(value = "/componentGroupCount")
    @ApiOperation(value = "数字车辆-根据车辆获取各构型部件履历数量统计-按构型分组")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleCode", value = "车辆code", required = true, dataType = "String")
    })
    public Object componentGroupListByVehicleCode(@RequestParam String vehicleCode){
        return service.componentGroupListByVehicleCode(vehicleCode);
    }

    @PostMapping(value = "/componentGroupList")
    @ApiOperation(value = "数字车辆-根据车辆、三维code 获取部件list")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleCode", value = "车辆code", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "threedCode", value = "三维模型code", required = true, dataType = "String")
    })
    public Object componentListGroupListByVehicleCodeAndThreedCode(@RequestParam String vehicleCode,@RequestParam String threedCode){
        return service.componentListGroupListByVehicleCodeAndThreedCode(vehicleCode,threedCode);
    }

    @PostMapping(value = "/relayContactorLifeGroupCount")
    @ApiOperation(value = "数字车辆-根据车辆获取各构型寿命预测 数量统计-按构型分组")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleCode", value = "车辆code", required = true, dataType = "String")
    })
    public Object relayContactorLifeGroupListByVehicleCode(@RequestParam String vehicleCode){
        return service.relayContactorLifeGroupListByVehicleCode(vehicleCode);
    }
    @PostMapping(value = "/relayContactorLifeGroupList")
    @ApiOperation(value = "数字车辆-根据车辆、三维code 获取寿命预测list")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleCode", value = "车辆code", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "threedCode", value = "三维模型code", required = true, dataType = "String")
    })
    public Object relayContactorLifeGroupListByVehicleCodeAndThreedCode(@RequestParam String vehicleCode,@RequestParam String threedCode){
        return service.relayContactorLifeGroupListByVehicleCodeAndThreedCode(vehicleCode,threedCode);
    }





      /*=================================数字车辆===================================*/




    @PostMapping(value = "/mechanismAlarmRuleList")
    @ApiOperation(value = "根据构型获取机理规则list")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleCode", value = "车辆code", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "structureCode", value = "构型code", required = true, dataType = "String")
    })
    public Object mechanismAlarmRuleListByStruCode(@RequestParam String vehicleCode,@RequestParam String structureCode){
        return service.mechanismAlarmRuleListByStruCode(vehicleCode,structureCode);
    }


    @PostMapping(value = "/ekbFaultTypeList")
    @ApiOperation(value = "根据构型获取故障类型list")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleCode", value = "车辆code", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "structureCode", value = "构型code", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pageNumber", value = "分页页码", required = true, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "分页条数", required = true, dataType = "int")
    })
    public Object ekbFaultTypeListByStruCode(@RequestParam String vehicleCode,@RequestParam String structureCode,
                                             @RequestParam int pageNumber, @RequestParam int pageSize){
        return service.ekbFaultTypeListByStruCode(vehicleCode,structureCode,pageNumber,pageSize);
    }
    @PostMapping(value = "/mtrSoftWareList")
    @ApiOperation(value = "根据构型获取软件履历list")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleCode", value = "车辆code", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "structureCode", value = "构型code", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pageNumber", value = "分页页码", required = true, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "分页条数", required = true, dataType = "int")
    })
    public Object mtrSoftWareListByStruCode(@RequestParam String vehicleCode,@RequestParam String structureCode,
                                             @RequestParam int pageNumber, @RequestParam int pageSize){
        return service.mtrSoftWareListByStruCode(vehicleCode,structureCode,pageNumber,pageSize);
    }


    @PostMapping(value = "/relayContactorLifeList")
    @ApiOperation(value = "根据构型获取生命周期list（车辆构型-寿命预测）")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleCode", value = "车辆code", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "structureCode", value = "构型code", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pageNumber", value = "分页页码", required = true, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "分页条数", required = true, dataType = "int")
    })
    public Object relayContactorLifeListByStruCode(@RequestParam String vehicleCode,@RequestParam String structureCode,
                                            @RequestParam int pageNumber, @RequestParam int pageSize){
        return service.relayContactorLifeListByStruCode(vehicleCode,structureCode,pageNumber,pageSize);
    }

    /**
     * 根据构型获取生命周期list（车辆构型-履历管理-部件履历）
     * <AUTHOR>
     * 2021/12/15
     **/
    @PostMapping(value = "/componentListByStruCode")
    @ApiOperation(value = "根据构型获取生命周期list（车辆构型-履历管理-部件履历）")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleCode", value = "车辆code", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "structureCode", value = "构型code", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pageNumber", value = "分页页码", required = true, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "分页条数", required = true, dataType = "int")
    })
    public Object componentListByStruCode(@RequestParam String vehicleCode,@RequestParam String structureCode,
                                                   @RequestParam int pageNumber, @RequestParam int pageSize){
        return service.componentListByStruCode(vehicleCode,structureCode,pageNumber,pageSize);
    }

    @PostMapping(value = "/struCodeInfo")
    @ApiOperation(value = "根据三维模型code获取构型编码")
    public Object struCodeInfoByThreedCode(@RequestParam String threedCode,@RequestParam String vehicleCode){
        return service.struCodeInfoByThreedCode(threedCode,vehicleCode);
    }

    @PostMapping(value = "/buttonList")
    @ApiOperation(value = "根据构型获取可展开按钮list")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleCode", value = "车辆code", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "structureCode", value = "构型code", required = true, dataType = "String")
    })
    public Object buttonListByStruCode(@RequestParam String vehicleCode,@RequestParam String structureCode){
        return service.buttonListByStruCode(vehicleCode,structureCode);
    }


    /**
     * @return
     * @Description 根据构型id查询构型故障信息
     * <AUTHOR>
     * @Date 2021/12/15 9:30
     * @Param
     **/
    @SystemLog(optDesc = "车辆构型-根据车辆、构型、查询故障列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "车辆构型-根据车辆、构型、查询故障（包含累计发生次数）列表", notes = "车型管理-树结构-节点关联页面")
    @PostMapping(value = "/structureCode/faultCountList")
    public Object listMtcAutoFaultRecordForStru( @RequestBody MtcAlarmWarningVO mtcAlarmWarningVO){
        return  service.listMtcAutoFaultRecordForStru(mtcAlarmWarningVO);
    }

    /**
     * @return
     * @Description 根据构型id查询构型故障信息
     * <AUTHOR>
     * @Date 2021/12/15 9:30
     * @Param
     **/
    @SystemLog(optDesc = "车辆构型-根据车辆、构型、faultTypeKey查询单个故障 历史故障列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "车辆构型-根据车辆、构型、faultTypeKey查询单个故障 历史故障列表", notes = "车型管理-树结构-节点关联页面")
    @PostMapping(value = "/structureCode/faultTypeKey/faultList")
    public Object listMtcAutoFaultRecordForStruByFaulitTypeKey( @RequestBody MtcAlarmWarningVO mtcAlarmWarningVO){
        return  service.listMtcAutoFaultRecordForStruByFaulitTypeKey(mtcAlarmWarningVO);
    }



    @PostMapping(value = "/structureCode/mtrSoftWareList")
    @ApiOperation(value = "车辆构型-根据构型获取软件履历list")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleCode", value = "车辆code", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "structureCode", value = "构型code", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pageNumber", value = "分页页码", required = true, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "分页条数", required = true, dataType = "int")
    })
    public Object mtrSoftWareListByStruCodeLite(@RequestParam String vehicleCode,@RequestParam String structureCode,
                                            @RequestParam int pageNumber, @RequestParam int pageSize){
        return service.mtrSoftWareListByStruCodeLite(vehicleCode,structureCode,pageNumber,pageSize);
    }
    @PostMapping(value = "/structureCode/signalNameEn/mtrSoftWareList")
    @ApiOperation(value = "车辆构型-根据构型获取软件履历list")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleCode", value = "车辆code", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "structureCode", value = "构型code", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "signalNameEn", value = "信号英文名", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pageNumber", value = "分页页码", required = true, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "分页条数", required = true, dataType = "int")
    })
    public Object mtrSoftWareListByStruCodeAndSignalName(@RequestParam String vehicleCode,@RequestParam String structureCode,
                                                         @RequestParam String signalNameEn, @RequestParam int pageNumber, @RequestParam int pageSize){
        return service.mtrSoftWareListByStruCodeAndSignalName(vehicleCode,structureCode,signalNameEn,pageNumber,pageSize);
    }

}
