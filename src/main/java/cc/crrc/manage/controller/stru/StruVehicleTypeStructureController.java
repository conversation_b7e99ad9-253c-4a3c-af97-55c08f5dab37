package cc.crrc.manage.controller.stru;

import cc.crrc.manage.common.annotation.*;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.stru.StruVehicleTypeStructurePO;
import cc.crrc.manage.service.stru.StruVehicleTypeStructureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @FileName StruVehicleTypeStructureController
 * <AUTHOR> yuxi
 * @Date 2019/11/18 9:06
 * @Version 1.0
 **/
@Api(tags = "车辆型号-构型")
@RestController
@RequestMapping("/vehicleTypeStructure")
public class StruVehicleTypeStructureController {
    @Autowired
    private StruVehicleTypeStructureService service;

    /**
     * @return
     * @Description 车辆构型新增
     * <AUTHOR>
     * @Date 2019/11/18 9:30
     * @Param [struVehicleTypeStructurePO]
     **/
    @SystemLog(optDesc = "新增车辆构型", optType = SystemLogEnum.INSERT)
    @ApiOperation("新增车辆构型")
    @PostMapping("/")
    public Object addStruVehicleTypeStructure(@RequestBody @InsertValidated StruVehicleTypeStructurePO struVehicleTypeStructurePO) {
        return service.addStruVehicleTypeStructure(struVehicleTypeStructurePO);
    }

    /**
     * @return
     * @Description 车辆构型更新
     * <AUTHOR>
     * @Date 2019/11/18 9:44
     * @Param [struVehicleTypeStructurePO]
     **/
    @SystemLog(optDesc = "更新车辆构型信息", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "更新车辆构型信息", notes = "id不可为空，其余项目可选，不传不改。")
    @PutMapping(value = "/")
    public Object updateStruVehicleTypeStructure(@RequestBody @UpdateValidated StruVehicleTypeStructurePO struVehicleTypeStructurePO) {
        service.updateStruVehicleTypeStructure(struVehicleTypeStructurePO);
        return null;
    }

    /**
     * 车型构型树，懒加载，第二步
     * 查询单个车辆类型的构型树
     * 提供树节点查询功能
     * @param vehicleTypeId 车辆类型id
     * @param nodeName      搜索关键字
     * @return java.lang.Object
     * <AUTHOR> GuoYang
     * 2019/12/14
     **/
    @SystemLog(optDesc = "查询车型构型", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询车型构型", notes = "参数可以传车型id、节点名称，可传空")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleTypeId", value = "车辆型号id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "nodeName", value = "节点名称", dataType = "String")
    })
    @GetMapping(value = "/getStructureByVehicleType")
    public Object getStructureByVehicleType(@LogParam(description = "车辆型号id") @RequestParam String vehicleTypeId, @LogParam(description = "节点名称") String nodeName) {
        return service.treeListStruVehicleTypeStructure(vehicleTypeId, nodeName);
    }

    /**
     * @return java.lang.Object
     * @Description 根据构型编码查询故障类型信息
     * <AUTHOR>
     * @Date 2019/11/20 10:00
     * @Param [structureCode]
     **/
    @SystemLog(optDesc = "查询故障类型信息（根据构型编码）", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询故障类型信息（根据构型编码）")
    @GetMapping(value = "/structureCode")
    @ApiImplicitParam(paramType = "query", name = "structureCode", value = "构型编码", required = true, dataType = "String")
    public Object getFaultTypeInfoByStructureCode(@LogParam(description = "构型编码") @RequestParam String structureCode) {
        return service.getFaultTypeInfoByStructureCode(structureCode);
    }

    /**
     * @return StruVehicleTypeStructurePO
     * @Description 根据构型id查询构型信息
     * <AUTHOR>
     * @Date 2019/11/23 14:30
     * @Param [id]
     **/
    @SystemLog(optDesc = "查询构型信息（根据构型id）", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询构型信息（根据构型id）")
    @GetMapping(value = "/id")
    @ApiImplicitParam(paramType = "query", name = "id", value = "构型id", required = true, dataType = "String")
    public Object getStruVehicleTypeStructureById(@LogParam(description = "构型id") @RequestParam String id) {
        return service.getStruVehicleTypeStructureById(id);
    }

    /**
     * @return StruVehicleTypeStructurePO
     * @Description 根据车辆类型id vehicleTypeId查询 车厢
     * <AUTHOR>
     * @Date 2020/02/14 11:30
     * @Param [vehicleTypeId]
     **/
    @SystemLog(optDesc = "查询构型信息（根据构型id）", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询构型信息（车辆类型id）")
    @GetMapping(value = "/vehicleTypeId")
    @ApiImplicitParam(paramType = "query", name = "vehicleTypeId", value = "车辆类型id", required = true, dataType = "String")
    public Object getLocationByVehicleTypeId(@LogParam(description = "车辆类型id") @RequestParam String vehicleTypeId) {
        return service.getLocationByVehicleTypeId(vehicleTypeId);
    }

    /**
     *
     * <AUTHOR>
     * @Date 2020/06/12
     * @param sysFile
     * @param id
     */
    @SystemLog(optDesc = "新增3D效果图", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "新增3D效果图")
    @PostMapping("/add3DRenderings")
    @ApiImplicitParam(paramType = "query", name = "id", value = "车辆构型id", required = true, dataType = "String")
    public Object add3DRenderings(@RequestBody @InsertValidated SysFilePO sysFile, @RequestParam String id) {
        return service.add3DRenderings(sysFile,id);
    }
    /**
     *
     * <AUTHOR>
     * @Date 2020/06/12
     * @param id
     * @param fileId
     */
    @SystemLog(optDesc = "删除3D效果图", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "删除3D效果图")
    @PostMapping("/delete3DRenderings")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "fileId", value = "文件id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "id", value = "车型构型id", required = true, dataType = "String")
    })
    public Object delete3DRenderings(@RequestParam String id,@RequestParam String fileId) {
        return service.delete3DRenderings(id,fileId);
    }

    /**
     * <AUTHOR>
     * @Date 2020/06/22
     *
     */
    @SystemLog(optDesc = "替换3D效果图", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "替换3D效果图")
    @PostMapping("/replace3DRenderings")
    @ApiImplicitParam(paramType = "query", name = "id", value = "车辆构型id", required = true, dataType = "String")
    public Object replace3DRenderings(@RequestBody @InsertValidated SysFilePO sysFile, @RequestParam String id, @RequestParam String fileId){
        return service.replace3DRenderings(sysFile,id,fileId);
    }
}
