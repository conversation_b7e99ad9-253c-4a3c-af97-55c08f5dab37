package cc.crrc.manage.controller;

import cc.crrc.manage.cache.RefreshCache;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.BusDict;
import cc.crrc.manage.pojo.BusDictQuery;
import cc.crrc.manage.service.BusDictService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Api(tags = "业务字典")
@RestController
@RequestMapping("/busDict")
@Validated
public class BusDictController {

    @Autowired
    private BusDictService busDictService;

    /**
     * 查询业务字典列表
     */
    @SystemLog(optDesc = "查询列表业务字典", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询业务字典")
    @GetMapping("/configuration/dicts")
    public Object list(BusDictQuery busDictQuery) {
        return busDictService.selectBusDictList(busDictQuery);
    }

    /**
     * 获取业务字典详细信息
     */
    @SystemLog(optDesc = "typeCode查询业务字典", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "主键查询业务字典")
    @GetMapping(value = "/configuration/dict")
    public Object getInfo(@RequestParam("typeCode") String typeCode, @RequestParam("lineId") String lineId) {
        return busDictService.selectBusDictByCode(typeCode, lineId);
    }

    /**
     * 新增业务字典
     */
    @SystemLog(optDesc = "添加业务字典", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "添加业务字典")
    @PostMapping("/configuration/dict")
    @RefreshCache(values = "BUS_DICT")
    public Object add(@RequestBody BusDict busDict) {
        return busDictService.insertBusDict(busDict);
    }

    /**
     * 修改业务字典
     */
    @SystemLog(optDesc = "更新业务字典", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "更新业务字典")
    @RefreshCache(values = "BUS_DICT")
    @PutMapping("/configuration/dict")
    public Object edit(@RequestBody BusDict busDict) {
        return busDictService.updateBusDict(busDict);
    }

    /**
     * 删除业务字典
     */
    @SystemLog(optDesc = "删除业务字典", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "删除业务字典")
    @RefreshCache(values = "BUS_DICT")
    @DeleteMapping("/configuration/dict")
    public Object remove(@RequestParam String id) {
        return busDictService.deleteBusDictById(id);
    }

    /**
     * @Description 查询字典类型字典通用接口
     * <AUTHOR>
     * @Date 2022/11/24
     **/
    @ApiOperation(value = "查询字典通用接口")
    @PostMapping("/configuration/dict/listDictUniversal")
    public Object listDictUniversal(@RequestBody BusDictQuery busDictQuery) {
        return busDictService.listDictUniversal(busDictQuery);
    }

}
