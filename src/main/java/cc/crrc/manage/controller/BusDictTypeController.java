package cc.crrc.manage.controller;

import cc.crrc.manage.common.annotation.InsertValidated;
import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.BusDictType;
import cc.crrc.manage.pojo.BusDictTypeQuery;
import cc.crrc.manage.service.BusDictTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@Api(tags = "业务字典类型")
@RestController
@RequestMapping("/busDict")
public class BusDictTypeController {

    @Autowired
    private BusDictTypeService busDictTypeService;

    /**
     * 查询业务字典类型列表
     */
    @SystemLog(optDesc = "查询列表业务字典类型", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询业务字典类型")
    @GetMapping("/configuration/dict/types")
    public Object list(BusDictTypeQuery busDictTypeQuery) {
        return busDictTypeService.selectBusDictTypeList(busDictTypeQuery);
    }

    /**
     * 新增业务字典类型
     */
    @SystemLog(optDesc = "添加业务字典类型", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "添加业务字典类型")
    @PostMapping("/configuration/dict/type")
    public Object add(@InsertValidated @RequestBody BusDictType busDictType) {
        return busDictTypeService.insertBusDictType(busDictType);
    }

    /**
     * 修改业务字典类型
     */
    @SystemLog(optDesc = "更新业务字典类型", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "更新业务字典类型")
    @PutMapping("/configuration/dict/type")
    public Object edit(@UpdateValidated @RequestBody BusDictType busDictType) {
        return busDictTypeService.updateBusDictType(busDictType);
    }

    /**
     * 删除业务字典类型
     */
    @SystemLog(optDesc = "删除业务字典类型", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "删除业务字典类型")
    @DeleteMapping("/configuration/dict/type/{id}")
    public Object remove(@PathVariable String id) {
        return busDictTypeService.deleteBusDictTypeByIds(id);
    }

}
