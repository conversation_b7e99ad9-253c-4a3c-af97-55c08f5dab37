package cc.crrc.manage.controller;

import cc.crrc.manage.common.annotation.LogParam;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.SysRoleVO;
import cc.crrc.manage.service.SysRoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Api(tags = "角色管理")
@RestController
@RequestMapping(value = "/sysRole")
public class SysRoleController {
    @Autowired
    private SysRoleService service;

    /**
     * @Description 通过roletype查询角色
     * <AUTHOR>
     * @Date 2019/6/12
     * @Param [roleInfo]
     * 更新：
     * @Date 2019/10/23
     * 开会讨论暂时不需要角色类型 代码注释 以备以后留用
     **/
    @SystemLog(optDesc = "通过roletype查询角色", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "通过roletype查询角色")
    @PostMapping("/getSysRoleByRoleType")
    public Object getSysRoleByRoleType(@RequestBody SysRoleVO sysRoleVO) {
        return service.getSysRoleByRoleType(sysRoleVO);
    }

    /**
     * @Description 通过data_permission查询角色
     * <AUTHOR>
     * @Date 2019/10/23
     * @Param data_permission
     **/
    @SystemLog(optDesc = "查询角色", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "通过data_permission查询角色")
    @PostMapping("/getSysRoleByDataPermission")
    public Object getSysRoleByDataPermission(@RequestBody SysRoleVO sysRoleVO) {
        return service.getSysRoleByDataPermission(sysRoleVO);
    }


    /**
     * @Description 查询角色
     * <AUTHOR>
     * @Date 2019/7/8
     * @Param [roleInfo]
     **/
    @SystemLog(optDesc = "查询角色", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询角色", notes = "不传值查询所有角色 传roleType则进行定向查询 如果传了currentPage，pageSize则返回分页类型 ")
    @PostMapping("/getSysRole")
    public Object getSysRole(@RequestBody SysRoleVO sysRoleVO) {
        return service.getSysRole(sysRoleVO);
    }


    /**
     * @Description 添加角色
     * <AUTHOR>
     * @Date 2019/6/12
     * @Param [roleInfo]
     **/
    @SystemLog(optDesc="增加角色", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "添加角色")
    @PostMapping("/saveSysRoleInfo")
    public Object saveSysRoleInfo(@Valid @RequestBody SysRoleVO sysRoleVO) {
        return service.saveSysRoleInfo(sysRoleVO);
    }

    /**
     * @Description 更新角色
     * <AUTHOR>
     * @Date 2019/6/12
     * @Param [roleInfo]
     **/
    @SystemLog(optDesc="修改角色", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "更新角色")
    @PutMapping("/updateSysRoleInfo")
    public Object updateSysRoleInfo(@Valid @RequestBody SysRoleVO sysRoleVO) {
        return service.updateSysRoleInfo(sysRoleVO);
    }

    /**
     * @Description 删除角色
     * <AUTHOR>
     * @Date 2019/6/12
     * @Param [roleInfo]
     **/
    @SystemLog(optDesc="删除角色", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "删除角色")
    @DeleteMapping("/deleteSysRoleInfo")
    public Object deleteSysRoleInfo(@LogParam(description = "角色id")@RequestParam String id) {
        return service.deleteSysRoleInfo(id);
    }

    /**
     * @Description 根据id查询角色信息
     * <AUTHOR>
     * @Date 2019/6/12
     * @Param [roleInfo]
     **/
    @SystemLog(optDesc = "根据id查询角色信息", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据id查询角色信息")
    @GetMapping("/getSysRoleInfoById")
    public Object getSysRoleInfoById(@LogParam(description = "角色id")@RequestParam String id) {
        return service.getSysRoleInfoById(id);
    }


    /**
     * @Description 根据id查询 角色对应的菜单权限
     * <AUTHOR>
     * @Date 2019/7/8
     * @Param [roleInfo]
     **/
    @SystemLog(optDesc = "根据id 角色对应的菜单权限", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "id 角色对应的菜单权限")
    @PostMapping("/getSysRoleMenuById")
    public Object getSysRoleMenuById(@LogParam(description = "角色id")@RequestParam String id) {
        return service.getSysRoleMenuById(id);
    }
    
   
    /**
     * 
     * @Title: getUserDataPermission   
     * @Description: 获取当前DataPermission
     * @param: @return      
     * @return: Object
     * @date:   2020年8月18日 上午9:47:03  
     * @author: Heshenglun   
     * @throws
     */
    @SystemLog(optDesc = "获取当前用户DataPermission", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "获取当前用户的DataPermission")
    @PostMapping("/getUserDataPermission")
    public Object getUserDataPermission() {
        return service.getUserMaxDataPermission();
    }
    

}
