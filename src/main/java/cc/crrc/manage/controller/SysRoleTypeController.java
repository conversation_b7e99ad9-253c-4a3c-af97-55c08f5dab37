package cc.crrc.manage.controller;


import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.SysRoleTypeVO;
import cc.crrc.manage.service.SysRoleTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "角色类型管理")
@RestController
@RequestMapping(value = "/sysRoleType")
public class SysRoleTypeController {

    @Autowired
    private SysRoleTypeService service;

    /**
     * @Author: lixin
     * @Date: 2019/10/23
     * 开会讨论 角色类型模块暂无使用场景 暂时抹除
     */


    /**
     * @Description: 新增 系统角色类型
     * @param: sysRoleTypeVO
     * @returns: java.lang.String
     * @Author: lixin
     * @Date: 2019/6/4
     */
    @SystemLog(optDesc="新增角色类型", optType = SystemLogEnum.INSERT)
    @PostMapping("/saveSysRoleType")
    @ApiOperation("新增 系统角色类型")
    public Object saveSysRoleType(@RequestBody SysRoleTypeVO sysRoleTypeVO) {
        return service.saveSysRoleType(sysRoleTypeVO);
    }

    /**
     * @Description: 删除 系统角色类型
     * @param: sysRoleTypeVO.id
     * @returns: java.lang.Object
     * @Author: lixin
     * @Date: 2019/6/5
     */
    @SystemLog(optDesc="删除角色类型", optType = SystemLogEnum.DELETE)
    @DeleteMapping("/deleteSysRoleTypeById")
    @ApiOperation("删除 系统角色类型")
    public Object deleteSysRoleTypeById(@RequestBody SysRoleTypeVO sysRoleTypeVO) {
        return service.deleteSysRoleTypeById(sysRoleTypeVO);
    }

    /**
     * @Description: 修改 系统角色类型
     * @param: sysRoleTypeVO
     * @returns: java.lang.Object
     * @Author: lixin
     * @Date: 2019/6/5
     */
    @SystemLog(optDesc="更新角色类型", optType = SystemLogEnum.UPDATE)
    @PutMapping("/updateSysRoleTypeById")
    @ApiOperation("修改 系统角色类型")
    public Object updateSysRoleTypeById(@RequestBody SysRoleTypeVO sysRoleTypeVO) {
        return service.updateSysRoleTypeById(sysRoleTypeVO);
    }

    /**
     * @Description: 查询 系统角色类型
     * @param: sysRoleTypeVO
     * @returns: java.lang.Object
     * @Author: lixin
     * @Date: 2019/6/5
     */
    @PostMapping("/selectSysRoleType")
    @ApiOperation(value = "查询 系统角色类型 ", notes = "如果传了currentPage，pageSize则返回分页类型 若没传currentPage，pageSize则返回list")
    public Object selectSysRoleType(@RequestBody SysRoleTypeVO sysRoleTypeVO) {
        return service.selectSysRoleType(sysRoleTypeVO);
    }

    /**
     * @Description: 查询 系统角色类型
     * @param: sysRoleTypeVO
     * @returns: java.lang.Object
     * @Author: lixin
     * @Date: 2019/6/11
     */
    @PostMapping("/selectSysRoleTypeById")
    @ApiOperation(value = "查询 系统角色类型byId ", notes = "查询 系统角色类型byId")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "id", required = true, dataType = "String")
    })
    public Object selectSysRoleTypeById(SysRoleTypeVO sysRoleTypeVO) {
        return service.selectSysRoleTypeById(sysRoleTypeVO);
    }

}
