package cc.crrc.manage.controller;

import cc.crrc.manage.common.annotation.InsertValidated;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.SysLocationVO;
import cc.crrc.manage.service.SysAreaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @ClassName SysAreaController
 * <AUTHOR> ming<PERSON>an
 * @Date 2020/8/3 19:56
 * @Version 1.0
 **/
@Api(tags = "区域管理")
@RestController
@RequestMapping("/sysArea")
public class SysAreaController {
    @Autowired
    private SysAreaService areaService;

    @SystemLog(optDesc = "新建区域", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "新建区域 ", notes = "新建区域")
    @PostMapping(value = "/saveSysArea")
    public Object saveSysArea(@RequestBody @InsertValidated SysLocationVO sysLocationVO) {
        return areaService.saveSysArea(sysLocationVO);
    }

    @SystemLog(optDesc = "删除区域", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "删除区域 ", notes = "删除区域")
    @DeleteMapping(value = "/deleteSysArea")
    public Object deleteSysArea(@RequestParam("id") String id) {
        return areaService.deleteSysArea(id);
    }

    @SystemLog(optDesc = "修改区域信息", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "修改区域信息")
    @PutMapping("/updateSysArea")
    public Object updateSysArea(@RequestBody @Valid SysLocationVO sysLocationVO) {
        return areaService.updateSysArea(sysLocationVO);
    }

    @SystemLog(optDesc = "分页查询区域列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "分页查询区域列表", notes = "分页查询区域列表")
    @GetMapping(value = "/getSysArea")
    public Object getSysArea(SysLocationVO sysLocationVO) {
        return areaService.getSysArea(sysLocationVO);
    }

    @SystemLog(optDesc = "查询市级列表(带模糊查询)", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询市级列表(带模糊查询)", notes = "查询市级列表(带模糊查询)")
    @GetMapping(value = "/getCityList")
    public Object getCityList(@RequestParam("areaName") String areaName) {
        return areaService.getCityList(areaName);
    }

    @SystemLog(optDesc = "查询当前用户的区域列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询当前用户的区域列表", notes = "查询当前用户的区域列表")
    @GetMapping(value = "/location/list")
    public Object getLocationList() {
        return areaService.getLocationList();
    }

    @SystemLog(optDesc = "查询当前区域下的线路列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询当前区域下的线路列表", notes = "查询当前区域下的线路列表")
    @ApiImplicitParam(value = "区域id", name = "locationId", required = true, dataType = "String", paramType = "query")
    @GetMapping(value = "/line/list/locationId")
    public Object getLineList(@RequestParam String locationId) {
        return areaService.getLineList(locationId);
    }

    @SystemLog(optDesc = "查询当前线路下的车型列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询当前线路下的车型列表", notes = "查询当前线路下的车型列表")
    @ApiImplicitParam(value = "线路id", name = "lineId", required = true, dataType = "String", paramType = "query")
    @GetMapping(value = "/vehicleType/list/lineId")
    public Object getVehicleTypeList(@RequestParam String lineId) {
        return areaService.getVehicleTypeList(lineId);
    }

    @SystemLog(optDesc = "查询车辆列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询车辆列表", notes = "查询车辆列表")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "线路id", name = "lineId", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(value = "车型id", name = "vehicleTypeId", dataType = "String", paramType = "query")
    })
    @GetMapping(value = "/vehicle/list/lineId/vehicleTypeId")
    public Object getVehicleList(@RequestParam String lineId, String vehicleTypeId) {
        return areaService.getVehicleList(lineId, vehicleTypeId);
    }
}
