package cc.crrc.manage.controller.ekb;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.ekb.EkbFaultSnowSlideDTO;
import cc.crrc.manage.pojo.ekb.EkbSnowSlideRelationDTO;
import cc.crrc.manage.service.ekb.EkbFaultSnowSlideService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "故障类型-故障雪崩操作")
@RestController
@RequestMapping(value = "/ekbFaultType/ekbFaultSnowSlide")
public class EkbFaultSnowSlideController {

    @Autowired
    private EkbFaultSnowSlideService ekbFaultSnowSlideService;

    /**
     * 查询当前节点故障雪崩网
     * @param faultTypeKey
     * @param nameCn
     * @return
     */
    @SystemLog(optDesc = "查看当前故障雪崩关系", optType = SystemLogEnum.SELECT)
    @ApiOperation("查看当前故障雪崩关系")
    @GetMapping("/findFaultSnowSlideByKey")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "faultTypeKey", value = "当前故障业务主键", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "nameCn", value = "当前故障中文名称", required = true, dataType = "String")
    })
    public Object findFaultSnowSlideByKey(@RequestParam String faultTypeKey,@RequestParam String nameCn){
        return ekbFaultSnowSlideService.findFaultSnowSlideByKey(faultTypeKey,nameCn);
    }


    @SystemLog(optDesc = "保存当前故障雪崩关系", optType = SystemLogEnum.INSERT)
    @ApiOperation("保存当前故障雪崩关系")
    @PostMapping("/saveFaultSnowSlideRelation")
    public Object saveFaultSnowSlideRelation(@RequestBody EkbSnowSlideRelationDTO ekbSnowSlideRelationDTO){
        return ekbFaultSnowSlideService.saveFaultSnowSlideRelation(ekbSnowSlideRelationDTO);
    }

    @SystemLog(optDesc = "启用/禁用当前雪崩抑制规则", optType = SystemLogEnum.UPDATE)
    @ApiOperation("启用/禁用当前雪崩抑制规则")
    @PutMapping("/enable")
    public Object changeEnableStatus(String faultTypeKey, Boolean enable, String vehicleTypeId) {
        ekbFaultSnowSlideService.changeEnableStatus(faultTypeKey, enable, vehicleTypeId);
        return null;
    }

    @SystemLog(optDesc = "当前故障雪崩关系校验", optType = SystemLogEnum.SELECT)
    @ApiOperation("当前故障雪崩关系校验")
    @PostMapping("/validSnowSlideRelation")
    public Object validSnowSlideRelation(@RequestBody EkbSnowSlideRelationDTO ekbSnowSlideRelationDTO){
        ekbFaultSnowSlideService.validSnowSlideRelation(ekbSnowSlideRelationDTO);
        return null;
    }

    @SystemLog(optDesc = "获取雪崩关系网列表", optType = SystemLogEnum.SELECT)
    @ApiOperation("获取雪崩关系网列表")
    @PostMapping("/findFaultSnowSlideRelations")
    public Object findFaultSnowSlideRelations(@RequestBody EkbFaultSnowSlideDTO ekbFaultSnowSlideDTO){
        return ekbFaultSnowSlideService.findFaultSnowSlideRelations(ekbFaultSnowSlideDTO);
    }


}
