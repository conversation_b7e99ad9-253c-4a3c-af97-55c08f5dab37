package cc.crrc.manage.controller.ekb;


import cc.crrc.manage.common.annotation.InsertValidated;
import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.ekb.EkbFaultMeasurePO;
import cc.crrc.manage.pojo.ekb.EkbFaultMeasureVO;
import cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO;
import cc.crrc.manage.pojo.ekb.EkbReasonMeasurePO;
import cc.crrc.manage.service.ekb.EkbFaultMeasureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @FileName EkbFaultMeasureController
 * <AUTHOR> yuxi
 * @Date 2019/11/9 10:02
 * @Version 1.0
 **/
@Api(tags = "故障解决措施")
@RestController
@RequestMapping("/faultMeasure")
public class EkbFaultMeasureController {
    @Autowired
    EkbFaultMeasureService faultMeasureService;

    @SystemLog(optDesc = "查询故障措施列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "分页查询故障措施列表")
    @PostMapping("/faultMeasure/page")
    public Object listFaultMeasure(@RequestBody EkbFaultMeasureVO faultMeasure) {
        return faultMeasureService.listFaultMeasure(faultMeasure);
    }

    /**
     * @param keyword
     * @return
     * <AUTHOR>
     */
    @SystemLog(optDesc = "查询故障措施列表（通过措施编码、措施类别、措施内容同时模糊查询）", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "按照措施编码、措施类别、措施内容同时模糊查询")
    @ApiImplicitParam(paramType = "query", name = "keyword", value = "关键字（“措施编码”或“措施类别”或“措施内容”）", required = false, dataType = "String")
    @GetMapping("/list/keyword")
    public Object listFaultMeasureVaguely(@RequestParam(required = false) String id, String keyword) {
        return faultMeasureService.listFaultMeasureVaguely(id, keyword);
    }

    @SystemLog(optDesc = "增加故障措施", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "新增故障措施")
    @PostMapping("/faultMeasure")
    public Object insertFaultMeasure(@RequestBody @InsertValidated EkbFaultMeasurePO faultMeasure) {
        return faultMeasureService.insertFaultMeasure(faultMeasure);
    }

    @SystemLog(optDesc = "更新故障措施", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "更新故障措施")
    @PutMapping("/faultMeasure")
    public Object updateFaultMeasure(@RequestBody @UpdateValidated EkbFaultMeasurePO faultMeasure) {
        return faultMeasureService.updateFaultMeasure(faultMeasure);
    }

    @SystemLog(optDesc = "删除故障措施", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "删除故障措施")
    @DeleteMapping("/faultMeasure")
    @ApiImplicitParam(paramType = "query", name = "ids", value = "多个故障措施id，用逗号分隔", required = true, dataType = "String")
    public Object deleteFaultMeasure(@RequestParam String ids) {
        return faultMeasureService.deleteFaultMeasure(ids);
    }

    @ApiOperation(value = "删除故障原因和故障措施关系（测试用）", hidden = true)
    @DeleteMapping("/reasonMeasure")
    public Object deleteReasonMeasure(@RequestParam(required = false) String faultTypeKey,
                                      @RequestParam(required = false) String faultReasonKey,
                                      @RequestParam(required = false) String faultMeasureKey) {
        faultMeasureService.deleteReasonMeasure(faultTypeKey, faultReasonKey, faultMeasureKey);
        return null;
    }

    @ApiOperation(value = "更新故障原因和故障措施关系实际计数realCounter", notes = "故障类型业务主键、故障原因业务主键、故障措施业务主键必须都不为空！")
    @PutMapping("/reasonMeasure/realCounter")
    public Object updateReasonMeasureRealCounter(@RequestBody EkbReasonMeasurePO reasonMeasure) {
        return faultMeasureService.updateReasonMeasureRealCounter(reasonMeasure);
    }

    @ApiOperation(value = "取得故障原因和故障措施关系（测试用）", notes = "开发测试用", hidden = true)
    @GetMapping("/reasonMeasure")
    public Object getReasonMeasure(@RequestParam(required = false) String faultTypeKey,
                                   @RequestParam(required = false) String faultReasonKey,
                                   @RequestParam(required = false) String faultMeasureKey) {
        return faultMeasureService.getReasonMeasure(faultTypeKey, faultReasonKey, faultMeasureKey);
    }

    @SystemLog(optDesc = "查询故障知识库", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "查询故障知识库")
    @PostMapping("/expertKnowledgeBase")
    public Object listExpertKnowledgeBase(@RequestBody EkbFaultTypeDTO faultType) {
        return faultMeasureService.listExpertKnowledgeBase(faultType);
    }
}
