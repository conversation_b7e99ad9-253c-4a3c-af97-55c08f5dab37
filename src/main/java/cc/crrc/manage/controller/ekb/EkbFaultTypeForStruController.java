package cc.crrc.manage.controller.ekb;


import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.service.ekb.EkbFaultTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @FileName MtcFaultReportController
 * <AUTHOR>
 * @Date 2019/12/19 10:02
 * @Version 1.0
 **/
@Api(tags = "车型管理-树结构-节点关联页面-故障类型")
@RestController
@RequestMapping("/ekbFaultTypeForStru")
public class EkbFaultTypeForStruController {
    @Autowired
    private EkbFaultTypeService ekbFaultTypeService;


    /**
     * @return
     * @Description 查询故障类型关联文件列表
     * <AUTHOR>
     * @Date 2019/12/19
     **/
    @SystemLog(optDesc = "查询车型的故障字典", optType = SystemLogEnum.SELECT)
    @ApiOperation("查询车型的故障字典")
    @GetMapping("/faultTypeForVehicleType/list")

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "structureCode", value = "车辆构型结构编码structureCode", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "vehicleTypeId", value = "车辆型号vehicleTypeId", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pageNumber", value = "页码", required = true,  dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "页大小", required = true,  dataType = "int")
    })
    public Object getEkbFaultTypeForVehicleTypeList(@RequestParam String structureCode, @RequestParam String vehicleTypeId, @RequestParam int pageNumber, @RequestParam int pageSize) {
        return ekbFaultTypeService.getEkbFaultTypeForVehicleTypeList(structureCode,vehicleTypeId,pageNumber,pageSize);
    }
}
