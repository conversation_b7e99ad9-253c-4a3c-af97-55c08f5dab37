package cc.crrc.manage.controller.ekb;

import cc.crrc.manage.common.annotation.InsertValidated;
import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.ekb.EkbFaultReasonDTO;
import cc.crrc.manage.service.ekb.EkbFaultReasonService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @FileName EkbFaultReasonController
 * <AUTHOR>
 * @Date 2019/11/11
 * @Version 1.0
 **/
@Api(tags = "故障原因")
@RestController
@RequestMapping("/ekbFaultReason")
public class EkbFaultReasonController {
    @Autowired
    private EkbFaultReasonService ekbFaultReasonService;


    /**
     * @return
     * @Description 故障原因新增
     * <AUTHOR>
     * @Date 2019/11/11
     * @Param [ekbFaultReasonDTO]
     **/
    @SystemLog(optDesc = "故障原因新增", optType = SystemLogEnum.INSERT)
    @ApiOperation("故障原因新增")
    @PostMapping("/addEkbFaultReason")
    public Object addEkbFaultReason(@RequestBody @InsertValidated EkbFaultReasonDTO ekbFaultReasonDTO) {
        return ekbFaultReasonService.addEkbFaultReason(ekbFaultReasonDTO);
    }


    /**
     * @return
     * @Description 故障原因删除
     * <AUTHOR>
     * @Date 2019/11/11
     * @Param [id]
     **/
    @SystemLog(optDesc = "故障原因删除", optType = SystemLogEnum.DELETE)
    @ApiOperation("故障原因删除")
    @DeleteMapping("/deleteEkbFaultReason")
    @ApiImplicitParam(paramType = "query", name = "ids", value = "多个故障原因id，用逗号分隔", required = true, dataType = "String")
    public Object deleteEkbFaultReason(@RequestParam String ids) {
        return ekbFaultReasonService.deleteEkbFaultReason(ids);
    }


    /**
     * @return
     * @Description 故障原因修改
     * <AUTHOR>
     * @Date 2019/11/11
     * @Param [ekbFaultReasonDTO]
     **/
    @SystemLog(optDesc = "故障原因修改", optType = SystemLogEnum.UPDATE)
    @ApiOperation("故障原因修改")
    @PutMapping("/updateEkbFaultReason")
    public Object updateEkbFaultReason(@RequestBody @UpdateValidated EkbFaultReasonDTO ekbFaultReasonDTO) {
        return ekbFaultReasonService.updateEkbFaultReason(ekbFaultReasonDTO);
    }

    /**
     * @return java.util.List<cc.crrc.manage.pojo.ekb.EkbFaultReasonDTO>
     * @Description 查询故障原因列表分页
     * <AUTHOR>
     * @Date 2019/11/11
     * @Param [ekbFaultReasonDTO]
     **/
    @SystemLog(optDesc = "查询故障原因列表分页", optType = SystemLogEnum.SELECT)
    @ApiOperation("查询故障原因列表分页")
    @PostMapping("/listEkbFaultReason")
    public Object listEkbFaultReason(@RequestBody EkbFaultReasonDTO ekbFaultReasonDTO) {
        return ekbFaultReasonService.listEkbFaultReason(ekbFaultReasonDTO);
    }

    /**
     * @return
     * @Description 更新故障原因和故障类型关系realCounter
     * <AUTHOR>
     * @Date 2019/11/18
     * @Param []
     **/
    @SystemLog(optDesc = "更新故障原因和故障类型关系realCounter", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "更新故障原因和故障类型关系realCounter")
    @PutMapping("/faultTypeReason/realCounter")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "faultTypeKey", required = true, dataType = "String"),
            @ApiImplicitParam(name = "faultReasonKey", required = true, dataType = "String"),
            @ApiImplicitParam(name = "realCounter", required = true, dataType = "Long")
    })
    public Object updateTypeReasonRealCounter(@RequestParam String faultTypeKey, @RequestParam String faultReasonKey, @RequestParam Long realCounter) {
        return ekbFaultReasonService.updateTypeReasonRealCounter(faultTypeKey, faultReasonKey, realCounter);
    }

    /**
     * @return java.util.List<cc.crrc.manage.pojo.ekb.EkbFaultReasonDTO>
     * @Description 查询故障原因列表 提供模糊查询
     * <AUTHOR>
     * @Date 2019/11/25
     * @Param [ekbFaultReasonDTO]
     * 
     * MITE changed
     **/
    @SystemLog(optDesc = "查询故障原因列表 提供模糊查询", optType = SystemLogEnum.SELECT)
    @ApiOperation("查询故障原因列表 提供模糊查询")
    @GetMapping("/listEkbFaultReasonVaguely")
    @ApiImplicitParam(paramType = "query", name = "keyword", value = "关键字（“原因编码”或“原因类别”或“原因内容”）", required = false, dataType = "String")
    public Object listEkbFaultReasonVaguely(@RequestParam(required = false) String id, String keyword) {
        return ekbFaultReasonService.listEkbFaultReasonVaguely(id, keyword);
    }

}
