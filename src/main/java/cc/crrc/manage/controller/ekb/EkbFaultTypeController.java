package cc.crrc.manage.controller.ekb;

import cc.crrc.manage.cache.RefreshCache;
import cc.crrc.manage.common.annotation.InsertValidated;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.ekb.EkbFaultMeasureVO;
import cc.crrc.manage.pojo.ekb.EkbFaultReasonDTO;
import cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO;
import cc.crrc.manage.service.ekb.EkbFaultTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @FileName EkbFaultTypeController
 * <AUTHOR>
 * @Date 2019/11/9
 * @Version 1.0
 **/
@Api(tags = "故障类型")
@RestController
@RequestMapping("/ekbFaultType")
public class EkbFaultTypeController {

    @Autowired
    private EkbFaultTypeService ekbFaultTypeService;


    /**
     * @return java.util.List<cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO>
     * @Description 故障类型新增
     * <AUTHOR>
     * @Date 2019/11/09
     * @Param [ekbFaultTypeVO]
     **/
    @SystemLog(optDesc = "故障类型新增", optType = SystemLogEnum.INSERT)
    @ApiOperation("故障类型新增")
    @PostMapping("/addEkbFaultType")
    @RefreshCache(values = "EKB_FAULT_TYPE")
    public Object addEkbFaultType(@RequestBody @InsertValidated EkbFaultTypeDTO ekbFaultTypeDTO) {
        return ekbFaultTypeService.addEkbFaultType(ekbFaultTypeDTO);
    }

    /**
     * @return
     * @Description 故障类型删除
     * <AUTHOR>
     * @Date 2019/11/09
     * @Param [id]
     **/
    @SystemLog(optDesc = "故障类型删除", optType = SystemLogEnum.DELETE)
    @ApiOperation("故障类型删除")
    @DeleteMapping("/deleteEkbFaultType")
    @RefreshCache(values = "EKB_FAULT_TYPE")
    @ApiImplicitParam(paramType = "query", name = "ids", value = "多个故障类型id，用逗号分隔", required = true, dataType = "String")
    public Object deleteEkbFaultType(@RequestParam String ids) {
        return ekbFaultTypeService.deleteEkbFaultType(ids);
    }

    /**
     * @return
     * @Description 故障类型修改
     * <AUTHOR>
     * @Date 2019/11/09
     * @Param [ekbFaultTypeVO]
     * <p>
     * MITE changed
     **/
    @SystemLog(optDesc = "故障类型修改", optType = SystemLogEnum.UPDATE)
    @ApiOperation("故障类型修改")
    @PutMapping("/updateEkbFaultType")
    @RefreshCache(values = "EKB_FAULT_TYPE")
    public Object updateEkbFaultType(@RequestBody @UpdateValidated EkbFaultTypeDTO ekbFaultTypeDTO) {
        return ekbFaultTypeService.updateEkbFaultType(ekbFaultTypeDTO);
    }

    /**
     * @param faultReasonList
     * @param faultTypeKey
     * @return
     * <AUTHOR>
     */
    @SystemLog(optDesc = "绑定故障原因", optType = SystemLogEnum.UPDATE)
    @ApiOperation("绑定故障原因")
    @PostMapping("/saveFaultTypeReason")
    public Object saveFaultTypeReason(@RequestBody List<EkbFaultReasonDTO> faultReasonList, @RequestParam String faultTypeKey) {
        return ekbFaultTypeService.saveFaultTypeReason(faultReasonList, faultTypeKey);
    }

    /**
     * @param ekbFaultMeasureList
     * @param faultReasonKey
     * @param faultTypeKey
     * @return
     * <AUTHOR>
     */
    @SystemLog(optDesc = "绑定故障措施", optType = SystemLogEnum.UPDATE)
    @ApiOperation("绑定故障措施")
    @PostMapping("/saveReasonMeasure")
    public Object saveReasonMeasure(@RequestBody List<EkbFaultMeasureVO> ekbFaultMeasureList, @RequestParam String faultReasonKey, @RequestParam String faultTypeKey) {
        return ekbFaultTypeService.saveReasonMeasure(ekbFaultMeasureList, faultReasonKey, faultTypeKey);
    }

    /**
     * @return java.util.List<cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO>
     * @Description 分页取得故障类型列表
     * <AUTHOR>
     * @Date 2019/11/09
     * @Param [ekbFaultTypeVO]
     **/
    @SystemLog(optDesc = "查询故障类型列表分页", optType = SystemLogEnum.SELECT)
    @ApiOperation("分页取得故障类型列表")
    @PostMapping("/listEkbFaultType")
    public Object listEkbFaultType(@RequestBody EkbFaultTypeDTO ekbFaultTypeDTO) {
        return ekbFaultTypeService.listEkbFaultType(ekbFaultTypeDTO);
    }

    /**
     * @return
     * @Description 新增故障类型关联文件
     * <AUTHOR>
     * @Date 2019/11/15
     * @Param [sysFilePO, faultTypeKey]
     **/
    @SystemLog(optDesc = "新增故障类型关联文件", optType = SystemLogEnum.INSERT)
    @ApiOperation("新增故障类型关联文件")
    @PostMapping("/file")
    @ApiImplicitParam(paramType = "query", name = "id", value = "故障类型业务主键faultTypeKey", required = true, dataType = "String")
    public Object saveFaultTypeFile(@RequestBody @InsertValidated SysFilePO sysFile, @RequestParam String id) {
        return ekbFaultTypeService.saveFaultTypeFile(sysFile, id);
    }

    /**
     * @return
     * @Description 删除故障类型关联文件
     * <AUTHOR>
     * @Date 2019/11/15
     * @Param [faultTypeKey]
     **/
    @SystemLog(optDesc = "删除故障类型关联文件", optType = SystemLogEnum.DELETE)
    @ApiOperation("删除故障类型关联文件")
    @DeleteMapping("/file")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "id", value = "故障类型业务主键faultTypeKey", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "fileId", value = "文件id", required = true, dataType = "String")
    })
    public Object deleteEkbFaultTypeFile(@RequestParam String id, @RequestParam String fileId) {
        return ekbFaultTypeService.deleteEkbFaultTypeFile(id, fileId);
    }

    /**
     * @return
     * @Description 查询故障类型关联文件列表
     * <AUTHOR>
     * @Date 2019/11/15
     * @Param [faultTypeKey]
     **/
    @SystemLog(optDesc = "查询故障类型关联文件列表", optType = SystemLogEnum.SELECT)
    @ApiOperation("查询故障类型关联文件列表")
    @GetMapping("/file/list")
    @ApiImplicitParam(paramType = "query", name = "id", value = "故障类型业务主键faultTypeKey", required = true, dataType = "String")
    public Object getEkbFaultTypeFileList(@RequestParam String id) {
        return ekbFaultTypeService.getEkbFaultTypeFileList(id);
    }

    /**
     * @return java.util.List<cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO>
     * @Description 取得故障类型列表
     * <AUTHOR>
     * @Date 2020/03/31
     **/
    @SystemLog(optDesc = "查询故障类型列表", optType = SystemLogEnum.SELECT)
    @ApiOperation("取得故障类型列表")
    @PostMapping("/getFaultTypeNamesList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "nameCn", value = "故障类型中文名称", required = false),
            @ApiImplicitParam(name = "faultTypeKey", value = "fault_type_key数组", required = false)
    })
    public Object getFaultTypeNamesList(String nameCn, String faultTypeKey) {
        return ekbFaultTypeService.listFaultname(nameCn, faultTypeKey);
    }

    /**
     * @param faultTypeKey
     * @param faultReasonKey
     * @return
     * <AUTHOR>
     */
    @SystemLog(optDesc = "删除故障类型关联原因", optType = SystemLogEnum.DELETE)
    @ApiOperation("删除故障类型关联原因")
    @DeleteMapping("/deleteEkbFaultTypeReason")
    public Object deleteEkbFaultTypeReasonRelation(@RequestParam String faultTypeKey, @RequestParam String faultReasonKey) {
        return ekbFaultTypeService.deleteEkbFaultTypeReasonRelation(faultTypeKey, faultReasonKey);
    }

    /**
     * @param faultTypeKey
     * @param faultReasonKey
     * @param faultMeasureKey
     * @return
     * <AUTHOR>
     */
    @SystemLog(optDesc = "删除故障原因关联措施", optType = SystemLogEnum.DELETE)
    @ApiOperation("删除故障原因关联措施")
    @DeleteMapping("/deleteEkbFaultReasonMeasure")
    public Object deleteEkbFaultReasonMeasureRelation(@RequestParam String faultTypeKey, @RequestParam String faultReasonKey, @RequestParam String faultMeasureKey) {
        return ekbFaultTypeService.deleteEkbFaultReasonMeasureRelation(faultTypeKey, faultReasonKey, faultMeasureKey);
    }

}
