package cc.crrc.manage.controller.comm;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.comm.protocol.*;
import cc.crrc.manage.service.comm.ProtocolService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;

/**
 * @FileName ProtocolController
 * <AUTHOR> shuangquan
 * @Date 2019/11/12 13:08
 **/
@Api(tags = "协议配置")
@RestController()
@Validated
@RequestMapping("/protocol")
public class ProtocolController {
    @Autowired
    private ProtocolService protocolService;

    @GetMapping("/list")
    @ApiOperation("查询协议")
    @SystemLog(optDesc = "查询协议", optType = SystemLogEnum.SELECT)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleTypeId", value = "车辆型号ID"),
            @ApiImplicitParam(name = "pageNumber", value = "分页页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "分页条数", required = true)
    })
    public Object list(@RequestParam(required = false)
                       @Pattern(regexp = "^[1-9][0-9]*$", message = "车辆型号不正确")
                       @LogParam(description = "车辆型号") String vehicleTypeId,
                       @RequestParam @LogParam(description = "页码") @Min(value = 1, message = "页码不正确") int pageNumber,
                       @RequestParam @LogParam(description = "分页条数") @Min(value = 1, message = "分页条数不正确") int pageSize) {
        return protocolService.list(vehicleTypeId, pageNumber, pageSize);
    }

    // 2020年8月14日 房明宽 获取协议列表 相比之前 查询条件新增 线路ID
    @GetMapping("/getProtocolList")
    @ApiOperation("获取协议列表")
    @SystemLog(optDesc = "获取协议列表", optType = SystemLogEnum.SELECT)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleTypeId", value = "车辆型号ID"),
            @ApiImplicitParam(name = "pageNumber", value = "分页页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "分页条数", required = true),
            @ApiImplicitParam(name = "lineId", value = "线路ID")// 增加lineId
    })
    public Object getProtocolList(@RequestParam(required = false)
                       @Pattern(regexp = "^[1-9][0-9]*$", message = "车辆型号不正确")
                       @LogParam(description = "车辆型号") String vehicleTypeId,
                       @RequestParam @LogParam(description = "页码") @Min(value = 1, message = "页码不正确") int pageNumber,
                       @RequestParam @LogParam(description = "分页条数") @Min(value = 1, message = "分页条数不正确") int pageSize,
                       @RequestParam(required = false) @LogParam(description = "线路ID") String lineId) {// 增加lineId
        return protocolService.getProtocolList(vehicleTypeId, pageNumber, pageSize,lineId);
    }

    @PostMapping("/add")
    @ApiOperation("新增协议")
    @SystemLog(optDesc = "新增协议", optType = SystemLogEnum.INSERT)
    public Object save(@RequestBody @Validated ProtocolPO protocolPO) {
        protocolService.saveProtocol(protocolPO);
        return null;
    }

    @DeleteMapping("/del")
    @ApiOperation("删除协议")
    @SystemLog(optDesc = "删除协议", optType = SystemLogEnum.DELETE)
    public Object delete(@RequestBody @Validated ProtocolDelDTO protocolDelDTO) {
        protocolService.deleteProtocol(protocolDelDTO);
        return null;
    }

    @PutMapping("/status")
    @ApiOperation("协议状态")
    @SystemLog(optDesc = "协议状态", optType = SystemLogEnum.UPDATE)
    public Object updateStatus(@RequestBody @Validated ProtocolStatusDTO protocolStatusDTO) {
        protocolService.updateStatus(protocolStatusDTO);
        return null;
    }

    @GetMapping("/match")
    @ApiOperation("查询协议匹配车辆")
    @SystemLog(optDesc = "查询协议匹配车辆", optType = SystemLogEnum.SELECT)
    public Object queryMatchVehicle(@RequestParam @Min(value = 0, message = "协议ID不正确") @LogParam(description = "协议ID") String protocolId) {
        return protocolService.queryMatchVehicle(protocolId);
    }

    @PostMapping("/match")
    @ApiOperation("匹配车辆")
    @SystemLog(optDesc = "匹配车辆", optType = SystemLogEnum.SELECT)
    public Object matchVehicle(@RequestBody @Validated ProtocolMatchDTO protocolMatchDTO) {
        protocolService.match(protocolMatchDTO);
        return null;
    }

    @GetMapping("/validate")
    @ApiOperation("协议校验")
    @SystemLog(optDesc = "协议校验", optType = SystemLogEnum.SELECT)
    @ApiImplicitParam(name = "protocolId", value = "协议ID", required = true)
    public Object validate(@RequestParam @Min(value = 1, message = "协议ID不正确")
                           @LogParam(description = "协议ID") String protocolId) {
        return protocolService.validateSignal(protocolId);
    }


}
