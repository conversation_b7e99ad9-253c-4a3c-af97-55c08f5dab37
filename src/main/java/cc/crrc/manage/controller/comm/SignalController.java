package cc.crrc.manage.controller.comm;

import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.comm.signal.SignalDelDTO;
import cc.crrc.manage.pojo.comm.signal.SignalPO;
import cc.crrc.manage.pojo.comm.signal.SignalQueryDTO;
import cc.crrc.manage.service.comm.SignalService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @FileName SignalController
 * <AUTHOR> shuangquan
 * @Date 2019/11/13 13:59
 **/
@RestController
@RequestMapping("/protocol")
@Validated
@Api(tags = "信号配置")
public class SignalController {
    @Autowired
    private SignalService signalService;

    @PostMapping("/signals")
    public Object list(@RequestBody @Validated SignalQueryDTO signalQueryDTO) {
        return signalService.list(signalQueryDTO);
    }

    @PostMapping("/signal")
    @SystemLog(optDesc="保存信号", optType = SystemLogEnum.INSERT)
    public Object save(@RequestBody @Validated(Insert.class) SignalPO signalPO) {
        signalService.saveSignal(signalPO);
        return null;
    }

    @PutMapping("/signal")
    @SystemLog(optDesc="修改信号", optType = SystemLogEnum.UPDATE)
    public Object update(@RequestBody @Validated(Update.class) SignalPO signalPO) {
        signalService.updateSignal(signalPO);
        return null;
    }

    @DeleteMapping("/signal")
    @SystemLog(optDesc="删除信号", optType = SystemLogEnum.DELETE)
    public Object deleteSignal(@RequestBody @Validated SignalDelDTO signalDelDTO) {
        signalService.deleteSignal(signalDelDTO);
        return null;
    }
}
