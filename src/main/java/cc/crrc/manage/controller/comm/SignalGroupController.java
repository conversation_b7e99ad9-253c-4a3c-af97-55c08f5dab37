package cc.crrc.manage.controller.comm;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.comm.signalgroup.*;
import cc.crrc.manage.service.comm.SignalGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;

/**
 * @FileName SignalGroupController
 * <AUTHOR> shuangquan
 * @Date 2019/11/19 13:44
 **/
@RestController
@RequestMapping("/signalgroup")
@Validated
@Api(tags = "信号分组")
public class SignalGroupController {
    @Autowired
    private SignalGroupService signalGroupService;

    @GetMapping("/list")
    @ApiOperation("查询信号分组")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "lineId", value = "线路id"),
            @ApiImplicitParam(name = "vehicleTypeId", value = "车辆型号ID"),
            @ApiImplicitParam(name = "name", value = "信号分组名称"),
            @ApiImplicitParam(name = "pageNumber", value = "分页页码", required = true),
            @ApiImplicitParam(name = "pageSize", value = "分页条数", required = true)
    })
    public Object list(@RequestParam(required = false) @Pattern(regexp = "^$|^[1-9]+[0-9]*$") @LogParam(description =
                            "线路id") String lineId,
                       @RequestParam(required = false) @Pattern(regexp = "^$|^[1-9]+[0-9]*$") @LogParam(description =
                            "车辆型号ID") String vehicleTypeId,
                       @RequestParam(required = false) @LogParam(description = "信号分组名称") String name,
                       @RequestParam @LogParam(description = "页码") @Min(value = 1, message = "页码不正确") int pageNumber,
                       @RequestParam @LogParam(description = "分页条数") @Min(value = 1, message = "分页条数不正确") int pageSize) {
        return signalGroupService.list(lineId,vehicleTypeId, name, pageNumber, pageSize);
    }

    @PostMapping("/add")
    @ApiOperation("新增信号分组")
    @SystemLog(optDesc="新增信号分组", optType = SystemLogEnum.INSERT)
    public Object save(@RequestBody @Validated(Insert.class) SignalGroupPO signalGroupPO) {
        signalGroupService.saveSignalGroup(signalGroupPO);
        return null;
    }

    @PutMapping("/mod")
    @ApiOperation("修改信号分组")
    @SystemLog(optDesc="修改信号分组", optType = SystemLogEnum.UPDATE)
    public Object update(@RequestBody @Validated(Update.class) SignalGroupPO signalGroupPO) {
        signalGroupService.updateSignalGroup(signalGroupPO);
        return null;
    }

    @DeleteMapping("/del")
    @ApiOperation("删除信号分组")
    @SystemLog(optDesc="删除信号分组", optType = SystemLogEnum.DELETE)
    @ApiImplicitParam(name = "id", value = "信号分组ID", required = true)
    public Object delete(@RequestParam
                         @Min(value = 1, message = "信号分组ID不正确")
                         @LogParam(description = "信号分组ID") String id) {
        signalGroupService.deleteSignalGroup(id);
        return null;
    }

    @GetMapping("/signals")
    @SystemLog(optDesc="查询信号分组信号列表", optType = SystemLogEnum.SELECT)
    @ApiOperation("查询信号分组信号列表")
    public Object listSignal(@Validated SignalGroupQueryDTO signalGroupQueryDTO) {
        return signalGroupService.listSignal(signalGroupQueryDTO);
    }

    @GetMapping("/signals/all")
    @SystemLog(optDesc="查询信号分组信号列表", optType = SystemLogEnum.SELECT)
    @ApiOperation("查询信号列表")
    public Object listAllSignal(@Validated SignalGroupQueryDTO signalGroupQueryDTO) {
        return signalGroupService.listAllSignal(signalGroupQueryDTO);
    }

    @PostMapping("/signal/relation")
    @ApiOperation("信号分组关联信号")
    @SystemLog(optDesc="信号分组关联信号", optType = SystemLogEnum.SELECT)
    public Object saveSignalGroupRelation(@RequestBody @Validated SignalGroupRelationPO signalGroupRelationPO) {
        signalGroupService.saveSignalGroupRelation(signalGroupRelationPO);
        return null;
    }

    @DeleteMapping("/signal/relation")
    @ApiOperation("删除关联信号")
    @SystemLog(optDesc="删除关联信号", optType = SystemLogEnum.DELETE)
    public Object deleteRelation(@RequestBody @Validated SignalGroupDelRelationPO signalGroupDelRelationPO) {
        signalGroupService.deleteRelation(signalGroupDelRelationPO);
        return null;
    }

    @GetMapping("/fault")
    @ApiOperation("故障条件查询")
    public Object listFault(SignalGroupFaultQueryDTO signalGroupFaultQueryDTO) {
        return signalGroupService.listFault(signalGroupFaultQueryDTO);
    }

    @PostMapping("/fault")
    @ApiOperation("新增信号分组与故障关联关系")
    @SystemLog(optDesc="新增信号分组与故障关联关系", optType = SystemLogEnum.INSERT)
    public Object insert(@RequestBody SignalFaultGroupPO signalFaultGroupPO) {
        signalGroupService.insert(signalFaultGroupPO);
        return null;
    }

}
