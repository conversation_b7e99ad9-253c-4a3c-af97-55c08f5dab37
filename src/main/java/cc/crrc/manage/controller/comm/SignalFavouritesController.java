package cc.crrc.manage.controller.comm;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.pojo.comm.signalfavourites.SignalFavouritesGroupDTO;
import cc.crrc.manage.service.comm.SignalFavouritesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Min;

/**
 * @version 1.0
 * @Description : 该控制器是针对当前用户操作的信号查询中：某一车型，某一车辆的添加的全部收藏信号操作功能
 * @FileName SignalFavouritesController
 * <AUTHOR> lei
 * @Date 2021年8月5日11:31:44
 **/
@RestController
@RequestMapping("/signalfavourites")
@Validated
@Api(tags = "信号收藏")
public class SignalFavouritesController {
    @Autowired
    private SignalFavouritesService signalFavouritesService;

    @GetMapping("/group/list")
    @ApiOperation("根据当前用户查询收藏组列表")
    public Object favouritesGroupList() {
        return signalFavouritesService.signalFavouritesGroupList();
    }

    @GetMapping("/signals/list")
    @ApiOperation("根据收藏分组ID查询收藏信号列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "groupId", value = "收藏分组ID")
    })
    public Object signalsFavouritesList(@RequestParam(required = true) @LogParam(description =
            "收藏ID") String groupId) {
        return signalFavouritesService.signalsFavouritesList(groupId);
    }

    @PostMapping("/add")
    @ApiOperation("新增收藏信号分组及信号列表")
    public Object save(@RequestBody @Validated(Insert.class) SignalFavouritesGroupDTO signalFavouritesGroupDTO) {
        signalFavouritesService.saveSignalsFavouritesAndGroup(signalFavouritesGroupDTO);
        return null;
    }

    @DeleteMapping("/delete")
    @ApiOperation("删除收藏信号分组及信号列表")
    @ApiImplicitParam(name = "id", value = "收藏组ID", required = true)
    public Object delete(@RequestParam
                         @Min(value = 1, message = "收藏组ID不正确")
                         @LogParam(description = "收藏组ID") String id) {
        signalFavouritesService.deleteSignalsFavouritesAndGroup(id);
        return null;
    }

    @GetMapping("/group/name/exists")
    @ApiOperation("根据输入收藏名称查询是否存在重复名称")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "收藏名称")
    })
    public Object signalsFavouritesGroupNameIsExists(@RequestParam(required = true) @LogParam(description =
            "收藏名称") String name) {
        return signalFavouritesService.signalsFavouritesGroupNameIsExists(name);
    }
}
