package cc.crrc.manage.controller.comm;

import cc.crrc.manage.common.annotation.InsertValidated;
import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.comm.protocolTypeConfig.MqttTopicTypeDTO;
import cc.crrc.manage.pojo.comm.protocolTypeConfig.TcpPacketTypeDTO;
import cc.crrc.manage.service.comm.CommTypeConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "通讯配置")
@RestController
@RequestMapping(value = "/CommTypeConfig")
public class CommTypeConfigController {
	@Autowired
	CommTypeConfigService commService;
	
	@SystemLog(optDesc="查询数据包类型", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/getPacketType")
    @ApiOperation(value = "查询数据包类型 ", notes = "车型ID：vehicleTypeId (Long)")
	public Object getPacketType(@RequestParam(required = false) Integer currentPage,@RequestParam(required = false) Integer pageSize,@RequestParam String vehicleTypeId) {
		return commService.getPacketType(currentPage,pageSize,vehicleTypeId);
	}
	
	@SystemLog(optDesc="增加TCP数据包类型", optType = SystemLogEnum.INSERT)
	@PostMapping(value = "/addTcpPacketType")
	@ApiOperation(value = "增加TCP数据包类型 ", notes = "车型ID：vehicleTypeId (Long)，主题类名称：name，主题类别名：alias，有效脚本：validScript")
	public Object addTcpPacketType(@InsertValidated@RequestBody TcpPacketTypeDTO tcpDTO) {
		return commService.addTcpPacketType(tcpDTO);
	}
	
	@SystemLog(optDesc="增加主题类", optType = SystemLogEnum.INSERT)
	@PostMapping(value = "/addMqttTopicType")
	@ApiOperation(value = "增加主题类", notes = "车型ID：vehicleTypeId (Long)，主题类名称：name，主题类别名：alias，主题类类型：type，消息质量：qos，备注：remark")
	public Object addMqttTopicType(@InsertValidated@RequestBody MqttTopicTypeDTO mqttDTO) {
		return commService.addMqttTopicType(mqttDTO);
	}
	
	@SystemLog(optDesc="修改TCP数据包类型", optType = SystemLogEnum.UPDATE)
	@PutMapping(value = "/updateTcpPacketType")
	@ApiOperation(value = "修改TCP数据包类型 ", notes = "数据包类型ID：Id (Long)，车型ID：vehicleTypeId (Long)，主题类名称：name，主题类别名：alias，有效脚本：validScript")
	public Object updateTcpPacketType(@UpdateValidated@RequestBody TcpPacketTypeDTO tcpDTO) {
		return commService.updateTcpPacketType(tcpDTO);
	}
	
	@SystemLog(optDesc="修改主题类", optType = SystemLogEnum.UPDATE)
	@PutMapping(value = "/updateMqttTopicType")
	@ApiOperation(value = "修改主题类", notes = "主题类ID：Id (Long)，车型ID：vehicleTypeId (Long)，主题类名称：name，主题类别名：alias，主题类类型：type，消息质量：qos，备注：remark")
	public Object updateMqttTopicType(@UpdateValidated@RequestBody MqttTopicTypeDTO mqttDTO) {
		return commService.updateMqttTopicType(mqttDTO);
	}
	
	@SystemLog(optDesc="删除TCP数据包类型", optType = SystemLogEnum.DELETE)
	@DeleteMapping(value = "/delPacketType")
	@ApiOperation(value = "删除TCP数据包类型 ", notes = "车型ID：vehicleTypeId(Long)， 数据包类型ID：Id (Long)")
	public Object delPacketType(@RequestParam String vehicleTypeId, @RequestParam String id) {
		return commService.delPacketType(vehicleTypeId,id);
	}

}
