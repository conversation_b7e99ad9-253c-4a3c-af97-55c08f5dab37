package cc.crrc.manage.controller.comm;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.comm.signal.SignalLineQueryDTO;
import cc.crrc.manage.service.comm.SignalSearchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * @Author: Li <PERSON>
 * @Date: 2019-12-27
 */
@RestController
@RequestMapping("/signalsearch")
@Validated
@Api(tags = "信号查询")
public class SignalSearchController {
    @Autowired
    private SignalSearchService signalSearchService;

    @GetMapping(value = "/vehicleType/list")
    @SystemLog(optDesc = "查询车辆型号列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询车辆型号列表")
    public Object listVehicleType() {
        return signalSearchService.listVehicleType();
    }

    @GetMapping(value = "/vehicle/vehicleType/list")
    @SystemLog(optDesc = "根据车型id查询车辆列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据车型id查询车辆列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleTypeId", value = "车型id", required = true, paramType = "query", dataType = "String")
    })
    public Object listVehicleByVehicleTypeId(@LogParam(description = "车型id") @RequestParam String vehicleTypeId) {
        return signalSearchService.listVehicleByVehicleTypeId(vehicleTypeId);
    }

    @GetMapping(value = "/signalTree/vehicleType/list")
    @SystemLog(optDesc = "根据车型和树状节点id查询信号树", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据车型和树状节点id查询信号树", notes = "懒加载查询信号树")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleTypeId", value = "车型id", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "nodeId", value = "树状图节点id", paramType = "query", dataType = "String")
    })
    public Object getSignalTreeLazy(@LogParam(description = "车型id") @RequestParam String vehicleTypeId,
                                    @LogParam(description = "树状图节点id") @RequestParam(required = false) String nodeId) {
        return signalSearchService.getSignalTreeLazy(vehicleTypeId, nodeId);
    }

    @GetMapping(value = "/signalTree/vehicleType/search")
    @SystemLog(optDesc = "根据关键字搜索信号树", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据关键字搜索信号树")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleTypeId", value = "车型id", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "searchWord", value = "搜索关键字", paramType = "query", dataType = "String")
    })
    public Object getSignalTree(@LogParam(description = "车型id") @RequestParam String vehicleTypeId, @LogParam(description = "搜索关键字")  String searchWord) {
        return signalSearchService.getSignalTreeInSearch(vehicleTypeId, searchWord);
    }

    /**
     * 
     * @param signalLineQueryDTO
     * @return
     */
    @GetMapping(value = "/signal/interval")
    @SystemLog(optDesc = "获取信号密度", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据车辆编号、起止时间和信号id获取信号密度",
            notes = "sample字段为是否抽样的标识位。若为true，则表示将会对数据进行抽样查询，此时必须传表示抽样时间间隔的interval字段。")
    public Object getSignalInterval(@Validated SignalLineQueryDTO signalLineQueryDTO) {
        return signalSearchService.getSignalInterval(signalLineQueryDTO);
    }


    @GetMapping(value = "/signal/history/line")
    @SystemLog(optDesc = "获取信号曲线", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据车辆编号、起止时间和信号id获取信号曲线",
            notes = "sample字段为是否抽样的标识位。若为true，则表示将会对数据进行抽样查询，此时必须传表示抽样时间间隔的interval字段。")
    public Object getSignalHistoryLine(@Validated SignalLineQueryDTO signalLineQueryDTO) {
        return signalSearchService.getSignalHistoryLine(signalLineQueryDTO);
    }

    /**
     * 新版信号查询
     * <AUTHOR>
     * 2021/12/01
     **/
    @GetMapping(value = "/signal/sysGroup/search")
    @SystemLog(optDesc = "根据车厢查询共有系统list", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据车厢查询共有系统list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleCode", value = "车辆编码", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "carList", value = "车厢list",required = true, paramType = "query", dataType = "String")
    })
    public Object getSignalBySysGroup( @LogParam(description = "车辆编码")  String vehicleCode
            , @RequestParam String carList) {
        return signalSearchService.getSignalBySysGroup(vehicleCode,carList);
    }


    @ApiOperation(value = "查询车辆构型的车厢list")
    @PostMapping(value = "/structureList")
    public Object structureList(@RequestParam String vehicleCode) {
        return signalSearchService.structureList(vehicleCode);
    }

    /**
     * @Description 根据输入字符串模糊查询信号变量名
     * @Param inputName 输入字符串
     * <AUTHOR> zhijian lixin迁移
     * @Date 2021/12/02
     */
    @ApiOperation(value = "根据输入字符串模糊查询信号变量名")
    @GetMapping(value = "/findLikeWtdSignalByInputName")
    public Object findLikeWtdSignalByInputName(@RequestParam String vehicleCode, String inputName){
        return signalSearchService.findLikeWtdSignalByInputName(vehicleCode, inputName);
    }

    /**
     * 新版信号查询
     * <AUTHOR>
     * 2021/12/01
     **/
    @GetMapping(value = "/signal/sysSignals/search")
    @SystemLog(optDesc = "根据系统和输入查询信号list", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据系统和输入查询信号list")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleCode", value = "车辆编码",required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "carList", value = "车厢list",required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "subsystem", value = "系统",required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "inputName", value = "搜索输入内容",required = false, paramType = "query", dataType = "String")
    })
    public Object getSignalBySysAndLocation( @RequestParam  String vehicleCode
            , @RequestParam String carList, @RequestParam String subsystem, String inputName) {
        return signalSearchService.getSignalBySysAndLocation(vehicleCode,carList,subsystem,inputName);
    }

}
