package cc.crrc.manage.controller.draw;

import cc.crrc.manage.cache.RefreshCache;
import cc.crrc.manage.common.annotation.InsertValidated;
import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.draw.DrawLineConfigDTO;
import cc.crrc.manage.service.draw.DrawLineConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@Api(tags = "绘制线路配置模块")
@RestController
@RequestMapping(value = "/drawLineConfig")
public class DrawLineConfigController {
    @Autowired
    private DrawLineConfigService drawLineConfigService;


    /**
     * @return int
     * @Description 新增区段绘制
     * <AUTHOR>
     * @Date 10:04 2021/08/25
     * @Param [drawLineConfigDTO]
     **/
    @SystemLog(optDesc = "新增区段绘制", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "新增区段绘制")
    @PostMapping("/drawLine")
    @RefreshCache(values = "DRAW_INFO")
    public Object addDrawLine(@RequestBody @InsertValidated DrawLineConfigDTO drawLineConfigDTO) {
        return drawLineConfigService.addDrawLine(drawLineConfigDTO);
    }

    /**
     * @return int
     * @Description 更新区段绘制
     * <AUTHOR>
     * @Date 16:05 2021/8/25
     * @Param [dictVO]
     **/
    @SystemLog(optDesc = "更新区段绘制", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "更新区段绘制")
    @PutMapping("/drawLine")
    @RefreshCache(values = "DRAW_INFO")
    public Object updateDrawLine(@RequestBody @UpdateValidated DrawLineConfigDTO drawLineConfigDTO) {
        return drawLineConfigService.updateDrawLine(drawLineConfigDTO);
    }

    /**
     * @return int
     * @Description 删除区段绘制
     * <AUTHOR>
     * @Date 16:06 2021/8/25
     * @Param [ids]
     **/
    @SystemLog(optDesc = "删除区段绘制", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "删除区段绘制")
    @DeleteMapping("/drawLine/id")
    @RefreshCache(values = "DRAW_INFO")
    public Object deleteDrawLine(@RequestParam("id") String id) {
        return drawLineConfigService.deleteDrawLine(id);
    }

    /**
     * @return void
     * @Description 查询区段绘制信息 byId
     * <AUTHOR>
     * @Date 16:06 2021/8/25
     * @Param [id]
     **/
    @SystemLog(optDesc = "查询区段绘制信息", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询区段绘制信息")
    @GetMapping("/drawLine/id")
    public Object getDrawLineInfoById(@RequestParam("id") String id) {
        return drawLineConfigService.getDrawLineInfoById(id);
    }

    /**
     * @return List
     * @Description 区段绘制列表
     * <AUTHOR>
     * @Date 10:04 2021/08/25
     * @Param [drawLineConfigDTO]
     **/
    @SystemLog(optDesc = "区段绘制列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "区段绘制列表")
    @PostMapping("/drawLine/list")
    public Object getDrawLineListForConfig(@Valid @RequestBody DrawLineConfigDTO drawLineConfigDTO) {
        return drawLineConfigService.getDrawLineListForConfig(drawLineConfigDTO);
    }


    /**
     * @return void
     * @Description 区段绘制记录复制
     * <AUTHOR>
     * @Date 10:04 2021/08/30
     * @Param [drawLineConfigDTO]
     **/
    @SystemLog(optDesc = "区段绘制记录复制", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "区段绘制记录复制")
    @PostMapping("/drawLine/copy")
    @RefreshCache(values = "DRAW_INFO")
    public Object copyDrawLine(@RequestParam("id") String id) {
        return drawLineConfigService.copyDrawLine(id);
    }


    /**
     * @return void
     * @Description 区段绘制记录初始化
     * <AUTHOR>
     * @Date 10:04 2021/11/02
     **/
    @SystemLog(optDesc = "区段绘制记录初始化", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "区段绘制记录初始化")
    @PostMapping("/drawLine/init")
    @RefreshCache(values = "DRAW_INFO")
    public Object copyDrawLineForInit(@RequestParam("lineId") String lineId) {
        return drawLineConfigService.copyDrawLineForInit(lineId);
    }


    /**
     * @return List
     * @Description 监控页面-区段绘制数据list
     * <AUTHOR>
     * @Date 10:04 2021/08/30
     * @Param [lineId, type]
     **/
    @SystemLog(optDesc = "监控页面-区段绘制数据list", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "监控页面-区段绘制数据list")
    @PostMapping("/drawLine/monitorList")
    public Object getDrawLineListForMonitor(@RequestParam("lineId") String lineId, @RequestParam("type") String type) {
        return drawLineConfigService.getDrawLineListForMonitor(lineId, type);
    }

    /**
     * @return List
     * @Description 区段绘制-获取车站列表
     * <AUTHOR>
     * @Date 10:04 2021/08/31
     * @Param [lineId]
     **/
    @SystemLog(optDesc = "区段绘制-获取车站列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "区段绘制-获取车站列表")
    @GetMapping("/drawLine/staList")
    public Object getStaListForDrawLine(@RequestParam("lineId") String lineId) {
        return drawLineConfigService.getStaListForDrawLine(lineId);
    }


}
