package cc.crrc.manage.controller.excel;


import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.excelUtiles.EasyPoiUtil;
import cc.crrc.manage.mapper.analysis.AnalysisMapping;
import cc.crrc.manage.mapper.ekb.EkbFaultTypeMapping;
import cc.crrc.manage.mapper.eva.HealthResultMapping;
import cc.crrc.manage.mapper.monitor.LifeForecastMapping;
import cc.crrc.manage.mapper.mtr.MtrVehicleTypeMapping;
import cc.crrc.manage.mapper.stru.StruVehicleTypeStructureMapping;
import cc.crrc.manage.pojo.analysis.AnalysisParamVO;
import cc.crrc.manage.pojo.analysis.CollectionReportByDayDTO;
import cc.crrc.manage.pojo.comm.signal.SignalLineQueryDTO;
import cc.crrc.manage.pojo.excel.*;
import cc.crrc.manage.pojo.excel.healthyScore.HealthyScoreDTO;
import cc.crrc.manage.pojo.excel.healthyScore.LineHealthyScoreDTO;
import cc.crrc.manage.pojo.mtc.MtcAlarmWarningDTO;
import cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO;
import cc.crrc.manage.service.analysis.AnalysisService;
import cc.crrc.manage.service.comm.SignalSearchService;
import cc.crrc.manage.service.excel.ExcelService;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.github.pagehelper.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;

/**
 * @FileName ExcelController
 * <AUTHOR> xin
 * @Date 2020/4/17 17:09
 * @Version 1.0
 **/
@Api(tags = "Excel上传下载")
@RestController
@RequestMapping(value = "/excel")
public class ExcelController {

    private final Logger logger = LoggerFactory.getLogger(ExcelController.class);

    private final static String PROJECT_LINE_NAME = "绍兴地铁2号线 ";

    @Autowired
    private ExcelService excelService;
    @Autowired
    private StruVehicleTypeStructureMapping mapping;
    @Autowired
    private EkbFaultTypeMapping ekbFaultTypeMapping;
    @Autowired
    private MtrVehicleTypeMapping vehicleTypeMapping;
    @Autowired
    private AnalysisService analysisService;
    @Autowired
    private LifeForecastMapping lifeForecastMapping;
    @Autowired
    private AnalysisMapping analysisMapping;
    @Autowired
    private SignalSearchService signalSearchService;
    @Autowired
    private HealthResultMapping healthResultMapping;


    /**
     * @return 成功插入条数
     * @Description 车型构型 上传Excel导入数据接口
     * <AUTHOR> xin
     * @Date 15:43 2020/4/20
     * @Param [file]
     **/
    @Transactional
    @PostMapping(value = "/importStruVehicleTypeStructureExcel")
    @SystemLog(optDesc = "车型构型 上传Excel导入数据接口", optType = SystemLogEnum.IMPORT)
    @ApiOperation(value = "车型构型 上传Excel导入数据接口")
    public Object importStruVehicleTypeStructureExcel(@RequestParam("file") MultipartFile file) {
        List<StruVehicleTypeStructureForExcelPO> list = new ArrayList<>();
        try {
            list = EasyPoiUtil.importExcel(file, 0, 1, StruVehicleTypeStructureForExcelPO.class);
        } catch (Exception e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "导入失败，请传入正确的模板!");
        }
        if (CollectionUtils.isEmpty(list)) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "导入失败，请在Excel模板中填写数据!");
        }
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    @GetMapping(value = "/exportStruVehicleTypeStructureExcel")
    @SystemLog(optDesc = "车型构型下载已存在的车型构型Excel数据接口", optType = SystemLogEnum.EXPORT)
    @ApiOperation(value = "车型构型下载已存在的车型构型Excel数据接口")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleTypeId", value = "vehicleTypeId", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "structureCode", value = "structureCode", dataType = "String")
    })
    public void exportStruVehicleTypeStructureExcel(@RequestParam("vehicleTypeId") String vehicleTypeId, String structureCode, HttpServletResponse response) {
        try {
            List<StruVehicleTypeStructureForExcelPO> list = mapping.getStruVehicleTypeStructureForExcel(vehicleTypeId, structureCode);
            //查询车型名称 为下面生成excel文件 做文件名称
            String fileName = vehicleTypeMapping.getVehicleTypeById(vehicleTypeId).getName();
            if (StringUtil.isNotEmpty(structureCode)) {
                String carName = structureCode.split("/")[1];
                fileName = fileName + carName + "车厢";
            }
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), StruVehicleTypeStructureForExcelPO.class, list);
            //设置导出的文件名
            fileName = fileName + ".xls";
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.reset();
            // ContentType 可以不设置
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            workbook.write(response.getOutputStream());
            logger.info(fileName + "导出成功");
        } catch (IOException e) {
            logger.error("导出失败，原因：", e);
        } finally {
            try {
                response.getOutputStream().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * @return 成功插入条数
     * @Description 检修管理  专家知识库  故障代码  上传Excel导入数据接口
     * <AUTHOR> mingkuan
     * @Date 2020年4月23日  2021-05-17 lixin更新（增加更新信号表逻辑）
     * @Param [file]
     **/
    @PostMapping(value = "/importEkbFaultCodeExcel")
    @SystemLog(optDesc = "导入故障字典Excel", optType = SystemLogEnum.IMPORT)
    @ApiOperation(value = "导入故障字典Excel")
    public Object importEkbFaultForExcel(@RequestParam("file") MultipartFile file) {
        return excelService.importEkbFaultTypeExcel(file);
    }

    /**
     * @return
     * @Description 检修管理  专家知识库  故障代码  Excel导出接口
     * <AUTHOR> mingkuan
     * @Date 2020年4月23日
     * @Param
     **/
    @Transactional(rollbackFor = Exception.class)
    @GetMapping(value = "/exportEkbFaultCodeExcel")
    @SystemLog(optDesc = "导出故障字典Excel", optType = SystemLogEnum.EXPORT)
    @ApiOperation(value = "导出故障字典Excel")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "countNum", value = "countNum", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "lineId", value = "lineId", dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "vehicleTypeId", value = "vehicleTypeId", dataType = "String")
    })
    public void exportEkbFaultCodeExcel(HttpServletResponse response, String countNum, String lineId, String vehicleTypeId, Integer faultCategory) {
        List<EkbFaultTypeForExcelPO> excelList = ekbFaultTypeMapping.getEkbFaultCodeByExcel(countNum, lineId, vehicleTypeId, faultCategory);
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), EkbFaultTypeForExcelPO.class, excelList);
        try {
            //设置导出的文件名
            String fileName = "故障代码";
            fileName = fileName + ".xls";
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.reset();
            // ContentType 可以不设置
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            workbook.write(response.getOutputStream());
            logger.info("故障代码数据导出成功");
        } catch (IOException e) {
            logger.error("故障代码数据导出失败，原因：", e);
        } finally {
            try {
                response.getOutputStream().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * @Description 检修管理  专家知识库  故障代码  下载故障代码关联故障原因导入模板
     * @Return void
     * <AUTHOR> wei
     * @Date 10:46 2020/4/29
     * @Param [response]
     **/
    @GetMapping(value = "/exportEkbFaultTypeReasonExcel")
    @SystemLog(optDesc = "下载故障字典导入模板", optType = SystemLogEnum.EXPORT)
    @ApiOperation(value = "下载故障字典导入模板")
    public void exportEkbFaultTypeReasonExcel(HttpServletResponse response) {
        EkbFaultTypeForExcelPO ekbFaultTypeReasonForExcelPO = new EkbFaultTypeForExcelPO();
        ekbFaultTypeReasonForExcelPO.setFaultTypeKey("F223412459");
        ArrayList<EkbFaultTypeForExcelPO> ekbFaultTypeReasonForExcelPOS = new ArrayList<>();
        ekbFaultTypeReasonForExcelPOS.add(ekbFaultTypeReasonForExcelPO);
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("故障字典导入模板", "故障字典导入模板"),
                EkbFaultTypeForExcelPO.class, ekbFaultTypeReasonForExcelPOS);
        try {
            //设置导出的文件名
            String fileName = "故障字典导入模板.xlsx";
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.reset();
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION);
        } finally {
            try {
                response.getOutputStream().close();
            } catch (IOException e) {
                throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION);
            }
        }
    }

    /**
     * @Description 检修管理  专家知识库  故障原因  下载故障原因关联故障措施导入模板
     * @Return void
     * <AUTHOR> wei
     * @Date 10:46 2020/4/29
     * @Param [response]
     **/
    @GetMapping(value = "/exportEkbFaultReasonMeasureExcel")
    @SystemLog(optDesc = "下载故障原因关联故障措施导入模板", optType = SystemLogEnum.EXPORT)
    @ApiOperation(value = "下载故障原因关联故障措施导入模板")
    public void exportEkbFaultReasonMeasureExcel(HttpServletResponse response) {
        EkbReasonMeasureForExcelPO ekbReasonMeasureForExcelPO = new EkbReasonMeasureForExcelPO();
        ekbReasonMeasureForExcelPO.setFaultReasonKey("A33592764");
        ekbReasonMeasureForExcelPO.setFaultTypeKey("F223477444");
        ekbReasonMeasureForExcelPO.setFaultMeasureKey("M223477444;M12378613;M223477444;M12378613");
        ArrayList<EkbReasonMeasureForExcelPO> ekbReasonMeasureForExcelPOS = new ArrayList<>();
        ekbReasonMeasureForExcelPOS.add(ekbReasonMeasureForExcelPO);
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("故障原因关联故障措施导入模板", "故障原因关联故障措施导入模板"),
                EkbReasonMeasureForExcelPO.class, ekbReasonMeasureForExcelPOS);
        try {
            //设置导出的文件名
            String fileName = "故障原因关联故障措施导入模板.xlsx";
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.reset();
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION);
        } finally {
            try {
                response.getOutputStream().close();
            } catch (IOException e) {
                throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION);
            }
        }
    }

    /**
     * 统计分析-车辆运行统计导出
     *
     * @param response
     * @param vehicleCode
     * @param startTime
     * @param endTime
     * @param name        2023/02/11
     * <AUTHOR>
     */
    @GetMapping(value = "/exportVehicleAnalysisExcel")
    @SystemLog(optDesc = "统计分析-车辆运行统计导出", optType = SystemLogEnum.EXPORT)
    @ApiOperation(value = "统计分析-车辆运行统计导出")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleCode", value = "vehicleCode", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "startTime", value = "startTime", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "endTime", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "name", value = "name", required = false, dataType = "String")
    })
    public void exportVehicleAnalysisExcel(HttpServletResponse response, String vehicleCode, String startTime, String endTime, String name) {
        if (StringUtils.isEmpty(vehicleCode) || StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION.getErrorCode(), "参数缺失！");
        }
        try {
            /**
             * 01.导出能耗统计
             */
            ExportSheetEntity energySheet = new ExportSheetEntity.Builder()
                    .setExportParams(new StringBuilder(PROJECT_LINE_NAME)
                            .append(vehicleCode)
                            .append("车")
                            .append("  车辆运行统计  ")
                            .append(startTime)
                            .append("--")
                            .append(endTime)
                            .toString(), "车辆运行统计")
                    .setDataList(analysisService.getEnergyForExcel(startTime, endTime, vehicleCode))
                    .setCls(EnergyForExcelPO.class)
                    .create();
            /**
             * 02.导出各系统能耗趋势
             */
            ExportSheetEntity systemTrendSheet = new ExportSheetEntity.Builder()
                    .setExportParams(new StringBuilder(PROJECT_LINE_NAME)
                            .append("各系统能耗趋势")
                            .toString(), "各系统能耗趋势")
                    .setDataList(analysisService.traConTrendForExcel(vehicleCode, startTime, endTime))
                    .setCls(AnalysisParamForExcelPO.class)
                    .create();
            /**
             * 03.导出总能耗趋势
             */
            ExportSheetEntity conTrendSheet = new ExportSheetEntity.Builder()
                    .setExportParams(new StringBuilder(PROJECT_LINE_NAME)
                            .append("总能耗趋势")
                            .toString(), "总能耗趋势")
                    .setDataList(analysisService.totalEnergyTrendForExcel(startTime, endTime, vehicleCode))
                    .setCls(TotalEnergyTrendForExcelPO.class)
                    .create();
            /**
             * 04.导出里程趋势
             */
            ExportSheetEntity mileageTrendSheet = new ExportSheetEntity.Builder()
                    .setExportParams(new StringBuilder(PROJECT_LINE_NAME)
                            .append("里程趋势")
                            .toString(), "里程趋势")
                    .setDataList(analysisService.mileageTrendForExcel(startTime, endTime, vehicleCode))
                    .setCls(MileageTrendForExcelPO.class)
                    .create();

            /**
             * 05.多sheet封装
             */
            List<Map<String, Object>> sheetList = new ExportMulitSheetEntity()
                    .append(energySheet)
                    .append(systemTrendSheet)
                    .append(conTrendSheet)
                    .append(mileageTrendSheet)
                    .get();

            Workbook workbook = ExcelExportUtil.exportExcel(sheetList, ExcelType.HSSF);

            //设置导出的文件名
            String fileName = "车辆运行统计." + ExcelType.XSSF;
            response.reset();
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            workbook.write(response.getOutputStream());
            logger.info("车辆运行统计数据导出成功");
        } catch (IOException e) {
            logger.error("车辆运行统计导出失败，原因：", e);
        }

    }

    /**
     * @Description 统计分析-全网运行统计导出
     * @Return void
     * <AUTHOR>
     * @Date 2021/6/1
     */
    @GetMapping(value = "/exportLineAnalysisExcel")
    @SystemLog(optDesc = "统计分析-全网运行统计导出", optType = SystemLogEnum.EXPORT)
    @ApiOperation(value = "统计分析-全网运行统计导出")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "lineId", value = "lineId", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "lineName", value = "lineName", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "startTime", value = "startTime", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "endTime", required = false, dataType = "String")
    })
    public void exportLineAnalysisExcel(HttpServletResponse response, String lineId, String lineName, String startTime, String endTime) {
        if (StringUtils.isEmpty(lineId) || StringUtils.isEmpty(lineName) || StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION.getErrorCode(), "参数缺失！");
        }
        try {
            /**
             * 01.导出线路运行统计
             */
            ExportSheetEntity runAllSheet = new ExportSheetEntity.Builder()
                    .setExportParams(new StringBuilder(lineName)
                            .append("  全网运行统计  ")
                            .append(startTime)
                            .append("--")
                            .append(endTime)
                            .toString(), "全网运行统计")
                    .setDataList(analysisService.lineAllRunExcel(startTime, endTime, lineId))
                    .setCls(LineAllRunExcelPO.class)
                    .create();
            /**
             * 02.导出能耗统计数据
             */
            ExportSheetEntity energySheet = new ExportSheetEntity.Builder()
                    .setExportParams("能耗统计", "能耗统计")
                    .setDataList(analysisMapping.totalEnergyPic(startTime, endTime, lineId))
                    .setCls(EnergyPicForExcelPO.class)
                    .create();

            /**
             * 03.导出线路运行总里程
             */
            ExportSheetEntity distanceSheet = new ExportSheetEntity.Builder()
                    .setExportParams("线路运行总里程", "线路运行总里程")
                    .setDataList(analysisMapping.lineDistanceForExcel(startTime, endTime, lineId))
                    .setCls(LineDistanceForExcel.class)
                    .create();

            /**
             * 04.各车辆能耗统计
             */
            ExportSheetEntity energyAllTrainSheet = new ExportSheetEntity.Builder()
                    .setExportParams("各车辆能耗", "各车辆能耗")
                    .setDataList(analysisMapping.LineAllForExcel(startTime, endTime, lineId))
                    .setCls(LineAllExcelPO.class)
                    .create();
            /**
             * 05.多sheet封装
             */
            List<Map<String, Object>> sheetList = new ExportMulitSheetEntity()
                    .append(runAllSheet)
                    .append(energySheet)
                    .append(distanceSheet)
                    .append(energyAllTrainSheet)
                    .get();
            Workbook workbook = ExcelExportUtil.exportExcel(sheetList, ExcelType.HSSF);
            //设置导出的文件名
            String fileName = "全网运行统计.xlsx";
            response.reset();
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            workbook.write(response.getOutputStream());
            logger.info("全网运行统计数据导出成功");
        } catch (IOException e) {
            logger.error("全网运行统计导出失败，原因：", e);
        }
    }

    /**
     * @Description 统计分析-线路数据统计导出
     * @Return void
     * <AUTHOR>
     * @Date 2021/6/1
     */
    @GetMapping(value = "/exportCollectionReportCountExcel")
    @SystemLog(optDesc = "统计分析-线路数据统计导出", optType = SystemLogEnum.EXPORT)
    @ApiOperation(value = "统计分析-线路数据统计导出")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "lineId", value = "lineId", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "lineName", value = "lineName", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "startTime", value = "startTime", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "endTime", required = false, dataType = "String")
    })
    public void exportCollectionReportCountExcel(HttpServletResponse response, String lineId, String lineName, String startTime, String endTime) {
        if (StringUtils.isEmpty(lineName) || StringUtils.isEmpty(lineId) || StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION.getErrorCode(), "参数缺失！");
        }

        AnalysisParamVO apv = new AnalysisParamVO();
        apv.setEndTime(endTime);
        apv.setStartTime(startTime);
        apv.setLineId(lineId);

        List<CollectionReportByDayDTO> list = analysisMapping.collectionReportCount(apv);

        //创建一个excel文件
        XSSFWorkbook xssfWorkbook = new XSSFWorkbook();
        //创建工作簿
        XSSFSheet sheet = xssfWorkbook.createSheet();
        sheet.setDefaultColumnWidth(15);
        sheet.setDefaultRowHeight((short) 500);
        //合并单元格
        CellRangeAddress cra = new CellRangeAddress(0, 0, 0, 2);
        sheet.addMergedRegion(cra);
        XSSFRow nameRow = sheet.createRow(0);
        nameRow.createCell(0).setCellValue(lineName + "线路    车辆数据量统计    " + startTime + "——" + endTime);
        XSSFRow titleRow = sheet.createRow(1);
        titleRow.createCell(0).setCellValue("序号");
        titleRow.createCell(1).setCellValue("车辆编码");
        titleRow.createCell(2).setCellValue("数据量");

        for (int i = 0; i < list.size(); i++) {
            int lastRowNum = sheet.getLastRowNum();
            XSSFRow data1Row = sheet.createRow(lastRowNum + 1);
            XSSFCell cell1 = data1Row.createCell(0);
            cell1.setCellValue(String.valueOf(i + 1));
            XSSFCell cell2 = data1Row.createCell(1);
            cell2.setCellValue(list.get(i).getVehicleCode());
            XSSFCell cell3 = data1Row.createCell(2);
            cell3.setCellValue(String.valueOf(list.get(i).getDataCount()));
        }

        try {
            String fileName = "CollectionReportCount.xlsx";
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.reset();
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            ServletOutputStream outputStream = response.getOutputStream();
            xssfWorkbook.write(outputStream);
        } catch (IOException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION);
        } finally {
            try {
                xssfWorkbook.close();
            } catch (IOException e) {
                throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION);
            }
        }
    }

    /**
     * @return
     * @Description 统计分析 寿命预测  Excel导出接口
     * <AUTHOR>
     * @Date 2020/06/01
     * @Param
     **/
    @GetMapping(value = "/lifeForecastExcel")
    @SystemLog(optDesc = "导出寿命预测Excel接口", optType = SystemLogEnum.EXPORT)
    @ApiOperation(value = "导出寿命预测Excel接口")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "lifeWarning", value = "lifeWarning", required = false, dataType = "Integer"),
            @ApiImplicitParam(paramType = "query", name = "lineId", value = "lineId", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "vehicleCode", value = "vehicleCode", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "componentType", value = "componentType", required = false, dataType = "String")

    })
    public void lifeForecastExcel(HttpServletResponse response, Integer lifeWarning, String lineId, String vehicleCode, String componentType) {

        try {
            List<LifeForecastForExcelPO> list = lifeForecastMapping.getLifeForecastForExcel(lifeWarning, lineId, vehicleCode, componentType);
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), LifeForecastForExcelPO.class, list);
            //设置导出的文件名
            String fileName = "寿命预测";
            fileName = fileName + ".xls";
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.reset();
            // ContentType 可以不设置
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            workbook.write(response.getOutputStream());
            logger.info("寿命预测数据导出成功");
        } catch (Exception exc) {
            logger.error("寿命预测数据导出失败，原因：", exc);
        }
//        catch (IOException e) {
//            logger.error("寿命预测数据导出失败，原因：", e);
//        }
        finally {
            try {
                response.getOutputStream().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * @return
     * @Description 信号查询  Excel导出接口
     * <AUTHOR>
     * @Date 2021年8月10日
     * @Param
     **/
    @Transactional(rollbackFor = Exception.class)
    @GetMapping(value = "/exportSignalExcel")
    @SystemLog(optDesc = "导出信号查询Excel接口", optType = SystemLogEnum.EXPORT)
    @ApiOperation(value = "导出信号查询Excel接口")
    public void exportSignalExcel(HttpServletResponse response, @Validated SignalLineQueryDTO signalLineQueryDTO) {
        try {
            XSSFWorkbook xssfWorkbook = signalSearchService.exportSignalExcel(signalLineQueryDTO);
            //设置导出的文件名
            String fileName = "信号查询";
            fileName = fileName + ".csv";
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.reset();
            // ContentType 可以不设置
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            xssfWorkbook.write(response.getOutputStream());
            logger.info("信号查询数据导出成功");
        } catch (IOException e) {
            logger.error("信号查询数据导出失败，原因：", e);
        } finally {
            try {
                response.getOutputStream().close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * @Description 统计分析-全网运行统计导出
     * @Return void
     * <AUTHOR>
     * @Date 2021/9/15
     */
    @GetMapping(value = "/exportHealthyScoreExcel")
    @SystemLog(optDesc = "健康评分-按时间段导出健康评分数据", optType = SystemLogEnum.EXPORT)
    @ApiOperation(value = "健康评分-按时间段导出健康评分数据")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "lineId", value = "lineId", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "vehicleCode", value = "vehicleCode", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "startTime", value = "startTime", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "endTime", value = "endTime", required = false, dataType = "String")
    })
    public void exportHealthyScoreExcel(HttpServletResponse response, String lineId, String vehicleCode, String startTime, String endTime) {
        if (StringUtils.isEmpty(lineId) || StringUtils.isEmpty(vehicleCode) || StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION.getErrorCode(), "参数缺失！");
        }
        try {
            try {
                /**
                 * 01.导出线路运行统计
                 */
                ExportSheetEntity lineSheet = new ExportSheetEntity.Builder()
                        .setExportParams(new StringBuilder("  线路  ")
                                .append(startTime)
                                .append("--")
                                .append(endTime)
                                .toString(), "车队评分")
                        .setDataList(healthResultMapping.lineScoreList(startTime, endTime, lineId))
                        .setCls(LineHealthyScoreDTO.class)
                        .create();
                /**
                 * 02.导出车辆运行统计
                 */
                ExportSheetEntity trainSheet = new ExportSheetEntity.Builder()
                        .setExportParams(new StringBuilder(vehicleCode)
                                .append("：车辆各系统健康评分").toString(), "车辆健康评分")
                        .setDataList(healthResultMapping.trainScoreList(startTime, endTime, lineId, vehicleCode))
                        .setCls(HealthyScoreDTO.class)
                        .create();
                /**
                 * 03.多sheet封装
                 */
                List<Map<String, Object>> sheetList = new ExportMulitSheetEntity()
                        .append(lineSheet)
                        .append(trainSheet)
                        .get();
                Workbook workbook = ExcelExportUtil.exportExcel(sheetList, ExcelType.HSSF);
                //设置导出的文件名
                String fileName = "健康评分.xls";
                response.reset();
                response.setContentType("application/vnd.ms-excel;charset=UTF-8");
                response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
                workbook.write(response.getOutputStream());
                logger.info("健康评分数据导出成功");
            } catch (IOException e) {
                logger.error("健康评分导出失败，原因：", e);
            }
        } catch (Exception e) {
            logger.error("健康评分导出失败，原因：", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @GetMapping(value = "/exportFaultWorkOrderExcel")
    @SystemLog(optDesc = "导出故障工单Excel接口", optType = SystemLogEnum.EXPORT)
    @ApiOperation(value = "导出故障工单Excel接口")
    public void exportFaultWorkOrderExcel(HttpServletResponse response, MtcAlarmWarningDTO condition) {
        List<MtcFaultRecordExcelPO> list = excelService.findFaultWorkOrderList(condition);
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), MtcFaultRecordExcelPO.class, list);
            //设置导出的文件名
            String fileName = "故障工单";
            fileName = fileName + ".xls";
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.reset();
            // ContentType 可以不设置
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            workbook.write(outputStream);
            logger.info("故障工单数据导出成功");
        } catch (IOException e) {
            logger.error("故障工单数据导出失败, 原因为{} info is ", e.getMessage(), e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @GetMapping(value = "/exportFaultExcel")
    @SystemLog(optDesc = "导出故障列表Excel接口", optType = SystemLogEnum.EXPORT)
    @ApiOperation(value = "导出故障列表Excel接口")
    public void exportFaultExcel(HttpServletResponse response, MtcAlarmWarningVO condition) {
        //设置导出的文件名
        String name = "故障列表";
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            List<MtcAlarmWarningForExcelPO> list = excelService.exportFaultExcel(condition);
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), MtcAlarmWarningForExcelPO.class, list);
            String fileName = URLEncoder.encode(name + ".xls", "UTF-8");
            response.reset();
            // ContentType 可以不设置
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            workbook.write(outputStream);
            logger.info("{} 数据导出成功", name);
        } catch (IOException e) {
            logger.error("{} 数据导出失败, 原因为{} info is ", name, e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.HANDLER_SYSTEM_EXCEPTION, "导出数据失败");
        }
    }

}
