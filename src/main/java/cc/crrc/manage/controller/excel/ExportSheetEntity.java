package cc.crrc.manage.controller.excel;

import cn.afterturn.easypoi.excel.entity.ExportParams;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 导出多sheet基础配置类
 * <AUTHOR>
 * 2023/02/08
 */
public class ExportSheetEntity {

    /**
     * 多sheet导出的每个导sheet封装成map对象
     */
    private Map<String,Object> sheet;

    private ExportSheetEntity(Builder builder) {
        this.sheet = builder.sheetMap;
    }

    public Map<String, Object> getSheet() {
        return sheet;
    }

    public static class Builder {
        /**
         * 每一个sheet的参数
         */
        private ExportParams exportParams;
        private List<?> dataList;
        private Class<?> cls;

        /**
         * 每个sheetMap的key默认名称
         */
        private final static String DEFAULT_MAP_KEY_TITLE_NAME = "title";
        private final static String DEFAULT_MAP_KEY_ENTITY_NAME = "entity";
        private final static String DEFAULT_MAP_KEY_DATA_NAME = "data";

        /**
         * 每个sheet的sheetMap
         */
        private final static String MAP_KEY_TITLE_NAME = DEFAULT_MAP_KEY_TITLE_NAME;
        private final static String MAP_KEY_ENTITY_NAME = DEFAULT_MAP_KEY_ENTITY_NAME;
        private final static String MAP_KEY_DATA_NAME = DEFAULT_MAP_KEY_DATA_NAME;
        private Map<String,Object> sheetMap = new HashMap();

        public Builder() {}

        public ExportParams getExportParams() {
            return exportParams;
        }

        public Builder setExportParams() {
            return setExportParams(null, null);
        }

        public Builder setExportParams(String title, String sheetName) {
            return setExportParams(new ExportParams(title, sheetName));
        }

        public Builder setExportParams(ExportParams exportParams) {
            this.exportParams = exportParams;
            return this;
        }

        public List<?> getDataList() {
            return dataList;
        }

        public Builder setDataList(List<?> dataList) {
            this.dataList = dataList;
            return this;
        }

        public Class<?> getCls() {
            return cls;
        }

        public Builder setCls(Class<?> cls) {
            this.cls = cls;
            return this;
        }

        /**
         * 封装每个sheet的sheetMap
         * @return
         */
        public ExportSheetEntity create() {
            return create(MAP_KEY_TITLE_NAME, MAP_KEY_ENTITY_NAME, MAP_KEY_DATA_NAME);
        }

        /**
         * 封装每个sheet的sheetMap
         * @return
         */
        protected ExportSheetEntity create(String titleKeyName, String entityKeyName, String dataKeyName) {
            sheetMap.put(titleKeyName, exportParams);
            sheetMap.put(entityKeyName, cls);
            sheetMap.put(dataKeyName, dataList);
            return new ExportSheetEntity(this);
        }

    }

}
