package cc.crrc.manage.controller.component;


import cc.crrc.manage.common.annotation.InsertValidated;
import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.component.ComponentTypeDTO;
import cc.crrc.manage.pojo.component.DesignParameterDTO;
import cc.crrc.manage.pojo.component.RamsParameterDTO;
import cc.crrc.manage.service.component.ComponentTypeService;
import cc.crrc.manage.service.mtr.MtrDesignParameterService;
import cc.crrc.manage.service.mtr.MtrRAMSParameterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "/ComponentType")
@Api(tags = "部件型号")
public class ComponentTypeController {
    @Autowired
    private ComponentTypeService conService;
    @Autowired
    private MtrDesignParameterService mtrDesignParameterService;
    @Autowired
    private MtrRAMSParameterService mtrRAMSParameterService;

    // 2020年8月14日 房明宽 获取部件型号列表 相比之前增加了 线路id和车型id 查询条件
    @SystemLog(optDesc = "获取部件型号列表", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/getComTypes")
    @ApiOperation(value = "获取部件型号列表",
            notes = "产品编号:productNumber(String),部件型号分类:catalog,部件型号中文名:nameCn,部件型号英文名:nameEn,车型ID:vehicleTypeId,线路ID:lineId")
    public Object getComTypes(@RequestBody ComponentTypeDTO comDTO) {
        return conService.getComponentTypes(comDTO);
    }

    @SystemLog(optDesc = "查询部件型号信息（根据部件型号id）", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/id")
    @ApiOperation(value = "查询部件型号信息（根据部件型号id） ", notes = "参数传部件型号id，不能为空")
    @ApiImplicitParam(paramType = "query", name = "id", value = "部件型号id", required = true, dataType = "String")
    public Object getComponentTypeById(@RequestParam String id) {
        return conService.getComponentTypeById(id);
    }

    // 2020年8月14日 房明宽 增加部件型号 相比之前多了车型id和线路id
    @SystemLog(optDesc = "增加部件型号", optType = SystemLogEnum.INSERT)
    @PostMapping(value = "/insertComType")
    @ApiOperation(value = "增加部件型号",
            notes = "线路ID:lineId,车型ID:vehicleTypeId,部件型号中文名:nameCn,部件型号英文名:nameEn,产品编号:productNumber,部件型号分类:catalog,制造商ID:manufacturerId(String),备注:remark")
    public Object insertComType(@InsertValidated @RequestBody ComponentTypeDTO comDTO) {
        return conService.insertComType(comDTO);
    }

    // 2020年8月14日 房明宽 修改部件型号信息 相比之前多了车型id和线路id
    @SystemLog(optDesc = "修改部件型号信息", optType = SystemLogEnum.UPDATE)
    @PutMapping(value = "/updateComType")
    @ApiOperation(value = "修改部件型号信息",
            notes = "部件型号ID:id(String),部件型号中文名:nameCn,部件型号英文名:nameEn,产品编号:productNumber,部件型号分类:catalog,制造商ID:manufacturerId(String),备注:remark,车型ID:vehicleTypeId")
    public Object updateComType(@UpdateValidated @RequestBody() ComponentTypeDTO comDTO) {
        return conService.updateComType(comDTO);
    }

    @SystemLog(optDesc = "删除部件型号", optType = SystemLogEnum.DELETE)
    @DeleteMapping(value = "/delComType")
    @ApiOperation(value = "删除部件型号", notes = "删除部件型号")
    public Object delComType(@RequestParam String ids) {
        return conService.delComType(ids);
    }

    @SystemLog(optDesc = "增加关联人员", optType = SystemLogEnum.INSERT)
    @PostMapping(value = "/assEmployee")
    @ApiOperation(value = "关联人员", notes = "人员id:employeeId(List<String>),部件型号ID:id(String)")
    public Object assEmployee(@RequestBody ComponentTypeDTO comDTO) {
        return conService.assEmployee(comDTO);
    }

    @SystemLog(optDesc = "查询关联人员", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/getEmployees")
    @ApiOperation(value = "查询关联人员", notes = "部件型号ID：id，供应商ID：manufacturerId")
    public Object getEmployees(@RequestBody ComponentTypeDTO comDTO) {
        return conService.getEmployees(comDTO);
    }

    @SystemLog(optDesc = "查询部件设计参数", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/getDesignParam")
    @ApiOperation(value = "查询部件设计参数", notes = "部件类型ID:comTypeId.注：车型使用时将部件ID更换成车型ID：vehicleTypeId")
    public Object getDesignParam(@RequestParam(required = false) String comTypeId, @RequestParam(required = false) String vehicleTypeId) {
        return mtrDesignParameterService.getDesignParam(comTypeId, vehicleTypeId);
    }

    @SystemLog(optDesc = "增加部件设计参数", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/insertDesignParam")
    @ApiOperation(value = "增加部件设计参数", notes = "部件类型ID:comTypeId,名称:itemName,值:itemValue.注：车型使用时将部件类型ID更换成车型ID：vehicleTypeId")
    public Object insertDesignParam(@InsertValidated @RequestBody DesignParameterDTO designParam) {
        return mtrDesignParameterService.insertDesignParam(designParam);
    }

    @SystemLog(optDesc = "修改部件设计参数", optType = SystemLogEnum.UPDATE)
    @PutMapping(value = "/updateDesignParam")
    @ApiOperation(value = "修改部件设计参数", notes = "参数ID:id,部件类型ID:comTypeId,名称:itemName,值:itemValue.注：车型使用时将部件类型ID更换成车型ID：vehicleTypeId")
    public Object updateDesignParam(@UpdateValidated @RequestBody DesignParameterDTO designParam) {
        return mtrDesignParameterService.updateDesignParam(designParam);
    }

    @SystemLog(optDesc = "删除部件设计参数", optType = SystemLogEnum.DELETE)
    @DeleteMapping(value = "/deleteDesignParam")
    @ApiOperation(value = "删除部件设计参数", notes = "参数ID:id")
    public Object deleteDesignParam(@RequestParam String id) {
        return mtrDesignParameterService.deleteDesignParam(id);
    }

    @SystemLog(optDesc = "查询部件RAMS参数", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/getRamsParam")
    @ApiOperation(value = "查询部件RAMS参数", notes = "部件类型ID:comTypeId.注：车型使用时将部件ID更换成车型ID：vehicleTypeId")
    public Object getRamsParam(@RequestParam(required = false) String comTypeId, @RequestParam(required = false) String vehicleTypeId) {
        return mtrRAMSParameterService.getRamsParam(comTypeId, vehicleTypeId);
    }

    @SystemLog(optDesc = "增加部件RAMS参数", optType = SystemLogEnum.INSERT)
    @PostMapping(value = "/insertRamsParam")
    @ApiOperation(value = "增加部件RAMS参数", notes = "部件类型ID:comTypeId,名称:itemName,值:itemValue,单位：unit.注：车型使用时将部件类型ID更换成车型ID：vehicleTypeId")
    public Object insertRamsParam(@InsertValidated @RequestBody RamsParameterDTO ramsParam) {
        return mtrRAMSParameterService.insertRamsParam(ramsParam);
    }

    @SystemLog(optDesc = "修改部件RAMS参数", optType = SystemLogEnum.UPDATE)
    @PutMapping(value = "/updateRamsParam")
    @ApiOperation(value = "修改部件RAMS参数", notes = "参数ID:id,部件类型ID:comTypeId,名称:itemName,值:itemValue，单位：unit.注：车型使用时将部件类型ID更换成车型ID：vehicleTypeId")
    public Object updateRamsParam(@UpdateValidated @RequestBody RamsParameterDTO ramsParam) {
        return mtrRAMSParameterService.updateRamsParam(ramsParam);
    }

    @SystemLog(optDesc = "删除部件RAMS参数", optType = SystemLogEnum.DELETE)
    @DeleteMapping(value = "/deleteRamsParam")
    @ApiOperation(value = "删除部件RAMS参数", notes = "参数ID:id")
    public Object deleteRamsParam(@RequestParam String id) {
        return mtrRAMSParameterService.deleteRamsParam(id);
    }

    @SystemLog(optDesc = "增加相关文件", optType = SystemLogEnum.INSERT)
    @PostMapping(value = "/insertFile")
    @ApiOperation(value = "增加相关文件", notes = "部件型号ID:id,文件相关信息:sysFile")
    public Object insertFile(@InsertValidated @RequestBody SysFilePO sysFile, @RequestParam String id) {
        conService.insertFile(sysFile, id);
        return null;
    }

    @SystemLog(optDesc = "删除相关文件", optType = SystemLogEnum.DELETE)
    @DeleteMapping(value = "/delFile")
    @ApiOperation(value = "删除相关文件", notes = "文件ID：fileId ,部件型号Id：id")
    public Object delFile(@RequestParam String fileId, @RequestParam String id) {
        return conService.delFile(fileId);
    }

    @SystemLog(optDesc = "查询相关文件", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/selectlFile")
    @ApiOperation(value = "查询相关文件", notes = "部件型号ID：id")
    public Object selectlFile(@RequestParam String id) {
        return conService.selectlFile(id);
    }
    
    @SystemLog(optDesc = "增加关联人员(单条)", optType = SystemLogEnum.INSERT)
    @PostMapping(value = "/insertMtrComponentTypeContacts")
    @ApiOperation(value = "增加关联人员", notes = "部件型号ID：componentTypeId,人员ID：manufacturerEmployeeId")
    public Object insertMtrComponentTypeContacts(@RequestParam String manufacturerEmployeeId, @RequestParam String componentTypeId) {
    	return conService.insertMtrComponentTypeContacts(manufacturerEmployeeId, componentTypeId);
    }
    
    @SystemLog(optDesc = "删除关联人员", optType = SystemLogEnum.DELETE)
    @DeleteMapping(value = "/deleteMtrComponentTypeContacts")
    @ApiOperation(value = "删除关联人员", notes = "部件型号ID：componentTypeId,人员ID：manufacturerEmployeeId")
    public Object deleteMtrComponentTypeContacts(@RequestParam String manufacturerEmployeeId, @RequestParam String componentTypeId) {
        return conService.deleteMtrComponentTypeContacts(manufacturerEmployeeId, componentTypeId);
    }
}
