package cc.crrc.manage.controller.component;

import cc.crrc.manage.common.annotation.InsertValidated;
import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.component.ComponentDTO;
import cc.crrc.manage.pojo.component.ManufacturerParameterDTO;
import cc.crrc.manage.service.component.ComponentService;
import cc.crrc.manage.service.mtr.MtrManufacturerParameterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "/Conponent")
@Api(tags = "部件")
public class ComponentController {
    @Autowired
    private ComponentService comService;
    @Autowired
    private MtrManufacturerParameterService mtrManufacturerParameterService;

    @SystemLog(optDesc = "查询部件信息", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/getComponents")
    @ApiOperation(value = "查询部件信息 ", notes = "部件型号ID：componentTypeId（Long），部件序列号：serialNumber（String）")
    public Object getComponents(@RequestBody ComponentDTO componentDTO) {
        return comService.getComponents(componentDTO);
    }

    @SystemLog(optDesc = "增加部件信息", optType = SystemLogEnum.INSERT)
    @PostMapping(value = "/insertComponents")
    @ApiOperation(value = "增加部件信息 ", notes = "部件型号ID：componentTypeId（Long），部件序列号：serialNumber（String），部件出厂日期：productionDate（String，YYYY-MM-DD hh：mm：ss）")
    public Object insertComponents(@InsertValidated @RequestBody ComponentDTO componentDTO) {
        return comService.insertComponents(componentDTO);
    }

    @SystemLog(optDesc = "修改部件信息", optType = SystemLogEnum.UPDATE)
    @PutMapping(value = "/updateComponents")
    @ApiOperation(value = "修改部件信息 ", notes = "部件ID：id（Long），部件型号ID：componentTypeId（Long），部件序列号：serialNumber（String），部件出厂日期：productionDate（String，YYYY-MM-DD hh：mm：ss）")
    public Object updateComponents(@UpdateValidated @RequestBody ComponentDTO componentDTO) {
        return comService.updateComponents(componentDTO);
    }

    @SystemLog(optDesc = "删除部件", optType = SystemLogEnum.DELETE)
    @DeleteMapping(value = "/delComponents")
    @ApiOperation(value = "删除部件 ", notes = "部件ID：id")
    public Object delComponents(@RequestParam String ids) {
        return comService.delComponents(ids);
    }

    @SystemLog(optDesc = "查询部件制造参数", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/getManufParam")
    @ApiOperation(value = "查询部件制造参数", notes = "部件ID:componentId.注：车型使用时将部件ID更换成车辆ID：vehicleId")
    public Object getManufParam(@RequestParam(required = false) String componentId, @RequestParam(required = false) String vehicleId) {
        return mtrManufacturerParameterService.getManufParam(componentId, vehicleId);
    }

    @SystemLog(optDesc = "增加部件制造参数", optType = SystemLogEnum.INSERT)
    @PostMapping(value = "/insertManufParam")
    @ApiOperation(value = "增加部件制造参数", notes = "部件ID:componentId,名称:itemName,值:itemValue.注：车型使用时将部件ID更换成车辆ID：vehicleId")
    public Object insertManufParam(@InsertValidated @RequestBody ManufacturerParameterDTO manufParam) {
        return mtrManufacturerParameterService.insertManufParam(manufParam);
    }

    @SystemLog(optDesc = "修改部件制造参数", optType = SystemLogEnum.UPDATE)
    @PutMapping(value = "/updateManufParam")
    @ApiOperation(value = "修改部件制造参数", notes = "参数ID:id,部件ID:componentId,名称:itemName,值:itemValue.注：车型使用时将部件ID更换成车辆ID：vehicleId")
    public Object updateManufParam(@UpdateValidated @RequestBody ManufacturerParameterDTO manufParam) {
        return mtrManufacturerParameterService.updateManufParam(manufParam);
    }

    @SystemLog(optDesc = "删除部件制造参数", optType = SystemLogEnum.DELETE)
    @DeleteMapping(value = "/deleteManufParam")
    @ApiOperation(value = "删除部件制造参数", notes = "参数ID:id")
    public Object deleteManufParam(@RequestParam String id) {
        return mtrManufacturerParameterService.deleteManufParam(id);
    }

    @SystemLog(optDesc = "增加相关文件", optType = SystemLogEnum.INSERT)
    @PostMapping(value = "/insertFile")
    @ApiOperation(value = "增加相关文件", notes = "部件ID:id,文件相关信息:sysFile")
    public Object insertFile(@InsertValidated @RequestBody SysFilePO sysFile, @RequestParam String id) {
        return comService.insertFile(sysFile, id);
    }

    @SystemLog(optDesc = "删除相关文件", optType = SystemLogEnum.DELETE)
    @DeleteMapping(value = "/delFile")
    @ApiOperation(value = "删除相关文件", notes = "文件ID：fileId , 部件ID:id")
    public Object delFile(@RequestParam String fileId, @RequestParam String id) {
        return comService.delFile(fileId);
    }

    @SystemLog(optDesc = "查询相关文件", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/selectlFile")
    @ApiOperation(value = "查询相关文件", notes = "部件ID：id")
    public Object selectlFile(@RequestParam String id) {
        return comService.selectlFile(id);
    }

}
