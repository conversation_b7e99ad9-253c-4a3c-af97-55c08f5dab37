package cc.crrc.manage.controller;

import cc.crrc.manage.common.annotation.*;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.SysDictTypePO;
import cc.crrc.manage.service.SysDictTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @FileName SysDictTypeController
 * <AUTHOR> yuxi
 * @Date 2019/10/16 11:12
 * @Version 1.0
 **/
@Api(tags = "字典类型")
@RestController
@RequestMapping("/dictType")
@Validated
public class SysDictTypeController {
    @Autowired
    SysDictTypeService dictTypeService;

    @SystemLog(optDesc = "查询字典类型", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "分页取得字典类型")
    @PostMapping("/types")
    public Object listByType(@RequestBody SysDictTypePO dictType) {
        return dictTypeService.listByType(dictType);
    }

    @SystemLog(optDesc = "修改字典类型", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "更新字典类型")
    @PutMapping("/type")
    public Object updateDictType(@RequestBody @UpdateValidated SysDictTypePO dictType) {
        return dictTypeService.updateDictType(dictType);
    }

    @SystemLog(optDesc = "删除字典类型", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "删除字典类型")
    @DeleteMapping("/type")
    public Object deleteDictType(@LogParam(description = "字典类型id") @RequestParam String id) {
        return dictTypeService.deleteDictType(id);
    }

    @SystemLog(optDesc = "增加字典类型", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "新增字典类型")
    @PostMapping("/type")
    public Object addDictType(@RequestBody @InsertValidated SysDictTypePO dictType) {
        return dictTypeService.addDictType(dictType);
    }

}
