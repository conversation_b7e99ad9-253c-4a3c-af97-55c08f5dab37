package cc.crrc.manage.controller.monitor;


import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.mapper.monitor.OilChangePeriodMapping;
import cc.crrc.manage.pojo.monitor.OilChangeHistoryForExcelPO;
import cc.crrc.manage.pojo.monitor.WorkPeriodPO;
import cc.crrc.manage.pojo.monitor.WorkPeriodVO;
import cc.crrc.manage.service.monitor.OilChangePeriodService;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * <AUTHOR> kangjian
 * @Description 换油周期
 * @Date 2021/9/16
 **/
@Api(tags = "换油周期")
@RestController
@RequestMapping(value = "/monitor/oilChange")
public class OilChangePeriodContorller {

    @Autowired
    private OilChangePeriodService oilChangePeriodService;
    @Autowired
    private OilChangePeriodMapping oilChangePeriodMapping;


    @SystemLog(optDesc = "新增部件换油信息", optType = SystemLogEnum.INSERT)
    @ApiOperation("新增部件换油信息")
    @PostMapping("/addOilChangeInfo")
    public Object addOilChangeInfo(@Validated(Insert.class) @RequestBody WorkPeriodPO workPeriodPO) {
        return oilChangePeriodService.addOilChangeInfo(workPeriodPO);
    }

    @SystemLog(optDesc = "更新部件换油信息", optType = SystemLogEnum.UPDATE)
    @ApiOperation("更新部件换油信息")
    @PostMapping("/updateOilChangeInfo")
    public Object updateOilChangeInfo(@RequestBody WorkPeriodPO workPeriodPO) {
        return oilChangePeriodService.updateOilChangeInfo(workPeriodPO);
    }

    @SystemLog(optDesc = "编辑当前记录", optType = SystemLogEnum.UPDATE)
    @ApiOperation("编辑当前记录")
    @PostMapping("/updateCurrentInfo")
    public Object updateCurrentInfo(@Validated(Update.class) @RequestBody WorkPeriodPO workPeriodPO) {
        return oilChangePeriodService.updateCurrentInfo(workPeriodPO);
    }

    @SystemLog(optDesc = "批量删除当前记录", optType = SystemLogEnum.DELETE)
    @ApiOperation("批量删除当前记录")
    @DeleteMapping("/deleteOilChangeInfo")
    public Object deleteOilChangeInfo(@RequestParam String ids) {
        return oilChangePeriodService.deleteOilChangeInfo(ids);
    }

    @SystemLog(optDesc = "查看换油周期列表", optType = SystemLogEnum.SELECT)
    @ApiOperation("查看换油周期列表")
    @PostMapping("/listOilChangeInfo")
    public Object listOilChangeInfo(@RequestBody WorkPeriodVO workPeriodVO) {
        return oilChangePeriodService.listOilChangeInfo(workPeriodVO);
    }

    @SystemLog(optDesc = "查看换油周期历史详情列表", optType = SystemLogEnum.SELECT)
    @ApiOperation("查看换油周期历史详情列表")
    @GetMapping("/listOilChangeHistory")
    public Object listOilChangeHistory(@RequestParam String id) {
        return oilChangePeriodService.listOilChangeHistory(id);
    }

    @SystemLog(optDesc = "换油周期历史Excel导出", optType = SystemLogEnum.DOWNLOAD)
    @GetMapping(value = "/exportOilChangeHistory")
    @ApiOperation(value = "换油周期历史Excel导出")
    public void exportOilChangeHistory(HttpServletResponse response, String id) {
        List<OilChangeHistoryForExcelPO> oilChangeHistory = oilChangePeriodMapping.getExcelData(id);
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("换油历史","换油历史", ExcelType.XSSF), OilChangeHistoryForExcelPO.class, oilChangeHistory);
        try {
            String fileName = "history.xlsx";
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.reset();
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION);
        } finally {
            try {
                response.getOutputStream().close();
            } catch (IOException e) {
                throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION);
            }
        }
    }

}
