package cc.crrc.manage.controller.monitor;


import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.mapper.monitor.WashVehiclePeriodMapping;
import cc.crrc.manage.pojo.monitor.*;
import cc.crrc.manage.service.monitor.WashVehiclePeriodService;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * <AUTHOR> kangjian
 * @Description 洗车周期
 * @Date 2021/9/17
 **/
@Api(tags = "洗车周期")
@RestController
@RequestMapping(value = "/monitor/washVehicle")
public class WashVehiclePeriodContorller {

    @Autowired
    private WashVehiclePeriodService washVehiclePeriodService;
    @Autowired
    private WashVehiclePeriodMapping washVehiclePeriodMapping;

    @SystemLog(optDesc = "新增洗车信息", optType = SystemLogEnum.INSERT)
    @ApiOperation("新增洗车信息")
    @PostMapping("/addWashVehicleInfo")
    public Object addWashVehicleInfo(@RequestBody WorkPeriodPO workPeriodPO) {
        return washVehiclePeriodService.addWashVehicleInfo(workPeriodPO);
    }

    @SystemLog(optDesc = "更新洗车信息", optType = SystemLogEnum.UPDATE)
    @ApiOperation("更新洗车信息")
    @PostMapping("/updateWashVehicleInfo")
    public Object updateWashVehicleInfo(@RequestBody WorkPeriodPO workPeriodPO) {
        return washVehiclePeriodService.updateWashVehicleInfo(workPeriodPO);
    }

    @SystemLog(optDesc = "编辑当前记录", optType = SystemLogEnum.UPDATE)
    @ApiOperation("编辑当前记录")
    @PostMapping("/updateCurrentInfo")
    public Object updateCurrentInfo(@RequestBody WorkPeriodPO workPeriodPO) {
        return washVehiclePeriodService.updateCurrentInfo(workPeriodPO);
    }

    @SystemLog(optDesc = "批量删除当前记录", optType = SystemLogEnum.DELETE)
    @ApiOperation("批量删除当前记录")
    @DeleteMapping("/deleteWashVehicleInfo")
    public Object deleteWashVehicleInfo(@RequestParam String ids) {
        return washVehiclePeriodService.deleteWashVehicleInfo(ids);
    }

    @SystemLog(optDesc = "查看洗车周期列表", optType = SystemLogEnum.SELECT)
    @ApiOperation("查看洗车周期列表")
    @PostMapping("/listWashVehicleInfo")
    public Object listWashVehicleInfo(@RequestBody WorkPeriodVO workPeriodVO) {
        return washVehiclePeriodService.listWashVehicleInfo(workPeriodVO);
    }

    @SystemLog(optDesc = "查看洗车周期历史详情列表", optType = SystemLogEnum.SELECT)
    @ApiOperation("查看洗车周期历史详情列表")
    @GetMapping("/listWashVehicleHistory")
    public Object listWashVehicleHistory(@RequestParam String id) {
        return washVehiclePeriodService.listWashVehicleHistory(id);
    }

    @SystemLog(optDesc = "洗车周期历史Excel导出", optType = SystemLogEnum.DOWNLOAD)
    @GetMapping(value = "/exportWashVehicleHistory")
    @ApiOperation(value = "洗车周期历史Excel导出")
    public void exportOilChangeHistory(HttpServletResponse response, String id) {
        List<WashVehicleHistoryForExcelPO> washVehicleHistory = washVehiclePeriodMapping.getExcelData(id);
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("洗车历史","洗车历史", ExcelType.XSSF), WashVehicleHistoryForExcelPO.class, washVehicleHistory);
        try {
            String fileName = "history.xlsx";
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.reset();
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION);
        } finally {
            try {
                response.getOutputStream().close();
            } catch (IOException e) {
                throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION);
            }
        }
    }
}
