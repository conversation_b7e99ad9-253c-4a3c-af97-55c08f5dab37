package cc.crrc.manage.controller.monitor;


import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.mapper.monitor.OtherDevicePeriodMapping;
import cc.crrc.manage.pojo.monitor.OtherDeviceHistoryForExcelPO;
import cc.crrc.manage.pojo.monitor.WorkPeriodPO;
import cc.crrc.manage.pojo.monitor.WorkPeriodVO;
import cc.crrc.manage.service.monitor.OtherDevicePeriodService;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * <AUTHOR> kangjian
 * @Description 其他设备周期
 * @Date 2022/2/17
 **/
@Api(tags = "其他设备周期")
@RestController
@RequestMapping(value = "/monitor/otherDevice")
public class OtherDevicePeriodContorller {

    @Autowired
    private OtherDevicePeriodService otherDevicePeriodService;
    @Autowired
    private OtherDevicePeriodMapping otherDevicePeriodMapping;


    @SystemLog(optDesc = "新增其他设备周期信息", optType = SystemLogEnum.INSERT)
    @ApiOperation("新增其他设备周期信息")
    @PostMapping("/addOtherDeviceInfo")
    public Object addOtherDeviceInfo(@Validated(Insert.class) @RequestBody WorkPeriodPO workPeriodPO) {
        return otherDevicePeriodService.addOtherDeviceInfo(workPeriodPO);
    }

    @SystemLog(optDesc = "更新其他设备周期信息", optType = SystemLogEnum.UPDATE)
    @ApiOperation("更新其他设备周期信息")
    @PostMapping("/updateOtherDeviceInfo")
    public Object updateOtherDeviceInfo(@RequestBody WorkPeriodPO workPeriodPO) {
        return otherDevicePeriodService.updateOtherDeviceInfo(workPeriodPO);
    }

    @SystemLog(optDesc = "编辑当前记录", optType = SystemLogEnum.UPDATE)
    @ApiOperation("编辑当前记录")
    @PostMapping("/updateCurrentInfo")
    public Object updateCurrentInfo(@Validated(Update.class) @RequestBody WorkPeriodPO workPeriodPO) {
        return otherDevicePeriodService.updateCurrentInfo(workPeriodPO);
    }

    @SystemLog(optDesc = "批量删除当前记录", optType = SystemLogEnum.DELETE)
    @ApiOperation("批量删除当前记录")
    @DeleteMapping("/deleteOtherDeviceInfo")
    public Object deleteOtherDeviceInfo(@RequestParam String ids) {
        return otherDevicePeriodService.deleteOtherDeviceInfo(ids);
    }

    @SystemLog(optDesc = "查看其他设备周期列表", optType = SystemLogEnum.SELECT)
    @ApiOperation("查看其他设备周期列表")
    @PostMapping("/listOtherDeviceInfo")
    public Object listOtherDeviceInfo(@RequestBody WorkPeriodVO workPeriodVO) {
        return otherDevicePeriodService.listOtherDeviceInfo(workPeriodVO);
    }

    @SystemLog(optDesc = "查看其他设备周期历史详情列表", optType = SystemLogEnum.SELECT)
    @ApiOperation("查看其他设备周期历史详情列表")
    @GetMapping("/listOtherDeviceHistory")
    public Object listOtherDeviceHistory(@RequestParam String id) {
        return otherDevicePeriodService.listOtherDeviceHistory(id);
    }

    @SystemLog(optDesc = "其他设备周期历史Excel导出", optType = SystemLogEnum.DOWNLOAD)
    @GetMapping(value = "/exportOtherDeviceHistory")
    @ApiOperation(value = "其他设备周期历史Excel导出")
    public void exportOtherDeviceHistory(HttpServletResponse response, String id) {
        List<OtherDeviceHistoryForExcelPO> otherDeviceHistory = otherDevicePeriodMapping.getExcelData(id);
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("其他设备周期历史","其他设备周期历史", ExcelType.XSSF), OtherDeviceHistoryForExcelPO.class, otherDeviceHistory);
        try {
            String fileName = "history.xlsx";
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.reset();
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION);
        } finally {
            try {
                response.getOutputStream().close();
            } catch (IOException e) {
                throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION);
            }
        }
    }

}
