package cc.crrc.manage.controller.monitor;

import cc.crrc.manage.common.annotation.InsertValidated;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.mapper.monitor.LifeForecastMapping;
import cc.crrc.manage.pojo.monitor.LifeForecastConfigDTO;
import cc.crrc.manage.pojo.monitor.LifeForecastDTO;
import cc.crrc.manage.pojo.monitor.RelayContactorLifePO;
import cc.crrc.manage.pojo.monitor.RelayContactorPO;
import cc.crrc.manage.service.monitor.LifeForecastService;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-12-22
 */
@Api(tags = "继电器/接触器寿命预测")
@RestController
@RequestMapping(value = "/monitor/lifeForecast")
public class LifeForecastContorller {

    @Autowired
    private LifeForecastService lifeForecastService;
    @Autowired
    private LifeForecastMapping lifeForecastMapping;

    /**
     * <AUTHOR> zhijian
     * @Date 2020/05/07
     * @param lifeForecastDTO
     * @return
     * 2021-12-13 lixin 因贵阳2将部件与寿命预测整合而更新
     */
    @SystemLog(optDesc = "部件、寿命预测列表（可根据componentKind区分）", optType = SystemLogEnum.SELECT)
    @ApiOperation("部件、寿命预测列表（可根据componentKind区分）")
    @PostMapping("/findRelayContactorBys")
    public Object findRelayContactorBys(@RequestBody LifeForecastDTO lifeForecastDTO){
        return lifeForecastService.findRelayContactorBys(lifeForecastDTO);
    }

    /**
     * <AUTHOR>
     * @Date 2021/05/27
     * @param
     * @return
     * 2021-12-13 lixin 因贵阳2将部件与寿命预测整合而更新
     */
    @SystemLog(optDesc = "初始化RelayContactorLifes数据", optType = SystemLogEnum.INSERT)
    @ApiOperation("初始化RelayContactorLifes数据")
    @GetMapping("/initRelayContactorLifes")
    public Object initRelayContactorLifes(String lineId){
        return lifeForecastService.initRelayContactorLifes(lineId);
    }

    /**
     * <AUTHOR>
     * @Date 2021/11/23
     * @param
     * @return
     * 2021-12-13 lixin 因贵阳2将部件与寿命预测整合而更新
     */
    @SystemLog(optDesc = "获取RelayContactorLifesHistory数据 不包含本身", optType = SystemLogEnum.SELECT)
    @ApiOperation("获取RelayContactorLifesHistory数据 不包含本身")
    @GetMapping("/RelayContactorLifesHistory")
    public Object relayContactorLifesHistory(String id){
        return lifeForecastService.relayContactorLifesHistory(id);
    }

    /**
     * <AUTHOR>
     * @Date 2021/06/01
     * @param
     * @return
     * 2021-12-13 lixin 因贵阳2将部件与寿命预测整合而更新
     */
    @SystemLog(optDesc = "重置继电器", optType = SystemLogEnum.UPDATE)
    @ApiOperation("重置继电器")
    @PutMapping("/replaceRelay")
    public Object replaceRelay(@RequestParam String id){
        return lifeForecastService.replaceRelay(id);
    }



    /**
     * <AUTHOR>
     * @Date 2021/12/16
     * @param
     * @return
     * @Description 构型-车辆构型-部件履历-单个部件信息更新
     */
    @SystemLog(optDesc = "构型-车辆构型-部件履历-单个部件信息更新", optType = SystemLogEnum.UPDATE)
    @ApiOperation("构型-车辆构型-部件履历-单个部件信息更新")
    @PutMapping("/relayContactorLife")
    public Object updateRelayContactorLife(@RequestBody RelayContactorLifePO relayContactorLifePO){
        return lifeForecastService.updateRelayContactorLife(relayContactorLifePO);
    }



    /**
     * <AUTHOR>
     * @Date 2021/06/01
     * @param
     * @return
     */
    @SystemLog(optDesc = "寿命预测更换历史Excel导出", optType = SystemLogEnum.DOWNLOAD)
    @GetMapping(value = "/exportRelayContactorHistory")
    @ApiOperation(value = "寿命预测更换历史Excel导出")
    public void exportRelayContactorHistory(HttpServletResponse response, String id) {
        List<RelayContactorPO> lifeForecast = lifeForecastMapping.getExcelData(id);
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("更换历史","更换历史", ExcelType.XSSF), RelayContactorPO.class, lifeForecast);
        try {
            String fileName = "history.xlsx";
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.reset();
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION);
        } finally {
            try {
                response.getOutputStream().close();
            } catch (IOException e) {
                throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION);
            }
        }
    }

    /**
     * <AUTHOR> kangjian
     * @Description  增加部件电气寿命信息
     * @Date 2021/11/23
     * @Param [relayContactorPO]
     * @return java.lang.Object
     * 2021-12-13 lixin 因贵阳2将部件与寿命预测整合而更新
     **/
    @SystemLog(optDesc = "增加部件电气寿命信息", optType = SystemLogEnum.INSERT)
    @PostMapping(value = "/insertRelayContactorInfo")
    @ApiOperation(value = "增加部件电气寿命信息", notes = "增加部件电气寿命信息")
    public Object insertRelayContactorInfo(@InsertValidated @RequestBody RelayContactorPO relayContactorPO) {
        return lifeForecastService.insertRelayContactorInfo(relayContactorPO);
    }

    /**
     * <AUTHOR> kangjian
     * @Description  修改部件电气寿命相关信息
     * @Date 2021/11/23
     * @Param [relayContactorPO]
     * @return java.lang.Object
     * 2021-12-13 lixin 因贵阳2将部件与寿命预测整合而更新
     **/
    @SystemLog(optDesc = "修改部件电气寿命相关信息", optType = SystemLogEnum.UPDATE)
    @PutMapping(value = "/updateRelayContactorInfo")
    @ApiOperation(value = "修改部件电气寿命相关信息", notes = "修改部件电气寿命相关信息")
    public Object updateRelayContactorInfo(@UpdateValidated @RequestBody RelayContactorPO relayContactorPO) {
        return lifeForecastService.updateRelayContactorInfo(relayContactorPO);
    }

    /**
     * <AUTHOR> kangjian
     * @Description  删除部件电气寿命信息
     * @Date 2021/11/23
     * @Param [id]
     * @return java.lang.Object
     **/
    @SystemLog(optDesc = "删除部件电气寿命信息", optType = SystemLogEnum.DELETE)
    @DeleteMapping(value = "/delRelayContactorInfo")
    @ApiOperation(value = "删除部件电气寿命信息", notes = "删除部件电气寿命信息")
    public Object delRelayContactorInfo(@RequestParam String id) {
        return lifeForecastService.delRelayContactorInfo(id);
    }

    /**
     * <AUTHOR> kangjian
     * @Description  查看部件寿命配置页面列表
     * @Date 2021/11/23
     * @Param [configDTO]
     * @return java.lang.Object
     * 2021-12-13 lixin 因贵阳2将部件与寿命预测整合而更新
     **/
    @SystemLog(optDesc = "查看部件寿命配置页面列表", optType = SystemLogEnum.SELECT)
    @ApiOperation("查看部件寿命配置页面列表")
    @PostMapping("/findRelayContactorConfigList")
    public Object findRelayContactorConfigList(@RequestBody LifeForecastConfigDTO configDTO){
        return lifeForecastService.findRelayContactorConfigList(configDTO);
    }
}
