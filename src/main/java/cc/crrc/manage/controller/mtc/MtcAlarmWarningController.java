package cc.crrc.manage.controller.mtc;


import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO;
import cc.crrc.manage.service.mtc.MtcAlarmWarningService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @FileName: MtcAlarmWarningController
 * @Author: liu xinchen
 * @Date: 2020/6/9 19:24
 * @Version 1.0
 */
@Api(tags = "故障诊断-故障查询")
@RestController
@RequestMapping("/mtcAlarmWarning")
public class MtcAlarmWarningController {

    @Autowired
    MtcAlarmWarningService mtcAlarmWarningService;


    @PostMapping(value = "/list")
    @SystemLog(optDesc = "故障诊断-故障查询", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询故障列表", notes = "多条件筛选")
    public Object listMtcAlarmWarning(@RequestBody MtcAlarmWarningVO mtcAlarmWarningVO) {
        return mtcAlarmWarningService.getMtcAlarmWarning(mtcAlarmWarningVO);
    }

    @PostMapping(value = "/ruleTestList")
    @SystemLog(optDesc = "故障诊断-故障查询-测试状态机理规则故障查询", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "测试状态机理规则故障查询", notes = "多条件筛选")
    public Object listMtcAlarmWarningRuleTest(@RequestBody MtcAlarmWarningVO mtcAlarmWarningVO) {
        return mtcAlarmWarningService.listMtcAlarmWarningRuleTest(mtcAlarmWarningVO);
    }

    // 2020年8月18日 房明宽
    @GetMapping(value = "/getRealtimeAlarmInformation")
    @SystemLog(optDesc = "获取实时告警信息", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "获取实时告警信息", notes = "模型编码：modelCode(String)，车辆编号：vehicleId(String)")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "modelCode", value = "modelCode", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "vehicleId", value = "vehicleId", required = true, dataType = "String"),
    })
    public Object getRealtimeAlarmInformation(@RequestParam("modelCode") String modelCode, @RequestParam("vehicleId") String vehicleId) {
        return mtcAlarmWarningService.getRealtimeAlarmInformationList(modelCode, vehicleId);
    }

    @GetMapping(value = "/id")
    @SystemLog(optDesc = "故障查询byId", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "故障查询单条记录", notes = "通过id索引")
    @ApiImplicitParam(paramType = "query", name = "id", value = "故障id", required = true, dataType = "String")
    public Object getMtcAutoFaultRecordById(String id, String faultSource) {
        return mtcAlarmWarningService.getMtcAlarmRecordById(id, faultSource);
    }

    @GetMapping(value = "/test/id")
    @SystemLog(optDesc = "测试机理故障查询byId", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "测试机理故障查询byId", notes = "通过id索引")
    @ApiImplicitParam(paramType = "query", name = "id", value = "故障id", required = true, dataType = "String")
    public Object getTestMtcAlarmWarningById(String id) {
        return mtcAlarmWarningService.getTestMtcAlarmWarningById(id);
    }

    /**
     * @Description: 线路监控、线网监控右侧 故障list查询更多接口
     * @Author: lixin
     * @Date: 2022/1/19 10:00
     * @Version 1.0
     */
    @PostMapping(value = "/getMoreSocketFaultList")
    @SystemLog(optDesc = "实时监控-故障和预警更多", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "实时监控-故障和预警更多")
    public Object getMoreSocketFaultList(@RequestBody MtcAlarmWarningVO mtcAlarmWarningVO) {
        return mtcAlarmWarningService.getMoreSocketFaultList(mtcAlarmWarningVO);
    }

    @PostMapping(value = "/fault")
    @SystemLog(optDesc = "故障诊断-故障查询-人工故障-新增", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "故障诊断-故障查询-人工故障-新增")
    public Object addManualAlarmFault(@RequestBody MtcAlarmWarningVO mtcAlarmWarningVO) {
        return mtcAlarmWarningService.addManualAlarmFault(mtcAlarmWarningVO);
    }

    @PutMapping(value = "/fault")
    @SystemLog(optDesc = "故障诊断-故障查询-人工故障-编辑", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "故障诊断-故障查询-人工故障-编辑")
    public Object updateManualAlarmFault(@RequestBody MtcAlarmWarningVO mtcAlarmWarningVO) {
        return mtcAlarmWarningService.updateManualAlarmFault(mtcAlarmWarningVO);
    }


    @DeleteMapping(value = "/fault")
    @SystemLog(optDesc = "故障诊断-故障查询-人工故障-删除", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "故障诊断-故障查询-人工故障-删除")
    public Object deleteManualAlarmFault(@RequestParam String ids) {
        return mtcAlarmWarningService.deleteManualAlarmFault(ids);
    }

    @GetMapping(value = "/fault/statistics")
    @SystemLog(optDesc = "线路监控-故障统计 & 预警统计", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "线路监控-故障统计 & 预警统计")
    public Object faultStatisticsOfLineMonitor(@RequestParam String lineId) {
        return mtcAlarmWarningService.faultStatisticsOfLineMonitor(lineId);
    }

    @GetMapping(value = "/trainMonitor/fault")
    @SystemLog(optDesc = "车辆监控-实时故障 & 状态预警查询", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "车辆监控-实时故障 & 状态预警查询")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "车辆代码", name = "vehicleCode", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(value = "故障源(1:预警/0:故障)", name = "faultSource", required = true, dataType = "String", paramType = "query"),
            @ApiImplicitParam(value = "页码", name = "pageNum", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(value = "每页数", name = "pageSize", required = true, dataType = "int", paramType = "query")
    })
    public Object getTrainMonitorFault(@RequestParam(required = true) String vehicleCode,
                                       @RequestParam(required = true) String faultSource,
                                       @RequestParam(required = true, defaultValue = "1") Integer pageNum,
                                       @RequestParam(required = true, defaultValue = "10") Integer pageSize) {
        return mtcAlarmWarningService.getMtcAlarmWarningBySocketPage(vehicleCode, faultSource, pageNum, pageSize);
    }

}

