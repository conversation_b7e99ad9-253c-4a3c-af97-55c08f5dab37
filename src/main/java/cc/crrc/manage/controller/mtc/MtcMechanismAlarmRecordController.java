package cc.crrc.manage.controller.mtc;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRecordDTO;
import cc.crrc.manage.service.mtc.MtcMechanismAlarmRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @FileName MtcAutoFaultRecordController
 * <AUTHOR>
 * @Date 2019/12/12 9:02
 * @Version 1.0
 * =============================暂时无用lixin 21-11-11================================
 **/
@Api(tags = "故障诊断-故障查询-机理故障查询")
@RestController
@RequestMapping("/mtcMechanismAlarmRecord")
public class MtcMechanismAlarmRecordController {
    @Autowired
    private MtcMechanismAlarmRecordService mtcMechanismAlarmRecordService;


    @PostMapping (value = "/list")
    @SystemLog(optDesc = "机理故障查询列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "机理故障查询列表", notes = "多条件筛选")
    public Object listMtcMechanismAlarmRecord(@RequestBody MtcMechanismAlarmRecordDTO mtcMechanismAlarmRecordDTO){
        return  mtcMechanismAlarmRecordService.listMtcMechanismAlarmRecord(mtcMechanismAlarmRecordDTO);
    }

    @PutMapping(value = "/close")
    @ApiOperation(value = "修改机理故障弹窗状态")
    public Object updateCloseStatue(String id,Boolean closeStatue){
        return  mtcMechanismAlarmRecordService.updateCloseStatue(id,closeStatue);
    }

}
