package cc.crrc.manage.controller.mtc;

import cc.crrc.manage.common.annotation.InsertValidated;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.mtc.MtcMechanismVariableRuleDTO;
import cc.crrc.manage.pojo.mtc.MtcMechanismVariableRuleEditDTO;
import cc.crrc.manage.service.mtc.MtcMechanismVariableRuleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * @ClassName MtcMechanismVariableRuleController
 * @Description 机理规则页面新增全局变量管理功能
 * <AUTHOR>
 * @Date 2021/1/4
 **/
@Api(tags = "故障诊断-全局变量管理")
@RestController
@RequestMapping("/variableRule")
public class MtcMechanismVariableRuleController {
    @Autowired
    private MtcMechanismVariableRuleService ruleService;


    @GetMapping(value = "/")
    @SystemLog(optDesc = "全局变量列表查询", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "变量列表查询", notes = "多条件筛选")
    public Object list(MtcMechanismVariableRuleDTO ruleDTO) {
        return ruleService.list(ruleDTO);
    }

    @PostMapping(value = "/")
    @SystemLog(optDesc = "添加全局变量", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "添加全局变量")
    public Object add(@InsertValidated MtcMechanismVariableRuleDTO ruleDTO) {
        return ruleService.add(ruleDTO);
    }

    @PutMapping(value = "/")
    @SystemLog(optDesc = "编辑全局变量信息", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "编辑全局变量信息")
    public Object update(@RequestBody @UpdateValidated MtcMechanismVariableRuleDTO ruleDTO) {
        return ruleService.update(ruleDTO);
    }

    @DeleteMapping(value = "/")
    @SystemLog(optDesc = "删除全局变量", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "删除全局变量")
    public Object delete(String ruleIdList) {
        return ruleService.delete(ruleIdList);
    }

    @PutMapping(value = "/edit")
    @SystemLog(optDesc = "编辑全局变量树图", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "编辑全局变量树图")
    public Object edit(@RequestBody @UpdateValidated MtcMechanismVariableRuleEditDTO ruleEditDTO) {
        return ruleService.edit(ruleEditDTO);
    }

    @GetMapping(value = "/json")
    @SystemLog(optDesc = "获取全局变量json", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "获取全局变量json")
    public Object getJson(@RequestParam String id) {
        return ruleService.getJson(id);
    }

    /**
     * <AUTHOR> kangjian
     * @Description // 机理规则配置 变量查询
     * @Date 2021/1/6
     * @Param [vehicleTypeId, location]
     * @return java.lang.Object
     **/
    @GetMapping(value = "/variable")
    @SystemLog(optDesc = "获取变量列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "获取变量列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleTypeId", value = "车型id", dataType = "String", required = true),
            @ApiImplicitParam(name = "location", value = "车厢", dataType = "String"),
            @ApiImplicitParam(name = "subsystem", value = "子系统", dataType = "String"),
            @ApiImplicitParam(name = "nameCn", value = "变量名称", dataType = "String")
    })
    public Object variable(@RequestParam String vehicleTypeId, String location, String subsystem, String nameCn) {
        return ruleService.variables(vehicleTypeId, location, subsystem, nameCn);
    }

}
