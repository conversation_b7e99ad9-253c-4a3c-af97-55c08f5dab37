package cc.crrc.manage.controller.mtc;


import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.mtc.*;
import cc.crrc.manage.service.mtc.MtcFaultRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@Api(tags = "故障工单")
@RestController
@RequestMapping("/mtcManualFaultRecord")
public class MtcFaultRecordController {

    @Autowired
    private MtcFaultRecordService mtcFaultRecordService;

    @PostMapping(value = "/list")
    @SystemLog(optDesc = "故障工单列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "故障工单列表", notes = "多条件筛选")
    public Object listMtcManualFaultRecord(@RequestBody MtcAlarmWarningDTO condition) {
        return mtcFaultRecordService.listMtcFaultRecord(condition);
    }

    @PostMapping(value = "/")
    @SystemLog(optDesc = "新增上报故障", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "新增上报故障")
    public Object addMtcManualFaultRecord(@RequestBody @Valid MtcFaultRecordPO mtcFaultRecordPO) {
        return mtcFaultRecordService.addFaultRecord(mtcFaultRecordPO);
    }

    @PutMapping(value = "/")
    @SystemLog(optDesc = "故障工单确认", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "故障工单确认")
    public Object updMtcManualFaultRecord(@RequestBody MtcFaultRecordPO mtcFaultRecordPO) {
        return mtcFaultRecordService.updMtcFaultRecordConfirm(mtcFaultRecordPO);
    }

    @PutMapping(value = "/info")
    @SystemLog(optDesc = "编辑故障工单", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "编辑故障工单")
    public Object updMtcManualFaultInfo(@RequestBody @Valid MtcFaultRecordPO mtcFaultRecordPO) {
        return mtcFaultRecordService.updMtcFaultRecordInfo(mtcFaultRecordPO);
    }

    @DeleteMapping(value = "/")
    @SystemLog(optDesc = "删除上报故障", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "删除上报故障")
    public Object delMtcManualFaultRecord(@RequestParam String id) {
        return mtcFaultRecordService.delMtcFaultRecord(id);
    }

    @PostMapping(value = "/fault")
    @SystemLog(optDesc = "故障工单新增-故障下拉列表", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "故障工单新增-故障下拉列表")
    public Object findCurrentFaultList(@RequestBody MtcAlarmWarningDTO codition) {
        return mtcFaultRecordService.findCurrentFaultList(codition);
    }

//    @GetMapping(value = "/fault/list/{faultSource}")
    @SystemLog(optDesc = "实时故障选择下拉列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "实时故障选择下拉列表")
    public Object getFaultList(@PathVariable() String faultSource, @RequestParam String vehicleId, @RequestParam String faultNameCn) {
        return mtcFaultRecordService.getFaultList(faultSource, vehicleId, faultNameCn);
    }
}
