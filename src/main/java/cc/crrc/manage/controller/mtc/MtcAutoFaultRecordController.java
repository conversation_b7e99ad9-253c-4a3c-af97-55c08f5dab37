package cc.crrc.manage.controller.mtc;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.mtc.MtcAutoFaultRecordDTO;
import cc.crrc.manage.service.mtc.MtcAutoFaultRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @FileName MtcAutoFaultRecordController
 * <AUTHOR>
 * @Date 2019/12/11 10:02
 * @Version 1.0
 **/
@Api(tags = "故障诊断-故障查询-自动上报故障查询")
@RestController
@RequestMapping("/mtcAutoFaultRecord")
public class MtcAutoFaultRecordController {
    @Autowired
    private MtcAutoFaultRecordService service;



    @PostMapping(value = "/list")
    @SystemLog(optDesc = "自动上报故障查询", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "自动上报故障查询列表", notes = "多条件筛选")
    public Object listMtcAutoFaultRecord(@RequestBody MtcAutoFaultRecordDTO mtcAutoFaultRecordDTO){
        return  service.listMtcAutoFaultRecord(mtcAutoFaultRecordDTO);
    }

//    @GetMapping(value = "/id")
    @SystemLog(optDesc = "自动上报故障查询byId", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "自动上报故障查询单条记录", notes = "通过id索引")
    @ApiImplicitParam(paramType = "query", name = "id", value = "自动上报故障id", required = true,  dataType = "String")
    @Deprecated
    public Object getMtcAutoFaultRecordById(@RequestParam("id")String id){
        return  service.getMtcAutoFaultRecordById(id);
    }

    @PutMapping(value = "/id")
    @SystemLog(optDesc = "自动上报故障记录 确认故障", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "自动上报故障记录 确认故障", notes = "通过id索引")
    @ApiImplicitParam(paramType = "query", name = "ids", value = "多个故障记录id，用逗号分隔", required = true, dataType = "String")
    public Object confirmMtcAutoFaultRecordById(@RequestParam("ids")String ids){
        return  service.confirmMtcAutoFaultRecordById(ids);
    }

    @GetMapping(value = "/getStruVehicleStructureComponent")
    @SystemLog(optDesc = "通过自动上报记录的故障位置查询车型构型展开信息", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "通过自动上报记录的故障位置查询车型构型展开信息", notes = "车辆信息")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "lineId", value = "lineId", required = true,  dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "vehicleTypeId", value = "vehicleTypeId", required = true,  dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "vehicleId", value = "vehicleId", required = true,  dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "structurePosition", value = "structurePosition", required = true,  dataType = "String")
    })
    public Object getStruVehicleStructureComponent(@RequestParam("lineId")String lineId,@RequestParam("vehicleTypeId")String vehicleTypeId,
            @RequestParam("vehicleId")String vehicleId,@RequestParam("structurePosition")String structurePosition){
        return  service.getStruVehicleStructureComponent(lineId,vehicleTypeId,vehicleId,structurePosition);
    }


}
