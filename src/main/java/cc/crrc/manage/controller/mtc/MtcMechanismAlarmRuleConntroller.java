package cc.crrc.manage.controller.mtc;

import cc.crrc.manage.common.annotation.InsertValidated;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRuleDTO;
import cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRuleEditDTO;
import cc.crrc.manage.service.mtc.MtcMechanismAlarmRuleService;
import cc.crrc.manage.service.stru.DigitalVehicleStruService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * @Author: Li <PERSON>hen<PERSON>
 * @Date: 2019-12-21
 */
@Api(tags = "故障诊断-机理管理")
@RestController
@RequestMapping("/rule")
public class MtcMechanismAlarmRuleConntroller {
    @Autowired
    private MtcMechanismAlarmRuleService ruleService;


    @GetMapping(value = "/")
    @SystemLog(optDesc = "机理规则列表查询", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "机理规则列表查询", notes = "多条件筛选")
    public Object list(MtcMechanismAlarmRuleDTO ruleDTO) {
        return ruleService.list(ruleDTO);
    }

    @PostMapping(value = "/")
    @SystemLog(optDesc = "添加机理规则", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "添加机理规则")
    public Object add(@InsertValidated MtcMechanismAlarmRuleDTO ruleDTO) {
        return ruleService.add(ruleDTO);
    }

    @PutMapping(value = "/")
    @SystemLog(optDesc = "编辑机理规则信息", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "编辑机理规则信息")
    public Object update(@RequestBody @UpdateValidated MtcMechanismAlarmRuleDTO ruleDTO) {
        return ruleService.update(ruleDTO);
    }

    @PutMapping(value = "/test")
    @SystemLog(optDesc = "机理规则测试开启关闭", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "机理规则测试开启关闭")
    public Object updateTestStatus(@RequestParam String id, @RequestParam boolean testStatus) {
        return ruleService.updateTestStatus(id,testStatus);
    }

    @DeleteMapping(value = "/")
    @SystemLog(optDesc = "删除机理规则", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "删除机理规则")
    public Object delete(String ruleIdList) {
        return ruleService.delete(ruleIdList);
    }

    @PutMapping(value = "/edit")
    @SystemLog(optDesc = "编辑机理规则树图", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "编辑机理规则树图")
    public Object edit(@RequestBody @UpdateValidated MtcMechanismAlarmRuleEditDTO ruleEditDTO) {
        return ruleService.edit(ruleEditDTO);
    }

    @GetMapping(value = "/json")
    @SystemLog(optDesc = "获取机理规则json", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "获取机理规则json")
    public Object getJson(@RequestParam String id) {
        return ruleService.getJson(id);
    }

    @GetMapping(value = "/vehicletypes")
    @SystemLog(optDesc = "获取车型下拉列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "获取车型下拉列表")
    public Object vehicleTypes() {
        return ruleService.vehicleTypes();
    }

    @GetMapping(value = "/system/{vehicleTypeId}")
    @SystemLog(optDesc = "获取车型下的系统列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "获取车型下的系统列表")
    public Object systems(String vehicleTypeId) {
        return ruleService.systems(vehicleTypeId);
    }

    @GetMapping(value = "/faults")
    @SystemLog(optDesc = "根据车型id车厢系统获取故障下拉列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据车型id车厢系统获取故障下拉列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleTypeId", value = "车型id", dataType = "String", required = true),
            @ApiImplicitParam(name = "location", value = "车厢，不填表示全车", dataType = "String", required = true),
            @ApiImplicitParam(name = "subsystem", value = "故障所属子系统", dataType = "String", required = true)
    })
    public Object faults(@RequestParam String vehicleTypeId, @RequestParam String location, @RequestParam String subsystem) {
        return ruleService.faults(vehicleTypeId, location, subsystem);
    }

    /**
     * @return
     * @Description  机理规则配置 数字量 信号查询
     * <AUTHOR> xin/Jin GuoYang
     * @Date 9:14 2020/3/6
     * @Param
     **/
    @GetMapping(value = "/booleanSignal")
    @SystemLog(optDesc = "获取数字量信号列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "获取数字量信号列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleTypeId", value = "车型id", dataType = "String", required = true),
            @ApiImplicitParam(name = "location", value = "车厢", dataType = "String"),
            @ApiImplicitParam(name = "subsystem", value = "子系统", dataType = "String"),
            @ApiImplicitParam(name = "nameCn", value = "数字量信号中文名称", dataType = "String"),
    })
    public Object booleanSignal(@RequestParam String vehicleTypeId, String location, String subsystem, String nameCn) {
        return ruleService.booleanSignal(vehicleTypeId, location, subsystem, nameCn);
    }

    /**
     * @return
     * @Description  机理规则配置 模拟量 信号查询
     * <AUTHOR> xin/Jin GuoYang
     * @Date 9:14 2020/3/6
     * @Param
     **/
    @GetMapping(value = "/analogSignal")
    @SystemLog(optDesc = "获取模拟量信号列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "获取模拟量信号列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleTypeId", value = "车型id", dataType = "String", required = true),
            @ApiImplicitParam(name = "location", value = "车厢", dataType = "String"),
            @ApiImplicitParam(name = "subsystem", value = "子系统", dataType = "String"),
            @ApiImplicitParam(name = "nameCn", value = "模拟量信号中文名称", dataType = "String"),
            @ApiImplicitParam(name = "signalCycle", value = "信号频率", dataType = "String")
    })
    public Object analogSignal(@RequestParam String vehicleTypeId, String location, String subsystem, String nameCn) {
        return ruleService.analogSignal(vehicleTypeId, location, subsystem, nameCn);
    }

    /**
     * @return
     * @Description  机理规则配置 故障列表 查询
     * <AUTHOR> xin
     * @Date 9:14 2020/3/6
     * @Param
     **/
    @GetMapping(value = "/faultList")
    @SystemLog(optDesc = "获取故障列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "获取故障列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleTypeId", value = "车型id", dataType = "String", required = true),
            @ApiImplicitParam(name = "location", value = "车厢", dataType = "String", required = true),
            @ApiImplicitParam(name = "subsystem", value = "系统id", dataType = "String", required = true)

    })
    public Object faultList(@RequestParam String vehicleTypeId, @RequestParam String location, @RequestParam String subsystem) {
        return ruleService.faultList(vehicleTypeId, location, subsystem);
    }


    @PostMapping(value = "/mechanismAlarmRuleList")
    @ApiOperation(value = "根据构型、车型 获取机理规则list")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "vehicleType", value = "车辆类型", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "structureCode", value = "构型code", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "pageNumber", value = "分页页码", required = true, dataType = "int"),
            @ApiImplicitParam(paramType = "query", name = "pageSize", value = "分页条数", required = true, dataType = "int")
    })
    public Object mechanismAlarmRuleListByStruCode(@RequestParam String vehicleType,@RequestParam String structureCode,
                                                   @RequestParam int pageNumber, @RequestParam int pageSize){
        return ruleService.mechanismAlarmRuleListByStruCodeAndVehicleType(vehicleType,structureCode,pageNumber,pageSize);
    }


    /**
     * @return
     * @Description  机理规则list 查询 给复制功能使用
     * <AUTHOR> xin
     * @Date 9:14 2022/1/27
     * @Param
     **/
    @PostMapping(value = "/ruleListForCopy")
    @ApiOperation(value = "根据线路id获取机理规则list")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "lineId", value = "线路id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "name", value = "机理规则名称", required = false, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "encryptionStatus", value = "加密状态", required = true, dataType = "Boolean")
    })
    public Object ruleListForCopy(@RequestParam String lineId,@RequestParam String name,
                                  @RequestParam Boolean encryptionStatus){
        return ruleService.ruleListForCopy(lineId,name,encryptionStatus);
    }

}
