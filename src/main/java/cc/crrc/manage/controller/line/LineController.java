package cc.crrc.manage.controller.line;

import cc.crrc.manage.cache.RefreshCache;
import cc.crrc.manage.common.annotation.InsertValidated;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.line.LineDTO;
import cc.crrc.manage.pojo.line.StationDTO;
import cc.crrc.manage.service.SysOrganizationService;
import cc.crrc.manage.service.line.LineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Api(tags = "线路管理")
@RestController
@RequestMapping(value = "/line")
public class LineController {
    @Autowired
    private LineService lineService;
    @Autowired
    private SysOrganizationService sysOrganizationService;

    @SystemLog(optDesc = "新增线路", optType = SystemLogEnum.INSERT)
    @PostMapping(value = "/")
    @ApiOperation(value = "新增线路", notes = "新增线路")
    public Object addLines(@InsertValidated @RequestBody LineDTO lineDTO) {
        return lineService.addLines(lineDTO);
    }

    @SystemLog(optDesc = "删除线路", optType = SystemLogEnum.DELETE)
    @DeleteMapping(value = "/id")
    @ApiOperation(value = "删除线路信息 ", notes = "删除线路信息")
    @ApiImplicitParam(name = "id", value = "线路id", required = true, dataType = "String")
    public Object deleteLines(@RequestParam String id) {
        return lineService.deleteLines(id);
    }

    @SystemLog(optDesc = "修改线路信息", optType = SystemLogEnum.UPDATE)
    @PutMapping(value = "/")
    @ApiOperation(value = "修改线路信息 ", notes = "修改线路信息")
    public Object updateLines(@UpdateValidated @RequestBody LineDTO lineDTO) {
        return lineService.updateLines(lineDTO);
    }

    @SystemLog(optDesc = "查询线路列表", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/list")
    @ApiOperation(value = "查询线路列表", notes = "查询线路列表")
    public Object getLines(@RequestBody LineDTO lineDTO) {
        return lineService.getLines(lineDTO);
    }

    @SystemLog(optDesc = "查询所在地", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/getLocations")
    @ApiOperation(value = "查询所在地 ", notes = "查询所在地")
    public Object getLocations() {
        return lineService.getLocations();
    }

    @SystemLog(optDesc = "查询部门列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询部门列表", notes = "查询部门列表")
    @PostMapping("/organizations")
    public Object listOrganizations() {
        return sysOrganizationService.listOrganizations();
    }

    @SystemLog(optDesc = "查询站点信息", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/getStations")
    @ApiOperation(value = "查询站点信息 ", notes = "查询站点信息")
    public Object getStations(@RequestBody StationDTO stationDTO) {
        return lineService.getStations(stationDTO);
    }

    @SystemLog(optDesc = "查询站点信息（仅供站点配置使用）", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/getAllStations")
    @ApiOperation(value = "查询站点信息（仅供站点配置使用） ", notes = "查询站点信息（仅供站点配置使用）")
    public Object getAllStations(@RequestBody StationDTO stationDTO) {
        return lineService.getAllStations(stationDTO);
    }

    @SystemLog(optDesc = "增加站点信息(单个站点)", optType = SystemLogEnum.INSERT)
    @PostMapping(value = "/insertStation")
    @ApiOperation(value = "增加站点信息(单个站点)", notes = "增加站点信息(单个站点)")
    @RefreshCache(values = "STATION")
    public Object insertStation(@InsertValidated @RequestBody StationDTO stationDTO) {
        return lineService.insertStation(stationDTO);
    }

    @SystemLog(optDesc = "增加站点信息(json串)", optType = SystemLogEnum.INSERT)
    @PostMapping(value = "/insertStations")
    @ApiOperation(value = "增加站点信息(json串)", notes = "增加站点信息(json串)")
    @RefreshCache(values = "STATION")
    public Object insertStation(@RequestBody String stations) {
        return lineService.insertStations(stations);
    }

    @SystemLog(optDesc = "修改站点信息(单个站点)", optType = SystemLogEnum.UPDATE)
    @PutMapping(value = "/updateStation")
    @ApiOperation(value = "修改站点信息(单个站点)", notes = "修改站点信息(单个站点)")
    @RefreshCache(values = "STATION")
    public Object updateStation(@UpdateValidated @RequestBody StationDTO stationDTO) {
        return lineService.updateStation(stationDTO);
    }

    @SystemLog(optDesc = "修改站点信息(json串)", optType = SystemLogEnum.UPDATE)
    @PutMapping(value = "/updateStations")
    @ApiOperation(value = "修改站点信息(json串)", notes = "修改站点信息(json串)")
    @RefreshCache(values = "STATION")
    public Object updateStations(@RequestBody String stations) {
        return lineService.updateStations(stations);
    }

    @SystemLog(optDesc = "删除站点信息(单个站点)", optType = SystemLogEnum.DELETE)
    @DeleteMapping(value = "/deleteStation")
    @ApiOperation(value = "删除站点信息(单个站点)", notes = "删除站点信息(单个站点)")
    @RefreshCache(values = "STATION")
    public Object deleteStation(@RequestBody StationDTO stationDTO) {
        return lineService.deleteStation(stationDTO);
    }

    @SystemLog(optDesc = "删除站点信息(json串)", optType = SystemLogEnum.DELETE)
    @DeleteMapping(value = "/deleteStations")
    @ApiOperation(value = "删除站点信息(json串)", notes = "删除站点信息(json串)")
    @RefreshCache(values = "STATION")
    public Object deleteStation(@RequestBody String stations) {
        return lineService.deleteStations(stations);
    }

    @SystemLog(optDesc = "通过id查询线路", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "通过id查询线路 ", notes = "通过id查询线路")
    @ApiImplicitParam(name = "id", value = "线路id", required = true, dataType = "String")
    @GetMapping("/id")
    public Object getLineById(@RequestParam String id) {
        return lineService.getLineById(id);
    }
}
