package cc.crrc.manage.controller;

import cc.crrc.manage.common.annotation.InsertValidated;
import cc.crrc.manage.common.annotation.LogParam;

import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.pojo.User;
import cc.crrc.manage.service.UserManageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.hibernate.validator.constraints.Length;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.IOException;

import static cc.crrc.manage.common.utils.CodecUtils.regexp;

/**
 * @FileName UserManageController
 * <AUTHOR> yuxi
 * @Date 2019/6/5 9:40
 * @Version 1.0
 **/
@Api(tags = "用户管理接口")
@RestController
@Validated
@RequestMapping("/userManager")
public class UserManageController {
    @Autowired
    private UserManageService userManageService;

    /**
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @Description 新增用户
     * <AUTHOR> yuxi
     * @Date 11:20 2019/6/5
     * @Param [user]
     **/
    @SystemLog(optDesc="增加用户", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "新增用户")
    @PostMapping("/user")
    public Object addNewUser(@InsertValidated @RequestBody User user) {
        User userTemp = userManageService.getUserByUsername(user.getUsername());
        if (userTemp != null) {
            throw new RestApiException(ExceptionInfoEnum.DATA_DUPLICATE_EXCEPTION.getErrorCode(), "登录名称已存在！");
        }

        userManageService.addNewUser(user);
        return null;
    }

    /**
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @Description 更新用户
     * <AUTHOR> yuxi
     * @Date 13:09 2019/6/5
     * @Param [user]
     **/
    @SystemLog(optDesc="修改用户信息", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "更新用户信息")
    @PutMapping("/user")
    public Object updateUser(@RequestBody @Valid User user) {
        int count = userManageService.checkUsernameExist(user.getId(), user.getUsername(),user.getName());
        if (count > 0) {
            throw new RestApiException(ExceptionInfoEnum.DATA_DUPLICATE_EXCEPTION.getErrorCode(), "登录名称已存在！");
        }
        userManageService.updateUser(user);
        return null;
    }

    /**
     * @return cc.crrc.manage.pojo.User
     * @Description 通过name查询用户
     * <AUTHOR> yuxi
     * @Date 16:11 2019/6/6
     * @Param [user]
     **/
    @ApiOperation(value = "通过name查询用户")
    @GetMapping("/user/username")
    public Object getUserByName(@LogParam(description = "用户名")@RequestParam("username") String username) {
        return userManageService.getUserByUsername(username);
    }

    /**
     * @return cc.crrc.manage.pojo.User
     * @Description 通过id查询用户
     * <AUTHOR> yuxi
     * @Date 16:11 2019/6/6
     * @Param [user]
     **/
    @ApiOperation(value = "通过id查询用户")
    @GetMapping("/user/id")
    public Object getUserById(@LogParam(description = "用户id")@RequestParam("id") String id) {
        return userManageService.getUserById(id);
    }

    /**
     * @return cc.crrc.manage.pojo.User
     * @Description 通过id删除用户（逻辑删除）
     * <AUTHOR> yuxi
     * @Date 14:39 2019/6/10
     * @Param [user]
     **/
    @SystemLog(optDesc="删除用户", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "通过id删除用户")
    @DeleteMapping("/user/id")
    public Object deleteUserById(@LogParam(description = "用户id")@RequestParam("id") String id) {
        userManageService.deleteUserById(id);
        return null;
    }

    /**
     * 修改密码
     *
     * @param id
     * @param passwordNew
     * @return
     */
    @SystemLog(optDesc="修改密码", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "更新用户密码")
    @PutMapping("/user/password")
    public Object updatePassword(@LogParam(description = "用户id")@RequestParam("id") String id,
                                 @LogParam(description = "新密码")
                                 @Pattern(regexp = regexp,message = "密码必须同时包含字母、数字、和下列所示的!@#$%^&*_-特殊字符")
                                 @Size(min = 6, max = 40, message = "密码长度介于6~40位之间")
                                 @RequestParam("passwordNew") String passwordNew) {
        return userManageService.updatePassword(id, passwordNew);
    }

    /**
     * 用户中心修改密码
     *
     * @param id
     * @param passwordNew
     * @return
     */
    @SystemLog(optDesc="用户修改密码", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "用户修改密码")
    @PostMapping("/user/userUpdatePassword")
    public Object userUpdatePassword(@LogParam(description = "id")@RequestParam("id") String id,
                                     @LogParam(description = "原密码") @RequestParam("passwordOld") String passwordOld,
                                     @Pattern(regexp = regexp, message = "密码必须同时包含字母、数字、和下列所示的!@#$%^&*_-特殊字符")
                                         @Size(min = 6, max = 40, message = "密码长度介于6~40位之间")
                                         @RequestParam("passwordNew") String passwordNew) {
        return userManageService.userUpdatePassword(id,passwordOld,passwordNew);
    }

    /**
     * 重置密码
     *
     * @param id
     * @return
     */
    @SystemLog(optDesc="修改密码（重置）", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "重置用户密码")
    @PutMapping("/user/password/reset")
    public Object resetPassword(@LogParam(description = "id")@RequestParam("id") String id) {
        return userManageService.resetPassword(id);
    }

    /**
     * 上传头像
     * 2019/10/24
     */
    @PostMapping("/user/uploadImg")
    @ApiOperation(value = "上传头像")
    public Object uploadImg(@RequestParam(value="file",required=false) MultipartFile file){
        return userManageService.uploadImg(file);
    }
    /**
     * 查看头像
     * 2019/10/24
     */
    @PostMapping("/user/getImg")
    @ApiOperation(value = "查看头像")
    public Void getImg(@RequestParam("image") String image, HttpServletResponse response) throws IOException {
        return userManageService.getImg(image,response);
    }

    /**
     * @return java.util.Map<java.lang.String, java.lang.String>
     * @Description 修改用户信息（用户中心）
     * <AUTHOR>
     * @Date  2019/10/25
     * @Param [user]
     **/
    @SystemLog(optDesc="修改用户信息（用户中心）", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "更新用户信息")
    @PutMapping("/updateUserForPersonalCenter")
    public Object updateUserForPersonalCenter(@UpdateValidated @RequestBody  User user) {
        return userManageService.updateUserForPersonalCenter(user);
    }

}
