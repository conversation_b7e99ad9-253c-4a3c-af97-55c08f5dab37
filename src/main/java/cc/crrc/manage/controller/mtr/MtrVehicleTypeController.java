package cc.crrc.manage.controller.mtr;

import cc.crrc.manage.common.annotation.InsertValidated;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.mtr.MtrVehicleTypeStructureVO;
import cc.crrc.manage.service.mtr.MtrVehicleTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @FileName MtrVehicleTypeController
 * <AUTHOR> yux<PERSON>
 * @Date 2019/11/16 16:33
 * @Version 1.0
 **/
@Api(tags = "车辆型号")
@RestController
@RequestMapping("/vehicleType")
@Validated
public class MtrVehicleTypeController {
    @Autowired
    private MtrVehicleTypeService vehicleTypeService;

    @SystemLog(optDesc = "查询车型信息（根据车型id）", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询车型信息（根据车型id）")
    @GetMapping("/id")
    @ApiImplicitParam(paramType = "query", name = "vehicleTypeId", value = "车型id", required = true, dataType = "String")
    public Object getVehicleTypeById(@RequestParam String vehicleTypeId) {
        return vehicleTypeService.getVehicleTypeById(vehicleTypeId);
    }

    @SystemLog(optDesc = "根据车辆类型id，查询携带图片的车辆类型信息", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据车辆类型id，查询携带图片的车辆类型信息）")
    @GetMapping("/id/detail")
    @ApiImplicitParam(paramType = "query", name = "vehicleTypeId", value = "车型id", required = true, dataType = "String")
    public Object getVehicleTypeWithPictureById(@RequestParam String vehicleTypeId) {
        return vehicleTypeService.getVehicleTypeWithPictureById(vehicleTypeId);
    }

    @SystemLog(optDesc = "新增车辆型号", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "新增车辆型号")
    @PostMapping("/vehicleType")
    public Object insertVehicleType(@RequestBody @InsertValidated MtrVehicleTypeStructureVO vehicleTypeStructure) {
        return vehicleTypeService.insertVehicleType(vehicleTypeStructure);
    }

    @SystemLog(optDesc = "更新车辆型号", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "更新车辆型号")
    @PutMapping("/vehicleType")
    public Object updateVehicleType(@RequestBody @UpdateValidated MtrVehicleTypeStructureVO vehicleTypeStructure) {
        return vehicleTypeService.updateVehicleType(vehicleTypeStructure);
    }


    @SystemLog(optDesc = "上传Excel 同步车型构型列表", optType = SystemLogEnum.UPLOAD)
    @ApiOperation(value = "上传Excel 同步车型构型列表")
    @PutMapping("/vehicleType/excel")
    public Object updateVehicleTypeByExcel(@RequestBody MtrVehicleTypeStructureVO vehicleTypeStructure) {
        return vehicleTypeService.updateVehicleTypeByExcel(vehicleTypeStructure);
    }


    @SystemLog(optDesc = "查询车型列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询车型列表")
    @GetMapping("/vehicleType/list")
    public Object listVehicleType() {
        return vehicleTypeService.listVehicleType();
    }

    @SystemLog(optDesc = "删除车辆型号", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "删除车辆型号")
    @DeleteMapping("/vehicleType/id")
    @ApiImplicitParam(paramType = "query", name = "id", value = "车型id", required = true, dataType = "String")
    public Object deleteVehicleType(@RequestParam String id) {
        return vehicleTypeService.deleteVehicleType(id);
    }

    @SystemLog(optDesc = "查询车型文件列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询车型文件列表")
    @GetMapping("/vehicleTypeFile/list/vehicleTypeId")
    @ApiImplicitParam(paramType = "query", name = "id", value = "车型id", required = true, dataType = "String")
    public Object insertVehicleTypeFile(@RequestParam String id) {
        return vehicleTypeService.listVehicleTypeFile(id);
    }

    @SystemLog(optDesc = "新增车型文件", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "新增车型文件")
    @PostMapping("/vehicleTypeFile")
    @ApiImplicitParam(paramType = "query", name = "id", value = "车型id", required = true, dataType = "String")
    public Object insertVehicleTypeFile(@RequestBody @InsertValidated SysFilePO sysFile, @RequestParam String id) {
        vehicleTypeService.insertVehicleTypeFile(sysFile, id);
        return null;
    }

    @SystemLog(optDesc = "删除车型文件", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "删除车型文件")
    @DeleteMapping("/vehicleTypeFile")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "id", value = "车型id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "fileId", value = "文件id", required = true, dataType = "String")
    })
    public Object deleteVehicleTypeFileRelation(@RequestParam String id, @RequestParam String fileId) {
        return vehicleTypeService.deleteVehicleTypeFileRelation(id, fileId);
    }
}
