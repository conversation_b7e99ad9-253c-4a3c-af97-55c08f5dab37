package cc.crrc.manage.controller.mtr.vehicle;

    /*    @ApiImplicitParams：用在请求的方法上，表示一组参数说明
    @ApiImplicitParam：用在@ApiImplicitParams注解中，指定一个请求参数的各个方面
    name：参数名
    value：参数的汉字说明、解释
    required：参数是否必须传
    paramType：参数放在哪个地方
            · header --> 请求参数的获取：@RequestHeader
            · query --> 请求参数的获取：@RequestParam
            · path（用于restful接口）--> 请求参数的获取：@PathVariable
            · body（不常用）
            · form（不常用）
    dataType：参数类型，默认String，其它值dataType="Integer"
    defaultValue：参数的默认值*/


import cc.crrc.manage.common.annotation.InsertValidated;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.mtr.ConfigSoftwareComponentPO;
import cc.crrc.manage.pojo.mtr.VehicleSoftwareDTO;
import cc.crrc.manage.pojo.mtr.VehicleSoftwarePO;
import cc.crrc.manage.service.mtr.VehicleSoftwareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 车载软件管理
 * <AUTHOR>
 * 2019/11/18
 **/

@RestController
@RequestMapping(value = "/VehicleSoftware")
@Api(tags = "车载软件管理")
public class VehicleSoftwareController {



    @Autowired
    private VehicleSoftwareService vehicleSoftwareService;

    /**
     * 软件管理栏目列表
     * @param vehicleSoftwareDTO
     * @return
     */
    @PostMapping(value = "/list")
    @SystemLog(optDesc="车载软件列表分页查询（车型、部件型号、软件名称、版本、制造商、更新人模糊查询)", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "车载软件列表分页查询",notes = "按车型、部件型号、软件名称、版本、制造商、更新人模糊查询。不传分页信息，默认不分页。")
    public Object VehicleSoftwareList(VehicleSoftwareDTO vehicleSoftwareDTO) {
        return vehicleSoftwareService.VehicleSoftwareList(vehicleSoftwareDTO);
    }

    /**
     * 新增软件版本
     * @param vehicleSoftwarePO
     * @return
     */
    @PostMapping(value = "/add")
    @SystemLog(optDesc="添加软件版本信息", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "添加软件版本信息",notes = "车型、部件型号、软件名称。其余项目可选。")
    public Object addVehicleSoftware(@InsertValidated @RequestBody VehicleSoftwarePO vehicleSoftwarePO) {
        return vehicleSoftwareService.addVehicleSoftware(vehicleSoftwarePO);
    }

    /**
     * 修改软件版本
     * @param vehicleSoftwarePO
     * @return
     */
    @PostMapping(value = "/update")
    @SystemLog(optDesc="修改软件版本信息", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "修改软件版本信息")
    public Object updateVehicleSoftware(@UpdateValidated @RequestBody VehicleSoftwarePO vehicleSoftwarePO) {
        return vehicleSoftwareService.updateVehicleSoftware(vehicleSoftwarePO);
    }

    /**
     * 升级软件版本
     * @param vehicleSoftwarePO
     * @return
     */
    @PostMapping(value = "/upgrading")
    @SystemLog(optDesc="升级软件版本信息", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "升级软件版本信息",notes = "填写软件版本和备注，其他数据上一层传入")
    public Object upgradingVehicleSoftware(@InsertValidated @RequestBody VehicleSoftwarePO vehicleSoftwarePO) {
        return vehicleSoftwareService.upgradingVehicleSoftware(vehicleSoftwarePO);
    }

    /**
     * 匹配车载软件加载列表
     * @param componentTypeId
     * <AUTHOR>
     * @return
     */
    @GetMapping (value = "/componentList")
    @SystemLog(optDesc="匹配车载软件加载列表", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "匹配车载软件加载列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "componentTypeId",  value = "部件型号ID",required = true, dataType = "Long")
    })
    public Object getComponentListByTypeID(@RequestParam String componentTypeId) {
        return vehicleSoftwareService.getComponentListByTypeID(componentTypeId);
    }


    /**
     * 获取部件已匹配软件列表
     * @param componentId,softwareId
     * <AUTHOR>
     * @return
     */
    @GetMapping (value = "/componentSoftwareList")
    @SystemLog(optDesc="获取部件已匹配软件列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "获取部件已匹配软件列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "componentId",  value = "部件ID",required = true, dataType = "Long"),
            @ApiImplicitParam(name = "softwareId",  value = "软件版本ID",required = true, dataType = "Long")
    })
    public Object getComponentSoftwareListByComponentID(@RequestParam String componentId,@RequestParam String softwareId) {
        return vehicleSoftwareService.getComponentSoftwareListByComponentID(componentId,softwareId);
    }


    /**
     * 软件版本匹配车辆部件
     * @param configSoftwareComponentPOList
     * @return
     */
    @PostMapping(value = "/config")
    @SystemLog(optDesc="匹配车辆部件软件版本", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "匹配车辆部件软件版本",notes = "选择相关部件进行配置")
    public Object configVehicleSoftware(@InsertValidated @RequestBody List<ConfigSoftwareComponentPO> configSoftwareComponentPOList,HttpServletRequest request) {
        String componentTypeId = request.getParameter("componentTypeId");
        return vehicleSoftwareService.configVehicleSoftware(configSoftwareComponentPOList,componentTypeId);
    }

    /**
     * 删除软件版本
     * @param softwareId
     * @param componentTypeId
     * @param vehicleTypeId
     * @return
     */
    @SystemLog(optDesc = "删除车辆部件软件版本", optType = SystemLogEnum.DELETE)
    @ApiOperation("删除车辆部件软件版本")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType="query", name = "softwareId", value = "软件版本ID", required = true, dataType = "Long"),
            @ApiImplicitParam(paramType="query", name = "componentTypeId", value = "部件型号ID", required = true, dataType = "Long"),
            @ApiImplicitParam(paramType="query", name = "vehicleTypeId", value = "车辆型号ID", required = true, dataType = "Long")
    })
    @DeleteMapping("/deleteVehicleSoftware")
    public Object deleteVehicleSoftware(@RequestParam String softwareId,@RequestParam String componentTypeId,@RequestParam String vehicleTypeId ) {
        return vehicleSoftwareService.deleteVehicleSoftware(softwareId,componentTypeId,vehicleTypeId);
    }

    /**
     * 软件版本关联文件上传功能
     * @param sysFile
     * @param id
     * @return
     */
    @PostMapping(value = "/file/uploadSoftwareFile")
    @SystemLog(optDesc="上传文件", optType = SystemLogEnum.UPLOAD)
    @ApiOperation(value = "上传文件",notes = "车载软件id、文件名、文件地址、文件分组、文件远程地址必填项。")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",  value = "软件版本ID",required = true, dataType = "Long")
    })
    public Object saveVehicleSoftwareFile(@RequestBody SysFilePO sysFile, @RequestParam String id){
        return vehicleSoftwareService.saveVehicleSoftwareFile(sysFile,id);
    }

    /**
     * 获取文件列表
     * @param id
     * @return
     */
    @GetMapping(value = "/file/getSoftwareFile")
    @SystemLog(optDesc="获取文件列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "获取文件列表",notes = "车载软件id、文件名、文件地址、文件分组、文件远程地址信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",  value = "软件版本ID",required = true, dataType = "Long")
    })
    public Object getVehicleSoftwareFile(@RequestParam String id){
        return vehicleSoftwareService.getVehicleSoftwareFile(id);
    }

    /**
     * 删除关联软件版本
     * @param fileId
     * @return
     */
    @DeleteMapping(value = "/file/deleteSoftwareFile")
    @SystemLog(optDesc="删除列表中文件", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "删除列表中文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileId",  value = "文件ID",required = true, dataType = "Long")
    })
    public Object deleteVehicleSoftwareFile(@RequestParam String fileId){
        return vehicleSoftwareService.deleteVehicleSoftwareFile(fileId);
    }
}
