package cc.crrc.manage.controller.mtr.vehicle;

import cc.crrc.manage.common.annotation.InsertValidated;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.component.ManufacturerParameterDTO;
import cc.crrc.manage.service.mtr.MtrManufacturerParameterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "车辆管理-制造参数")
@RestController
@RequestMapping("/vehicle_manu")
public class MtrVehicleManuParamController {
	@Autowired
	private MtrManufacturerParameterService mtrManufacturerParameterService;
	
	@SystemLog(optDesc="查询部件制造参数", optType = SystemLogEnum.SELECT)
	@GetMapping(value = "/getManufParam")
	@ApiOperation(value = "查询部件制造参数", notes = "部件ID:componentId.注：车型使用时将部件ID更换成车辆ID：vehicleId")
	public Object getManufParam(@RequestParam(required = false) String componentId, @RequestParam(required = false)String vehicleId) {
		return mtrManufacturerParameterService.getManufParam(componentId,vehicleId);
	}

	@SystemLog(optDesc="增加部件制造参数", optType = SystemLogEnum.INSERT)
	@PostMapping(value = "/insertManufParam")
	@ApiOperation(value = "增加部件制造参数", notes = "部件ID:componentId,名称:itemName,值:itemValue.注：车型使用时将部件ID更换成车辆ID：vehicleId")
	public Object insertManufParam(@InsertValidated@RequestBody ManufacturerParameterDTO manufParam) {
		return mtrManufacturerParameterService.insertManufParam(manufParam);
	}
	
	@SystemLog(optDesc="修改部件制造参数", optType = SystemLogEnum.UPDATE)
	@PutMapping(value = "/updateManufParam")
	@ApiOperation(value = "修改部件制造参数", notes = "参数ID:id,部件ID:componentId,名称:itemName,值:itemValue.注：车型使用时将部件ID更换成车辆ID：vehicleId")
	public Object updateManufParam(@UpdateValidated@RequestBody ManufacturerParameterDTO manufParam) {
		return mtrManufacturerParameterService.updateManufParam(manufParam);
	}
	
	@SystemLog(optDesc="删除部件制造参数", optType = SystemLogEnum.DELETE)
	@DeleteMapping(value = "/deleteManufParam")
	@ApiOperation(value = "删除部件制造参数", notes = "参数ID:id")
	public Object deleteManufParam(@RequestParam String id) {
		return mtrManufacturerParameterService.deleteManufParam(id);
	}
}
