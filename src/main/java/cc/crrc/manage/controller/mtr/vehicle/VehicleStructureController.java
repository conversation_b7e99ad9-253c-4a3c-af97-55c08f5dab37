package cc.crrc.manage.controller.mtr.vehicle;

import cc.crrc.manage.cache.RefreshCache;
import cc.crrc.manage.common.annotation.*;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.line.LineDTO;
import cc.crrc.manage.pojo.mtr.MtrVehicleDTO;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import cc.crrc.manage.pojo.mtr.MtrVehicleVO;
import cc.crrc.manage.pojo.mtr.StruVehicleStructureComponentUpdateDTO;
import cc.crrc.manage.service.component.ComponentService;
import cc.crrc.manage.service.line.LineService;
import cc.crrc.manage.service.mtr.MtrVehicleService;
import cc.crrc.manage.service.mtr.StruVehicleStructureComponentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 车辆构型管理
 *
 * <AUTHOR> GuoYang
 * 2019/12/18
 **/
@Api(tags = "车辆管理-构型树")
@RestController
@RequestMapping("/vehicle_structure")
public class VehicleStructureController {
    @Autowired
    private ComponentService componentService;
    @Autowired
    private MtrVehicleService mtrVehicleService;
    @Autowired
    private LineService lineService;
    @Autowired
    private StruVehicleStructureComponentService struVehicleStructureComponentService;

    @PostMapping(value = "/getLines")
    @SystemLog(optDesc = "查询线路信息", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询线路信息 ", notes = "查询线路信息")
    public Object getLines(@RequestBody LineDTO lineDTO) {
        return lineService.getLines(lineDTO);
    }

    @GetMapping(value = "/vehicle/list")
    @SystemLog(optDesc = "查询车辆列表（支持线路id查询)", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询车辆列表", notes = "按线路id查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "metroLineId", value = "线路id", required = true, dataType = "String")
    })
    public Object listVehicle(@LogParam(description = "线路id") @RequestParam String metroLineId) {
        return mtrVehicleService.listVehicle(metroLineId);
    }

    @PostMapping(value = "/vehicle")
    @SystemLog(optDesc = "添加车辆信息", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "添加车辆信息", notes = "车辆名称不可为空。无需传递id。其余项目可选。现存在，车辆编码不可重复的问题")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleTypeId", value = "车辆类型id", required = true, dataType = "String")
    })
    @RefreshCache(values = "VEHICLE")
    public Object addVehicle(@InsertValidated @RequestBody MtrVehiclePO mtrVehiclePO, @LogParam(description = "车辆类型id") @RequestParam String vehicleTypeId) {
        mtrVehicleService.addVehicle(mtrVehiclePO, vehicleTypeId);
        return null;
    }

    /**
     *
     * <AUTHOR>
     */
    @PostMapping(value = "/add")
    @SystemLog(optDesc = "添加车辆构型信息", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "根据id批量添加车辆构型信息", notes = "根据id批量添加车辆构型信息,支持批量删,id用\",\"分割")
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "idList", value = "构型id", required = true, dataType = "String"),
//            @ApiImplicitParam(name = "vehicleId", value = "车辆id", required = true, dataType = "String")
//    })
    public Object addVehicleInfo(@RequestBody MtrVehicleDTO mtrVehicleDTO) {
        struVehicleStructureComponentService.addVehicleInfo(mtrVehicleDTO.getIdList(),mtrVehicleDTO.getVehicleId());
    	return null;
    }

    /**
     *
     * <AUTHOR>
     */
    @PutMapping(value = "/update")
    @SystemLog(optDesc = "修改车辆构型信息", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "根据id修改车辆构型信息", notes = "根据id修改车辆构型信息")
    public Object updateVehicleStructureBasicInfo(@RequestBody StruVehicleStructureComponentUpdateDTO struVehicleStructureComponentUpdateDTO) {
    	struVehicleStructureComponentService.updateVehicleStructureBasicInfo(struVehicleStructureComponentUpdateDTO);
    	return null;
    }

    /**
     *
     * <AUTHOR>
     */
    @DeleteMapping(value = "/delete")
    @SystemLog(optDesc = "删除车辆构型信息", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "根据id删除车辆构型信息", notes = "根据id删除车辆构型信息")
    public Object deleteVehicleInfo(@RequestParam String id) {
        struVehicleStructureComponentService.deleteVehicleInfo(id);
        return null;
    }

    @DeleteMapping(value = "/vehicle")
    @SystemLog(optDesc = "根据id批量删除车辆信息", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "根据id批量删除车辆信息", notes = "根据id批量删除车辆信息,支持批量删除,id用\",\"分割")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idList", value = "车辆id", required = true, dataType = "String")
    })
    @RefreshCache(values = "VEHICLE")
    public Object batchDeleteVehicle(@LogParam(description = "车辆id") @RequestParam String idList) {
        mtrVehicleService.batchDeleteVehicle(idList);
        return null;
    }

    @PutMapping(value = "/vehicle")
    @SystemLog(optDesc = "更新车辆信息", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "更新车辆信息", notes = "id不可为空。其余项目可选，不传不改。")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleTypeId", value = "车辆类型id", required = true, dataType = "String")
    })
    @RefreshCache(values = "VEHICLE")
    public Object updateVehicle(@UpdateValidated @RequestBody MtrVehiclePO mtrVehiclePO, @LogParam(description = "车辆类型id") @RequestParam String vehicleTypeId) {
        mtrVehicleService.updateVehicle(mtrVehiclePO, vehicleTypeId);
        return null;
    }

    @GetMapping(value = "/vehicle")
    @SystemLog(optDesc = "根据车辆id，查询带图片的车辆基本信息", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询带图片的车辆基本信息", notes = "根据车辆id查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "车辆id", required = true, dataType = "String")
    })
    public Object getVehicleById(@LogParam(description = "车辆id") @RequestParam String id) {
        return mtrVehicleService.getVehicleById(id);
    }

    @GetMapping(value = "/vehicle/structure")
    @SystemLog(optDesc = "根据车辆id查询构型", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据车辆id查询构型")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleId", value = "车辆id", required = true, dataType = "String"),
            @ApiImplicitParam(name = "nodeName", value = "节点名称", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "time", value = "回溯时间", paramType = "query", dataType = "String")
    })
    public Object getStructureByVehicleId(@LogParam(description = "车辆id") @RequestParam String vehicleId,
                                          @LogParam(description = "节点名称") String nodeName,
                                          @LogParam(description = "回溯时间")  String time) {
        return mtrVehicleService.getStructureByVehicleId(vehicleId, nodeName,time);
    }

    @GetMapping(value = "/component")
    @SystemLog(optDesc = "查询部件详细信息", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询部件详细信息 ", notes = "部件ID：componentId（String）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "componentId", value = "部件ID", required = true, dataType = "String")
    })
    public Object getComponentDetailById(@LogParam(description = "部件ID") @RequestParam String componentId) {
        return componentService.getComponentDetailById(componentId);
    }

    @GetMapping(value = "/diffStructure/list")
    @SystemLog(optDesc = "查询指定构型位置下可添加的车辆构型", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询指定构型位置下可添加的车辆构型 ")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleId", value = "车辆ID", required = true, dataType = "String"),
            @ApiImplicitParam(name = "structureCode", value = "构型编码", required = true, dataType = "String")
    })
    public Object getDiffStructureTree(@LogParam(description = "车辆ID") @RequestParam String vehicleId, @LogParam(description = "构型编码") @RequestParam String structureCode) {
        return struVehicleStructureComponentService.getDiffStructureTree(vehicleId,structureCode);
    }

    @GetMapping(value = "/vehicle/selectVehicleStartDate")
    @SystemLog(optDesc = "根据车辆id查询构型最早存在时间", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据车辆id查询构型最早存在时间")
    @ApiImplicitParam(name = "vehicleId", value = "车辆id", required = true, dataType = "String")
    public Object selectVehicleStartDate(@LogParam(description = "车辆id") @RequestParam String vehicleId){
        return mtrVehicleService.selectVehicleStartDate(vehicleId);
    }
}
