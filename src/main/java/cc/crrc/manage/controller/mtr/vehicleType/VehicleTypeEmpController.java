package cc.crrc.manage.controller.mtr.vehicleType;

import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cc.crrc.manage.common.annotation.LogParam;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.pojo.component.ComponentTypeDTO;
import cc.crrc.manage.pojo.mtr.ManufacturerDTO;
import cc.crrc.manage.pojo.mtr.ManufacturerEmployeePO;
import cc.crrc.manage.service.component.ComponentTypeService;
import cc.crrc.manage.service.mtr.ManufacturerEmployeeService;
import cc.crrc.manage.service.mtr.ManufacturerService;
import cc.crrc.manage.service.mtr.MtrVehicleTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping(value = "/VehicleTypeEmpController")
@Api(tags = "车辆型号-联系人管理")

public class VehicleTypeEmpController {

    @Autowired
    private ManufacturerService manufacturerService;

    @Autowired
    private ManufacturerEmployeeService manufacturerEmployeeService;

    @Autowired
    private ComponentTypeService conService;

    @Autowired
    private MtrVehicleTypeService mtrVehicleTypeService;

    @GetMapping(value = "/list")
    @SystemLog(optDesc = "分页查询制造商列表（支持姓名、地址模糊查询)", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "分页查询制造商列表", notes = "按姓名、地址模糊查询。不传分页信息，默认不分页。")
    public Object listManufacturer(ManufacturerDTO manufacturerDTO) {
        return manufacturerService.listManufacturer(manufacturerDTO);
    }

    @GetMapping(value = "/list/{manufacturerId}")
    @SystemLog(optDesc = "根据制造商查询下属雇员", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据制造商查询下属雇员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "manufacturerId", value = "制造商id", required = true, paramType = "path", dataType = "Long")
    })
    public Object listManufacturerEmployeeByManufacturerId(@LogParam(description = "制造商id") @PathVariable String manufacturerId) {
        return manufacturerEmployeeService.listManufacturerEmployeeByManufacturerId(manufacturerId);
    }

    @SystemLog(optDesc = "增加部件关联人员(单条)", optType = SystemLogEnum.INSERT)
    @PostMapping(value = "/insertMtrComponentTypeContacts")
    @ApiOperation(value = "增加部件关联人员", notes = "部件型号ID：componentTypeId,人员ID：manufacturerEmployeeId")
    public Object insertMtrComponentTypeContacts(@RequestParam String manufacturerEmployeeId, @RequestParam String componentTypeId) {
        return conService.insertMtrComponentTypeContacts(manufacturerEmployeeId, componentTypeId);
    }

    @SystemLog(optDesc = "删除部件关联人员", optType = SystemLogEnum.DELETE)
    @DeleteMapping(value = "/deleteMtrComponentTypeContacts")
    @ApiOperation(value = "删除部件关联人员", notes = "部件型号ID：componentTypeId,人员ID：manufacturerEmployeeId")
    public Object deleteMtrComponentTypeContacts(@RequestParam String manufacturerEmployeeId, @RequestParam String componentTypeId) {
        return conService.deleteMtrComponentTypeContacts(manufacturerEmployeeId, componentTypeId);
    }

    @SystemLog(optDesc = "查询部件关联人员", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/getEmployees")
    @ApiOperation(value = "查询部件关联人员", notes = "部件型号ID：id，供应商ID：manufacturerId")
    public Object getEmployees(@RequestBody ComponentTypeDTO comDTO) {
        return conService.getEmployees(comDTO);
    }


    @PutMapping(value = "/updateManufacturerEmployee")
    @SystemLog(optDesc = "更新雇员信息", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "更新雇员信息", notes = "雇员id不可为空。其余项目可选，不传不改。")
    public Object updateManufacturerEmployee(@UpdateValidated @RequestBody ManufacturerEmployeePO manufacturerEmployeePO) {
        manufacturerEmployeeService.updateManufacturerEmployee(manufacturerEmployeePO);
        return null;
    }

    @PostMapping(value = "/insertMtrVehicleTypeContacts")
    @SystemLog(optDesc = "添加车型联系人信息", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "添加车型联系人信息", notes = "制造商雇员ID：manufacturerEmployeeId,车辆型号ID：vehicleTypeId")
    public Object insertMtrVehicleTypeContacts(@RequestParam String manufacturerEmployeeId, @RequestParam String vehicleTypeId) {
        return mtrVehicleTypeService.insertMtrVehicleTypeContacts(manufacturerEmployeeId, vehicleTypeId);

    }

    @SystemLog(optDesc = "删除车型联系人信息", optType = SystemLogEnum.DELETE)
    @DeleteMapping(value = "/deleteMtrVehicleTypeContacts")
    @ApiOperation(value = "删除车型联系人信息", notes = "制造商雇员ID：manufacturerEmployeeId,车辆型号ID：vehicleTypeId")
    public Object deleteMtrVehicleTypeContacts(@RequestParam String manufacturerEmployeeId, @RequestParam String vehicleTypeId) {
        return mtrVehicleTypeService.deleteMtrVehicleTypeContacts(manufacturerEmployeeId, vehicleTypeId);
    }

    @SystemLog(optDesc = "查询车型制造商雇员", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/selectEmployees")
    @ApiOperation(value = "查询车型制造商雇员", notes = "车辆型号ID：vehicleTypeId")
    public Object selectEmployees(@RequestParam String vehicleTypeId) {
        return mtrVehicleTypeService.selectEmployees(vehicleTypeId);
    }

}
