package cc.crrc.manage.controller.mtr.vehicleType;

import cc.crrc.manage.common.annotation.InsertValidated;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.component.DesignParameterDTO;
import cc.crrc.manage.service.mtr.MtrDesignParameterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "车辆型号-设计参数")
@RestController
@RequestMapping("/vehicleType_design")
public class MtrVehicleTypeDesignParamController {
	
	@Autowired
	private MtrDesignParameterService mtrDesignParameterService;
	
	@SystemLog(optDesc="查询部件设计参数", optType = SystemLogEnum.SELECT)
	@GetMapping(value = "/getDesignParam")
	@ApiOperation(value = "查询部件设计参数", notes = "部件类型ID:comTypeId.注：车型使用时将部件ID更换成车型ID：vehicleTypeId")
	public Object getDesignParam(@RequestParam(required = false)String comTypeId, @RequestParam(required = false)String vehicleTypeId) {
		return mtrDesignParameterService.getDesignParam(comTypeId,vehicleTypeId);
	}

	@SystemLog(optDesc="增加部件设计参数", optType = SystemLogEnum.INSERT)
	@PostMapping(value = "/insertDesignParam")
	@ApiOperation(value = "增加部件设计参数", notes = "部件类型ID:comTypeId,名称:itemName,值:itemValue.注：车型使用时将部件类型ID更换成车型ID：vehicleTypeId")
	public Object insertDesignParam(@InsertValidated@RequestBody DesignParameterDTO designParam) {
		return mtrDesignParameterService.insertDesignParam(designParam);
	}
	
	@SystemLog(optDesc="修改部件设计参数", optType = SystemLogEnum.UPDATE)
	@PutMapping(value = "/updateDesignParam")
	@ApiOperation(value = "修改部件设计参数", notes = "参数ID:id,部件类型ID:comTypeId,名称:itemName,值:itemValue.注：车型使用时将部件类型ID更换成车型ID：vehicleTypeId")
	public Object updateDesignParam(@UpdateValidated@RequestBody DesignParameterDTO designParam) {
		return mtrDesignParameterService.updateDesignParam(designParam);
	}
	
	@SystemLog(optDesc="删除部件设计参数", optType = SystemLogEnum.DELETE)
	@DeleteMapping(value = "/deleteDesignParam")
	@ApiOperation(value = "删除部件设计参数", notes = "参数ID:id")
	public Object deleteDesignParam(@RequestParam String id) {
		return mtrDesignParameterService.deleteDesignParam(id);
	}
}
