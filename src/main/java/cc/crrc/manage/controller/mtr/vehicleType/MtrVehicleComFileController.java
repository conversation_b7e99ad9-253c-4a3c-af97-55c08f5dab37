package cc.crrc.manage.controller.mtr.vehicleType;

import cc.crrc.manage.common.annotation.InsertValidated;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.service.component.ComponentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "车辆管理-部件文件")
@RestController
@RequestMapping("/vehicle_ComFile")
public class MtrVehicleComFileController {
	@Autowired
    private ComponentService comService;

    @SystemLog(optDesc = "增加相关文件", optType = SystemLogEnum.INSERT)
    @PostMapping(value = "/insertFile")
    @ApiOperation(value = "增加相关文件", notes = "部件ID:id,文件相关信息:sysFile")
    public Object insertFile(@InsertValidated @RequestBody SysFilePO sysFile, @RequestParam String id) {
        return comService.insertFile(sysFile, id);
    }

    @SystemLog(optDesc = "删除相关文件", optType = SystemLogEnum.DELETE)
    @DeleteMapping(value = "/delFile")
    @ApiOperation(value = "删除相关文件", notes = "文件ID：fileId , 部件ID:id")
    public Object delFile(@RequestParam String fileId, @RequestParam String id) {
        return comService.delFile(fileId);
    }

    @SystemLog(optDesc = "查询相关文件", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/selectlFile")
    @ApiOperation(value = "查询相关文件", notes = "部件ID：id")
    public Object selectlFile(@RequestParam String id) {
        return comService.selectlFile(id);
    }
}
