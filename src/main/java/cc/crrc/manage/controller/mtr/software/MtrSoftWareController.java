package cc.crrc.manage.controller.mtr.software;


import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.mtr.MtrSoftWareDTO;
import cc.crrc.manage.pojo.mtr.MtrSoftWarePO;
import cc.crrc.manage.pojo.mtr.MtrSoftwareVersionVO;
import cc.crrc.manage.service.mtr.software.MtrSoftWareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 制造商管理
 *
 * <AUTHOR>
 * 2021/09/17
 **/
@Api(tags = "车载软件管理-新版")
@RestController
@RequestMapping(value = "/mtrSoftWare")
public class MtrSoftWareController {

    @Autowired
    private MtrSoftWareService mtrSoftWareService;


    /**
     * 软件履历列表
     *
     * @param mtrSoftWareDTO
     * @return
     */
    @PostMapping(value = "/list")
//    @SystemLog(optDesc="软件履历列表分页查询（线路、车型、软件名称、版本、更新人模糊查询)", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "软件履历列表分页查询", notes = "按车型、部件型号、软件名称、版本、更新人模糊查询。不传分页信息，默认不分页。")
    public Object VehicleSoftwareList(MtrSoftWareDTO mtrSoftWareDTO) {
        return mtrSoftWareService.softWareInfoList(mtrSoftWareDTO);
    }

    /**
     * @return java.lang.Object
     * <AUTHOR> kangjian
     * @Description 更新软件履历更新人信息
     * @Date 2021/11/15
     * @Param [updater]
     **/
    @PutMapping(value = "/updateUpdater")
    @SystemLog(optDesc = "更新软件履历更新人信息", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "更新软件履历更新人信息", notes = "更新软件履历更新人信息")
    public Object updateUpdaterInfo(@RequestParam String id, @RequestParam String updater) {
        return mtrSoftWareService.updateUpdaterInfo(id, updater);
    }

    /**
     * @return java.lang.Object
     * <AUTHOR>
     * @Description 查找车辆的软件版本号
     * @Date 2022/04/25
     * @Param [get]
     **/
    @GetMapping(value = "/listAllSoftwareVersion")
//    @SystemLog(optDesc = "查找所有车辆的软件版本信息", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查找所有车辆的软件版本信息", notes = "查找所有车辆的软件版本信息")
    public Object ListAllSoftwareVersion() {
        return mtrSoftWareService.ListAllSoftwareVersion();
    }

    /**
     * @return java.lang.Object
     * <AUTHOR>
     * @Description 查找车辆的不同车厢的版本号
     * @Date 2022/04/25
     * @Param [get]
     **/
    @GetMapping(value = "/listVehicleLocationVersion")
//    @SystemLog(optDesc = "查找车辆的不同车厢的版本号", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查找车辆的不同车厢的版本号", notes = "查找车辆的不同车厢的版本号")
    public Object listVehicleLocationVersion(@RequestParam String vehicleCode, @RequestParam String softwareName, @RequestParam String subsystem) {
        List<MtrSoftwareVersionVO> list = mtrSoftWareService.ListSoftwareVersionByLocation(vehicleCode, subsystem, softwareName);
        return list;
    }

    /**
     * @return java.lang.Object
     * <AUTHOR>
     * @Description 查找车辆的历史版本号
     * @Date 2022/04/25
     * @Param [get]
     **/
    @GetMapping(value = "/listVehicleHistoryVersion")
//    @SystemLog(optDesc = "查找车辆的历史版本号", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查找车辆的历史版本号", notes = "查找车辆的历史版本号")
    public Object listVehicleHistoryVersion(@RequestParam String vehicleCode, @RequestParam String subsystem, @RequestParam String softwareName, @RequestParam String location) {
        List<MtrSoftwareVersionVO> list = mtrSoftWareService.listVehicleHistoryVersion(vehicleCode, subsystem, softwareName, location);
        return list;
    }

    /**
     * @return java.lang.Object
     * <AUTHOR>
     * @Description 导出所有软件版本
     * @Date 2022/04/25
     * @Param [get]
     **/
    @GetMapping(value = "/exportVehicleVersion")
//    @SystemLog(optDesc = "导出所有软件版本", optType = SystemLogEnum.DOWNLOAD)
    @ApiOperation(value = "导出所有软件版本", notes = "导出所有软件版本")
    public void exportVehicleVersion(HttpServletRequest request, HttpServletResponse response) {
        mtrSoftWareService.exportVehicleVersion(request, response);
    }

    @GetMapping(value = "/listManualSoftware")
//    @SystemLog(optDesc = "查找所有可手动更新车辆的软件版本信息", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查找所有可手动更新车辆的软件版本信息", notes = "查找所有可手动更新车辆的软件版本信息")
    public Object listManualSoftware() {
        return mtrSoftWareService.listManualSoftware();
    }

    @PostMapping(value = "/manualSoftware")
    @SystemLog(optDesc = "手动更新软件版本或忽略更新", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "手动更新软件版本或忽略更新", notes = "status = 1更新，status = 2忽略")
    public Object manualSoftware(@RequestBody List<MtrSoftWarePO> list, @RequestParam int status) {
        mtrSoftWareService.updateSoftWareInfo(true, status, list.toArray(new MtrSoftWarePO[list.size()]));
        return null;
    }
}
