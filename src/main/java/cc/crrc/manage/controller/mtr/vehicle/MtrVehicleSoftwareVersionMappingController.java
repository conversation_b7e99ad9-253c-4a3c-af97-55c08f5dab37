package cc.crrc.manage.controller.mtr.vehicle;


import cc.crrc.manage.common.annotation.InsertValidated;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.mtr.MtrSoftwareVersionMappingVO;
import cc.crrc.manage.service.mtr.VehicleSoftwareVersionMappingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "车辆型号-软件版本映射")
@RestController
@RequestMapping("/vehicleType_SoftwareVersionMapping")
public class MtrVehicleSoftwareVersionMappingController {

    @Autowired
    private VehicleSoftwareVersionMappingService service;

    /**
     * 新增软件版本信息
     *
     * @param vehicleSoftwarePO
     * @return
     */
    @PostMapping(value = "/add")
    @SystemLog(optDesc = "添加软件版本信息", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "添加软件版本信息")
    public Object addSoftwareVersion(@InsertValidated @RequestBody MtrSoftwareVersionMappingVO vehicleSoftwarePO) {
        return service.addSoftwareVersion(vehicleSoftwarePO);
    }

    /**
     * 删除软件版本信息
     *
     * @param id 主键id
     * @return
     */
    @GetMapping(value = "/delete")
    @SystemLog(optDesc = "删除软件版本信息", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "删除软件版本信息")
    public Object deleteSoftwareVersion(@RequestParam String id) {
        return service.deleteSoftwareVersionById(id);
    }

    /**
     * 更新版本信息，如果字段为null不更新，主键id必填
     *
     * @param vehicleSoftwarePO
     * @return
     */
    @PostMapping(value = "/update")
    @SystemLog(optDesc = "更新软件版本信息", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "更新软件版本信息")
    public Object updateSoftwareVersion(@InsertValidated @RequestBody MtrSoftwareVersionMappingVO vehicleSoftwarePO) {
        return service.updateSoftwareVersion(vehicleSoftwarePO);
    }

    /**
     * 通过id查询版本信息
     *
     * @return
     */
    @GetMapping(value = "/selectById")
    @SystemLog(optDesc = "通过id查询软件版本信息", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "通过id查询软件版本信息")
    public Object selectSoftwareVersionById(@RequestParam String id) {
        return service.selectById(id);
    }

    /**
     * 多条件查询版本信息
     *
     * @return
     */
    @PostMapping(value = "/selectByCondition")
    @SystemLog(optDesc = "通过StructureCode查询软件版本信息", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "通过StructureCode查询软件版本信息")
    public Object selectSoftwareVersionByStructureCode(@InsertValidated @RequestBody MtrSoftwareVersionMappingVO vehicleSoftwarePO) {
        return service.selectByMultiCondition(vehicleSoftwarePO);
    }

    /**
     * 通过车型和信号名查询前20个信号
     */
    @GetMapping(value = "/dropDownSelect")
    @SystemLog(optDesc = "查询下拉列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询下拉列表")
    public Object dropDownSelect(@RequestParam String vehicleType, String inputName) {
        return service.dropDownSelect(vehicleType, inputName);
    }

}
