package cc.crrc.manage.controller.mtr;

import cc.crrc.manage.common.annotation.InsertValidated;

import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.mtr.WheelMeasurementPO;
import cc.crrc.manage.service.mtr.WheelMeasurementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @ClassName WheelMeasurementController
 * <AUTHOR>
 * @Date 2022/3/15 10:49
 * @Version 1.0
 */
@Api(tags = "专项记录-轮对测量")
@RestController
@RequestMapping(value = "/mtrWheelMeasurement")
public class WheelMeasurementController {

    @Resource
    WheelMeasurementService wheelMeasurementService;

    @SystemLog(optDesc = "查询记录列表", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/list")
    @ApiOperation(value = "查询记录列表", notes = "多条件筛选")
    public Object getMeasurement(@RequestBody WheelMeasurementPO wheelMeasurementPO) {
        return wheelMeasurementService.getVehicle(wheelMeasurementPO);
    }

    @SystemLog(optDesc = "查询测量数据", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/data/{measurementVehicleId}")
    @ApiOperation(value = "查询测量数据", notes = "根据测量车辆表id查询")
    public Object getMeasurementData(@PathVariable String measurementVehicleId) {
        return wheelMeasurementService.getMeasurementData(measurementVehicleId);
    }


    @SystemLog(optDesc = "新增测量记录", optType = SystemLogEnum.INSERT)
    @PostMapping(value = "/record")
    @ApiOperation(value = "新增测量记录")
    public Object saveMeasurementRecord(@InsertValidated @RequestBody WheelMeasurementPO wheelMeasurementPO) {
        return wheelMeasurementService.saveWheelData(wheelMeasurementPO);
    }

    @SystemLog(optDesc = "修改测量记录", optType = SystemLogEnum.UPDATE)
    @PutMapping(value = "/record")
    @ApiOperation(value = "修改测量记录")
    public Object changeMeasurementRecord(@UpdateValidated @RequestBody WheelMeasurementPO wheelMeasurementPO) {
        return wheelMeasurementService.changeWheelData(wheelMeasurementPO);
    }

    @SystemLog(optDesc = "删除测量记录", optType = SystemLogEnum.DELETE)
    @DeleteMapping(value = "/record/{id}")
    @ApiOperation(value = "删除测量记录")
    public Object deleteMeasurementRecord(@PathVariable(value = "id") String id) {
        return wheelMeasurementService.deleteWheelData(id);
    }

}
