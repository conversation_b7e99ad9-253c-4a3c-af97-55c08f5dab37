package cc.crrc.manage.controller.mtr.vehicle;

/*    @ApiImplicitParams：用在请求的方法上，表示一组参数说明
    @ApiImplicitParam：用在@ApiImplicitParams注解中，指定一个请求参数的各个方面
    name：参数名
    value：参数的汉字说明、解释
    required：参数是否必须传
    paramType：参数放在哪个地方
            · header --> 请求参数的获取：@RequestHeader
            · query --> 请求参数的获取：@RequestParam
            · path（用于restful接口）--> 请求参数的获取：@PathVariable
            · body（不常用）
            · form（不常用）
    dataType：参数类型，默认String，其它值dataType="Integer"
    defaultValue：参数的默认值*/

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.mtr.VehicleSoftwareVersionDTO;
import cc.crrc.manage.service.mtr.VehicleSoftwareVersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * 车载软件版本查询
 * 2019/11/18
 **/
@RestController
@RequestMapping(value = "/VehicleSoftwareVersion")
@Api(tags = "车载软件版本查询")
public class VehicleSoftwareVersionController {

    @Autowired
    private VehicleSoftwareVersionService vehicleSoftwareVersionService;

    @PostMapping(value = "/list")
    @SystemLog(optDesc="车载软件版本履历（车型、部件型号、软件名称、版本、制造商、更新人模糊查询)", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "车载软件版本履历")
    public Object VehicleSoftwareVersionList(VehicleSoftwareVersionDTO vehicleSoftwareVersionDTO) {
        return vehicleSoftwareVersionService.VehicleSoftwareVersionList(vehicleSoftwareVersionDTO);
    }

    @GetMapping(value = "/getVersionResume")
    @SystemLog(optDesc="查询车载软件版本履历（软件名称、版本)", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "查询车载软件版本履历")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType="query", name = "softwareId", value = "软件版本ID", required = true, dataType = "String"),
            @ApiImplicitParam(paramType="query", name = "componentId", value = "部件ID", required = true, dataType = "String")
    })
    public Object getVersionResume( @RequestParam String softwareId,@RequestParam String componentId) {
        return vehicleSoftwareVersionService.getVersionResume(softwareId,componentId);
    }


}
