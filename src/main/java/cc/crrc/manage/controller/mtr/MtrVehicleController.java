package cc.crrc.manage.controller.mtr;


import cc.crrc.manage.cache.RefreshCache;
import cc.crrc.manage.common.annotation.*;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.component.ManufacturerParameterDTO;
import cc.crrc.manage.pojo.component.OperatingParameterDTO;
import cc.crrc.manage.pojo.mtr.MtrVehicleDTO;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import cc.crrc.manage.service.mtr.MtrManufacturerParameterService;
import cc.crrc.manage.service.mtr.MtrOperatingParameterService;
import cc.crrc.manage.service.mtr.MtrVehicleService;
import cc.crrc.manage.service.mtr.StruVehicleStructureComponentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 车辆管理
 *
 * <AUTHOR> GuoYang
 * 2019/12/3
 **/
@Api(tags = "车辆管理")
@RestController
@RequestMapping("/vehicle")
@Validated
public class MtrVehicleController {
    @Autowired
    private MtrVehicleService mtrVehicleService;
    @Autowired
    private MtrManufacturerParameterService mtrManufacturerParameterService;
    @Autowired
    private MtrOperatingParameterService mtrOperatingParameterService;
    @Autowired
    private StruVehicleStructureComponentService struVehicleStructureComponentService;

    @GetMapping(value = "/list/info")
    @SystemLog(optDesc = "分页查询车辆信息列表（支持姓名模糊查询，车辆编码、车辆类型精准查询。)", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "分页查询车辆信息列表", notes = "按姓名模糊查询；按车辆编码、车辆类型精准查询。不传分页信息，默认不分页。")
    public Object listVehicleInfo(MtrVehicleDTO mtrVehicleDTO) {
        return mtrVehicleService.listVehicleInfo(mtrVehicleDTO);
    }

    @GetMapping(value = "/vehicle/list")
    @SystemLog(optDesc = "根据线路id和车型id(可不添)查询车辆list", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据线路id和车型id(可不添)查询车辆list")

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "metroLineId", value = "metroLineId", required = true,  dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "vehicleTypeId", value = "vehicleTypeId", dataType = "String")
    })
    public Object listVehicleByVehicleTypeId(@RequestParam String metroLineId,String vehicleTypeId) {
        return mtrVehicleService.listVehicleByLineIdAndVehicleTypeId(metroLineId,vehicleTypeId);
    }
    @GetMapping(value = "/list")
    @SystemLog(optDesc = "查询车辆列表（支持线路id查询)", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询车辆列表", notes = "按线路id查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "metroLineId", value = "线路id", required = true, dataType = "String")
    })
    public Object listVehicle(@LogParam(description = "线路id") @RequestParam String metroLineId) {
        return mtrVehicleService.listVehicle(metroLineId);
    }

    @PostMapping(value = "/")
    @SystemLog(optDesc = "添加车辆信息", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "添加车辆信息", notes = "车辆名称不可为空。无需传递id。其余项目可选。")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleTypeId", value = "车辆类型id", required = true, dataType = "String")
    })
    @RefreshCache(values = "VEHICLE")
    public Object addVehicle(@InsertValidated @RequestBody MtrVehiclePO mtrVehiclePO,@LogParam(description = "车辆类型id") @RequestParam String vehicleTypeId) {
        mtrVehicleService.addVehicle(mtrVehiclePO,vehicleTypeId);
        return null;
    }

    @DeleteMapping(value = "/")
    @SystemLog(optDesc = "根据id批量删除车辆信息", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "根据id批量删除车辆信息", notes = "根据id批量删除车辆信息,支持批量删除,id用\",\"分割")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idList", value = "车辆id", required = true, dataType = "String")
    })
    @RefreshCache(values = "VEHICLE")
    public Object batchDeleteVehicle(@LogParam(description = "车辆id") @RequestParam String idList) {
        mtrVehicleService.batchDeleteVehicle(idList);
        return null;
    }

    @PutMapping(value = "/")
    @SystemLog(optDesc = "更新车辆信息", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "更新车辆信息", notes = "id不可为空。其余项目可选，不传不改。")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleTypeId", value = "车辆类型id", required = true, dataType = "String")
    })
    @RefreshCache(values = "VEHICLE")
    public Object updateVehicle(@UpdateValidated @RequestBody MtrVehiclePO mtrVehiclePO,@LogParam(description = "车辆类型id") @RequestParam String vehicleTypeId) {
        mtrVehicleService.updateVehicle(mtrVehiclePO,vehicleTypeId);
        return null;
    }

    @PostMapping(value = "/file")
    @SystemLog(optDesc = "上传文件", optType = SystemLogEnum.UPLOAD)
    @ApiOperation(value = "上传文件", notes = "车辆id、文件名、文件地址、文件分组、文件远程地址不可为空。其余项目可选。")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",  value = "车辆id",required = true, dataType = "String")
    })
    public Object saveManufacturerFile(@InsertValidated @RequestBody SysFilePO sysFile,@LogParam(description = "车辆类型id") @RequestParam String id) {
        mtrVehicleService.saveVehicleFile(sysFile,id);
        return null;
    }

    @DeleteMapping(value = "/file")
    @SystemLog(optDesc = "删除文件", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "删除文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileId", value = "文件id", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "id", value = "车辆id", required = true, paramType = "query", dataType = "String")
    })
    public Object deleteManufacturerFile(@LogParam(description = "文件id数组") @RequestParam String fileId,
                                         @LogParam(description = "车辆id") @RequestParam String id) {
        mtrVehicleService.deleteVehicleFile(fileId, id);
        return null;
    }

    @GetMapping(value = "/file")
    @SystemLog(optDesc = "查看车辆下的所有文件", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查看车辆下的所有文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "车辆id", required = true, dataType = "String")
    })
    public Object getFilesByManufacturerId(@LogParam(description = "车辆id") @RequestParam String id) {
        return mtrVehicleService.getFilesByVehicleId(id);
    }

    @SystemLog(optDesc="查询车辆制造参数", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/getManufParam")
    @ApiOperation(value = "查询车辆制造参数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleId", value = "车辆id", required = true, dataType = "String")
    })
    public Object getManufParam(@LogParam(description = "车辆id") @RequestParam String vehicleId) {
        return mtrManufacturerParameterService.getManufParam(null,vehicleId);
    }

    @SystemLog(optDesc="增加车辆制造参数", optType = SystemLogEnum.INSERT)
    @PostMapping(value = "/insertManufParam")
    @ApiOperation(value = "增加车辆制造参数", notes = "部件ID:componentId,名称:itemName,值:itemValue.注：车型使用时将部件ID更换成车辆ID：vehicleId")
    public Object insertManufParam(@InsertValidated @RequestBody ManufacturerParameterDTO manufParam) {
        return mtrManufacturerParameterService.insertManufParam(manufParam);
    }

    @SystemLog(optDesc="修改车辆制造参数", optType = SystemLogEnum.UPDATE)
    @PutMapping(value = "/updateManufParam")
    @ApiOperation(value = "修改车辆制造参数", notes = "参数ID:id,部件ID:componentId,名称:itemName,值:itemValue.注：车型使用时将部件ID更换成车辆ID：vehicleId")
    public Object updateManufParam(@UpdateValidated @RequestBody ManufacturerParameterDTO manufParam) {
        return mtrManufacturerParameterService.updateManufParam(manufParam);
    }

    @SystemLog(optDesc="删除车辆制造参数", optType = SystemLogEnum.DELETE)
    @DeleteMapping(value = "/deleteManufParam")
    @ApiOperation(value = "删除车辆制造参数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "车辆id", required = true, dataType = "String")
    })
    public Object deleteManufParam(@LogParam(description = "车辆id") @RequestParam String id) {
        return mtrManufacturerParameterService.deleteManufParam(id);
    }

    @SystemLog(optDesc="查询车辆运行参数", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/getOperatingParameter")
    @ApiOperation(value = "查询车辆运行参数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleId", value = "车辆id", required = true, dataType = "String")
    })
    public Object getOperatingParameter(@LogParam(description = "车辆id") @RequestParam String vehicleId) {
        return mtrOperatingParameterService.getOperatingParam(null,vehicleId);
    }

    @SystemLog(optDesc="增加车辆运行参数", optType = SystemLogEnum.INSERT)
    @PostMapping(value = "/insertOperatingParameter")
    @ApiOperation(value = "增加车辆运行参数", notes = "部件ID:componentId,名称:itemName,值:itemValue.注：车型使用时将部件ID更换成车辆ID：vehicleId")
    public Object insertOperatingParameter(@InsertValidated @RequestBody OperatingParameterDTO operatingParam) {
        return mtrOperatingParameterService.insertOperatingParam(operatingParam);
    }

    @SystemLog(optDesc="修改车辆运行参数", optType = SystemLogEnum.UPDATE)
    @PutMapping(value = "/updateOperatingParameter")
    @ApiOperation(value = "修改车辆运行参数", notes = "参数ID:id,部件ID:componentId,名称:itemName,值:itemValue.注：车型使用时将部件ID更换成车辆ID：vehicleId")
    public Object updateOperatingParameter(@UpdateValidated @RequestBody OperatingParameterDTO operatingParam) {
        return mtrOperatingParameterService.updateOperatingParam(operatingParam);
    }

    @SystemLog(optDesc="删除车辆运行参数", optType = SystemLogEnum.DELETE)
    @DeleteMapping(value = "/deleteOperatingParameter")
    @ApiOperation(value = "删除车辆运行参数")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "车辆id", required = true, dataType = "String")
    })
    public Object deleteOperatingParameter(@LogParam(description = "车辆id") @RequestParam String id) {
        return mtrOperatingParameterService.deleteOperatingParam(id);
    }

    @SystemLog(optDesc = "查询子系统字典列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据车型id取得子系统字典列表")
    @GetMapping("/subsystemDict/list/vehicleId")
    @ApiImplicitParam(paramType = "query", name = "vehicleId", value = "车辆id", required = true, dataType = "String")
    public Object listSubsystemDictByVehicleId(@RequestParam String vehicleId) {
        return struVehicleStructureComponentService.listSubsystemDictByVehicleId(vehicleId);
    }

    @GetMapping(value = "/getLocationByVehicleCode")
    @SystemLog(optDesc = "根据车辆code查询车辆对应的车厢", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据车辆code查询车辆对应的车厢", notes = "按顺序排列。")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleCode", value = "车辆编码", required = true, dataType = "String")
    })
    public Object getLocationByVehicleCode(@LogParam(description = "车辆编码") @RequestParam String vehicleCode) {
        return struVehicleStructureComponentService.getLocationByVehicleCode(vehicleCode);
    }
    //诺丽轮对、受电弓检测和告警页面使用
    @GetMapping(value = "/vehicle/listNuoLi")
    @SystemLog(optDesc = "根据线路id和车型id(可不添)查询车辆list-nuoli", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据线路id和车型id(可不添)查询车辆list-nuoli")

    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "metroLineId", value = "metroLineId", required = true,  dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "vehicleTypeId", value = "vehicleTypeId", dataType = "String")
    })
    public Object listVehicleByVehicleTypeIdNuoLi(@RequestParam String metroLineId,String vehicleTypeId) {
        return mtrVehicleService.listVehicleByLineIdAndVehicleTypeIdNuoLi(metroLineId,vehicleTypeId);
    }
}
