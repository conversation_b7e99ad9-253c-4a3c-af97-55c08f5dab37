package cc.crrc.manage.controller.mtr.vehicleType;

import cc.crrc.manage.common.annotation.InsertValidated;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.component.RamsParameterDTO;
import cc.crrc.manage.service.mtr.MtrRAMSParameterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "车辆型号-RAMS参数")
@RestController
@RequestMapping("/vehicleType_rams")
public class MtrVehicleTypeRamsParamController {
	@Autowired
	private MtrRAMSParameterService mtrRAMSParameterService;
	
	@SystemLog(optDesc="查询部件RAMS参数", optType = SystemLogEnum.SELECT)
	@GetMapping(value = "/getRamsParam")
	@ApiOperation(value = "查询部件RAMS参数", notes = "部件类型ID:comTypeId.注：车型使用时将部件ID更换成车型ID：vehicleTypeId")
	public Object getRamsParam(@RequestParam(required = false) String comTypeId, @RequestParam(required = false)String vehicleTypeId) {
		return mtrRAMSParameterService.getRamsParam(comTypeId,vehicleTypeId);
	}
	
	@SystemLog(optDesc="增加部件RAMS参数", optType = SystemLogEnum.INSERT)
	@PostMapping(value = "/insertRamsParam")
	@ApiOperation(value = "增加部件RAMS参数", notes = "部件类型ID:comTypeId,名称:itemName,值:itemValue,单位：unit.注：车型使用时将部件类型ID更换成车型ID：vehicleTypeId")
	public Object insertRamsParam(@InsertValidated@RequestBody RamsParameterDTO ramsParam) {
		return mtrRAMSParameterService.insertRamsParam(ramsParam);
	}
	
	@SystemLog(optDesc="修改部件RAMS参数", optType = SystemLogEnum.UPDATE)
	@PutMapping(value = "/updateRamsParam")
	@ApiOperation(value = "修改部件RAMS参数", notes = "参数ID:id,部件类型ID:comTypeId,名称:itemName,值:itemValue，单位：unit.注：车型使用时将部件类型ID更换成车型ID：vehicleTypeId")
	public Object updateRamsParam(@UpdateValidated@RequestBody RamsParameterDTO ramsParam) {
		return mtrRAMSParameterService.updateRamsParam(ramsParam);
	}
	
	@SystemLog(optDesc="删除部件RAMS参数", optType = SystemLogEnum.DELETE)
	@DeleteMapping(value = "/deleteRamsParam")
	@ApiOperation(value = "删除部件RAMS参数", notes = "参数ID:id")
	public Object deleteRamsParam(@RequestParam String id) {
		return mtrRAMSParameterService.deleteRamsParam(id);
	}
}
