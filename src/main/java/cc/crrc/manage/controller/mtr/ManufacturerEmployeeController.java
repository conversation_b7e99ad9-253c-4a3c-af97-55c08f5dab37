package cc.crrc.manage.controller.mtr;

import cc.crrc.manage.common.annotation.*;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.mtr.ManufacturerEmployeePO;
import cc.crrc.manage.service.mtr.ManufacturerEmployeeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 制造商雇员管理
 *
 * <AUTHOR>
 * 2019/11/11
 **/
@Api(tags = "制造商雇员管理")
@RestController
@RequestMapping(value = "/manufacturer/employee")
public class ManufacturerEmployeeController {
    @Autowired
    private ManufacturerEmployeeService manufacturerEmployeeService;

    @GetMapping(value = "/list")
    @SystemLog(optDesc = "查询雇员列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询雇员列表", notes = "查询分组方式为1时，按雇员姓名首字母分组;其余数值，按雇员所属公司名分组。搜索关键字可不填，默认无筛选条件。")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "groupType", value = "查询分组方式", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "searchKey", value = "搜索关键字", paramType = "query", dataType = "String")
    })
    public Object listManufacturerEmployee(@LogParam(description = "查询分组方式") @RequestParam(defaultValue="1",required = false) String groupType,
                                           @LogParam(description = "搜索关键字") String searchKey) {
        return manufacturerEmployeeService.listManufacturerEmployee(groupType, searchKey);
    }

    @GetMapping(value = "/{id}")
    @SystemLog(optDesc = "根据id查询雇员", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据id查询雇员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "雇员id", required = true, paramType = "path", dataType = "String")
    })
    public Object getManufacturerEmployeeById(@LogParam(description = "雇员id") @PathVariable String id) {
        return manufacturerEmployeeService.getManufacturerEmployeeById(id);
    }

    @GetMapping(value = "/list/{manufacturerId}")
    @SystemLog(optDesc = "根据制造商查询下属雇员", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据制造商查询下属雇员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "manufacturerId", value = "制造商id", required = true, paramType = "path", dataType = "String")
    })
    public Object listManufacturerEmployeeByManufacturerId(@LogParam(description = "制造商id") @PathVariable String manufacturerId) {
        return manufacturerEmployeeService.listManufacturerEmployeeByManufacturerId(manufacturerId);
    }

    @PostMapping(value = "/")
    @SystemLog(optDesc = "添加雇员信息", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "添加雇员信息", notes = "雇员的姓名、性别、所属制造商id不可为空。其余项目可选。")
    public Object addManufacturerEmployee(@InsertValidated @RequestBody ManufacturerEmployeePO manufacturerEmployeePO) {
        manufacturerEmployeeService.addManufacturerEmployee(manufacturerEmployeePO);
        return null;
    }

    @DeleteMapping(value = "/{id}")
    @SystemLog(optDesc = "根据id删除雇员", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "根据id删除雇员")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "雇员id", required = true, paramType = "path", dataType = "String")
    })
    public Object deleteManufacturerEmployeeById(@LogParam(description = "雇员id") @PathVariable String id) {
        manufacturerEmployeeService.deleteManufacturerEmployeeById(id);
        return null;
    }

    @PutMapping(value = "/")
    @SystemLog(optDesc = "更新雇员信息", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "更新雇员信息", notes = "雇员id不可为空。其余项目可选，不传不改。")
    public Object updateManufacturerEmployee(@UpdateValidated @RequestBody ManufacturerEmployeePO manufacturerEmployeePO) {
        manufacturerEmployeeService.updateManufacturerEmployee(manufacturerEmployeePO);
        return null;
    }

}
