package cc.crrc.manage.controller.mtr.vehicle;

import cc.crrc.manage.common.annotation.InsertValidated;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.component.OperatingParameterDTO;
import cc.crrc.manage.service.mtr.MtrOperatingParameterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "车辆管理-运行参数")
@RestController
@RequestMapping("/vehicle_Opera")
public class MtrVehicleOperaParamController {
	@Autowired
	private MtrOperatingParameterService mtrOperatingParameterService;
	
	@SystemLog(optDesc="查询部件运行参数", optType = SystemLogEnum.SELECT)
	@GetMapping(value = "/getOperaParam")
	@ApiOperation(value = "查询部件运行参数", notes = "部件ID:componentId.注：车型使用时将部件ID更换成车辆ID：vehicleId")
	public Object getOperaParam(@RequestParam(required = false) String componentId, @RequestParam(required = false)String vehicleId) {
		return mtrOperatingParameterService.getOperatingParam(componentId,vehicleId);
	}

	@SystemLog(optDesc="增加部件运行参数", optType = SystemLogEnum.INSERT)
	@PostMapping(value = "/insertOperaParam")
	@ApiOperation(value = "增加部件运行参数", notes = "部件ID:componentId,名称:itemName,值:itemValue.注：车型使用时将部件ID更换成车辆ID：vehicleId")
	public Object insertOperaParam(@InsertValidated@RequestBody OperatingParameterDTO OperaParam) {
		return mtrOperatingParameterService.insertOperatingParam(OperaParam);
	}
	
	@SystemLog(optDesc="修改部件运行参数", optType = SystemLogEnum.UPDATE)
	@PutMapping(value = "/updateOperaParam")
	@ApiOperation(value = "修改部件运行参数", notes = "参数ID:id,部件ID:componentId,名称:itemName,值:itemValue.注：车型使用时将部件ID更换成车辆ID：vehicleId")
	public Object updateOperaParam(@UpdateValidated@RequestBody OperatingParameterDTO OperaParam) {
		return mtrOperatingParameterService.updateOperatingParam(OperaParam);
	}
	
	@SystemLog(optDesc="删除部件运行参数", optType = SystemLogEnum.DELETE)
	@DeleteMapping(value = "/deleteOperaParam")
	@ApiOperation(value = "删除部件运行参数", notes = "参数ID:id")
	public Object deleteOperaParam(@RequestParam String id) {
		return mtrOperatingParameterService.deleteOperatingParam(id);
	}
}
