package cc.crrc.manage.controller.mtr.vehicle;

import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cc.crrc.manage.common.annotation.InsertValidated;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.mtr.VehicleSoftwareDTO;
import cc.crrc.manage.pojo.mtr.VehicleSoftwareFileDTO;
import cc.crrc.manage.pojo.mtr.VehicleSoftwarePO;
import cc.crrc.manage.service.mtr.VehicleSoftwareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
/*******
 * 
 * <AUTHOR>
 *
 */
@Api(tags = "车辆型号-软件版本")
@RestController
@RequestMapping("/vehicleType_Software")
public class MtrVehicleSoftController {
	
    @Autowired
    private VehicleSoftwareService vehicleSoftwareService;

    /****
     * 查询车型的软件
     * <AUTHOR>
     * @param componentTypeId
     * @param vehicleTypeId
     * @return
     */
    @PostMapping(value = "/list")
    @SystemLog(optDesc="车型软件列表（车型、部件型号)", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "车载软件列表分页查询",notes = "按车型、部件型号")
    public Object VehicleSoftwareList(@RequestParam String componentTypeId, @RequestParam String vehicleTypeId, @RequestParam(required = false) String softwareName) {
        return vehicleSoftwareService.getVehicleSoftwareList(componentTypeId,vehicleTypeId, softwareName);
    }
    
	/**
	  *  查询部件的软件
	 * 
	 * @return
	 * <AUTHOR>
	 */
    @GetMapping(value = "/getSoftware")
    @SystemLog(optDesc="部件软件列表)", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据部件id查询软件",notes = "")
    public Object getSoftware(@RequestParam String componentId) {
        return vehicleSoftwareService.getSoftware(componentId);
    }
    
    /**
     * 新增软件版本
     * @param vehicleSoftwarePO
     * @return
     */
    @PostMapping(value = "/add")
    @SystemLog(optDesc="添加软件版本信息", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "添加软件版本信息",notes = "车型、部件型号、软件名称。其余项目可选。")
    public Object addVehicleSoftware(@InsertValidated @RequestBody VehicleSoftwarePO vehicleSoftwarePO) {
        return vehicleSoftwareService.addVehicleSoftware(vehicleSoftwarePO);
    }
    
    /**
     * 修改软件版本
     * @param vehicleSoftwarePO
     * @return
     */
    @PostMapping(value = "/update")
    @SystemLog(optDesc="修改软件版本信息", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "修改软件版本信息")
    public Object updateVehicleSoftware(@UpdateValidated @RequestBody VehicleSoftwarePO vehicleSoftwarePO) {
        return vehicleSoftwareService.updateVehicleSoftware(vehicleSoftwarePO);
    }
    /**
           * 获取文件列表
     * @param id
     * @return
     */
    @GetMapping(value = "/file/getSoftwareFile")
    @SystemLog(optDesc="获取文件列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "获取文件列表",notes = "车载软件id、文件名、文件地址、文件分组、文件远程地址信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",  value = "软件版本ID",required = true, dataType = "String")
    })
    public Object getVehicleSoftwareFile(@RequestParam String id){
        return vehicleSoftwareService.getVehicleSoftwareFile(id);
    }
    /**
     * 软件版本关联文件上传功能
     * @param sysFile
     * @param id
     * @return
     */
    @PostMapping(value = "/file/uploadSoftwareFile")
    @SystemLog(optDesc="上传文件", optType = SystemLogEnum.UPLOAD)
    @ApiOperation(value = "上传文件",notes = "车载软件id、文件名、文件地址、文件分组、文件远程地址必填项。")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",  value = "软件版本ID",required = true, dataType = "String")
    })
    public Object saveVehicleSoftwareFile(@RequestBody SysFilePO sysFile, @RequestParam String id){
        return vehicleSoftwareService.saveVehicleSoftwareFile(sysFile,id);
    }
    
    /**
     * 删除关联文件
     * @param fileId
     * @return
     */
    @DeleteMapping(value = "/file/deleteSoftwareFile")
    @SystemLog(optDesc="删除列表中文件", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "删除列表中文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileId",  value = "文件ID",required = true, dataType = "String")
    })
    public Object deleteVehicleSoftwareFile(@RequestParam String fileId){
        return vehicleSoftwareService.deleteVehicleSoftwareFile(fileId);
    }
    
    /**
     * 删除软件版本
     * @param softwareId
     * @param componentTypeId
     * @param vehicleTypeId
     * @return
     */
    @SystemLog(optDesc = "删除车辆部件软件版本", optType = SystemLogEnum.DELETE)
    @ApiOperation("删除车辆部件软件版本")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType="query", name = "softwareId", value = "软件版本ID", required = true, dataType = "String"),
            @ApiImplicitParam(paramType="query", name = "componentTypeId", value = "部件型号ID", required = true, dataType = "String"),
            @ApiImplicitParam(paramType="query", name = "vehicleTypeId", value = "车辆型号ID", required = true, dataType = "String")
    })
    @DeleteMapping("/deleteVehicleSoftware")
    public Object deleteVehicleSoftware(@RequestParam String softwareId,@RequestParam String componentTypeId,@RequestParam String vehicleTypeId ) {
        return vehicleSoftwareService.deleteVehicleSoftware(softwareId,componentTypeId,vehicleTypeId);
    }
    
    /**
     * 升级软件版本
     * @param vehicleSoftwarePO
     * @return
     */
    @PostMapping(value = "/upgrading")
    @SystemLog(optDesc="升级软件版本信息", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "升级软件版本信息",notes = "填写软件版本和备注，其他数据上一层传入")
    public Object upgradingVehicleSoftware(@InsertValidated @RequestBody VehicleSoftwarePO vehicleSoftwarePO) {
        return vehicleSoftwareService.upgradingVehicleSoftware(vehicleSoftwarePO);
    }
    /***
     * 部件匹配软件
     * <AUTHOR>
     * @param softwareId
     * @param componentId
     * @param vehicleId
     * @return
     */
    @PostMapping(value = "/configSoftware")
    @SystemLog(optDesc="部件匹配软件", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "部件匹配软件",notes = "")
    public Object configSoftware(@RequestParam String softwareId, @RequestParam String componentId, @RequestParam String vehicleId, @RequestParam String softwareName) {
    	return vehicleSoftwareService.configSoftware(softwareId,componentId,vehicleId,softwareName);
    }
}
