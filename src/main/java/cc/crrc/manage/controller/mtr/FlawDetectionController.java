package cc.crrc.manage.controller.mtr;

import cc.crrc.manage.common.annotation.InsertValidated;

import cc.crrc.manage.common.annotation.UpdateValidated;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.mtr.FlawDetectionPO;
import cc.crrc.manage.service.mtr.FlawDetectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @ClassName FlawDetectionController
 * @Description: 探伤检测记录
 * <AUTHOR>
 * @Date 2022/3/25 9:12
 * @Version 1.0
 */
@Api(tags = "专项记录-探伤检测")
@RestController
@RequestMapping(value = "/mtrFlawDetection")
public class FlawDetectionController {
    @Resource
    FlawDetectionService flawDetectionService;

    @SystemLog(optDesc = "查询探伤记录列表", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/list")
    @ApiOperation(value = "查询探伤记录列表", notes = "多条件筛选")
    public Object queryFlawDetectionList(@RequestBody FlawDetectionPO flawDetectionPO) {
        return flawDetectionService.queryFlawDetectionList(flawDetectionPO);
    }

    @SystemLog(optDesc = "查询探伤记录详情", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/info/{id}")
    @ApiOperation(value = "查询探伤记录详情")
    public Object queryFlawDetectionInfo(@PathVariable String id) {
        return flawDetectionService.queryFlawDetectionInfo(id);
    }

    @SystemLog(optDesc = "添加探伤记录", optType = SystemLogEnum.INSERT)
    @PostMapping(value = "/record")
    @ApiOperation(value = "添加探伤记录")
    public Object saveFlawDetection(@InsertValidated @RequestBody FlawDetectionPO flawDetectionPO) {
        return flawDetectionService.saveFlawDetection(flawDetectionPO);
    }

    @SystemLog(optDesc = "修改探伤记录", optType = SystemLogEnum.UPDATE)
    @PutMapping(value = "/record")
    @ApiOperation(value = "修改探伤记录")
    public Object changeFlawDetection(@UpdateValidated @RequestBody FlawDetectionPO flawDetectionPO) {
        return flawDetectionService.changeFlawDetection(flawDetectionPO);
    }

    @SystemLog(optDesc = "删除探伤记录", optType = SystemLogEnum.DELETE)
    @DeleteMapping(value = "/record/{id}")
    @ApiOperation(value = "删除探伤记录")
    public void deleteFlawDetection(@PathVariable String id) {
        flawDetectionService.deleteFlawDetection(id);
    }




}
