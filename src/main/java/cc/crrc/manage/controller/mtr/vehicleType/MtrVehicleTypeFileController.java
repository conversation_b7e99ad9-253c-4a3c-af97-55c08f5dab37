package cc.crrc.manage.controller.mtr.vehicleType;

import cc.crrc.manage.common.annotation.InsertValidated;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.service.mtr.MtrVehicleTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "车辆型号-文件")
@RestController
@RequestMapping("/vehicleType_File")
public class MtrVehicleTypeFileController {
	
	@Autowired
    private MtrVehicleTypeService vehicleTypeService;
	
    @SystemLog(optDesc = "查询车型文件列表", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查询车型文件列表")
    @GetMapping("/vehicleTypeFile/list/vehicleTypeId")
    @ApiImplicitParam(paramType = "query", name = "id", value = "车型id", required = true, dataType = "String")
    public Object insertVehicleTypeFile(@RequestParam String id) {
        return vehicleTypeService.listVehicleTypeFile(id);
    }
	
    @SystemLog(optDesc = "新增车型文件", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "新增车型文件")
    @PostMapping("/vehicleTypeFile")
    @ApiImplicitParam(paramType = "query", name = "id", value = "车型id", required = true, dataType = "String")
    public Object insertVehicleTypeFile(@RequestBody @InsertValidated SysFilePO sysFile, @RequestParam String id) {
        vehicleTypeService.insertVehicleTypeFile(sysFile, id);
        return null;
    }

    @SystemLog(optDesc = "删除车型文件", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "删除车型文件")
    @DeleteMapping("/vehicleTypeFile")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "query", name = "id", value = "车型id", required = true, dataType = "String"),
            @ApiImplicitParam(paramType = "query", name = "fileId", value = "文件id", required = true, dataType = "String")
    })
    public Object deleteVehicleTypeFileRelation(@RequestParam String id, @RequestParam String fileId) {
        return vehicleTypeService.deleteVehicleTypeFileRelation(id, fileId);
    }
}
