package cc.crrc.manage.controller.mtr.vehicle;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


import cc.crrc.manage.service.component.ComponentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping(value = "/Vehicle_Component")
@Api(tags = "车辆管理-绑定部件")
public class MtrVehicleComponentController {
	@Autowired
    private ComponentService comService;
	
	@SystemLog(optDesc = "查询部件列表", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/getComponents")
    @ApiOperation(value = "查询部件列表", notes = "部件型号ID：componentTypeId（String）")
    public Object getComponents(@RequestParam String componentTypeId) {
        return comService.getComponentsByTypeId(componentTypeId);
    }
	
	@SystemLog(optDesc = "绑定部件", optType = SystemLogEnum.UPDATE)
    @PostMapping(value = "/bindComponent")
    @ApiOperation(value = "绑定部件 ", notes = "部件型号ID：componentTypeId（String），车辆ID：vehicleId（String），部件ID：componentId（String），车辆构型Code：vehicleStructureCode（String）")
	public Object bindComponent(@RequestParam String componentTypeId, @RequestParam String vehicleId, @RequestParam String componentId, String vehicleStructureCode) {
		return comService.bindComponent(componentTypeId, vehicleId, componentId, vehicleStructureCode);
	}

}
