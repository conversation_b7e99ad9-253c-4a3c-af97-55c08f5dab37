package cc.crrc.manage.controller.mtr;

import cc.crrc.manage.common.annotation.*;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.mtr.ManufacturerDTO;
import cc.crrc.manage.pojo.mtr.ManufacturerPO;
import cc.crrc.manage.service.mtr.ManufacturerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * 制造商管理
 *
 * <AUTHOR>
 * 2019/11/11
 **/
@Api(tags = "制造商管理")
@RestController
@RequestMapping(value = "/manufacturer")
public class ManufacturerController {
    @Autowired
    private ManufacturerService manufacturerService;

    @GetMapping(value = "/list")
    @SystemLog(optDesc = "分页查询制造商列表（支持姓名、地址模糊查询)", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "分页查询制造商列表", notes = "按姓名、地址模糊查询。不传分页信息，默认不分页。")
    public Object listManufacturer(ManufacturerDTO manufacturerDTO) {
        return manufacturerService.listManufacturer(manufacturerDTO);
    }

    @GetMapping(value = "/{id}")
    @SystemLog(optDesc = "根据id查询制造商", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据id查询制造商")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "制造商id", required = true, paramType = "path", dataType = "String")
    })
    public Object getManufacturerById(@LogParam(description = "制造商id") @PathVariable String id) {
        return manufacturerService.getManufacturerById(id);
    }

    @PostMapping(value = "/")
    @SystemLog(optDesc = "添加制造商信息", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "添加制造商信息", notes = "制造商名称不可为空。无需传递id。其余项目可选。")
    public Object addManufacturer(@InsertValidated @RequestBody ManufacturerPO manufacturerPO) {
        manufacturerService.addManufacturer(manufacturerPO);
        return null;
    }

    @DeleteMapping(value = "/{idList}")
    @SystemLog(optDesc = "根据id批量删除制造商", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "根据id批量删除制造商", notes = "根据id批量删除制造商,支持批量删除,id用\",\"分割")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "idList", value = "制造商id", required = true, paramType = "path", dataType = "String")
    })
    public Object deleteManufacturerById(@LogParam(description = "制造商id列表") @PathVariable String idList) {
        manufacturerService.batchDeleteManufacturer(idList);
        return null;
    }

    @PutMapping(value = "/")
    @SystemLog(optDesc = "更新制造商信息", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "更新制造商信息", notes = "id不可为空。其余项目可选，不传不改。")
    public Object updateManufacturer(@UpdateValidated @RequestBody ManufacturerPO manufacturerPO) {
        manufacturerService.updateManufacturer(manufacturerPO);
        return null;
    }

    @PostMapping(value = "/file")
    @SystemLog(optDesc = "上传文件", optType = SystemLogEnum.UPLOAD)
    @ApiOperation(value = "上传文件", notes = "制造商id、文件名、文件地址、文件分组、文件远程地址不可为空。其余项目可选。")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",  value = "制造商id",required = true, dataType = "String")
    })
    public Object saveManufacturerFile(@InsertValidated @RequestBody SysFilePO sysFile, @RequestParam String id) {
        manufacturerService.saveManufacturerFile(sysFile,id);
        return null;
    }

    @DeleteMapping(value = "/file")
    @SystemLog(optDesc = "删除文件", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "删除文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileId", value = "文件id", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "id", value = "制造商id", required = true, paramType = "query", dataType = "String")
    })
    public Object deleteManufacturerFile(@LogParam(description = "文件id数组") @RequestParam String fileId,
                                         @LogParam(description = "制造商id") @RequestParam String id) {
        manufacturerService.deleteManufacturerFile(fileId, id);
        return null;
    }

    @GetMapping(value = "/file")
    @SystemLog(optDesc = "查看制造商下的所有文件", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查看制造商下的所有文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "制造商id", required = true, dataType = "String")
    })
    public Object getFilesByManufacturerId(@LogParam(description = "制造商id") @RequestParam String id) {
        return manufacturerService.getFilesByManufacturerId(id);
    }
}
