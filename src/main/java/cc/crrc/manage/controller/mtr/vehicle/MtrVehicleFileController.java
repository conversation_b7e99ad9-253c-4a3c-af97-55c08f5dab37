package cc.crrc.manage.controller.mtr.vehicle;

import cc.crrc.manage.common.annotation.InsertValidated;
import cc.crrc.manage.common.annotation.LogParam;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.service.mtr.MtrVehicleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "车辆管理-文件")
@RestController
@RequestMapping("/vehicle_File")
public class MtrVehicleFileController {
	
    @Autowired
    private MtrVehicleService mtrVehicleService;
	
	@PostMapping(value = "/file")
    @SystemLog(optDesc = "上传文件", optType = SystemLogEnum.UPLOAD)
    @ApiOperation(value = "上传文件", notes = "车辆id、文件名、文件地址、文件分组、文件远程地址不可为空。其余项目可选。")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",  value = "车辆id",required = true, dataType = "String")
    })
    public Object saveManufacturerFile(@InsertValidated @RequestBody SysFilePO sysFile,@LogParam(description = "车辆类型id") @RequestParam String id) {
        mtrVehicleService.saveVehicleFile(sysFile,id);
        return null;
    }

    @DeleteMapping(value = "/file")
    @SystemLog(optDesc = "删除文件", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "删除文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileId", value = "文件id", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "id", value = "车辆id", required = true, paramType = "query", dataType = "String")
    })
    public Object deleteManufacturerFile(@LogParam(description = "文件id数组") @RequestParam String fileId,
                                         @LogParam(description = "车辆id") @RequestParam String id) {
        mtrVehicleService.deleteVehicleFile(fileId, id);
        return null;
    }

    @GetMapping(value = "/file")
    @SystemLog(optDesc = "查看车辆下的所有文件", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "查看车辆下的所有文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "车辆id", required = true, dataType = "String")
    })
    public Object getFilesByManufacturerId(@LogParam(description = "车辆id") @RequestParam String id) {
        return mtrVehicleService.getFilesByVehicleId(id);
    }
}
