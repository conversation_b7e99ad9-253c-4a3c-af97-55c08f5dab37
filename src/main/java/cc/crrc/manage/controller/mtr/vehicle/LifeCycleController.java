package cc.crrc.manage.controller.mtr.vehicle;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.mtr.LifeCycleQueryVO;
import cc.crrc.manage.service.mtr.LifeCycleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/*******
 * 
 * <AUTHOR>
 *
 */
@Api(tags = "车辆管理-生命周期")
@RestController
@RequestMapping("/LifeCycle")
public class LifeCycleController {

	@Autowired
	private LifeCycleService struComponentService;

	@PostMapping(value = "/getLifeCycle")
	@SystemLog(optDesc = "车辆管理——部件生命周期查询", optType = SystemLogEnum.SELECT)
	@ApiOperation(value = "车辆管理——部件生命周期查询", notes = "按车型型号id,时间段、履历类型查询")
	public Object getLifeCycle(@RequestBody LifeCycleQueryVO lifeCycleQueryVO) {
		return struComponentService.getLifeCycle(lifeCycleQueryVO.getStructureCode(), lifeCycleQueryVO.getVehicleId(),
				lifeCycleQueryVO.getType(), lifeCycleQueryVO.getBeginTime(), lifeCycleQueryVO.getEndTime());
	}



}
