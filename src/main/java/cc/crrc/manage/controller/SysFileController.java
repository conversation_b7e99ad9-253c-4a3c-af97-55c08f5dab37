package cc.crrc.manage.controller;



import cc.crrc.manage.common.annotation.AopIgnore;
import cc.crrc.manage.common.utils.FastDFSClient;
import cc.crrc.manage.common.utils.FastDFSFile;
import io.swagger.annotations.Api;
import org.csource.fastdfs.FileInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
@Api(tags = "文件上传")
@RestController
public class SysFileController {
    private static Logger logger = LoggerFactory.getLogger(SysFileController.class);

    /**
     * @param multipartFile
     * @return
     * @throws IOException
     */
    @PostMapping(value = "/upload")
    public Object saveFile(@RequestParam("file")MultipartFile multipartFile) throws IOException {
        HashMap map  = new HashMap();
        String[] fileAbsolutePath={};
        String fileName=multipartFile.getOriginalFilename();
        String ext = fileName.substring(fileName.lastIndexOf(".") + 1);
        byte[] file_buff = null;
        InputStream inputStream=multipartFile.getInputStream();
        if(inputStream!=null){
            int len1 = inputStream.available();
            file_buff = new byte[len1];
            inputStream.read(file_buff);
        }
        inputStream.close();
        FastDFSFile file = new FastDFSFile(fileName, file_buff, ext);
        try {
            fileAbsolutePath = FastDFSClient.upload(file);  //upload to fastdfs
            System.out.println("============================");
            System.out.println(fileAbsolutePath);
        } catch (Exception e) {
            logger.error("upload file Exception!",e);
        }
        if (fileAbsolutePath==null) {
            logger.error("upload file failed,please upload again!");
        }
        //获取文件信息
        FileInfo info = null;
        try {
            info = FastDFSClient.getFile(fileAbsolutePath[0],fileAbsolutePath[1]);  //get from fastdfs
        } catch (Exception e) {
            logger.error("get file Exception!",e);
        }
        if (info==null) {
            logger.error("get file failed,please upload again!");
        }
        String path= FastDFSClient.getTrackerUrl()+fileAbsolutePath[0]+ "/"+fileAbsolutePath[1];
        map.put("group",fileAbsolutePath[0]);
        map.put("fileLocation",fileAbsolutePath[1]);
        map.put("fileName",fileName);
        map.put("path",path);
        map.put("size",info.getFileSize());
        map.put("format",ext);
        return map;
    }
    @DeleteMapping(value = "/deleteFile")
    @ResponseBody
    @AopIgnore
    public Object deleteFile(@RequestParam String groupName, @RequestParam String remoteFileName)throws Exception{
        HashMap map  = new HashMap();
        InputStream ins = null;
        int i = FastDFSClient.deleteFiles(groupName,remoteFileName);
        if(i==0){
            map.put("result","success");
        }else{
            map.put("result","false");
            map.put("message","分组名或文件路径错误！");
        }

        return map;
    }
    @GetMapping(value = "/downFilelx")
    @ResponseBody
    @AopIgnore
    public Object downFilelx(@RequestParam String groupName, @RequestParam String remoteFileName, @RequestParam String specFileName){
        return FastDFSClient.downFilelx(groupName,remoteFileName,specFileName);
    }

    @GetMapping(value = "/getfile")
    @ResponseBody
    public Object getFile(@RequestParam String groupName, @RequestParam String remoteFileName){
        FileInfo info = null;
        try {
            info = FastDFSClient.getFile(groupName,remoteFileName);  //get from fastdfs
        } catch (Exception e) {
            logger.error("get file Exception!",e);
        }
        if (info==null) {
            logger.error("get file failed,please upload again!");
        }
        return info;
    }

}

