package cc.crrc.manage.controller;

import cc.crrc.manage.common.annotation.*;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.SysMenuVO;
import cc.crrc.manage.service.SysMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "菜单管理")
@RestController
@RequestMapping(value = "/sysMenu")
public class SysMenuController {
    @Autowired
    private SysMenuService service;

    @SystemLog(optDesc="增加菜单", optType = SystemLogEnum.INSERT)
    @PostMapping(value = "/saveSysMenu")
    @ApiOperation(value = "新建 系统菜单 ", notes = "系统菜单新建")
    public Object saveSysMenu(@RequestBody @InsertValidated SysMenuVO sysMenuVO) {
        return service.saveSysMenu(sysMenuVO);
    }

    @SystemLog(optDesc="删除菜单", optType = SystemLogEnum.DELETE)
    @DeleteMapping(value = "/deleteSysMenuById")
    @ApiOperation(value = "删除 系统菜单 ", notes = "系统菜单删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "Id", required = true, dataType = "String")
    })
    public Object deleteSysMenuById(@RequestBody SysMenuVO sysMenuVO) {
        return service.deleteSysMenuById(sysMenuVO);
    }

    @SystemLog(optDesc="修改菜单", optType = SystemLogEnum.UPDATE)
    @PutMapping(value = "/updateSysMenu")
    @ApiOperation(value = "更新 系统菜单 ", notes = "系统菜单更新")
    public Object updateSysMenu(@RequestBody @UpdateValidated SysMenuVO sysMenuVO) {
        return service.updateSysMenu(sysMenuVO);
    }

    @SystemLog(optDesc = "查询 系统菜单", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/selectSysMenuById")
    @ApiOperation(value = "查找 系统菜单 ", notes = "系统菜单查找")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "Id", required = true, dataType = "String")
    })
    public Object selectSysMenuById(@RequestBody SysMenuVO sysMenuVO) {
        return service.selectSysMenuById(sysMenuVO);
    }

    @SystemLog(optDesc = "查询 系统菜单树list", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/selectSysMenuTreeList")
    @ApiOperation(value = "查找 系统菜单树list", notes = "系统菜单树list查找")
    public Object selectSysMenuTreeList() {
        return service.selectSysMenuTreeList();
    }

    @SystemLog(optDesc = "通过roleId查询 系统菜单树list", optType = SystemLogEnum.SELECT)
    @GetMapping(value = "/selectSysMenuTreeListByRoleId")
    @ApiOperation(value = "通过roleId查找 系统菜单树list", notes = "通过用户查找 系统菜单树list")
    public Object selectSysMenuTreeListByRoleId(@LogParam(description = "角色id")@RequestParam String roleId) {
        return service.selectSysMenuTreeListByRoleId(roleId);
    }
    
    
    @SystemLog(optDesc = "获取当前user的 系统菜单树list", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/selectSysMenuTreeListByUser")
    @ApiOperation(value = "获取当前User 系统菜单树list", notes = "通过用户查找 系统菜单树list")
    public Object selectSysMenuTreeListByRoleId() {
        return service.selectSysMenuTreeListByUser();
    }
    
    
    @SystemLog(optDesc = "获取选择完权限后的首页结构树", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/selectSysMenuTreeListByHomePage")
    @ApiOperation(value = "获取选择完权限后的首页结构树", notes = "获取选择完权限后的首页结构树")
    public Object selectSysMenuTreeListByHomePage(@RequestBody List<String> MenuIds) {
        return service.selectSysMenuTreeListByHomePage(MenuIds);
    }
    
    
    
    
    


}
