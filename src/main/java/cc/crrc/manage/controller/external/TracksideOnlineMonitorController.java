package cc.crrc.manage.controller.external;



import cc.crrc.manage.common.utils.MinioUtils;
import cc.crrc.manage.pojo.external.CheckoutAlarmDataDTO;
import cc.crrc.manage.pojo.external.TrainLogInfoDTO;
import cc.crrc.manage.service.external.TracksideOnlineMonitorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


/**
 * <AUTHOR>
 * @Description 东莞诺丽轨旁在线监测相关接口
 * @Date 2023/8/18
 **/
@Api(tags = "东莞诺丽轨旁在线监测相关接口")
@RestController
@RequestMapping(value = "/tracksideOnlineMonitor")
public class TracksideOnlineMonitorController {

    @Autowired
    private TracksideOnlineMonitorService tracksideOnlineMonitorService;


    @PostMapping(value = "/listTrainLogInfo")
    @ApiOperation(value = "监测记录", notes = "监测记录")
    public Object listTrainLogInfo(@RequestBody TrainLogInfoDTO trainLogInfoDTO) {
        return tracksideOnlineMonitorService.listTrainLogInfo(trainLogInfoDTO);
    }

    @GetMapping(value = "/listPanInfo")
    @ApiOperation(value = "受电弓检出数据", notes = "受电弓检出数据")
    public Object listPanInfo(@RequestParam String id) {
        return tracksideOnlineMonitorService.listPanInfo(id);
    }

    @GetMapping(value = "/listWheelInfo")
    @ApiOperation(value = "轮对检出数据", notes = "轮对检出数据")
    public Object listWheelInfo(@RequestParam String id) {
        return tracksideOnlineMonitorService.listWheelInfo(id);
    }

    @GetMapping(value = "/wheelCheckPointDict")
    @ApiOperation(value = "轮对检测项名称与编码字典", notes = "轮对检测项名称与编码字典")
    public Object wheelCheckPointDict() {
        return tracksideOnlineMonitorService.wheelCheckPointDict();
    }

    @GetMapping(value = "/wheelPartDict")
    @ApiOperation(value = "轮对部位名称与编码字典", notes = "轮对部位名称与编码字典")
    public Object wheelPartDict() {
        return tracksideOnlineMonitorService.wheelPartDict();
    }

    @GetMapping(value = "/panCheckPointDict")
    @ApiOperation(value = "受电弓检测项名称与编码字典", notes = "受电弓检测项名称与编码字典")
    public Object panCheckPointDict() {
        return tracksideOnlineMonitorService.panCheckPointDict();
    }

    @GetMapping(value = "/panPartDict")
    @ApiOperation(value = "受电弓部位名称与编码字典", notes = "受电弓部位名称与编码字典")
    public Object panPartDict() {
        return tracksideOnlineMonitorService.panPartDict();
    }

    @PostMapping(value = "/listAlarmInfo")
    @ApiOperation(value = "告警查询", notes = "告警查询")
    public Object listAlarmInfo(@RequestBody CheckoutAlarmDataDTO checkoutAlarmDataDTO) {
        return tracksideOnlineMonitorService.listAlarmInfo(checkoutAlarmDataDTO);
    }

    @GetMapping(value = "/wheelImages")
    @ApiOperation(value = "获取轮对图片", notes = "获取轮对图片")
    public Object getWheelImages(@RequestParam String traceFile, @RequestParam(required = false) String location) {
        return tracksideOnlineMonitorService.getWheelImages(traceFile, location);
    }

    @GetMapping(value = "/panImages")
    @ApiOperation(value = "获取受电弓图片", notes = "获取受电弓图片")
    public Object getPanImages(@RequestParam String traceFile) {
        return tracksideOnlineMonitorService.getPanImages(traceFile);
    }

    /**
     * @param picture 存储文件夹时间戳路径
     * @param partCn  部位
     * @param pointTypeCn 检测项
     * @return
     */
    @GetMapping(value = "/alarmWheelImages")
    @ApiOperation(value = "获取告警轮对图片", notes = "获取告警轮对图片")
    public Object getAlarmWheelImages(@RequestParam String picture, @RequestParam(required = false) String partCn, @RequestParam(required = false) String pointTypeCn) {
        return tracksideOnlineMonitorService.getAlarmWheelImages(picture, partCn, pointTypeCn);
    }

    @GetMapping(value = "/alarmSpanImages")
    @ApiOperation(value = "获取告警受电弓图片", notes = "获取告警受电弓图片")
    public Object getAlarmSpanImages(@RequestParam String picture, @RequestParam(required = false) String partCn) {
        return tracksideOnlineMonitorService.getAlarmPanImages(picture,  partCn);
    }

    //@RequestParam(value = "file") MultipartFile multipartFile,
    @PostMapping(value = "/fileUpload")
    @ApiOperation(value = "上传文件，测试使用", notes = "上传文件，测试使用")
    public Object fileUpload(@RequestParam(value = "file") MultipartFile multipartFile,String path) {
        String fileUrl = MinioUtils.fileUpload(multipartFile,path);
        return fileUrl;
    }

    @GetMapping(value = "/locationDict")
    @ApiOperation(value = "获取车厢下拉", notes = "获取车厢下拉")
    public Object locationDict() {
        return tracksideOnlineMonitorService.locationDict();
    }

    @GetMapping(value = "/deleteObject")
    @ApiOperation(value = "删除文件夹", notes = "删除minio文件夹")
    public void deleteObject() throws Exception {
        MinioUtils.removeObject();
    }
}