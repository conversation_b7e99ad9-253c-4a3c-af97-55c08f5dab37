package cc.crrc.manage.controller.external;

import cc.crrc.manage.pojo.line.LineDTO;
import cc.crrc.manage.service.external.ExtIntfManegerService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.Map;


@Api(tags = "对外接口管理")
@RestController
@RequestMapping(value = "/extIntf/manager")
public class ExtIntfManegerController {
    private ExtIntfManegerService service;

    @PostMapping(value = "/token")
    @ApiOperation(value = "新增token")
    public Object addToken(@RequestBody String requester,String intfUrl) {
        return service.addToken(requester,intfUrl);
    }

    @GetMapping(value = "/token")
    @ApiOperation(value = "查询token")
    public Object getToken() {
        System.out.println("link start...");
        JSONObject obj = new JSONObject();
        obj.put("aaa",1);
        obj.put("bbb",2);
//        return service.getToken();
        return obj;
    }

    @DeleteMapping(value = "/token")
    @ApiOperation(value = "删除token")
    public void delToken(@RequestBody String id) {
//        return service.delToken(id);
    }

}
