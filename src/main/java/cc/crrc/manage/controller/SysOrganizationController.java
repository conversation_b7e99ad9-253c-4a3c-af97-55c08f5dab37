package cc.crrc.manage.controller;

import cc.crrc.manage.common.annotation.LogParam;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.pojo.SysOrganizationPO;
import cc.crrc.manage.service.SysOrganizationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * @FileName SysOrganizationController
 * <AUTHOR> yuxi
 * @Date 2019/10/12 13:10
 * @Version 1.0
 **/
@Api(tags = "组织管理")
@RestController
@RequestMapping("/organization")
@Validated
public class SysOrganizationController {
    @Autowired
    private SysOrganizationService organizationService;

    @SystemLog(optDesc = "查询所有组织", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "取得所有组织")
    @PostMapping("/organizations")
    public Object listOrganizations() {
        return organizationService.listOrganizations();
    }
    
    /**
     * 
     * @Title: listOrganizationsUser   
     * @Description: 当前用户组织结构  
     * @param: @return      
     * @return: Object
     * @date:   2020年8月17日 下午2:41:32  
     * @author: Heshenglun   
     * @throws
     */
    @SystemLog(optDesc = "查询用户的所有组织", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "取得用户的所有组织")
    @PostMapping("/organizations/user")
    public Object listOrganizationsUser() {
        return organizationService.listOrganizationsUser();
    }

    @ApiOperation(value = "根据id查询组织")
    @GetMapping("/organization/id")
    public Object getOrganizationById(@RequestParam("id") String id) {
        return organizationService.getOrganizationById(id);
    }

    @SystemLog(optDesc = "查询组织", optType = SystemLogEnum.SELECT)
    @ApiOperation(value = "根据名称查询组织")
    @GetMapping("/organization/name")
    public Object getOrganizationByName(@LogParam(description = "组织名称") @RequestParam("name") String name) {
        return organizationService.getOrganizationByName(name);
    }

    @SystemLog(optDesc = "增加组织", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "新增组织")
    @PostMapping("/organization")
    public Object addOrganization(@RequestBody @Valid SysOrganizationPO organizationNew) {
        // 校验组织名称是否已经存在
        int nameExistsCount = organizationService.getOrganizationCountByName(organizationNew.getName());
        if (nameExistsCount > 0) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "组织名称已存在！");
        }
        // 校验组织编码是否已经存在
        int codeExistsCount = organizationService.getOrganizationCountByCode(organizationNew.getCode());
        if (codeExistsCount > 0) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "组织编码已存在！");
        }
        return organizationService.addOrganization(organizationNew);
    }

    @SystemLog(optDesc = "修改组织", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "更新组织")
    @PutMapping("/organization")
    public Object updateOrganization(@RequestBody @Valid SysOrganizationPO organizationNew) {
        // 取得待更新的组织本身，验证组织名称是否有变换
        SysOrganizationPO organizationOrigin = organizationService.getOrganizationById(organizationNew.getId());
        // 验证修改后的组织名称是否已存在
        int nameExistsCount = organizationService.getOrganizationCountByName(organizationNew.getName());

        // 如果组织名称发生变化，并且修改后的名称已经存在
        if (!organizationOrigin.getName().equals(organizationNew.getName()) && nameExistsCount > 0) {
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION.getErrorCode(), "组织名称已存在！");
        }

        // 验证修改后的组织编码是否已存在
        int codeExistsCount = organizationService.getOrganizationCountByCode(organizationNew.getCode());
        // 如果组织编码发生变化，并且修改后的编码已经存在
        if (!organizationOrigin.getCode().equals(organizationNew.getCode()) && codeExistsCount > 0) {
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION.getErrorCode(), "组织编码已存在！");
        }
        return organizationService.updateOrganization(organizationNew);
    }

    @SystemLog(optDesc = "删除组织", optType = SystemLogEnum.DELETE)
    @ApiOperation(value = "删除组织")
    @DeleteMapping("/organization")
    public Object deleteOrganization(@LogParam(description = "组织id") @RequestParam("id") String id) {
        return organizationService.deleteOrganization(id);
    }

    @SystemLog(optDesc = "新增用户组织关系（测试用）", optType = SystemLogEnum.INSERT)
    @ApiOperation(value = "新增用户组织关系", hidden = true)
    @PostMapping("/organization/{organizationIdStr}/userId/{userId}")
    public Object addOrganizationUserRelation(@ApiParam(value = "组织ids", required = true) @PathVariable String organizationIdStr,
                                              @ApiParam(value = "用户id", required = true) @PathVariable String userId) {
        organizationService.addOrganizationUserRelation(organizationIdStr, userId);
        return 1;
    }

    @SystemLog(optDesc = "更新用户组织关系（测试用）", optType = SystemLogEnum.UPDATE)
    @ApiOperation(value = "更新用户组织关系", hidden = true)
    @PutMapping("/organization/{organizationIdStr}/userId/{userId}")
    public Object updateOrganizationUserRelation(@ApiParam(value = "组织ids", required = true) @PathVariable String organizationIdStr,
                                                 @ApiParam(value = "用户id", required = true) @PathVariable String userId) {
        organizationService.updateOrganizationUserRelation(organizationIdStr, userId);
        return 1;
    }
    @SystemLog(optDesc = "查询可更改的组织树结构",optType = SystemLogEnum.SELECT)
    @ApiOperation(value="查询可更改的组织树结构")
    @GetMapping("/organization/tree/user")
    public Object getOrganizationTreeUser(@ApiParam(value = "组织id",required = true)@RequestParam("id")String id){
        return organizationService.getOrganizationTreeUser(id);
    }
}
