package cc.crrc.manage.controller;

import cc.crrc.manage.common.annotation.LogParam;

import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.pojo.SysLogDTO;
import cc.crrc.manage.service.SysLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "日志管理")
@RestController
@RequestMapping(value = "/log")
public class LogController {
    @Autowired
    private SysLogService logService;

    @SystemLog(optDesc = "查询日志信息", optType=SystemLogEnum.SELECT)
    @PostMapping(value = "/getLogs")
    @ApiOperation(value = "查询log信息 ", notes = "查询log信息")
    public Object getLogs(@RequestBody SysLogDTO log) {
        return logService.getLogs(log);
    }
    
    @PostMapping(value = "/getOptCount")
    @ApiOperation(value = "查询操作数量 ", notes = "查询操作数量")
    public Object getOptCount(@RequestBody SysLogDTO log) {
    	return logService.getOptCount(log);
    }
    
    @SystemLog(optDesc="查询菜单访问数量", optType = SystemLogEnum.SELECT)
    @PostMapping(value = "/getMenuCount")
    @ApiOperation(value = "查询菜单访问数量 ", notes = "查询菜单访问数量")
    public Object getMenuCount(@LogParam(description="开始时间")@RequestParam String startTime,@LogParam(description="结束时间")@RequestParam String endTime) {
    	return logService.getMenuCount(startTime,endTime);
    }

    @PostMapping(value = "/getAccessCount")
    @ApiOperation(value = "查询系统访问数量 ", notes = "查询系统访问数量")
    public Object getAccessCount() {
    	return logService.getAccessCount();
    }
    
    @PostMapping(value = "/getAgent")
    @ApiOperation(value = "查询浏览器列表 ", notes = "查询浏览器列表")
    public Object getAgent(@RequestParam String startTime,@RequestParam String endTime) {
    	return logService.getAgent(startTime,endTime);
    }
    
    @PostMapping(value = "/getVisitors")
    @ApiOperation(value = "查询访客列表 ", notes = "查询访客列表")
    public Object getVisitors(@RequestBody SysLogDTO log) {
    	return logService.getVisitors(log);
    }
}
