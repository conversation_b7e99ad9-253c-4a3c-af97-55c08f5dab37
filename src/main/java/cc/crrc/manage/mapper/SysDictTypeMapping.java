package cc.crrc.manage.mapper;

import cc.crrc.manage.pojo.SysDictTypePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @FileName SysDictTypeMapping
 * <AUTHOR> yuxi
 * @Date 2019/10/16 11:13
 * @Version 1.0
 **/
public interface SysDictTypeMapping {
    int updateDictType(SysDictTypePO dictType);

    int addDictType(SysDictTypePO dictType);

    List<SysDictTypePO> listByType(SysDictTypePO dictType);

    String getTypeLevel(@Param("typeCode") String typeCode);
}
