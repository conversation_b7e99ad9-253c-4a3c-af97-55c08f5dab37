package cc.crrc.manage.mapper.monitor;

import cc.crrc.manage.pojo.excel.OilChangeForExcelPO;
import cc.crrc.manage.pojo.monitor.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 换油周期
 * @Date 2021/9/17
 **/
@Repository
public interface OilChangePeriodMapping {

    int checkComponentUnique(WorkPeriodPO workPeriodPO);

    int addOilChangeInfo(WorkPeriodPO workPeriodPO);

    WorkPeriodPO getInfoById(@Param("id") String id);

    List<WorkPeriodVO> listOilChangeInfo(WorkPeriodVO workPeriodVO);

    int oilChangeHistory(WorkPeriodPO workPeriodPO);

    List<OilChangeHistoryForExcelPO> getExcelData(@Param("id") String id);

    List<OilChangeForExcelPO> getOilChangeForExcel(@Param("countNum") String countNum, @Param("lineId") String lineId, @Param("vehicleTypeId") String vehicleTypeId, @Param("vehicleCode") String vehicleCode, @Param("type") String type, @Param("location") String location);

    List<WorkPeriodVO> listOilChangeHistory(@Param("id") String id);

    int updateCurrentInfo(WorkPeriodPO workPeriodPO);

    int deleteOilChangeInfo(@Param("id") String id, @Param("userId") String userId);

    void batchAddRecord(@Param("list") List<WorkPeriodPO> list);

    String getLineIdByVehicleTypeId(@Param("vehicleTypeId") String vehicleTypeId);

    Long getMileageByTargetTime(@Param("targetTime")Date targetTime,@Param("vehicleCode") String vehicleCode);
}
