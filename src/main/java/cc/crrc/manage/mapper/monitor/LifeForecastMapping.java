package cc.crrc.manage.mapper.monitor;

import cc.crrc.manage.pojo.excel.LifeForecastForExcelPO;
import cc.crrc.manage.pojo.monitor.LifeForecastConfigDTO;
import cc.crrc.manage.pojo.monitor.LifeForecastDTO;
import cc.crrc.manage.pojo.monitor.RelayContactorLifePO;
import cc.crrc.manage.pojo.monitor.RelayContactorPO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/5/6 17:01
 */
@Repository
public interface LifeForecastMapping {

    ArrayList<RelayContactorPO> findRelayContactorBys(LifeForecastDTO lifeForecastDTO);

    int findCountBys(LifeForecastDTO lifeForecastDTO);

    List<RelayContactorPO> listRelayContactor(@Param("vehicleTypeId") String vehicleTypeId);

    int insertRelayContactorLife(@Param("relayContactorLifePOS")List<RelayContactorLifePO> relayContactorLifePOS);

    RelayContactorPO getById(String id);

    void addRelayHistory(RelayContactorPO rPo);

    void addReplaceTimes(@Param("id")String id, @Param("userName")String userName,@Param("replaceTimes")Long replaceTimes);

    void updRelayContactorLife(@Param("id")String id, @Param("userName")String userName);

    ArrayList<RelayContactorPO> getExcelData(String id);

    List<LifeForecastForExcelPO> getLifeForecastForExcel(@Param("lifeWarning") Integer lifeWarning, @Param("lineId")String lineId,@Param("vehicleCode")String vehicleCode,@Param("componentType")String componentType);

    List<RelayContactorPO> relayContactorLifesHistory(@Param("id")String id);

    int insertRelayContactorInfo(RelayContactorPO relayContactorPO);

    int updateRelayContactorInfo(RelayContactorPO relayContactorPO);

    int delRelayContactorInfo(@Param("id") String id, @Param("userId") String userId);

    List<RelayContactorPO> findRelayContactorConfigList(LifeForecastConfigDTO configDTO);

    int repeatCount(RelayContactorPO relayContactorPO);

    List<HashMap<String, String>> relayContactorExistList(@Param("lineId") String lineId);

    int updateRelayContactorLife(RelayContactorLifePO relayContactorLifePO);
}
