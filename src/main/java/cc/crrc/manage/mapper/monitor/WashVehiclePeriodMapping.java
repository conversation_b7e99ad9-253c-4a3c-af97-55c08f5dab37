package cc.crrc.manage.mapper.monitor;

import cc.crrc.manage.pojo.excel.LifeForecastForExcelPO;
import cc.crrc.manage.pojo.excel.OilChangeForExcelPO;
import cc.crrc.manage.pojo.excel.WashVehicleForExcelPO;
import cc.crrc.manage.pojo.monitor.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 洗车周期
 * @Date 2021/9/17
 **/
@Repository
public interface WashVehiclePeriodMapping {

    int checkVehicleUnique(WorkPeriodPO workPeriodPO);

    int addWashVehicleInfo(WorkPeriodPO workPeriodPO);

    WorkPeriodPO getInfoById(@Param("id") String id);

    List<WorkPeriodVO> listWashVehicleInfo(WorkPeriodVO workPeriodVO);

    int washVehicleHistory(WorkPeriodPO workPeriodPO);

    List<WashVehicleHistoryForExcelPO> getExcelData(@Param("id") String id);

    List<WashVehicleForExcelPO> getWashVehicleForExcel(@Param("countNum") String countNum, @Param("lineId") String lineId, @Param("vehicleCode") String vehicleCode, @Param("mode") String mode);

    List<WorkPeriodVO> listWashVehicleHistory(@Param("id") String id);

    int updateCurrentInfo(WorkPeriodPO workPeriodPO);

    int deleteWashVehicleInfo(@Param("id") String id, @Param("userId") String userId);

    void batchAddRecord(@Param("list") List<WorkPeriodPO> list);

    String getLineIdByVehicleCode(@Param("vehicleCode") String vehicleCode);
}
