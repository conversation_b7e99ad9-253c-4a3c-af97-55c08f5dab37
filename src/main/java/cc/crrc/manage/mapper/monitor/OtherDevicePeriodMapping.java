package cc.crrc.manage.mapper.monitor;

import cc.crrc.manage.pojo.excel.OtherDeviceForExcelPO;
import cc.crrc.manage.pojo.monitor.OtherDeviceHistoryForExcelPO;
import cc.crrc.manage.pojo.monitor.OtherDeviceImportExportPO;
import cc.crrc.manage.pojo.monitor.WorkPeriodPO;
import cc.crrc.manage.pojo.monitor.WorkPeriodVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 其他设备周期
 * @Date 2022/2/17
 **/
@Repository
public interface OtherDevicePeriodMapping {

    int checkComponentUnique(WorkPeriodPO workPeriodPO);

    int addOtherDeviceInfo(WorkPeriodPO workPeriodPO);

    WorkPeriodPO getInfoById(@Param("id") String id);

    List<WorkPeriodVO> listOtherDeviceInfo(WorkPeriodVO workPeriodVO);

    int otherDeviceHistory(WorkPeriodPO workPeriodPO);

    List<OtherDeviceHistoryForExcelPO> getExcelData(@Param("id") String id);

    List<OtherDeviceForExcelPO> getOtherDeviceForExcel(@Param("countNum") String countNum, @Param("lineId") String lineId, @Param("vehicleTypeId") String vehicleTypeId, @Param("vehicleCode") String vehicleCode, @Param("type") String type, @Param("location") String location, @Param("carriage") String carriage, @Param("nameCn") String nameCn);

    List<WorkPeriodVO> listOtherDeviceHistory(@Param("id") String id);

    int updateCurrentInfo(WorkPeriodPO workPeriodPO);

    int deleteOtherDeviceInfo(@Param("id") String id, @Param("userId") String userId);

    void batchAddRecord(@Param("list") List<WorkPeriodPO> list);
}
