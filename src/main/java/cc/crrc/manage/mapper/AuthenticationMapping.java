package cc.crrc.manage.mapper;

import cc.crrc.manage.security.PathGrantedAuthority;
import cc.crrc.manage.security.UserDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @FileName AuthenticationMapping
 * @<PERSON> <PERSON>
 * @Date 2019/9/26 14:13
 **/
public interface AuthenticationMapping {

    UserDetail loadUserByUsername(@Param("username") String username);

    List<PathGrantedAuthority> loadUserAuthority(@Param("roleId") Long roleId);

    List<Long> loadOrganization(@Param("userId") Long userId);

    List<Long> loadSelfOrganization(@Param("userId") Long userId);

}
