package cc.crrc.manage.mapper;

import cc.crrc.manage.pojo.BusDictType;
import cc.crrc.manage.pojo.BusDictTypeQuery;
import cc.crrc.manage.pojo.BusDictTypeVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BusDictTypeMapping {

    /**
     * 查询业务字典类型列表
     *
     * @param busDictTypeQuery 业务字典类型
     * @return 业务字典类型集合
     */
    List<BusDictTypeVO> selectBusDictTypeList(BusDictTypeQuery busDictTypeQuery);

    /**
     * 新增业务字典类型
     *
     * @param busDictType 业务字典类型
     * @return 结果
     */
    int insertBusDictType(BusDictType busDictType);

    /**
     * 修改业务字典类型
     *
     * @param busDictType 业务字典类型
     * @return 结果
     */
    int updateBusDictType(BusDictType busDictType);


    /**
     * 批量删除业务字典类型
     *
     * @param
     * @return 结果
     */
    int deleteBusDictTypeByIds(@Param("id") String id, @Param("lastModifyBy") String lastModifyBy, @Param("lastModifyTime") String lastModifyTime);

    String listCodeByIds(@Param("id") String id);

}
