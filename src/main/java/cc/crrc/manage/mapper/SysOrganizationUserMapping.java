package cc.crrc.manage.mapper;

import cc.crrc.manage.pojo.SysOrganizationUserPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @FileName SysOrganizationUserMapping
 * <AUTHOR> yuxi
 * @Date 2019/10/14 14:22
 * @Version 1.0
 **/
public interface SysOrganizationUserMapping {
    int addOrganizationUser(SysOrganizationUserPO organizationUserPO);

    List<SysOrganizationUserPO> listOrganizationUserRelationsByUserId(@Param("userId") String userId);
    //根据组织ID查询阻止用户表中的userId
    List<SysOrganizationUserPO> UserIdByOrganizationId(@Param("organizationId") String organizationId);

    int deleteOrganizationUserRelation(SysOrganizationUserPO organizationUserPO);

    int countUserByOrganizationId(@Param("organizationId") String organizationId);
}
