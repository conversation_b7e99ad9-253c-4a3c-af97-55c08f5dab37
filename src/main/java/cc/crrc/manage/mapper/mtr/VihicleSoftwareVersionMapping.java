package cc.crrc.manage.mapper.mtr;

import cc.crrc.manage.pojo.mtr.VehicleSoftwareVersionDTO;
import cc.crrc.manage.pojo.mtr.VehicleSoftwareVersionPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface VihicleSoftwareVersionMapping {

    List<VehicleSoftwareVersionPO> VehicleSoftwareVersionList(VehicleSoftwareVersionDTO vehicleSoftwareVersionDTO);

    List<VehicleSoftwareVersionPO> getVersionResume(@Param("softwareId") String softwareId, @Param("componentId") String componentId);
}
