package cc.crrc.manage.mapper.mtr;

import cc.crrc.manage.common.annotation.ParamReplace;
import cc.crrc.manage.pojo.mtr.StruVehicleStructureComponentPO;
import cc.crrc.manage.pojo.mtr.StruVehicleStructureComponentUpdateDTO;
import cc.crrc.manage.pojo.stru.VehicleStructureTreeVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

/**
 * @FileName StruVehicleStructureComponentMapping
 * <AUTHOR> xin
 * @Date 2020/3/18 9:51
 * @Version 1.0
 **/
@Repository
public interface StruVehicleStructureComponentMapping {

    void addStruVehicleStructureComponent(@Param("list") List<StruVehicleStructureComponentPO> list);

    void deleteStruVehicleStructureComponent(@Param("vehicleId") String vehicleId, @Param("currentId") String currentId);

    List<VehicleStructureTreeVO> selectVehicleStructureTreeList(@Param("vehicleTypeId") String vehicleTypeId, @Param("lineId") String lineId, @Param("vehicleId") String vehicleId);

    /**
     * 2020-03-11 车辆查询构型
     */
    List<VehicleStructureTreeVO> selectVehicleStructureTreeHistoryList(@Param("vehicleTypeId") String vehicleTypeId,
                                                                       @Param("lineId") String lineId,
                                                                       @Param("vehicleId") String vehicleId,
                                                                       @Param("time") String time);

    @ParamReplace(param = "nameCn")
    List<String> listUniqueFlag(@Param("nameCn") String nameCn, @Param("lineId") String lineId, @Param("vehicleId") String vehicleId);

    int valid(@Param("vehicleId") String vehicleId, @Param("structureCode") String structureCode);

    int updateVehicleInfo(StruVehicleStructureComponentPO struVehicleStructureComponentPO);

    int updateVehicleStructureBasicInfo(StruVehicleStructureComponentUpdateDTO struVehicleStructureComponentPO);

    int deleteVehicleInfo(@Param("id") String id, @Param("userId") String userId);

    String getComponentId(@Param("structureCode") String structureCode, @Param("vehicleId") String vehicleId);

    void bindComponent(@Param("componentTypeId") String componentTypeId, @Param("vehicleId") String vehicleId, @Param("componentId") String componentId, @Param("structureCode") String structureCode);

    List<StruVehicleStructureComponentPO> getSubStructureById(@Param("id") String id);

    StruVehicleStructureComponentPO getStruVehicleStructureById(@Param("id") String id);

    HashMap<String, String> getStructureSystem(@Param("structureCodeList") List<String> structureCodeList, @Param("vehicleId") String vehicleId);

    int countSubSystemInVehicle(@Param("vehicleId") String vehicleId, @Param("name") String name);

    //21-11-18 lixin调试发现该方法暂时无用
    StruVehicleStructureComponentPO getVehicleStructureComponentByVehicleIdAndStructureCode(@Param("vehicleId") String vehicleId,
                                                                                            @Param("structureCode") String structureCode);
    //查询已删除 车辆构型 有没有又添加回来的记录
    int selectStruVehicleStructureComponentDif(@Param("vehicleId") String vehicleId, @Param("structureCode")String structureCode);

    List<String> getLocationByVehicleId(@Param("vehicleId") String vehicleId, @Param("nameCn") String nameCn);
}
