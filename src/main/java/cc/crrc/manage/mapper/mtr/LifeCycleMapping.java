package cc.crrc.manage.mapper.mtr;

import cc.crrc.manage.pojo.mtr.ComponentLifeCycleVo;
import cc.crrc.manage.pojo.mtr.SoftwareLifeCycleVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface LifeCycleMapping {
	List<ComponentLifeCycleVo> getComponentLiftCycle(@Param("structureCode")String structureCode, @Param("vehicleId")String vehicleId);

	List<SoftwareLifeCycleVo> getSoftwareLiftCycle(@Param("componentId")String componentId);

}
