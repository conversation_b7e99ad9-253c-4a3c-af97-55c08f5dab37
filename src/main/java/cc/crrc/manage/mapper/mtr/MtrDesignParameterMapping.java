package cc.crrc.manage.mapper.mtr;

import cc.crrc.manage.pojo.component.DesignParameterDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MtrDesignParameterMapping {
    List<DesignParameterDTO> getDesignParam(@Param("comTypeId")String comTypeId, @Param("vehicleTypeId")String vehicleTypeId);

    int insertDesignParam(DesignParameterDTO designParam);

    int updateDesignParam(DesignParameterDTO designParam);

    int deleteDesignParam(@Param("id")String id);
}
