package cc.crrc.manage.mapper.mtr;

import cc.crrc.manage.common.utils.Page;
import cc.crrc.manage.pojo.mtr.MtrSoftwareVersionMappingVO;

import java.util.List;

/**
 * @Entity generate.MtrSoftwareMapping
 */
public interface MtrSoftwareVersionMappingMapping {
    /**
     * @mbg.generated 2021-11-08 15:18:00
     */
    int deleteByPrimaryKey(String id);


    /**
     * @mbg.generated 2021-11-08 15:18:00
     */
    int insertSelective(MtrSoftwareVersionMappingVO record);

    /**
     * @mbg.generated 2021-11-08 15:18:00
     */
    MtrSoftwareVersionMappingVO selectByPrimaryKey(String id);

    /**
     * @mbg.generated 2021-11-08 15:18:00
     */
    int updateByPrimaryKeySelective(MtrSoftwareVersionMappingVO record);

    MtrSoftwareVersionMappingVO selectByStructureCode(String structureCode);

    List<MtrSoftwareVersionMappingVO> selectByMultiCondition(MtrSoftwareVersionMappingVO softwareVO);

    String getIdByEnName(String signalNameEn);
}