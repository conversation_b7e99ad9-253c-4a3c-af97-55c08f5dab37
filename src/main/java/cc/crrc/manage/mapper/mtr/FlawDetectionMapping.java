package cc.crrc.manage.mapper.mtr;

import cc.crrc.manage.pojo.mtr.FlawDetectionPO;
import cc.crrc.manage.pojo.mtr.FlawDetectionProductPO;
import cc.crrc.manage.pojo.mtr.FlawDetectionProductVO;
import cc.crrc.manage.pojo.mtr.FlawDetectionVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/** 专项记录-探伤记录
 * <AUTHOR>
 * @date 2022/3/25 9:58
 */
@Repository
public interface FlawDetectionMapping {

    List<FlawDetectionVO> queryFlawDetectionList(FlawDetectionPO flawDetectionPO);

    FlawDetectionVO queryFlawDetectionInfo(@Param("id") String id);

    List<FlawDetectionProductVO> queryFlawDetectionProduct(@Param("flawDetectionId") String flawDetectionId);

    int saveFlawDetection(FlawDetectionPO flawDetectionPO);

    int saveFlawDetectionProduct(FlawDetectionProductPO flawDetectionProductPO);

    int changeFlawDetection(FlawDetectionPO flawDetectionPO);

    int deleteFlawDetectionProduct(@Param("flawDetectionId") String flawDetectionId);

    int changeFlawDetectionDelFlag(@Param("id") String id);
}
