package cc.crrc.manage.mapper.mtr;

import cc.crrc.manage.pojo.SysFilePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface VehicleFileMapping {
    List<SysFilePO> getFilesByVehicleId(@Param("vehicleId")String vehicleId);

    Integer addVehicleFile(@Param("fileId") String fileId, @Param("vehicleId") String vehicleId);

    Integer deleteVehicleIdFileByFileId(@Param("fileId") String fileId,@Param("vehicleId") String vehicleId);
}