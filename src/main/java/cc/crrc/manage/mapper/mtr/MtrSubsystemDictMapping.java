package cc.crrc.manage.mapper.mtr;

import cc.crrc.manage.pojo.mtr.MtrSubsystemDictPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @FileName MtrSubsystemDictMapping
 * @<PERSON> <PERSON>uxi
 * @Date 2019/11/19 13:30
 * @Version 1.0
 **/
public interface MtrSubsystemDictMapping {
    List<MtrSubsystemDictPO> listSubsystemDictByVehicleId(@Param("vehicleId") String vehicleId);

    int deleteSubsystemDict(@Param("vehicleId") String vehicleId,@Param("nameCn") String nameCn);

    int insertSubsystemDict(MtrSubsystemDictPO subsystemDict);

    int addSystemFromAllDict(@Param("vehicleId") String vehicleId,@Param("userId") String userId,@Param("ids") List<String> ids,@Param("id") String id);
}
