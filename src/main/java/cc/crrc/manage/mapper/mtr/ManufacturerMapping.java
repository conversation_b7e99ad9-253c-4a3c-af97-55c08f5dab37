package cc.crrc.manage.mapper.mtr;

import cc.crrc.manage.pojo.mtr.ManufacturerDTO;
import cc.crrc.manage.pojo.mtr.ManufacturerPO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * 2019/11/9
 **/
public interface ManufacturerMapping {

    List<ManufacturerPO> listManufacturer(ManufacturerDTO manufacturerDTO);

    ManufacturerPO getManufacturerById(@Param("id") String id);

    Integer addManufacturer(ManufacturerPO manufacturerPO);

    Integer updateManufacturer(ManufacturerPO manufacturerPO);

    Integer deleteManufacturerById(@Param("id") String id);

}
