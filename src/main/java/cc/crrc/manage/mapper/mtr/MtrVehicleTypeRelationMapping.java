package cc.crrc.manage.mapper.mtr;

import cc.crrc.manage.pojo.mtr.MtrVehicleTypeRelationPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MtrVehicleTypeRelationMapping {
    MtrVehicleTypeRelationPO getVehicleTypeById(@Param("vehicleId")String vehicleId);

    List<MtrVehicleTypeRelationPO> listVehicleByVehicleTypeId(@Param("vehicleTypeId")Long vehicleTypeId);

    List<MtrVehicleTypeRelationPO> listVehicleHistory(@Param("vehicleId")Long vehicleId);

    Integer invalidRelationByVehicleId(@Param("vehicleId")String vehicleId);

    Integer addRelation(@Param("vehicleId")String vehicleId,@Param("vehicleTypeId") String vehicleTypeId);
}
