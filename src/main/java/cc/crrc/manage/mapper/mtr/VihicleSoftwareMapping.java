package cc.crrc.manage.mapper.mtr;

import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.mtr.ConfigSoftwareComponentPO;
import cc.crrc.manage.pojo.mtr.VehicleSoftwareDTO;
import cc.crrc.manage.pojo.mtr.VehicleSoftwarePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface VihicleSoftwareMapping {


    List<VehicleSoftwarePO> VehicleSoftwareList(VehicleSoftwareDTO vehicleSoftwareDTO);

    int addVehicleSoftware(VehicleSoftwarePO vehicleSoftwarePO);

    int instcomponentTypeSoftware(@Param("vehicleTypeId")String vehicleTypeId, @Param("componentTypeId") String componentTypeId, @Param("id") String id);

    int configVehicleSoftware(@Param("vehicleId")String vehicleId, @Param("componentId")String componentId, @Param("softwareId")String softwareId,@Param("id") String id);

    int saveVehicleSoftwareFile(@Param("fileId")String fileId, @Param("id")String id);

    List<SysFilePO> getVehicleSoftwareFile(@Param("id")String id);

    int deleteVehicleSoftwareFile(@Param("fileId")String fileId);

    List<Map<String,String>>  getComponentSoftware(@Param("softwareId")String softwareId);

    int delSoftware(String softwareId);

    List<Map<String,Long>> getComponentTypeSoftware(@Param("componentTypeId")Long componentTypeId, @Param("softwareId")String id,@Param("vehicleTypeId")String vehicleTypeId);

    int updateVehicleSoftware(VehicleSoftwarePO vehicleSoftwarePO);

    List<ConfigSoftwareComponentPO> getComponentListByTypeID(@Param("componentTypeId")String componentTypeId);

    List<Map<String,Long>> getComponentSoftwareListByComponentID(@Param("componentId")String componentId, @Param("softwareId")String softwareId);

    int delComponentSoftware(@Param("componentTypeId")String componentTypeId);

	List<VehicleSoftwarePO> getSoftware(@Param("componentId")String componentId);

//	void insertSoftware(VehicleSoftwareFileDTO vehicleSoftwarePO);

	void changeValid(@Param("componentId") String componentId, @Param("softwareName") String softwareName);

	List<VehicleSoftwarePO> getVehicleSoftwareList(@Param("componentTypeId")String componentTypeId, @Param("vehicleTypeId")String vehicleTypeId, @Param("softwareName") String softwareName);
}
