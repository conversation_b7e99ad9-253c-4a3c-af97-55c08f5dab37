package cc.crrc.manage.mapper.mtr;

import cc.crrc.manage.pojo.mtr.ManufacturerEmployeePO;
import cc.crrc.manage.pojo.mtr.ManufacturerEmployeeVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * 2019/11/9
 **/
public interface ManufacturerEmployeeMapping {

    List<ManufacturerEmployeeVO> listManufacturerEmployee(@Param("searchKey") String searchKey);

    List<ManufacturerEmployeeVO> listManufacturerEmployeeByManufacturerId(@Param("manufacturerId") String manufacturerId);

    ManufacturerEmployeeVO getManufacturerEmployeeById(@Param("id") String id);

    Integer addManufacturerEmployee(ManufacturerEmployeePO manufacturerEmployeePO);

    Integer updateManufacturerEmployee(ManufacturerEmployeePO manufacturerEmployeePO);

    Integer deleteManufacturerEmployeeById(@Param("id") String id);

    Integer deleteManufacturerEmployeeByManufacturerId(@Param("manufacturerId") String manufacturerId);
}
