package cc.crrc.manage.mapper.mtr;

import cc.crrc.manage.pojo.mtr.MtrVehicleDTO;
import cc.crrc.manage.pojo.mtr.MtrVehicleDetailVO;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import cc.crrc.manage.pojo.mtr.MtrVehicleVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
public interface MtrVehicleMapping {

    List<MtrVehicleVO> listVehicleInfo(MtrVehicleDTO mtrVehicleDTO);

    List<MtrVehiclePO> listVehicle(@Param("metroLineId") String metroLineId);

    List<Map<String, Object>> listVehicleExcel(@Param("metroLineId") String metroLineId);
    //<!--2020-0302 for 故障预警查询车辆  数字从小到大排序 -->
    List<MtrVehiclePO> listVehicleForPrognostic(@Param("metroLineId") String metroLineId,@Param("vehicleTypeId") String vehicleTypeId);

    MtrVehicleDetailVO getVehicleById(@Param("vehicleId") String vehicleId,@Param("fileType") String fileType);

    //2020-0312 车辆构型历史回溯
    MtrVehicleDetailVO getVehicleByIdForHistory(@Param("vehicleId") String vehicleId,
                                                @Param("time") String time,@Param("fileType") String fileType);

    Integer addVehicle(MtrVehiclePO mtrVehiclePO);

    Integer deleteVehicle(@Param("id") String id,@Param("modifyBy") String modifyBy);

    Integer updateVehicle(MtrVehiclePO mtrVehiclePO);

    MtrVehiclePO getVehicleByCode(@Param("vehicleCode") String vehicleCode);
    
    List<MtrVehiclePO> listVehicleByTypeId(String vehicleTypeId);
    //2020-04-07 增加返回所查询车辆存在的最早记录
    Date selectVehicleStartDate(@Param("vehicleId")String vehicleId);
    //2020-04-22
    List<MtrVehiclePO>  listVehicleByLineIdAndVehicleTypeId(@Param("metroLineId") String metroLineId,@Param("vehicleTypeId") String vehicleTypeId);

    Map<String, String> getVehicleTypeAndLineIdById(@Param("vehicleId")String vehicleId);

    Map<String, Object> getVehicleTypeAndLineIdByVehicleCode(@Param("vehicleCode")String vehicleCode);

    List<MtrVehiclePO> listVehicleForRelayContactor(@Param("lineId")String lineId);




}
