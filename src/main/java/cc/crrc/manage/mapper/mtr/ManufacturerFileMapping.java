package cc.crrc.manage.mapper.mtr;

import cc.crrc.manage.pojo.SysFilePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ManufacturerFileMapping {

    List<SysFilePO> getFilesByManufacturerId(@Param("manufacturerId")String manufacturerId);

    Integer addManufacturerFile(@Param("fileId") String fileId, @Param("manufacturerId") String manufacturerId);

    Integer deleteManufacturerFileByFileId(@Param("fileId") String fileId,@Param("manufacturerId") String manufacturerId);
}
