package cc.crrc.manage.mapper.mtr;

import cc.crrc.manage.pojo.mtr.WheelMeasurementDataPO;
import cc.crrc.manage.pojo.mtr.WheelMeasurementDataVO;
import cc.crrc.manage.pojo.mtr.WheelMeasurementPO;
import cc.crrc.manage.pojo.mtr.WheelMeasurementVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description 轮对测量
 * <AUTHOR> yong<PERSON>
 */
@Repository
public interface WheelMeasurementMapping {
    /**
     * @Description 查询测量车辆信息
     */
    List<WheelMeasurementVO> queryVehicle(WheelMeasurementPO wheelMeasurementPO);

    /**
     * @Description 查询测量数据
     */
    List<WheelMeasurementDataVO> queryData(@Param("id") String id);

    Long saveVehicle(WheelMeasurementPO wheelMeasurementPO);

    int saveData(WheelMeasurementDataPO wheelMeasurementDataPO);

    int deleteVehicle(@Param("id") String id);

    int updateVehicle(WheelMeasurementPO wheelMeasurementPO);

    int deleteData(@Param("id") String id);

}
