package cc.crrc.manage.mapper.mtr;

import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.mtr.ManufacturerEmployeePO;
import cc.crrc.manage.pojo.mtr.MtrVehicleTypeDetailVO;
import cc.crrc.manage.pojo.mtr.MtrVehicleTypePO;
import cc.crrc.manage.pojo.mtr.MtrVehicleTypeVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @FileName MtrVehicleTypeMapping
 * <AUTHOR> yuxi
 * @Date 2019/11/16 16:30
 * @Version 1.0
 **/
@Repository
public interface MtrVehicleTypeMapping {
    List<MtrVehicleTypeVO> listVehicleType();

    MtrVehicleTypeVO getVehicleTypeById(@Param("id") String id);

    MtrVehicleTypeDetailVO getVehicleTypeWithPictureById(@Param("id") String id,@Param("fileType") String fileType);

    int updateVehicleType(MtrVehicleTypePO vehicleTypePO);

    int deleteVehicleType(@Param("userId") String userId, @Param("id") String id);

    long insertVehicleType(MtrVehicleTypePO vehicleTypePO);

    Integer clearManufacturerFromVehicleType(@Param("manufacturerId") String manufacturerId, @Param("modifyBy") String modifyBy);

    List<Long> getVehicleTypeIdByEmployeeId(@Param("id") String id);

    Integer deleteVehicleTypeContactsByEmployeeId(@Param("id") String id);

    int insertVehicleTypeFileRelation(@Param("vehicleTypeId") String vehicleTypeId, @Param("fileId") String fileId);

    int deleteVehicleTypeFileRelation(@Param("vehicleTypeId") String vehicleTypeId, @Param("fileId") String fileId);

    List<SysFilePO> listVehicleTypeFile(@Param("vehicleTypeId") String vehicleTypeId);

    void deleteMtrVehicleTypeContacts(@Param("manufacturerEmployeeId") String manufacturerEmployeeId, @Param("vehicleTypeId") String vehicleTypeId);
	
	void insertMtrVehicleTypeContacts(@Param("manufacturerEmployeeId") String manufacturerEmployeeId, @Param("vehicleTypeId") String vehicleTypeId);

	List<ManufacturerEmployeePO> selectEmployees(@Param("vehicleTypeId") String vehicleTypeId);

	int validCount(@Param("manufacturerEmployeeId")String manufacturerEmployeeId, @Param("vehicleTypeId")String vehicleTypeId);

    String getVehicleTypeIdByVehicleCode(@Param("vehicleCode")String vehicleCode);
}
