package cc.crrc.manage.mapper.mtr;

import cc.crrc.manage.pojo.excel.software.MtrSoftWareForExcelVO;
import cc.crrc.manage.pojo.mtr.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 制造商管理
 *
 * <AUTHOR>
 * 2021/09/17
 **/
@Repository
public interface MtrSoftWareMapping {

    int insertSoftWareInfo(MtrSoftWarePO mtrSoftWarePO);

    int updateSoftWareForEndTime(MtrSoftWarePO updateSoftWare);

    List<MtrSoftWareVO> softWareInfoList(MtrSoftWareDTO mtrSoftWareDTO);

    List<MtrSoftWareForExcelVO> getVersionResumeForExcel(@Param("lineId") String lineId,
                                                         @Param("vehicleTypeId") String vehicleTypeId,
                                                         @Param("name") String name,
                                                         @Param("signalNameEn") String signalNameEn,
                                                         @Param("version") String version,
                                                         @Param("operator") String operator,
                                                         @Param("effectiveTimeStart") String effectiveTimeStart,
                                                         @Param("vehicleCode") String vehicleCode,
                                                         @Param("effectiveTimeEnd") String effectiveTimeEnd);

    int duplicationChecking(MtrSoftWarePO po);

    int countVersions(@Param("code") String code,
                      @Param("nameEn") String nameEn);

    int updateUpdaterInfo(@Param("id") String id,
                          @Param("updater") String updater,
                          @Param("userId") String userId);

    List<MtrSoftWareForExcelVO> listResume(@Param("code") String code,
                                           @Param("signalName") String signalName);

    List<MtrSoftWareResumeVO> listHistoryVersion(@Param("vehicleCode") String vehicleCode,
                                                 @Param("signalNameEn") String signalNameEn);

    List<MtrSoftwareVersionVO> ListAllSoftwareVersion();

    List<MtrSoftwareVersionVO> ListSoftwareVersionByLocation(@Param("vehicleCode") String vehicleCode,
                                                             @Param("subsystem") String subsystem,
                                                             @Param("softwareName") String softwareName);

    List<MtrSoftwareVersionVO> listVehicleHistoryVersion(@Param("vehicleCode") String vehicleCode,
                                                         @Param("subsystem") String subsystem,
                                                         @Param("softwareName") String softwareName,
                                                         @Param("location") String location);

    void insertSoftwareManualHistory(MtrSoftWarePO mtrSoftWarePO);

    List<MtrSoftwareVersionVO> listManualSoftware();

    void updateManualSoftware(@Param("id") String id,
                              @Param("status") int status);
}
