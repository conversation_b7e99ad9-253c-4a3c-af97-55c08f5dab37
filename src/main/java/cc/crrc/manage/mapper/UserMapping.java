package cc.crrc.manage.mapper;


import cc.crrc.manage.datascope.DataScope;
import cc.crrc.manage.pojo.User;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserMapping {

    User getUserInfoByName(User user);

    @DataScope(userIdColumn = "t1.create_by")
    List<User> getAll(User user);

    int insertUser(User user);


    User selectUserInfoById(@Param("userId") String userId);

    List<User> findAllRoleNameCode();
}