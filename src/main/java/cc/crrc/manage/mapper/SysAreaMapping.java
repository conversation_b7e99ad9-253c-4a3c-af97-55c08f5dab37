package cc.crrc.manage.mapper;

import cc.crrc.manage.pojo.SysAreaVO;
import cc.crrc.manage.pojo.SysLocationVO;
import cc.crrc.manage.pojo.mtr.MtrVehicleTypeVO;
import cc.crrc.manage.pojo.mtr.MtrVehicleVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SysAreaMapping {

    int saveSysArea(SysLocationVO sysLocationVO);

    // 根据中文名 统计个数
    int getCountByNameCn(@Param("nameCn") String nameCn);

    int deleteSysArea(SysLocationVO sysLocationVO);

    int updateSysArea(SysLocationVO sysLocationVO);

    List<SysLocationVO> getSysAreaList(SysLocationVO sysLocationVO);

    // 查询市级列表(带模糊查询)
    List<SysAreaVO> getCityListByAreaName(@Param("areaName") String areaName);

    List<SysLocationVO> getLocationList(@Param("userId") String userId);

    // List<LineDTO> getLineList(@Param("locationId") Long locationId);

    List<MtrVehicleTypeVO> getVehicleTypeList(@Param("lineId") String lineId);

    List<MtrVehicleVO> getVehicleList(@Param("lineId") String lineId, @Param("vehicleTypeId") String vehicleTypeId);
}
