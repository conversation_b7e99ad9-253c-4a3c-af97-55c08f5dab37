package cc.crrc.manage.mapper.draw;

import cc.crrc.manage.pojo.draw.DrawLineConfigDTO;
import cc.crrc.manage.pojo.draw.DrawLineConfigVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DrawLineConfigMapping {

    int addDrawLine(DrawLineConfigDTO drawLineConfigDTO);

    int updateDrawLine(DrawLineConfigDTO drawLineConfigDTO);

    DrawLineConfigVO getDrawLineInfoById(@Param("id") String id);

    List<DrawLineConfigVO> getDrawLineListForConfig(DrawLineConfigDTO drawLineConfigDTO);

    DrawLineConfigVO getDrawLineInfo(DrawLineConfigDTO drawLineConfigDTO);

    List<DrawLineConfigVO> getDrawLineInfoList(@Param("lineId")String lineId,@Param("type") String drawLineType);

    int batchInsert(@Param("list")List<DrawLineConfigDTO> drawLineList);
}
