package cc.crrc.manage.mapper;

import cc.crrc.manage.pojo.BusDict;
import cc.crrc.manage.pojo.BusDictQuery;
import cc.crrc.manage.pojo.BusDictVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BusDictMapping {
    /**
     * 查询业务字典
     *
     * @param typeCode 业务字典code
     * @param lineId
     * @return 业务字典
     */
    List<BusDictVO> selectBusDictByCode(@Param("typeCode") String typeCode, @Param("lineId") String lineId);

    /**
     * 查询业务字典列表
     *
     * @param busDictQuery 业务字典
     * @return 业务字典集合
     */
    List<BusDictVO> selectBusDictList(BusDictQuery busDictQuery);

    /**
     * 新增业务字典
     *
     * @param busDict 业务字典
     * @return 结果
     */
    int insertBusDict(BusDict busDict);

    /**
     * 修改业务字典
     *
     * @param busDict 业务字典
     * @return 结果
     */
    int updateBusDict(BusDict busDict);

    /**
     * 删除业务字典
     *
     * @param id 业务字典主键
     * @return 结果
     */
    int deleteBusDictById(@Param("id") String id, @Param("lastModifyBy") String lastModifyBy, @Param("lastModifyTime") String lastModifyTime);

    String getTypeLevel(@Param("typeCode") String typeCode);

    List<BusDictVO> listDictUniversal(BusDictQuery busDictQuery);

    @Select("select value from bus_dict where del_flag='0' and line_id=#{lineId} and type_code=#{typeCode}")
    List<String> queryValues(@Param("lineId") String lineId, @Param("typeCode") String typeCode);

    List<BusDictVO> listByType(@Param("typeCode") String typeCode);

    int deleteBusDictByTypeCode(@Param("typeCode") String typeCode);
}
