package cc.crrc.manage.mapper;

import cc.crrc.manage.pojo.SysMenuVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;
@Repository
public interface SysMenuMapping {

    int saveSysMenu(SysMenuVO sysMenuVO);

    int deleteSysMenuById(SysMenuVO sysMenuVO);

    int updateSysMenu(SysMenuVO sysMenuVO);

    List<SysMenuVO>selectSysMenuById(SysMenuVO sysMenuVO);

    List<SysMenuVO> selectSysMenuTreeList();

    List<SysMenuVO> selectSysMenuTreeListByRoleId(@Param("roleId") String roleId);

    SysMenuVO selectMenu(@Param("code") String code);
    
    /**
     * 
     * @Title: selectSysMenuTreeListByUsername   
     * @Description: 获取当前用户的Menu树状结构
     * @param: @param username
     * @param: @return      
     * @return: 数据结构的list集合
     * @date:   2020年8月18日 下午7:29:20  
     * @author: <PERSON><PERSON><PERSON><PERSON>n   
     * @throws
     */
    List<SysMenuVO> selectSysMenuTreeListByUsername(@Param("userId") String userId);
    
    /**
     * 
     * @Title: selectSysMenuTreeListByHomePage   
     * @Description: 根据前台传过来来的MenuId集合列出对应首页选择的树状图   
     * @param: @param MenuIds
     * @param: @return      
     * @return: List<SysMenuVO>
     * @date:   2020年8月29日 下午3:13:05  
     * @author: Heshenglun   
     * @throws
     */
    List<SysMenuVO> selectSysMenuTreeListByHomePage(@Param("MenuIds") List<String> MenuIds);


    SysMenuVO getParentMenuForAccess(@Param("id")String id);
}
