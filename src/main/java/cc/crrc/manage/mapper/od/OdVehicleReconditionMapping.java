package cc.crrc.manage.mapper.od;

import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import cc.crrc.manage.pojo.od.OdReconditionDecisionPO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface OdVehicleReconditionMapping {

    /**查询车辆运维决策List*/
    List<OdReconditionDecisionPO> findReconditionDecisions(OdReconditionDecisionPO reconditionDecisionPO);

    /**对决策进行操作,更新提交状态*/
    int actOnDecisions(OdReconditionDecisionPO reconditionDecisionPO);

    /**根据车辆编码查询决策列表数据*/
    List<OdReconditionDecisionPO> findDecisionByVehicleCode(@Param("vehicleCodes") List<MtrVehiclePO> vehicleCodes,
                                                             @Param("triggerTime") Date triggerTime);
}
