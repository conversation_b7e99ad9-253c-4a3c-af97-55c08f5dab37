package cc.crrc.manage.mapper.od;

import cc.crrc.manage.pojo.od.SchedulingPlanDecisionPO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @FileName SchedulingPlanDecisionMapping
 * @Description 调度计划决策dao层
 * <AUTHOR>
 * @Date 2020/7/29 17:21
 **/
@Repository
public interface SchedulingPlanDecisionMapping {
    /**1、查询调度计划决策列表*/
    List<SchedulingPlanDecisionPO> findSchedulingPlanPOList(@Param("pushStatus") String pushStatus);

    /**2、保存调度计划决策信息*/
    int saveSchedulingPlanPO(SchedulingPlanDecisionPO schedulingPlanDecisionPO);

    /**3、查询该时间段调度计划数量*/
    int findCountByStartAndEnd(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**4、查询该调度计划决策编码数量*/
    int findCountBySchedulingCode(@Param("schedulingCode") String schedulingCode);

    /**5、删除多个行车调度计划决策数据*/
    int deleteSchedulingPlanPO(@Param("ids") String[] ids);

    /**6、编辑行车调度计划决策和推送状态修改*/
    int updeteSchedulingPlanPO(SchedulingPlanDecisionPO schedulingPlanDecisionPO);

    /**7、根据id查询编辑数据的原始数据*/
    SchedulingPlanDecisionPO findSchedulingPlanById(String id);

    /**8、线路监控推送数据列表查询 推送状态为1*/
    List<SchedulingPlanDecisionPO> findDecisionByLineId(@Param("lineId") Long lineId);
}
