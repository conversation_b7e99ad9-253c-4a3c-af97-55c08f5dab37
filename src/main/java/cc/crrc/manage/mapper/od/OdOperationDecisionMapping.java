package cc.crrc.manage.mapper.od;

import cc.crrc.manage.common.annotation.ParamReplace;
import cc.crrc.manage.pojo.od.OdOperationDecisionPO;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @FileName OdOperationDecisionMapping
 * @Description 运维决策dao
 * <AUTHOR> yuxi
 * @Date 2020/5/7 15:05
 **/
@Repository
public interface OdOperationDecisionMapping {

    @ParamReplace(param = {"schedulingCode","decisionSubstance","recommendDecision"},type = OdOperationDecisionPO.class)
    List<OdOperationDecisionPO> listOperationDecision(OdOperationDecisionPO operationDecision);

    /**修改决策推送时间和状态 zhangzhijian 2020-08-16*/
    int UpdateReconditionStatus(OdOperationDecisionPO operationDecision);

}
