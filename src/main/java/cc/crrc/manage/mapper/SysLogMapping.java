package cc.crrc.manage.mapper;

import cc.crrc.manage.datascope.DataScope;
import cc.crrc.manage.pojo.SysLogDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface SysLogMapping {

    int insertLog(SysLogDTO sysLogDTO);

    SysLogDTO getMenuByPath(@Param("path") String path);

    @DataScope(userIdColumn = "user_id")
    List<SysLogDTO> getLogs(SysLogDTO log);

    @DataScope(userIdColumn = "user_id")
    Map<String, Integer> getOptCount(SysLogDTO log);

    @DataScope(userIdColumn = "l.user_id")
    List<SysLogDTO> getMenuCount(@Param("startTime") String startTime, @Param("endTime") String endTime);

    @DataScope(userIdColumn = "user_id")
    Map<String, Object> getAccessCount(@Param("startTime") String startTime);

    @DataScope(userIdColumn = "user_id")
    List<SysLogDTO> getAgent(@Param("startTime") String startTime, @Param("endTime") String endTime);

    @DataScope(userIdColumn = "user_id")
    List<SysLogDTO> getVisitors(SysLogDTO log);

}
