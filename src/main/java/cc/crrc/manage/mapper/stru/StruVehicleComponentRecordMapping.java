package cc.crrc.manage.mapper.stru;

import cc.crrc.manage.pojo.stru.StruVehicleComponentRecordExcelVO;
import cc.crrc.manage.pojo.stru.StruVehicleComponentRecordVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * 2021/1/14
 **/
@Repository
public interface StruVehicleComponentRecordMapping {

    /*2021-01-18 Xukangjian 车辆履历模板下载*/
    List<StruVehicleComponentRecordExcelVO> getVehicleComponentRecordForExcel(@Param("vehicleId") String vehicleId);

    List<StruVehicleComponentRecordVO> getVehicleComponentRecordList(@Param("vehicleId") String vehicleId,
                                                                     @Param("structureCode") String structureCode);

    int batchAddRecord(@Param("voList") List<StruVehicleComponentRecordExcelVO> struVehicleComponentRecordExcelVOList,
                       @Param("createBy") String createBy);

}
