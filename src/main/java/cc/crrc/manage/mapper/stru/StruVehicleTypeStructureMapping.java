package cc.crrc.manage.mapper.stru;

import cc.crrc.manage.common.annotation.ParamReplace;
import cc.crrc.manage.pojo.excel.StruVehicleTypeStructureForExcelPO;
import cc.crrc.manage.pojo.mtr.StruVehicleStructureComponentPO;
import cc.crrc.manage.pojo.stru.StruVehicleTypeStructurePO;
import cc.crrc.manage.pojo.stru.StruVehicleTypeStructureTreeVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * @FileName StruVehicleTypeStructureMapping
 * <AUTHOR> yuxi
 * @Date 2019/11/18 9:07
 * @Version 1.0
 **/
@Repository
public interface StruVehicleTypeStructureMapping {

    int addStruVehicleTypeStructure(StruVehicleTypeStructurePO struVehicleTypeStructurePO);

    int deleteStruVehicleTypeStructure(@Param("id") Long id, @Param("modifyBy") Long modifyBy);

    StruVehicleTypeStructurePO getStruVehicleTypeStructureById(@Param("id") String id);

    List<StruVehicleTypeStructurePO> getStructureByStructureCode(@Param("structureCode") String structureCode, @Param("vehicleTypeId") Long vehicleTypeId);

    List<StruVehicleTypeStructurePO> getSubStructureById(@Param("id") Long id);

    Map<String,String> getVehicleTypeRootStructureCode(@Param("vehicleTypeId") String vehicleTypeId);

    int updateStruVehicleTypeStructure(StruVehicleTypeStructurePO struVehicleTypeStructurePO);

    List<StruVehicleTypeStructureTreeVO> selectStructureTreeList(@Param("vehicleTypeId") String vehicleTypeId);

    @ParamReplace(param = "nameCn")
    List<String> listUniqueFlag(@Param("vehicleTypeId") String vehicleTypeId, @Param("nameCn") String nameCn);

    int updateChildrenStructureCode(@Param("structureCodeOrigin") String structureCodeOrigin,
                                    @Param("structureCodeNew") String structureCodeNew,
                                    @Param("vehicleTypeId") String vehicleTypeId);

    StruVehicleTypeStructurePO getParentNodeById(@Param("id") Long id);

    List<StruVehicleTypeStructurePO> getLocationByVehicleTypeId(@Param("vehicleTypeId") String vehicleTypeId);

    List<String> getAllSystemByVehicleTypeId(@Param("vehicleTypeId") Long vehicleTypeId);

    /*2020-3-18 基本构型 车型构型 查询*/
    List<StruVehicleStructureComponentPO> getVehicleTypeStructureCode(@Param("vehicleTypeId") String vehicleTypeId,
                                                                      @Param("vehicleId") String vehicleId,
                                                                      @Param("currentId") String currentId);

    List<String> getDiffStructureListByStructureCode(@Param("vehicleId") String vehicleId,
                                                     @Param("structureCode") String structureCode);

    StruVehicleTypeStructureTreeVO getStructureTreeByStructureCode(@Param("vehicleId") String vehicleId,
                                                                @Param("structureCode") String structureCode);

    List<StruVehicleTypeStructureTreeVO> getAllowStructureTree(@Param("vehicleId") String vehicleId,
                                                               @Param("structureCodeList") List<String> structureCodeList);

    StruVehicleStructureComponentPO getStruVehicleStructureById(@Param("id")String id, @Param("vehicleId")String vehicleId, @Param("currentId")String currentId);

    /*2020-04-20 批量导入接口 forExcel lx*/
    int addStruVehicleTypeStructureList(@Param("list")List<StruVehicleTypeStructureForExcelPO> list);
    /*2020-04-20 下载模板接口 forExcel lx*/
    List<StruVehicleTypeStructureForExcelPO> getStruVehicleTypeStructureForExcel(@Param("vehicleTypeId") String vehicleTypeId,@Param("structureCode") String structureCode);

    /*2020-04-28 提供前端用车辆code查车厢*/
    List<String> getLocationByVehicleCode(@Param("vehicleCode")String vehicleCode);
    /*2020-05-07 提供前端用车辆code查车厢*/
    List<StruVehicleTypeStructureForExcelPO> getParentNodeByStructureCode(@Param("vehicleTypeId") String vehicleTypeId, @Param("structureCode")String structureCode);
    //2020年6月12日  房明宽  保存车辆构型的文件关系（fbx_file_id）
    int saveVehicleTypeStructureFileRelation(@Param("fbxFileId") String fbxFileId,@Param("id")String id);
    //2020年6月12日  房明宽  删除车辆构型的文件关系（fbx_file_id 改成 null）
    int deleteVehicleTypeStructureFileRelation(@Param("id")String id);
    //2020年6月22日    fangmingkuan    更新车辆构型的文件关系（fbx_file_id）
    int updateVehicleTypeStructureFileRelation(@Param("fbxFileId") String fbxFileId,@Param("id")String id);
}
