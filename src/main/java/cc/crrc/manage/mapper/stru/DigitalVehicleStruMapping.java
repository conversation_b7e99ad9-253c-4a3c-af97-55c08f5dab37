package cc.crrc.manage.mapper.stru;

import cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO;
import cc.crrc.manage.pojo.monitor.RelayContactorLifePO;
import cc.crrc.manage.pojo.monitor.RelayContactorPO;
import cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO;
import cc.crrc.manage.pojo.mtc.MtcAutoFaultRecordVO;
import cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRuleVO;
import cc.crrc.manage.pojo.mtr.MtrSoftWareVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

@Repository
public interface DigitalVehicleStruMapping {

    List<MtcMechanismAlarmRuleVO> mechanismAlarmRuleListByStruCode(@Param("vehicleCode") String vehicleCode,@Param("structureCode") String structureCode);

    List<EkbFaultTypeDTO> listEkbFaultType(@Param("vehicleCode") String vehicleCode,@Param("structureCode") String structureCode);

    String struCodeInfoByThreedCode(@Param("threedCode")String threedCode,@Param("vehicleCode")String vehicleCode);

    List<MtrSoftWareVO> softWareInfoListForStru(@Param("vehicleCode") String vehicleCode,@Param("structureCode") String structureCode);

    List<RelayContactorPO> relayContactorLifeListByStruCode(@Param("vehicleCode") String vehicleCode,@Param("structureCode") String structureCode);

    List<MtcMechanismAlarmRuleVO> mechanismAlarmRuleListByVehicleCode(@Param("vehicleCode")String vehicleCode);

    List<RelayContactorPO> componentListByStruCode(@Param("vehicleCode") String vehicleCode,@Param("structureCode") String structureCode);

    List<MtcAutoFaultRecordVO> listMtcAutoFaultRecordForStru(MtcAlarmWarningVO mtcAlarmWarningVO);

    List<MtcAutoFaultRecordVO> listMtcAutoFaultRecordForStruByFaulitTypeKey(MtcAlarmWarningVO mtcAlarmWarningVO);

    List<MtrSoftWareVO> mtrSoftWareListByStruCodeLite(@Param("vehicleCode") String vehicleCode,@Param("structureCode") String structureCode);

    List<MtrSoftWareVO> mtrSoftWareListByStruCodeAndSignalName(@Param("vehicleCode") String vehicleCode,@Param("structureCode") String structureCode, @Param("signalNameEn")String signalNameEn);

    List<MtcAutoFaultRecordVO> autoFaultGroupListByVehicleCodeAndThreedCode(@Param("vehicleCode") String vehicleCode,@Param("threedCode") String threedCode);

    List<HashMap<String,Object>> autoFaultGroupCountNumByVehicleCode(@Param("vehicleCode") String vehicleCode);

    List<HashMap<String, Object>> componentGroupListByVehicleCode(@Param("vehicleCode")  String vehicleCode);

    List<RelayContactorPO> componentListGroupListByVehicleCodeAndThreedCode(@Param("vehicleCode") String vehicleCode,@Param("threedCode") String threedCode);

    List<HashMap<String, Object>> relayContactorLifeGroupListByVehicleCode(@Param("vehicleCode") String vehicleCode);

    List<RelayContactorPO> relayContactorLifeGroupListByVehicleCodeAndThreedCode(@Param("vehicleCode") String vehicleCode, @Param("threedCode") String threedCode);

    List<HashMap<String, Object>> mechanismAlarmRuleGroupCount(@Param("vehicleCode") String vehicleCode);

    List<MtcMechanismAlarmRuleVO> mechanismAlarmRuleListByVehicleCodeAndThreedCode(@Param("vehicleCode") String vehicleCode,@Param("threedCode") String threedCode);
}
