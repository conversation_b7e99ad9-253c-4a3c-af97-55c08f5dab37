package cc.crrc.manage.mapper;

import cc.crrc.manage.pojo.SysDictDTO;
import cc.crrc.manage.pojo.SysDictVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @FileName SysDictMapping
 * <AUTHOR> yuxi
 * @Date 2019/6/14 15:05
 * @Version 1.0
 **/
@Component
public interface SysDictMapping {
    List<SysDictVO> listDict(SysDictVO dictVO);

    int addDict(SysDictDTO dictDTO);

    int updateDict(SysDictDTO dictDTO);

    int getCountByValue_typeId(@Param("id") String id, @Param("value") String value, @Param("typeId") String typeId);

    List<SysDictVO> listByTypeId(@Param("typeId") String typeId);

    List<SysDictVO> listByType(@Param("typeCode") String typeCode);

    String getLabel(@Param("value") String value , @Param("typeId") int typeId);

    List<SysDictVO> listDictUniversal(SysDictDTO sysDictDTO);
}
