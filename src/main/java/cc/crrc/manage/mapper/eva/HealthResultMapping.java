package cc.crrc.manage.mapper.eva;

import cc.crrc.manage.pojo.eva.EvaHealthResultPO;
import cc.crrc.manage.pojo.excel.healthyScore.HealthyScoreDTO;
import cc.crrc.manage.pojo.excel.healthyScore.LineHealthyScoreDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * 2020/02/17
 **/
public interface HealthResultMapping {

    List<EvaHealthResultPO> getScopeHistoryByVehicleCode(@Param("vehicleCode") String vehicleCode,@Param("startTime")String startTime, @Param("endTime")String endTime);

    EvaHealthResultPO getLatestScopeByVehicleCode(@Param("vehicleCode") String vehicleCode);

    List<EvaHealthResultPO> getSystemScopeByVehicleCodeAndSysName(@Param("vehicleCode") String vehicleCode, @Param("sysName") String sysName
            ,@Param("startTime")String startTime, @Param("endTime")String endTime);

    List<EvaHealthResultPO> getScopeHistoryByLineId();

    List<EvaHealthResultPO> getScopeHistoryByLine(@Param("startTime")String startTime, @Param("endTime")String endTime,
                                                  @Param("lineId") String lineId);

    List<HealthyScoreDTO> trainScoreList(@Param("startTime")String startTime, @Param("endTime")String endTime,
                                         @Param("lineId") String lineId, @Param("vehicleCode")String vehicleCode);

    List<LineHealthyScoreDTO> lineScoreList(@Param("startTime")String startTime, @Param("endTime")String endTime,
                                            @Param("lineId") String lineId);
}
