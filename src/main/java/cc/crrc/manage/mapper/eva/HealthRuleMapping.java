package cc.crrc.manage.mapper.eva;

import cc.crrc.manage.pojo.eva.EvaHealthRuleDTO;
import cc.crrc.manage.pojo.eva.EvaHealthRulePO;
import io.lettuce.core.dynamic.annotation.Param;

import java.util.List;

/**
 * <AUTHOR>
 * 2020/02/20
 **/
public interface HealthRuleMapping {

    List<EvaHealthRulePO> listHealthRule(EvaHealthRuleDTO evaHealthRuleDTO);

    Integer addHealthRule(EvaHealthRulePO evaHealthRulePO);

    Integer updateHealthRule(EvaHealthRulePO evaHealthRulePO);

    Integer deleteHealthRuleById(@Param("id") Long id);

}
