package cc.crrc.manage.mapper.eva;

import cc.crrc.manage.pojo.component.OperatingParameterDTO;
import cc.crrc.manage.pojo.component.RamsParameterDTO;
import cc.crrc.manage.pojo.eva.RamsLineVehicleTypeVO;
import cc.crrc.manage.pojo.eva.RamsLineVo;
import cc.crrc.manage.pojo.line.LineDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName RamsMapping
 * @Description TODO
 * <AUTHOR> <PERSON>
 * @Date 2020/7/8 10:23
 * @Version 1.0
 **/
@Component
public interface RamsMapping {
    List<LineDTO> getLineDTOList();

    RamsLineVo getDrivingDistanceAndTime(String lineId);

    List<RamsLineVehicleTypeVO> getRamsVehicleTypeVOs(String lineId);

    List<RamsParameterDTO> getItems(String lineId);
    //暂时不用
    List<RamsLineVo> getRamsLineList();

    //根据车型ID统计相某车型的数量
    int count(String id);

    //查找同款车型里程数、里程时间
    List<OperatingParameterDTO> getParamList(String id);

    List<RamsParameterDTO> getRamsParamList(@Param("comTypeId") String comTypeId, @Param("vehicleTypeId") String vehicleTypeId);

    List<Object> selectRams(@Param("nameCn") String nameCn, @Param("productNumber") String productNumber);
}
