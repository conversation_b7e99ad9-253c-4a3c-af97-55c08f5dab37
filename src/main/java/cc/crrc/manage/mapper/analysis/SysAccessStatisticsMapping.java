package cc.crrc.manage.mapper.analysis;

import cc.crrc.manage.pojo.SysLogDTO;
import cc.crrc.manage.pojo.analysis.SysAccessStatisticsPO;
import cc.crrc.manage.pojo.analysis.SysAccessStatisticsVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
public interface SysAccessStatisticsMapping {
    int addAccessStatistics(SysAccessStatisticsPO sysAccessStatisticsPO);

    List<SysAccessStatisticsVO> getVisitors(SysAccessStatisticsVO vo);

    List<SysAccessStatisticsVO> getAgent(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("userId")String userId);

    List<Map<String, Object>> getCountByDate(@Param("startTime")String startTime, @Param("endTime")String endTime, @Param("userId")String userId);

    List<Map<String, Object>> getParentMenuCount(@Param("startTime")String startTime, @Param("endTime")String endTime, @Param("userId")String userId);

    List<Map<String, Object>> getMenuCountByDate(@Param("parentMenuCode")String parentMenuCode, @Param("startTime")String startTime,
                                                 @Param("endTime")String endTime, @Param("userId")String userId);
}
