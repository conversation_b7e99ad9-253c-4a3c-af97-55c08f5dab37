package cc.crrc.manage.mapper.analysis;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cc.crrc.manage.pojo.analysis.AnalysisParamPO;
import cc.crrc.manage.pojo.analysis.AnalysisParamVO;
import cc.crrc.manage.pojo.analysis.CollectionReportByDayDTO;
import cc.crrc.manage.pojo.excel.*;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface AnalysisMapping {

    List<AnalysisParamPO> getEnergy(AnalysisParamVO param);

    List<EnergyForExcelPO> getEnergyForExcel(AnalysisParamVO param);

    List<MtrVehiclePO> listVehicleorLine(String name);

    List<MtrVehiclePO> listVehicleor(String vehicleId);

    List<Map<String, String>> mileageTrend(AnalysisParamVO param);

    List<MileageTrendForExcelPO> mileageTrendForExcel(AnalysisParamVO param);

    List<Map<String, String>> totalEnergyTrend(AnalysisParamVO param);

    List<TotalEnergyTrendForExcelPO> totalEnergyTrendForExcel(AnalysisParamVO param);

    List<AnalysisParamPO> traConTrend(AnalysisParamVO param);

    List<AnalysisParamForExcelPO> traConTrendForExcel(@Param("vehicleCode") String vehicleCode, @Param("startTime") String startTime, @Param("endTime") String endTime);

    List<Map<String, String>> totalRunTime(@Param("lineId") String lineId);

    List<Map<String, String>> totalRunDistance(@Param("lineId") String lineId);

    List<Map<String, String>> totalEngyByVehicle(@Param("lineId") String lineId);

    List<Map<String, String>> lineMileageTrend(@Param("lineId") String lineId);

    List<CollectionReportByDayDTO> collectionReportCount(AnalysisParamVO param);

    List<Map<String, String>> collectionReportVehicleCount(AnalysisParamVO param);

    /**
     * @throws
     * @Title: getTotolEnergy
     * @Description: 全网运行统计-根据线路获取所有信息
     * @param: [startTime, endTime, lineId]
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     * @date: 2020/11/6 9:16
     * @author: Heshenglun
     */
    List<AnalysisParamPO> getTotolEnergy(@Param("startTime") String startTime,
                                         @Param("endTime") String endTime,
                                         @Param("lineId") String lineId);

    List<Map<String, Object>> lineAllRunExcel(@Param("startTime") String startTime,
                                              @Param("endTime") String endTime,
                                              @Param("lineId") String lineId);

    /**
     * @throws
     * @Title: getTotalEnergyPic
     * @Description: 全网运行统计-根据线路获取所有信息（用于饼图）
     * @param: [startTime, endTime, lineId]
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     * @date: 2020/11/6 11:21
     * @author: Heshenglun
     */
    List<Map<String, String>> getTotalEnergyPic(@Param("startTime") String startTime,
                                                @Param("endTime") String endTime,
                                                @Param("lineId") String lineId);

    List<EnergyPicForExcelPO> totalEnergyPic(@Param("startTime") String startTime,
                                             @Param("endTime") String endTime,
                                             @Param("lineId") String lineId);

    /**
     * @throws
     * @Title: getLineAll
     * @Description: 全网运行统计-根据线路获取各个信息(时间、里程、能耗)
     * @param: [startTime, endTime, lineId]
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     * @date: 2020/11/6 9:16
     * @author: Heshenglun
     */
    List<Map<String, String>> getLineAll(@Param("startTime") String startTime,
                                         @Param("endTime") String endTime,
                                         @Param("lineId") String lineId);

    List<LineAllExcelPO> LineAllForExcel(@Param("startTime") String startTime,
                                         @Param("endTime") String endTime,
                                         @Param("lineId") String lineId);

    List<LineDistanceForExcel> lineDistanceForExcel(@Param("startTime") String startTime,
                                                    @Param("endTime") String endTime,
                                                    @Param("lineId") String lineId);

    /**
     * @throws
     * @Title: getFaultByLocation
     * @Description: 车辆故障统计-根据车厢查询
     * @param: [startTime, endTime, vehicleId]
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     * @date: 2020/11/6 15:37
     * @author: Heshenglun
     */
    List<Map<String, String>> getFaultByLocation(@Param("startTime") String startTime,
                                                 @Param("endTime") String endTime,
                                                 @Param("vehicleId") String vehicleId);

    List<FaultByLocationForExcelPO> FaultByLocation(@Param("startTime") String startTime,
                                                    @Param("endTime") String endTime,
                                                    @Param("vehicleId") String vehicleId);

    /**
     * @throws
     * @Title: getFaultBySubsystem
     * @Description: 车辆故障统计-根据子系统查询
     * @param: [startTime, endTime, vehicleId]
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     * @date: 2020/11/6 15:40
     * @author: Heshenglun
     */
    List<Map<String, String>> getFaultBySubsystem(@Param("startTime") String startTime,
                                                  @Param("endTime") String endTime,
                                                  @Param("vehicleId") String vehicleId);

    /**
     * @throws
     * @Title: getFaultByLevel
     * @Description: 车辆故障统计-根据故障等级
     * @param: [startTime, endTime, vehicleTypeId]
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     * @date: 2020/11/6 15:42
     * @author: Heshenglun
     */
    List<Map<String, String>> getFaultByLevel(@Param("startTime") String startTime,
                                              @Param("endTime") String endTime,
                                              @Param("vehicleId") String vehicleId);

    /**
     * @throws
     * @Title: getFaultByDate
     * @Description: 车辆故障统计-根据日期分组
     * @param: [startTime, endTime, vehicleTypeId]
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     * @date: 2020/11/6 15:43
     * @author: Heshenglun
     */
    List<Map<String, String>> getFaultByDate(@Param("startTime") String startTime,
                                             @Param("endTime") String endTime,
                                             @Param("vehicleId") String vehicleId);

    List<FaultByDateForExcelPO> FaultByDate(@Param("startTime") String startTime,
                                            @Param("endTime") String endTime,
                                            @Param("vehicleId") String vehicleId);

    /**
     * @throws
     * @Title: getVehicleFaultByLevel
     * @Description: 车辆故障统计-根据故障等级&车辆查询
     * @param: [startTime, endTime, vehicleId]
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     * @date: 2020/11/6 15:44
     * @author: Heshenglun
     */
    List<Map<String, String>> getSystemFaultByLevel(@Param("startTime") String startTime,
                                                    @Param("endTime") String endTime,
                                                    @Param("vehicleId") String vehicleId);

    /**
     * @param vehicleCode
     * @param startTime
     * @param endTime
     * @return
     * @Description: 车辆运行统计-获取车辆各月里程数
     */
    List<HashMap<String, String>> totleMileageTrendExcel(@Param("vehicleCode") String vehicleCode,
                                                         @Param("startTime") String startTime,
                                                         @Param("endTime") String endTime);

    /**
     * @param vehicleCode
     * @param startTime
     * @param endTime
     * @return
     * @Description: 车辆运行统计-获取车辆各月总能耗
     */
    List<HashMap<String, String>> totleEnergyTrendExcel(@Param("vehicleCode") String vehicleCode,
                                                        @Param("startTime") String startTime,
                                                        @Param("endTime") String endTime);

    Integer getTotalDistance(@Param("paramName") String paramName, @Param("vehicleCode") String vehicleCode);

    List<HashMap<String, String>> getDistancesAndEnergyByV(@Param("signalNameEn") String signalNameEn);

    List<HashMap<String, Object>> getVehicleHistoryData(@Param("traCode") String traCode, @Param("now") String now);
}
