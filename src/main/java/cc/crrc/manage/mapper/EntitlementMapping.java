package cc.crrc.manage.mapper;


import cc.crrc.manage.pojo.Element;
import cc.crrc.manage.pojo.Menu;
import cc.crrc.manage.pojo.SysRoleVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EntitlementMapping {


    List<Menu> getAllMenu();

    List<Menu> getMenuByUserId(@Param("userId") String userId);

    List<Element> getAllElement();

    List<Element> getAllElementByUserId(@Param("userId") String userId);

    List<Menu> findMenuByHomeMenuId(@Param("homeMenuId") String homeMenuId);

    List<SysRoleVO> findRoleIdByUserId(@Param("userId") String userId);
}