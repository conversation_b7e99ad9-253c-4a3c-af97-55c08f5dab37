package cc.crrc.manage.mapper;

import cc.crrc.manage.pojo.SysRoleTypeVO;

import java.util.List;

public interface SysRoleTypeMapping {
    /*查询角色分组是否存在*/
    List<SysRoleTypeVO> getSysRoleType(SysRoleTypeVO SysRoleTypeVO);
    /*保存角色分类信息*/
    void saveSysRoleType(SysRoleTypeVO SysRoleTypeVO);
    /*删除角色分类信息*/
    int deleteSysRoleTypeById(SysRoleTypeVO SysRoleTypeVO);

    int updateSysRoleTypeById(SysRoleTypeVO SysRoleTypeVO);

    List<SysRoleTypeVO> selectSysRoleType(SysRoleT<PERSON><PERSON> SysRoleTypeVO);

    SysRoleTypeVO selectSysRoleTypeById(SysRoleTypeVO SysRoleTypeVO);
}
