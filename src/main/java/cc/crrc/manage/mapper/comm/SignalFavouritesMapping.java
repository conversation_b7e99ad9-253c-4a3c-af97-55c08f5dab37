package cc.crrc.manage.mapper.comm;

import cc.crrc.manage.pojo.comm.signalfavourites.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @version 1.0
 * @Descrition ：信号收藏功能 Mapping
 * @FileName SignalFavouritesMapping
 * <AUTHOR>
 * @Date 2021-8-6
 **/
@Component
public interface SignalFavouritesMapping {
    /**
     * @return
     * @desc ：查询当前用户的信号收藏列表
     */
    List<SignalFavouritesGroupVO> signalFavouritesGroupList(@Param("createBy") String createBy);

    /**
     * @return
     * @desc ：根据收藏id获取收藏的信号列表
     */
    List<SignalsFavouritesVO> signalsFavouritesList(@Param("groupId") String groupId);

    /**
     * 保存收藏组及信号
     *
     * @param po
     */
    int saveSignalFavouritesGroup(SignalFavouritesGroupPO po);

    /**
     * 保存收藏组及信号
     *
     * @param list
     */
    void saveSignalsFavourites(List<SignalsFavouritesPO> list);

    /**
     * 删除信号收藏组
     *
     * @param id
     * @return
     */
    int deleteSignalFavouritesGroup(@Param("id") String id);

    /**
     * 删除收藏组信号
     *
     * @param id
     * @return
     */
    int deleteSignalsFavourites(@Param("id") String id);

    /**
     * @return
     * @desc ：校验信号收藏名称是否存在
     */
    Integer signalsFavouritesGroupNameIsExists(@Param("groupName") String groupName, @Param("createBy") String createBy);
}
