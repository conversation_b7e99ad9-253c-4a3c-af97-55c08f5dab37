package cc.crrc.manage.mapper.comm;

import cc.crrc.manage.pojo.comm.signal.CommSignalIntervalPO;
import cc.crrc.manage.pojo.comm.signal.CommSignalVO;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 2020/3/3
 **/
public interface CommOriginalSignalMapping {
    List<CommSignalVO> listOriginalSignalByVehicleTypeId(@Param("vehicleTypeId") Long vehicleTypeId, @Param("location") String location, @Param("subsystem") String subsystem);

    ArrayList<CommSignalIntervalPO> getSignalInterval(@Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("vehicleCode") String vehicleCode);
}
