package cc.crrc.manage.mapper.comm;

import cc.crrc.manage.pojo.comm.protocol.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @FileName ProtocolMapping
 * <AUTHOR> shuangquan
 * @Date 2019/11/12 13:09
 **/
public interface ProtocolMapping {
    List<ProtocolVO> list(@Param("vehicleTypeId") String vehicleTypeId);

    // 2020年8月14日 房明宽 获取协议列表 相比之前 查询条件新增 线路ID
    List<ProtocolVO> getProtocolList(@Param("vehicleTypeId") String vehicleTypeId, @Param("lineId") String lineId);

    void saveProtocol(ProtocolPO protocolPO);

    ProtocolVO queryDetail(@Param("id") String id);

    void insertTcpProtocolPacketRelation(@Param("vehicleTypeId") String vehicleTypeId,
                                         @Param("protocolId") String protocolId,
                                         @Param("createBy") String createBy);

    int deleteProtocol(ProtocolDelDTO protocolDelDTO);

    int queryProtocolByTcp(@Param("protocolId") String protocolId);

    int queryProtocolByMqtt(@Param("protocolId") String protocolId);

    void updateStatus(ProtocolStatusDTO protocolStatusDTO);

    void upgradeProtocol(ProtocolUpgradeDTO protocolUpgradeDTO);

    List<ProtocolMatchVO> queryMatchVehicle(@Param("protocolId") String protocolId,
                                            @Param("vehicleTypeId") String vehicleTypeId);

    void invalidVehicleProtocol(@Param("protocolId") String protocolId,
                                @Param("invaidVehicleList") List<ProtocolMatchVO> invaidVehicleList,
                                @Param("modifyBy") String modifyBy);

    void matchTcpVehicle(@Param("protocolId") String protocolId,
                         @Param("matchList") List<ProtocolMatchVO> matchList,
                         @Param("createBy") String createBy);

    void matchMqttVehicle(@Param("protocolId") String protocolId, @Param("packetTypeId") String packetTypeId,
                          @Param("matchList") List<ProtocolMatchVO> matchList,
                          @Param("createBy") String createBy);

    void updateInvaidByPacket(@Param("protocolId") String protocolId,
                              @Param("matchList") List<ProtocolMatchVO> matchList,
                              @Param("modifyBy") String modifyBy);

    void insertMQTTTopic(@Param("vehicleId") String vehicleId, @Param("topicTypeId") String topicTypeId, @Param("createBy") String createBy);

    List<ProtocolVO> getLatestProtocolByVehicleType(@Param("vehicleTypeId") String vehicleTypeId);

    List<ProtocolVO> getValidProtocolByVehicleId(@Param("vehicleId") String vehicleId,@Param("commType") String commType);

    int selectEnableProNum(@Param("protocolId") String protocolId);
}
