package cc.crrc.manage.mapper.comm;

import cc.crrc.manage.pojo.comm.signal.SignalVO;
import cc.crrc.manage.pojo.comm.signalgroup.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @FileName SignalGroupMapping
 * @<PERSON> <PERSON>hua<PERSON>quan
 * @Date 2019/11/19 13:17
 **/
public interface SignalGroupMapping {
    List<SignalGroupVO> list(@Param("lineId") String lineId,@Param("vehicleTypeId") String vehicleTypeId, @Param("name") String name);

    void saveSignalGroup(SignalGroupPO signalGroupPO);

    void updateSignalGroup(SignalGroupPO signalGroupPO);

    int deleteSignalGroup(@Param("id") String id);

    void deleteSignalGroupOriginal(@Param("groupId") String groupId);

    void deleteSignalGroupExtend(@Param("groupId") String groupId);

    List<SignalVO> listSignal(SignalGroupQueryDTO signalGroupQueryDTO);

    List<SignalVO> listAllSignal(SignalGroupQueryDTO signalGroupQueryDTO);

    void saveSignalGroupRelation(SignalGroupRelationPO signalGroupRelationPO);

    void deleteRelation(SignalGroupDelRelationPO signalGroupDelRelationPO);

    List<SignalGroupFaultVO> listFault(SignalGroupFaultQueryDTO signalGroupFaultQueryDTO);

    void insert(@Param("signalGroupId")String signalGroupId, @Param("list") List<String> list);

    void deleteRelatedBeforeInsert(String signalGroupId);

    List<SignalGroupFaultVO> listRelated(String signalGroupId);

}
