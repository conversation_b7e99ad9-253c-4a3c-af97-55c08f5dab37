package cc.crrc.manage.mapper.comm;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cc.crrc.manage.pojo.comm.protocolTypeConfig.MqttTopicTypeDTO;
import cc.crrc.manage.pojo.comm.protocolTypeConfig.TcpPacketTypeDTO;

public interface CommTypeConfigMapping {

	List<TcpPacketTypeDTO> getTcpPacketType(@Param("vehicleTypeId")String vehicleTypeId);

	List<MqttTopicTypeDTO> getMqttTopicType(@Param("vehicleTypeId")String vehicleTypeId);

	void addTcpPacketType(TcpPacketTypeDTO tcpDTO);

	void addMqttTopicType(MqttTopicTypeDTO mqttDTO);

	void updateTcpPacketType(TcpPacketTypeDTO tcpDTO);

	void updateMqttTopicType(MqttTopicTypeDTO mqttDTO);

	void delTcpPacketType(@Param("id")String id);

	void delMqttTopicType(@Param("id")String id);

}
