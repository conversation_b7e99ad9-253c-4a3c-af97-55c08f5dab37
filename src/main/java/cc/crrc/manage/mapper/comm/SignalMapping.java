package cc.crrc.manage.mapper.comm;

import cc.crrc.manage.common.annotation.ParamReplace;
import cc.crrc.manage.monitoringConfig.entity.MonitorConfigWtdSignalPO;
import cc.crrc.manage.pojo.comm.signal.SignalPO;
import cc.crrc.manage.pojo.comm.signal.SignalQueryDTO;
import cc.crrc.manage.pojo.comm.signal.SignalTreeVO;
import cc.crrc.manage.pojo.comm.signal.SignalVO;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @FileName SignalMapping
 * <AUTHOR> shuangquan
 * @Date 2019/11/13 14:01
 **/
public interface SignalMapping {
    List<SignalVO> list(SignalQueryDTO signalQueryDTO);

    void saveOriginalSignal(SignalPO signalPO);

    void saveExtendSignal(SignalPO signalPO);

    int updateSignal(SignalPO signalPO);

    int queryDuplicate(SignalPO signalPO);

    int deleteSignal(@Param("ids") String[] ids, @Param("signalType") int signalType);

    void copyOriginalSignal(@Param("newProtocolId") long newProtocolId, @Param("oldProtocolId") long oldProtocolId,
                            @Param("createBy") long createBy);

    void copyExtendSignal(@Param("newProtocolId") long newProtocolId, @Param("oldProtocolId") long oldProtocolId,
                          @Param("createBy") long createBy);

    List<SignalVO> validateSignal(@Param("protocolId") String protocolId);

    List<SignalTreeVO> listSignalByVehicleTypeId4Tree(@Param("vehicleTypeId") String vehicleTypeId ,
                                                      @Param("location") String location,
                                                      @Param("subsystem") String subsystem,
                                                      @Param("searchWord") String searchWord);

    List<SignalVO> getRIOMSignals(@Param("riomName") String riomName);

    /*2021年05月17号 根据信号英文名称查找时候存在对应信号 lx*/
    int selectSignalByNameEn(String signalNameEn);
    /*2021年05月17号 根据信号英文名称更新信号的fault_type_key 和trigger_value lx*/
    int updateSignalForImportEkbFaultType(SignalPO spo);

    List<Map<String,Object>> getNameCnByIds(@Param("ids")String[] temp);

    List<HashMap<String, Object>> getSysListForSignalSearch(@Param("vehicleCode")String vehicleCode, @Param("cars")List<String> cars);

    List<SignalVO> signalListBySysAndLocation(@Param("vehicleCode")String vehicleCode,@Param("subsystem")String subsystem,
                                              @Param("cars")List<String> cars,@Param("inputName") String inputName);

    List<String> structureList(@Param("vehicleCode")String vehicleCode);

    String selectProtocolIdByVehicleCode(@Param("vehicleCode")String vehicleCode);


    @ParamReplace(param = {"inputName"})
    List<MonitorConfigWtdSignalPO> findLikeWtdSignalByInputName(@Param("inputName")String inputName,@Param("protocolId") String protocolId);
}
