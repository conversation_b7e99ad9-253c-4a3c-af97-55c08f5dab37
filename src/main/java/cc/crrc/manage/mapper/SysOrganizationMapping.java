package cc.crrc.manage.mapper;


import cc.crrc.manage.pojo.SysOrganizationPO;
import org.apache.ibatis.annotations.Param;


import java.util.List;

/**
 * @FileName SysOrganizationMapping
 * <AUTHOR> yuxi
 * @Date 2019/10/12 13:04
 * @Version 1.0
 **/
public interface SysOrganizationMapping {
    List<SysOrganizationPO> listOrganizations();

    String getParentNameByParentId(@Param("parentId") String parentId);

    SysOrganizationPO getOrganizationById(@Param("id") String id);

    SysOrganizationPO getOrganizationByName(@Param("name") String name);

    int getOrganizationCountByName(@Param("name") String name);

    int getOrganizationCountByCode(@Param("code") String code);

    int addOrganization(SysOrganizationPO organizationPO);
    //新增对组织用户表的添加操作
    int addOrganizationUser(@Param("userId") String userId,@Param("organizationId") String organizationId);

    int updateOrganization(SysOrganizationPO organizationPO);

    int countSubOrganization(@Param("id") String id);
    
    /**
     * 
     * @Title: listOrganizationsUser   
     * @Description: 根据名字查询当前用户的组织结构  
     * @param: @param name
     * @param: @return      
     * @return: List<SysOrganizationPO>
     * @date:   2020年8月17日 下午2:42:45  
     * @author: Heshenglun   
     * @throws
     */
    List<SysOrganizationPO> listOrganizationsUser( @Param("userId") String userId);
    
    /**
     *  
     * @Title: listSelectBox   
     * @Description: 显示User可编辑的对应的复选框
     * @param: @param name
     * @param: @return      
     * @return: id值得集合
     * @date:   2020年8月18日 下午1:20:53  
     * @author: Heshenglun   
     * @throws
     */
    List<String> listSelectBox(@Param("userId") String userId);

    /**
     *
     * @Title: listOrganizationsUserTraversalDown
     * @Description: 用户所在最高组织向下遍历
     * @param: @param name
     * @param: @return
     * @return: List<SysOrganizationPO>
     * @date:   2020年8月17日 下午2:42:45
     * @author: Heshenglun
     * @throws
     */
    List<SysOrganizationPO> listOrganizationsUserTraversalDown( @Param("userId") String userId);

    List<SysOrganizationPO> listOrganizationsByUserAndNotId( @Param("userId") String userId, @Param("id") String id);
    
}
