package cc.crrc.manage.mapper.mtc;

import cc.crrc.manage.pojo.ekb.EkbFaultSnowSlidePO;
import cc.crrc.manage.pojo.mtc.AutoFaultRecordDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AutoFaultRecordMapping {

	void saveFault(AutoFaultRecordDTO autoFaultRecordDTO);

	void changeFault(AutoFaultRecordDTO autoFaultRecordDTO);

	List<EkbFaultSnowSlidePO> getSnowSlide(String faultTypeKey);

	int updateSnowSlide(@Param("keys")List<EkbFaultSnowSlidePO> keys, @Param("autoFaultRecordDTO")AutoFaultRecordDTO autoFaultRecordDTO);

	EkbFaultSnowSlidePO getParentFaultTypeKey(@Param("keys")List<EkbFaultSnowSlidePO> faultTypeKeys, @Param("autoFaultRecordDTO")AutoFaultRecordDTO autoFaultRecordDTO);

	List<AutoFaultRecordDTO> getChildFault(AutoFaultRecordDTO autoFaultRecordDTO);

	List<EkbFaultSnowSlidePO> getParentFault(@Param("childFault")AutoFaultRecordDTO childFault, @Param("autoFaultRecordDTO")AutoFaultRecordDTO autoFaultRecordDTO);

	int rmSnowSlide(Long id);

	int updateSlideList(@Param("keys")List<EkbFaultSnowSlidePO> keys,  @Param("childFault")AutoFaultRecordDTO childFault);

}
