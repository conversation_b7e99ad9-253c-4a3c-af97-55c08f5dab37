package cc.crrc.manage.mapper.mtc;

import cc.crrc.manage.mq.process.KafkaAlarmWarningPO;
import cc.crrc.manage.pojo.ekb.EkbFaultSnowSlidePO;
import cc.crrc.manage.pojo.excel.AlarmLevelForExcelPO;
import cc.crrc.manage.pojo.excel.MtcAlarmWarningForExcelPO;
import cc.crrc.manage.pojo.mtc.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

/**
 * @FileName MtcAlarmWarningMapping
 * @Description 告警预警mapper
 * <AUTHOR> yuxi
 * @Date 2020/6/5 16:23
 **/
@Repository
public interface MtcAlarmWarningMapping {

    List<MtcAlarmWarningVO> getAlarmWarning(MtcAlarmWarningVO mtcAlarmWarningVO);

    int insertAlarmWarning(KafkaAlarmWarningPO alarmWarning);

    int updateAlarmWaring(KafkaAlarmWarningPO alarmWarning);

    List<MtcAlarmWarningVO> getChildAlarmWarning(@Param("vehicleId") String vehicleId,
                                                 @Param("faultTypeKey") String faultTypeKey);

    int removeSnowSlide(String id);

    int updateSlideList(@Param("keys") List<EkbFaultSnowSlidePO> keys,
                        @Param("childFault") MtcAlarmWarningVO childFault);

    List<HashMap<String, String>> countAlarmWarningByFaultLevel(@Param("lineId") String lineId);

    int getHighestLevel(@Param("lineId") String lineId,
                             @Param("vehicleCode") String vehicleCode,
                             @Param("faultSource") String faultSource);

    // 2020年8月18日 房明宽
    List<MtcAutoFaultRecordVO> getRealtimeAlarmInformationList(@Param("modelCode") String modelCode, @Param("vehicleId") String vehicleId);

    List<AlarmLevelForExcelPO> getAlarmLevel(@Param("startTime") String startTime, @Param("endTime") String endTime, @Param("vehicleId") String vehicleId);
    
    MtcAlarmWarningVO getMtcAlarmRecordById(@Param("id") String id);

    //实时监控故障查询专用 lixin 21-11-26
    List<MtcAlarmWarningVO> getMtcAlarmWarningForMonitor(MtcAlarmWarningVO mtcAlarmWarningVO);

    List<MtcAlarmWarningVO> getMoreMtcAlarmWarningForMonitor(MtcAlarmWarningVO mtcAlarmWarningVO);

    List<MtcAlarmWarningVO> getLineFaultStatistics(@Param("lineId")String lineId);

    List<HashMap<String, Object>> getWarningCount(@Param("lineId")String lineId);

    List<HashMap<String, Object>> getAlarmCount(@Param("lineId")String lineId);

    List<HashMap<String, Object>>  getAlarmOrWarningCountDef(@Param("lineId")String lineId);

    List<MtcAlarmWarningVO> listMtcAlarmWarningRuleTest(MtcAlarmWarningVO mtcAlarmWarningVO);

    int insertTestAlarmWarning(KafkaAlarmWarningPO alarmWarning);

    int updateTestAlarmWaring(KafkaAlarmWarningPO alarmWarning);

    MtcAlarmWarningVO getTestMtcAlarmWarningById(@Param("id")String id);


    /**
     * 车辆监控查询故障预警数据
     * @param vehicleCode 列车编号
     * @return
     */
    List<MtcAlarmWarningVO> getTrainFaultList(@Param("vehicleCode")String vehicleCode);
    List<MtcAlarmWarningVO> getTrainFaultListV2(@Param("vehicleCode")String vehicleCode,@Param("faultSource")String faultSource);

    List<MtcAlarmWarningVO> getAutoFaultList(MtcAlarmWarningDTO codition);

    int addManualAlarmFault(MtcAlarmWarningVO mtcAlarmWarningVO);

    List<MtcAlarmWarningVO> getManualAlarmWarning(MtcAlarmWarningVO mtcAlarmWarningVO);

    List<MtcAlarmWarningForExcelPO> exportManualFaultExcel(MtcAlarmWarningVO condition);

    List<MtcAlarmWarningForExcelPO> exportAutoFaultExcel(MtcAlarmWarningVO condition);

    MtcAlarmWarningPO getManualMtcAlarmRecordById(String id);

    int updateManualAlarmFault(MtcAlarmWarningVO mtcAlarmWarningVO);

    int deleteManualAlarmFault(@Param("ids") List<String> ids);

    int updateAlarmFaultStatus(@Param("id")String id, @Param("tableName")String tableName);

    List<AlarmWarningInfo> getTrainMonitorFaults(@Param("vehicleCode")String vehicleCode,@Param("faultSource") String faultSource);

}
