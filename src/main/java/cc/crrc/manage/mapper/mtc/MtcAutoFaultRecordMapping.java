package cc.crrc.manage.mapper.mtc;

import cc.crrc.manage.pojo.comm.signal.SignalPO;
import cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO;
import cc.crrc.manage.pojo.mtc.MtcAutoFaultRecordDTO;
import cc.crrc.manage.pojo.mtc.MtcAutoFaultRecordVO;
import cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRecordVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Repository
public interface MtcAutoFaultRecordMapping {

    List<MtcAutoFaultRecordVO> listMtcAutoFaultRecord(MtcAutoFaultRecordDTO mtcAutoFaultRecordDTO);

    //查询所有故障 并在线路监控逻辑中进行分类
    List<MtcAutoFaultRecordVO> listCurrentAutoFaultRecordByVehicleCode(@Param("vehicleCode") String vehicleCode);

    MtcAutoFaultRecordVO getMtcAutoFaultRecordById(@Param("id") String id);

    List<SignalPO> getCommOriginalSignalByFaultTypeKey(@Param("faultTypeKey") String faultTypeKey);

    String getVehicleFaultLevel(@Param("vehicleCode") String vehicleCode);

    //websocket 线路监控页面查询线路相关自动上报故障 2020-03-30
    //查询线路未确认故障
    List<MtcAutoFaultRecordVO> listCurrentAutoFaultRecordByLineId(@Param("lineId") String lineId);

    //查询 已处理故障的总数
    int findCurrentAutoFaultRecordByVehicleCode(@Param("vehicleCode") String vehicleCode);

    //车辆当前故障最大等级
    List<Map<String, String>> getMaxTroubleLevelList();

    int confirmMtcAutoFaultRecordById(@Param("id") String id,
                                      @Param("userId") String userId);

    /*2020-03-30 提供Fracas系统 进行自动上报记录修改提报未提报状态*/
    int changeStatusFromFracas(@Param("id") Long id,
                               @Param("status") String status,
                               @Param("confirm") Integer confirm,
                               @Param("endTime") Date endTime);

    /*2020-04-01 提供车辆监控查询车辆的未确认故障数量*/
    int countNotConfirmNumberFroTrain(@Param("vehicleCode") String vehicleCode);

    /*2020-04-01 提供车辆监控查询线路的未确认故障数量*/
    int countNotConfirmNumberForLine(@Param("lineId") String lineId);

    /*2020-04-01 自动上报查询页面的未确认故障数量*/
    int countNotConfirmNumber();

    /*2020-04-01 查询所有自动上报故障的数量*/
    int countFaultNumber();

    /*2020-04-03 查询车辆所有自动上报故障的数量*/
    int countFaultNumberForTrain(@Param("vehicleCode") String vehicleCode);

    String getUniqueFlag(@Param("structureCode") String structureCode,
                         @Param("vehicleId") String vehicleId,
                         @Param("vehicleTypeId") String vehicleTypeId,
                         @Param("lineId") String lineId);

    String getParentStructureCode(@Param("structurePosition") String structurePosition,
                                  @Param("vehicleId") String vehicleId);

    /*查询实时告警和状态预警数据list*/
    List<MtcAlarmWarningVO> getRealTimeAlarmOrEarlyWarningList(@Param("lineId") String lineId,
                                                               @Param("trainCode") String trainCode,
                                                               @Param("faultMode") String faultMode);

    Map<String, Object> getSupressionFault(@Param("id") String id);

    //当天的机理规则故障数据
    List<MtcMechanismAlarmRecordVO> listCurrentMechanismFaultRecordByLineId(@Param("onlineVehicles") List<String> onlineVehicles);
}
