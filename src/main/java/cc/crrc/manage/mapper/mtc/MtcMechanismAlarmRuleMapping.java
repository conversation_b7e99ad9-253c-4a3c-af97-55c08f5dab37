package cc.crrc.manage.mapper.mtc;

import cc.crrc.manage.pojo.comm.signal.CommSignalVO;
import cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRuleDTO;
import cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRulePO;
import cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRuleVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * @Author: Li Caisheng
 * @Date: 2019-12-21
 */
@Repository
public interface MtcMechanismAlarmRuleMapping {

    List<MtcMechanismAlarmRuleVO> list(MtcMechanismAlarmRuleDTO ruleDTO);

    int add(MtcMechanismAlarmRulePO ruleDTO);

    int update(MtcMechanismAlarmRulePO rulePO);

    int delete(String id);

    Map<String, Object> getJson(String id);

    List<CommSignalVO> booleanSignal(@Param("vehicleTypeId")String vehicleTypeId, @Param("location")String location,
                                     @Param("subsystem")String subsystem, @Param("nameCn")String nameCn);

    List<CommSignalVO> analogSignal(@Param("vehicleTypeId")String vehicleTypeId, @Param("location")String location,
                                    @Param("subsystem")String subsystem, @Param("nameCn")String nameCn);

    MtcMechanismAlarmRulePO selectById(String id);

    List<MtcMechanismAlarmRuleVO> mechanismAlarmRuleListByStruCodeAndVehicleType(@Param("vehicleTypeId")String vehicleTypeId,@Param("structureCode") String structureCode);

    List<MtcMechanismAlarmRuleVO> ruleListForCopy(@Param("lineId")String lineId, @Param("name")String name,
                                                  @Param("encryptionStatus")Boolean encryptionStatus);

    List<CommSignalVO> booleanSignal64ms( @Param("location")String location,
                                          @Param("subsystem")String subsystem, @Param("nameCn")String nameCn);

    List<CommSignalVO> analogSignal64ms( @Param("location")String location,
                                         @Param("subsystem")String subsystem, @Param("nameCn")String nameCn);

    List<MtcMechanismAlarmRuleVO> ruleListForCopyEncryption(@Param("idList") List<String> idList);

    List<MtcMechanismAlarmRuleVO> ruleListForEncryption(@Param("idList")List<String> idList);

    Integer updateTestStatus(@Param("id")String id, @Param("testStatus")boolean testStatus, @Param("modifyBy")String modifyBy);
}
