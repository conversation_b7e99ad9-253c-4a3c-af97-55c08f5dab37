package cc.crrc.manage.mapper.mtc;

import cc.crrc.manage.pojo.mtc.MtcMechanismVariableRuleDTO;
import cc.crrc.manage.pojo.mtc.MtcMechanismVariableRulePO;
import cc.crrc.manage.pojo.mtc.MtcMechanismVariableRuleVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * @FileName MtcMechanismVariableRuleMapping
 * @Description TODO
 * @<PERSON> <PERSON>
 * @Date 2021/1/4
 **/
@Repository
public interface MtcMechanismVariableRuleMapping {

    List<MtcMechanismVariableRuleVO> list(MtcMechanismVariableRuleDTO ruleDTO);

    int add(MtcMechanismVariableRulePO ruleDTO);

    int update(MtcMechanismVariableRulePO ruleDTO);

    int delete(String id);

    Map<String, String> getJson(String id);

    MtcMechanismVariableRulePO selectById(String id);

    List<MtcMechanismVariableRuleVO> variables(@Param("vehicleTypeId")String vehicleTypeId, @Param("location")String location,
                                               @Param("subsystem")String subsystem, @Param("nameCn")String nameCn);
}
