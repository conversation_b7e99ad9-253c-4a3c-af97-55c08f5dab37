package cc.crrc.manage.mapper.mtc;

import cc.crrc.manage.pojo.excel.MtcFaultRecordExcelPO;
import cc.crrc.manage.pojo.mtc.MtcAlarmWarningDTO;
import cc.crrc.manage.pojo.mtc.MtcFaultRecordPO;
import cc.crrc.manage.pojo.mtc.MtcFaultRecordVO;


import java.util.List;

/**
 * 文件描述
 *
 * <AUTHOR>
 * @date 2022/08/10  16:08
 */
public interface MtcFaultRecordMapping {


    void saveFaultRecord(MtcFaultRecordPO mtcFaultRecordPO);

    void updFaultRecord(MtcFaultRecordPO mtcFaultRecordPO);

    void updFaultRecordConfirm(MtcFaultRecordPO mtcFaultRecordPO);

    void delFaultRecord(String id);

    List<MtcFaultRecordVO>  getFaultRecord(MtcAlarmWarningDTO condition);

    List<MtcFaultRecordExcelPO> findFaultWorkOrderList(MtcAlarmWarningDTO condition);
}
