package cc.crrc.manage.mapper.mtc;

import cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRecordDTO;
import cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRecordVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MtcMechanismAlarmRecordMapping {

    List<MtcMechanismAlarmRecordVO> listMtcMechanismAlarmRecord(MtcMechanismAlarmRecordDTO mtcMechanismAlarmRecordDTO);

    int countMtcMechanismAlarmRecordByLineId(@Param("lineId") Long lineId);
    /**
     * @return java.util.List<cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRecordVO>
     * @Description websocket推送 机理故障接口（查询线路的所有机理故障）
     * <AUTHOR> xin
     * @Date 9:44 2020/1/2
     * @Param [lineId]
     **/
    List<MtcMechanismAlarmRecordVO> findMechanismAlarmFaultListByLineId(@Param("lineId") Long lineId);
    /**
     * @return java.util.List<cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRecordVO>
     * @Description websocket 查询车辆对应的机理故障
     * <AUTHOR> xin
     * @Date 10:06 2020/1/2
     * @Param [vehicleCode]
     **/
    List<MtcMechanismAlarmRecordVO> findMechanismAlarmFaultListByVehicleCode(@Param("vehicleCode")String vehicleCode);

    String getMechanismAlarmFaultLevel(@Param("vehicleCode")String vehicleCode);

    int updateCloseStatue (@Param("id") String id,@Param("closeStatue") Boolean closeStatue);
}
