package cc.crrc.manage.mapper;

import cc.crrc.manage.pojo.SysFilePO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SysFileMapping {
    int addSysFile(SysFilePO sysFile);

    int deleteSysFile(@Param("id") String id);

    List<SysFilePO> getSysFileListByFaultTypeKey(@Param("faultTypeKey") String faultTypeKey);
}
