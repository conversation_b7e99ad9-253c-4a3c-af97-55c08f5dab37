package cc.crrc.manage.mapper;

import cc.crrc.manage.pojo.Element;
import cc.crrc.manage.pojo.SysElementRole;
import cc.crrc.manage.pojo.SysRoleUserVO;

import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface SysElementMapping {

    List<Element> selectElement(@Param("elementCode") String elementCode);

    int addSysElement(Element element);

    int deleteSysElementById(Element element);

    int updateSysElementById(Element element);

    List<Element> selectSysElement(Element element);

    int addElementByRoleId(SysElementRole sysElementRole);

    int removeElementByRoleId(@Param("elementId") String elementId, @Param("roleId") String roleId);

    List<Element> getAllElementByRoleId(@Param("roleId") String roleId);

    //角色资源关系数据 重复校验查询
    List<Element> selectElementRole(SysElementRole sysElementRole);
    
    //查询当前用户的element权限 heshenglun 2020-8-26
    List<Element> selectSysElementByUser(@Param("menuId") String menuId,@Param("roleIdList") List<SysRoleUserVO> roleIdList);
    
    //查询当前所编辑的角色的element权限  heshenglun 2020-8-26
    List<String> selectSyeElementByRoleId(Element element);
    
    //根据当前编辑的角色删除   heshenglun 2020-8-26
    int removeEleByRoleId(@Param("roleId") String roleId);
    
    
    
   
    
}
