package cc.crrc.manage.mapper.external;

import cc.crrc.manage.pojo.SysDictDTO;
import cc.crrc.manage.pojo.external.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @ClassName TracksideOnlineMonitorMapping
 * @Description
 * @<PERSON> <PERSON>
 * @Date 2023/8/18 15:47
 **/
@Repository
public interface TracksideOnlineMonitorMapping {
    int batchInsertCheckoutData(@Param("dataList") List<CheckoutData> dataList);

    void insertTrainLogInfo(TrainLogInfo info);

    List<TrainLogInfo> listTrainLogInfo(TrainLogInfoDTO trainLogInfoDTO);

    List<CheckoutData> listCheckoutDataWheel(@Param("id") String id);

    List<CheckoutData> listCheckoutDataAxle(@Param("id") String id);

    List<SysDictDTO> wheelCheckPointDict();

    List<SysDictDTO> panCheckPointDict();

    List<CheckoutData> listCheckoutDataPan(@Param("id") String id);

    List<SysDictDTO> wheelPartDict();

    List<CheckoutAlarmDataVO> listAlarmInfo(CheckoutAlarmDataDTO checkoutAlarmDataDTO);

    List<SysDictDTO> panPartDict();

    List<String> getComponentsByLocation(@Param("location") String location);

    String getComponentsByLocationSecond(@Param("location") String location);

    List<String> getComponents();

    List<String> getPantograph();

    List<SysDictDTO> locationDict();

    String getPantographSecond(String partCn);
}
