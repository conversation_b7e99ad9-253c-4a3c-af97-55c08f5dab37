package cc.crrc.manage.mapper;

import cc.crrc.manage.pojo.User;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @FileName UserManageMapping
 * <AUTHOR> yuxi
 * @Date 2019/6/5 9:38
 * @Version 1.0
 **/
@Repository
public interface UserManageMapping {
    int addNewUser(User user);

    int updateUser(User user);

    List<User> listUser(User user);

    User getUserByUsername(String username);

    User getUserById(String id);

    int checkUsernameExist(@Param("id") String id,@Param("username") String username,@Param("name") String name);

    int checkOrganizationExistUser(String departId);

    int updateUserForPersonalCenter(User user);
    /*角色用户关系多对多 删除时需要删除对应关系 所以需要查询原本的关系 */
    User getRoleIdsById(@Param("id") String id);
}
