package cc.crrc.manage.mapper.component;

import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.component.ComponentDTO;
import cc.crrc.manage.pojo.component.ComponentDetailVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
@Component
public interface ComponentMapping {

    List<ComponentDTO> getComponents(ComponentDTO componentDTO);

    ComponentDetailVO getComponentDetailById(@Param("id") String id, @Param("fileType") String fileType);

    int insertComponents(ComponentDTO componentDTO);

    int updateComponents(ComponentDTO componentDTO);

    int delComponents(@Param("id") String id);

    int saveFaultTypeFile(@Param("fileId") String fileId, @Param("id") String id);

    void delFile(@Param("id") String id);

    List<SysFilePO> selectlFile(@Param("componentId") String componentId);

    List<ComponentDTO> getComponentsByTypeId(@Param("componentTypeId") String componentTypeId);

    ComponentDTO getComponentBySerialNumber(@Param("serialNumber") String serialNumber);
}
