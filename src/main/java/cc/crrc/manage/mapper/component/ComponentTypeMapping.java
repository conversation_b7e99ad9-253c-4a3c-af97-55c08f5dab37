package cc.crrc.manage.mapper.component;

import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.component.ComponentTypeDTO;
import cc.crrc.manage.pojo.component.ComponentTypeDetailVO;
import cc.crrc.manage.pojo.mtr.ManufacturerEmployeePO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ComponentTypeMapping {
    // 2020年8月14日 房明宽 增加 线路名称和车型名称 查询条件
    List<ComponentTypeDTO> getComponentTypes(ComponentTypeDTO comDTO);

    ComponentTypeDetailVO getComponentTypeById(@Param("id") String id,@Param("fileType") String fileType);

    // 2020年8月14日 房明宽 增加部件型号 相比之前多了车型id和线路id
    int insertComType(ComponentTypeDTO comDTO);

    // 2020年8月14日 房明宽 相比之前多了车型id和线路id
    int updateComType(ComponentTypeDTO comDTO);

    int delComType(@Param("id")String id);

    int clearManufacturerFromComponentType(@Param("manufacturerId")String manufacturerId,@Param("modifyBy")String modifyBy);

    int delEmployee(@Param("id")String id);

    void deleteComponentTypeContactsByEmployeeId(@Param("id")String id);
    
    int valEmployee(@Param("id")String id, @Param("empId")String empId);

    void assEmployee(@Param("id")String id, @Param("empId")String empId);

    List<String> getComponentTypeIdByEmployeeId(@Param("id")String id);

    List<ManufacturerEmployeePO> getEmployees(ComponentTypeDTO comDTO);

    int saveFaultTypeFile(@Param("fileId")String fileId, @Param("id")String id);

    int delFile(@Param("id")String id);

    List<SysFilePO> selectlFile(@Param("componentTypeId")String componentTypeId);

    SysFilePO getSysFileByComponentTypeId(@Param("componentTypeId") String componentTypeId);

	int deleteMtrComponentTypeContacts(@Param("manufacturerEmployeeId")String manufacturerEmployeeId, @Param("componentTypeId")String componentTypeId);

	int validCount(@Param("manufacturerEmployeeId")String manufacturerEmployeeId, @Param("componentTypeId")String componentTypeId);

}
