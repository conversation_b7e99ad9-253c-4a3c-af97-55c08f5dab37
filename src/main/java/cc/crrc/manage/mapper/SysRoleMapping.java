package cc.crrc.manage.mapper;

import cc.crrc.manage.datascope.DataScope;
import cc.crrc.manage.pojo.SysRoleMenu;
import cc.crrc.manage.pojo.SysRoleUserVO;
import cc.crrc.manage.pojo.SysRoleVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SysRoleMapping {
    List<SysRoleVO> getSysRoleByRoleType(SysRoleVO sysRoleVO);

    @DataScope(userIdColumn = "sr.create_by")
    List<SysRoleVO> getSysRoleByDataPermission(SysRoleVO sysRoleVO);

    List<SysRoleVO> getSysRole(SysRoleVO sysRoleVO);

    int saveSysRoleSonInfo(SysRoleVO sysRoleVO);

    int updateSysRoleSonInfo(SysRoleVO sysRoleVO);

    int deleteSysRoleInfo(@Param("id") String id);

    /**
     * 通过角色ID删除角色和菜单关联
     *
     * @param id 角色ID
     * @return 结果
     */
    void deleteRoleMenuByRoleId(@Param("id") String id);

    /**
     * 批量新增角色菜单信息
     *
     * @param list 角色菜单列表
     * @return 结果
     */
    int batchRoleMenu(List<SysRoleMenu> list);

    /**
     * 通过id查询角色信息     *
     *
     * @param id
     */
    SysRoleVO getSysRoleInfoById(@Param("id") String id);

    /**
     * 通过parentId查询角色是否含有子角色信息     *
     *
     * @param parentId
     */
    List<SysRoleVO> getSysRoleInfoByParentId(@Param("parentId") String parentId);

    /**
     * 查询角色对应的所有菜单权限     *
     *
     * @param id
     */
    List<SysRoleMenu> getSysRoleMenuById(@Param("id") String id);

    /**
     * 更新时 做重复校验用
     */
    List<SysRoleVO> selectExistSysRole(SysRoleVO sysRoleVO);


    /**
     * 2019/11/06
     * 用户角色关系多对多 新建用户时调用的维护角色和用户关系的方法
     */
    void addRoleUserRelation(SysRoleUserVO sysRoleUserVO);

    /**
     * 2019/11/06
     * 用户角色关系多对多 查询已经存在的用户角色关系
     */
    List<SysRoleUserVO> listRoleUserRelationsByUserId(@Param("userId") String userId);

    /**
     * 2019/11/06
     * 用户角色关系多对多 删除已经存在的用户角色关系
     */
    void deleteRoleUserRelation(SysRoleUserVO relation);

    /**
     * @Description 根据homeMenuId查询是否是父级菜单
     * @Name
     * @Param
     * @Return
     * <AUTHOR> zhijian
     * @Date 2020/6/22 16:56
     */
    int findCountByHomeMenuId(@Param("homeMenuId") String homeMenuId);
    
    
    /**
     * 
     * @Title: getMaxDataPermissionByUsername   
     * @Description: 获取当前用户最大的datapermission
     * @param: @param username
     * @param: @return      
     * @return: 返回最大的datapermission
     * @date:   2020年8月18日 下午6:41:05  
     * @author: Heshenglun   
     * @throws
     */
    int getMaxDataPermissionByUsername(@Param("userId") String userId);
}
