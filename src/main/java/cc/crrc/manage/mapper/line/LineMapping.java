package cc.crrc.manage.mapper.line;

import cc.crrc.manage.pojo.SysOrganizationPO;
import cc.crrc.manage.pojo.line.LineDTO;
import cc.crrc.manage.pojo.line.OrgMetroLineDTO;
import cc.crrc.manage.pojo.line.StationDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

@Repository
public interface LineMapping {

    List<LineDTO> getLines(@Param("lineDTO") LineDTO lineDTO, @Param("list") List<SysOrganizationPO> list, @Param("userId") String userId);

    int updateLine(LineDTO lineDTO);

    int addLine(LineDTO lineDTO);

    int deleteLine(@Param("id") String id, @Param("userId") String userId);

    List<LineDTO> getLocations();

    int addOrgMetroLine(OrgMetroLineDTO orgMetroLineDTO);

    int deleteOrganizationLine(@Param("metroLineId") String metroLineId);

    List<StationDTO> getStations(StationDTO stationDTO);

    int insertStations(StationDTO station);

    int updateStations(StationDTO station);

    int deleteStation(StationDTO stationDTO);

    List<StationDTO> getStation(StationDTO stationDTO);

    StationDTO getCurrentStation(@Param("metroLineId") String metroLineId,@Param("stationCode") int stationCode);

    HashMap<String, String> getOrganizationsByLineId(@Param("lineId") String lineId);

    LineDTO getLineById(@Param("id") String id);
    
    List<StationDTO> getStationByVehicleId(@Param("vehicleId") String vehicleId);

    Float getCurrentRunDistance(@Param("trainId") String trainCode);

    StationDTO getStartStation(@Param("metroLineId")String lineId, @Param("direction")String direction);

    List<StationDTO>  getStationsForDrawLine(@Param("metroLineId")String lineId);
}
