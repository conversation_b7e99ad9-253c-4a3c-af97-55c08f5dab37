package cc.crrc.manage.mapper.ekb;

import cc.crrc.manage.pojo.SysFilePO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EkbFaultTypeFileMapping {

    int saveFaultTypeFile(@Param("fileId") String fileId,@Param("faultTypeKey") String faultTypeKey);

    int deleteFaultTypeFile(@Param("faultTypeKey") String faultTypeKey,@Param("fileId") String fileId);

    List<SysFilePO> getEkbFaultTypeFileList(@Param("faultTypeKey")String faultTypeKey);
}
