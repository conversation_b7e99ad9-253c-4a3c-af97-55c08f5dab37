package cc.crrc.manage.mapper.ekb;

import cc.crrc.manage.pojo.ekb.EkbFaultTypeReasonVO;
import cc.crrc.manage.pojo.excel.EkbFaultTypeReasonForExcelPO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
public interface EkbFaultTypeReasonMapping {
    void addFaultTypeReasonRelation(@Param("faultReasonKey") String faultReasonKey, @Param("faultTypeKey") String faultTypeKey
            , @Param("initCounter") Long initCounter, @Param("realCounter") Long realCounter);

    void deleteEkbFaultTypeReason(@Param("faultTypeKey") String faultTypeKey);

    List<EkbFaultTypeReasonVO> listFaultTypeReason(@Param("faultTypeKey") String faultTypeKey, @Param("faultReasonKey") String faultReasonKey);

    void deleteEkbFaultTypeReasonByFaultReasonKey(@Param("faultReasonKey") String faultReasonKey);

	void deleteEkbFaultTypeReasonRelation(@Param("faultTypeKey")String faultTypeKey, @Param("faultReasonKey")String faultReasonKey);

	void deleteEkbFaultReasonMeasureRelation(@Param("faultTypeKey")String faultTypeKey, @Param("faultReasonKey")String faultReasonKey, @Param("faultMeasureKey")String faultMeasureKey);

    void batchInsert(ArrayList<EkbFaultTypeReasonForExcelPO> ekbFaultTypeReasonForExcelPOS);
}
