package cc.crrc.manage.mapper.ekb;

import cc.crrc.manage.pojo.ekb.EkbReasonMeasurePO;
import cc.crrc.manage.pojo.excel.EkbReasonMeasureForExcelPO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * @FileName EkbReasonMeasureMapping
 * <AUTHOR>
 * @Date 2019/11/11 9:18
 * @Version 1.0
 **/
@Repository
public interface EkbReasonMeasureMapping {
    List<EkbReasonMeasurePO> listReasonMeasure(@Param("faultTypeKey") String faultTypeKey, @Param("faultReasonKey") String faultReasonKey, @Param("faultMeasureKey") String faultMeasureKey);

    int updateReasonMeasureRealCounter(EkbReasonMeasurePO reasonMeasure);

    int deleteReasonMeasure(@Param("faultTypeKey") String faultTypeKey, @Param("faultReasonKey") String faultReasonKey, @Param("faultMeasureKey") String faultMeasureKey);

    void batchInsertReasonMeasure(ArrayList<EkbReasonMeasureForExcelPO> ekbReasonMeasureForExcelPOS);


    /**根据measureKey集合删除故障原因和故障措施关联关系 zhangzhijian 2020-09-16*/
    int deleteReasonMeasureByMeasureKeys(@Param("measureList") List<String> measureList);
}
