package cc.crrc.manage.mapper.ekb;

import cc.crrc.manage.pojo.ekb.EkbFaultMeasurePO;
import cc.crrc.manage.pojo.ekb.EkbFaultMeasureVO;
import cc.crrc.manage.pojo.excel.EkbFaultMeasureForExcelPO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @FileName EkbFaultMeasureMapping
 * <AUTHOR> yuxi
 * @Date 2019/11/9 10:08
 * @Version 1.0
 **/
@Repository
public interface EkbFaultMeasureMapping {
    List<EkbFaultMeasureVO> listFaultMeasureVaguely(@Param("id")String id, @Param("keyword") String keyword);

    List<EkbFaultMeasureVO> listFaultMeasure(EkbFaultMeasureVO faultMeasure);

    int insertFaultMeasure(EkbFaultMeasurePO faultMeasure);

    int updateFaultMeasure(EkbFaultMeasurePO faultMeasure);

    int deleteFaultMeasure(@Param("ids") String[] ids);

    EkbFaultMeasurePO getEkbFaultMeasure(@Param("faultTypeKey") String faultTypeKey, @Param("faultReasonKey") String faultReasonKey);

    List<EkbFaultMeasurePO> getFaultMeasureByContent(@Param("content") String content);

    /*2020年4月23日 批量导入接口 forExcel Fang*/
    int addFaultMeasureList(@Param("list")List<EkbFaultMeasureForExcelPO> list);
    /*2020年4月23日 下载模板接口 forExcel Fang*/
    List<EkbFaultMeasureForExcelPO> getEkbFaultMeasureForExcel(@Param("countNum")String countNum);

    //2020/04/23 guowei 查询所有measurekey
    List<String> getMeasureKeyList();

    List<EkbFaultMeasureForExcelPO> getMeasureKeyAndMeasureCodeList();

    /**根据ids查询所有的故障措施业务主键measurekey*/
    List<String> findMeasureKeyByIds(@Param("ids") String[] ids);
}
