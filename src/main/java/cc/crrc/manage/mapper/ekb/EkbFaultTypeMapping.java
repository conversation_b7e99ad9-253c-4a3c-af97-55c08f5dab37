package cc.crrc.manage.mapper.ekb;


import cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO;
import cc.crrc.manage.pojo.excel.EkbFaultTypeForExcelPO;
import cc.crrc.manage.pojo.excel.EkbFaultTypeForExportPO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository/*@Repository来告诉spring我已经把这个bean注入进来了*/
public interface EkbFaultTypeMapping {
    int addEkbFaultType(EkbFaultTypeDTO ekbFaultTypeDTO);

    int deleteEkbFaultType(@Param("id") String id);

    int updateEkbFaultType(EkbFaultTypeDTO ekbFaultTypeDTO);

    List<EkbFaultTypeDTO> listEkbFaultType(EkbFaultTypeDTO ekbFaultTypeDTO);

    EkbFaultTypeDTO getEkbFaultTypeById(@Param("id") String id);

    List<EkbFaultTypeDTO> getFaultTypeByStructureCodeAndSubsystem(@Param("structureCode") String structureCode, @Param("subsystem") String subsystem);

    EkbFaultTypeDTO getEkbFaultTypeByCode(@Param("faultCode") String faultCode);

    List<EkbFaultTypeDTO> getEkbFaultTypeForVehicleTypeList(@Param("structureCode") String structureCode, @Param("vehicleTypeId") String vehicleTypeId);

    EkbFaultTypeDTO getEkbFaultTypeByFaultTypeKey(@Param("faultTypeKey") String faultTypeKey);

    List<Map<String, String>> faultList(EkbFaultTypeDTO dto);

    List<Map<String, String>> faultFuzzyList(@Param("vehicleTypeId") String vehicleTypeId, @Param("location") String location, @Param("subsystem") String subsystem, @Param("nameCn") String nameCn);

    List<EkbFaultTypeDTO> getFaultTypeByNameCn(@Param("nameCn") String nameCn);

    List<EkbFaultTypeDTO> getFaultTypeByFaultTypeKey(@Param("faultTypeKey") String faultTypeKey);
    
    // 统计 未启用的父节点数量
    List<Boolean> getNumbeOfDisabledNodes(@Param("faultTypeKey")String faultTypeKey);

    //2020-03-31 fangmingkuan 提供雪崩 查询故障（模糊查询）
    List<EkbFaultTypeDTO> listEkbFaultTypeForSnowSlide(@Param("nameCn") String nameCn,@Param("faultTypeKey") String[] faultTypeKey);

    List<EkbFaultTypeDTO> getFaultTypeListByFaultTypeKey(@Param("faultTypeKeyList") List<String> faultTypeKeyList);

    /*2020年4月23日 批量导入接口 forExcel Fang*/
    int addFaultTypeList(@Param("list")List<EkbFaultTypeForExcelPO> list);

    /*2020年4月23日 下载模板接口 forExcel Fang*/
    List<EkbFaultTypeForExportPO> getEkbFaultCodeForExcel(@Param("countNum")String countNum,
                                                          @Param("lineId")String lineId,
                                                          @Param("vehicleTypeId")String vehicleTypeId,
                                                          @Param("faultCategory")Integer faultCategory);

    //2020/04/23 guowei  查询ekb_fault_type所有的key
    List<String> getFaultTypeList();

    // 查询 所有 fault_type_key 和 fault_code
    List<EkbFaultTypeForExcelPO> getFaultTypeAndFaultCodeList();


    /**根据fault_type_key修改enable状态*/
    Integer updateEkbFaultTypeByKey(@Param("faultTypeKey") String faultTypeKey,@Param("enable") Boolean enable,@Param("location") String location);

    /**故障字典表新增 excel导入（同步更新信号表使用）*/
    int addEkbFaultTypeForExcel(EkbFaultTypeForExcelPO excelPO);

    List<EkbFaultTypeDTO> getEkbFaultTypeAll();

    List<EkbFaultTypeForExcelPO> getEkbFaultCodeByExcel(@Param("countNum")String countNum,
                                                        @Param("lineId")String lineId,
                                                        @Param("vehicleTypeId")String vehicleTypeId,
                                                        @Param("faultCategory")Integer faultCategory);

}
