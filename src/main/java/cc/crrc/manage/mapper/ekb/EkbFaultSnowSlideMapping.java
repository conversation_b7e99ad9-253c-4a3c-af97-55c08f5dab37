package cc.crrc.manage.mapper.ekb;

import cc.crrc.manage.common.vo.TreeNodeVO;
import cc.crrc.manage.pojo.ekb.EkbFaultSnowSlidePO;
import cc.crrc.manage.pojo.mtc.MtcAlarmWarningPO;
import cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EkbFaultSnowSlideMapping {

    List<EkbFaultSnowSlidePO> findFaultSnowSlideByKey(@Param("faultTypeKey") String faultTypeKey);

    Boolean getEnableStatusByFaultTypeKey(@Param("faultTypeKey") String faultTypeKey);

    Integer deleteFaultSnowSlideByKey(@Param("faultTypeKey") String faultTypeKey);

    Integer saveFaultSnowSlideRelation(@Param("listSlidePO") List<EkbFaultSnowSlidePO> listEkbFaultSnowSlidePO);

    Integer changeEnableStatus(@Param("faultTypeKey") String faultTypeKey,
                               @Param("enable") Boolean enable,
                               @Param("vehicleTypeId") String vehicleTypeId,
                               @Param("modifyBy") String modifyBy);

    List<TreeNodeVO> getFaultSnowSlideExceptKey(@Param("faultTypeKey") String faultTypeKey,
                                                @Param("vehicleTypeId") String vehicleTypeId);

    List<EkbFaultSnowSlidePO> findListEkbFaultSnowSlide(@Param("vehicleTypeId") String vehicleTypeId);

    List<String> findFirstParentFaultTypeKey(@Param("vehicleTypeId") String vehicleTypeId);

    List<EkbFaultSnowSlidePO> getSnowSlide(@Param("faultTypeKey")String faultTypeKey,@Param("enable")Boolean enable);

    int updateSnowSlide(@Param("keys") List<EkbFaultSnowSlidePO> keys,
                        @Param("alarmWarning") MtcAlarmWarningPO alarmWarning);

    EkbFaultSnowSlidePO getParentFaultTypeKey(@Param("keys") List<EkbFaultSnowSlidePO> faultTypeKeys,
                                              @Param("alarmWarning") MtcAlarmWarningVO alarmWarning);

    List<EkbFaultSnowSlidePO> getParentFault(@Param("childFault") MtcAlarmWarningVO childFault,
                                             @Param("alarmWarning") MtcAlarmWarningVO alarmWarning);

    /**根据faultTypeKey删除故障雪崩相关数据*/
    int deleteSnowSlideByKeys(@Param("faultTypeKeyList") List<String> faultTypeKeyList);

    /**根据父节点faultTypeKey查询子节点faultTypeKey*/
    List<String> findFaultTypeKeyByParentKey(@Param("faultTypeKeyList") List<String> faultTypeKeyList);
}
