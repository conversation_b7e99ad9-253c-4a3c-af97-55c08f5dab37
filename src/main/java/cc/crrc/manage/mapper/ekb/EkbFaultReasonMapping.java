package cc.crrc.manage.mapper.ekb;

import cc.crrc.manage.pojo.ekb.EkbFaultReasonDTO;
import cc.crrc.manage.pojo.excel.EkbFaultReasonForExcelPO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EkbFaultReasonMapping {
    int addEkbFaultReason(EkbFaultReasonDTO ekbFaultReasonDTO);

    int deleteEkbFaultReason(@Param("id") long id);

    int updateEkbFaultReason(EkbFaultReasonDTO ekbFaultReasonDTO);

    List<EkbFaultReasonDTO> listEkbFaultReason(EkbFaultReasonDTO ekbFaultReasonDTO);

    EkbFaultReasonDTO getEkbFaultReasonById(@Param("id") long id);

    int updateTypeReasonRealCounter(@Param("faultTypeKey") String faultTypeKey, @Param("faultReasonKey") String faultReasonKey, @Param("realCounter") Long realCounter);

    List<EkbFaultReasonDTO> listEkbFaultReasonVaguely(@Param("id") String InsertValidated, @Param("keyword") String keyword);

    EkbFaultReasonDTO getEkbFaultReason(@Param("faultTypeKey") String faultTypeKey);

    List<EkbFaultReasonDTO> getFaultReasonByContent(@Param("content") String content);

    /*2020年4月23日 批量导入接口 forExcel Fang*/
    int addFaultReasonList(@Param("list") List<EkbFaultReasonForExcelPO> list);

    /*2020年4月23日 下载模板接口 forExcel Fang*/
    List<EkbFaultReasonForExcelPO> getEkbFaultReasonForExcel(@Param("countNum") String countNum);

    //2020/04/23 guowei 查询ekb_fault_reason所有key
    List<String> getFaultTypeReasonKeyList();

    //2020/04/23 guowei 通过故障类型筛选ekb_fault_type_reason中相应的reason_key
    List<String> getFaultTypeReasonKeyListByfaultTypeKey(@Param("faultTypeKey") String faultTypeKey);

    List<EkbFaultReasonForExcelPO> getReasonKeyAndReasonCodeList();

}
