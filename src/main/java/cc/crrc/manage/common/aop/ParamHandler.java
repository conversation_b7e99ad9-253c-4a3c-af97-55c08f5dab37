package cc.crrc.manage.common.aop;

import cc.crrc.manage.common.annotation.ParamReplace;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;

/**
 * @fileName querySpecialChar
 * @description 查询中特殊字符过滤 当前只处理了 { '%' }
 * <AUTHOR>
 * @date 2021/7/27 10:07
 **/
@Aspect
@Component
public class ParamHandler {
    private static final Logger logger = LoggerFactory.getLogger(ParamHandler.class);

    @Pointcut("@annotation(cc.crrc.manage.common.annotation.ParamReplace)")
    public void aspect() {}


    @Around("aspect() && @annotation(anno)")
    public Object around(ProceedingJoinPoint joinPoint, ParamReplace anno){
        try{
            //获取方法参数值数组
            Object[] args = joinPoint.getArgs();
            String[] targets = anno.param();
            Class<?> type = anno.type();
            for (int i = 0;i < args.length;i++) {
                Object arg = args[i];
                if (arg == null || !arg.getClass().equals(type))
                    continue;
                if (type != String.class){
                    for (String target : targets) {
                        Field field = arg.getClass().getDeclaredField(target);
                        field.setAccessible(true);
                        String value = field.get(arg) == null ? null:String.valueOf(field.get(arg));
                        String newValue = StringUtils.disposePerCent(value);
                        field.set(arg,newValue);
                    }
                }else {
                    args[i] = StringUtils.disposePerCent((String)arg);
                }
            }
            return joinPoint.proceed(args);
        }catch (Throwable th){
            logger.info("特殊字符处理异常{}",th);
            throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION);
        }
    }

}
