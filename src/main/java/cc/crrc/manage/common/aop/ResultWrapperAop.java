package cc.crrc.manage.common.aop;


import cc.crrc.manage.common.DataType;
import cc.crrc.manage.common.annotation.AopIgnore;
import cc.crrc.manage.common.response.Result;
import cc.crrc.manage.common.utils.Constants;
import cc.crrc.manage.common.utils.UserUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * @FileName ResultWrapperAop
 * <AUTHOR> shuangquan
 * @Date 2019/5/30 8:48
 **/
@Aspect
@Configuration
public class ResultWrapperAop {
    private static final Logger logger = LoggerFactory.getLogger(ResultWrapperAop.class);

    /**
     * @param joinPoint
     * @Description: 拦截controller返回值  构造统一格式
     * @returns: java.lang.Object
     * @Author: <PERSON> shuangquan
     * @Date: 2019/5/30
     */
    //@Around("execution(* cc..controller.*.*
    @Around("@within(org.springframework.web.bind.annotation.RestController)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object[] objs = joinPoint.getArgs();
        invokeParameter(objs);
        if (method.isAnnotationPresent(AopIgnore.class)) {
            return joinPoint.proceed();
        }
        Object object = joinPoint.proceed();
        return Result.builder().success(Constants.SUCCESS).body(object).build();
    }

    private void invokeParameter(Object[] objs) {
        for (Object obj : objs) {
            if (obj == null || DataType.contains(obj.getClass().getSimpleName())) {
                continue;
            }
            invoke("createBy", obj);
            invoke("modifyBy", obj);
        }
    }

    private void invoke(String fieldName, Object obj) {
        try {
            Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            if ("long".equalsIgnoreCase(field.getGenericType().getTypeName())) {
                field.set(obj, UserUtils.getUserId());
            }
        } catch (NoSuchFieldException e) {
        } catch (IllegalArgumentException e) {
            logger.warn("No field {}", fieldName);
        } catch (IllegalAccessException e) {
            logger.error(e.getMessage());
        }
    }
}
