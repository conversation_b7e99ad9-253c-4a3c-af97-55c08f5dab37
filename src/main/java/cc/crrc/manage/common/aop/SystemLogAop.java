package cc.crrc.manage.common.aop;

import cc.crrc.manage.cache.CacheUtils;
import cc.crrc.manage.cache.dict.DictCache;
import cc.crrc.manage.common.annotation.log.SystemLog;
import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.common.utils.Constants;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.common.utils.WebUtils;
import cc.crrc.manage.pojo.SysDictVO;
import cc.crrc.manage.pojo.SysLogDTO;
import cc.crrc.manage.service.SysLogService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Date;
import java.util.List;

/**
 * 系统操作日志切面
 * <AUTHOR> zhiyang
 * @date 2022/06/21/ 11:37
 */
@Aspect
@Component
public class SystemLogAop {

    /**
     * logger
     */
    private static final Logger logger = LoggerFactory.getLogger(SystemLogAop.class);

    @Autowired
    private SysLogService logService;

    /**
     * 切点
     */
    @Around("@annotation(annolog)")
    public Object around(ProceedingJoinPoint joinPoint, SystemLog annolog) throws Throwable{

        Object obj;
        //查询系统日志标识
        boolean saveLogFlag = true;
        //日志记录
        SysLogDTO sysLogDTO = new SysLogDTO();
        try {
            List<SysDictVO> sysDictList = CacheUtils.getValue(DictCache.class, String.join("_", Constants.ENABLE_SELECT_LOG, Constants.LINE_ID, Constants.VEHICLE_TYPE_ID));
            String configValue = "";
            if(CollectionUtils.isNotEmpty(sysDictList)){
                configValue = sysDictList.get(0).getValue();
            }
            //操作描述
            String optDesc = annolog.optDesc();
            //操作类型
            String optType = annolog.optType().value();
            if(SystemLogEnum.SELECT.value().equals(optType) && !Constants.ENABLE_SELECT_LOG_ON.equals(configValue)){
                saveLogFlag = false;
            }
            //请求中参数
            handleRequest(sysLogDTO);
            //方法参数
            handleParameters(joinPoint, sysLogDTO);
            //用户参数
            handleUserInfo(optType,optDesc,sysLogDTO);

            //目标方法
            obj = joinPoint.proceed();
        } catch (Throwable  e){
            //记录标识
            saveLogFlag = true;
            //异常状态
            sysLogDTO.setStatus("0");
            //异常信息
            sysLogDTO.setException(e.getMessage());
            sysLogDTO.setRequestTime(new Date());
            throw e;
        } finally {
            //日志记录
            if(saveLogFlag){
                saveLog(sysLogDTO);
            }
        }

        return obj;
    }

    /**
     * 获取请求中的参数
     * @param sysLog
     */
    private void handleRequest(SysLogDTO sysLog){
        //获取request
        HttpServletRequest request =
                ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String userAgent = request.getHeader("User-Agent");
        String uri = request.getRequestURI().substring(4);
        //菜单path
        String path = "/";
        if(StringUtils.isNotBlank(uri) && uri.length() > 1){
            path = path + uri.split("\\/")[1];
        }

        //用户IP
        sysLog.setIp(WebUtils.getIpAddress(request));
        //URI
        sysLog.setUri(uri);
        //用户Agent
        sysLog.setUserAgent(userAgent);
        //菜单Path
        sysLog.setMenuPath(path);
    }

    /**
     * 获取方法参数
     * @param joinPoint
     * @param sysLog
     */
    private void handleParameters(ProceedingJoinPoint joinPoint, SysLogDTO sysLog){
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        //获取方法
        Method method = signature.getMethod();

        //参数名
        Parameter[] parameters = method.getParameters();
        //参数值
        Object[] args = joinPoint.getArgs();

        //解析参数
        JSONObject jsonObject = new JSONObject();
        int length = parameters.length;
        for (int i = 0; i < length; i++) {
            if (args[i] instanceof HttpServletResponse){
                continue;
            }
            if (args[i] instanceof HttpServletRequest){
                continue;
            }
            jsonObject.put(parameters[i].getName(), args[i]);
        }
        //方法参数
        sysLog.setParams(JSON.toJSONString(jsonObject,SerializerFeature.IgnoreErrorGetter));
        sysLog.setParams(jsonObject.toJSONString());
    }

    /**
     * 获取用户信息
     * @param sysLog
     */
    private void handleUserInfo(String optType,String optDesc, SysLogDTO sysLog){
        //用户ID
        sysLog.setUserId(UserUtils.getUser().getId());
        //真实名称
        sysLog.setName(UserUtils.getUser().getName());
        //用户名
        sysLog.setUserName(UserUtils.getUser().getUsername());
        //正常状态
        sysLog.setStatus("1");
        //操作描述
        sysLog.setOpt(optDesc);
        //操作类型
        sysLog.setOptType(optType);
        //请求时间
        sysLog.setRequestTime(new Date());
    }

    /**
     * 日志记录
     * @param sysLog
     */
    private void saveLog(SysLogDTO sysLog){
        try {
            //响应时间
            sysLog.setResponseTime(System.currentTimeMillis() - sysLog.getRequestTime().getTime());
            //保存记录
            logService.insertLog(sysLog);
        } catch (Exception e) {
            logger.error("***SystemLogAop***around***saveLog error:", e);
        }
    }
}
