package cc.crrc.manage.common.utils.MechanismUtils;

import cc.crrc.manage.common.utils.MechanismUtils.pojo.BaseRulePO;
import cc.crrc.manage.common.utils.MechanismUtils.pojo.MechanismRule;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: <PERSON>
 * @Date: 2019-12-19
 */
public class MechanismProcessorFactory {
    /**
     * 输入机理列表获取processor实例。
     * 机理故障有修改后需要重新获取实例。
     * @param list 数据库中的机理列表
     */
    public static MechanismProcessor getProcessor(List<BaseRulePO> list) {
        // 将BaseRulePO列表转换为MechanismRule列表
        List<MechanismRule> ruleList = list.parallelStream().map(MechanismRule::new).filter(MechanismRule::isNotEmpty).collect(Collectors.toList());
        return new MechanismProcessor(ruleList);
    }

}
