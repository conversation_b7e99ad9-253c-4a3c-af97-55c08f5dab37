package cc.crrc.manage.common.utils;

import org.csource.common.NameValuePair;
import org.csource.fastdfs.*;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

import static org.csource.fastdfs.ClientGlobal.*;

@Component
public class FastDFSClient {
    private static org.slf4j.Logger logger = LoggerFactory.getLogger(FastDFSClient.class);

    private static String FASTDFS_READ_PORT;

    @Autowired
    public FastDFSClient(Environment env) {
        try {
            Properties props = new Properties();
            props.setProperty(PROP_KEY_TRACKER_SERVERS, env.getProperty(PROP_KEY_TRACKER_SERVERS));
            props.setProperty(PROP_KEY_CONNECT_TIMEOUT_IN_SECONDS, env.getProperty(PROP_KEY_CONNECT_TIMEOUT_IN_SECONDS));
            props.setProperty(PROP_KEY_NETWORK_TIMEOUT_IN_SECONDS, env.getProperty(PROP_KEY_NETWORK_TIMEOUT_IN_SECONDS));
            props.setProperty(PROP_KEY_CHARSET, env.getProperty(PROP_KEY_CHARSET));
            props.setProperty(PROP_KEY_HTTP_ANTI_STEAL_TOKEN, env.getProperty(PROP_KEY_HTTP_ANTI_STEAL_TOKEN));
            props.setProperty(PROP_KEY_HTTP_SECRET_KEY, env.getProperty(PROP_KEY_HTTP_SECRET_KEY));
            FASTDFS_READ_PORT = env.getProperty(PROP_KEY_HTTP_TRACKER_HTTP_PORT);
            if (!StringUtils.isTrimEmpty(FASTDFS_READ_PORT)) {
                props.setProperty(PROP_KEY_HTTP_TRACKER_HTTP_PORT, FASTDFS_READ_PORT);
            }
            ClientGlobal.initByProperties(props);
        } catch (Exception e) {
            logger.error("FastDFS Client Init Fail!", e);
        }
    }

    public static String[] upload(FastDFSFile file) {
        logger.info("File Name: " + file.getName() + "File Length:" + file.getContent().length);



        NameValuePair[] meta_list = new NameValuePair[1];
        meta_list[0] = new NameValuePair("author", file.getAuthor());

        long startTime = System.currentTimeMillis();
        String[] uploadResults = null;
        StorageClient storageClient=null;
        try {
            storageClient = getTrackerClient();
            uploadResults = storageClient.upload_file(file.getContent(), file.getExt(), meta_list);
        } catch (IOException e) {
            logger.error("IO Exception when uploadind the file:" + file.getName(), e);
        } catch (Exception e) {
            logger.error("Non IO Exception when uploadind the file:" + file.getName(), e);
        }
        logger.info("upload_file time used:" + (System.currentTimeMillis() - startTime) + " ms");

        if (uploadResults == null && storageClient!=null) {
            logger.error("upload file fail, error code:" + storageClient.getErrorCode());
        }
        String groupName = uploadResults[0];
        String remoteFileName = uploadResults[1];

        System.out.println(groupName);
        System.out.println(remoteFileName);
        logger.info("upload file successfully!!!" + "group_name:" + groupName + ", remoteFileName:" + " " + remoteFileName);
        return uploadResults;
    }

    public static FileInfo getFile(String groupName, String remoteFileName) {
        try {
            StorageClient storageClient = getTrackerClient();
            return storageClient.get_file_info(groupName, remoteFileName);
        } catch (IOException e) {
            logger.error("IO Exception: Get File from Fast DFS failed", e);
        } catch (Exception e) {
            logger.error("Non IO Exception: Get File from Fast DFS failed", e);
        }
        return null;
    }

    public static InputStream downFile(String groupName, String remoteFileName) {

        try {
            StorageClient storageClient = getTrackerClient();
            byte[] fileByte = storageClient.download_file(groupName, remoteFileName);
            InputStream ins = new ByteArrayInputStream(fileByte);
            return ins;
        } catch (IOException e) {
            logger.error("IO Exception: Get File from Fast DFS failed", e);
        } catch (Exception e) {
            logger.error("Non IO Exception: Get File from Fast DFS failed", e);
        }
        return null;
    }

    public static void deleteFile(String groupName, String remoteFileName)
            throws Exception {
        StorageClient storageClient = getTrackerClient();
        int i = storageClient.delete_file(groupName, remoteFileName);
        logger.info("delete file successfully!!!" + i);
    }

    public static StorageServer[] getStoreStorages(String groupName)
            throws IOException {
        TrackerClient trackerClient = new TrackerClient();
        TrackerServer trackerServer = trackerClient.getConnection();
        return trackerClient.getStoreStorages(trackerServer, groupName);
    }

    public static ServerInfo[] getFetchStorages(String groupName,
                                                String remoteFileName) throws IOException {
        TrackerClient trackerClient = new TrackerClient();
        TrackerServer trackerServer = trackerClient.getConnection();
        return trackerClient.getFetchStorages(trackerServer, groupName, remoteFileName);
    }

    public static String getTrackerUrl() throws IOException {
        String res = "http://" + getTrackerServer().getInetSocketAddress().getHostString();
        if (!StringUtils.isTrimEmpty(FASTDFS_READ_PORT)) {
            //返回带端口号的
            res = res + ":" + ClientGlobal.getG_tracker_http_port();
        }
        return res + "/";
    }

    private static StorageClient getTrackerClient() throws IOException {
        TrackerServer trackerServer = getTrackerServer();
        StorageClient storageClient = new StorageClient(trackerServer, null);
        return  storageClient;
    }

    private static TrackerServer getTrackerServer() throws IOException {
        TrackerClient trackerClient = new TrackerClient();
        TrackerServer trackerServer = trackerClient.getConnection();
        return  trackerServer;
    }


    //lx  下载文件 并还原原来的文件名字
    public static ResponseEntity<byte[]> downFilelx(String groupName, String remoteFileName, String specFileName) {
        try {
            StorageClient storageClient = getTrackerClient();
            byte[] fileByte = storageClient.download_file(groupName, remoteFileName);
            HttpHeaders headers = new HttpHeaders();
            try {
                headers.setContentDispositionFormData("attachment",  new String(specFileName.getBytes("UTF-8"),"iso-8859-1"));
                headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            } catch (Exception e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
            return new ResponseEntity<byte[]>(fileByte, headers, HttpStatus.OK);
//            return new ByteArrayInputStream(fileByte);
        } catch (IOException e) {
            logger.error("IO Exception: Get File from Fast DFS failed", e);
        } catch (Exception e) {
            logger.error("Non IO Exception: Get File from Fast DFS failed", e);
        }
        return null;
    }
    //增加返回值判断删除状态
    public static int deleteFiles(String groupName, String remoteFileName)
            throws Exception {
        StorageClient storageClient = getTrackerClient();
        int i = storageClient.delete_file(groupName, remoteFileName);
        return i;
    }
}
