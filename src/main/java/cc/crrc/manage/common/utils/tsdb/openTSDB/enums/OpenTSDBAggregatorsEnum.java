package cc.crrc.manage.common.utils.tsdb.openTSDB.enums;

public enum OpenTSDBAggregatorsEnum {
    NONE("none"),
    AVG("avg"),
    SUM("sum"),
    MIN("min"),
    MAX("max"),
    ZIMSUM("zimsum"),
    MIMMAX("mimmax"),
    MIMMIN("mimmin"),
    FIRST("first"),
    LAST("last"),
    MULT("mult"),
    DEV("dev"),
    COUNT("count"),
    P50("p50"),
    EP50R7("ep50r7"),
    EP50R3("ep50r3"),
    P75("p75"),
    EP75R3("ep75r3"),
    EP75R7("ep75r7"),
    P90("p90"),
    EP90R3("ep90r3"),
    EP90R7("ep90r7"),
    P95("p95"),
    EP95R3("ep95r3"),
    EP95R7("ep95r7"),
    P99("p99"),
    EP99R3("ep99r3"),
    EP99R7("ep99r7"),
    P999("p999"),
    EP999R7("ep999r7"),
    EP999R3("ep999r3");


    private final String value;

    OpenTSDBAggregatorsEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static OpenTSDBAggregatorsEnum fromCamelCase(String lowerCaseValue) {
        for (OpenTSDBAggregatorsEnum signalEnum : OpenTSDBAggregatorsEnum.values()) {
            if (signalEnum.value.equals(lowerCaseValue)) {
                return signalEnum;
            }
        }
        return NONE;
    }
}
