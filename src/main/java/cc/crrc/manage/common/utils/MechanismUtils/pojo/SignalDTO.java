package cc.crrc.manage.common.utils.MechanismUtils.pojo;

import com.alibaba.fastjson.JSONObject;

import java.util.Date;

/**
 * @Author: Li Caisheng
 * @Date: 2019-12-19
 */
public class SignalDTO {
    // 车辆id
    private String signalOwner;
    // 信号键值对
    private JSONObject signalJson;
    // 信号日期
    private Date signalDate;
    // 信号其他信息
    private JSONObject signalInfo;
    
    public String getSignalOwner() {
        return signalOwner;
    }
    
    public void setSignalOwner(String signalOwner) {
        this.signalOwner = signalOwner;
    }
    
    public JSONObject getSignalJson() {
        return signalJson;
    }
    
    public void setSignalJson(JSONObject signalJson) {
        this.signalJson = signalJson;
    }
    
    public Date getSignalDate() {
        return signalDate;
    }
    
    public void setSignalDate(Date signalDate) {
        this.signalDate = signalDate;
    }
    
    public JSONObject getSignalInfo() {
        return signalInfo;
    }
    
    public void setSignalInfo(JSONObject signalInfo) {
        this.signalInfo = signalInfo;
    }
}
