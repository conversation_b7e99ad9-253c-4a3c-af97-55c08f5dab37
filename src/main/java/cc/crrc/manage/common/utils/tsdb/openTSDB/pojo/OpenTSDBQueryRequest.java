package cc.crrc.manage.common.utils.tsdb.openTSDB.pojo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;

public class OpenTSDBQueryRequest {
    private String openTSDBUrl;
    private String startTime;
    private String endTime;
    private boolean msResolution;
    private ArrayList<OpenTSDBSubQuery> subQueries;

    public OpenTSDBQueryRequest(Builder builder) {
        this.startTime = builder.startTime;
        this.endTime = builder.endTime;
        this.msResolution = builder.msResolution;
        this.subQueries = builder.subQueries;
    }

    public String getOpenTSDBUrl() {
        return openTSDBUrl;
    }

    public void setOpenTSDBUrl(String openTSDBUrl) {
        this.openTSDBUrl = openTSDBUrl;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public ArrayList<OpenTSDBSubQuery> getSubQueries() {
        return subQueries;
    }

    public void setSubQueries(ArrayList<OpenTSDBSubQuery> subQueries) {
        this.subQueries = subQueries;
    }

    public boolean isMsResolution() {
        return msResolution;
    }

    public void setMsResolution(boolean msResolution) {
        this.msResolution = msResolution;
    }

    public String getPreUrl() {
        return getOpenTSDBUrl() + "/api/query";
    }

    public JSONObject toJSONObject() {
        JSONObject res = new JSONObject();
        res.put("start", getStartTime());
        res.put("msResolution", isMsResolution());
        if (getEndTime() != null) {
            res.put("end", getEndTime());
        }
        for (OpenTSDBSubQuery subQuery : subQueries) {
            if (!res.containsKey("queries")) {
                res.put("queries", new JSONArray());
            }
            JSONArray queries = res.getJSONArray("queries");
            queries.add(subQuery.toJSONObject());
        }
        return res;
    }

    public static class Builder {
        private String startTime;
        private String endTime;
        private boolean msResolution;
        private ArrayList<OpenTSDBSubQuery> subQueries;

        public Builder(String startTime) {
            this.startTime = startTime;
            this.subQueries = new ArrayList<>();
            this.msResolution = true;
        }

        public Builder setEndTime(String endTime) {
            this.endTime = endTime;
            return this;
        }

        public Builder setMsResolution(boolean msResolution) {
            this.msResolution = msResolution;
            return this;
        }

        public Builder addSubQuery(OpenTSDBSubQuery subQuery) {
            this.subQueries.add(subQuery);
            return this;
        }

        public OpenTSDBQueryRequest build() {
            return new OpenTSDBQueryRequest(this);
        }
    }
}
