package cc.crrc.manage.common.utils.tsdb.iotdb;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.tsdb.TSDBUtils;

import java.sql.*;
import java.util.ArrayList;

public class IoTDBUtils extends TSDBUtils {
    private final String url1;
    private final String userName1;
    private final String password1;
    private final String url2;
    private final String userName2;
    private final String password2;

    public IoTDBUtils(String userName1, String password1, String url1,String userName2, String password2, String url2, String driverClass) {
        this.userName1 = userName1;
        this.password1 = password1;
        this.url1 = url1;
        this.userName2 = userName2;
        this.password2 = password2;
        this.url2 = url2;
        try {
            Class.forName(driverClass);
        } catch (ClassNotFoundException e) {
            throw new ExceptionInInitializerError("加载驱动失败");
        }
    }

    /**
     * 连接数据库，可选择主从数据源
     *
     * @param source 数据库选择 1：主库 2：从库
     * @return java.sql.Connection
     * <AUTHOR> GuoYang
     * 2021/4/12
     */
    public Connection getConnection(int source) {
        try {
            Connection conn;
            if (source == 1) {
                conn =  DriverManager.getConnection(url1, userName1, password1);
            }else {
                conn =  DriverManager.getConnection(url2, userName2, password2);
            }
            return conn;
        } catch (Exception e) {
            throw new RuntimeException("链接数据库的url或用户名密码错误,请检查您的配置文件");
        }
    }

    public ArrayList<ArrayList<String>> query2IoTDB(String sql) {
        if (sql == null) {
            throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION);
        }
        return queryDataBase(sql,1);
    }

    /**
     * 查询iotdb数据库
     * 当主库连接异常、无数据时，去从库中查询。
     *
     * @param sql 查询sql
     * @param source 数据库选择 1：主库 2：从库
     * @return java.util.ArrayList<java.util.ArrayList<java.lang.String>>
     * <AUTHOR> GuoYang
     * 2021/4/12
     */
    private ArrayList<ArrayList<String>> queryDataBase(String sql,int source){
        ArrayList<ArrayList<String>> res = new ArrayList<>();
        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;
        try {
            connection = getConnection(source);
            statement = connection.createStatement();
            resultSet = statement.executeQuery(sql);
            if (resultSet != null) {
                res = readResultSet(resultSet);
                if (res.isEmpty() && source == 1) {
                    res = queryDataBase(sql, 2);
                }
            } else if (source == 1) {
                res = queryDataBase(sql, 2);
            }
        } catch (SQLException | RuntimeException e) {
            if (source == 1) {
                res = queryDataBase(sql, 2);
            }else {
                throw new RestApiException(ExceptionInfoEnum.HANDLER_SYSTEM_EXCEPTION, e.getMessage());
            }
        } finally {
            release(resultSet, statement, connection);
        }
        return res;
    }

    /**
     * 读取iotdb返回数据，并封装为ArrayList
     *
     * @param resultSet iotdb返回数据
     * @return java.util.ArrayList<java.util.ArrayList<java.lang.String>>
     * <AUTHOR> GuoYang
     * 2021/4/12
     */
    private ArrayList<ArrayList<String>> readResultSet(ResultSet resultSet) throws SQLException {
        ArrayList<ArrayList<String>> res = new ArrayList<>();
        final ResultSetMetaData metaData = resultSet.getMetaData();
        final int columnCount = metaData.getColumnCount();
        while (resultSet.next()) {
            ArrayList<String> rowInfo = new ArrayList<>();
            for (int i = 1; i < columnCount+1; i++) {
                rowInfo.add(resultSet.getString(i));
            }
            res.add(rowInfo);
        }
        return res;
    }

    /**
     * 释放资源
     * 传递三个参数: 结果集对象 ，处理Sql语句对象 , 连接对象
     * 无返回值状态
     */
    public static void release(ResultSet rs, Statement stmt, Connection conn) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
            rs = null;
        }
        if (stmt != null) {
            try {
                stmt.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
            stmt = null;
        }
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
            conn = null;
        }
    }
}

