package cc.crrc.manage.common.utils;


import cc.crrc.manage.common.exception.JsonParseException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import com.jayway.jsonpath.spi.json.JacksonJsonProvider;
import com.jayway.jsonpath.spi.mapper.JacksonMappingProvider;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.List;

/**
 * @FileName JsonPathUtils
 * <AUTHOR> shuangquan
 * @Date 2019/6/11 9:35
 **/
public final class JsonPathUtils {
    private static Configuration conf;

    static {
        conf = Configuration.builder().jsonProvider(new JacksonJsonProvider())
                .mappingProvider(new JacksonMappingProvider()).
                        options(Option.DEFAULT_PATH_LEAF_TO_NULL).build();
    }

    private JsonPathUtils() {

    }

    public static String parseString(String json, String jsonPath) throws JsonParseException {
        return parseOject(json, jsonPath, String.class);
    }

    public static int parseInt(String json, String jsonPath) throws JsonParseException {
        Integer obj = parseOject(json, jsonPath, Integer.class);
        checkObjectNull(obj, jsonPath);
        return obj.intValue();
    }

    public static double parseDouble(String json, String jsonPath) throws JsonParseException {
        Double obj = parseOject(json, jsonPath, Double.class);
        checkObjectNull(obj, jsonPath);
        return obj.doubleValue();
    }

    public static long parseLong(String json, String jsonPath) throws JsonParseException {
        Long obj = parseOject(json, jsonPath, Long.class);
        checkObjectNull(obj, jsonPath);
        return obj.longValue();
    }

    /**
     * @param json
     * @param jsonPath
     * @param clazz
     * @Description: Json字符串转List
     * @returns: java.util.List<T>
     * @Author: Dong shuangquan
     * @Date: 2019/6/11
     */
    public static <T> List<T> parseList(String json, String jsonPath, Class<T> clazz) throws IOException,
            JsonParseException {
        check(json, jsonPath);
        String sourceJson = JsonPath.read(json, jsonPath).toString();
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        JavaType javaType = mapper.getTypeFactory().constructCollectionType(List.class, clazz);
        return mapper.readValue(sourceJson, javaType);
    }


    /**
     * @param json
     * @param jsonPath
     * @param cls
     * @Description: Json字符串转对象
     * @returns: T
     * @Author: Dong shuangquan
     * @Date: 2019/6/11
     */
    public static <T> T parseOject(String json, String jsonPath, Class<T> cls) throws JsonParseException {
        check(json, jsonPath);
        return JsonPath.using(conf).parse(json).read(jsonPath, cls);
    }

    private static void check(String json, String jsonPath) throws JsonParseException {
        if (StringUtils.isEmpty(json)) {
            throw new JsonParseException("Json字符串不能为空");
        }
        if (StringUtils.isEmpty(jsonPath)) {
            throw new JsonParseException("Json path不能为空");
        }
    }

    private static void checkObjectNull(Object obj, String jsonPath) throws JsonParseException {
        if (obj == null) {
            throw new JsonParseException("Json path[" + jsonPath + "]不存在");
        }
    }
}
