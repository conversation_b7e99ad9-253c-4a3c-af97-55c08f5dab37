package cc.crrc.manage.common.utils;

import cc.crrc.manage.security.UserDetail;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * @FileName UserUtils
 * <AUTHOR> shuangquan
 * @Date 2019/9/24 10:54
 **/
public final class UserUtils {
    private UserUtils() {

    }

    public static UserDetail getUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return null;
        }
        Object user = authentication.getPrincipal();
        if (user != null) {
            return (UserDetail) user;
        }
        return null;
    }

    public static String getUserId() {
        UserDetail user = getUser();
        return user != null ? user.getId() : null;
    }

    public static String getUsername() {
        UserDetail user = getUser();
        return user != null ? user.getUsername() : null;
    }

    /*public static Long getOrganizationId() {
        UserDetail user = getUser();
        return user != null ? user.getOrganizationId() : null;
    }*/

    public static String getSuperAdmin() {
        UserDetail user = getUser();
        return user != null ? user.getIsSuperAdmin() : null;
    }


    public static String getDataPermission() {
        UserDetail user = getUser();
        return user != null ? user.getDataPermission() : null;
    }

    public static String getName() {
        UserDetail user = getUser();
        return user != null ? user.getName() : null;
    }
}
