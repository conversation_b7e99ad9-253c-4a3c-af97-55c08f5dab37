package cc.crrc.manage.common.utils.MechanismUtils.pojo;

import java.util.Date;

/**
 * @Author: Li <PERSON>g
 * @Date: 2019-12-21
 */
public interface BaseRecordPO {
    public String getSignalOwner();
    
    public void setSignalOwner(String signalOwner);
    
    public String getRuleId();
    
    public void setRuleId(String ruleId);
    
    public String getJsonStr();
    
    public void setJsonStr(String jsonStr);
    
    public Date getStartTime();
    
    public void setStartTime(Date startTime);
    
    public Date getEndTime();
    
    public void setEndTime(Date endTime);
    
}
