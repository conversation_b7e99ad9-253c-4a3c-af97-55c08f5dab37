package cc.crrc.manage.common.utils.vehicle;

import cc.crrc.manage.common.utils.RedisUtils;

/**
 * @program: phm-admin
 * @description: 车辆信息相关工具类
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2021-07-20 11:44
 **/
public class VehicleInfoUtil {

    /**
      * @Description: 判断车辆是否在线
      * @Params:
      * @Return:
      * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
      * @Date: 2021/7/20 11:45
     **/
    public static String getVehicleStatus(String trainCode) {
        if (RedisUtils.hasKey(trainCode + "_online")) {
            return "1";
        }
        else {
            return "0";
        }
    }

    public static boolean getVehicleOnlineStatus(String trainCode){
        return RedisUtils.hasKey(trainCode + "_online");
    }


}
