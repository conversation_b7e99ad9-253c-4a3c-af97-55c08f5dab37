package cc.crrc.manage.common.utils.tsdb.openTSDB;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.StringUtils;
import cc.crrc.manage.common.utils.tsdb.TSDBUtils;
import cc.crrc.manage.common.utils.tsdb.openTSDB.pojo.OpenTSDBQueryRequest;
import cc.crrc.manage.common.utils.tsdb.openTSDB.pojo.OpenTSDBQueryResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeSet;

import static cc.crrc.manage.common.utils.TimeUtils.utcToLocal;


public class OpenTSDBUtils extends TSDBUtils {
    private RestTemplate restTemplate;
    private String openTSDBUrl;

    public OpenTSDBUtils(String openTSDBUrl) {
        this.openTSDBUrl = openTSDBUrl;
        this.restTemplate = new RestTemplate();
    }

    public String queryOpenTSDB(OpenTSDBQueryRequest openTSDBQueryRequest) {
        openTSDBQueryRequest.setOpenTSDBUrl(openTSDBUrl);
        String url = openTSDBQueryRequest.getPreUrl();
        JSONObject param = openTSDBQueryRequest.toJSONObject();
        try {
            return restTemplate.postForObject(url, param, String.class);
        } catch (ResourceAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION, "数据库连接异常！");
        } catch (HttpClientErrorException e){
            String responseBody = e.getResponseBodyAsString();
            if (responseBody.contains("No such name for 'tagv'")){
                throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION, "尚无该车辆、信号相关数据！");
            }
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION, "数据库访问异常！");
        }
    }

    public JSONArray queryOpenTSDBInJSONArray(OpenTSDBQueryRequest openTSDBQueryRequest) {
        String response = queryOpenTSDB(openTSDBQueryRequest);
        return JSONArray.parseArray(response);
    }

    public static JSONArray formatOpenTSDBDps(OpenTSDBQueryResponse metric) {
        // 包括key和varValue
        JSONArray varValue = new JSONArray();
        HashMap<String, Double> dps = metric.getDps();
        // 遍历dps主键的有序集合，否则将按主键的hash排序遍历，造成时间乱序
        TreeSet<String> keySet = new TreeSet<>(dps.keySet());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss.SSS");
        for (String key : keySet) {
            JSONArray point = new JSONArray();
            // unix时间戳转 "2019/7/26 00:15:00" 格式
            Date date = new Date(Long.parseLong(key));
            point.add(0, sdf.format(date));
            point.add(1, dps.get(key));
            varValue.add(point);
        }
        return varValue;
    }

    public String queryOpenTSDB(String train_id, String start_Date, String end_Date, String ids, String metricName, String downSample, String type) {
        String[] idArr = ids.split(",");
        StringBuilder url = new StringBuilder();
        // 获得json.result需要的各指标数据数组
        String startDateLocal = utcToLocal(start_Date);
        String endDateLocal = utcToLocal(end_Date);
        url.append(openTSDBUrl);
        url.append("/api/query?start=").append(startDateLocal).append("&end=").append(endDateLocal);
        Map<String, String> params = new HashMap<>();
        for (int i = 0; i < idArr.length; i++) {
            String vid = idArr[i];
            if (StringUtils.isNotEmpty(vid)) {
                String tagKey = "tag" + i;
                url.append("&m=avg:").append(downSample).append("-last-none:").append(metricName).append("{").append(tagKey).append("}");
                // todo p_id应通过查询得到
                String tagValue = "";
                if (type == null) {
                    tagValue = String.format("{train_id=%s,p_id=109,v_id=%s}", train_id, vid);
                } else {
                    tagValue = String.format("{train_id=%s,p_id=109,v_id=%s,type=%s}", train_id, vid, type);
                }
                params.put(tagKey, tagValue);
            }
        }
        String result;
        try {
            result = new RestTemplate().getForObject(url.toString(), String.class, params);
        } catch (ResourceAccessException e) {
            e.printStackTrace();
            return "";
        }
        return result;
    }

    public String searchResult(String train_id, String start_Date, String end_Date, String ids) {
        //http API请求查询
        String result = "";
        JSONObject json = new JSONObject();
        String metricName = "train_realtime_status";
        String startDateLocal = utcToLocal(start_Date);
        String endDateLocal = utcToLocal(end_Date);
        StringBuilder url = new StringBuilder();
        url.append(openTSDBUrl);
        url.append("/api/query?start=").append(startDateLocal).append("&end=").append(endDateLocal);
        Map<String, String> params = new HashMap<>();
        String[] idArr = ids.split(",");
        for (int i = 0; i < idArr.length; i++) {
            String vid = idArr[i];
            if (StringUtils.isNotEmpty(vid)) {
                String tagKey = "tag" + i;
                url.append("&m=last:1s-last-none:").append(metricName).append("{").append(tagKey).append("}");
                String tagValue = String.format("{train_id=%s,p_id=109,v_id=%s}", train_id, vid);
                params.put(tagKey, tagValue);
            }
        }
        result = new RestTemplate().getForObject(url.toString(), String.class, params);
        if (null == result) {
            return null;
        }
        // 数据格式转化
        JSONArray resultArray = new JSONArray();
        JSONArray array = JSON.parseArray(result);
        for (int i = 0; i < array.size(); i++) {
            JSONObject aResult = new JSONObject();
            JSONObject metric = array.getJSONObject(i);
            String vid = metric.getJSONObject("tags").getString("v_id");
            aResult.put("key", vid);
//            JSONArray varValue = formatOpenTSDBDps(metric);
//            aResult.put("varValue", varValue);
            resultArray.add(aResult);
        }
        json.put("result", resultArray);
        return json.toString();
    }

}
