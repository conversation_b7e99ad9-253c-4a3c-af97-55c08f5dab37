package cc.crrc.manage.common.utils;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @FileName QueueProcesser
 * <AUTHOR> shuangquan
 * @Date 2020/6/22 18:47
 **/
public final class JSONObjectUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(JSONObjectUtils.class);
    private JSONObjectUtils(){

    }

    public static String getJsonStringValue(JSONObject result, String key,String  valueOrUnit) {
        return getJsonValue(result,key,valueOrUnit,String.class);
    }

    public static Integer getJsonIntegerValue(JSONObject result, String key,String  valueOrUnit) {
        return getJsonValue(result,key,valueOrUnit,Integer.class);
    }

    public static Float getJsonFloatValue(JSONObject result, String key,String  valueOrUnit) {
        return getJsonValue(result,key,valueOrUnit,Float.class);
    }

    public static Double getJsonDoubleValue(JSONObject result, String key,String  valueOrUnit) {
        return getJsonValue(result,key,valueOrUnit,Double.class);
    }

    public static Boolean getJsonBooleanValue(JSONObject result, String key,String  valueOrUnit) {
        return getJsonValue(result,key,valueOrUnit,Boolean.class);
    }

    public static String getJsonStringValue(JSONObject result, String key,String  valueOrUnit,String defaultValue) {
        String value = getJsonValue(result,key,valueOrUnit,String.class);
        return value==null?defaultValue:value;
    }

    public static Integer getJsonIntegerValue(JSONObject result, String key,String  valueOrUnit,Integer defaultValue) {
        Integer value = getJsonValue(result,key,valueOrUnit,Integer.class);
        return value==null?defaultValue:value;
    }

    public static Float getJsonFloatValue(JSONObject result, String key,String  valueOrUnit,Float defaultValue) {
        Float value = getJsonValue(result,key,valueOrUnit,Float.class);
        return value==null?defaultValue:value;
    }

    public static Double getJsonDoubleValue(JSONObject result, String key,String valueOrUnit,Double defaultValue) {
        Double value = getJsonValue(result,key,valueOrUnit,Double.class);
        return value==null?defaultValue:value;
    }

    public static Boolean getJsonBooleanValue(JSONObject result, String key,String  valueOrUnit,Boolean defaultValue) {
        Boolean value = getJsonValue(result,key,valueOrUnit,Boolean.class);
        return value==null?defaultValue:value;
    }

    private static <T> T getJsonValue(JSONObject result, String key,String  valueOrUnit,Class<T> clz){
        if(result==null || key==null) {
            return null;
        }
        Object obj = result.get(key);
        if (obj == null) {
            LOGGER.error("getJsonValue  error {}", key+"不存在");
            return null;
        }
        return ((JSONObject) obj).getObject(valueOrUnit,clz);
    }

}
