package cc.crrc.manage.common.utils.MechanismUtils;

import cc.crrc.manage.common.utils.MechanismUtils.pojo.BaseRecordPO;
import cc.crrc.manage.common.utils.MechanismUtils.pojo.MechanismRule;
import cc.crrc.manage.common.utils.MechanismUtils.pojo.SignalDTO;
import cc.crrc.manage.common.utils.MechanismUtils.utils.MechanismRedisUtil;
import cc.crrc.manage.common.utils.MechanismUtils.utils.RedisMapKeyEnum;
import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: Li Caisheng
 * @Date: 2020-01-04
 */
public class MechanismProcessor {
    List<MechanismRule> ruleList;
    
    /**
     * 构造器，仅供MechanismProcessorFactory调用
     * @param ruleList
     */
    MechanismProcessor(List<MechanismRule> ruleList) {
        this.ruleList = ruleList;
    }
    
    /**
     * 传入信号，计算每条rule的结果。
     * 从redis读取上次的计算结果，比对新的结果存入redis并返回
     * @param signal 车辆信号
     * @return 结果有变化的机理的记录
     */
    public List<BaseRecordPO> analyze(final SignalDTO signal) {
        //获取redis内 车 -> 时间和record
        String signalOwner = signal.getSignalOwner();
        String redisKey = MechanismRedisUtil.getRedisKeyByKey(signalOwner);
        JSONObject previous = MechanismRedisUtil.getJsonFromRedis(redisKey, RedisMapKeyEnum.MECHANISM_REDIS_PREVIOUS_KEY);
        long previousTime;
        JSONObject records = null;
        if (previous != null) {
            previousTime = previous.getLongValue("time");
            // 如果由于网络原因刚到的signal时间比redis内上次运算的信号时间还早，则不处理这包信号。
            if (signal.getSignalDate().getTime() <= previousTime) {
                return new ArrayList<>();
            }
            records = previous.getJSONObject("records");
        }
        if(records == null){
            records = new JSONObject();
        }
        
        //根据新的signal计算结果，将结果与redis内上次结果比对，输出变化的结果List
        JSONObject finalRecords = records;
        List<BaseRecordPO> changingRecords = ruleList.parallelStream()
                .filter(rule -> rule.analyzeRuleFindChanging(finalRecords, signal))
                .map(rule -> rule.changingRuleToRecord(signal))
                .collect(Collectors.toList());
        
        //将变化的结果存入redis
        for (BaseRecordPO record : changingRecords) {
            records.put(record.getRuleId(), null == record.getEndTime());
        }
        JSONObject newPrevious = new JSONObject();
        newPrevious.put("time", signal.getSignalDate().getTime());
        newPrevious.put("records", records);
        MechanismRedisUtil.setJsonIntoRedis(redisKey, RedisMapKeyEnum.MECHANISM_REDIS_PREVIOUS_KEY, newPrevious);
        
        //返回变化的records，供重写的虚拟方法record到数据库中
        return changingRecords;
    }
    
}
