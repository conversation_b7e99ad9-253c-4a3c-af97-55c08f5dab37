package cc.crrc.manage.common.utils;

import java.io.PrintWriter;
import java.io.StringWriter;

public final class ThrowableUtil {

    private ThrowableUtil(){
    }

    public static String getStackTrace(Throwable throwable){
        StringWriter sw = new StringWriter();
        try (PrintWriter pw = new PrintWriter(sw)) {
            throwable.printStackTrace(pw);
            return sw.toString();
        }
    }
}
