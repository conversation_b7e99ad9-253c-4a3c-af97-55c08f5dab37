package cc.crrc.manage.common.utils.MechanismUtils.utils;

/**
 * @Author: <PERSON>
 * @Date: 2019-12-24
 */
public enum RedisMapKeyEnum {
    MECHANISM_REDIS_PREVIOUS_KEY("previous"),
    MECHANISM_REDIS_TIMER_KEY("timer"),
    MECHANISM_REDIS_CACHE_KEY("cache");
    
    private String mapKey;
    
    private RedisMapKeyEnum(String mapKey){
        this.mapKey = mapKey;
    }
    
    public String getMapKey(){
        return mapKey;
    }
}
