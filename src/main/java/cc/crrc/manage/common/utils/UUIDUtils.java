package cc.crrc.manage.common.utils;

import java.util.Random;
import java.util.UUID;

/**
 * Created by ace on 2017/9/27.
 */
public class UUIDUtils {
    public static String[] chars = new String[]{"a", "b", "c", "d", "e", "f",
            "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s",
            "t", "u", "v", "w", "x", "y", "z", "0", "1", "2", "3", "4", "5",
            "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G", "H", "I",
            "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V",
            "W", "X", "Y", "Z"};


    public static String generateShortUuid() {
        StringBuffer shortBuffer = new StringBuffer();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        for (int i = 0; i < 8; i++) {
            String str = uuid.substring(i * 4, i * 4 + 4);
            int x = Integer.parseInt(str, 16);
            shortBuffer.append(chars[x % 0x3E]);
        }
        return shortBuffer.toString();
    }

    public static String generateUuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * @param length : 要生成多少长度的字符串
     * @param type   : 需要哪种类型
     * @Description type=0：纯数字(0-9)
     * type=1：全小写字母(a-z)
     * type=2：全大写字母(A-Z)
     * type=3: 数字+小写字母
     * type=4: 数字+大写字母
     * type=5：大写字母+小写字母
     * type=6：数字+大写字母+小写字母
     * type=7：固定长度33位：根据UUID拿到的随机字符串，去掉了四个"-"(相当于长度33位的小写字母加数字)
     */
    public static String getRandomCode(int length, int type) {
        StringBuffer buffer = null;
        StringBuffer result = new StringBuffer();
        Random random = new Random(System.currentTimeMillis());
        switch (type) {
            case 0:
                buffer = new StringBuffer("0123456789");
                break;
            case 1:
                buffer = new StringBuffer("abcdefghijklmnopqrstuvwxyz");
                break;
            case 2:
                buffer = new StringBuffer("ABCDEFGHIJKLMNOPQRSTUVWXYZ");
                break;
            case 3:
                buffer = new StringBuffer("0123456789abcdefghijklmnopqrstuvwxyz");
                break;
            case 4:
                buffer = new StringBuffer("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");
                break;
            case 5:
                buffer = new StringBuffer("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ");
                break;
            case 6:
                buffer = new StringBuffer("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789");
                result.append(buffer.charAt(random.nextInt(buffer.length() - 10)));
                length -= 1;
                break;
            case 7:
                String s = UUID.randomUUID().toString();
                result.append(s, 0, 8)
                        .append(s, 9, 13)
                        .append(s, 14, 18)
                        .append(s, 19, 23)
                        .append(s.substring(24));
                break;
            default:
                return "";
        }

        if (type != 7) {
            int range = buffer.length();
            for (int i = 0; i < length; ++i) {
                result.append(buffer.charAt(random.nextInt(range)));
            }
        }
        return result.toString();
    }
}
