package cc.crrc.manage.common.utils.MechanismUtils.utils;

import cc.crrc.manage.common.utils.RedisUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;

/**
 * @Author: <PERSON>
 * @Date: 2019-12-20
 */
public class MechanismRedisUtil {
    /**
     * 机理分析在redis内的数据结构为“mechanism_列车名” -> HashMap。HashMap的键值为
     * previous -> {"time": 上一包数据的时间, "records": 上一包数据的结果jsonObject, key为ruleId, value为true/false}
     * timer  -> {timerId: Timer}
     * cache  -> {cacheId: 缓存数据的jsonArray，每一个元素是{"time": 时间, "value“: 数值}，按时间顺序排列}
     */
    private static final String MECHANISM_REDIS_KEY_HEAD = "mechanism_";

    /**
     * redis内缓存数据有效期，默认1小时。
     */
    private static final long timeout = 60 * 60;
    
    public static String getRedisKeyByKey(String key){
        return MECHANISM_REDIS_KEY_HEAD + key;
    }
    
    public static JSONObject getJsonFromRedis(String redisKey, RedisMapKeyEnum key){
        if(!RedisUtils.hHasKey(redisKey, key.getMapKey())){
            return new JSONObject();
        }
        String value = String.valueOf(RedisUtils.hget(redisKey, key.getMapKey()));
        return JSON.parseObject(value);
    }
    
    public static boolean setJsonIntoRedis(String redisKey, RedisMapKeyEnum key, JSONObject json){
        HashMap<String, Object> map = new HashMap<>();
        map.put(key.getMapKey(), json);
        return RedisUtils.hmset(redisKey, map, timeout);
    }
}
