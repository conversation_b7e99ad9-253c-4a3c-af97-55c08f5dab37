package cc.crrc.manage.common.utils.MechanismUtils.pojo;

import java.util.Date;

/**
 * @Author: Li Caisheng
 * @Date: 2019-12-19
 */
public class MechanismRecord implements BaseRecordPO{
    private String signalOwner;
    private String ruleId;
    private String jsonStr;
    private Date startTime;
    private Date endTime;
    
    public String getSignalOwner() {
        return signalOwner;
    }
    
    public void setSignalOwner(String signalOwner) {
        this.signalOwner = signalOwner;
    }
    
    public String getRuleId() {
        return ruleId;
    }
    
    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }
    
    public String getJsonStr() {
        return jsonStr;
    }
    
    public void setJsonStr(String jsonStr) {
        this.jsonStr = jsonStr;
    }
    
    public Date getStartTime() {
        return startTime;
    }
    
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }
    
    public Date getEndTime() {
        return endTime;
    }
    
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
    
}
