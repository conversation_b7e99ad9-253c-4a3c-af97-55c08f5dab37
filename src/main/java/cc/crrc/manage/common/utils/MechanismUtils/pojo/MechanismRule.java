package cc.crrc.manage.common.utils.MechanismUtils.pojo;

import cc.crrc.manage.common.utils.LoggerUtils;
import cc.crrc.manage.common.utils.MechanismUtils.utils.MechanismRedisUtil;
import cc.crrc.manage.common.utils.MechanismUtils.utils.RedisMapKeyEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;

/**
 * @Author: Li Caisheng
 * @Date: 2019-12-19
 */
public class MechanismRule implements BaseRulePO {
    private static final Logger logger = LoggerFactory.getLogger(MechanismRule.class);
    
    private String ruleId;
    private JSONObject ruleJson;
    private String rootNodeId;
    private HashMap<String, JSONObject> nodeMap;
    private HashMap<String, JSONObject> edgeMap;
    
    private Object rootValue;
    private boolean rootValueChanging;
    
    /**
     * 构造器，将BaseRule解码变成MechanismRule
     * @param rule
     */
    public MechanismRule(BaseRulePO rule){
        try {
            ruleId = rule.getRuleId();
            ruleJson = JSON.parseObject(rule.getRuleJsonString());
            JSONObject source = ruleJson.getJSONObject("source");
            JSONArray nodeArray = source.getJSONArray("nodes");
            JSONArray edgeArray = source.getJSONArray("edges");
            edgeMap = new HashMap<>();
            for (int j = 0; j < edgeArray.size(); j++) {
                JSONObject edge = edgeArray.getJSONObject(j);
                String targetAnchor = edge.getString("targetAnchor");
                if (targetAnchor == null || "".equals(targetAnchor)) {
                    targetAnchor = "0";
                }
                String edgeKey = edge.getString("target") + "_" + targetAnchor;
                edgeMap.put(edgeKey, edge);
            }
            nodeMap = new HashMap<>();
            for (int i = 0; i < nodeArray.size(); i++) {
                JSONObject node = nodeArray.getJSONObject(i);
                String nodeId = node.getString("id");
                nodeMap.put(nodeId, node);
                String shape = node.getString("shape");
                if ("f".equals(shape)) {
                    rootNodeId = nodeId;
                }
            }
        } catch (Exception e){
            LoggerUtils.error(logger, e);
        }
    }
    
    /**
     * @return 根节点id不为空，则MechanismRule构造成功
     */
    public boolean isNotEmpty(){
        return null != rootNodeId && !rootNodeId.isEmpty();
    }
    
    public BaseRecordPO changingRuleToRecord(SignalDTO signal){
        MechanismRecord record = new MechanismRecord();
        record.setSignalOwner(signal.getSignalOwner());
        record.setRuleId(ruleId);
//        JSONArray nodes = new JSONArray();
//        nodes.addAll(nodeMap.values());
//        JSONArray edges = new JSONArray();
//        edges.addAll(edgeMap.values());
        JSONObject source = new JSONObject();
        source.put("nodes", nodeMap.values());
        source.put("edges", edgeMap.values());
//        source.put("nodes", nodes);
//        source.put("edges", edges);
        JSONObject treeJson = new JSONObject();
        treeJson.put("guides", new JSONArray());
        treeJson.put("source", source);
        record.setJsonStr(treeJson.toJSONString());
        if(rootValueChanging) {
            if (Boolean.TRUE.equals(rootValue)) {
                record.setStartTime(signal.getSignalDate());
            }
            if (Boolean.FALSE.equals(rootValue)) {
                record.setEndTime(signal.getSignalDate());
            }
        }
        return record;
    }
    
    /**
     * 进行树图计算
     * @param signal 信号
     * @return 如果计算结果与历史记录不同则返回ture， 计算结果与历史记录相同或计算异常返回false
     */
    public boolean analyzeRuleFindChanging(JSONObject records, SignalDTO signal){
        rootValue = calNode(rootNodeId, signal);
        if(null == rootValue){
            rootValueChanging = false;
        }
        Boolean record = records.getBoolean(ruleId);
        rootValueChanging = !rootValue.equals(record);
        return rootValueChanging;
    }
    
    private Object calNode(String nodeId, SignalDTO signal) {
        JSONObject node = nodeMap.get(nodeId);
        if(null == node){
            // ruleJson错误, 找不到指定nodeId的node
            logger.error("Node is not found, ruleJson is wrong. ruleId = {}, nodeId = {}.", ruleId, nodeId);
            return null;
        }
        String nodeShape = node.getString("shape");
        switch (nodeShape){
            case "f":
                // 根节点
                String edgeKey = nodeId + "_0";
                JSONObject edge = edgeMap.get(edgeKey);
                if(null == edge){
                    // 没有箭头指向根节点
                    logger.error("rootNode has no preNode, ruleId = {}", ruleId);
                    return null;
                }
                String preNodeId = edge.getString("source");
                Object result = calNode(preNodeId, signal);
                if (result != null) {
                    node.put("value", result.toString());
                }
                return result;
            case "v-a":
                // 模拟节点
                String signalId = node.getString("signal_id");
                String value = signal.getSignalJson().getString(signalId);
                if(null == value){
                    // 车辆信号解析结果中找不到指定的模拟量
                    logger.error("Data is not in signals, ruleId = {}, signal_id = {}", ruleId, signalId);
                    return null;
                }
                Double va = Double.valueOf(value);                       // 整数和浮点数的模拟量都返回Double
                node.put("value", va);
                return va;
            case "v-d":
                // 数字节点
                signalId = node.getString("signal_id");
                value = signal.getSignalJson().getString(signalId);
                if(null == value){
                    // 车辆信号解析结果中找不到指定的模拟量
                    logger.error("Data is not in signals, ruleId = {}, signal_id = {}", ruleId, signalId);
                    return null;
                }
                Boolean vd = "1".equals(value);
                node.put("value", vd.toString());
                return vd;
            default:
                // 逻辑节点, 递归求所有前节点的值, 然后计算
                int inputCount = node.getIntValue("inputCount");
                Object[] inputs = new Object[inputCount];
                for(int i=0; i<inputCount; i++){
                    edgeKey = nodeId +"_"+ i;
                    edge = edgeMap.get(edgeKey);
                    String inKey = "in" + (i+1);
                    // 输入值来自于箭头连接的前一节点
                    if(null != edge){
                        JSONObject preNode = nodeMap.get(edge.getString("source"));
                        preNodeId = preNode.getString("id");
                        inputs[i] = calNode(preNodeId, signal);
                        if(null == inputs[i]){
                            // 之前的节点计算过程中有异常
                            return null;
                        }
                        // 将之前节点的计算结果放入json中，用来显示key为in1/in2……
                        node.put(inKey, inputs[i].toString());
                    }
                    // 没有连接箭头, 读取节点内预设值
                    else{
                        // 预设值都是模拟值
                        inputs[i] = node.getDouble(inKey);
                        if (null == inputs[i]){
                            // 逻辑节点输入点既没有连接箭头, 也没有预设值
                            logger.error("There is no input value, ruleId = {}, nodeId = {}", ruleId, nodeId);
                            return null;
                        }
                    }
                }
                String id = node.getString("id");
                switch (nodeShape){
                    case "f-ton":
                        result = calTon(id, inputs, signal);
                        break;
                    // todo 其它计时器和聚合计算能功能模块
                    default:
                        result = calculate(nodeShape, inputs);
                }
                if(null != result) {
                    node.put("out", result.toString());
                }
                return result;
        }
    }
    
    /**
     * 计算通电延时计时器
     * @param id json中的节点id
     * @param inputs 计时器输入节点的输入值，input[0]是输入的数字量，input[1]是延时时间，单位秒
     * @param signal 车辆信号
     * @return
     */
    private Boolean calTon(String id, Object[] inputs, SignalDTO signal) {
        Boolean output;
        try{
            String redisKey = MechanismRedisUtil.getRedisKeyByKey(signal.getSignalOwner());
            JSONObject timerJson = MechanismRedisUtil.getJsonFromRedis(redisKey, RedisMapKeyEnum.MECHANISM_REDIS_TIMER_KEY);
            Timer timer = timerJson.getObject(id, Timer.class);
            if(null == timer){
                // todo 当添加其他延时计时器时，只要将nodeShape传入，并据其将timer新建为不同的Timer实现类即可
                timer = new TimerKeepOnDelay(id);
            }
            boolean in = (boolean) inputs[0];
            double ptS = (double) inputs[1];
            long signalTime = signal.getSignalDate().getTime();
            output = timer.updateAndOutput(signalTime, in, ptS);
            timerJson.put(id, timer);
            MechanismRedisUtil.setJsonIntoRedis(redisKey, RedisMapKeyEnum.MECHANISM_REDIS_TIMER_KEY, timerJson);
        } catch (Exception e){
            logger.error(e.getMessage(), e);
            return null;
        }
        return output;
    }
    
    // 逻辑节点运算, 输入类型可能为Boolean和Double
    private Object calculate(String nodeShape, Object[] inputs) {
        try {
            switch (nodeShape) {
                case "f-abs":
                    return Math.abs((double) inputs[0]);
                case "f-add":
                    return (double) inputs[0] + (double) inputs[1];
                case "f-mul":
                    return (double) inputs[0] * (double) inputs[1];
                case "f-div":
                    return (double) inputs[0] / (double) inputs[1];
                case "f-mod":
                    return (int) inputs[0] % (int) inputs[1];
                case "f-eq":
                    // 输入同为Boolean，或同为Double，且值相同时为true
                    return inputs[0].equals(inputs[1]);
                case "f-ne":
                    return !inputs[0].equals(inputs[1]);
                case "f-ge":
                    return (double) inputs[0] >= (double) inputs[1];
                case "f-gt":
                    return (double) inputs[0] > (double) inputs[1];
                case "f-le":
                    return (double) inputs[0] <= (double) inputs[1];
                case "f-lt":
                    return (double) inputs[0] < (double) inputs[1];
                case "f-len":
                    // TODO f-len 意义不明
                    return (double) inputs[0].toString().length();
                case "f-limit":
                    double inputs0 = (double) inputs[0];
                    double inputs1 = (double) inputs[1];
                    double inputs2 = (double) inputs[2];
                    return inputs1 <= inputs0 ? inputs0 : (inputs1 >= inputs2 ? inputs2 : inputs1);
                case "f-ln":
                    return Math.log((double) inputs[0]);
                case "f-log":
                    return Math.log10((double) inputs[0]);
                case "f-max":
                    return Math.max((double) inputs[0], (double) inputs[1]);
                case "f-min":
                    return Math.min((double) inputs[0], (double) inputs[1]);
                case "f-and":
                    return (boolean) inputs[0] && (boolean) inputs[1];
                case "f-or":
                    return (boolean) inputs[0] || (boolean) inputs[1];
                case "f-not":
                    return !(boolean) inputs[0];
                case "f-xor":
                    return (boolean) inputs[0] ^ (boolean) inputs[1];
                case "f-sin":
                    return Math.sin(Math.toRadians((double) inputs[0]));
                case "f-cos":
                    return Math.cos(Math.toRadians((double) inputs[0]));
                case "f-tan":
                    return Math.tan(Math.toRadians((double) inputs[0]));
                default:
                    logger.error("Unknown nodeShape. ruleId = {}, nodeShape = {}.", ruleId, nodeShape);
                    return null;
            }
        } catch (Exception e) {
            logger.error("Calculate error. ruleId = {}, nodeShape = {}.\n{}", ruleId, nodeShape, e);
            return null;
        }
    }
        
    @Override
    public String getRuleJsonString() {
        return ruleJson.toJSONString();
    }
    
    @Override
    public String getRuleId() {
        return ruleId;
    }
    
    public Object getRootValue() {
        return rootValue;
    }
}
