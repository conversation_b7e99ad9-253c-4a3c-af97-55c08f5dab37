package cc.crrc.manage.common.utils;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.service.external.TracksideOnlineMonitorService;
import io.minio.*;
import io.minio.errors.*;
import io.minio.messages.Item;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName MinioUtils
 * @Description minio工具类
 * <AUTHOR> kangjian
 * @Date 2023/10/25 15:35
 **/
@Component
@Configuration
public class MinioUtils {

    private static final String BUCKET_NAME = "shaoxing2-nuoli";
    private static final boolean NOT_SORT = false;
    private final Logger logger = LoggerFactory.getLogger(MinioUtils.class);

    public List<String> listObjectNames(String bucketName, String prefix)  {
        return listObjectNames(bucketName, prefix, NOT_SORT);
    }

    public List<String> listObjectNames(String bucketName, String prefix, Boolean sort) {
        try {

            ListObjectsArgs listObjectsArgs;
            if (null == prefix) {
                listObjectsArgs = ListObjectsArgs.builder()
                        .bucket(bucketName)
                        .recursive(true)
                        .build();
            } else {
                listObjectsArgs = ListObjectsArgs.builder()
                        .bucket(bucketName)
                        .prefix(prefix)
                        .recursive(true)
                        .build();
            }
            Iterable<Result<Item>> chunks = SpringBeanUtils.getBean(MinioClient.class).listObjects(listObjectsArgs);
            List<String> chunkPaths = new ArrayList<>();
            for (Result<Item> item : chunks) {
               if( item.get().objectName().contains(".")){
                chunkPaths.add(item.get().objectName());}
            }
            if (sort) {
                chunkPaths.sort(new Str2IntComparator(false));
            }
            return chunkPaths;
        } catch (Exception e) {
            logger.error("Method[getWheelImages] Error:{}", e.getMessage());
        }
        return new ArrayList<>();
    }

    static class Str2IntComparator implements Comparator<String> {
        private final boolean reverseOrder;

        public Str2IntComparator(boolean reverseOrder) {
            this.reverseOrder = reverseOrder;
        }

        @Override
        public int compare(String o1, String o2) {
            Integer intArg0 = Integer.parseInt(o1.substring(o1.indexOf("/") + 1, o1.lastIndexOf(".")));
            Integer intArg1 = Integer.parseInt(o2.substring(o2.indexOf("/") + 1, o2.lastIndexOf(".")));
            if (reverseOrder) {
                return intArg1 - intArg0;
            } else {
                return intArg0 - intArg1;
            }
        }
    }
    //上传功能仅供测试图片使用，正常业务中，上传功能由第三方诺丽上传图片。我处负责读取。
    public static String fileUpload(MultipartFile multipartFile,String path){
        try {

            String prefix=path;//测试路径（具体路径自行修改）
            //判断某一个桶是否存在
            boolean bucketExists = SpringBeanUtils.getBean(MinioClient.class).bucketExists(BucketExistsArgs.builder().bucket(BUCKET_NAME).build());

            //不存在就创建桶
            if (!bucketExists){
                SpringBeanUtils.getBean(MinioClient.class).makeBucket(MakeBucketArgs.builder().bucket(BUCKET_NAME).build());
            }

            //调用上传文件的方法上传文件
            InputStream fileInputStream = multipartFile.getInputStream();

            String FileExtName = multipartFile.getOriginalFilename();
            String filename =  prefix+FileExtName;//prefix路径即可生产文件夹
            SpringBeanUtils.getBean(MinioClient.class).putObject(
                    PutObjectArgs.builder().bucket(BUCKET_NAME).object(prefix).stream(
                            new ByteArrayInputStream(new byte[] {}), 0, -1)
                            .build());
            PutObjectArgs putObjectArgs = PutObjectArgs.builder()
                    .bucket(BUCKET_NAME)   //指定桶的名称
                    .object(filename)    //指定上传以后的文件在minio中的文化路径和名称
                    .stream(fileInputStream, fileInputStream.available(), -1)
                    .build();
            SpringBeanUtils.getBean(MinioClient.class).putObject(putObjectArgs);

            //返回文件的访问路径
            return "ok";
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }


    /**
     * 定时删除对象，每天0点执行一次
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public static void removeObject() throws Exception {


        ListObjectsArgs listObjectsArgs;
        listObjectsArgs = ListObjectsArgs.builder()
                .bucket(BUCKET_NAME)
                .recursive(true)
                .build();
        Iterable<Result<Item>> chunks = SpringBeanUtils.getBean(MinioClient.class).listObjects(listObjectsArgs);


    for (Result<Item> item : chunks) {
        String name = URLDecoder.decode(item.get().objectName(), "UTF-8");
        String fileName=name;
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
         if (name.length()>10){
             name=name.substring(0,10);
             name=name.replaceAll("/","-");
        Date time =dateFormat.parse(name);
        // 获取当前时间
        Date now = new Date();
        // 获取calendar
        Calendar calendar = Calendar.getInstance();
        // 设置时间
        calendar.setTime(now);
        // 将时间调整到7天前
        calendar.add(Calendar.DAY_OF_MONTH, -3);
        // 获取时间
        Date beforeTime = calendar.getTime();
        // 比较时间
        if(beforeTime.getTime() < time.getTime()){
        }else{
            RemoveObjectArgs args = RemoveObjectArgs.builder().bucket(BUCKET_NAME).object(fileName).build();
            SpringBeanUtils.getBean(MinioClient.class).removeObject(args);
           }

         }
      }
    }
}
