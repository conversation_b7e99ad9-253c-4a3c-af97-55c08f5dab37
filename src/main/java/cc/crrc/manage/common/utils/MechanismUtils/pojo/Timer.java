package cc.crrc.manage.common.utils.MechanismUtils.pojo;


/**
 * 计时器抽象父类
 * @Author: Li <PERSON>heng
 * @Date: 2019-09-18
 */
public abstract class Timer {
    /**
     * 机理模型json中该计时器节点的id
     */
    String id;
    /**
     * input == true 的开始时间戳，单位毫秒
     * 若计时器处在未计时状态则startTime = -1
     */
    long startTime;
    /**
     * input == true 的开始时间戳，单位毫秒
     * 若计时器处在未计时状态则lastTime = -1
     */
    long lastTime;
    /**
     * 最大时间间隔，2个数据间的时间间隔大于这个值时，清除计时器中的老数据，不累加这两包数据间的时间。
     * 单位毫秒，默认为1分钟
     */
    double maxIntervalMs = 60000;
    
    /**
     * 构造器
     * @param id 计时器在机理json中的id
     */
    public Timer(String id){
        this.id = id;
        this.startTime = -1;
        this.lastTime = -1;
    }
    
    /**
     * 接收最新数据包信号，更新计时器，返回输出值
     * @param newTime   新数据包时间戳，单位毫秒
     * @param in        计时器输入值
     * @param ptS       计时器延迟时间，单位秒
     * @return          计时器输出值
     */
    public abstract boolean updateAndOutput(long newTime, boolean in, double ptS);
    
    /**
     * 判断之前计时器是否未在计时
     * @param newTime   新数据包的时间戳
     * @return          当startTime为负，或者新数据包与计时器记录的上一包数据时间间隔过大时，计时器未在计时
     */
    boolean timerWasClosed(long newTime){
        return startTime < 0 || (newTime-lastTime)>=maxIntervalMs;
    }
    
    // 以下get set方法
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public long getStartTime() {
        return startTime;
    }
    
    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }
    
    public long getLastTime() {
        return lastTime;
    }
    
    public void setLastTime(long lastTime) {
        this.lastTime = lastTime;
    }
    
    public double getMaxIntervalMs() {
        return maxIntervalMs;
    }
    
    public void setMaxIntervalMs(double maxIntervalMs) {
        this.maxIntervalMs = maxIntervalMs;
    }
}
