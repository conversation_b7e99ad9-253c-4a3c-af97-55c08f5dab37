package cc.crrc.manage.common.utils;

import java.util.Collections;
import java.util.List;

/**
 * @FileName Page
 * <AUTHOR> shuangquan
 * @Date 2020/3/4 10:06
 **/
public class Page<T> {
    private Integer pageNum;
    private Integer pageSize;

    private Integer total;
    private List<T> list = Collections.emptyList();

    public Page() {

    }

    public Page(Integer pageNum, Integer pageSize) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }


    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }


    public int[] getPageParams(Integer total) {
        if (this.pageNum == null || this.pageNum < 1) {
            this.pageNum = 1;
        }
        if (this.pageSize == null || this.pageSize < 1) {
            this.pageSize = 10;
        }
        int firstResult = (this.pageNum - 1) * this.pageSize;
        int maxResult = this.pageSize;
        this.total = total;


        if (firstResult >= total || firstResult < 0) {
            firstResult = 0;
            this.pageNum = 1;
        }
        return new int[]{firstResult, maxResult};
    }
}
