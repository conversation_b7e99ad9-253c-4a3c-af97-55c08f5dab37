package cc.crrc.manage.common.utils;


import cc.crrc.manage.common.vo.TreeNodeForStructureVO;
import cc.crrc.manage.common.vo.TreeNodeVO;
import cc.crrc.manage.pojo.SysDictVO;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

public class TreeUtil<T extends TreeNodeVO> {
    /**
     * 两层循环实现建树
     *
     * @param treeNodes  传入的树节点列表
     * @param comparator
     * @return
     */
    public static <T extends TreeNodeVO> List<T> bulid(List<T> treeNodes, Object root, Comparator comparator) {
        List<T> trees = new ArrayList<T>();
        for (T treeNode : treeNodes) {

            if (root.equals(treeNode.getParentId())) {
                trees.add(treeNode);
            }

            for (T it : treeNodes) {
                if (it.getParentId().equals(treeNode.getId())) {
                    if (treeNode.getChildren() == null) {
                        treeNode.setChildren(new ArrayList<>());
                    }
                    treeNode.add(it);
                }
            }
            if (comparator != null) {
                List children = treeNode.getChildren();
                treeNode.getChildren().sort(comparator);
            }
        }
        return trees;
    }

    /**
     * 提供车辆构型
     */
    public static <T extends TreeNodeForStructureVO<T>> List<T> buildForStructure(List<T> treeNodes, Object root, Comparator<TreeNodeForStructureVO<T>> comparator,List<SysDictVO> subSystemList) {
        String SYSTEM_TYPE_NAME = "系统";
        List<T> trees = new ArrayList<T>();
        treeNodes.sort((obj1, obj2) -> {
            boolean o1System = SYSTEM_TYPE_NAME.equals(obj1.getStructureType());
            boolean o2System = SYSTEM_TYPE_NAME.equals(obj2.getStructureType());
            if (o1System && !o2System){
                return 1;
            }else if (o2System && !o1System){
                return -1;
            }else {
                return 0;
            }
        });
        if (comparator == null) {
            // 比较sortNumber，大的在后；若sortNumber为null，则排在最后。
            comparator = Comparator.comparing(TreeNodeForStructureVO::getSortNumber,Comparator.nullsLast(Comparator.naturalOrder()));
        }
        for (T treeNode : treeNodes) {
            if (root.equals(treeNode.getParentStructureCode())) {
                trees.add(treeNode);
            }
            for (T it : treeNodes) {
                if (it.getParentStructureCode().equals(treeNode.getStructureCode())) {
                    if (treeNode.getChildren() == null) {
                        treeNode.setChildren(new ArrayList<>());
                    }
                    treeNode.add(it);
                }
            }
            // 排序
            List<T> children = treeNode.getChildren();
            children.sort(comparator);
            // 子系统处理
            if (SYSTEM_TYPE_NAME.equals(treeNode.getStructureType())){
                String systemKey = null;
                String systemName = treeNode.getNameCn();
                if (subSystemList!=null&& !subSystemList.isEmpty()){
                    for (SysDictVO sysDictVO : subSystemList) {
                        if (sysDictVO.getLabel().equals(systemName)){
                            systemKey = sysDictVO.getValue();
                        }
                    }
                }
                treeNode.setSystemKey(systemKey);
                treeNode.setSubSystem(systemName);
                updateChildrenSubSystem(treeNode,systemName,systemKey);
            }
            // 节点类型处理
            if (treeNode.getChildren().isEmpty()){
                treeNode.setNodeType("叶节点");
            }else if(root.equals(treeNode.getStructureCode())){
                treeNode.setNodeType("根节点");
            }else {
                treeNode.setNodeType("子节点");
            }
        }
        // 对最外层进行排序
        trees.sort(comparator);
        return trees;
    }

    public static <T extends TreeNodeForStructureVO> List<T> buildForStructure(List<T> treeNodes, Object root, Comparator comparator) {
        return buildForStructure(treeNodes,root,comparator,null);
    }

    private static <T extends TreeNodeForStructureVO> void updateChildrenSubSystem(T treeNode,String subSystem,String systemKey){
        List<T> children = treeNode.getChildren();
        for (T child : children) {
            child.setSubSystem(subSystem);
            child.setSystemKey(systemKey);
            updateChildrenSubSystem(child,subSystem,systemKey);
        }
    }

    /**
     * 使用递归方法建树
     *
     * @param treeNodes
     * @return
     */
    public static <T extends TreeNodeVO> List<T> buildByRecursive(List<T> treeNodes, Object root) {
        List<T> trees = new ArrayList<T>();
        for (T treeNode : treeNodes) {
            if (root.equals(treeNode.getParentId())) {
                trees.add(findChildren(treeNode, treeNodes));
            }
        }
        return trees;
    }

    /**
     * 递归查找子节点
     *
     * @param treeNodes
     * @return
     */
    public static <T extends TreeNodeVO> T findChildren(T treeNode, List<T> treeNodes) {
        for (T it : treeNodes) {
            if (treeNode.getId().equals(it.getParentId())) {
                if (treeNode.getChildren() == null) {
                    treeNode.setChildren(new ArrayList<TreeNodeVO>());
                }
                treeNode.add(findChildren(it, treeNodes));
            }
        }
        return treeNode;
    }
}