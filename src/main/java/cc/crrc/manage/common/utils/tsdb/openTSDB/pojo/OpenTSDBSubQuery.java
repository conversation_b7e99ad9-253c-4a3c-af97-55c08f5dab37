package cc.crrc.manage.common.utils.tsdb.openTSDB.pojo;

import cc.crrc.manage.common.utils.tsdb.openTSDB.enums.OpenTSDBAggregatorsEnum;
import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class OpenTSDBSubQuery {
    private String metric;
    private OpenTSDBAggregatorsEnum aggregator;
    private Map<String, String> tagMap;
    private DownSampling downSampling;

    public enum DownSamplingPoliceEnum {
        NONE("none"),
        NAN("nan"),
        NULL("null"),
        ZERO("zero");
        private final String value;

        DownSamplingPoliceEnum(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    public static class DownSampling {
        private String interval;
        private OpenTSDBAggregatorsEnum aggregator;
        private DownSamplingPoliceEnum police;

        public DownSampling(Builder builder) {
            this.interval = builder.interval;
            this.aggregator = builder.aggregator;
            this.police = builder.police;
        }

        public String getInterval() {
            return interval;
        }

        public void setInterval(String interval) {
            this.interval = interval;
        }

        public OpenTSDBAggregatorsEnum getAggregator() {
            return aggregator;
        }

        public void setAggregator(OpenTSDBAggregatorsEnum aggregator) {
            this.aggregator = aggregator;
        }

        public DownSamplingPoliceEnum getPolice() {
            return police;
        }

        public void setPolice(DownSamplingPoliceEnum police) {
            this.police = police;
        }

        public static class Builder {
            private String interval;
            private OpenTSDBAggregatorsEnum aggregator;
            private DownSamplingPoliceEnum police;

            public Builder(String interval, OpenTSDBAggregatorsEnum aggregator) {
                this.interval = interval;
                this.aggregator = aggregator;
            }

            public Builder setDownSamplingPolice(DownSamplingPoliceEnum police) {
                this.police = police;
                return this;
            }

            public DownSampling build() {
                return new DownSampling(this);
            }
        }
    }

    public OpenTSDBSubQuery(Builder builder) {
        this.metric = builder.metric;
        this.aggregator = builder.aggregator;
        this.tagMap = builder.tagMap;
        this.downSampling = builder.downSampling;
    }

    public String getMetric() {
        return metric;
    }

    public void setMetric(String metric) {
        this.metric = metric;
    }

    public OpenTSDBAggregatorsEnum getAggregator() {
        return aggregator;
    }

    public void setAggregator(OpenTSDBAggregatorsEnum aggregator) {
        this.aggregator = aggregator;
    }

    public Map<String, String> getTagMap() {
        return tagMap;
    }

    public void setTagMap(Map<String, String> tagMap) {
        this.tagMap = tagMap;
    }

    public DownSampling getDownSampling() {
        return downSampling;
    }

    public void setDownSampling(DownSampling downSampling) {
        this.downSampling = downSampling;
    }

    public String getDownSamplingString() {
        StringBuilder downSampleBuilder = new StringBuilder();
        downSampleBuilder.append(this.getDownSampling().getInterval()).append("-");
        downSampleBuilder.append(this.getDownSampling().getAggregator().getValue());
        if (this.getDownSampling().getPolice() != null) {
            downSampleBuilder.append("-").append(this.getDownSampling().getPolice().getValue());
        }
        return downSampleBuilder.toString();
    }

    public JSONObject toJSONObject() {
        JSONObject res = new JSONObject();
        res.put("metric", getMetric());
        res.put("aggregator", getAggregator().getValue());
        if (downSampleNotEmpty()) {
            res.put("downsample", getDownSamplingString());
        }
        if (tagMapNotEmpty()) {
            res.put("tags", JSONObject.parseObject(JSONObject.toJSONString(getTagMap())));
        }
        return res;
    }

    private boolean tagMapNotEmpty() {
        return getTagMap() != null && !getTagMap().isEmpty();
    }

    private boolean downSampleNotEmpty(){
        return getDownSampling() != null && getDownSampling().getInterval() != null;
    }

    public static class Builder {
        private String metric;
        private OpenTSDBAggregatorsEnum aggregator;
        private Map<String, String> tagMap;
        private DownSampling downSampling;

        public Builder(String metric, OpenTSDBAggregatorsEnum aggregator) {
            this.metric = metric;
            this.aggregator = aggregator;
        }

        public Builder setTag(Map<String, String> tagMap) {
            this.tagMap = tagMap;
            return this;
        }

        public Builder putTag(String key, String tagValue) {
            if (this.tagMap == null) {
                this.tagMap = new HashMap<>();
            }
            this.tagMap.put(key, tagValue);
            return this;
        }

        public Builder setDownSampling(DownSampling downSampling) {
            this.downSampling = downSampling;
            return this;
        }

        public OpenTSDBSubQuery build() {
            return new OpenTSDBSubQuery(this);
        }

    }
}
