package cc.crrc.manage.common.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * @description:
 * @author: dongshuangquan
 * @createDate: 2021/4/14
 * @version: 1.0
 */
public final class PrimaryKeyGenerator {

    private static long machineId;
    private static long sequence = 0L;
    private static long lastTimeStamp = -1L;

    static{
        String machineIdStr = System.getenv(Constants.MACHINE_ID_ENV_KEY);
        if(StringUtils.isEmpty(machineIdStr)){
            machineId = 0L;
        }else {
            machineId = Long.parseLong(machineIdStr);
        }
        if (machineId > Constants.MAX_MACHINE_NUM || machineId < 0) {
            throw new IllegalArgumentException("机器码超出范围");
        }
    }

    private PrimaryKeyGenerator(){

    }

    public static synchronized String generatorId() {
        if (Constants.PRIMARY_KEY_DATA_CENTER_ID > Constants.MAX_DATA_CENTER_NUM || Constants.PRIMARY_KEY_DATA_CENTER_ID < 0) {
            throw new IllegalArgumentException("数据标识超出范围");
        }
        long currentTimeStamp = getCurrentTimeStamp();
        if (currentTimeStamp < lastTimeStamp) {
            throw new RuntimeException("时间戳回退，拒绝生成主键");
        }

        if (currentTimeStamp == lastTimeStamp) {
            sequence = (sequence + 1) & Constants.MAX_SEQUENCE;
            if (sequence == 0L) {
                currentTimeStamp = getNextTimeStamp();
            }
        } else {
            sequence = 0L;
        }
        lastTimeStamp = currentTimeStamp;
        return String.valueOf((currentTimeStamp - Constants.START_DATESTAMP) << Constants.TIMESTAMP_LEFT
                | Constants.PRIMARY_KEY_DATA_CENTER_ID << Constants.DATA_CENTER_LEFT
                | machineId << Constants.MACHINE_LEFT
                | sequence);
    }

    private static long getNextTimeStamp() {
        long currentTimeStamp = getCurrentTimeStamp();
        while (currentTimeStamp <= lastTimeStamp) {
            currentTimeStamp = getCurrentTimeStamp();
        }
        return currentTimeStamp;
    }

    private static long getCurrentTimeStamp() {
        return System.currentTimeMillis();
    }

}
