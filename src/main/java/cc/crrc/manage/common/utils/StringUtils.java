package cc.crrc.manage.common.utils;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 *
 * <AUTHOR>
 * 2019/11/9
 **/
public final class StringUtils {

    public static String getFullSpell(String source) {
        StringBuilder res = new StringBuilder();
        char[] upperSrcArr = source.toUpperCase().toCharArray();
        HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();
        defaultFormat.setCaseType(HanyuPinyinCaseType.UPPERCASE);
        defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        for (char upperChar : upperSrcArr) {
            if (upperChar > 128) {
                try {
                    res.append(PinyinHelper.toHanyuPinyinStringArray(upperChar, defaultFormat)[0]);
                } catch (BadHanyuPinyinOutputFormatCombination e) {
                    e.printStackTrace();
                }
            } else {
                res.append(upperChar);
            }
        }
        return res.toString();
    }

    public static boolean isEmpty(String source){
        return source == null || source.isEmpty();
    }

    public static boolean isTrimEmpty(String source){
        return source == null || source.trim().isEmpty();
    }

    public static boolean isNotEmpty(String source){
        return !isEmpty(source);
    }

    public static boolean isValidDate(String str) {
        boolean convertSuccess = true;
        SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        try {
            format.setLenient(false);
            format.parse(str);
        } catch (ParseException | NullPointerException e) {
            convertSuccess = false;
        }
        return convertSuccess;
    }

    public static String list2String(List<String> list) {
        StringBuilder sb = new StringBuilder();
        sb.append('[');
        for (int i = 0; i < list.size(); i++) {
            sb.append('"').append(list.get(i)).append('"');
            if (i != list.size() - 1) {
                sb.append(',');
            }
        }
        sb.append(']');
        return sb.toString();
    }


    private static final  String[] fbsArr = { "\\", "$", "|","%","_" , "(", ")", "*", "+", ".", "[", "]", "?", "^", "{", "}"};
    public static String disposePerCent(String target){
        if (StringUtils.isNotEmpty(target))
            for (String regex : fbsArr) {
                if (target.contains(regex))
                    target = target.replace(regex,"\\" + regex);
            }
        return target;
    }

    /**
     * @Description 将给定的字符串按照指定的正则拆分并拼成字符串
     * @Param str：给定的字符串 regex：正则表达式
     * @Return String
     * <AUTHOR> zhijian
     * @Date 2020/7/30 16:35
     */
    public static String stringJoint(String str, String regex){
        String[] strArray = str.split(regex);
        StringBuilder builder = new StringBuilder();
        for (int i=0;i<strArray.length;i++){
            builder.append(strArray[i]);
        }
        return builder.toString();
    }
}
