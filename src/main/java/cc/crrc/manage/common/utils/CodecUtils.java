package cc.crrc.manage.common.utils;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Random;
import java.util.function.Supplier;

/**
 * Encodes a password
 *
 * <AUTHOR> <PERSON>
 * @date 2023/03/29
 */
public class CodecUtils {

    public static final String regexp = "^^(?![a-zA-Z]+$)(?!\\d+$)(?![!@#$%^&*_-]+$)(?![a-zA-Z\\d]+$)(?![a-zA-Z!@#$%^&*_-]+$)(?![\\d!@#$%^&*_-]+$)[a-zA-Z\\d!@#$%^&*_-]+$";
    //密码字典
    private static final char[] UPPERS = {'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'};
    private static final char[] LOWERS = {'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'};
    private static final char[] NUMBERS = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9'};
    private static final char[] CHARS = {'!', '@', '#', '$', '%', '&', '*'};

    private List<Character> randomSeedList = new ArrayList<>();

    public static String passwordBcryptEncode(String password) {
        return new BCryptPasswordEncoder().encode(password);
    }

    public static Boolean passwordConfirm(String rawPassword, String encodePassword) {
        return new BCryptPasswordEncoder().matches(rawPassword, encodePassword);
    }


    /**
     * 随机生成密码8位
     *<pre>
     * {@code   code.appendLoop(2, () -> randomNext(UPPERS))
     *              .appendLoop(2, () -> randomNext(LOWERS))
     *              .appendLoop(2, () -> randomNext(NUMBERS))
     *              .appendLoop(2, () -> randomNext(CHARS))
     *              .randomSeed(strBuffer);
     * }</pre>
     * 非随机生成密码
     * <pre>
     * {@code   code.appendLoop(2, () -> randomNext(UPPERS))
     *              .appendLoop(2, () -> randomNext(LOWERS))
     *              .appendLoop(2, () -> randomNext(NUMBERS))
     *              .appendLoop(2, () -> randomNext(CHARS))
     *              .get(strBuffer);
     * }</pre>
     * @return
     */
    public static String randomPassword() {
        StringBuffer strBuffer = new StringBuffer();
        CodecUtils code = new CodecUtils();
        return code.appendLoop(2, () -> randomNext(UPPERS))
                .appendLoop(2, () -> randomNext(LOWERS))
                .appendLoop(2, () -> randomNext(NUMBERS))
                .appendLoop(2, () -> randomNext(CHARS))
                .randomSeed(strBuffer);
    }

    /**
     * 取值后不随机排序
     * @param strBuffer
     * @return
     */
    public String get(StringBuffer strBuffer) {
        Objects.requireNonNull(strBuffer);
        for (Character c : this.randomSeedList) {
            strBuffer.append(c);
        }
        return strBuffer.toString();
    }

    /**
     * 一次取值
     * @param supplier 根据lambda表达式取值不同字符类型
     * @return
     */
    private CodecUtils append(Supplier<? extends Character> supplier) {
        Objects.requireNonNull(supplier);
        this.randomSeedList.add(supplier.get());
        return this;
    }

    /**
     * 循环多次取值
     * @param loop 循环次数
     * @param supplier 根据lambda表达式取值不同字符类型
     * @return
     */
   private CodecUtils appendLoop(int loop, Supplier<? extends Character> supplier) {
       Objects.requireNonNull(supplier);
       if (loop <= 0){
           return this;
       }
        for (int i = 0; i < loop; i++) {
            this.append(supplier);
        }
        return this;
    }

    /**
     * 随机获取字符数组值
     * @param arr
     * @return
     */
    private static char randomNext(char[] arr) {
        Objects.requireNonNull(arr);
        Random random = new Random();
        return arr[random.nextInt(arr.length)];
    }

    /**
     * 随机排序
     * @param strBuffer
     */
    private String randomSeed(StringBuffer strBuffer) {
        Objects.requireNonNull(strBuffer);
        Random random = new Random();
        int index = random.nextInt(this.randomSeedList.size());
        strBuffer.append(this.randomSeedList.remove(index));
        if (this.randomSeedList.isEmpty()) {
            return strBuffer.toString();
        }
        return randomSeed(strBuffer);
    }

}
