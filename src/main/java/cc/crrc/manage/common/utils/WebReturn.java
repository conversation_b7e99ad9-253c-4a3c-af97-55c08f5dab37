package cc.crrc.manage.common.utils;

import java.util.List;

/**
 * @FileName WebReturn
 * <AUTHOR> yuxi
 * @Date 2019/7/3 16:51
 * @Version 1.0
 **/
public class WebReturn {
    private Boolean success;
    private String message;
    private List<?> list;

    /**
     * @return cc.crrc.manage.util.WebReturn
     * @Description 获得一个WebReturn对象，massage为”xxxx成功“或”xxxx操作失败“
     * <AUTHOR> yuxi
     * @Date 16:51 2019/7/3
     * @Param [success, optName]
     **/
    public static WebReturn getSimpleWebReturn(Boolean success, String optName) {
        WebReturn wr = new WebReturn();
        wr.setSuccess(success);
        if (success) {
            wr.setMessage(optName + "成功");
        } else {
            wr.setMessage(optName + "操作失败");
        }
        return wr;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<?> getList() {
        return list;
    }

    public void setList(List<?> list) {
        this.list = list;
    }
}
