//package cc.crrc.manage.common.utils.tsdb.influxdb;
//
//import cc.crrc.manage.common.utils.tsdb.TSDBUtils;
//import org.influxdb.InfluxDB;
//import org.influxdb.InfluxDBFactory;
//import org.influxdb.dto.Query;
//import org.influxdb.dto.QueryResult;
//
///**
// * @Author: Li Caisheng
// * @Date: 2019-12-29
// */
//public class InfluxDbUtils extends TSDBUtils {
//    private String userName;
//    private String password;
//    private String url;
//    private String database;
//    // InfluxDB实例
//    private InfluxDB influxDB;
//
//    /**
//     * 构造方法，供InfluxDbConfig.java调用。
//     * @param userName
//     * @param password
//     * @param url
//     * @param database
//     */
//    public InfluxDbUtils(String userName, String password, String url, String database) {
//        this.userName = userName;
//        this.password = password;
//        this.url = url;
//        this.database = database;
//        if("".equals(userName)){
//            this.influxDB = InfluxDBFactory.connect(url);
//        } else {
//            this.influxDB = InfluxDBFactory.connect(url, userName, password);
//        }
//    }
//
//    /**
//     * 获取influxdb结果
//     * @param command influxdb sql语句
//     * @return QueryResult
//     */
//    public QueryResult query(String command){
//        Query query = new Query(command, database);
//        return influxDB.query(query);
//    }
//
//    public String getUserName() {
//        return userName;
//    }
//
//    public void setUserName(String userName) {
//        this.userName = userName;
//    }
//
//    public String getPassword() {
//        return password;
//    }
//
//    public void setPassword(String password) {
//        this.password = password;
//    }
//
//    public String getUrl() {
//        return url;
//    }
//
//    public void setUrl(String url) {
//        this.url = url;
//    }
//
//    public String getDatabase() {
//        return database;
//    }
//
//    public void setDatabase(String database) {
//        this.database = database;
//    }
//
//    public InfluxDB getInfluxDB() {
//        return influxDB;
//    }
//
//    public void setInfluxDB(InfluxDB influxDB) {
//        this.influxDB = influxDB;
//    }
//}