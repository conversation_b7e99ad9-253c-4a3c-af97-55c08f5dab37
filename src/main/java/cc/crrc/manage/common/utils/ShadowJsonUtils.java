package cc.crrc.manage.common.utils;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public final class ShadowJsonUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(ShadowJsonUtils.class);
    private ShadowJsonUtils(){

    }

    public static String getJsonStringValue(JSONObject result, String key) {
        return getJsonValue(result, key, String.class);
    }

    public static Integer getJsonIntegerValue(JSONObject result, String key) {
        return getJsonValue(result, key, Integer.class);
    }

    public static Float getJsonFloatValue(JSONObject result, String key) {
        return getJsonValue(result, key, Float.class);
    }

    public static Long getJsonLongValue(JSONObject result, String key) {
        return getJsonValue(result, key, Long.class);
    }

    public static Long getJsonLongValue(JSONObject result, String key, Long defaultValue) {
        Long value = getJsonValue(result, key, Long.class);
        if (value == null && defaultValue != null)
            return defaultValue;
        return value;
    }

    public static Double getJsonDoubleValue(JSONObject result, String key,Double defaultValue) {
        Double value = getJsonValue(result,key,Double.class);
        return value==null?defaultValue:value;
    }

    public static Boolean getJsonBooleanValue(JSONObject result, String key,Boolean defaultValue) {
        Boolean value = getJsonValue(result,key,Boolean.class);
        return value==null?defaultValue:value;
    }

    public static String getJsonStringValue(JSONObject result, String key, String defaultValue) {
        String value = getJsonValue(result, key, String.class);
        if (value == null && defaultValue != null)
            return defaultValue;
        return value;
    }

    public static Integer getJsonIntegerValue(JSONObject result, String key, Integer defaultValue) {
        Integer value = getJsonValue(result, key, Integer.class);
        if (value == null && defaultValue != null)
            return defaultValue;
        return value;
    }

    public static Float getJsonFloatValue(JSONObject result, String key, Float defaultValue) {
        Float value = getJsonValue(result, key, Float.class);
        if (value == null && defaultValue != null)
            return defaultValue;
        return value;
    }

    private static <T> T getJsonValue(JSONObject result, String key, Class<T> clz) {
        if (result == null || key == null)
            return null;
        return result.getObject(key,clz);

    }
}
