package cc.crrc.manage.common.utils;

import org.slf4j.Logger;

/**
 * @Author: <PERSON>
 * @Date: 2019-12-24
 */
public class LoggerUtils {
    /**
     * 记录error发生的方法和错误栈
     * @param logger    LoggerFactory获取的对象
     * @param t         异常
     */
    public static void error(Logger logger, Throwable t){
        error(logger, t.getMessage());
    }

    public static void error(Logger logger, String message){
        String format = "Method[{}] Error:{}";
        logger.error(format,getMethodName(), message);
    }

    public static void info(Logger logger, String info){
        String format = "Method[{}] Info:{}";
        logger.info(format,getMethodName(), info);
    }

    public static void warn(Logger logger, String warn){
        String format = "Method[{}] Warn:{}";
        logger.warn(format,getMethodName(), warn);
    }

    private static String getMethodName(){
        return Thread.currentThread().getStackTrace()[2].getMethodName();
    }
}
