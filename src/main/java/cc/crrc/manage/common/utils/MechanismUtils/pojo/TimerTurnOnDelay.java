package cc.crrc.manage.common.utils.MechanismUtils.pojo;


/**
 * 通电触发延时计时器，在输入信号上升沿ptS秒后输出一次true，其余时候输出false
 * @Author: Li <PERSON>heng
 * @Date: 2019-09-16
 */
public class TimerTurnOnDelay extends Timer{
    
    /**
     * 构造器
     *
     * @param id 计时器在机理json中的id
     */
    public TimerTurnOnDelay(String id) {
        super(id);
    }
    
    /**
     * 接受新输入值，更新数据，返回输出值
     * @param newTime 新数据包的UNIX时间戳
     * @param in 新输入值
     * @return 计时器的输出值
     * 当输入true时，如果计时器未开启，则将startTime和lastTime都置为newTime；若计时器正在计时，则只将lastTime置为newTime。
     *      若新数据与上次数据时间间隔太久，则视为计时器老数据超时，将startTime和lastTime都置为newTime。
     * 当输入false时，将startTime和lastt置为-1，表示关闭计时器。返回false
     */
    @Override
    public boolean updateAndOutput(long newTime, boolean in, double ptS){
        if(in){
            long oldLastTime = lastTime;
            if(timerWasClosed(newTime)){
                startTime = newTime;
            }
            lastTime = newTime;
            double ptMs = ptS * 1000;
            return (oldLastTime-startTime) < ptMs && (newTime-startTime) >= ptMs;
        } else {
            startTime = -1;
            lastTime = -1;
            return false;
        }
    }

}
