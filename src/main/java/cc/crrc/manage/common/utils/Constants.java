package cc.crrc.manage.common.utils;

/**
 * @FileName Constants
 * <AUTHOR> shuangquan
 * @Date 2019/6/12 16:05
 **/
public final class Constants {

    private Constants() {

    }
    public static final String CACHE_REFRESH_CHANNEL = "refresh_cache";
    public static final boolean SUCCESS = true;
    public static final boolean FAILURE = false;
    public static final String REDIS_USER_TOKEN_KEY_PREFIX = "TOKEN_USER:";
    public static final String REDIS_USER_AUTHRITY_KEY_PREFIX = "USER:ELEMENT:AUTHORITY:";
    public static final String ALL = "*";
    public static final String ALL_PATH = "/**";
    public static final String LOGIN = "/login";
    public static final String LOGOUT = "/logout";
    public static final String TOKEN_KEY = "access_token";
    public static final int NUMBER_ZERO = 0;
    public static final int NUMBER_ONE = 1;
    public static final int NUMBER_TWO = 2;
    public static final int NUMBER_THREE = 3;
    public static final int NUMBER_FOUR = 4;
    public static final String DEV_PROFILE = "dev";
    public static final String PROD_PROFILE = "prod";
    public static final String LOCAL_PROFILE = "local";
    public static final String USER_NAME_NOT_FOUND = "[username not found]";
    public static final String BAD_CREDENTIALS_MESSAGE = "用户名密码不正确";
    public static final String IP_BAD_ACCESS_MESSAGE = "IP地址登录异常，1分钟内连续输入错误密码%s次";
    public static final String USER_BAD_ACCESS_MESSAGE = "用户登录异常，1分钟内连续输入错误密码%s次";
    public final static String REDIS_USER_FAILURE_COUNT_KEY="FAILURE_COUNT:%s";
    public final static int USER_FAILURE_MAX = 5;
    public final static String USER_LOCK_MESSAGE = "账号被锁定，请稍后重试";
    public final static String USER_NAME="username";
    public static final String USER_LOGIN_MESSAGE = "用户登录";
    public static final String LOGIN_MENU_ID = "-1";
    public static final String LOGIN_MENU_CODE = "login";
    public static final String STRUCTURE_PICTURE_FILE_TYPE = "structurePic";
    public static final String STRUCTURE_3D_PICTURE_FILE_TYPE = "structureFbx";
    public static final String[] STRUCTURE_PICTURE_FILE_FORMAT = {"png","jpg","fbx"};
    public static final String[] STRUCTURE_3D_PICTURE_FILE_FORMAT = {"fbx"};
    public static final String[] PAGEHELPER_IGNORE = {"cc.crrc.manage.mapper.AuthenticationMapping", "cc.crrc..manage" +
            ".mapper.SysLogMapping.getMenuByUri"};
    public static final String NODESHAPE = "image";//节点的形状
    public static final String EDGESHAPE = "polyLineFlow";//线的形状
    public static final String RESTSIMG = "/img/End.png";//其他节点图片相对路径
    public static final String CURRENTIMG = "/img/Start.png";//当前节点图片相对路径

    // 车辆构型的节点类型
    public static final String STRUCTURE_TYPE_SYSTEM = "系统";
    public static final String STRUCTURE_TYPE_CARRIER = "车厢";
    public static final String STRUCTURE_TYPE_LOCATION = "位置";
    public static final String STRUCTURE_TYPE_COMPONENT = "部件";

    // 时序库种类
    public static final String INFLUXDB = "1";
    public static final String OPENTSDB = "2";
    public static final String IOTDB = "3";

    //主键
    public static final long PRIMARY_KEY_DATA_CENTER_ID=1L;
    public static final String MACHINE_ID_ENV_KEY="MACHINE_ID";
    public static final long START_DATESTAMP = 1618452321758L;
    public static final long SEQUENCE_BIT = 9;
    public static final long MACHINE_BIT = 4;
    public static final long DATA_CENTER_BIT = 9;
    public static final long MAX_DATA_CENTER_NUM = -1L ^ (-1L << DATA_CENTER_BIT);
    public static final long MAX_MACHINE_NUM = -1L ^ (-1L << MACHINE_BIT);
    public static final long MAX_SEQUENCE = -1L ^ (-1L << SEQUENCE_BIT);
    public static final long MACHINE_LEFT = SEQUENCE_BIT;
    public static final long DATA_CENTER_LEFT = SEQUENCE_BIT + MACHINE_BIT;
    public static final long TIMESTAMP_LEFT = DATA_CENTER_LEFT + DATA_CENTER_BIT;

    //查询日志标识
    public static final String ENABLE_SELECT_LOG = "enable_select_log";
    public static final String ENABLE_SELECT_LOG_ON = "on";
    public static final String LINE_ID = "24";
    public static final String VEHICLE_TYPE_ID = "129";

}
