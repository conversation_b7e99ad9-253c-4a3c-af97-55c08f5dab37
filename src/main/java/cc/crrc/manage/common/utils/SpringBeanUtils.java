package cc.crrc.manage.common.utils;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @FileName SpringBeanUtils
 * <AUTHOR> shuangquan
 * @Date 2019/6/3 8:58
 **/
@Component
public final class SpringBeanUtils implements ApplicationContextAware {

    private static ApplicationContext context;

    private SpringBeanUtils() {

    }

    public static <T> T getBean(String beanName) {
        try {
            return (T) context.getBean(beanName);
        } catch (NoSuchBeanDefinitionException e) {
            return null;
        }
    }

    public static <T> T getBean(Class<T> cls) {
        try {
            return context.getBean(cls);
        } catch (NoSuchBeanDefinitionException e) {
            return null;
        }
    }

    public static <T> Map<String,T> getBeans(Class<T> clz){
        try {
            return context.getBeansOfType(clz);
        }catch (BeansException e){
            return null;
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        setContext(applicationContext);
    }

    public static void setContext(ApplicationContext applicationContext) {
        context = applicationContext;
    }
}
