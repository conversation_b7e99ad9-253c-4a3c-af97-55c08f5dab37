package cc.crrc.manage.common.exception;



/**
 * @FileName RestApiException
 * @<PERSON> <PERSON> shuangquan
 * @Date 2019/6/3 14:43
 **/
public class RestApiException extends RuntimeException{
    private String errorCode;
    private String errorMessage;

    public RestApiException(ExceptionInfoEnum exceptionInfoEnum){
        super(exceptionInfoEnum.getErrorMessage());
        this.errorCode = exceptionInfoEnum.getErrorCode();
    }

    public RestApiException(ExceptionInfoEnum exceptionInfoEnum,String errorMessage){
        this(exceptionInfoEnum.getErrorCode(),errorMessage);
    }

    public RestApiException(String errorCode,String errorMessage){
        super(errorMessage);
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }
}
