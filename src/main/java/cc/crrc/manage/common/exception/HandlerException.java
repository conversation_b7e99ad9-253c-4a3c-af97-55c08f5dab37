package cc.crrc.manage.common.exception;


import cc.crrc.manage.common.response.Result;
import cc.crrc.manage.common.utils.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingPathVariableException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.text.MessageFormat;
import java.util.List;
import java.util.Set;

/**
 * @FileName HandlerException
 * <AUTHOR> shuangquan
 * @Date 2019/5/30 11:10
 **/
@ControllerAdvice
@Configuration
public class HandlerException {

    private final Logger logger = LoggerFactory.getLogger(cc.crrc.manage.common.exception.HandlerException.class);

    @ExceptionHandler(Exception.class)
    @ResponseBody
    public Result exception(Exception e) {
        e.printStackTrace();
        logger.error("{} {}", e.getClass(), e.getMessage());
        return failure(ExceptionInfoEnum.HANDLER_SYSTEM_EXCEPTION);
    }

    @ExceptionHandler(RestApiException.class)
    @ResponseBody
    public Result handleRestApiException(RestApiException e) {
        return failure(e.getErrorCode(), e.getMessage());
    }

    @ExceptionHandler(NoHandlerFoundException.class)
    @ResponseBody
    public Result handleNoHandlerFoundException(NoHandlerFoundException e) {
        return failure(ExceptionInfoEnum.HANDLER_NOT_FOUND);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public Result handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        List<ObjectError> errorList = e.getBindingResult().getAllErrors();
        String message = errorList.parallelStream().map(obj -> obj.getDefaultMessage()).findFirst().orElse("");
        return failure(ExceptionInfoEnum.PARAMETER_VALIDATION_EXCEPTION.getErrorCode(), message);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseBody
    public Result handleConstraintViolationException(ConstraintViolationException e) {
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        String message = violations.parallelStream().map(item -> item.getMessage()).findFirst().orElse("");
        return failure(ExceptionInfoEnum.PARAMETER_VALIDATION_EXCEPTION.getErrorCode(), message);
    }

    @ExceptionHandler(MissingPathVariableException.class)
    @ResponseBody
    public Result handleMissingPathVariableException(MissingPathVariableException e) {
        String message = MessageFormat.format(ExceptionInfoEnum.URL_PARAMETER_MESSING_EXCEPTION.getErrorMessage(),
                e.getVariableName(), e.getParameter().getParameterType().getSimpleName());
        return failure(ExceptionInfoEnum.URL_PARAMETER_MESSING_EXCEPTION.getErrorCode(), message);
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseBody
    public Result handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e) {
        return failure(ExceptionInfoEnum.URL_PARAMETER_MISMATCH_EXCEPTION);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseBody
    public Result handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
        return failure(ExceptionInfoEnum.URL_HTTPMESSAGE_READABLE_EXCEPTION);
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseBody
    public Result handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        return failure(ExceptionInfoEnum.HANDLER_METHOD_NOT_SUPPORT_EXCEPTION);
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseBody
    public Result handleMissingServletRequestParameterException(MissingServletRequestParameterException e) {
        return failure(ExceptionInfoEnum.URL_PARAMETER_MESSING_EXCEPTION.getErrorCode(), "参数缺失");
    }

    @ExceptionHandler(BindException.class)
    @ResponseBody
    public Result BindExceptionHandler(BindException e) {
        String message =
                e.getBindingResult().getAllErrors().stream().map(DefaultMessageSourceResolvable::getDefaultMessage).findFirst().orElse("");
        return failure(ExceptionInfoEnum.PARAMETER_VALIDATION_EXCEPTION.getErrorCode(), message);
    }


    private Result failure(ExceptionInfoEnum exceptionInfoEnum) {
        return Result.builder().success(Constants.FAILURE).error(exceptionInfoEnum).build();
    }

    private Result failure(String code, String message) {
        return Result.builder().success(Constants.FAILURE).error(code, message).build();
    }

}
