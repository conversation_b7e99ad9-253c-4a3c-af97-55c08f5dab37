package cc.crrc.manage.common.response;


import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * @FileName Error
 * @<PERSON> <PERSON> shuangquan
 * @Date 2019/5/31 10:28
 **/
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Error {

    private String code;
    private String message;

    public Error(ExceptionInfoEnum exceptionInfoEnum){
        this.code = exceptionInfoEnum.getErrorCode();
        this.message = exceptionInfoEnum.getErrorMessage();
    }

    public Error(String errorCode, String errorMessage) {
        this.code = errorCode;
        this.message = errorMessage;
    }


    public String getCode() {

        return code;
    }

    public void setCode(String code) {

        this.code = code;
    }

    public String getMessage() {

        return message;
    }

    public void setMessage(String message) {

        this.message = message;
    }
}
