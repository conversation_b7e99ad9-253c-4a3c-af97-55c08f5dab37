package cc.crrc.manage.common.response;

/**
 * 状态码
 */
public class CodeMsg {

    public static final int OK=200;//成功
    public static final int FAILED=4000;//增删改查失败统一码

    public static final int CREATED=201;//创建
    public static final int ACCEPTED =202;//认证
    public static final int NON_AUTHORITATIVE_INFORMATION =203;//非授权信息
    public static final int REMOTEERROR =204;//远程调用失败
    public static final int REPERROR =205;//重复操作
    public static final int BAD_REQUEST =400;//请求失败
    public static final int UNAUTHORIZED =401;//权限不足
    public static final int FORBIDDEN =403;//禁止访问
    public static final int NOT_FOUND =404;//未找到页面
    public static final int METHOD_NOT_ALLOWED =405;//方法不被允许
    public static final int INTERNAL_SERVER_ERROR =500;//内部服务器错误





}
