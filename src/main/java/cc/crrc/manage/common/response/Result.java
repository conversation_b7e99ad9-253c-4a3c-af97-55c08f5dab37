package cc.crrc.manage.common.response;


import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * @FileName Result
 * <AUTHOR> shuangquan
 * @Date 2019/5/31 10:27
 **/
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Result {

    private boolean success;
    private Object body;
    private Error error;

    private Result(boolean success, Error error, Object body) {
        this.success = success;
        this.error = error;
        this.body = body;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public Object getBody() {
        return body;
    }

    public void setBody(Object body) {
        this.body = body;
    }

    public Error getError() {
        return error;
    }

    public void setError(Error error) {
        this.error = error;
    }

    public static Builder builder() {
        return new BuilderImpl();
    }

    public interface Builder {

        Builder success(boolean success);

        Builder body(Object body);

        Builder error(ExceptionInfoEnum exceptionInfoEnum);

        Builder error(String code, String message);

        Result build();
    }

    private static class BuilderImpl implements Builder {
        private boolean success;
        private Object body;
        private Error error;

        public Builder body(Object body) {
            this.body = body;
            return this;
        }

        public Builder success(boolean success) {
            this.success = success;
            return this;
        }

        public Builder error(ExceptionInfoEnum exceptionInfoEnum) {
            this.error = new Error(exceptionInfoEnum);
            return this;
        }

        public Builder error(String code, String message) {
            this.error = new Error(code, message);
            return this;
        }

        public Result build() {
            return new Result(this.success, this.error, this.body);
        }
    }
}
