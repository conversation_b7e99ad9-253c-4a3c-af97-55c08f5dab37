package cc.crrc.manage.common.validator;

import cc.crrc.manage.common.annotation.EnumValue;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;

/**
 * @FileName EnumValidator
 * <AUTHOR> shuangquan
 * @Date 2019/6/18 19:57
 **/
public class EnumValidator implements ConstraintValidator<EnumValue, Object> {

    private Logger logger = LoggerFactory.getLogger(cc.crrc.manage.common.validator.EnumValidator.class);

    private Class<? extends Enum<?>> enumClass;

    private String enumMethod;

    @Override
    public void initialize(EnumValue enumValue) {
        enumMethod = enumValue.enumMethod();
        enumClass = enumValue.enumClass();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null) {
            return Boolean.FALSE;
        }
        Class<?> valueClass = value.getClass();
        try {
            Method method = enumClass.getMethod(enumMethod, valueClass);
            if (!Boolean.TYPE.equals(method.getReturnType()) && !Boolean.class.equals(method.getReturnType())) {
                logger.error("{enumClass}[{validMethod}]方法返回值必须为boolean类型");
                throw new RestApiException(ExceptionInfoEnum.PARAMETER_VALIDATION_EXCEPTION.getErrorCode(), "参数校验异常");
            }
            if (!Modifier.isStatic(method.getModifiers())) {
                logger.error("{enumClass}[{validMethod}]方法必须为静态方法");
                throw new RestApiException(ExceptionInfoEnum.PARAMETER_VALIDATION_EXCEPTION.getErrorCode(), "参数校验异常");
            }
            Boolean result = (Boolean) method.invoke(null, value);
            return result == null ? false : result;
        } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException e) {
            logger.error(e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.PARAMETER_VALIDATION_EXCEPTION.getErrorCode(), "参数校验异常");
        } catch (NoSuchMethodException | SecurityException e) {
            logger.error("{enumClass}[{validMethod}]方法不存在");
            throw new RestApiException(ExceptionInfoEnum.PARAMETER_VALIDATION_EXCEPTION.getErrorCode(), "参数校验异常");
        }
    }
}
