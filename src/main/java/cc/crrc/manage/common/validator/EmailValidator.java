package cc.crrc.manage.common.validator;

import cc.crrc.manage.common.annotation.Email;
import cc.crrc.manage.common.utils.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

/**
 * 邮箱校验
 *
 * <AUTHOR>
 * 2019/11/25
 **/
public class EmailValidator implements ConstraintValidator<Email, String> {

    /**
     * 正则解释：
     * 1.必须包含@
     * 2.允许开头及结尾有空白
     * <p>
     * "@"前：
     * 3.第一个非空白字符（有效字符）,只能是字母、数字、下划线
     * 4.@前的部分不能出现连续的..,但是允许.-或者-.
     * 5.从第二个字符开始，除了字母、数字、下划线，还支持中划线-和英文句号.
     * <p>
     * "@"后：
     * 6.@后面第一个字符是字母、数字
     * 7.最后一个.后面只能接字母
     * 8.邮箱以.+ 至少一个字母结尾
     * 9.@后面，从第二个字符开始允许.或者-,但是不允许..,.-,-.的出现..和-需要和字母数字下划线间隔出现
     **/
    private static final String REGEX_EMAIL = "^\\s*\\w+(?:\\.?[\\w-]+)*@[\\w]+(?:[-.][\\w]+)*\\.[a-zA-Z]+\\s*$";

    @Override
    public void initialize(Email constraintAnnotation) {
    }

    @Override
    public boolean isValid(String email, ConstraintValidatorContext constraintValidatorContext) {
        if (StringUtils.isEmpty(email)) {
            return true;
        }
        return Pattern.matches(REGEX_EMAIL, email);
    }
}
