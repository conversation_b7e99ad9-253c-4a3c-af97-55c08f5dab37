package cc.crrc.manage.common.validator;

import cc.crrc.manage.common.annotation.Contained;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * @FileName ContainedValidator
 * <AUTHOR> shuangquan
 * @Date 2019/11/12 14:24
 **/
public class ContainedValidator implements ConstraintValidator<Contained, Object> {
    private static final Logger logger = LoggerFactory.getLogger(ContainedValidator.class);
    private String[] values;
    private boolean allowEmpty;

    @Override
    public void initialize(Contained contained) {
        this.values = contained.value();
        this.allowEmpty = contained.allowEmpty();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (allowEmpty && (value == null || "".equals(String.valueOf(value).trim()))) {
            return true;
        }
        return ArrayUtils.contains(values, value.toString());
    }

}
