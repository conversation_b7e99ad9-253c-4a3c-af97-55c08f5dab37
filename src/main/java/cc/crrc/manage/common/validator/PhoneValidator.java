package cc.crrc.manage.common.validator;

import cc.crrc.manage.common.annotation.Phone;
import cc.crrc.manage.common.utils.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

/**
 * 电话号校验
 *
 * <AUTHOR>
 * 2019/11/25
 **/
public class PhoneValidator implements ConstraintValidator<Phone, String> {

    /**
     * 正则解释：
     * 可用来匹配手机号或者座机号
     *
     * 座机号：
     * +86-0411-12345678
     * 地区号可选填。规则必须为数字0开头，后接两或者三为数字，再接短横杠。例如：0411-
     *
     * 国家号可选填。规则必须前有+加号，后接两位数字，再接短横杠和地区号（此处地区号可不以0开头）。
     * 例如：+86-731-
     *
     * 座机号匹配七或者八位数字。
     *
     * 手机号：
     * +86-13512345678
     * 国家号可选填。规则必须有+加号，后接两位数字，再接短横杠。例如：+86-
     * 手机号必须为数字1开头，后接十位数字。
     */
    private static final String REGEX_PHONE = "^((((\\+\\d{2}-\\d{2,4}-)|(0\\d{2,3}-))?\\d{7,8})|((\\+\\d{2}-)?([1]\\d{10})))$";

    @Override
    public void initialize(Phone constraintAnnotation) {
    }

    @Override
    public boolean isValid(String phone, ConstraintValidatorContext constraintValidatorContext) {
        if (StringUtils.isEmpty(phone)) {
            return true;
        }
        return Pattern.matches(REGEX_PHONE, phone);
    }

}
