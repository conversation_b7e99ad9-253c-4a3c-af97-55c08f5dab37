package cc.crrc.manage.common.validator;

import cc.crrc.manage.common.annotation.IntegerRange;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 数值范围校验
 * <AUTHOR>
 * 2023/1/31
 */
public class IntegerRangeValidator implements ConstraintValidator<IntegerRange, Long> {

    private static final Logger logger = LoggerFactory.getLogger(IntegerRangeValidator.class);
    private long max;
    private long min;

    @Override
    public void initialize(IntegerRange range) {
        this.max = range.max();
        this.min = range.min();
    }

    @Override
    public boolean isValid(Long origin, ConstraintValidatorContext constraintValidatorContext) {
        if (origin == null){
            return false;
        }
        return origin <= max && origin >= min;
    }

}
