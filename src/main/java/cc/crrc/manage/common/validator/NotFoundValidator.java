package cc.crrc.manage.common.validator;

import cc.crrc.manage.common.annotation.NotFound;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.SpringBeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.lang.reflect.Field;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @FileName NotFoundValidator
 * <AUTHOR> shuangquan
 * @Date 2019/11/15 15:44
 **/
public class NotFoundValidator implements ConstraintValidator<NotFound, Object> {

    private static final Logger logger = LoggerFactory.getLogger(NotFoundValidator.class);
    private static Pattern p = Pattern.compile("\\$\\{(.*?)\\}");
    private JdbcTemplate jdbcTemplate;
    private String table;
    private String condition;
    private String sql;

    @Override
    public void initialize(NotFound notFound) {
        jdbcTemplate = SpringBeanUtils.getBean(JdbcTemplate.class);
        table = notFound.table();
        condition = notFound.condition();
        sql = notFound.sql();
    }

    @Override
    public boolean isValid(Object obj, ConstraintValidatorContext context) {
        StringBuilder builder = new StringBuilder();
        try {
            if (!StringUtils.isEmpty(sql)) {
                builder.append(buildSql(sql, obj));
            } else {
                builder.append("SELECT COUNT(1) FROM ");
                builder.append(table);
                builder.append(" WHERE 1=1 ");
                if (!StringUtils.isEmpty(condition)) {
                    builder.append(" AND ");
                    builder.append(buildSql(condition, obj));
                }
            }
        } catch (NoSuchFieldException | SecurityException | IllegalArgumentException | IllegalAccessException e) {
            logger.error("isValid error {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.PARAMETER_VALIDATION_EXCEPTION.getErrorCode(), e.getMessage());
        }
        logger.info("Valid sql: {}", builder.toString());
        int count = jdbcTemplate.queryForObject(builder.toString(), Integer.class);
        if (count == 0) {
            return false;
        }
        return true;
    }


    public String buildSql(String sql, Object obj)
            throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
        Matcher matcher = p.matcher(sql);
        while (matcher.find()) {
            String fieldName = matcher.group(1);
            sql = sql.replace("${" + fieldName + "}", getFieldValue(fieldName, obj));
        }
        return sql;
    }

    public String getFieldValue(String fieldName, Object obj)
            throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
        Field field = obj.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        return String.valueOf(field.get(obj));
    }
}
