package cc.crrc.manage.common.validator;

import cc.crrc.manage.common.DataType;
import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.SpringBeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.lang.reflect.Field;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class DuplicateValidator implements ConstraintValidator<Duplicate, Object> {
    private static final Logger logger = LoggerFactory.getLogger(DuplicateValidator.class);
    private static Pattern p = Pattern.compile("\\$\\{(.*?)\\}");
    private JdbcTemplate jdbcTemplate;
    private String table;
    private String condition;
    private String sql;

    @Override
    public void initialize(Duplicate exists) {
        jdbcTemplate = SpringBeanUtils.getBean(JdbcTemplate.class);
        table = exists.table();
        condition = exists.condition();
        sql = exists.sql();
    }

    @Override
    public boolean isValid(Object obj, ConstraintValidatorContext context) {
        StringBuilder builder = new StringBuilder();
        try {
            if (!StringUtils.isEmpty(sql)) {
                builder.append(buildSql(sql, obj));
            } else {
                builder.append("SELECT COUNT(1) FROM ");
                builder.append(table);
                builder.append(" WHERE 1=1 ");
                if (!StringUtils.isEmpty(condition)) {
                    builder.append(" AND ");
                    builder.append(buildSql(condition, obj));
                }
            }
        } catch (NoSuchFieldException | SecurityException | IllegalArgumentException | IllegalAccessException e) {
            logger.error("isValid error {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.PARAMETER_VALIDATION_EXCEPTION.getErrorCode(), e.getMessage());
        }
        logger.info("Valid sql: {}", builder.toString());
        int count = jdbcTemplate.queryForObject(builder.toString(), Integer.class);
        if (count >= 1) {
            return false;
        }
        return true;
    }

    public String buildSql(String sql, Object obj)
            throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
        if (DataType.contains(obj.getClass().getName())) {
            sql = sql.replace("${value}", String.valueOf(obj));
        } else {
            Matcher matcher = p.matcher(sql);
            while (matcher.find()) {
                String fieldName = matcher.group(1);
                sql = sql.replace("${" + fieldName + "}", getFieldValue(fieldName, obj));
            }
        }
        //将where条件中的"= null"替换为"is null"
        sql = processNullCondition(sql);
        return sql;
    }

    public String getFieldValue(String fieldName, Object obj)
            throws NoSuchFieldException, SecurityException, IllegalArgumentException, IllegalAccessException {
        Field field = obj.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        String value = String.valueOf(field.get(obj));
        value = value.replace("'","''");
        return value;
    }

    public String processNullCondition(String sql) {
        return sql.replaceAll("\\s*=\\s*null", " IS NULL");
    }
}
