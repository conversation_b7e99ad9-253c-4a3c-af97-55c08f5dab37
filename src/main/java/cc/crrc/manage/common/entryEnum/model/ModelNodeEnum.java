package cc.crrc.manage.common.entryEnum.model;

public enum ModelNodeEnum {
    V_D("v-d","数字量"),
    V_A("v-a","模拟量"),
    V_NUMBER("v-number","常量"),
    RM("rM","只读型M变量"),
    WM("wM","只写型M变量"),
    V_M("v-m","故障节点"),
    V_COUNT("v-count","算数运算"),
    V_MATH("v-math","数学运算"),
    V_TRIGGER("v-trigger","触发器"),
    V_TIMECOUNTER("v-timeCounter","定时器"),
    V_TIMER("v-timer","计时器"),
    V_COUNTER("v-counter","定数计时器"),
    V_TIMECOUNTING("v-timeCounting","定时计数器"),
    V_POWER("v-power","平均数"),
    V_SYSTIME("v-systime","系统时间"),
    ;

    private String type;
    private String str;

     ModelNodeEnum(String type, String str) {
        this.type = type;
        this.str = str;
    }

    public String getType() {
        return type;
    }


    public String getStr() {
        return str;
    }

    public static String getName(String type) {
        for (ModelNodeEnum c : ModelNodeEnum.values()) {
            if (type.equals(c.getType())) {
                return c.getStr();
            }
        }
        return null;
    }
}
