package cc.crrc.manage.common.entryEnum.model;

public enum KeyOfRuleJsonEnum {
    CELLS("cells"),
    ID("id"),
    NAME("name"),
    SHAPE("shape"),
    DATA("data"),
    SOURCE("source"),
    TARGET("target"),
    CELL("cell"),
    PORT("port"),
    PORTS("ports"),
    ITEMS("items"),
    TYPE("type"),
    VALUE("value"),
    ATTRS("attrs"),
    TEXT("text"),
    ;

    private String key;


     KeyOfRuleJsonEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }


}
