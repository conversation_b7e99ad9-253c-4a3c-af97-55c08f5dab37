package cc.crrc.manage.common.entryEnum.model;

public enum NotCheckNullEnum {
    v_m("v-m","故障节点"),
    v_timeCounter("v-timeCounter","定时器"),
    v_timer("v-timer","计时器"),
    v_counter("v-counter","定数计时器"),
    v_timeCounting("v-timeCounting","定时计数器"),
    v_power("v-power","平均数"),
    v_systime("v-systime","系统时间"),
    ;

    private String type;
    private String str;

     NotCheckNullEnum(String type, String str) {
        this.type = type;
        this.str = str;
    }

    public String getType() {
        return type;
    }


    public String getStr() {
        return str;
    }

    public static String getName(String type) {
        for (NotCheckNullEnum c : NotCheckNullEnum.values()) {
            if (type.equals(c.getType())) {
                return c.getStr();
            }
        }
        return null;
    }

    public static boolean isExist(String type) {
         if (type == null){
             return false;
         }
        for (NotCheckNullEnum e : NotCheckNullEnum.values()){
            if (type.equals(e.getType())){
                return true;
            }
        }

        return false;
    }
}
