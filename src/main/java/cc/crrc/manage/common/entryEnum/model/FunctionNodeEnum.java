package cc.crrc.manage.common.entryEnum.model;

public enum FunctionNodeEnum {
    V_BRANCH("v-branch","比较运算"),
    V_LOGIC("v-logic","逻辑运算"),
    V_COUNT("v-count","算数运算"),
    V_MATH("v-math","数学运算"),
    V_TRIGGER("v-trigger","触发器"),
    V_TIME_COUNTER("v-timeCounter","定时器"),
    V_TIMER("v-timer","计时器"),
    V_COUNTER("v-counter","定数计时器"),
    V_TIME_COUNTING("v-timeCounting","定时计数器"),
    V_POWER("v-power","标准差"),
    V_AVERAGE("v-average","平均数"),
    V_SYS_TIME("v-systime","系统时间"),
    ;

    private String type;
    private String str;

     FunctionNodeEnum(String type, String str) {
        this.type = type;
        this.str = str;
    }

    public String getType() {
        return type;
    }


    public String getStr() {
        return str;
    }

    public static String getName(String type) {
        for (FunctionNodeEnum c : FunctionNodeEnum.values()) {
            if (type.equals(c.getType())) {
                return c.getStr();
            }
        }
        return null;
    }

    public static boolean isExist(String type) {
        if (type == null){
            return false;
        }
        for (FunctionNodeEnum f : FunctionNodeEnum.values()){
            if (type.equals(f.getType())){
                return true;
            }
        }

        return false;
    }
}
