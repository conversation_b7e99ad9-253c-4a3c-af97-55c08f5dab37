package cc.crrc.manage.common.annotation;

import cc.crrc.manage.common.validator.ContainedValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Documented
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ContainedValidator.class)
public @interface Contained {
    String[] value() default {};

    boolean allowEmpty() default true;

    String message() default "";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}

