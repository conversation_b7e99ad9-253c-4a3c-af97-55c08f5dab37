package cc.crrc.manage.common.annotation;


import cc.crrc.manage.common.validator.EnumValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ ElementType.METHOD, ElementType.FIELD,ElementType.PARAMETER })
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = EnumValidator.class)
public @interface EnumValue {

    String message() default "";

    Class<? extends Enum<?>> enumClass();

    String enumMethod();

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
