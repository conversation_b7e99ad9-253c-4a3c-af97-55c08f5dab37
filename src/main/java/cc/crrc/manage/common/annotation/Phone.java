package cc.crrc.manage.common.annotation;

import cc.crrc.manage.common.validator.PhoneValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Constraint(validatedBy = PhoneValidator.class)
public @interface Phone {
    String message() default "请输入正确的电话号码！";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
