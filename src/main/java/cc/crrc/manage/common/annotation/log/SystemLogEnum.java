package cc.crrc.manage.common.annotation.log;

/**
 * 日志操作类型枚举
 */
public enum SystemLogEnum {

    INSERT("insert"),
    DELETE("delete"),
    UPDATE("update"),
    SELECT("select"),
    LOGIN("login"),
    LOGOUT("logout"),
    UPLOAD("upload"),
    DOWNLOAD("download"),
    EXPORT("export"),
    IMPORT("import");
    private String value;

    SystemLogEnum(String value) {
        this.value = value;
    }

    public String value() {
        return this.value;
    }
}
