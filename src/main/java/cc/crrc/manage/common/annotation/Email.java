package cc.crrc.manage.common.annotation;

import cc.crrc.manage.common.validator.EmailValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Constraint(validatedBy = EmailValidator.class)
public @interface Email {
    String message() default "请输入正确的邮箱格式！";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
