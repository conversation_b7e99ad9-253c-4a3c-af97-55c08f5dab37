package cc.crrc.manage.common.annotation;

import cc.crrc.manage.common.validator.NotFoundValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Constraint(validatedBy = NotFoundValidator.class)
public @interface NotFound {

    String message() default "未找到相关记录";

    String table() default "";

    String condition() default "";

    String sql() default "";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
