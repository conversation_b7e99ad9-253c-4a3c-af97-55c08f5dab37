package cc.crrc.manage.common.annotation;


import cc.crrc.manage.common.validator.DuplicateValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Repeatable;

@Constraint(validatedBy = DuplicateValidator.class)
@Repeatable(Duplicates.class)
public @interface Duplicate {

    String message() default "已存在";

    String table() default "";

    String condition() default "";

    String sql() default "";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
