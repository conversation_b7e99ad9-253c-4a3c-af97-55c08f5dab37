package cc.crrc.manage.common.vo.model;

import io.swagger.annotations.ApiModelProperty;

public class ModelLine {
    @ApiModelProperty(value = "线id")
    private String id;
    @ApiModelProperty(value = "来源id，来源节点id")
    private String sourceCell;
    @ApiModelProperty(value = "来源端口，基本上是out")
    private String sourcePort;
    @ApiModelProperty(value = "目标id，目标节点id")
    private String targetCell;
    @ApiModelProperty(value = "目标端口，比如：in1，in2 ，up等等")
    private String targetPort;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSourceCell() {
        return sourceCell;
    }

    public void setSourceCell(String sourceCell) {
        this.sourceCell = sourceCell;
    }

    public String getSourcePort() {
        return sourcePort;
    }

    public void setSourcePort(String sourcePort) {
        this.sourcePort = sourcePort;
    }

    public String getTargetCell() {
        return targetCell;
    }

    public void setTargetCell(String targetCell) {
        this.targetCell = targetCell;
    }

    public String getTargetPort() {
        return targetPort;
    }

    public void setTargetPort(String targetPort) {
        this.targetPort = targetPort;
    }
}
