package cc.crrc.manage.common.vo.model;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class ModelNodeInfo {
    @ApiModelProperty(value = "节点id")
    private String id;
    @ApiModelProperty(value = "节点name")
    private String name;
    @ApiModelProperty(value = "items中的id的值（所有端口类型）")
    private List<JSONObject> itemIdList;
    @ApiModelProperty(value = "data下必有type，不一定有value")
    private JSONObject data;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<JSONObject> getItemIdList() {
        return itemIdList;
    }

    public void setItemIdList(List<JSONObject> itemIdList) {
        this.itemIdList = itemIdList;
    }

    public JSONObject getData() {
        return data;
    }

    public void setData(JSONObject data) {
        this.data = data;
    }
}
