package cc.crrc.manage.common.vo;

import java.util.ArrayList;
import java.util.List;

public class TreeNodeForStructureVO<T> {
    // 结构编码
    protected Object structureCode;
    // 父结构编码
    protected Object parentStructureCode;
    // 所属子系统
    protected String subSystem;
    // 所属子系统的key
    protected String systemKey;
    // 结构类型（车厢/子系统/部件）
    protected String structureType;
    // 节点类型（根节点/子节点/叶节点）
    protected String nodeType;
    // 结构中文名
    protected String nameCn;
    // 结构英文名
    protected String nameEn;
    // 结构英文名缩写
    protected String shortNameEn;
    // 顺序编号
    protected Long sortNumber;
    List<T> children = new ArrayList<T>();

    public List<T> getChildren() {
        return children;
    }

    public void setChildren(List<T> children) {
        this.children = children;
    }

    public void add(T node){
        children.add(node);
    }

    public Object getStructureCode() {
        return structureCode;
    }

    public void setStructureCode(Object structureCode) {
        this.structureCode = structureCode;
    }

    public Object getParentStructureCode() {
        return parentStructureCode;
    }

    public void setParentStructureCode(Object parentStructureCode) {
        this.parentStructureCode = parentStructureCode;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }

    public String getSubSystem() {
        return subSystem;
    }

    public void setSubSystem(String subSystem) {
        this.subSystem = subSystem;
    }

    public String getStructureType() {
        return structureType;
    }

    public void setStructureType(String structureType) {
        this.structureType = structureType;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getShortNameEn() {
        return shortNameEn;
    }

    public void setShortNameEn(String shortNameEn) {
        this.shortNameEn = shortNameEn;
    }

    public Long getSortNumber() {
        return sortNumber;
    }

    public void setSortNumber(Long sortNumber) {
        this.sortNumber = sortNumber;
    }

    public String getSystemKey() {
        return systemKey;
    }

    public void setSystemKey(String systemKey) {
        this.systemKey = systemKey;
    }

}
