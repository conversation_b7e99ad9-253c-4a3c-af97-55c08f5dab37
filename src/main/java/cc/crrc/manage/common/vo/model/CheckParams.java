package cc.crrc.manage.common.vo.model;

import io.swagger.annotations.ApiModelProperty;

import java.util.HashMap;
import java.util.List;

public class CheckParams {

    @ApiModelProperty(value = "jsonArray中所有线的信息")
    private List<ModelLine> modelLineList;
    @ApiModelProperty(value = "起点节点信息：节点id - 节点name")
    private HashMap<String,String> initialNodeMap;
    @ApiModelProperty(value = "jsonArray中所有节点的信息")
    private HashMap<String,ModelNodeInfo> allNodeMap;
    @ApiModelProperty(value = "jsonArray中所有常量节点的信息")
    private HashMap<String,ModelNodeInfo> allConstantNodeMap;
    @ApiModelProperty(value = "jsonArray中所有函数节点的信息")
    private HashMap<String,ModelNodeInfo> allFunctionNodeMap;
    @ApiModelProperty(value = "故障节点id")
    private String faultNodeId;
    @ApiModelProperty(value = "故障节点出现的次数")
    private int faultNodeCount;

    public List<ModelLine> getModelLineList() {
        return modelLineList;
    }

    public void setModelLineList(List<ModelLine> modelLineList) {
        this.modelLineList = modelLineList;
    }

    public HashMap<String, String> getInitialNodeMap() {
        return initialNodeMap;
    }

    public void setInitialNodeMap(HashMap<String, String> initialNodeMap) {
        this.initialNodeMap = initialNodeMap;
    }

    public HashMap<String, ModelNodeInfo> getAllNodeMap() {
        return allNodeMap;
    }

    public void setAllNodeMap(HashMap<String, ModelNodeInfo> allNodeMap) {
        this.allNodeMap = allNodeMap;
    }

    public String getFaultNodeId() {
        return faultNodeId;
    }

    public void setFaultNodeId(String faultNodeId) {
        this.faultNodeId = faultNodeId;
    }

    public int getFaultNodeCount() {
        return faultNodeCount;
    }

    public void setFaultNodeCount(int faultNodeCount) {
        this.faultNodeCount = faultNodeCount;
    }

    public HashMap<String, ModelNodeInfo> getAllConstantNodeMap() {
        return allConstantNodeMap;
    }

    public void setAllConstantNodeMap(HashMap<String, ModelNodeInfo> allConstantNodeMap) {
        this.allConstantNodeMap = allConstantNodeMap;
    }

    public HashMap<String, ModelNodeInfo> getAllFunctionNodeMap() {
        return allFunctionNodeMap;
    }

    public void setAllFunctionNodeMap(HashMap<String, ModelNodeInfo> allFunctionNodeMap) {
        this.allFunctionNodeMap = allFunctionNodeMap;
    }
}
