package cc.crrc.manage.common.vo;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * @FileName PageInfoExtend
 * <AUTHOR> xin
 * @Date 2020/4/1 10:07
 * @Version 1.0
 **/

public class PageInfoExtend extends PageInfo {
    private int confirmNumber;

    private int notConfirmNumber;

    private int totalFaultNumber;

    public int getConfirmNumber() {
        return confirmNumber;
    }

    public void setConfirmNumber(int confirmNumber) {
        this.confirmNumber = confirmNumber;
    }

    public int getNotConfirmNumber() {
        return notConfirmNumber;
    }

    public void setNotConfirmNumber(int notConfirmNumber) {
        this.notConfirmNumber = notConfirmNumber;
    }

    public int getTotalFaultNumber() {
        return totalFaultNumber;
    }

    public void setTotalFaultNumber(int totalFaultNumber) {
        this.totalFaultNumber = totalFaultNumber;
    }
}
