package cc.crrc.manage.websocket;

import cc.crrc.manage.common.utils.SpringBeanUtils;
import com.alibaba.fastjson.JSONObject;

import java.lang.reflect.Method;
import java.util.HashMap;

public class WebSocketDispatcher {

    // 反射执行缓存，提高getMethod速度。key为类名+方法名。
    private static HashMap<String, CacheObject> invokeCache = new HashMap<>();

    // 用于缓存的内部类
    static class CacheObject {
        Method method;
        Object object;

        CacheObject(Method method, Object object) {
            this.method = method;
            this.object = object;
        }

        Method getMethod() {
            return method;
        }

        Object getObject() {
            return object;
        }
    }

    /**
     * 从json对象中获取cmd字段并返回与其相匹配的枚举类。
     * 若请求信息中无cmd字段，或无法匹配枚举类，则返回null
     *
     * <AUTHOR> <PERSON>
     * 2019/11/1
     **/
    private static WebSocketRouteEnum getWebSocketRouteEnumFromJson(JSONObject messageJson) {
        if (messageJson.containsKey("cmd")) {
            String cmd = messageJson.getString("cmd");
            return WebSocketRouteEnum.getWebSocketRouteEnum(cmd);
        }
        return null;
    }

    /**
     * 从json对象中获取message字段并返回。
     * 若无message字段，返回null
     *
     * <AUTHOR> GuoYang
     * 2019/11/1
     **/
    private static String getMessageFromJson(JSONObject messageJson) {
        return messageJson.containsKey("message") ? messageJson.getString("message") : null;
    }

    /**
     * 处理websocket接受到的message，并分配到相应的发送函数予以处理。
     * <p>
     * 若解析异常，发送异常信息。
     * 若cmd对应的时间间隔为-1，则分配到单发一次的函数之中。
     *
     * <AUTHOR> GuoYang
     * 2019/11/1
     **/
    public static void processMessage(WebSocketServer currentWSServer, String message) throws Exception {
        JSONObject messageJson = JSONObject.parseObject(message);
        WebSocketRouteEnum webSocketRouteEnum = getWebSocketRouteEnumFromJson(messageJson);
        String messageValue = getMessageFromJson(messageJson);
        // 根据前端需要 添加接受消息异常时的返回信息
        if (webSocketRouteEnum == null || messageValue == null) {
            currentWSServer.sendMessage("参数有误");
        } else if (webSocketRouteEnum.getIntervalTime() == -1) {// 若cmd有特殊要求，可在此处添加条件，单独发送消息。
            currentWSServer.singleSendMessage(webSocketRouteEnum, currentWSServer, messageValue, 0);
        } else {
            currentWSServer.cycleSendMessage(webSocketRouteEnum, messageValue);
        }

    }

    /**
     * websocket路由方法
     * 根据获得的cmd数据，选定相应的执行方法。
     *
     * <AUTHOR> GuoYang
     * 2019/11/1
     **/
    public static String execute(WebSocketRouteEnum webSocketRouteEnum, WebSocketServer wsServer, String message) throws Exception {
        String targetMethod = webSocketRouteEnum.getTargetMethod();
        String cacheKey = webSocketRouteEnum.getTargetClass() + targetMethod;
        Method method;
        Object object;
        // 缓存机制，提高获取Method速度。并且不用每次调用都创建对象，减少内存占用。
        if (invokeCache.containsKey(cacheKey)) {
            CacheObject cacheObject = invokeCache.get(cacheKey);
            method = cacheObject.getMethod();
            object = cacheObject.getObject();
        } else {
            Object targetClass = SpringBeanUtils.getBean(webSocketRouteEnum.getTargetClass());
            // 执行函数,根据路由配置是否开启ws上下文，选择不同加载方式
            if (webSocketRouteEnum.getHasCache()) {
                method = targetClass.getClass().getMethod(targetMethod, WebSocketServer.class, String.class);
            } else {
                method = targetClass.getClass().getMethod(targetMethod, String.class);
            }
            // 此处根据业务需要，可能需要getBean，从容器中获取。
            object = targetClass;
            CacheObject newCacheObject = new CacheObject(method, object);
            invokeCache.put(cacheKey, newCacheObject);
        }
        // 返回类型必须为String类型，或实现toString()方法。
        Object res;
        if (webSocketRouteEnum.getHasCache()) {
            res = method.invoke(object, wsServer, message);
        } else {
            res = method.invoke(object, message);
        }
        return res == null ? null : String.valueOf(res);
    }

}

