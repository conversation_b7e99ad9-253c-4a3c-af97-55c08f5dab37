package cc.crrc.manage.websocket.controller;

import cc.crrc.manage.websocket.entity.MonitorMenuEntityPO;
import cc.crrc.manage.websocket.service.MonitoringService.SocketMonitorMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description：菜单控制层
 * @FileName: MonitorMenuController
 * @Author: liu xinchen
 * @Date: 2020/7/16
 * @Version 1.0
 */
@RestController
@RequestMapping(value = "/websocket/monitoringConfig/monitorMenu")
public class SocketMonitorMenuController {

    @Autowired
    private SocketMonitorMenuService monitorMenuService;

    /**
     * @Description 菜单查询
     * @Author: liu xinchen
     * @Param: [monitorMenuEntityPO]
     * @return: java.lang.Object
     * @Date: 2020/7/20
     */
    @PostMapping(value = "/list")
    public Object getMonitorMenuList(@RequestBody MonitorMenuEntityPO monitorMenuEntityPO) {
        return monitorMenuService.getMonitorMenuList(monitorMenuEntityPO);
    }

    /**
     * @Description 查询父子结构菜单信息
     * @Author: liu xinchen
     * @Param: [monitorMenuEntityPO]
     * @return: java.lang.Object
     * @Date: 2020/7/24
     */
    @PostMapping(value = "/getMenuList")
    public Object getMenuList(@RequestBody MonitorMenuEntityPO monitorMenuEntityPO) {
        return monitorMenuService.getMenuList(monitorMenuEntityPO);
    }
    @GetMapping(value = "/labelAndLogo")
    public Object listLabelAndLogo(String itemId) {
        return monitorMenuService.listLabelAndLogo(itemId);
    }

}