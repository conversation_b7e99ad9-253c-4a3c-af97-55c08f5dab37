package cc.crrc.manage.websocket.controller;



import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.monitoringConfig.entity.SvgMapEntity;
import cc.crrc.manage.monitoringConfig.service.SvgMapService;
import cc.crrc.manage.websocket.service.MonitoringService.SocketSvgMapService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * @FileName SvgCodeController
 * <AUTHOR> xin
 * @Date 2020/8/13 10:39
 * @Version 1.0
 **/
@RestController
@RequestMapping(value = "/websocket/monitoringConfig/svg")
public class SocketSvgMapController {
    @Autowired
    private SocketSvgMapService svgMapService;

    /*
     * @return java.lang.Object
     * @Description 根据线路id获取svgMap信息
     * <AUTHOR> xin
     * @Date 13:10 2020/8/13
     * @Param [itemEntity]
     **/
    @GetMapping(value = "/getSvgMapInfoByLineId")
    public Object getSvgMapInfoByLineId(@RequestParam String lineId) {
        return svgMapService.getSvgMapInfoByLineId(lineId);
    }




    /*
     * @return java.lang.Object
     * @Description 根据线路id删除配置所需的css
     * <AUTHOR> xin
     * @Date 13:10 2022/2/16
     * @Param [itemEntity]
     **/
    @GetMapping(value = "/getCssByLineId")
    public Object getCssByLineId(@RequestParam String lineId) {
        return svgMapService.getCssByLineId(lineId);
    }
    /*
     * @return java.lang.Object
     * @Description 根根据菜单code删除配置所需的html
     * <AUTHOR> xin
     * @Date 13:10 2022/2/16
     * @Param [itemEntity]
     **/
    @GetMapping(value = "/getHtmlByMenuCode")
    public Object deleteHtmlByMenuCode(@RequestParam String menuCode) {
        return svgMapService.getHtmlByMenuCode(menuCode);
    }
}
