package cc.crrc.manage.websocket.entity;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * @Description：菜单实体类
 * @FileName: monitorMenuEntity
 * @Author: liu x<PERSON><PERSON>
 * @Date: 2020/7/16
 */
public class MonitorMenuEntity implements Serializable {
    private static final Long  serialVersionUID = 1L;
    // 菜单id
    private String id;
    // 菜单编码
    private String menuCode;
    // 父级菜单id
    private String parentId;
    // 菜单名称
    private String name;
    // 菜单排序
    private String sort;
    // 下方仪表盘启用状态
    private Boolean boardStatus;
    // 菜单类型 1.基础类型  2.url跳转  3.svg  4.(等等)
    private String menuType;
    // url跳转地址
    private String url;
    // 创建时间
    private Timestamp createTime;
    // 创建人
    private String createBy;
    // 修改时间
    private Timestamp modifyTime;
    // 修改人
    private String modifyBy;
    // 删除标识
    private Boolean delFlag;
    // 菜单显示隐藏状态
    private Boolean showStatus;
    // 所属车辆
    private String traCode;
    // 前端组件地址
    private String componentsUrl;
    // 默认激活
    private Boolean defaultActive;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMenuCode() {
        return menuCode;
    }

    public void setMenuCode(String menuCode) {
        this.menuCode = menuCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public Boolean getBoardStatus() {
        return boardStatus;
    }

    public void setBoardStatus(Boolean boardStatus) {
        this.boardStatus = boardStatus;
    }

    public String getMenuType() {
        return menuType;
    }

    public void setMenuType(String menuType) {
        this.menuType = menuType;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Timestamp getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Timestamp modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Boolean getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Boolean delFlag) {
        this.delFlag = delFlag;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public Boolean getShowStatus() {
        return showStatus;
    }

    public void setShowStatus(Boolean showStatus) {
        this.showStatus = showStatus;
    }

    public String getTraCode() {
        return traCode;
    }

    public void setTraCode(String traCode) {
        this.traCode = traCode;
    }

    public String getComponentsUrl() {
        return componentsUrl;
    }

    public void setComponentsUrl(String componentsUrl) {
        this.componentsUrl = componentsUrl;
    }

    public Boolean getDefaultActive() {
        return defaultActive;
    }

    public void setDefaultActive(Boolean defaultActive) {
        this.defaultActive = defaultActive;
    }
}
