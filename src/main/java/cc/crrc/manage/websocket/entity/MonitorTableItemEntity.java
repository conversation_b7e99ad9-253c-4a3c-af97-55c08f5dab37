package cc.crrc.manage.websocket.entity;

import java.util.Date;
import java.util.List;

public class MonitorTableItemEntity {
/**
 * @FileName monitorTableItemEntity
 * <AUTHOR> xin
 * @Date 2020/7/16 17:44
 * @Version 1.0
 **/

private String id;
    private String menuId;//项点所属菜单id
    private String name;//项点名称
    private String type;//项点类型 车厢还是基本
    private String sort;//排序
    private String relationKey;//前端需要绑定的一个key值
    private String carType;//车厢类型
    private String signalFunctionId;//信号处理脚本id
    private Date createTime;
    private String createBy;
    private Date modifyTime;
    private String modifyBy;
    private Boolean delFlag;
    private Integer pubAndPri;//公私有标识
    private String slotBoardId;//板卡id

    //前端需要的一个拼接字段 哪个菜单下的那个项点名称 例子：（状态总览）受电弓
    private String menuItemName;
    private String carTypeName;
    private String slotBoardName;

    private long column;//行
    private long row;//列
    private int slotNumber;//单个车厢卡槽个数
    private List<String> formatStruList;//格式结构list（数据可填充普通菜单的 vehicle车厢list  或者板卡的所属板卡id）

    //SVG类型菜单保存的时候所需
    private String svgJson;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }


    public String getMenuId() {
        return menuId;
    }

    public void setMenuId(String menuId) {
        this.menuId = menuId;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }


    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }


    public String getCarType() {
        return carType;
    }

    public void setCarType(String carType) {
        this.carType = carType;
    }


    public String getSignalFunctionId() {
        return signalFunctionId;
    }

    public void setSignalFunctionId(String signalFunctionId) {
        this.signalFunctionId = signalFunctionId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }



    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Boolean getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Boolean delFlag) {
        this.delFlag = delFlag;
    }

    public long getColumn() {
        return column;
    }

    public void setColumn(long column) {
        this.column = column;
    }

    public long getRow() {
        return row;
    }

    public void setRow(long row) {
        this.row = row;
    }

    public int getSlotNumber() {
        return slotNumber;
    }

    public void setSlotNumber(int slotNumber) {
        this.slotNumber = slotNumber;
    }

    public List<String> getFormatStruList() {
        return formatStruList;
    }

    public void setFormatStruList(List<String> formatStruList) {
        this.formatStruList = formatStruList;
    }

    public Integer getPubAndPri() {
        return pubAndPri;
    }

    public void setPubAndPri(Integer pubAndPri) {
        this.pubAndPri = pubAndPri;
    }

    public String getMenuItemName() {
        return menuItemName;
    }

    public void setMenuItemName(String menuItemName) {
        this.menuItemName = menuItemName;
    }

    public String getRelationKey() {
        return relationKey;
    }

    public void setRelationKey(String relationKey) {
        this.relationKey = relationKey;
    }

    public String getSlotBoardId() {
        return slotBoardId;
    }

    public void setSlotBoardId(String slotBoardId) {
        this.slotBoardId = slotBoardId;
    }

    public String getCarTypeName() {
        return carTypeName;
    }

    public void setCarTypeName(String carTypeName) {
        this.carTypeName = carTypeName;
    }

    public String getSlotBoardName() {
        return slotBoardName;
    }

    public void setSlotBoardName(String slotBoardName) {
        this.slotBoardName = slotBoardName;
    }

    public String getSvgJson() {
        return svgJson;
    }

    public void setSvgJson(String svgJson) {
        this.svgJson = svgJson;
    }
}
