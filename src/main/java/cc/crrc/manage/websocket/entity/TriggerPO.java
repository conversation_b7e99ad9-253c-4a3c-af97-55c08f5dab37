package cc.crrc.manage.websocket.entity;

/**
 * @FileName TriggerPO
 * @Description trigger实体类
 * <AUTHOR>
 * @Date 2020/7/16 19:57
 **/
public class TriggerPO extends ConfigBasePO {

    //id(UUID生成)
    private String id;
    //卡槽id
    private String slotId;
    //卡槽类型
    private Integer slotType;
    //状态名称(例如:车门关闭)
    private String label;
    //排序编码(手动输入)
    private Integer sort;
    //信号id
    private String signalId;
    //信号中文名
    private String signalNameCn;
    //信号英文名
    private String signalNameEn;
    //状态触发值(例:0车门关闭,1车门开启)
    private String triggerValue;
    //前端数据展示样式(例：数值、数值+图片...)
    private String dataDisplayPoint;
    //单位有无
    private Boolean unitStatus;
    //图片地址
    private String svgUrl;
    //图片包路径
    private String imageType;
    //图片类型
    private String imagePath;
    //信号的扩展属性(例：RIOM中管道的多个值 json格式的字符串)
    private String extProperties;

    //每个trigger对象对应的数据状态（新增还是修改）保存时使用
    private String updeteOrInsertFlag;

    //trigger对应信号的resultType类型
    private String resultType;

    public String getSignalNameEn() {
        return signalNameEn;
    }

    public void setSignalNameEn(String signalNameEn) {
        this.signalNameEn = signalNameEn;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSlotId() {
        return slotId;
    }

    public void setSlotId(String slotId) { this.slotId = slotId; }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) { this.sort = sort; }

    public String getSignalId() {
        return signalId;
    }

    public void setSignalId(String signalId) {
        this.signalId = signalId;
    }

    public String getSignalNameCn() {
        return signalNameCn;
    }

    public void setSignalNameCn(String signalNameCn) {
        this.signalNameCn = signalNameCn;
    }

    public String getTriggerValue() {
        return triggerValue;
    }

    public void setTriggerValue(String triggerValue) {
        this.triggerValue = triggerValue;
    }

    public String getDataDisplayPoint() { return dataDisplayPoint; }

    public void setDataDisplayPoint(String dataDisplayPoint) {
        this.dataDisplayPoint = dataDisplayPoint;
    }

    public Boolean getUnitStatus() {
        return unitStatus;
    }

    public void setUnitStatus(Boolean unitStatus) {
        this.unitStatus = unitStatus;
    }

    public String getSvgUrl() {
        return svgUrl;
    }

    public void setSvgUrl(String svgUrl) {
        this.svgUrl = svgUrl;
    }

    public String getExtProperties() {
        return extProperties;
    }

    public void setExtProperties(String extProperties) {
        this.extProperties = extProperties;
    }

    public Integer getSlotType() { return slotType; }

    public void setSlotType(Integer slotType) { this.slotType = slotType; }

    public String getUpdeteOrInsertFlag() {
        return updeteOrInsertFlag;
    }

    public void setUpdeteOrInsertFlag(String updeteOrInsertFlag) {
        this.updeteOrInsertFlag = updeteOrInsertFlag;
    }

    public String getResultType() {
        return resultType;
    }

    public void setResultType(String resultType) {
        this.resultType = resultType;
    }

    public String getImageType() {
        return imageType;
    }

    public void setImageType(String imageType) {
        this.imageType = imageType;
    }

    public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }
}
