package cc.crrc.manage.websocket.entity;

import java.util.Date;

public class SignalTrendWarningEntity {
    private static final long serialVersionUID = 1L;
    private Long id;
    private Long protocolId;
    private String signalNameEn;
    private String createBy;
    private Date createTime;
    private String modifyBy;
    private Date modifyTime;
    private String remark;
    private String subsystem;
    private String modelName;
    private String modelRmse;

    private String carLocation;
    private String nameCn;
    private String unit;
    private String maxValue;
    private String minValue;


    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getCarLocation() {
        return carLocation;
    }

    public void setCarLocation(String carLocation) {
        this.carLocation = carLocation;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getMaxValue() {
        return maxValue;
    }

    public void setMaxValue(String maxValue) {
        this.maxValue = maxValue;
    }

    public String getMinValue() {
        return minValue;
    }

    public void setMinValue(String minValue) {
        this.minValue = minValue;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProtocolId() {
        return protocolId;
    }

    public void setProtocolId(Long protocolId) {
        this.protocolId = protocolId;
    }

    public String getSignalNameEn() {
        return signalNameEn;
    }

    public void setSignalNameEn(String signalNameEn) {
        this.signalNameEn = signalNameEn;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getModelRmse() {
        return modelRmse;
    }

    public void setModelRmse(String modelRmse) {
        this.modelRmse = modelRmse;
    }
}

