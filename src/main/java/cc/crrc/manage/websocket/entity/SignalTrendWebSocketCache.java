package cc.crrc.manage.websocket.entity;

import java.util.HashMap;
import java.util.TreeMap;

/**
 * 信号趋势预测-websocket数据缓存实体类
 *
 * <AUTHOR>
 * 2021/4/20
 */
public class SignalTrendWebSocketCache {

    // 用于保存单个webSocket中所有曲线最后一包的时间戳，用于清除多余数据，提高传输和前端渲染性能。
    private final HashMap<String, Long> signalLineLastTimeMap;
    // 信号数值缓存 统一发送
    private final HashMap<String, TreeMap<String, Double[]>> signalValueCache;
    // 是否第一次传输信号数据
    private volatile boolean first;

    public SignalTrendWebSocketCache() {
        signalLineLastTimeMap = new HashMap<>();
        signalValueCache = new HashMap<>();
        first = true;
    }

    public HashMap<String, Long> getSignalLineLastTimeMap() {
        return signalLineLastTimeMap;
    }

    public HashMap<String, TreeMap<String, Double[]>> getSignalValueCache() {
        return signalValueCache;
    }

    public boolean isFirst() {
        return first;
    }

    public void setFirst(boolean first) {
        this.first = first;
    }
}
