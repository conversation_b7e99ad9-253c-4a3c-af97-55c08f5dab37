package cc.crrc.manage.websocket.entity;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;

import java.util.HashMap;

public class OpenTSDBQueryEntity {
    private String metric;
    private tagsObject tags;
    private HashMap<String,Double> dps;

    public OpenTSDBQueryEntity(){
        this.metric = null;
        this.tags = new tagsObject(new JSONObject());
        this.dps = new HashMap<>();
    }

    public OpenTSDBQueryEntity(JSONObject queryJson) {
        this.metric = queryJson.getString("metric");
        this.tags = new tagsObject(queryJson.getJSONObject("tags"));
        this.dps = JSONObject.parseObject(queryJson.getString("dps"),
                new TypeReference<HashMap<String,Double>>(){});
    }

    public String getMetric() {
        return metric;
    }

    public void setMetric(String metric) {
        this.metric = metric;
    }

    public tagsObject getTags() {
        return tags;
    }

    public void setTags(tagsObject tags) {
        this.tags = tags;
    }

    public HashMap<String, Double> getDps() {
        return dps;
    }

    public void setDps(HashMap<String, Double> dps) {
        this.dps = dps;
    }

    public String getVId() {
        return tags.vId;
    }

    public void setVId(String vId) {
        this.tags.vId = vId;
    }

    public String getType() {
        return tags.type;
    }

    public void setType(String type) {
        this.tags.type = type;
    }

    public String getTrainId() {
        return tags.trainId;
    }

    public void setTrainId(String trainId) {
        this.tags.trainId = trainId;
    }

    public String getPId() {
        return tags.pId;
    }

    public void setPId(String pId) {
        this.tags.pId = pId;
    }

    static class tagsObject{
        private String vId;
        private String type;
        private String trainId;
        private String pId;

        public tagsObject(JSONObject tagsJson) {
            this.vId = tagsJson.getString("v_id");
            this.type = tagsJson.getString("type");
            this.trainId = tagsJson.getString("train_id");
            this.pId = tagsJson.getString("p_id");
        }
    }
}

