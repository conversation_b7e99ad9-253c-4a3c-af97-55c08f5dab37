package cc.crrc.manage.websocket.entity;

import java.util.List;

/**
 * @Author: guo wei
 * 2020-08-11
 */
public class WebsocketSlotEntity extends MonitorSlotEntity {
    private List<TriggerPO> websocketTriggerEntity;

    public List<TriggerPO> getWebsocketTriggerEntity() {
        return websocketTriggerEntity;
    }

    public void setWebsocketTriggerEntity(List<TriggerPO> websocketTriggerEntity) {
        this.websocketTriggerEntity = websocketTriggerEntity;
    }
}
