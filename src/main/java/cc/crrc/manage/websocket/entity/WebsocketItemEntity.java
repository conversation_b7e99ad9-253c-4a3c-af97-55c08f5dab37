package cc.crrc.manage.websocket.entity;

import java.util.List;

/**
 * @Author: guo wei
 * 2020-08-11
 */
public class WebsocketItemEntity extends MonitorTableItemEntity {
    private MonitorSignalFunctionPO monitorSignalFunctionPO;
    private List<WebsocketFormatEntity> websocketFormatEntity;

    public MonitorSignalFunctionPO getMonitorSignalFunctionPO() {
        return monitorSignalFunctionPO;
    }

    public void setMonitorSignalFunctionPO(MonitorSignalFunctionPO monitorSignalFunctionPO) {
        this.monitorSignalFunctionPO = monitorSignalFunctionPO;
    }

    public List<WebsocketFormatEntity> getWebsocketFormatEntity() {
        return websocketFormatEntity;
    }

    public void setWebsocketFormatEntity(List<WebsocketFormatEntity> websocketFormatEntity) {
        this.websocketFormatEntity = websocketFormatEntity;
    }
}
