package cc.crrc.manage.websocket.dao;

import cc.crrc.manage.websocket.entity.SignalTrendWarningEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SignalTrendWarningDao {

    List<SignalTrendWarningEntity> getSignalBasicInfo(@Param("protocolId")String protocolId, @Param("subsystem")String subsystem);
}
