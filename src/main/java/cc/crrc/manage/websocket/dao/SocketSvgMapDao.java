package cc.crrc.manage.websocket.dao;

import cc.crrc.manage.monitoringConfig.entity.MonitorPageInfoPO;
import cc.crrc.manage.websocket.entity.SvgMapEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
@Mapper
public interface SocketSvgMapDao {


    SvgMapEntity getSvgMapInfoByLineId(@Param("lineId") String lineId);

    MonitorPageInfoPO getHtmlByMenuCode(@Param("menuCode") String menuCode);

    MonitorPageInfoPO getCssByLineId(@Param("lineId") String lineId);


}
