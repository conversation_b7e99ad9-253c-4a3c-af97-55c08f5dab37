package cc.crrc.manage.websocket.dao;

import cc.crrc.manage.websocket.entity.WebsocketVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
@Mapper
public interface WSMonitorDao {

    List<WebsocketVO> getWebsocketVO(@Param("trainCode") String trainCode);

}
