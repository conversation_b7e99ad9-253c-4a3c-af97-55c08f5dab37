package cc.crrc.manage.websocket.dao;

import cc.crrc.manage.websocket.entity.TriggerPO;
import cc.crrc.manage.websocket.entity.MonitorMenuEntityPO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @Description：菜单数据访问层
 * @FileName: MonitorMenuDao
 * @Author: liu xinchen
 * @Date: 2020/7/16
 */
@Repository
@Mapper
public interface SocketMonitorMenuDao {

    List<MonitorMenuEntityPO> getMonitorMenuList(MonitorMenuEntityPO monitorMenuEntityPO);

    List<MonitorMenuEntityPO> getMenuList(MonitorMenuEntityPO monitorMenuEntityPO);

    List<TriggerPO> listLabelAndLogo(String itemId);

}
