package cc.crrc.manage.websocket.service;


import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.DateUtils;
import cc.crrc.manage.common.utils.ShadowJsonUtils;
import cc.crrc.manage.common.utils.RedisUtils;
import cc.crrc.manage.common.utils.StringUtils;
import cc.crrc.manage.mapper.SysDictMapping;
import cc.crrc.manage.mapper.analysis.AnalysisMapping;
import cc.crrc.manage.mapper.draw.DrawLineConfigMapping;
import cc.crrc.manage.mapper.line.LineMapping;
import cc.crrc.manage.mapper.mtc.MtcAlarmWarningMapping;
import cc.crrc.manage.mapper.mtc.MtcAutoFaultRecordMapping;
import cc.crrc.manage.mapper.mtr.MtrVehicleMapping;
import cc.crrc.manage.pojo.SysDictDTO;
import cc.crrc.manage.pojo.SysDictVO;
import cc.crrc.manage.pojo.draw.DrawLineConfigVO;
import cc.crrc.manage.pojo.line.StationDTO;
import cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.QueryTimeoutException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DecimalFormat;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;


@Service
@Transactional(readOnly = false)
public class SubwayLineSocketService {

    private final Logger logger = LoggerFactory.getLogger(SubwayLineSocketService.class);

    @Autowired
    private LineMapping lineMapping;
    @Autowired
    private MtrVehicleMapping mtrVehicleMapping;
    @Autowired
    private AnalysisMapping analysisMapping;
    @Autowired
    private MtcAlarmWarningMapping mtcAlarmWarningMapping;
    @Autowired
    private SysDictMapping sysDictMapping;
    @Autowired
    private DrawLineConfigMapping drawLineConfigMapping;

    @Autowired
    Executor asyncServiceExecutor;//异步线程池

    @Autowired
    private MtcAutoFaultRecordMapping mtcAutoFaultRecordMapping;

    private static ConcurrentHashMap<String, String> startStationCache = new ConcurrentHashMap<>();

    private static ConcurrentHashMap<String, List<StationDTO>> stationInfoCache = new ConcurrentHashMap<>();

    private static ConcurrentHashMap<String, HashMap<String, String>> staNameInfo = new ConcurrentHashMap<>();

    private static ConcurrentHashMap<String, HashMap<String, String>> staDirection = new ConcurrentHashMap<>();

    private static ConcurrentHashMap<String, HashMap<String, Object>> faultCountNum = new ConcurrentHashMap<>();

    private static ConcurrentHashMap<String, HashMap<String, Object>> Distance = new ConcurrentHashMap<>();

    private static ConcurrentHashMap<String, HashMap<String, Object>> TotalEngyCon = new ConcurrentHashMap<>();

    private static ConcurrentHashMap<String, HashMap<String, Object>> DistanceAndTotalEngyList = new ConcurrentHashMap<>();

    private static List<StationDTO> staList = new ArrayList<>();

    private static ConcurrentHashMap<String, List<DrawLineConfigVO>> drawLineList = new ConcurrentHashMap<String, List<DrawLineConfigVO>>();


    /**
     * 线网监控 websocket 实时推送
     * 若在本机部署 访问接口为 ws://127.0.0.1:8085/api/websocket
     * 发送message为 {"cmd":"subwayLineMonitor","message":{"lineId":"24"}}
     * (cmd 和 轮询时间配置 在枚举类WebSocketRouteEnum中)
     *
     * @param message 线路id 例如 {"lineId":"24"}
     * @return java.lang.String
     * <AUTHOR>
     * 2021/06/01
     **/
    public String getAll(String message) {
        JSONObject messageJson = JSONObject.parseObject(message);
        String lineId = messageJson.getString("lineId");
        List<MtrVehiclePO> vehicleCodes = mtrVehicleMapping.listVehicle(lineId);
        JSONObject returnObject = new JSONObject();
        List<String> onlineVehicles = new ArrayList<>();
        List<String> operationVehicles = new ArrayList<>();//投运车辆
        Boolean LineDataStatus = true;
        try {
            JSONArray lineOverview = new JSONArray();
            //统一处理缓存数据
            unifiedProcessingOfCachedData(lineId);
//            //协调多线程同步数据返回//多线程异步处理 暂时不用 请勿删除
//            CountDownLatch latch = new CountDownLatch(vehicleCodes.size());
//            //统一查询redis数据2021-10-14 测试redis一次性获取多个key值用
//            long startforlistgetredis = System.currentTimeMillis();
//            List<String> list = new ArrayList<>();
//            for (MtrVehiclePO vehicle : vehicleCodes) {
//                    list.add(vehicle.getVehicleCode()+"_shadow_new");
//            }
//            ArrayList listForredis1 = (ArrayList) RedisUtils.redisTemplate.opsForValue().multiGet(list);

            for (MtrVehiclePO vehicle : vehicleCodes) {
                String vehicleCode = vehicle.getVehicleCode();
                String vehicleNameCn = vehicle.getNameCn();
                //判断是否在线
                Boolean onlineStatus = onlineStatusOfTrain(vehicleCode);
                JSONObject trainOverview = new JSONObject();
                //获取redis数据
                JSONObject objResult = getRedisDateByVehicleCode(vehicleCode);
                if (null != objResult) {
                    try {
                        if (onlineStatus) {
                            onlineVehicles.add(vehicleCode);
                        }
                        // 贵阳2 线网监控-1是否在库 0:非正线（库内）,1:正线
                        // 投运状态
                        Integer expandPower = getstatus(objResult);
                        //如果判断状态不为空 且等于1 且车辆在线 那么车辆属于投运状态
                        if (null != expandPower && expandPower == 1 && onlineStatus) {
                            operationVehicles.add(vehicleCode);
                        }
                        // 贵阳2 线网监控-2 车辆里程 车辆能耗
                        getDistanceAndTotalEngy(trainOverview, objResult, vehicleCode);
                        // 贵阳2 线网监控-3 各个车辆的告警和预警故障数量统计
                        getAlarmOrWarningCountNum(trainOverview, vehicle.getId());
                        // 贵阳2 线网监控4 各个车辆车站信息 已经在两站之间的百分比
                        getStationAndLocation(trainOverview, objResult, lineId);
//                            //测试20210616 通用车站信息查询接口
//                            getStationsUniversal(lineId,objResult,trainOverview);
                        trainOverview.put("status", expandPower);
                        trainOverview.put("vehicleCode", vehicleCode);
                        trainOverview.put("vehicleNameCn", vehicleNameCn);
                        trainOverview.put("onlineStatus", onlineStatus);
                        trainOverview.put("dateStatus", "true");//车辆数据获取正常
                    } catch (Exception e) {
                        trainOverview.put("status", 0);
                        trainOverview.put("Distance", 0);
                        trainOverview.put("TotalEngyCon", 0);
                        trainOverview.put("vehicleCode", vehicleCode);
                        trainOverview.put("vehicleNameCn", vehicleNameCn);
                        trainOverview.put("onlineStatus", onlineStatus);
                        trainOverview.put("currentFaultsNum", 0);
                        trainOverview.put("warningFaultsNum", 0);
                        trainOverview.put("dateStatus", "false");

                        trainOverview.put("startStstion", "--");
                        trainOverview.put("currentStation", "--");
                        trainOverview.put("nextStation", "--");
                        trainOverview.put("endStation", "--");
                        trainOverview.put("position", "0.00");
                        trainOverview.put("direction", "");
                    }
                } else {
                    trainOverview.put("status", 0);
                    trainOverview.put("Distance", 0);
                    trainOverview.put("TotalEngyCon", 0);
                    trainOverview.put("vehicleCode", vehicleCode);
                    trainOverview.put("vehicleNameCn", vehicleNameCn);
                    trainOverview.put("onlineStatus", onlineStatus);
                    trainOverview.put("currentFaultsNum", 0);
                    trainOverview.put("warningFaultsNum", 0);
                    trainOverview.put("dateStatus", "false");

                    trainOverview.put("startStstion", "--");
                    trainOverview.put("currentStation", "--");
                    trainOverview.put("nextStation", "--");
                    trainOverview.put("endStation", "--");
                    trainOverview.put("position", "0.00");
                    trainOverview.put("direction", "");
                }
                lineOverview.add(trainOverview);
                //同步计算器 计数减1  //多线程异步处理 暂时不用 请勿删除
//                    latch.countDown();
//                });
            }
//            }
//            try {//多线程异步处理 暂时不用 请勿删除
//                latch.await();
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            }
//            //如果采用多线程异步处理需要对存进list里的数据重新排序
//            lineOverview.sort(Comparator.comparing(obj -> ((JSONObject) obj).getString("vehicleCode")));
            returnObject.put("SubwayLineOverview", lineOverview);
            //在线离线
            returnObject.put("OnlineCount", onlineVehicles.size());
            returnObject.put("OfflineCount", vehicleCodes.size() - onlineVehicles.size());
            //投运
            returnObject.put("OperationCount", operationVehicles.size());
            returnObject.put("UnOperationCount", vehicleCodes.size() - operationVehicles.size());
            //LineOverview本包数据是否异常的标识(正常)
            returnObject.put("LineDataStatus", LineDataStatus);
        } catch (QueryTimeoutException querye) {
            returnObject.put("LineOverview", "LineOverview，redis数据获取超时");
            returnObject.put("OnlineCount", 0);
            returnObject.put("OfflineCount", vehicleCodes.size());
            returnObject.put("OperationCount", 0);
            returnObject.put("UnOperationCount", vehicleCodes.size());
            //LineOverview本包数据是否异常的标识(异常)
            returnObject.put("LineDataStatus", false);
        } catch (Exception e) {
            returnObject.put("LineOverview", "LineOverview数据获取出现问题，请联系开发人员");
            returnObject.put("OnlineCount", 0);
            returnObject.put("OfflineCount", vehicleCodes.size());
            returnObject.put("OperationCount", 0);
            returnObject.put("UnOperationCount", vehicleCodes.size());
            //LineOverview本包数据是否异常的标识(异常)
            returnObject.put("LineDataStatus", false);
        }
        //2.线路故障统计
        // 告警
//        logger.info("=======线路故障统计 开始=======");
//        // 下面测试成功该方法注销
//        try {
//            JSONArray alarmList = getAlarmOrWarningList(lineId, "0", onlineVehicles);
//            returnObject.put("alarmList", alarmList);
//        } catch (Exception e) {
//            logger.error("SubwayLineSocketService  error {}", e.getMessage());
//            returnObject.put("alarmList", "alarmList数据获取出现问题，请联系开发人员");
//        }
        //自动上报故障分组（ddu/repair）
        try {
            JSONObject alarmGroupList = getAlarmGroupListNew(lineId, "0", onlineVehicles);
            if (alarmGroupList.size() == 0) {
                alarmGroupList.put("ddu", null);
                alarmGroupList.put("repair", null);
            }
            returnObject.put("alarmList", alarmGroupList);
        } catch (Exception e) {
            logger.error("SubwayLineSocketService  error {}", e.getMessage());
            returnObject.put("alarmList", "alarmList数据获取出现问题，请联系开发人员");
        }
        // 预警
        try {
            JSONArray warningList = getAlarmOrWarningList(lineId, "1", onlineVehicles);
            returnObject.put("warningList", warningList);
        } catch (Exception e) {
            logger.error("SubwayLineSocketService  error {}", e.getMessage());
            returnObject.put("warningList", "warningList数据获取出现问题，请联系开发人员");
        }

        // 当日告警故障历史
//        //下面测试成功该方法注销
//        try {
//            JSONArray currentDayAlarmList = getCurrentDayAlarmList(lineId, "0", onlineVehicles);
//            returnObject.put("currentDayAlarmList", currentDayAlarmList);
//        } catch (Exception e) {
//            logger.error("SubwayLineSocketService  error {}", e.getMessage());
//            returnObject.put("currentDayAlarmList", "currentDayAlarmList数据获取出现问题，请联系开发人员");
//        }
        // 当日告警故障历史 自动上报故障分组（ddu/repair）
        try {
            JSONObject currentDayAlarmList = getCurrentDayAlarmGroupList(lineId, "0", onlineVehicles);
            if (currentDayAlarmList.size() == 0) {
                currentDayAlarmList.put("ddu", null);
                currentDayAlarmList.put("repair", null);
            }
            returnObject.put("currentDayAlarmList", currentDayAlarmList);
        } catch (Exception e) {
            logger.error("SubwayLineSocketService  error {}", e.getMessage());
            returnObject.put("currentDayAlarmList", "currentDayAlarmList数据获取出现问题，请联系开发人员");
        }
//        logger.info("=======线路故障统计 结束=======");
        return returnObject.toString();
    }

    private void unifiedProcessingOfCachedData(String lineId) {
        //统一需要循环查询的数据列表
        /*1各个车辆的告警和预警故障数量统计*/
        List<HashMap<String, Object>> faultCountNumList = mtcAlarmWarningMapping.getAlarmCount(lineId);
        JSONObject a = new JSONObject();
        for (HashMap<String, Object> faultCount : faultCountNumList) {
            a.put(String.valueOf(faultCount.get("vehicleId")), faultCount.get("current"));
        }
        List<HashMap<String, Object>> warningCountNumList = mtcAlarmWarningMapping.getWarningCount(lineId);
        JSONObject b = new JSONObject();
        for (HashMap<String, Object> warningCount : warningCountNumList) {
            b.put(String.valueOf(warningCount.get("vehicleId")), warningCount.get("warning"));
        }
        faultCountNum.clear();
        List<HashMap<String, Object>> defValueList = mtcAlarmWarningMapping.getAlarmOrWarningCountDef(lineId);

        for (HashMap<String, Object> defValue : defValueList) {
            String vehicleId = (String) defValue.get("vehicleId");
            HashMap<String, Object> value = new HashMap<>();
            if (a.containsKey(vehicleId)) {
                value.put("current", (Long) a.get(vehicleId));
            } else {
                value.put("current", 0L);
            }
            if (b.containsKey(vehicleId)) {
                value.put("warning", (Long) b.get(vehicleId));
            } else {
                value.put("warning", 0L);
            }
            faultCountNum.put(vehicleId, value);
        }

        /*2车站信息 此处查询可以优化 2021-09-07   优化完成 2021-0908*/
        if (null == stationInfoCache.get(lineId)) {
            //第一步车站信息获取
            StationDTO stationDTO = new StationDTO();
            stationDTO.setMetroLineId(lineId);
            staList = lineMapping.getStation(stationDTO);
            stationInfoCache.put(lineId, staList);
            //在进行两步数据初始化
            //维护sta_id与站名之间的关系 维护sta_id 与车辆行驶方向的关系
            HashMap<String, String> staNameMap = new HashMap<>();
            HashMap<String, String> staDirectionMap = new HashMap<>();
            for (StationDTO stationInfo : staList) {
                staNameMap.put(stationInfo.getStaId(), stationInfo.getName());
                staDirectionMap.put(stationInfo.getStaId(), stationInfo.getDirection());
            }
            staNameInfo.put(lineId, staNameMap);
            staDirection.put(lineId, staDirectionMap);
        }
        /*3 车辆能耗与里程 统一查询处理
        List<HashMap<String, Object>> analysisList = analysisMapping.getTotalDistancesAndTotleEnergy(lineId,null);
        DistanceAndTotalEngyList.clear();
        for (HashMap<String, Object> analysis : analysisList) {
            DistanceAndTotalEngyList.put((String) analysis.get("vehicleCode"), analysis);
        }*/

        /*4 统一缓存 区段绘制信息 */
        if (null == drawLineList.get(lineId)) {
            String drawLineType = "3";//线路监控类型id
            List<DrawLineConfigVO> drawLineInfoList = drawLineConfigMapping.getDrawLineInfoList(lineId, drawLineType);
            drawLineList.put(lineId, drawLineInfoList);
        }
    }

    /**
     * @Description 获取车辆的车站信息 以及车辆行驶在两站之间的百分比
     * @Return com.alibaba.fastjson.JSONArray
     * <AUTHOR>
     * @Date 2021/6/4
     * @Param [objResult]
     **/
    private void getStationAndLocation(JSONObject trainOverview, JSONObject objResult, String lineId) {
        try {
            //判断主从逻辑
            int masterStatus = getMasterStatus(objResult);
            //贵阳2号线没有始发站 所以需要进行计算
            String startStationId = "0";
            String currentStationId = "0";
            String nextStationId = "0";
            String endStationId = "0";
            if (1 == masterStatus) {
                nextStationId = ShadowJsonUtils.getJsonStringValue(objResult,
                        "PIC1_IudNextStationID", "0");
                endStationId = ShadowJsonUtils.getJsonStringValue(objResult,
                        "PIC1_IudEndStation", "0");
                currentStationId = ShadowJsonUtils.getJsonStringValue(objResult,
                        "PIC1_IudCurStation", "0");
            } else {
                nextStationId = ShadowJsonUtils.getJsonStringValue(objResult,
                        "PIC6_IudNextStationID", "0");
                endStationId = ShadowJsonUtils.getJsonStringValue(objResult,
                        "PIC6_IudEndStation", "0");
                currentStationId = ShadowJsonUtils.getJsonStringValue(objResult,
                        "PIC6_IudCurStation", "0");
            }
            String finalEndStationId = endStationId;
            String finalNextStationId = nextStationId;
            String finalCurrentStationId = currentStationId;
            //判断车辆上下行
            //todo 此处查询可以优化 2021-09-08   优化完成 2021-0908
            String direction = staDirection.get(lineId).get(finalEndStationId);
            if (StringUtils.isEmpty(direction)) {
                trainOverview.put("direction", "");
            } else {
                if (direction.equals("up")) {
                    //todo 此处查询可以优化 2021-09-07   优化完成 2021-0908
                    if (startStationCache.containsKey(lineId + "up")) {
                        startStationId = startStationCache.get(lineId + "up");
                    } else {
                        StationDTO station = lineMapping.getStartStation(lineId, direction);
                        startStationId = station.getStaId();
                        startStationCache.put(lineId + "up", startStationId);
                    }

                } else if (direction.equals("down")) {
                    //todo 此处查询可以优化 2021-09-07   优化完成 2021-0908
                    if (startStationCache.containsKey(lineId + "down")) {
                        startStationId = startStationCache.get(lineId + "down");
                    } else {
                        StationDTO station = lineMapping.getStartStation(lineId, direction);
                        startStationId = station.getStaId();
                        startStationCache.put(lineId + "down", startStationId);
                    }
                }
                trainOverview.put("direction", direction);
            }
            String finalStartStationId = startStationId;

            //第二步 两站之前百分比计算
            int CuiDisToCurStation = ShadowJsonUtils.getJsonIntegerValue(objResult,
                    "DDU_CuiDisToCurStation", 0);//距离当前站距离
            int CuiDisToNextStation = ShadowJsonUtils.getJsonIntegerValue(objResult,
                    "DDU_CuiDisToNextStation", 0);//距离下一站距离
            Float position = 0f;

            if (CuiDisToCurStation > 0) {
                position = (float) CuiDisToCurStation / (CuiDisToCurStation + CuiDisToNextStation);
            }
            if (position > 1) {
                position = Float.valueOf(1);
            }
            //先判断距离上下两站距离是否为零
            if (!finalCurrentStationId.equals(finalNextStationId)) {
                if (CuiDisToCurStation == 0 || CuiDisToNextStation == 0) {
                    position = Float.valueOf(1);
                }
            }
            //后判断两站如果相等 则在站上 行驶百分比为0
            if (finalCurrentStationId.equals(finalNextStationId)) {
                position = Float.valueOf(0);
            }
            DecimalFormat decimalFormat = new DecimalFormat("0.00");   //构造方法的字符格式这里如果小数不足2位,会以0补足.
            String positionStr = decimalFormat.format(position);

            //第三步：通过当前站id 判断车辆所在的区段id
            String segmentId = "--";
            for (DrawLineConfigVO drawLineConfigVO : drawLineList.get(lineId)) {
                String drawLineCurrentSta = drawLineConfigVO.getCurrentSta();
                String drawLineDirection = drawLineConfigVO.getDirection();
                if (drawLineCurrentSta.equals(finalCurrentStationId) && drawLineDirection.equals(direction)) {
                    segmentId = drawLineConfigVO.getSegmentId();
                    break;
                }
            }
            //todo  此处查询可以优化 2021-09-08   优化完成 2021-0908
            trainOverview.put("startStstion", staNameInfo.get(lineId).get(finalStartStationId));
            trainOverview.put("currentStation", staNameInfo.get(lineId).get(finalCurrentStationId));
            trainOverview.put("nextStation", staNameInfo.get(lineId).get(finalNextStationId));
            trainOverview.put("endStation", staNameInfo.get(lineId).get(finalEndStationId));
            trainOverview.put("position", positionStr);
            trainOverview.put("segmentId", segmentId);
        } catch (Exception e) {
            System.out.println("==========" + "车辆:车辆实时位置处理异常");
            logger.info(e.getMessage() + "车辆实时位置处理异常");
        }

    }

    private int getMasterStatus(JSONObject objResult) {
        //判断获取信号的主从逻辑
        int masterStatus = 1;
        int CxPCUFlt1 = ShadowJsonUtils.getJsonIntegerValue(objResult,
                "DDU_CxPCUFlt_1", 0);
        int CxPCUFlt6 = ShadowJsonUtils.getJsonIntegerValue(objResult,
                "DDU_CxPCUFlt_6", 0);
        int PIC1IxMaster = ShadowJsonUtils.getJsonIntegerValue(objResult,
                "PIC1_IxMaster", 0);
        int PIC6IxMaster = ShadowJsonUtils.getJsonIntegerValue(objResult,
                "PIC6_IxMaster", 0);
        if (CxPCUFlt1 == 0 && CxPCUFlt6 == 0) {
            if (PIC1IxMaster != 1 && PIC6IxMaster == 1) {
                masterStatus = 2;
            }
        } else if (CxPCUFlt1 == 1 && CxPCUFlt6 == 0) {
            masterStatus = 2;
        }
        return masterStatus;
    }

    /**
     * @Description 获取车辆的告警 预警 故障数
     * @Return com.alibaba.fastjson.JSONArray
     * <AUTHOR>
     * @Date 2021/6/4
     * @Param [vehicleId]
     **/
    private void getAlarmOrWarningCountNum(JSONObject trainOverview, String vehicleId) {
        Long currentFaultsNum = 0L;
        Long warningFaultsNum = 0L;
        try {
            //todo 调整逻辑 改为只查询一次 同线路车辆数据循环迭代
            HashMap<String, Object> countNum = faultCountNum.get(vehicleId);
            if (null != countNum) {
                currentFaultsNum = (Long) countNum.get("current");
                warningFaultsNum = (Long) countNum.get("warning");
            }
        } catch (Exception e) {
            System.out.println("==========" + vehicleId + ":本车故障查询数量统计异常");
            logger.error("getAlarmOrWarningCountNum:", e.getMessage());
        }
        trainOverview.put("currentFaultsNum", currentFaultsNum);
        trainOverview.put("warningFaultsNum", warningFaultsNum);
    }

    /**
     * 判断车辆在线离线
     */
    private boolean onlineStatusOfTrain(String trainCode) {
        String online = String.valueOf(RedisUtils.get(trainCode + "_online"));
        return StringUtils.isNotEmpty(online) && "true".equals(online);
    }

    /**
     * 获取redis 数据 封装方法
     */
    private JSONObject getRedisDateByVehicleCode(String vehicleCode) {
//        String redisResult;
//        JSONObject objResult = null;
//        String redisTblName = vehicleCode + "_shadow_new";
//        Object result = RedisUtils.get(redisTblName);
//        try {
//            if (result != null) {
//                redisResult = JSONObject.toJSONString(result);
//                objResult = (JSONObject) JSONObject.parseObject(redisResult).get("signals");
//            } else {
//                return null;
//            }
//        } catch (Exception e) {
//            logger.error("获取redis数据报错" + vehicleCode);
//        }
//
//        return objResult;
        String key = vehicleCode + "_shadow_new";
        Object redisResult = RedisUtils.get(key);
//        // 去除转义2022-0305 lixin 只对需要转义的数据进行处理
//        String signal =JSONObject.toJSONString(redisResult);
//        if(redisResult != null && signal.contains("\\")){
//            String a2 = signal.replace("\"{","{");
//            String a3 = a2.replace("}\"","}");
//            String a4 = a3.replace("\\","");
//            JSONObject resJson = (JSONObject)(JSONObject.parseObject(a4)).get("signals");
//            return resJson;
//        }
        if (redisResult != null) {
            return (JSONObject) JSONObject.parseObject(JSONObject.toJSONString(redisResult)).get("signals");
        }
        return null;
    }

    /**
     * @Description 获取车辆的正线状态  投运状态
     * @Return com.alibaba.fastjson.JSONArray
     * <AUTHOR>
     * @Date 2021/6/4
     * @Param [objResult]
     **/
    private Integer getstatus(JSONObject objResult) {
        int nextStationId = ShadowJsonUtils.getJsonIntegerValue(objResult,
                "DDU_CudNextStation", 0);
        int endStationId = ShadowJsonUtils.getJsonIntegerValue(objResult,
                "DDU_CudEndStation", 0);
        int currentStationId = ShadowJsonUtils.getJsonIntegerValue(objResult,
                "DDU_CudCurrentStation", 0);
        return ((nextStationId == 0 && endStationId == 0 && currentStationId == 0) ? 0 : 1);
    }

    /**
     * @Description 获取车辆的里程与能耗
     * @Return com.alibaba.fastjson.JSONArray
     * <AUTHOR>
     * @Date 2021/6/4
     * @Param [objResult, vehicleCode]
     **/
    private void getDistanceAndTotalEngy(JSONObject trainOverview, JSONObject objResult, String vehicleCode) {
        try {
            //第一步 获取里程
            Integer DDU_CudRunDistance = ShadowJsonUtils.getJsonIntegerValue(objResult, "DDU_CudRunDistance", 0);
            if (0 == DDU_CudRunDistance) {
                if (null != DistanceAndTotalEngyList.get(vehicleCode)) {
                    String paramName = "DDU_CudRunDistance";
                    Object value = DistanceAndTotalEngyList.get(vehicleCode).get(paramName);
                    DDU_CudRunDistance = (null == value) ? 0 : (Integer) value;
                }
            }
            //第二步 获取能耗
            Integer DDU_CudTotalEngyCon = ShadowJsonUtils.getJsonIntegerValue(objResult, "DDU_CudTotalEngyCon", 0);
            if (0 == DDU_CudTotalEngyCon) {
                if (null != DistanceAndTotalEngyList.get(vehicleCode)) {
                    String paramName = "DDU_CudTotalEngyCon";
                    Object value = DistanceAndTotalEngyList.get(vehicleCode).get(paramName);
                    DDU_CudTotalEngyCon = (null == value) ? 0 : (Integer) value;
                }
            }
            trainOverview.put("Distance", DDU_CudRunDistance);
            trainOverview.put("TotalEngyCon", DDU_CudTotalEngyCon);

        } catch (Exception e) {
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "redis数据查询异常！");
        }
    }

    /**
     * @Description 告警或预警list 0：告警，1：预警
     * @Return com.alibaba.fastjson.JSONArray
     * <AUTHOR> yuxi
     * @Date 18:46 2020/6/2
     * @Param [lineId, faultSource]
     **/
    private JSONArray getAlarmOrWarningList(String lineId, String faultSource, List<String> onlineVehicles) {
        MtcAlarmWarningVO alarmWarning = new MtcAlarmWarningVO();
        alarmWarning.setLineId(lineId);
        alarmWarning.setFaultSource(faultSource);
        alarmWarning.setEndTime(null);
        if ("0".equals(faultSource)) {
            alarmWarning.setRunningStatus(true);
        }
        if (onlineVehicles.size() == 0) {
            onlineVehicles.add("");
        }
        alarmWarning.setOnlineVehicles(onlineVehicles);
        List<MtcAlarmWarningVO> alarmWarningList = mtcAlarmWarningMapping.getMtcAlarmWarningForMonitor(alarmWarning);
        return JSONArray.parseArray(JSON.toJSONString(alarmWarningList));
    }

    /*2022-1-10 新版实时故障查询区分 ddu故障 检修故障*/
    private JSONObject getAlarmGroupListNew(String lineId, String faultSource, List<String> onlineVehicles) {
        if (onlineVehicles.size() == 0) {
            onlineVehicles.add("");
        }
        JSONObject alarmGroupList = new JSONObject();
        //ddu
        MtcAlarmWarningVO alarmWarningDDU = new MtcAlarmWarningVO();
        alarmWarningDDU.setLineId(lineId);
        alarmWarningDDU.setFaultSource(faultSource);
        alarmWarningDDU.setEndTime(null);
        if ("0".equals(faultSource)) {
            alarmWarningDDU.setRunningStatus(true);
        }
        alarmWarningDDU.setOnlineVehicles(onlineVehicles);
        List<MtcAlarmWarningVO> alarmWarningListDDU = mtcAlarmWarningMapping.getMtcAlarmWarningForMonitor(alarmWarningDDU);
        alarmGroupList.put("ddu", alarmWarningListDDU);
        //检修
        MtcAlarmWarningVO alarmWarningRepair = new MtcAlarmWarningVO();
        alarmWarningRepair.setLineId(lineId);
        alarmWarningRepair.setFaultSource(faultSource);
        alarmWarningRepair.setEndTime(null);
        alarmWarningRepair.setRunningStatus(true);
        alarmWarningRepair.setOnlineVehicles(onlineVehicles);
        List<MtcAlarmWarningVO> alarmWarningListRepair = mtcAlarmWarningMapping.getMtcAlarmWarningForMonitor(alarmWarningRepair);
        alarmGroupList.put("repair", alarmWarningListRepair);
        return alarmGroupList;
    }

    /**
     * @Description 告警list 0：告警
     * @Return com.alibaba.fastjson.JSONArray
     * <AUTHOR>
     * @Date 18:46 2020/6/2
     * @Param [lineId, faultSource]
     **/
    private JSONArray getCurrentDayAlarmList(String lineId, String faultSource, List<String> onlineVehicles) {
        MtcAlarmWarningVO alarmWarning = new MtcAlarmWarningVO();
        alarmWarning.setLineId(lineId);
        alarmWarning.setFaultSource(faultSource);
        alarmWarning.setEndTime(new Date());//为了查询endtime不为空的情况 随机传一个默认时间让sql条件排出endtime=null限制
        alarmWarning.setStartTime(DateUtils.parseDate(DateUtils.getDate()));
        alarmWarning.setRunningStatus(true);
        if (onlineVehicles.size() == 0) {
            onlineVehicles.add("");
        }
        alarmWarning.setOnlineVehicles(onlineVehicles);
        List<MtcAlarmWarningVO> alarmWarningList = mtcAlarmWarningMapping.getMtcAlarmWarningForMonitor(alarmWarning);
        return JSONArray.parseArray(JSON.toJSONString(alarmWarningList));
    }

    /**
     * @Description 告警list 0：告警 当日（分组 ddu故障 检修故障）
     * @Return com.alibaba.fastjson.JSONArray
     * <AUTHOR>
     * @Date 09:46 2022/1/12
     * @Param [lineId, faultSource]
     **/
    private JSONObject getCurrentDayAlarmGroupList(String lineId, String faultSource, List<String> onlineVehicles) {
        if (onlineVehicles.size() == 0) {
            onlineVehicles.add("");
        }
        JSONObject alarmCurrentGroupList = new JSONObject();
        //ddu
        MtcAlarmWarningVO alarmWarningDDU = new MtcAlarmWarningVO();
        alarmWarningDDU.setLineId(lineId);
        alarmWarningDDU.setFaultSource(faultSource);
        alarmWarningDDU.setEndTime(new Date());//为了查询endtime不为空的情况 随机传一个默认时间让sql条件排出endtime=null限制
        alarmWarningDDU.setStartTime(DateUtils.parseDate(DateUtils.getDate()));
        alarmWarningDDU.setRunningStatus(true);
        alarmWarningDDU.setOnlineVehicles(onlineVehicles);
        List<MtcAlarmWarningVO> alarmWarningListDDU = mtcAlarmWarningMapping.getMtcAlarmWarningForMonitor(alarmWarningDDU);
        alarmCurrentGroupList.put("ddu", alarmWarningListDDU);
        //检修
        MtcAlarmWarningVO alarmWarningRepair = new MtcAlarmWarningVO();
        alarmWarningRepair.setLineId(lineId);
        alarmWarningRepair.setFaultSource(faultSource);
        alarmWarningRepair.setEndTime(new Date());//为了查询endtime不为空的情况 随机传一个默认时间让sql条件排出endtime=null限制
        alarmWarningRepair.setStartTime(DateUtils.parseDate(DateUtils.getDate()));
        alarmWarningRepair.setRunningStatus(true);
        alarmWarningRepair.setRunningStatus(true);
        alarmWarningRepair.setOnlineVehicles(onlineVehicles);
        List<MtcAlarmWarningVO> alarmWarningListRepair = mtcAlarmWarningMapping.getMtcAlarmWarningForMonitor(alarmWarningRepair);
        alarmCurrentGroupList.put("repair", alarmWarningListRepair);
        return alarmCurrentGroupList;
    }

    //根据线路 和站点id获取当前站下一站终点站 始发站等等信息 暂时未使用
    private Object getStationsUniversal(String lineId, JSONObject objResult, JSONObject trainOverview) {
        //第一步 通过lineId 查询出线路对应的车站信号都是啥 通过字典查询 存进去对应的协议 车站英文名 并进行取值
        //车站信息获取
        StationDTO stationDTO = new StationDTO();
        stationDTO.setMetroLineId(lineId);
        List<StationDTO> staList = lineMapping.getStation(stationDTO);
        //贵阳2号线没有始发站 所以需要进行计算
        //当前站下一站终点站信息取自字典
        SysDictDTO sysDictDTO = new SysDictDTO();
        sysDictDTO.setLineId(lineId);
        sysDictDTO.setTypeCode("GY02_station_name_en");
        List<SysDictVO> stationDict = sysDictMapping.listDictUniversal(sysDictDTO);

        String CurrentStation = "";
        ;
        String NextStation = "";
        String EndStation = "";
        for (SysDictVO sysDictVO : stationDict) {
            if (sysDictVO.getLabel().equals("CurrentStation")) {
                CurrentStation = sysDictVO.getCode();
            }
            if (sysDictVO.getLabel().equals("NextStation")) {
                NextStation = sysDictVO.getCode();
            }
            if (sysDictVO.getLabel().equals("EndStation")) {
                EndStation = sysDictVO.getCode();
            }
        }
        //算出始发站
        int startStationId = 0;
        int nextStationId = ShadowJsonUtils.getJsonIntegerValue(objResult,
                NextStation, 0);
        int endStationId = ShadowJsonUtils.getJsonIntegerValue(objResult,
                EndStation, 0);
        int currentStationId = ShadowJsonUtils.getJsonIntegerValue(objResult,
                CurrentStation, 0);
        //车站id大于1的时候是上行 小于等于1的时候是下行
        int startId = staList.get(0).getStationCode();
        int endId = staList.get(staList.size() - 1).getStationCode();
        String direction = "down";
        if (startId < endStationId) {
            direction = "up";
            trainOverview.put("direction", direction);
            startStationId = startId;//GY2根据终点站判断始发站是什么
        } else if (startId >= endStationId) {
            trainOverview.put("direction", direction);
            startStationId = endId;//GY2根据终点站判断始发站是什么
        } else {
            trainOverview.put("direction", "");
        }
        int finalStartStationId = startStationId;

        //第二部做逻辑判断
        //第一层 判断如果终点站不为0 证明数据有效接着往下推导
        if (endStationId != 0) {
            //第二层 直接对当前站和 下一站做判断 如果其中有一个值为0的情况需要根据行驶方向计算他的下一站
            if (nextStationId != 0 && currentStationId == 0) {
                if ("down".equals(direction)) {
                    for (StationDTO staDTO : staList) {
                        if (staDTO.getStationCode().equals(nextStationId)) {
                            int index = staDTO.getSortNumber();
                            if (index != 0) {
                                currentStationId = staList.get(index - 1).getStationCode();
                            } else {
                                currentStationId = nextStationId;
                            }
                            break;
                        }
                    }//for循环闭环
                }//if "down" 闭环
                if ("up".equals(direction)) {
                    for (StationDTO staDTO : staList) {
                        if (staDTO.getStationCode().equals(nextStationId)) {
                            int index = staDTO.getSortNumber();
                            if (index != staList.size() - 1) {
                                currentStationId = staList.get(index - 1).getStationCode();
                            } else {
                                currentStationId = nextStationId;
                            }
                            break;
                        }
                    }//for循环闭环
                }//if "up" 闭环
            }
            if (nextStationId == 0 && currentStationId != 0) {
                if ("down".equals(direction)) {
                    for (StationDTO staDTO : staList) {
                        if (staDTO.getStationCode().equals(nextStationId)) {
                            int index = staDTO.getSortNumber();
                            if (index != staList.size() - 1) {
                                nextStationId = staList.get(index + 1).getStationCode();
                            } else {
                                nextStationId = currentStationId;
                            }
                            break;
                        }
                    }//for循环闭环
                }//if "down" 闭环
                if ("up".equals(direction)) {
                    for (StationDTO staDTO : staList) {
                        if (staDTO.getStationCode().equals(nextStationId)) {
                            int index = staDTO.getSortNumber();
                            if (index != 0) {
                                nextStationId = staList.get(index - 1).getStationCode();
                            } else {
                                nextStationId = currentStationId;
                            }
                            break;
                        }
                    }//for循环闭环
                }//if "up" 闭环
            }
        }

        //处理最终变量给lambda表达式使用（java: 从lambda 表达式引用的本地变量必须是最终变量或实际上的最终变量）
        int finalCurrentStationId = currentStationId;
        int finalNextStationId = nextStationId;

        //将处理好的站点信息 赋值
        trainOverview.put("startStstion",
                staList.parallelStream().filter(vo -> vo.getStationCode() == finalStartStationId).findFirst().orElse(new StationDTO("")).getName());
        trainOverview.put("currentStation",
                staList.parallelStream().filter(vo -> vo.getStationCode() == finalCurrentStationId).findFirst().orElse(new StationDTO("")).getName());
        trainOverview.put("nextStation",
                staList.parallelStream().filter(vo -> vo.getStationCode() == finalNextStationId).findFirst().orElse(new StationDTO("")).getName());
        trainOverview.put("endStation",
                staList.parallelStream().filter(vo -> vo.getStationCode() == endStationId).findFirst().orElse(new StationDTO("")).getName());

        return null;
    }

}
