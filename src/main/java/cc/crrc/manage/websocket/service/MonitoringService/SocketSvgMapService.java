package cc.crrc.manage.websocket.service.MonitoringService;


import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.UUIDUtils;
import cc.crrc.manage.monitoringConfig.entity.MonitorPageInfoPO;
import cc.crrc.manage.monitoringConfig.service.TriggerConfigService;
import cc.crrc.manage.websocket.dao.SocketSvgMapDao;
import cc.crrc.manage.websocket.entity.SvgMapEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @FileName SvgCodeService
 * <AUTHOR> xin
 * @Date 2020/8/13 10:40
 * @Version 1.0
 **/
@Service
public class SocketSvgMapService {
    @Autowired
    private SocketSvgMapDao svgMapDao;

    private final static Logger LOGGER = LoggerFactory.getLogger(TriggerConfigService.class);

    public Object getSvgMapInfoByLineId(String lineId) {
        try{
            SvgMapEntity svgMapEntity = svgMapDao.getSvgMapInfoByLineId(lineId);
            return svgMapEntity;
        }catch(Exception e){
            LOGGER.error(TriggerConfigService.class+"查询svgMap信息失败");
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    

    public Object getCssByLineId(String lineId) {
        try {
            MonitorPageInfoPO monitorPageInfoPO = svgMapDao.getCssByLineId(lineId);
            return monitorPageInfoPO;
        } catch (Exception e) {
            LOGGER.error("Method[getCssByLineId] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object getHtmlByMenuCode(String menuCode) {
        try {
            MonitorPageInfoPO monitorPageInfoPO = svgMapDao.getHtmlByMenuCode(menuCode);
            return monitorPageInfoPO;
        } catch (Exception e) {
            LOGGER.error("Method[getHtmlByMenuCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }
}
