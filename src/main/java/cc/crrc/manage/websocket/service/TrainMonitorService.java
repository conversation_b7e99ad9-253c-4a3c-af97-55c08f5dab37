package cc.crrc.manage.websocket.service;

import cc.crrc.manage.cache.CacheUtils;
import cc.crrc.manage.cache.dict.DictCache;
import cc.crrc.manage.cache.dict.VehicleCache;
import cc.crrc.manage.common.utils.RedisUtils;
import cc.crrc.manage.pojo.SysDictVO;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import cc.crrc.manage.websocket.utils.CommonUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


@Service
@Transactional
public class TrainMonitorService {
    private static final Logger LOGGER = LoggerFactory.getLogger(TrainMonitorService.class);
    private static ConcurrentHashMap<String, List<String>> trainStructureCache = new ConcurrentHashMap<>();

    @Autowired
    private VehicleCache vehicleCache;
    @Autowired
    private LineSocketService lineSocketService;
    @Autowired
    private MonitorConfigProcesser configProcesser;
    @Autowired
    private CommonUtil util;

    /**
     * 车辆监控 websocket 实时推送
     * 若在本机部署 访问接口为 ws://127.0.0.1:10018/api/websocket
     * 发送message为 {"cmd":"newTrainMonitor","message":{"trainCode":"02001"}}
     * (cmd 和 轮询时间配置 在枚举类WebSocketRouteEnum中)
     *
     * @param message 线路id 例如 {"trainCode":"02001"}
     * @return java.lang.String
     **/

    public String getAll(String message) {
        JSONObject returnObject = new JSONObject();
        String vehicleCode = JSONObject.parseObject(message).getString("trainCode");
        JSONObject dashboard = new JSONObject();
        JSONObject result = new JSONObject();
        try {
            MtrVehiclePO mtrVehiclePO = vehicleCache.getValue(vehicleCode);
            HashMap<String, JSONObject> dataKeyHashMap = new HashMap<>();
            String redisSignalName = vehicleCode + "_shadow_new";
            Object redisValue = RedisUtils.get(redisSignalName);
            JSONObject mainData = JSONObject.parseObject(JSONObject.toJSONString(redisValue));
            JSONObject signals = (JSONObject) mainData.get("signals");
            //当前站、下一站、终点站
            String lineId = mtrVehiclePO == null ? CacheUtils.METRO_LINE_ID : mtrVehiclePO.getMetroLineId();
            JSONObject trainStation = util.getStationAndLocationBaseOnCache(signals, lineId, vehicleCode);
            //JSONObject trainStation = util.getStationAndLocation(signals, lineId, vehicleCode);
            //车站信息放到影子数据中
            mainData.putAll(trainStation);
            //将处理好的数据 放进 map数据集 方便后续统一调用
            dataKeyHashMap.put("trainCode_shadow", mainData);
            //得到车厢名称list 如；["A1","B1","B2","A2"]
            String vehicleTypeId = mtrVehiclePO.getVehicleTypeId();
            List<String> carNames = trainStructureCache.computeIfAbsent(vehicleTypeId, TrainMonitorService::noLocationInCache);
            //数据包时间
            long date = Long.parseLong(String.valueOf(mainData.get("time")));
            //车辆配置信号解析
            result = configProcesser.processer(returnObject, mtrVehiclePO, dataKeyHashMap, date, carNames, signals);
            //dashboard增加额外的数据
            if (result != null) {
                dashboard = JSON.parseObject(String.valueOf(result.get("dashboard")));
            }
            //车辆在线离线
            boolean onlineStatus = lineSocketService.onlineStatusOfTrain(vehicleCode);
            dashboard.getJSONObject("todayRunTime")
                    .fluentPut("slotValue", Math.min(dashboard.getJSONObject("todayRunTime").getLongValue("slotValue") / 2, 1440));
            dashboard.put("onlineStatus", onlineStatus);
            if (!onlineStatus) {
                CommonUtil.packgeData.remove(vehicleCode);//不在线车辆，清楚缓存影响
            }
            dashboard.put("trainStation", trainStation);
            dashboard.put("redisDate", date);
            //查询实时故障和当前故障和状态预警  -----该部分转移至 MtcAlarmWarningController.getTrainMonitorFault
            //List<MtcAlarmWarningVO> alarmList = alarmWarningMapping.getTrainFaultList(vehicleCode);
            /*Map<String, List<MtcAlarmWarningVO>> faultAndAlarmMap = alarmList.stream()
                    .filter(o -> o.getFaultLevel() != null)
                    .filter(MtcAlarmWarningVO::getRunningStatus)
                    //多重条件下使用多个reversed（）会导致左侧反转
                    .sorted(Comparator.comparing(MtcAlarmWarningVO::getStartTime)
                            .thenComparing(MtcAlarmWarningVO::getFaultLevel).reversed())
                    .collect(groupingBy(MtcAlarmWarningVO::getFaultSource,
                            collectingAndThen(toList(), l -> l.stream().limit(50).collect(toList()))));*/
            //实时故障
            //dashboard.put("currentFault", faultAndAlarmMap.getOrDefault("0", new ArrayList<>()));
            // 13.状态预警
            //dashboard.put("faultEarlyWarning", faultAndAlarmMap.getOrDefault("1", new ArrayList<>()));
            // 车型名称
            dashboard.put("vehicleTypeName", mtrVehiclePO.getVehicleTypeName());
            result.put("dashboard", dashboard);
            result.put("vehicleCode", vehicleCode);
            Object clone = result.clone();
            return clone.toString();
        } catch (Exception e) {
            LOGGER.error("TrainMonitorService  error {}", e.getMessage());
            dashboard.put("date", null);
            dashboard.put("vehicleTypeName", "绍兴2号线车型");
            dashboard.put("onlineStatus", false);
            result.put("vehicleCode", vehicleCode);
            result.put("dashboard", dashboard);
            result.put("error", e.getStackTrace());
            return result.toString();
        }
    }

    private static List<String> noLocationInCache(String vehicleTypeId) {
        List<SysDictVO> dicts = CacheUtils.getValue(DictCache.class, StringUtils.join(DictCache.TRAIN_ORGANIZE, "_", CacheUtils.METRO_LINE_ID, "_", vehicleTypeId));
        return dicts.stream()
                .filter(i -> !"ALL".equals(i.getValue()))
                .map(SysDictVO::getValue)
                .collect(Collectors.toList());
    }

}
