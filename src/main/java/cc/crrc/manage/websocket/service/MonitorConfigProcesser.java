package cc.crrc.manage.websocket.service;

import cc.crrc.manage.cache.CacheUtils;
import cc.crrc.manage.cache.dict.DictCache;
import cc.crrc.manage.pojo.SysDictVO;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import cc.crrc.manage.service.analysis.AnalysisService;
import cc.crrc.manage.websocket.dao.WSMonitorDao;
import cc.crrc.manage.websocket.entity.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.TypeUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.script.*;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cc.crrc.manage.websocket.WebSocketServer.SCRIPT_ENGINE_THREAD_LOCAL;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR> zhang
 * @FileName MonitorConfigProcesser
 * @date 2023/5/24
 * @description 信号配置处理类
 */
@Component
public class MonitorConfigProcesser {
    private static final Logger LOGGER = LoggerFactory.getLogger(MonitorConfigProcesser.class);
//    private static ScriptEngine se = new ScriptEngineManager().getEngineByName("javascript");

    @Autowired
    private WSMonitorDao wSMonitorDao;
    @Autowired
    private AnalysisService analysisService;

    /**
     * 处理入口
     * @param result
     * @param mtrVehiclePO
     * @param dataKeyHashMap
     * @param date
     * @param carNames
     * @param signals
     * @return
     */
    public JSONObject processer(JSONObject result, MtrVehiclePO mtrVehiclePO, HashMap<String, JSONObject> dataKeyHashMap,
                                 long date, List<String> carNames, JSONObject signals) {
        String traCode = mtrVehiclePO.getVehicleCode();
        try {
            long l = System.currentTimeMillis();
            //返回数据处理开始
            List<SysDictVO> trainMappings = CacheUtils.getValue(DictCache.class, StringUtils.join(DictCache.TRAIN_MONITOR_CONFIG_MAPPING, "_", CacheUtils.METRO_LINE_ID, "_", mtrVehiclePO.getVehicleTypeId()));
            String targetTrainCode = trainMappings.stream()
                    .filter(i -> traCode.equals(i.getValue()))
                    .map(SysDictVO::getLabel)
                    .findFirst()
                    .orElse("02001");
            List<WebsocketVO> websocketVOS = wSMonitorDao.getWebsocketVO(targetTrainCode);
            System.out.println(System.currentTimeMillis() - l + "---time");
            for (int i = 0; i < websocketVOS.size(); i++) {
                WebsocketVO websocketVO = websocketVOS.get(i);
                JSONObject items = new JSONObject();
                items.put("date", date);
                String menuType = websocketVO.getMenuType();
                List<WebsocketItemEntity> itemList = websocketVO.getWebsocketItemEntity();
                JSONObject boardItems = new JSONObject();
                JSONArray carItems = new JSONArray();
                JSONArray basicItems = new JSONArray();
                //todo 2022-02-10 增加自定义模板 类型返回 李鑫
                JSONObject customTemplateItems = new JSONObject();
                //查询车辆的日统计数据
                List<HashMap<String, Object>> historyData = analysisService.getVehicleHistoryData(traCode);
                Map<String, Double> dwDataMap = historyData.stream().collect(toMap(k -> (String) k.get("signalname"), v -> (Double) v.get("value"), (a, b) -> a));
                items.put("dwDataMap", dwDataMap);
                if (null != itemList) {
                    //筛选出dashboard数据类型项点
                    List<WebsocketItemEntity> boardItemList = itemList.stream().filter(o -> "board".equals(o.getType())).collect(Collectors.toList());
                    boardItems = dashBoardItem("board", items, dataKeyHashMap, signals, boardItemList, date, traCode);
                    //筛选出车厢数据类型项点
                    List<WebsocketItemEntity> carItemList = itemList.stream().filter(o -> "car".equals(o.getType())).collect(Collectors.toList());
                    carItems = wrapperItem("car", dataKeyHashMap, signals, carItemList, date, traCode);
                    try {
                        carItems = carContainItem(carItems, carNames);
                    } catch (Exception e) {
                        LOGGER.error("基本数据类型转为车厢数据错误 {}", carItems);
                    }
                    //筛选出基本数据类型项点
                    List<WebsocketItemEntity> basicItemList = itemList.stream().filter(o -> "basic".equals(o.getType())).collect(Collectors.toList());
                    basicItems = wrapperItem("basic", dataKeyHashMap, signals, basicItemList, date, traCode);

                    //todo 2022-02-10 增加自定义模板 类型返回 李鑫
                    //筛选出customTemplate数据类型项点
                    List<WebsocketItemEntity> customTemplateItemList = itemList.stream().filter(o -> "customTemplate".equals(o.getType())).collect(Collectors.toList());
                    customTemplateItems = dashBoardItem("customTemplate", items, dataKeyHashMap, signals, customTemplateItemList, date, traCode);
                }
//            if (menuType == null) {
//                continue;
//            }
                switch (menuType) {
                    case "menuStaticComponentType":
                        items.put("url", websocketVO.getComponentsUrl());
                        break;
                    case "dashBoard":
                        items = boardItems;
                        break;
                    case "menuSvgType":
                    case "menuRiomType":
                    case "menuBaseType":
                        items.put("carItemList", carItems);
                        items.put("basicItemList", basicItems);
                        break;
                    case "menuUrlType":
                        items.put("url", websocketVO.getUrl());
                        break;
                    //todo 2022-02-10 增加自定义模板 类型返回 李鑫
                    case "menuCustomType":
                        items = customTemplateItems;
                        break;
                    default:
                        break;
                }
                result.put(websocketVO.getMenuCode(), items);
            }
        } catch (Exception e) {
            LOGGER.error("--error{}", e);
            LOGGER.error("--getAllData：" + traCode);
        }
        return result;
    }


    /**
     * @Description 封装dashboard类型item
     * @Return com.alibaba.fastjson.JSONObject
     * <AUTHOR> wei
     * @Date 10:11 2020/8/6
     * @Param [itemType, result, objResult, boardItemList]
     **/
    private JSONObject dashBoardItem(String itemType, JSONObject result, HashMap<String, JSONObject> dataKeyHashMap,
                                     JSONObject signals, List<WebsocketItemEntity> boardItemList, long date, String traCode) {
        //dahsboard类型一个项点只有一个卡槽
        for (WebsocketItemEntity monitorTableItemEntity : boardItemList) {
            MonitorSignalFunctionPO function = monitorTableItemEntity.getMonitorSignalFunctionPO();
            List<WebsocketFormatEntity> formatList = monitorTableItemEntity.getWebsocketFormatEntity();
            for (WebsocketFormatEntity o : formatList) {
                List<WebsocketSlotEntity> slots = o.getWebsocketSlotEntity();
                for (WebsocketSlotEntity slot : slots) {
                    if (function != null && StringUtils.isNotBlank(function.getId())) {
                        Object signalFunctionResult = null;
                        String functionStr = function.getFunctionStr();
                        String functionName = function.getFunctionName();
                        String redisKey = function.getRedisKey();
                        ArrayList<Object> valueList = new ArrayList<>();
                        valueList.add(0, dataKeyHashMap.get(redisKey));
                        valueList.add(1, traCode);
                        valueList.add(2, slot.getWebsocketTriggerEntity());
                        Object[] triggerValues = valueList.toArray();
                        try {
                            signalFunctionResult = signalFunction(functionStr, functionName, triggerValues);
                        } catch (Exception e) {
                            LOGGER.error("--error{}", e);
                            LOGGER.error("--function[]{}的取值或计算有问题,functionStr{}", function.getId(), functionStr);
                        }
                        if (signalFunctionResult == null) {
                            LOGGER.error("--function[]{}的值为空", function.getId());
                        }
                        result.put(monitorTableItemEntity.getRelationKey(), JSONObject.parseObject(String.valueOf(signalFunctionResult)));
                    } else {
                        HashMap<String, Double> dwDataMap = (HashMap<String, Double>) result.get("dwDataMap");
                        Double aFloat = dwDataMap.get(slot.getWebsocketTriggerEntity().get(0).getSignalNameEn());
                        JSONObject slotValue = slotConverter(itemType, signals, slot, date, aFloat);
                        result.put(monitorTableItemEntity.getRelationKey(), slotValue);
                    }
                }
            }
        }
        return result;
    }

    /**
     * @Description 将基本项点数据格式转化为车厢项点数据格式。 基本项点数据格式是以每个项点划分为多个车厢（横切），
     * 而厢项点数据格式是一个车厢下显示多个项点（纵分）。
     * @Return com.alibaba.fastjson.JSONArray
     * <AUTHOR> wei
     * @Date 10:14 2020/8/6
     * @Param [carItems, traCode, carNames]
     **/
    private JSONArray carContainItem(JSONArray carItems, List<String> carNames) {
        ArrayList<JSONObject> carList = new ArrayList<>();
        JSONObject car;
        ArrayList<JSONArray> carValueList = new ArrayList<>();
        JSONArray carValue;
        for (String carName : carNames) {
            car = new JSONObject();
            car.put("carName", carName);
            carList.add(car);
            carValue = new JSONArray();
            carValueList.add(carValue);
        }
        for (Object carItem : carItems) {
            JSONObject o = (JSONObject) carItem;
            Object carType = o.get("carType");
            Object relationKey = o.get("relationKey");
            Object itemName = o.get("itemName");
            Object itemId = o.get("itemId");
            Object slotId = o.get("slotId");
            JSONArray jsonArray1 = JSONArray.parseArray(o.get("itemValue").toString());
            for (int i = 0; i < jsonArray1.size(); i++) {
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("relationKey", relationKey);
                jsonObject1.put("carType", carType);
                jsonObject1.put("itemName", itemName);
                jsonObject1.put("itemId", itemId);
                jsonObject1.put("slotId", slotId);
                jsonObject1.put("itemValue", jsonArray1.get(i));
                carValueList.get(i).add(jsonObject1);
            }
        }
        for (int i = 0; i < carNames.size(); i++) {
            carList.get(i).put("value", carValueList.get(i));
        }
        return JSONArray.parseArray(carList.toString());
    }

    /**
     * @Description 封装item的内容
     * @Return com.alibaba.fastjson.JSONArray
     * <AUTHOR> wei
     * @Date 10:25 2020/8/6
     * @Param [itemType, objResult, itemList]
     **/
    private JSONArray wrapperItem(String itemType, HashMap<String, JSONObject> objResult, JSONObject signals, List<WebsocketItemEntity> itemList, long date, String traCode) {
        JSONArray items = new JSONArray();
        for (WebsocketItemEntity monitorTableItemEntity : itemList) {
            JSONObject item = new JSONObject();
            MonitorSignalFunctionPO function = monitorTableItemEntity.getMonitorSignalFunctionPO();
            if (function != null && StringUtils.isNotBlank(function.getId())) {
                Object signalFunctionResult = null;
                String functionStr = function.getFunctionStr();
                String functionName = function.getFunctionName();
                String redisKey = function.getRedisKey();
                ArrayList<Object> valueList = new ArrayList<>();
                valueList.add(0, objResult.get(redisKey));
                valueList.add(1, traCode);
                valueList.add(2, monitorTableItemEntity);
                Object[] triggerValues = valueList.toArray();
                try {
                    signalFunctionResult = signalFunction(functionStr, functionName, triggerValues);
                } catch (Exception e) {
                    LOGGER.error("--error{}", e);
                    LOGGER.error("--function[]{}的取值或计算有问题,functionStr{}", function.getId(), functionStr);
                }
                if (signalFunctionResult == null) {
                    LOGGER.error("--function[]{}的值为空", function.getId());
                }
                item = JSONObject.parseObject(String.valueOf(signalFunctionResult));

            } else {

                JSONArray formats = new JSONArray();
                List<WebsocketFormatEntity> formatList = monitorTableItemEntity.getWebsocketFormatEntity();
                for (WebsocketFormatEntity o : formatList) {
                    JSONObject format = new JSONObject();
                    if (StringUtils.isNotBlank(monitorTableItemEntity.getSlotBoardId())) {
                        item.put("row", o.getNRow());
                        item.put("col", o.getNColumn());
                    } else {
                        format.put("row", o.getNRow());
                        format.put("col", o.getNColumn());
                    }
                    JSONArray formatValue = new JSONArray();
                    List<WebsocketSlotEntity> slots = o.getWebsocketSlotEntity();
                    for (WebsocketSlotEntity slot : slots) {
                        JSONObject slotValue = slotConverter(itemType, signals, slot, date, 0.0);
                        formatValue.add(slotValue);
                    }
                    format.put("vehicleLocation", o.getVehicleLocation());
                    format.put("value", formatValue);
                    formats.add(format);
                }
                item.put("itemId", monitorTableItemEntity.getId());
                item.put("itemName", monitorTableItemEntity.getName());
                item.put("relationKey", monitorTableItemEntity.getRelationKey());
                item.put("itemValue", formats);
                item.put("carType", monitorTableItemEntity.getCarType());
                item.put("slotId", monitorTableItemEntity.getSlotBoardId());
            }
            items.add(item);
        }
        return items;
    }

    /**
     * @Description 封装slot
     * @Return com.alibaba.fastjson.JSONObject
     * <AUTHOR> wei
     * @Date 10:26 2020/8/6
     * @Param [itemType, all, slotType, slotId, functionId]
     **/
    private JSONObject slotConverter(String itemType, JSONObject signals, WebsocketSlotEntity slot,
                                     long date, Double dwData) {
        JSONObject slotResult = new JSONObject();
        try {
            String slotId = slot.getId();
            //针对绑卡类型菜单：项点类型为car类型的时候需要返回slotId，用于和下方基本类型数据联动
            if ("car".equals(itemType)) {
                slotResult.put("slotId", slotId);
            }
//            if(slotId.equals("5b665a5d75384ec6a8fee4e47355c908")){
//                System.out.println(slotId);
//            }
            Object value = null;
            //按优先级查询slotId下的所有trigger可能
            List<TriggerPO> triggers = slot.getWebsocketTriggerEntity();
            String dataDisplayPoint = "";
            String url = null;
            String label = "--";
            String nameEn = null;
            String triggerValue;
            Object triggerValueResult = null;
            String extProperties = "";
            String resultType;
            for (TriggerPO trigger : triggers) {
                if (trigger.getId() == null) {
                    continue;
                }
                dataDisplayPoint = trigger.getDataDisplayPoint();
                url = String.join(".", trigger.getSvgUrl(), trigger.getImageType() == null ? "" : trigger.getImageType());
                label = trigger.getLabel();
                triggerValue = trigger.getTriggerValue();
                extProperties = trigger.getExtProperties();
                nameEn = trigger.getSignalNameEn();
                resultType = trigger.getResultType();
                if (nameEn == null) {
                    continue;
                }
                switch (resultType) {
                    case "Boolean":
                        value = signals.getObject(nameEn, boolean.class);
                        if (StringUtils.isNotBlank(triggerValue)) {
                            try {
                                triggerValueResult = TypeUtils.castToJavaBean(triggerValue, Boolean.class);
                            } catch (Exception e) {
                                LOGGER.debug("triggerValueResult[{}] signalId[{}]", trigger.getId(), trigger.getSignalId());
                            }
                        }
                        break;
                    case "String":
                        value = signals.getObject(nameEn, String.class);
                        if (StringUtils.isNotBlank(triggerValue)) {
                            triggerValueResult = String.valueOf(triggerValue);
                        }
                        break;
                    case "Float":
                        value = signals.getObject(nameEn, double.class);
                        if (StringUtils.isNotBlank(triggerValue)) {
                            triggerValueResult = Float.valueOf(triggerValue);
                        }
                        break;
                    case "Integer":
                        value = signals.getObject(nameEn, long.class);
                        if (label.equals("当日") && dwData != null) {
                            value = (Long) value - dwData.longValue();
                        }
                        if (StringUtils.isNotBlank(triggerValue)) {
                            triggerValueResult = Long.valueOf(triggerValue);
                        }
                        break;
                }
                if (triggerValueResult != null && value != null && (triggerValueResult.equals(value))) {
                    break;
                }
            }
            value = value == null ? "--" : value;
            switch (dataDisplayPoint == null ? "" : dataDisplayPoint) {
                case "1":
                    slotResult.put("slotValue", String.valueOf(value));
                    slotResult.put("label", label);
                    break;
                case "2":
                    slotResult.put("slotValue", value + signals.getString(nameEn + "_unit"));
                    break;
                case "3":
                    slotResult.put("slotValue", value);
                    slotResult.put("url", url);
                    slotResult.put("label", label);
                    break;
                case "4":
                    slotResult.put("slotValue", value);
                    slotResult.put("label", label);
                    slotResult.put("url", url);
                    break;
                case "5":
                    JSONArray jsonArray = JSONArray.parseArray(extProperties);
                    String resolvedExtProperties = "";
                    if (jsonArray != null && jsonArray.size() > 0) {
                        int size = jsonArray.size();
                        for (int i = 0; i < size; i++) {
                            JSONObject o = (JSONObject) jsonArray.get(i);
                            Collection<Object> values = o.values();
                            Iterator<Object> iterator = values.iterator();
                            while (iterator.hasNext()) {
                                resolvedExtProperties += "," + iterator.next();
                            }
                        }
                    }
                    if (StringUtils.isNotBlank(resolvedExtProperties)) {
                        resolvedExtProperties = resolvedExtProperties.replaceFirst(",", "");
                    }
                    slotResult.put("extProperties", resolvedExtProperties);
                    slotResult.put("label", label);
                    slotResult.put("slotValue", value);
                    break;
                case "6":
                    slotResult.put("slotValue", String.valueOf(value));
                    slotResult.put("date", date);
                    break;
                //针对svg类型 主从信号展示使用
                case "7":
                    slotResult.put("label", label);
                    slotResult.put("slotValue", String.valueOf(value));
                    if (extProperties == null || extProperties.isEmpty()) {
                        slotResult.put("master", 0);
                    } else {
                        JSONArray o = JSONArray.parseArray(extProperties);
                        JSONObject jsonSvgMaster = o.getJSONObject(0);
                        String masterSignal = "";
                        if (jsonSvgMaster != null) {
                            masterSignal = jsonSvgMaster.getString("master");
                            if (!masterSignal.isEmpty()) {
                                slotResult.put("master", signals.getObject(masterSignal, Integer.class));
                            } else {
                                slotResult.put("master", 0);
                            }
                        }
                    }

                    break;
                //只显示图片和label
                case "8":
                    slotResult.put("label", label);
                    slotResult.put("url", url);
                    break;
                case "0":
                default:
                    slotResult.put("label", label);
                    slotResult.put("slotValue", label);
                    break;
            }
        } catch (RuntimeException e) {
            e.printStackTrace();
            LOGGER.error("slotId{}数据处理异常", slot.getId());
        }
        return slotResult;
    }


    /**
     * @Description 信号js函数处理
     * @Return java.lang.Float
     * <AUTHOR> wei
     * @Date 13:30 2020/8/6
     * @Param [triggers]
     **/
    static Object signalFunction(String functionStr, String functionName, Object[] triggerValues) {
        try {
            StopWatch stopWatch = new StopWatch();
            // 开始计时
            stopWatch.start();
//            LOGGER.info("link excute signalFunction Current Thread id is {}, Thread Name is {} scriptEngine is {}", Thread.currentThread().getId(), Thread.currentThread().getName(), SCRIPT_ENGINE_THREAD_LOCAL.get());
            ScriptEngine se = SCRIPT_ENGINE_THREAD_LOCAL.get();
            se.eval(functionStr);
            if (se instanceof Invocable) {
                Invocable in = (Invocable) se;
                Object result = in.invokeFunction(functionName, triggerValues);
                // 停止计时
                stopWatch.stop();
                // 输出执行时间
                System.out.println("执行时长：" + stopWatch.getTotalTimeMillis() + " 毫秒");
                return result;
            }

        } catch (ScriptException | NoSuchMethodException e) {
            LOGGER.error("脚本[{}]运行错误 error is {} triggerValues is {}", functionName, e.getStackTrace(), triggerValues);
        }
        return null;
    }

}
