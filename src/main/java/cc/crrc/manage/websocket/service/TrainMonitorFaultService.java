package cc.crrc.manage.websocket.service;

import cc.crrc.manage.mapper.mtc.MtcAlarmWarningMapping;
import cc.crrc.manage.pojo.mtc.AlarmWarningInfo;
import cc.crrc.manage.pojo.mtc.AlarmWarningInfoPage;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 车辆监控-故障数据
 *
 * <AUTHOR>
 * @date 2023/06/12/ 10:03
 */
@Service
public class TrainMonitorFaultService {

    private static final Logger logger = LoggerFactory.getLogger(TrainMonitorFaultService.class);

    @Autowired
    private MtcAlarmWarningMapping alarmWarningMapping;

    /**
     * 当日故障/状态预警数据推送
     *
     * @param message
     * @return
     */
    public Object process(String message) {
        //解析参数
        JSONObject jsonObject = JSONObject.parseObject(message);
        //车辆编码
        String vehicleCode = jsonObject.getString("vehicleCode");
        //0-故障 1-预警
        String faultSource = jsonObject.getString("faultSource");
        int pageNum = jsonObject.getIntValue("pageNum");
        int pageSize = jsonObject.getIntValue("pageSize");

        AlarmWarningInfoPage page = new AlarmWarningInfoPage();
        try {
            page.setList(new ArrayList<>());
            List<AlarmWarningInfo> alarmList = alarmWarningMapping.getTrainMonitorFaults(vehicleCode, faultSource);
            int total = alarmList.size();
            int pages = 1;
            if (total > 0) {
                pages = (total + pageSize - 1) / pageSize;
                pageNum = Math.min(pageNum, pages);
                List<List<AlarmWarningInfo>> partition = Lists.partition(alarmList, pageSize);
                page.setList(partition.get(pageNum - 1));
            }
            pageNum = Math.min(pageNum, pages);
            page.setTotal(total);
            page.setPageNum(pageNum);
            page.setPageSize(pageSize);
            page.setPages(pages);
            page.setFaultSource(faultSource);
        } catch (Exception e) {
            logger.error("vehicleCode is {} faultSource is {}  error is {} ", vehicleCode, faultSource, e.getStackTrace());
        }

        //返回结果
        return JSON.toJSONString(page);
    }
}
