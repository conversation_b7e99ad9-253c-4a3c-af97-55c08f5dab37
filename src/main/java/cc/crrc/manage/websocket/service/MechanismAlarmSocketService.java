package cc.crrc.manage.websocket.service;

import cc.crrc.manage.common.utils.LoggerUtils;
import cc.crrc.manage.common.utils.RedisUtils;
import cc.crrc.manage.common.utils.ShadowJsonUtils;
import cc.crrc.manage.common.utils.StringUtils;
import cc.crrc.manage.mapper.mtc.MtcAutoFaultRecordMapping;
import cc.crrc.manage.mapper.mtr.MtrVehicleMapping;
import cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRecordVO;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional(readOnly = false)
public class MechanismAlarmSocketService {

    @Autowired
    private MtrVehicleMapping mtrVehicleMapping;
    @Autowired
    private MtcAutoFaultRecordMapping mtcAutoFaultRecordMapping;

    private static final Logger logger = LoggerFactory.getLogger(MechanismAlarmSocketService.class);

    /**
     * 线路监控 websocket 实时推送
     * 若在本机部署 访问接口为 ws://localhost:10018/api/websocket
     * 发送message为 {"cmd":"mechanismAlarm","message":{"lineId":"24"}}
     * (cmd 和 轮询时间配置 在枚举类WebSocketRouteEnum中)
     **/

    public String getAll(String message) {
        JSONObject messageJson = JSONObject.parseObject(message);
        String lineId = messageJson.getString("lineId");
        JSONObject returnObject = new JSONObject();

        try {
            List<MtrVehiclePO> vehicles = mtrVehicleMapping.listVehicle(lineId);

            List<String> vehiclesToQuery = vehicles.stream()
                    .map(MtrVehiclePO::getVehicleCode)
                    .filter(this::onlineStatusOfTrain)
                    .filter(vehicleCode -> {
                        JSONObject redisData = getRedisDateByVehicleCode(vehicleCode);
                        return redisData != null && getstatus(redisData) == 1;
                    })
                    .collect(Collectors.toList());

            List<MtcMechanismAlarmRecordVO> faultRecords = new ArrayList<>();
            if (!vehiclesToQuery.isEmpty()) {
                faultRecords = mtcAutoFaultRecordMapping.listCurrentMechanismFaultRecordByLineId(vehiclesToQuery);
            }

            returnObject.put("mtcMechanismAlarmRecord", faultRecords);
        } catch (Exception e) {
            LoggerUtils.error(logger, e);
            returnObject.put("mtcMechanismAlarmRecord", "当前故障数据获取出现问题，请联系开发人员");
        }
        return returnObject.toString();
    }


    /**
     * 判断车辆在线离线
     */
    private boolean onlineStatusOfTrain(String trainCode) {
        String online = String.valueOf(RedisUtils.get(trainCode + "_online"));

        return StringUtils.isNotEmpty(online) && "true".equals(online);
    }

    /**
     * 获取redis 数据 封装方法
     */
    private JSONObject getRedisDateByVehicleCode(String vehicleCode) {
        String redisResult;
        JSONObject objResult;
        String redisTblName = vehicleCode + "_shadow_new";
        Object result = RedisUtils.get(redisTblName);
        if (result != null) {
            redisResult = JSONObject.toJSONString(RedisUtils.get(redisTblName));
            objResult = (JSONObject) JSONObject.parseObject(redisResult).get("signals");
        } else {
            return null;
        }
        return objResult;
    }


    private Integer getstatus(JSONObject objResult) {

        int nextStationId = ShadowJsonUtils.getJsonIntegerValue(objResult,
                "COM_uiNextStID", 0);
        return nextStationId == 0  ? 0 : 1;
    }



}
