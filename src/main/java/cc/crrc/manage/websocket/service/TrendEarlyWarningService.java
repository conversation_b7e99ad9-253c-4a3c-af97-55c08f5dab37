package cc.crrc.manage.websocket.service;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.StringUtils;
import cc.crrc.manage.common.utils.tsdb.TSDBUtils;
import cc.crrc.manage.common.utils.tsdb.iotdb.IoTDBUtils;
import cc.crrc.manage.mapper.comm.ProtocolMapping;
import cc.crrc.manage.mapper.mtr.MtrVehicleMapping;
import cc.crrc.manage.pojo.comm.protocol.ProtocolVO;
import cc.crrc.manage.pojo.mtr.MtrVehicleDTO;
import cc.crrc.manage.pojo.mtr.MtrVehicleVO;
import cc.crrc.manage.websocket.WebSocketServer;
import cc.crrc.manage.websocket.dao.SignalTrendWarningDao;
import cc.crrc.manage.websocket.entity.OpenTSDBQueryEntity;
import cc.crrc.manage.websocket.entity.SignalTrendWarningEntity;
import cc.crrc.manage.websocket.entity.SignalTrendWebSocketCache;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;


@Service
public class TrendEarlyWarningService {

    @Autowired
    private TSDBUtils tsdbUtils;
    @Autowired
    private ProtocolMapping protocolMapping;
    @Autowired
    private MtrVehicleMapping mtrVehicleMapping;
    @Autowired
    private SignalTrendWarningDao signalTrendWarningDao;

    /**
     * 查询某子系统下的信号基本信息、预测模型
     * <p>
     * {"cmd":"trendBasicInfo","message":{"vehicleCode":"NLCJ_T1","subSystem":"1"}}
     * vehicleCode 车辆编号
     * subSystem 子系统id
     *
     * <AUTHOR> GuoYang
     * 2021/4/20
     **/
    public String getSignalTrendBasicInfo(String message) {
        JSONObject messageJson = JSONObject.parseObject(message);
        String vehicleCode = messageJson.getString("vehicleCode");
        String subSystem = messageJson.getString("subSystem");
        MtrVehicleDTO mtrVehicleDTO = new MtrVehicleDTO();
        mtrVehicleDTO.setVehicleCode(vehicleCode);
        List<MtrVehicleVO> vehicleVOList = mtrVehicleMapping.listVehicleInfo(mtrVehicleDTO);
        if (vehicleVOList.isEmpty()) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION, "车辆不存在！");
        }
        String vehicleTypeId = vehicleVOList.get(0).getVehicleTypeId();
        List<ProtocolVO> protocolVOList = protocolMapping.getLatestProtocolByVehicleType(vehicleTypeId);
        if (protocolVOList.isEmpty()) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION, "车辆未绑定协议！");
        }
        String protocolId = protocolVOList.get(0).getId();
        List<SignalTrendWarningEntity> signalsBasicInfo = signalTrendWarningDao.getSignalBasicInfo(protocolId, subSystem);
        List<Map<String, Object>> res = new ArrayList<>();
        for (SignalTrendWarningEntity signalBasicInfo : signalsBasicInfo) {
            Map<String, Object> signalRes = new HashMap<>();
            signalRes.put("max", signalBasicInfo.getMaxValue());
            signalRes.put("min", signalBasicInfo.getMinValue());
            signalRes.put("unit", signalBasicInfo.getUnit());
            signalRes.put("car_location", signalBasicInfo.getCarLocation());
            signalRes.put("model_name", signalBasicInfo.getModelName());
            signalRes.put("model_rmse", signalBasicInfo.getModelRmse());
            signalRes.put("name_cn", signalBasicInfo.getNameCn());
            signalRes.put("name_en", signalBasicInfo.getSignalNameEn());
            signalRes.put("key", signalBasicInfo.getId());
            res.add(signalRes);
        }
        return insertHeadForTrendResponse(res, "init");
    }

    /**
     * 查询某子系统下的信号趋势预测
     * <p>
     * {"cmd":"trendSignalData","message":{"vehicleCode":"NLCJ_T1","ids":"1,2"}}
     * vehicleCode 车辆编号
     * ids 信号id，用逗号分割
     *
     * <AUTHOR> GuoYang
     * 2021/4/20
     **/
    public String getSignalTrendFromIotDB(WebSocketServer webSocketServer, String message) {
        JSONObject messageJson = JSONObject.parseObject(message);
        String vehicleCode = messageJson.getString("vehicleCode");
        String ids = messageJson.getString("ids");
        SignalTrendWebSocketCache signalTrendWebSocketCache = webSocketServer.signalTrendWebSocketCache;
        HashMap<String, Long> signalLineLastTimeMap = signalTrendWebSocketCache.getSignalLineLastTimeMap();
        HashMap<String, TreeMap<String, Double[]>> signalValueCache = signalTrendWebSocketCache.getSignalValueCache();
        boolean firstFlag = signalTrendWebSocketCache.isFirst();

        if (firstFlag) {
            signalTrendWebSocketCache.setFirst(false);
        }

        // 配置需要查询的metric 和 相对应的返回数据字段名称
        List<List<String>> metricNames = new ArrayList<>();
        metricNames.add(new ArrayList<>(Arrays.asList("ActualValue", "real")));
        metricNames.add(new ArrayList<>(Arrays.asList("PredictMean", "forecast")));
        metricNames.add(new ArrayList<>(Arrays.asList("PredictUpper", "confidenceMax")));
        metricNames.add(new ArrayList<>(Arrays.asList("PredictLower", "confidenceMin")));

        long now = System.currentTimeMillis() / 1000 * 1000;
        long pastTime = now - 45 * 60 * 1000;
        long futureTime = now + 5 * 60 * 1000;

        HashMap<String, ArrayList<OpenTSDBQueryEntity>> metricDataList = new HashMap<>();
        String downSample = firstFlag ? "1s" : "500ms";
        for (List<String> metricName : metricNames) {
            ArrayList<ArrayList<String>> iotRes = new ArrayList<>();
            String[] iotDBKeyArr = {};
            if (!ids.isEmpty()) {
                long endTime = "real".equals(metricName.get(1)) ? now : futureTime;
                // 非第一次请求，缩短请求时间，提高请求性能。
                if (!firstFlag) {
                    if ("real".equals(metricName.get(1))) {
                        pastTime = now - 3 * 60 * 1000;
                    } else {
                        pastTime = now + 2 * 60 * 1000;
                    }
                }
                iotDBKeyArr = ids.split(",");
                for (int i = 0; i < iotDBKeyArr.length; i++) {
                    iotDBKeyArr[i] = iotDBKeyArr[i] + "_" + metricName.get(0);
                }
                StringBuilder stringBuilder = new StringBuilder();
                for (int i = 0; i < iotDBKeyArr.length; i++) {
                    stringBuilder.append("last_value(").append(iotDBKeyArr[i]).append(")");
                    if (i != iotDBKeyArr.length - 1) {
                        stringBuilder.append(",");
                    }
                }
                String selectKey = stringBuilder.toString();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String pastStr = sdf.format(new Date(pastTime));
                String endStr = sdf.format(new Date(endTime));
                String sampleSqlModel = "SELECT {0} FROM root.analysis.sg.{1} GROUP BY([{2}, {3}), {4})";
                String sql = MessageFormat.format(sampleSqlModel, selectKey, vehicleCode, pastStr, endStr, downSample);
                iotRes = ((IoTDBUtils) tsdbUtils).query2IoTDB(sql);
            }
            if (iotRes.isEmpty()) {
                continue;
            }
            ArrayList<OpenTSDBQueryEntity> openTSDBQueryResponseList = dbQueryResult2OpenTSDBQueryResponse(iotRes, iotDBKeyArr);
            metricDataList.put(metricName.get(1), openTSDBQueryResponseList);
        }
        ArrayList<HashMap<String, Object>> body = null;
        // 清除多余数据包
        boolean haveDataPacket = clearRepeatSignalValue(metricDataList, signalLineLastTimeMap);
        if (haveDataPacket) {
            // 消息统一发送机制
            HashMap<String, ArrayList<OpenTSDBQueryEntity>> sendBody = queryMessageUnitySend(metricDataList, signalValueCache, metricNames, firstFlag);
            // 将数据进行格式化封装
            if (!sendBody.isEmpty()) {
                body = wrapperSignalTrendResponse(sendBody);
            }
        }
        // 为返回信息添加一个标识头，第一次绘制图像返回“history”，其余每次都是“line”
        String head = firstFlag ? "history" : "line";
        String res = null;
        if (firstFlag && body == null) {
            body = getEmptyBody(metricNames, ids);
        }
        if (body != null) {
            res = insertHeadForTrendResponse(body, head);
        }
        return res;
    }

    /**
     * 将iotdb查询到的数据封装成OpenTSDBQueryResponse类，便于复用opentsdb的处理函数。
     * （可将opentsdb和iotdb的处理函数改为中性通用函数）
     *
     * @param dbQueryResult iotdb查询的返回数据
     * @param iotDBKeyArr   iotdb查询key的数组
     * @return java.util.ArrayList<com.dhc.rad.modules.monitor.entity.OpenTSDBQueryEntity>
     * <AUTHOR> GuoYang
     * 2021/4/15
     */
    private ArrayList<OpenTSDBQueryEntity> dbQueryResult2OpenTSDBQueryResponse(ArrayList<ArrayList<String>> dbQueryResult, String[] iotDBKeyArr) {
        ArrayList<OpenTSDBQueryEntity> res = new ArrayList<>();
        ArrayList<HashMap<String, Double>> tempMapList = new ArrayList<>();
        for (String ignored : iotDBKeyArr) {
            tempMapList.add(new HashMap<>());
        }
        for (ArrayList<String> rowInfo : dbQueryResult) {
            String timeStamp = rowInfo.get(0);
            for (int i = 1; i < rowInfo.size(); i++) {
                String signalValue = rowInfo.get(i);
                if (signalValue != null) {
                    if ("false".equalsIgnoreCase(signalValue)) {
                        signalValue = "0";
                    }
                    if ("true".equalsIgnoreCase(signalValue)) {
                        signalValue = "1";
                    }
                    tempMapList.get(i - 1).put(timeStamp, Double.valueOf(signalValue));
                }
            }
        }
        for (int i = 0; i < iotDBKeyArr.length; i++) {
            OpenTSDBQueryEntity signalValues = new OpenTSDBQueryEntity();
            signalValues.setVId(iotDBKeyArr[i]);
            signalValues.setDps(tempMapList.get(i));
            res.add(signalValues);
        }
        return res;
    }

    /**
     * 将openTSDB查询得出的数据，封装至信号趋势预测返回体。
     *
     * @param src openTSDB查询得到的数据
     * <AUTHOR> GuoYang
     * 2019/11/22
     **/
    private ArrayList<HashMap<String, Object>> wrapperSignalTrendResponse(HashMap<String, ArrayList<OpenTSDBQueryEntity>> src) {
        ArrayList<HashMap<String, Object>> res = new ArrayList<>();
        for (Map.Entry<String, ArrayList<OpenTSDBQueryEntity>> entry : src.entrySet()) {
            String metricName = entry.getKey();
            for (OpenTSDBQueryEntity signal : entry.getValue()) {
                String vid = signal.getVId();
                JSONArray valueArray = formatOpenTSDBDps(signal);
                putMessage2res(res, valueArray, metricName, vid);
            }
        }
        return res;
    }

    private void putMessage2res(ArrayList<HashMap<String, Object>> res, JSONArray valueArray, String metricName, String vid) {
        for (HashMap<String, Object> signalAllInfo : res) {
            if (signalAllInfo.get("key").equals(vid)) {
                signalAllInfo.put(metricName, valueArray);
                return;
            }
        }
        HashMap<String, Object> newSignalRes = new HashMap<>();
        newSignalRes.put("key", vid);
        newSignalRes.put(metricName, valueArray);
        res.add(newSignalRes);
    }

    /**
     * 清除已发送给前端的数据，只保留增量包
     *
     * @param src                   请求opentsdb得到的数据
     * @param signalLineLastTimeMap 保存单个session中所有曲线最后一包的时间戳
     * <AUTHOR> GuoYang
     * 2020/1/3
     **/
    private boolean clearRepeatSignalValue(HashMap<String, ArrayList<OpenTSDBQueryEntity>> src, HashMap<String, Long> signalLineLastTimeMap) {
        int totalCount = 0;
        int emptyCount = 0;
        for (Map.Entry<String, ArrayList<OpenTSDBQueryEntity>> typeEntry : src.entrySet()) {
            for (OpenTSDBQueryEntity signal : typeEntry.getValue()) {
                totalCount++;
                String vId = signal.getVId();
                String type = signal.getType();
                HashMap<String, Double> dps = signal.getDps();
                Long lastTime = getSignalLineLastTime(signalLineLastTimeMap, type + vId);
                ArrayList<String> removeKeyList = new ArrayList<>();
                Long tempLastTime = lastTime;
                for (Map.Entry<String, Double> entry : dps.entrySet()) {
                    String timeString = entry.getKey();
                    long targetTime = Long.parseLong(String.valueOf(timeString));
                    if (targetTime <= lastTime) {
                        removeKeyList.add(timeString);
                    } else {
                        tempLastTime = Math.max(targetTime, tempLastTime);
                    }
                }
                // 保存最后一包的时间戳
                signalLineLastTimeMap.put(type + vId, tempLastTime);
                // 清除多余数据
                for (String key : removeKeyList) {
                    dps.remove(key);
                }
                if (dps.isEmpty()) {
                    emptyCount++;
                }
            }
        }
        return totalCount != emptyCount;
    }

    private HashMap<String, ArrayList<OpenTSDBQueryEntity>> queryMessageUnitySend(HashMap<String, ArrayList<OpenTSDBQueryEntity>> metricDataList, HashMap<String, TreeMap<String, Double[]>> signalValueCache, List<List<String>> metricNames, boolean firstFlag) {
        // 添加
        for (Map.Entry<String, ArrayList<OpenTSDBQueryEntity>> metric : metricDataList.entrySet()) {
            for (OpenTSDBQueryEntity openTSDBQueryEntity : metric.getValue()) {
                String vid = openTSDBQueryEntity.getVId();
                String[] a = vid.split("_");
                vid = a[0];
                String type = a[1];
                HashMap<String, Double> dps = openTSDBQueryEntity.getDps();
                for (Map.Entry<String, Double> singleValue : dps.entrySet()) {
                    String time = singleValue.getKey();
                    Double value = singleValue.getValue();
                    // 时间对齐
                    String write2DBTime = time;
                    if (!type.equals(metricNames.get(0).get(0))) {
                        write2DBTime = String.valueOf(Long.parseLong(time) - 300 * 1000);
                    }
                    Double[] valuesGroupByTime = getValueGroupByTime(signalValueCache, vid, write2DBTime);
                    // 对应类型
                    for (int i = 0; i < metricNames.size(); i++) {
                        if (metricNames.get(i).get(0).equals(type)) {
                            valuesGroupByTime[i] = value;
                        }
                    }
                    setValueGroupByTime(signalValueCache, vid, write2DBTime, valuesGroupByTime);
                }
            }
        }
        // 查询
        HashMap<String, ArrayList<OpenTSDBQueryEntity>> res = new HashMap<>();
        for (Map.Entry<String, TreeMap<String, Double[]>> signal : signalValueCache.entrySet()) {
            String signalId = signal.getKey();
            TreeMap<String, Double[]> signalInfo = signal.getValue();
            HashMap<String, Double[]> tempValues = new HashMap<>();
            getFullArrayAndClearPrevious(tempValues, signalInfo, null, false, firstFlag);
            if (tempValues.isEmpty()) {
                continue;
            }
            for (int i = 0; i < metricNames.size(); i++) {
                putReadyData2Res(res, tempValues, signalId, i, metricNames);
            }
        }
        return res;
    }

    private Double[] getValueGroupByTime(HashMap<String, TreeMap<String, Double[]>> signalValueCache, String vid, String time) {
        if (!signalValueCache.containsKey(vid)) {
            signalValueCache.put(vid, new TreeMap<>());
        }
        TreeMap<String, Double[]> signalInfo = signalValueCache.get(vid);
        if (!signalInfo.containsKey(time)) {
            signalInfo.put(time, new Double[4]);
        }
        return signalInfo.get(time);
    }

    private void setValueGroupByTime(HashMap<String, TreeMap<String, Double[]>> signalValueCache, String vid, String time, Double[] value) {
        if (!signalValueCache.containsKey(vid)) {
            signalValueCache.put(vid, new TreeMap<>());
        }
        TreeMap<String, Double[]> signalInfo = signalValueCache.get(vid);
        if (!signalInfo.containsKey(time)) {
            signalInfo.put(time, value);
        }
    }

    private static void getFullArrayAndClearPrevious(HashMap<String, Double[]> res, TreeMap<String, Double[]> signalInfo, String lastTime, boolean delete, boolean firstFlag) {
        Map.Entry<String, Double[]> timeEntry = StringUtils.isEmpty(lastTime) ? signalInfo.lastEntry() : signalInfo.lowerEntry(lastTime);
        if (timeEntry == null) {
            return;
        }
        Double[] values = timeEntry.getValue();
        String time = timeEntry.getKey();
        for (Double value : values) {
            if (value == null) {
                if (firstFlag) {
                    res.put(time, values);
                    signalInfo.remove(time);
                } else {
                    if (delete) {
                        signalInfo.remove(time);
                    } else {
                        lastTime = time;
                    }
                }
                getFullArrayAndClearPrevious(res, signalInfo, lastTime, delete, firstFlag);
                return;
            }
        }
        res.put(time, values);
        signalInfo.remove(time);
        getFullArrayAndClearPrevious(res, signalInfo, lastTime, true, firstFlag);
    }

    private void putReadyData2Res(HashMap<String, ArrayList<OpenTSDBQueryEntity>> res, HashMap<String, Double[]> tempValues, String signalId, int index, List<List<String>> metricNames) {
        String metricName = metricNames.get(index).get(1);
        if (!res.containsKey(metricName)) {
            res.put(metricName, new ArrayList<>());
        }
        ArrayList<OpenTSDBQueryEntity> signals = res.get(metricName);
        // 存在
        for (OpenTSDBQueryEntity value : signals) {
            if (signalId.equals(value.getVId())) {
                rollbackTime(value, tempValues, index);
                return;
            }
        }
        // 初始
        OpenTSDBQueryEntity value = new OpenTSDBQueryEntity();
        value.setVId(signalId);
        rollbackTime(value, tempValues, index);
        signals.add(value);
    }

    private void rollbackTime(OpenTSDBQueryEntity value, HashMap<String, Double[]> tempValues, int index) {
        HashMap<String, Double> dps = value.getDps();
        for (Map.Entry<String, Double[]> timeEntry : tempValues.entrySet()) {
            String time = timeEntry.getKey();
            if (index != 0) {
                time = String.valueOf(Long.parseLong(time) + 300 * 1000);
            }
            dps.put(time, timeEntry.getValue()[index]);
        }
    }

    /**
     * 获取指定map中指定key的value，没有目标键，则添加该键并返回0L
     *
     * @param signalLineLastTimeMap 保存单个session中所有曲线最后一包的时间戳
     * @param key                   map中的key
     * @return java.lang.Long
     * <AUTHOR> GuoYang
     * 2020/1/3
     **/
    private Long getSignalLineLastTime(HashMap<String, Long> signalLineLastTimeMap, String key) {
        if (signalLineLastTimeMap.containsKey(key)) {
            return signalLineLastTimeMap.get(key);
        } else {
            signalLineLastTimeMap.put(key, 0L);
            return 0L;
        }
    }

    /**
     * 为信号趋势websocket中待返回的消息体，添加一个标识头，以便前端页面能够分辨出返回的消息体类型。
     *
     * @param body 待封装的消息体
     * @param head 伴随消息体的消息头
     * @return java.lang.String
     * <AUTHOR> GuoYang
     * 2019/11/28
     **/
    private String insertHeadForTrendResponse(Object body, String head) {
        JSONObject res = new JSONObject();
        res.put("head", head);
        res.put("body", body);
        return JSONObject.toJSONString(res, SerializerFeature.DisableCircularReferenceDetect);
    }


    public JSONArray formatOpenTSDBDps(OpenTSDBQueryEntity metric) {
        // 包括key和varValue
        List<JSONArray> varValue = new ArrayList<>();
        HashMap<String, Double> dps = metric.getDps();
        for (Map.Entry<String, Double> entry : dps.entrySet()) {
            JSONArray point = new JSONArray();
            // unix时间戳转 "2019/7/26 00:15:00"
            String key = entry.getKey();
            long unixTime = Long.parseLong(key);
            Date date = new Date(unixTime);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
            point.set(0, sdf.format(date));
            Object a = entry.getValue();
            if (a == null) {
                continue;
            }
            String value = String.format("%.1f", a);
            point.set(1, value);
            varValue.add(point);
        }
        // 时间顺序排序
        varValue.sort((o1, o2) -> {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
            try {
                return sdf.parse((String) o1.get(0)).compareTo(sdf.parse((String) o2.get(0)));
            } catch (ParseException e) {
                e.printStackTrace();
                return 0;
            }
        });
        return JSONArray.parseArray(JSON.toJSONString(varValue));
    }

    private ArrayList<HashMap<String, Object>> getEmptyBody(List<List<String>> metricNames, String ids) {
        ArrayList<HashMap<String, Object>> res = new ArrayList<>();
        String[] idArr = ids.split(",");
        for (String id : idArr) {
            HashMap<String, Object> lineMap = new HashMap<>();
            for (List<String> metricName : metricNames) {
                String lineName = metricName.get(1);
                lineMap.put(lineName, new ArrayList<>());
            }
            lineMap.put("key", id);
            res.add(lineMap);
        }
        return res;
    }

}
