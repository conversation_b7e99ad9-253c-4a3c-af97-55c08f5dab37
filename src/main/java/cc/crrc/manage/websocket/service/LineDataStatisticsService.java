package cc.crrc.manage.websocket.service;

import cc.crrc.manage.cache.CacheUtils;
import cc.crrc.manage.cache.dict.VehicleCache;
import cc.crrc.manage.common.utils.Constants;
import cc.crrc.manage.common.utils.tsdb.TSDBUtils;
import cc.crrc.manage.common.utils.tsdb.iotdb.IoTDBUtils;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zhiyang
 * @date 2022/07/05/ 9:15
 */
@Service
public class LineDataStatisticsService {

    @Autowired
    private TSDBUtils tsdbUtils;

    public JSONObject process(String message) {
        JSONObject msgJson = JSONObject.parseObject(message);
        //车辆编码
        String vehicleCode = msgJson.getString("vehicleCode");
        //车辆信息
        List<MtrVehiclePO> vehicleList = CacheUtils.getValue(VehicleCache.class, Constants.LINE_ID);
        Map<String, Integer> vehicleMap = vehicleList.stream().collect(Collectors.toMap(MtrVehiclePO::getVehicleCode, MtrVehiclePO::getVehicleStorageGroup));
        //存储组
        String iotDBSql = "SELECT last statistics,time FROM root.statistics.sg{0,number,#}.{1}";
        //查询sql
        String querySql = MessageFormat.format(iotDBSql, vehicleMap.get(vehicleCode), vehicleCode);
        //查询结果
        ArrayList<ArrayList<String>> dbQueryResult = ((IoTDBUtils) tsdbUtils).query2IoTDB(querySql);
        JSONObject json = new JSONObject();
        if (CollectionUtils.isEmpty(dbQueryResult)) {
            json.put("value", 0);
            json.put("time", System.currentTimeMillis());
        } else {
            json.put("value", Long.valueOf(dbQueryResult.get(0).get(2)));
            json.put("time", Long.valueOf(dbQueryResult.get(0).get(0)));
        }
        return json;
    }

}
