package cc.crrc.manage.websocket.service;


import cc.crrc.manage.cache.CacheUtils;
import cc.crrc.manage.cache.dict.VehicleCache;
import cc.crrc.manage.common.utils.DateUtils;
import cc.crrc.manage.common.utils.RedisUtils;
import cc.crrc.manage.common.utils.ShadowJsonUtils;
import cc.crrc.manage.mapper.analysis.AnalysisMapping;
import cc.crrc.manage.mapper.mtc.MtcAlarmWarningMapping;
import cc.crrc.manage.pojo.mtc.MtcAlarmWarningPO;
import cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import cc.crrc.manage.websocket.utils.CommonUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static java.util.stream.Collectors.*;

@Service
public class LineSocketService {
    private final Logger logger = LoggerFactory.getLogger(LineSocketService.class);

    @Autowired
    private MtcAlarmWarningMapping alarmWarningMapping;

    @Autowired
    private AnalysisMapping analysisMapping;

    @Autowired
    private CommonUtil util;

    /**
     * 线路监控(深圳六模板) websocket 实时推送
     * 若在本机部署 访问接口为 ws://127.0.0.1:10018/api/websocket
     * 发送message为 {"cmd":"lineMonitor","message":{"lineId":"24"}}
     * (cmd 和 轮询时间配置 在枚举类WebSocketRouteEnum中)
     *
     * @param message 车辆编码 例如 {"trainCode":"SZ6_01"}
     * @return java.lang.String
     * <AUTHOR> GuoYang
     * 2019/12/24
     **/
    public String getAll(String message) {
        JSONObject messageJson = JSONObject.parseObject(message);
        String lineId = messageJson.getString("lineId");
        List<MtrVehiclePO> vehicleCodes = CacheUtils.getValue(VehicleCache.class, lineId);
        JSONObject returnObject = new JSONObject();
        JSONArray vehiclesMileage = new JSONArray();
        List<String> onlineVehicles = new ArrayList<>();
        //21-08-09 标签模式 上方故障车辆、预警车辆百分比展示使用
        int onlineFaultVehicleCount = 0;
        int onlineWarningVehicleCount = 0;
        //查询当前线路下未解除的故障和预警
        List<MtcAlarmWarningVO> list = alarmWarningMapping.getLineFaultStatistics(lineId);
        //在线车辆map，用于故障统计和预警统计过滤
        Map<String, Boolean> onlineVehicleMap = new HashMap<>(vehicleCodes.size());
        // 1.线路总览和里程统计
        try {
            JSONArray stations = new JSONArray();
            //查询里程信号
            List<HashMap<String, String>> signalValueList = analysisMapping.getDistancesAndEnergyByV(RUN_DISTANCE_SIGNAL);
            Map<String, String> runDistance = signalValueList.stream()
                    .collect(toMap(i -> i.get("vehiclecode"), i -> String.valueOf(i.get("value"))));
            //计算故障最高等级
            Map<String, Map<String, Integer>> faultMaxLevel = list
                    .stream()
                    .filter(o -> o.getFaultLevel() != null)
                    .filter(MtcAlarmWarningVO::getRunningStatus)
                    .collect(groupingBy(MtcAlarmWarningVO::getVehicleCode,
                            toMap(MtcAlarmWarningVO::getFaultSource, MtcAlarmWarningVO::getFaultLevel, Integer::max)));
            for (MtrVehiclePO vehicle : vehicleCodes) {
                String vehicleCode = vehicle.getVehicleCode();
                //获取redis数据
                JSONObject objResult = getRedisDateByVehicleCode(vehicleCode);
                //里程统计
                Long distanceRedis = ShadowJsonUtils.getJsonLongValue(objResult, RUN_DISTANCE_SIGNAL, 0L);
                String distance = distanceRedis == 0L ? runDistance.getOrDefault(vehicleCode, "0") : String.valueOf(distanceRedis);
                vehicle.setMileage(distance);
                //获取站点信息 必须在业务的第一行，保证trainOverview不被替换掉
//                JSONObject trainOverview = util.getStationAndLocation(objResult, lineId, vehicleCode);
                JSONObject trainOverview = util.getStationAndLocationBaseOnCache(objResult, lineId, vehicleCode);
                //判断是否在线
                boolean onlineStatus = onlineStatusOfTrain(vehicleCode);
                if (null != objResult) {
                    onlineVehicleMap.put(vehicleCode, onlineStatus);
                    onlineVehicles.add(vehicleCode);
                    //车辆速度、运行状态、投运状态、行车模式 故障和预警最高等级
                    Map<String, Integer> vehicleLevel = faultMaxLevel.getOrDefault(vehicleCode, new HashMap<>());
                    onlineFaultVehicleCount += vehicleLevel.getOrDefault("0", 0) > 0 ? 1 : 0;
                    onlineWarningVehicleCount += vehicleLevel.getOrDefault("1", 0) > 0 ? 1 : 0;
                    getVehicleSpeedAndModeInfo(trainOverview, objResult, vehicleLevel);
                    //基础信息
                    trainOverview.put("vehicleCode", vehicleCode);
                    trainOverview.put("vehicleNameCn", vehicle.getNameCn());
                    trainOverview.put("onlineStatus", onlineStatus);
                    if (!onlineStatus) {
                        CommonUtil.packgeData.remove(vehicleCode);//不在线车辆，清楚缓存影响
                    }
                } else {
                    trainOverview = initVechileDefaultValue(vehicle);
                    vehicle.setMileage("0");
                }
                stations.add(trainOverview);
                vehiclesMileage.add(vehicle);
            }
            stations.sort(Comparator.comparing(obj -> ((JSONObject) obj).getBoolean("onlineStatus")).reversed());
            returnObject.put("LineOverview", stations);
            returnObject.put("MileageStatistics", vehiclesMileage);
        } catch (Exception e) {
            logger.error("LineSocketService  error {}", e.getMessage());
        }
        // 2.故障统计（标签模式） 统计不区分正线非正线
        try {
            JSONObject lineFaultOverview = new JSONObject();
            Map<Integer, Map<String, Long>> map = list.stream()
                    .filter(o -> onlineVehicleMap.containsKey(o.getVehicleCode()))
                    .filter(o -> o.getFaultLevel() != null && o.getFaultLevel() > 0)
                    .collect(groupingBy(MtcAlarmWarningVO::getFaultLevel, groupingBy(MtcAlarmWarningVO::getFaultSource, counting())));
            // 告警
            lineFaultOverview.put("SeriousAlarm", map.getOrDefault(3, new HashMap<>(1)).getOrDefault("0", 0L));
            lineFaultOverview.put("GeneralAlarm", map.getOrDefault(2, new HashMap<>(1)).getOrDefault("0", 0L));
            lineFaultOverview.put("MinorAlarm", map.getOrDefault(1, new HashMap<>(1)).getOrDefault("0", 0L));
            // 预警
            lineFaultOverview.put("SeriousWarning", map.getOrDefault(3, new HashMap<>(1)).getOrDefault("1", 0L));
            lineFaultOverview.put("GeneralWarning", map.getOrDefault(2, new HashMap<>(1)).getOrDefault("1", 0L));
            lineFaultOverview.put("MinorWarning", map.getOrDefault(1, new HashMap<>(1)).getOrDefault("1", 0L));
            returnObject.put("LineFaultOverview", lineFaultOverview);
        } catch (Exception e) {
            logger.error("LineSocketService  error {}", e.getMessage());
        }

        // 5.（告警）故障记录列表 && 预警记录列表
        try {
            Map<String, List<MtcAlarmWarningVO>> collect = list.stream()
                    .filter(o -> o.getFaultLevel() != null)
                    .filter(MtcAlarmWarningPO::getRunningStatus)
                    ////多重条件下使用多个reversed（）会导致左侧反转
                    .sorted(Comparator.comparing(MtcAlarmWarningVO::getStartTime).reversed().thenComparing(MtcAlarmWarningVO::getId))
                    .collect(groupingBy(MtcAlarmWarningVO::getFaultSource,
                            collectingAndThen(toList(), l -> l.stream().limit(100).collect(toList()))));
            returnObject.put("alarmList", collect.getOrDefault("0", new ArrayList<>(1)));
            returnObject.put("warningList", collect.getOrDefault("1", new ArrayList<>(1)));
        } catch (Exception e) {
            logger.error("LineSocketService  error {}", e.getMessage());
        }

        //当日故障
        try {
            List<MtcAlarmWarningVO> currentFault = list.stream()
                    .filter(o -> o.getFaultLevel() != null)
                    .filter(i -> "0".equals(i.getFaultSource()) && i.getRunningStatus())
                    .filter(i -> i.getStartTime().after(DateUtils.getStartOfDay(new Date())))
                    .sorted(Comparator.comparing(MtcAlarmWarningVO::getStartTime).reversed().thenComparing(MtcAlarmWarningVO::getId))
                    .limit(100)
                    .collect(toList());
            returnObject.put("currentDayAlarmList", currentFault);
        } catch (Exception e) {
            logger.error("alarm data error ：", e);
        }
        //故障车辆 预警车辆
        returnObject.put("onlineFaultVehicleCount", onlineFaultVehicleCount);
        returnObject.put("onlineWarningVehicleCount", onlineWarningVehicleCount);
        return JSONObject.toJSONString(returnObject, SerializerFeature.DisableCircularReferenceDetect);
    }

    protected boolean onlineStatusOfTrain(String vehicleCode) {
        Object online = RedisUtils.get(vehicleCode + "_online");
        return !Objects.isNull(online) && online.equals(true);
    }

    /**
     * 车辆未上线或离线状态时进行基础数据初始化
     *
     * @param vehicle
     * @return JSONObject
     * @throws
     * <AUTHOR>
     * @date 2022-8-9
     * @version 1.0
     */
    private JSONObject initVechileDefaultValue(MtrVehiclePO vehicle) {
        JSONObject trainOverview = new JSONObject();
        //基础信息
        trainOverview.put("vehicleCode", vehicle.getVehicleCode());
        trainOverview.put("vehicleNameCn", vehicle.getNameCn());
        trainOverview.put("onlineStatus", false);
        // 线网监控4 各个车辆车站信息 已经在两站之间的百分比
        trainOverview.put("startStstion", "--");
        trainOverview.put("currentStation", "--");
        trainOverview.put("nextStation", "--");
        trainOverview.put("endStation", "--");
        trainOverview.put("position", "0.00");
        trainOverview.put("direction", "");
        //获取车辆告警等级 预警等级信息 和车辆是否属于故障状态的信息 和速度
        trainOverview.put("vehicleFaultLevel", -1);
        trainOverview.put("vehicleWarningLevel", -1);
        trainOverview.put("faultStatus", "0");
        //车辆速度、运行状态、投运状态、行车模式
        trainOverview.put("trainATOMode", "未知");
        trainOverview.put("workingMode", "未知");
        trainOverview.put("speed", 0);
        trainOverview.put("status", 0);
        return trainOverview;
    }

    /**
     * @Description 获取redis数据
     * @Return com.alibaba.fastjson.JSONObject
     * @Param [vehicleCodeForRedis]
     **/
    private JSONObject getRedisDateByVehicleCode(String vehicleCode) {
        String key = vehicleCode + "_shadow_new";
        Object redisResult = RedisUtils.get(key);
        if (redisResult != null) {
            return JSONObject.parseObject(JSONObject.toJSONString(redisResult)).getJSONObject("signals");
        }
        return null;
    }

    //驾驶模式map
    public final static Map<String, String> DRIVE_MODE_MAP = new HashMap<String, String>() {
        {
            put("1", "洗车模式");
            put("2", "EUM模式");
            put("3", "RM模式");
            put("4", "CM模式");
            put("5", "RRM模式");
            put("6", "FAM模式");
            put("7", "CAM模式");
            put("8", "ATB模式");
            put("9", "ATO模式");
            put("10", "紧急牵引");
        }
    };

    //工况模式map
    public final static Map<Integer, String> WORK_CONDITION_MAP = new HashMap<Integer, String>() {
        {
            put(1, "待命");
            put(2, "正线服务");
            put(3, "清扫、检修");
            put(4, "洗车工况");
            put(5, "退出正线");
            put(6, "调车");
            put(7, "上电至待命前");
            put(8, "场内运行");
        }
    };

    //运行里程信号
    private final static String RUN_DISTANCE_SIGNAL = "ECR_udRunDistance";
    //列车速度信号
    private final static String VEHICLE_SPEED_SIGNAL = "DRV_uiTrainSpeed";
    //驾驶模式信号
    private final static String MAN_MODE_SIGNAL = "DRV_usDriveMode";
    //工况模式信号 20230524日查询，工况信号不可用
    private final static String WORK_CONDITION_SIGNAL = "ATC1_IxWorkCondition";
    //工况模式中正线状态值
    private final static int WORK_CONDITION_VALUE = 2;

    /**
     * 封装车辆的 行车模式、车辆速度、运行模式、投运状态api 故障和预警最高等级
     *
     * @param trainOverview
     * @param objResult
     * @param vehicleLevel  车辆的故障和预警等级map
     * @throws
     * <AUTHOR>
     * @date 2022-5-10
     * @version 1.0
     */
    private void getVehicleSpeedAndModeInfo(JSONObject trainOverview, JSONObject objResult, Map<String, Integer> vehicleLevel) {
        // 获取控制模式
        String drvMode = ShadowJsonUtils.getJsonStringValue(objResult, MAN_MODE_SIGNAL);
        String trainMode = DRIVE_MODE_MAP.getOrDefault(drvMode, "未知");
        // 获取车辆速度
        String speed = ShadowJsonUtils.getJsonStringValue(objResult, VEHICLE_SPEED_SIGNAL);
//        Integer workingValue = ShadowJsonUtils.getJsonIntegerValue(objResult, WORK_CONDITION_SIGNAL, 0);
        Integer workingValue = WORK_CONDITION_VALUE;
        // 工况模式（正常有4个信号）
        String workingMode = WORK_CONDITION_MAP.getOrDefault(workingValue, "未知");
        //车辆最高故障和最高预警值
        trainOverview.put("vehicleFaultLevel", vehicleLevel.getOrDefault("0", -1));
        trainOverview.put("vehicleWarningLevel", vehicleLevel.getOrDefault("1", -1));
        trainOverview.put("faultStatus", vehicleLevel.getOrDefault("0", 0) > 0 ? "1" : "0");
        // 投运状态
        Integer expandPower = workingValue == WORK_CONDITION_VALUE ? 1 : 0;
        trainOverview.put("trainATOMode", trainMode);
        trainOverview.put("speed", speed);
        trainOverview.put("workingMode", WORK_CONDITION_MAP.get(WORK_CONDITION_VALUE));
        trainOverview.put("status", expandPower);
    }

}