package cc.crrc.manage.websocket.service.MonitoringService;


import cc.crrc.manage.cache.CacheUtils;
import cc.crrc.manage.cache.dict.DictCache;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.pojo.SysDictVO;
import cc.crrc.manage.websocket.entity.TriggerPO;
import cc.crrc.manage.websocket.dao.SocketMonitorMenuDao;
import cc.crrc.manage.websocket.entity.MonitorMenuEntityPO;
import cc.crrc.manage.websocket.utils.MenuTree;
import cc.crrc.manage.websocket.utils.TreeUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * @Description：菜单业务层
 * @FileName: MonitorMenuService
 * @Author: liu xinchen
 * @Date: 2020/7/16
 */
@Service
public class SocketMonitorMenuService {


    @Autowired
    private SocketMonitorMenuDao monitorMenuDao;

    /**
     * @Description 查询菜单信息/通过id查询菜单信息
     * @Author: liu xinchen
     * @Param: [monitorMenuEntityPO]
     * @return: java.util.List<com.dhc.rad.modules.monitoringConfig.entity.MonitorMenuEntityPO>
     * @Date: 2020/7/20
     */
    public List<MonitorMenuEntityPO> getMonitorMenuList(MonitorMenuEntityPO monitorMenuEntityPO) {
        try{
            List<MonitorMenuEntityPO> list = monitorMenuDao.getMonitorMenuList(monitorMenuEntityPO);
            return list;
        }catch (Exception e){
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }

    }

    /**
     * @Description 查询父子结构菜单信息
     * @Author: liu xinchen
     * @Param: [monitorMenuEntityPO]
     * @return: java.util.List<com.dhc.rad.modules.monitoringConfig.util.MenuTree>
     * @Date: 2020/7/24
     */
    public List<MenuTree> getMenuList(MonitorMenuEntityPO monitorMenuEntityPO) {
        List<MonitorMenuEntityPO> menuList = new ArrayList();
        List<SysDictVO> trainMappings = CacheUtils.getValue(DictCache.class, StringUtils.join(DictCache.TRAIN_MONITOR_CONFIG_MAPPING, "_", CacheUtils.METRO_LINE_ID, "_", CacheUtils.VEHICLE_TYPE_ID));
        monitorMenuEntityPO.setTraCode(trainMappings.stream()
                .filter(i -> monitorMenuEntityPO.getTraCode().equals(i.getValue()))
                .map(SysDictVO::getLabel)
                .findFirst()
                .orElse("02001"));
        for (MonitorMenuEntityPO menu : monitorMenuDao.getMenuList(monitorMenuEntityPO)) {
            // 首页面不显示仪表盘类型的菜单 过滤仪表盘(dashBoard)类型数据
            if (!menu.getMenuType().equals("dashBoard")) {
                menuList.add(menu);
            }
        }
        return getMenuTree(menuList, "-1");
    }


    /**
     * @Description 获取树形结构的json串
     * @Author: liu xinchen
     * @Param: [menus, root]
     * @return: java.util.List<com.dhc.rad.modules.monitoringConfig.util.MenuTree>
     * @Date: 2020/7/20
     */
    List<MenuTree> getMenuTree(List<MonitorMenuEntityPO> menus, String root) {
        List<MenuTree> trees = new ArrayList<MenuTree>();
        MenuTree node = null;
        for (MonitorMenuEntityPO menu : menus) {
            node = new MenuTree();
            node.setUrl(menu.getUrl());
            node.setMenuCode(menu.getMenuCode());
            node.setShowStatus(menu.getShowStatus());
            node.setComponentsUrl(menu.getComponentsUrl());
            node.setDefaultActive(menu.getDefaultActive());
            BeanUtils.copyProperties(menu, node);
            trees.add(node);
        }
        List<MenuTree> bulid = TreeUtil.bulid(trees, root, null);
        return bulid;
    }

    public Object listLabelAndLogo(String itemId) {
        try {
            List<TriggerPO> list = monitorMenuDao.listLabelAndLogo(itemId);
            return list.stream()
                    .filter(o-> StringUtils.isNotBlank(o.getSvgUrl()))
                    .map(o->{
                        o.setSvgUrl(String.join(".",o.getSvgUrl(),o.getImageType()));
                        return o;
                    })
                    .collect(toList());
        } catch (Exception e) {
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }
}
