package cc.crrc.manage.websocket;

import cc.crrc.manage.websocket.entity.SignalTrendWebSocketCache;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.atomic.AtomicInteger;

@ServerEndpoint(value = "/websocket")
@Component
public class WebSocketServer {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private static final ConcurrentHashMap<String, SocketThread> socketThreadMap = new ConcurrentHashMap<>();

    // concurrent包的线程安全Set，用来存放每个客户端对应的MyWebSocket对象。
    private static final CopyOnWriteArraySet<WebSocketServer> webSocketSet = new CopyOnWriteArraySet<>();

    // 线程安全的int型原子操作类，自增自减统计ws连接数量。
    private static final AtomicInteger ONLINE_COUNT = new AtomicInteger(0);

    // 最大重试次数
    private static final int RETRY_TIME = 5;
    // 最长重试时间间隔
    private static final int MAX_RETRY_INTERVAL = 10000;

    // 与某个客户端的连接会话，需要通过它来给客户端发送数据。
    private Session session;

    // 用于信号趋势预警
    public final SignalTrendWebSocketCache signalTrendWebSocketCache = new SignalTrendWebSocketCache();
    //用于处理线程的ScriptEngine
    public static final InheritableThreadLocal<ScriptEngine> SCRIPT_ENGINE_THREAD_LOCAL = new InheritableThreadLocal<>();

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session) {
        this.session = session;
        webSocketSet.add(this);
        int cnt = ONLINE_COUNT.incrementAndGet(); // 在线数加1
        logger.info("Connection! Current number of connections：{} session id {}", cnt, session.getId());
        sendMessage("Connection succeeded: " + session.getId());
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        if (Objects.nonNull(session)) {
            String sessionId = session.getId();
            SocketThread thread = socketThreadMap.get(sessionId);
            if (thread != null) {
                thread.setLoop(false);
                socketThreadMap.remove(sessionId);
            }
        }
        webSocketSet.remove(this);
        int cnt = ONLINE_COUNT.decrementAndGet();// 在线数减1
        logger.info("Disconnect! Current number of connections：{}", cnt);
    }

    /**
     * 收到客户端消息后调用的方法
     *
     * @param message 客户端发送过来的消息
     */
    @OnMessage
    public void onMessage(String message) throws Exception {
        logger.info("Receive message: {} session Id {}", message, session.getId());
        JSONObject messageJson = JSONObject.parseObject(message);
        messageJson.getJSONObject("message").put("sessionId", session.getId());
        WebSocketDispatcher.processMessage(this, messageJson.toJSONString());
    }

    /**
     * 出现异常时调用，关闭session连接，不予返回信息。
     *
     * <AUTHOR> GuoYang
     * 2019/11/4
     */
    @OnError
    public void onError(Throwable error) {
//        logger.info("link onError Current Thread id is {}, Thread Name is {} scriptEngine is {}", Thread.currentThread().getId(), Thread.currentThread().getName(), SCRIPT_ENGINE_THREAD_LOCAL.get());
        SCRIPT_ENGINE_THREAD_LOCAL.remove();

        if (Objects.nonNull(session)) {
            String sessionId = session.getId();
            SocketThread thread = socketThreadMap.get(sessionId);
            if (Objects.nonNull(thread)) {
                thread.setLoop(false);
                socketThreadMap.remove(sessionId);
            }
        }

        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        error.printStackTrace(pw);
        logger.error("Websocket error: {}", sw.toString());
        // IOException通讯异常时，session会自动调用onClose，无需手动关闭。
        if (!(error instanceof IOException)) {
            activeClose();
        }
    }

    /**
     * 后台主动关闭session连接。
     * session连接关闭时，会自动调用onClose方法！
     *
     * <AUTHOR> GuoYang
     * 2019/11/4
     **/
    public void activeClose() {
        try {
            logger.info("Closing session active...");
            session.close();
        } catch (IOException e) {
            logger.error("Failed to close session active: {}", e.getMessage());
        }
    }

    /**
     * 实现服务器主动推送
     */
    public void sendMessage(Object message) {
        try {
            if (this.session.isOpen()) {
                this.session.getBasicRemote().sendText(JSON.toJSONString(message));
            }
        } catch (IOException e) {
            logger.error("Sending message error: {}", e.getMessage());
        }
    }

    /**
     * 实现服务器主动群发消息
     */
    public static void broadMessage(Object message) {
        for (WebSocketServer item : webSocketSet) {
            item.sendMessage(message);
        }
    }

    /**
     * 根据路由中cmd对应的方法，处理并发送一次数据。
     * 出现异常时，重试五次，重试时间越来越长。
     * 重试五次失效，则主动断开连接。
     *
     * <AUTHOR> GuoYang
     * 2019/11/1
     **/
    public void singleSendMessage(WebSocketRouteEnum webSocketRouteEnum, WebSocketServer wsServer, String message, int i) throws Exception {
        try {
            String res = WebSocketDispatcher.execute(webSocketRouteEnum, wsServer, message);
            // 若返回数据为null，则不发送数据
            if (res != null) {
                sendMessage(res);
            }
        } catch (Exception e) {
            // 异常重试
            if (i < RETRY_TIME) {
                int retryInterval = Math.min(1000 + 2000 * i, MAX_RETRY_INTERVAL);
                Thread.sleep(retryInterval);
                i++;
                singleSendMessage(webSocketRouteEnum, wsServer, message, i);
            } else {
                onError(e);
            }
        }
    }

    /**
     * 根据路由中cmd对应的方法，周期性的发送数据。
     * 发送周期定义在WebSocketRouter的WebSocketRouter中。
     * 出现异常，通过主动关闭session，实现停止该线程循环。
     *
     * <AUTHOR> GuoYang
     * 2019/11/1
     **/
    public void cycleSendMessage(WebSocketRouteEnum webSocketRouteEnum, String message) throws Exception {
        final long timeInterval = webSocketRouteEnum.getIntervalTime();
        //设置每个连接的ScriptEngine
//         Runnable runnable = () -> {
//             if (SCRIPT_ENGINE_THREAD_LOCAL.get() == null){
//                 SCRIPT_ENGINE_THREAD_LOCAL.set(new ScriptEngineManager().getEngineByName("js"));
//             }
////             logger.info("link start Current Thread id is {}, Thread Name is {} scriptEngine is {}", Thread.currentThread().getId(), Thread.currentThread().getName(), SCRIPT_ENGINE_THREAD_LOCAL.get());
//             while (session.isOpen()) {
//                try {
//                    singleSendMessage(webSocketRouteEnum, this, message, 0);
//                    Thread.sleep(timeInterval);
//                } catch (Exception e) {
//                    logger.error("Cycle send message error: {}", e.getMessage());
//                }
//            }
//            //关闭会话前删除每个连接的ScriptEngine
////            logger.info("link close Current Thread id is {}, Thread Name is {} scriptEngine is {}", Thread.currentThread().getId(), Thread.currentThread().getName(), SCRIPT_ENGINE_THREAD_LOCAL.get());
//            SCRIPT_ENGINE_THREAD_LOCAL.remove();
//        };

        String sessionId = session.getId();
        SocketThread skThread = socketThreadMap.get(sessionId);
        if (Objects.nonNull(skThread)) {
            skThread.setLoop(false);
            socketThreadMap.remove(sessionId);
        }
        SocketThread skThreadNew = new SocketThread(webSocketRouteEnum, message, timeInterval, this);
        socketThreadMap.put(sessionId, skThreadNew);

        //线程名称
        String name = sessionId + System.currentTimeMillis();
        Thread thread = new Thread(skThreadNew, name);
        thread.start();
    }

    class SocketThread implements Runnable {

        private WebSocketRouteEnum webSocketRouteEnum;
        private String message;
        private long timeInterval;
        private WebSocketServer socketServer;
        private boolean loop = true;

        public SocketThread(WebSocketRouteEnum webSocketRouteEnum, String message, long timeInterval, WebSocketServer socketServer) {
            this.webSocketRouteEnum = webSocketRouteEnum;
            this.message = message;
            this.timeInterval = timeInterval;
            this.socketServer = socketServer;
        }

        public void setLoop(boolean loop) {
            this.loop = loop;
        }

        @Override
        public void run() {
            if (SCRIPT_ENGINE_THREAD_LOCAL.get() == null) {
                SCRIPT_ENGINE_THREAD_LOCAL.set(new ScriptEngineManager().getEngineByName("js"));
            }
            while (loop && session.isOpen()) {
                try {
                    singleSendMessage(webSocketRouteEnum, socketServer, message, 0);
                    Thread.sleep(timeInterval);
                } catch (Exception e) {
                    logger.error("Cycle send message error: {}", e.getMessage());
                }
            }
            SCRIPT_ENGINE_THREAD_LOCAL.remove();
        }
    }

}
