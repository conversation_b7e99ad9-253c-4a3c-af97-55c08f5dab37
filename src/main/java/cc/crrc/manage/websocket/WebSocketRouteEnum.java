package cc.crrc.manage.websocket;

import cc.crrc.manage.websocket.service.*;
//import cc.crrc.manage.websocket.service.LineSocketService;


/**
 * 该枚举类保存所有期望cmd、执行类、执行方法及其对应的返回数据时间间隔。
 * 执行时间间隔为-1表示为只执行一次。
 * <p>
 * 删除或修改反射方法时，注意此处影响！
 *
 * <AUTHOR>
 * 2019/11/4
 **/
public enum WebSocketRouteEnum {
    NEW_TRAIN_MONITOR("newTrainMonitor", TrainMonitorService.class, "getAll", 1000, false),
    LINE_MONITOR("lineMonitor", LineSocketService.class, "getAll", 1500, false),
    SUBWAY_LINE_MONITOR("subwayLineMonitor", SubwayLineSocketService.class, "getAll", 1500, false),
    TREND_WARNING_BASIC_INFO("trendBasicInfo", TrendEarlyWarningService.class, "getSignalTrendBasicInfo", -1, false),
    TREND_WARNING_SIGNAL_DATA("trendSignalData", TrendEarlyWarningService.class, "getSignalTrendFromIotDB", 3000, true),
    /* 线路数据统计-实时统计 */
    LINE_DATA_STATISTICS("lineDataStatistics", LineDataStatisticsService.class, "process", 10 * 1000, false),
    NEW_TRAIN_MONITOR_FAULT("newTrainMonitorFault", TrainMonitorFaultService.class, "process", 1000, false),
    MECHANISM_ALARM("mechanismAlarm", MechanismAlarmSocketService.class, "getAll", 2000,false);

    private String cmd; // 命令名称
    private Class<?> targetClass; // 执行函数所在类
    private String targetMethod; // 执行函数名称
    private int intervalTime; // 查询时间间隔
    private boolean hasCache; // 是否启用server缓存类

    WebSocketRouteEnum(String cmd, Class<?> targetClass, String targetMethod, int intervalTime, boolean hasCache) {
        this.cmd = cmd;
        this.targetClass = targetClass;
        this.targetMethod = targetMethod;
        this.intervalTime = intervalTime;
        this.hasCache = hasCache;
    }

    public String getCmd() {
        return cmd;
    }

    public Class<?> getTargetClass() {
        return targetClass;
    }

    public String getTargetMethod() {
        return targetMethod;
    }

    public int getIntervalTime() {
        return intervalTime;
    }

    public boolean getHasCache() {
        return hasCache;
    }

    public static WebSocketRouteEnum getWebSocketRouteEnum(String cmd) {
        for (WebSocketRouteEnum item : WebSocketRouteEnum.values()) {
            if (cmd.equals(item.getCmd())) {
                return item;
            }
        }
        return null;
    }

}
