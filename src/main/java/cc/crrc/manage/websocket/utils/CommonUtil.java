package cc.crrc.manage.websocket.utils;

import cc.crrc.manage.cache.CacheUtils;
import cc.crrc.manage.cache.dict.StationCache;
import cc.crrc.manage.common.utils.ShadowJsonUtils;
import cc.crrc.manage.common.utils.StringUtils;
import cc.crrc.manage.pojo.line.StationDTO;
import cc.crrc.manage.websocket.service.LineSocketService;
import com.alibaba.fastjson.JSONObject;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static cc.crrc.manage.cache.dict.StationCache.MAX_STATION_CODE;
import static cc.crrc.manage.cache.dict.StationCache.MIN_STATION_CODE;
import static java.util.stream.Collectors.toMap;

/**
 * <AUTHOR> z<PERSON>
 * @FileName CommonUtil
 * @date 2023/7/12
 * @description 公共方法
 */
@Component
public class CommonUtil {

    // 反射执行缓存
    public static HashMap<String, packgeOb> packgeData = new HashMap<>();

    // 用于缓存的内部类
    static class packgeOb {
        String position;
        int nextStation;
        int current;

        packgeOb(String position, int nextStation) {
            this.position = position;
            this.nextStation = nextStation;
        }

        String getPosition() {
            return position;
        }

        int getNextStation() {
            return nextStation;
        }

    }


    private final Logger logger = LoggerFactory.getLogger(CommonUtil.class);
    //下一站id
    private static final String NEXT_STATION_ID_SIGNAL = "COM_uiNextStID";
    //终点站id
    private static final String DEST_STATION_ID_SIGNAL = "COM_uiDestStID";
    //起始站id
    private static final String START_STATION_ID_SIGNAL = "PIS_uiStartStationID";
    //下一站距离
    private static final String TARGET_DISTANCE_SIGNAL = "COM_uiTargetDistance";

    //站点之间默认距离，用于没有给出站点间实际距离参数
    private static final int CUI_DIS_TO_CUR_STATION_DEFAULT = 2000;
    private static final int CUI_DIS_TO_CUR_STATION_INVALID = 10000;
    //到站默认百分比&离站默认百分比
    private static final String DEFAULT_ARRIVE_AT_STATION_PERCENT = "0.00";
    private static final String DEFAULT_LEAVE_THE_STATION_PERCENT = "0.50";
    //默认站点值为0时，为折返线区间
    private static final String DEFAULT_STATION_SIGNAL_VALUE_ZERO_NAME = "折返线区间";
    //列车上行和下行信号
    private static final String TRAIN_ATC_UP_SIGNAL_NAME = "PIC_xATCUp";
    private static final String TRAIN_ATC_DOWN_SIGNAL_NAME = "PIC_xATCDowm";
    //使用guava缓存车辆的上一站 设置缓存项的过期时间为3分钟
    Cache<String, String> cache = CacheBuilder.newBuilder()
            .expireAfterWrite(3, TimeUnit.MINUTES)
            .build();

    /**
     * @description  获取车辆的车站信息 以及车辆行驶在两站之间的百分比
     * @return  com.alibaba.fastjson.JSONArray
     * @auther zhangzhijian
     * @date 2022/6/4
     * @param objResult redis信号
     * @param lineId 线路id
     **/
    public JSONObject getStationAndLocation(JSONObject objResult, String lineId, String vehicleCode) {
        JSONObject trainOverview = new JSONObject();
        if (objResult == null) {
            return trainOverview;
        }
        try {
            //没有始发站 所以需要进行计算
            int nextStationId = ShadowJsonUtils.getJsonIntegerValue(objResult, NEXT_STATION_ID_SIGNAL, 0);
            int endStationId = ShadowJsonUtils.getJsonIntegerValue(objResult, DEST_STATION_ID_SIGNAL, 0);
            int startStationId = ShadowJsonUtils.getJsonIntegerValue(objResult, START_STATION_ID_SIGNAL, 0);

            //列车上行(镜湖医院[id=31] -> 檀渎[id=39])和下行(檀渎[id=39] -> 镜湖医院[id=31])
            boolean uplink = ShadowJsonUtils.getJsonBooleanValue(objResult, TRAIN_ATC_UP_SIGNAL_NAME, false);
            boolean downlink = ShadowJsonUtils.getJsonBooleanValue(objResult, TRAIN_ATC_DOWN_SIGNAL_NAME, false);
            //判断上下行
            String direction = "down";
            if (uplink ^ downlink && uplink){
                direction = "up";
            }
            //距离下一站距离 当前只能依据到下一站距离判断是否车辆到站
            int cuiDisToNextStation = ShadowJsonUtils.getJsonIntegerValue(objResult, TARGET_DISTANCE_SIGNAL, 0);
            String positionStr = DEFAULT_ARRIVE_AT_STATION_PERCENT;
            int currentStationId = nextStationId;
            //计算当前站
            if (cuiDisToNextStation > 0 && cuiDisToNextStation < CUI_DIS_TO_CUR_STATION_INVALID && uplink && nextStationId != 0){
                positionStr = DEFAULT_LEAVE_THE_STATION_PERCENT;
                currentStationId = nextStationId >= MIN_STATION_CODE && nextStationId <= MAX_STATION_CODE ? nextStationId - 1 : MIN_STATION_CODE;
            }else if(cuiDisToNextStation > 0 && cuiDisToNextStation < CUI_DIS_TO_CUR_STATION_INVALID && !uplink && nextStationId != 0){
                positionStr = DEFAULT_LEAVE_THE_STATION_PERCENT;
                currentStationId = nextStationId >= MIN_STATION_CODE && nextStationId <= MAX_STATION_CODE ? nextStationId + 1 : MAX_STATION_CODE;
            }
            String segmentId = currentStationId + "-" + direction;
            trainOverview.put("direction", direction);
            trainOverview.put("startStation", displayStation(lineId, direction, startStationId));
            trainOverview.put("currentStation", displayStation(lineId, direction, currentStationId));
            trainOverview.put("nextStation", displayStation(lineId, direction, nextStationId));
            trainOverview.put("endStation", displayStation(lineId, direction, endStationId));
            trainOverview.put("position", positionStr);
            trainOverview.put("segmentId", segmentId);
        } catch (Exception e) {
            logger.error("getStationAndLocation  error {}", e.getMessage());
        }
        return trainOverview;
    }

    /**
     * @description 处理站点id对应的站点信息
     * @param lineId 线路id
     * @param direction 上下行
     * @param stationId 站点id
     * @return String
     * <AUTHOR>
     */
    public String displayStation(String lineId, String direction, int stationId){
        List<StationDTO> stationList = CacheUtils.getValue(StationCache.class, direction + "_" + lineId);
        Map<Integer, String> stationMap = stationList.stream()
                .collect(toMap(StationDTO::getStationCode, StationDTO::getName));
        return stationMap.containsKey(stationId) ? stationMap.get(stationId) : DEFAULT_STATION_SIGNAL_VALUE_ZERO_NAME;
    }

    /**
     * @description 基于缓存来解决，绍兴2车辆下一站信号，距离不为0时不准确，故下述代码做了特殊处理，不可直接借鉴
     * <AUTHOR>
     * @date 2023/07/12
     */
    public JSONObject getStationAndLocationBaseOnCache(JSONObject objResult, String lineId,String vehicleCode) {
        JSONObject trainOverview = new JSONObject();
        if (objResult == null) {
            return trainOverview;
        }
        try {
            //没有始发站 所以需要进行计算
            int nextStationId = ShadowJsonUtils.getJsonIntegerValue(objResult, NEXT_STATION_ID_SIGNAL, 0);
            int endStationId = ShadowJsonUtils.getJsonIntegerValue(objResult, DEST_STATION_ID_SIGNAL, 0);

            if(endStationId==0 && nextStationId==39){
                endStationId=MIN_STATION_CODE;
            }
            if(endStationId==0 && nextStationId==31){
                endStationId=MAX_STATION_CODE;
            }
            int startStationId = endStationId > 30 && endStationId < 40 ? MAX_STATION_CODE == endStationId ? MIN_STATION_CODE : MAX_STATION_CODE : 30;
            //判断上下行
            String direction = "down";
            int currentStationId = 0;
            int nextStationFinally=0;//下一站
            //距离下一站距离 当前只能依据到下一站距离判断是否车辆到站
            int cuiDisToNextStation = ShadowJsonUtils.getJsonIntegerValue(objResult, TARGET_DISTANCE_SIGNAL, 0);
            String positionStr = "0.00";
            if (cuiDisToNextStation > 0 && cuiDisToNextStation < CUI_DIS_TO_CUR_STATION_INVALID) {
                positionStr = "0.50";
            }
            if(10000==cuiDisToNextStation){//剔除距离为10000的数据，根据数据规律设为0.50不影响行驶轨迹
                positionStr = "0.50";
            }
                if (!packgeData.containsKey(vehicleCode) && positionStr.equals("0.50")) {
                    return trainOverview;//车辆在正线两站之间位置。我处先不处理车辆位置，待车辆到站开始计算
                }
                if (packgeData.containsKey(vehicleCode) && positionStr.equals("0.50")) {
                    //计算当前站和上下行
                    if (MAX_STATION_CODE.equals(endStationId)) {
                        direction = "down";
                        CommonUtil.packgeOb b = packgeData.get(vehicleCode);//上一包数据
                          //前端识别，当前站是0.50是过了当前站，距离是0.00到站
                            nextStationFinally = b.getNextStation() + 1;
                            currentStationId = b.getNextStation() ;
                    } else {
                        direction = "up";
                            CommonUtil.packgeOb b = packgeData.get(vehicleCode);//上一包数据
                            nextStationFinally = b.getNextStation() - 1;
                            currentStationId = b.getNextStation() ;
                        }
                    }
                if (positionStr.equals("0.00")) {
                    //计算当前站和上下行
                    if (MAX_STATION_CODE.equals(endStationId)) {
                        direction = "down";
                        currentStationId = nextStationId;
                        nextStationFinally = nextStationId+1;
                        if(nextStationFinally>MAX_STATION_CODE){
                            nextStationFinally=MAX_STATION_CODE;
                        }
                    } else {
                        direction = "up";
                        currentStationId = nextStationId;
                        nextStationFinally = nextStationId-1;//根据车辆行驶趋势
                        if(nextStationFinally<MIN_STATION_CODE){
                            nextStationFinally=MIN_STATION_CODE;
                        }
                    }
                    CommonUtil.packgeOb b = new CommonUtil.packgeOb("0.00", currentStationId);
                    packgeData.put(vehicleCode, b);
                }

            String segmentId = currentStationId + "-" + direction;
            trainOverview.put("direction", direction);
            List<StationDTO> stationList = CacheUtils.getValue(StationCache.class, direction + "_" + lineId);
            Map<Integer, String> stationMap = stationList.stream()
                    .collect(toMap(StationDTO::getStationCode, StationDTO::getName));

            trainOverview.put("startStation", stationMap.get(startStationId));
            trainOverview.put("currentStation", stationMap.get(currentStationId));
            trainOverview.put("nextStation", stationMap.get(nextStationFinally));
            trainOverview.put("endStation", stationMap.get(endStationId));
            trainOverview.put("position", positionStr);
            trainOverview.put("segmentId", segmentId);
        } catch (Exception e) {
            logger.error("getStationAndLocation  error {}", e.getMessage());
        }
        return trainOverview;
    }
}
