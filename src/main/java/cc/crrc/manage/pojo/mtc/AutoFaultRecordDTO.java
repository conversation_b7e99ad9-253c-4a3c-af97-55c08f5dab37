package cc.crrc.manage.pojo.mtc;

public class AutoFaultRecordDTO {

	private static final long serialVersionUID = 1L;
	
	private String id;
	private String vehicleId;			//车辆ID
	private String faultTypeKey;	//故障类型业务主键
	private String componentId;		//部件ID
	private String startTime;		//故障发生时间
	private String endTime;			//结束时间
	private String status;			//处理状态
	private Integer confirm;		//用户是否确认，0：未确认，1：确认
	private Float speed;			//速度
	private Long mileage;			//里程
	private Long currentStationId;	//当前站ID
	private Long nextStationId;		//下一站ID
	private Long endStationId;		//终点站
	private Integer level;			//手柄级位
	private Integer tracCmd;		//牵引级位，标识
	private Integer effDmd;			//牵引级位，值
	private String activeCabin;		//司机室激活端
	private Integer keyA1;
	private Integer keyA2;
	private String mode;			//车辆运行模式
	private String modifyBy;			//修改用户编号
	private String modifyTime;		//修改时间
	private String suppressedBy;	//抑制故障父节点
	private Boolean suppressedStatus;//雪崩压制状态
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getVehicleId() {
		return vehicleId;
	}
	public void setVehicleId(String vehicleId) {
		this.vehicleId = vehicleId;
	}
	public String getFaultTypeKey() {
		return faultTypeKey;
	}
	public void setFaultTypeKey(String faultTypeKey) {
		this.faultTypeKey = faultTypeKey;
	}
	public String getComponentId() {
		return componentId;
	}
	public void setComponentId(String componentId) {
		this.componentId = componentId;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public Integer getConfirm() {
		return confirm;
	}
	public void setConfirm(Integer confirm) {
		this.confirm = confirm;
	}
	public Float getSpeed() {
		return speed;
	}
	public void setSpeed(Float speed) {
		this.speed = speed;
	}
	public Long getMileage() {
		return mileage;
	}
	public void setMileage(Long mileage) {
		this.mileage = mileage;
	}
	public Long getCurrentStationId() {
		return currentStationId;
	}
	public void setCurrentStationId(Long currentStationId) {
		this.currentStationId = currentStationId;
	}
	public Long getNextStationId() {
		return nextStationId;
	}
	public void setNextStationId(Long nextStationId) {
		this.nextStationId = nextStationId;
	}
	public Integer getLevel() {
		return level;
	}
	public Long getEndStationId() {
		return endStationId;
	}
	public void setEndStationId(Long endStationId) {
		this.endStationId = endStationId;
	}
	public void setLevel(Integer level) {
		this.level = level;
	}
	public String getActiveCabin() {
		return activeCabin;
	}
	public void setActiveCabin(String activeCabin) {
		this.activeCabin = activeCabin;
	}
	public String getMode() {
		return mode;
	}
	public void setMode(String mode) {
		this.mode = mode;
	}
	public String getModifyBy() {
		return modifyBy;
	}
	public void setModifyBy(String modifyBy) {
		this.modifyBy = modifyBy;
	}
	public String getModifyTime() {
		return modifyTime;
	}
	public void setModifyTime(String modifyTime) {
		this.modifyTime = modifyTime;
	}
	public Integer getTracCmd() {
		return tracCmd;
	}
	public void setTracCmd(Integer tracCmd) {
		this.tracCmd = tracCmd;
	}
	public Integer getEffDmd() {
		return effDmd;
	}
	public void setEffDmd(Integer effDmd) {
		this.effDmd = effDmd;
	}
	public Integer getKeyA1() {
		return keyA1;
	}
	public void setKeyA1(Integer keyA1) {
		this.keyA1 = keyA1;
	}
	public Integer getKeyA2() {
		return keyA2;
	}
	public void setKeyA2(Integer keyA2) {
		this.keyA2 = keyA2;
	}
	public String getSuppressedBy() {
		return suppressedBy;
	}
	public void setSuppressedBy(String suppressedBy) {
		this.suppressedBy = suppressedBy;
	}
	public Boolean getSuppressedStatus() {
		return suppressedStatus;
	}
	public void setSuppressedStatus(Boolean suppressedStatus) {
		this.suppressedStatus = suppressedStatus;
	}
	
	

}
