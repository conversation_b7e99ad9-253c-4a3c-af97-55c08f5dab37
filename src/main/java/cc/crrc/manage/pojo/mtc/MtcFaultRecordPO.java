package cc.crrc.manage.pojo.mtc;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.PageVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import java.util.Date;

/**
 * 对应手动上报故障
 *
 * <AUTHOR>
 * @date 2022/08/10  15:37
 */
public class MtcFaultRecordPO extends PageVO {

    @ApiModelProperty(value = "主键id")
    @LogParam(description = "主键id")
    private String id;

    @ApiModelProperty(value = "车辆id")
    @LogParam(description = "车辆id")
    private String  vehicleId;

    @ApiModelProperty(value = "线路id")
    @LogParam(description = "线路id")
    private String  lineId;

    @ApiModelProperty(value = "故障中文名称")
    @LogParam(description = "故障中文名称")
    private String  faultNameCn;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "故障发生时间", example = "2022-11-01 15:20:00")
    @LogParam(description = "故障发生时间")
    private Date startTime;

    @ApiModelProperty(value = "系统负责人")
    @LogParam(description = "系统负责人")
    @Length(max = 64,message = "系统负责人长度不能超过64")
    private String sysResponsibleBy;

    @ApiModelProperty(value = "故障地点")
    @LogParam(description = "故障地点")
    @Length(max = 255,message = "故障地点不能超过255")
    private String faultLocation;

    @ApiModelProperty(value = "发现人")
    @LogParam(description = "发现人")
    @Length(max = 64,message = "发现人长度不能超过64")
    private String foundBy;

    @ApiModelProperty(value = "录入人")
    @LogParam(description = "录入人")
    @Length(max = 64,message = "录入人长度不能超过64")
    private String inputBy;

    @ApiModelProperty(value = "处理人")
    @LogParam(description = "处理人")
    @Length(max = 64,message = "处理人长度不能超过64")
    private String treatmentBy;

    @ApiModelProperty(value = "处理单位")
    @LogParam(description = "处理单位")
    @Length(max = 64,message = "处理单位长度不能超过64")
    private String treatmentCompany;

    @ApiModelProperty(value = "处理时间")
    @LogParam(description = "处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date treatmentTime;

    @ApiModelProperty(value = "处理情况")
    @LogParam(description = "处理情况")
    private String treatment;

    @ApiModelProperty(value = "用料情况")
    @LogParam(description = "用料情况")
    private String materialUsage;

    @ApiModelProperty(value = "用料归口")
    @LogParam(description = "用料归口")
    @Length(max = 255,message = "用料归口不能超过255")
    private String materialAscription;

    @ApiModelProperty(value = "确认人")
    @LogParam(description = "确认人")
    @Length(max = 64,message = "确认人长度不能超过64")
    private String comitBy;

    @ApiModelProperty(value = "工程师故障描述与确认")
    @LogParam(description = "工程师故障描述与确认")
    private String faultInfo;

    @LogParam(description = "故障类型")
    @ApiModelProperty("故障类型 0：自动故障 1：预警 2：人工故障")
    private String faultSource;

    @LogParam(description = "实时故障Id")
    @ApiModelProperty("实时故障Id")
    private String faultId;

    @ApiModelProperty(value = "确认状态")
    private int confirm;

    @ApiModelProperty(value = "修改人")
    @LogParam(description = "修改人")
    private String modifyBy;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public String getSysResponsibleBy() {
        return sysResponsibleBy;
    }

    public void setSysResponsibleBy(String sysResponsibleBy) {
        this.sysResponsibleBy = sysResponsibleBy;
    }

    public String getFaultLocation() {
        return faultLocation;
    }

    public void setFaultLocation(String faultLocation) {
        this.faultLocation = faultLocation;
    }

    public String getFoundBy() {
        return foundBy;
    }

    public void setFoundBy(String foundBy) {
        this.foundBy = foundBy;
    }

    public String getInputBy() {
        return inputBy;
    }

    public void setInputBy(String inputBy) {
        this.inputBy = inputBy;
    }

    public String getTreatmentBy() {
        return treatmentBy;
    }

    public void setTreatmentBy(String treatmentBy) {
        this.treatmentBy = treatmentBy;
    }

    public String getTreatmentCompany() {
        return treatmentCompany;
    }

    public void setTreatmentCompany(String treatmentCompany) {
        this.treatmentCompany = treatmentCompany;
    }

    public Date getTreatmentTime() {
        return treatmentTime;
    }

    public void setTreatmentTime(Date treatmentTime) {
        this.treatmentTime = treatmentTime;
    }

    public String getTreatment() {
        return treatment;
    }

    public void setTreatment(String treatment) {
        this.treatment = treatment;
    }

    public String getMaterialUsage() {
        return materialUsage;
    }

    public void setMaterialUsage(String materialUsage) {
        this.materialUsage = materialUsage;
    }

    public String getMaterialAscription() {
        return materialAscription;
    }

    public void setMaterialAscription(String materialAscription) {
        this.materialAscription = materialAscription;
    }

    public String getComitBy() {
        return comitBy;
    }

    public void setComitBy(String comitBy) {
        this.comitBy = comitBy;
    }

    public String getFaultInfo() {
        return faultInfo;
    }

    public void setFaultInfo(String faultInfo) {
        this.faultInfo = faultInfo;
    }

    public String getFaultSource() {
        return faultSource;
    }

    public void setFaultSource(String faultSource) {
        this.faultSource = faultSource;
    }

    public int getConfirm() {
        return confirm;
    }

    public void setConfirm(int confirm) {
        this.confirm = confirm;
    }

    public String getFaultId() {
        return faultId;
    }

    public void setFaultId(String faultId) {
        this.faultId = faultId;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public String getFaultNameCn() {
        return faultNameCn;
    }

    public void setFaultNameCn(String faultNameCn) {
        this.faultNameCn = faultNameCn;
    }
}
