package cc.crrc.manage.pojo.mtc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ParamsPO {

    private String driveMode;
    private String runningStatus;
    private String cabActOne;
    private String cabActTwo;
    private Float speed;
    private Long mileage;
    private Integer nextStationId;
    private Integer endStationId;

    public String getDriveMode() {
        return driveMode;
    }

    public String getRunningStatus() {
        return runningStatus;
    }

    public String getCabActOne() {
        return cabActOne;
    }

    public String getCabActTwo() {
        return cabActTwo;
    }

    public Float getSpeed() {
        return speed;
    }

    public Long getMileage() {
        return mileage;
    }

    public Integer getNextStationId() {
        return nextStationId;
    }

    public Integer getEndStationId() {
        return endStationId;
    }
}
