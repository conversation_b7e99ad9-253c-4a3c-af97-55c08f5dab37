package cc.crrc.manage.pojo.mtc;

import cc.crrc.manage.cache.CacheUtils;
import cc.crrc.manage.cache.dict.StationCache;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.ekb.EkbFaultMeasurePO;
import cc.crrc.manage.pojo.ekb.EkbFaultReasonDTO;
import cc.crrc.manage.pojo.line.StationDTO;
import cc.crrc.manage.websocket.service.LineSocketService;
import com.alibaba.fastjson.JSONArray;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class MtcAutoFaultRecordVO {

    @LogParam(description = "故障代码")
    private String faultCode;//故障代码

    @LogParam(description = "故障名称中文")
    private String faultNameCn;//故障名称中文

    @LogParam(description = "故障等级")
    private Integer faultLevel;//故障等级

    @LogParam(description = "故障等级名称")
    private String faultLevelName;

    @LogParam(description = "故障来源")
    private String source;//故障来源

    @LogParam(description = "故障发生时间")
    private Date startTime;

    @LogParam(description = "故障结束时间")
    private Date endTime;

    @LogParam(description = "故障发生频率")
    private Integer realCounter;

    @LogParam(description = "线路名称")
    private String lineName;//线路名称

    @LogParam(description = "线路id")
    private String lineId;//线路id

    @LogParam(description = "车辆名称中文")
    private String vehicleNameCn;//车辆名称

    @LogParam(description = "车辆名称英文")
    private String vehicleNamEn;//车辆名称英文

    @LogParam(description = "车辆编码")
    private String vehicleCode;//车辆编码

    @LogParam(description = "故障车厢")
    private String location;//故障车厢

    @LogParam(description = "故障系统")
    private String subsystem;//故障系统

    @LogParam(description = "故障系统名称")
    private String label;//故障系统名称

    @LogParam(description = "故障名称英文")
    private String faultNameEn;//故障名称英文

    @LogParam(description = "故障描述")
    private String description;//故障描述

    @LogParam(description = "车辆类型id")
    private Integer vehicleTypeId;//车辆类型id

    @LogParam(description = "故障发生时间end")
    private Date startTimeEnd;//故障实际发生时间end 筛选时间用

    @LogParam(description = "当前站名称")
    private String currentStationName;

    @LogParam(description = "下一站名称")
    private String nextStationName;

    @LogParam(description = "故障编号id")
    @ApiModelProperty(hidden = true)
    private String id;

    @LogParam(description = "车辆编号")
    private String vehicleId;

    @LogParam(description = "引用故障类型表的业务主键")
    private String faultTypeKey;

    @LogParam(description = "引用部件表的主键")
    @ApiModelProperty(hidden = true)
    private String componentId;

    @LogParam(description = "处理状态")
    private String status;

    @LogParam(description = "用户是否确认")
    private Integer confirm;

    @LogParam(description = "速度")
    private Float speed;

    @LogParam(description = "运行里程")
    private Long mileage;

    @LogParam(description = "当前站 站点id")
    private String currentStationId;

    @LogParam(description = "下一站 站点id")
    private String nextStationId;

    @LogParam(description = "手柄级位")
    private Long level;

    @LogParam(description = "司机室激活端")
    @ApiModelProperty(hidden = true)
    private String activeCabin;

    @LogParam(description = "车辆运行模式")
    private String mode;

    @LogParam(description = "修改人")
    private String modifyBy;

    @LogParam(description = "修改时间")
    private Date modifyTime;

    @LogParam(description = "持续时间")
    private String duration;

    @LogParam(description = "构件名字")
    private String componentName;

    @LogParam(description = "故障位置")
    private String structurePosition;

    @LogParam(description = "距当前站距离")
    private Long disToCurstation;
    @LogParam(description = "距下一站距离")
    private Long disToNextstation;

    @LogParam(description = "信号组,包含数据值,json")
    private JSONArray signalGroupValues;

    private EkbFaultReasonDTO ekbFaultReason;

    private EkbFaultMeasurePO ekbFaultMeasure;

    private String faultReason;

    private String fdr;

    private String overhaulSuggestion;

    @LogParam(description = "抑制故障父节点，外键，应用故障类型表的业务主键（fault_type_key）")
    private String suppressedBy;

    @LogParam(description = "正线非正线状态")
    private Boolean runningStatus;

    @LogParam(description = "（故障雪崩功能）雪崩压制状态")
    private Boolean suppressedStatus;

    private int ifSuppressed;

    private Map<String, Object> supressionFault;

    private String faultMode;
    // 模型编码
    private String modelCode;

    //数字车辆三维模型code
    private String threedCode;

    private String vehicleStructureCode;

    public String getFaultCode() {
        return faultCode;
    }

    public void setFaultCode(String faultCode) {
        this.faultCode = faultCode;
    }

    public String getFaultNameCn() {
        return faultNameCn;
    }

    public void setFaultNameCn(String faultNameCn) {
        this.faultNameCn = faultNameCn;
    }

    public Integer getFaultLevel() {
        return faultLevel;
    }

    public void setFaultLevel(Integer faultLevel) {
        this.faultLevel = faultLevel;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getVehicleNameCn() {
        return vehicleNameCn;
    }

    public void setVehicleNameCn(String vehicleNameCn) {
        this.vehicleNameCn = vehicleNameCn;
    }

    public String getVehicleNamEn() {
        return vehicleNamEn;
    }

    public void setVehicleNamEn(String vehicleNamEn) {
        this.vehicleNamEn = vehicleNamEn;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getFaultNameEn() {
        return faultNameEn;
    }

    public void setFaultNameEn(String faultNameEn) {
        this.faultNameEn = faultNameEn;
    }

    public Integer getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(Integer vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public Date getStartTimeEnd() {
        return startTimeEnd;
    }

    public void setStartTimeEnd(Date startTimeEnd) {
        this.startTimeEnd = startTimeEnd;
    }

    public String getCurrentStationName() {
        return currentStationName;
    }

    public void setCurrentStationName(String currentStationName) {
        this.currentStationName = currentStationName;
    }

    public String getNextStationName() {
        return nextStationName;
    }

    public void setNextStationName(String nextStationName) {
        this.nextStationName = nextStationName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getFaultTypeKey() {
        return faultTypeKey;
    }

    public void setFaultTypeKey(String faultTypeKey) {
        this.faultTypeKey = faultTypeKey;
    }

    public String getComponentId() {
        return componentId;
    }

    public void setComponentId(String componentId) {
        this.componentId = componentId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getConfirm() {
        return confirm;
    }

    public void setConfirm(Integer confirm) {
        this.confirm = confirm;
    }

    public Float getSpeed() {
        return speed;
    }

    public void setSpeed(Float speed) {
        this.speed = speed;
    }

    public Long getMileage() {
        return mileage;
    }

    public void setMileage(Long mileage) {
        this.mileage = mileage;
    }

    public String getCurrentStationId() {
        return currentStationId;
    }

    public void setCurrentStationId(String currentStationId) {
        this.currentStationId = currentStationId;
    }

    public String getNextStationId() {
        return nextStationId;
    }

    public void setNextStationId(String nextStationId) {
        this.nextStationId = nextStationId;
    }

    public Long getLevel() {
        return level;
    }

    public void setLevel(Long level) {
        this.level = level;
    }

    public String getActiveCabin() {
        return activeCabin;
    }

    public void setActiveCabin(String activeCabin) {
        this.activeCabin = activeCabin;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    public Integer getRealCounter() {
        return realCounter;
    }

    public void setRealCounter(Integer realCounter) {
        this.realCounter = realCounter;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public EkbFaultReasonDTO getEkbFaultReason() {
        return ekbFaultReason;
    }

    public void setEkbFaultReason(EkbFaultReasonDTO ekbFaultReason) {
        this.ekbFaultReason = ekbFaultReason;
    }

    public EkbFaultMeasurePO getEkbFaultMeasure() {
        return ekbFaultMeasure;
    }

    public void setEkbFaultMeasure(EkbFaultMeasurePO ekbFaultMeasure) {
        this.ekbFaultMeasure = ekbFaultMeasure;
    }

    public String getComponentName() {
        return componentName;
    }

    public void setComponentName(String componentName) {
        this.componentName = componentName;
    }

    public String getStructurePosition() {
        return structurePosition;
    }

    public void setStructurePosition(String structurePosition) {
        this.structurePosition = structurePosition;
    }

    public String getFaultLevelName() {
        return faultLevelName;
    }

    public void setFaultLevelName(String faultLevelName) {
        this.faultLevelName = faultLevelName;
    }

    public JSONArray getSignalGroupValues() {
        return signalGroupValues;
    }

    public void setSignalGroupValues(JSONArray signalGroupValues) {
        this.signalGroupValues = signalGroupValues;
    }

    public String getSuppressedBy() {
        return suppressedBy;
    }

    public void setSuppressedBy(String suppressedBy) {
        this.suppressedBy = suppressedBy;
    }

    public Boolean getRunningStatus() {
        return runningStatus;
    }

    public void setRunningStatus(Boolean runningStatus) {
        this.runningStatus = runningStatus;
    }

    public Boolean getSuppressedStatus() {
        return suppressedStatus;
    }

    public String getFaultReason() {
        return faultReason;
    }

    public void setFaultReason(String faultReason) {
        this.faultReason = faultReason;
    }

    public String getFdr() {
        return fdr;
    }

    public void setFdr(String fdr) {
        this.fdr = fdr;
    }

    public String getOverhaulSuggestion() {
        return overhaulSuggestion;
    }

    public void setOverhaulSuggestion(String overhaulSuggestion) {
        this.overhaulSuggestion = overhaulSuggestion;
    }

    public void setSuppressedStatus(Boolean suppressedStatus) {
        this.suppressedStatus = suppressedStatus;
    }

    public int getIfSuppressed() {
        return ifSuppressed;
    }

    public void setIfSuppressed(int ifSuppressed) {
        this.ifSuppressed = ifSuppressed;
    }

    public Map<String, Object> getSupressionFault() {
        return supressionFault;
    }

    public void setSupressionFault(Map<String, Object> supressionFault) {
        this.supressionFault = supressionFault;
    }

    public String getFaultMode() {
        return faultMode;
    }

    public void setFaultMode(String faultMode) {
        this.faultMode = faultMode;
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public Long getDisToCurstation() {
        return disToCurstation;
    }

    public void setDisToCurstation(Long disToCurstation) {
        this.disToCurstation = disToCurstation;
    }

    public Long getDisToNextstation() {
        return disToNextstation;
    }

    public void setDisToNextstation(Long disToNextstation) {
        this.disToNextstation = disToNextstation;
    }

    public String getThreedCode() {
        return threedCode;
    }

    public void setThreedCode(String threedCode) {
        this.threedCode = threedCode;
    }

    public String getVehicleStructureCode() {
        return vehicleStructureCode;
    }

    public void setVehicleStructureCode(String vehicleStructureCode) {
        this.vehicleStructureCode = vehicleStructureCode;
    }

}
