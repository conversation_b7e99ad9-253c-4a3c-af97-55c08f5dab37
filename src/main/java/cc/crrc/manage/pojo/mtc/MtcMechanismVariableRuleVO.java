package cc.crrc.manage.pojo.mtc;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Update;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @ClassName MtcMechanismVariableRuleVO
 * @Description TODO
 * @<PERSON> <PERSON>
 * @Date 2021/1/4 11:32
 **/
public class MtcMechanismVariableRuleVO {

    @LogParam(description = "全局变量主键")
    @NotNull(message = "变量ID不正确", groups = {Update.class})
    private String id;
    @LogParam(description = "引用车辆型号表的主键（id）")
    private String vehicleTypeId;
    @LogParam(description = "所属车厢")
    private String location;
    @LogParam(description = "所属系统")
    private String subsystem;
    @LogParam(description = "变量名称")
    private String name;
    @LogParam(description = "使能标志位")
    private Boolean enable;
    @LogParam(description = "变量描述")
    private String description;
    @LogParam(description = "图片url")
    private String imageUrl;
    private String subSystemName;
    private String vehicleTypeName;
    private String lineId;


    public String getSubSystemName() {
        return subSystemName;
    }

    public void setSubSystemName(String subSystemName) {
        this.subSystemName = subSystemName;
    }

    public String getVehicleTypeName() {
        return vehicleTypeName;
    }

    public void setVehicleTypeName(String vehicleTypeName) {
        this.vehicleTypeName = vehicleTypeName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
}
