package cc.crrc.manage.pojo.mtc;

import cc.crrc.manage.common.annotation.LogParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/06/12/ 10:28
 */
public class AlarmWarningInfo {
    @ApiModelProperty(hidden = true)
    private String id;
    @ApiModelProperty(value = "车辆id")
    private String vehicleId;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(example = "2018-01-01 12:00:00",value = "故障发生时间")
    @LogParam(description = "故障发生时间")
    private Date startTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(example = "2018-01-01 12:00:00",value = "故障解除时间")
    @LogParam(description = "故障结束时间")
    private Date endTime;
    @LogParam(description = "故障来源")
    private String faultSource;
    @ApiModelProperty(value = "故障中文名称")
    @LogParam(description = "故障中文名称")
    private String faultNameCn;
    @LogParam(description = "正线状态")
    private Boolean runningStatus;
    @ApiModelProperty(value = "故障代码")
    @LogParam(description = "故障代码")
    private String faultCode;
    @ApiModelProperty(value = "故障级别")
    @LogParam(description = "故障级别")
    private Integer faultLevel;
    @LogParam(description = "车辆编号")
    private String vehicleCode;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getFaultSource() {
        return faultSource;
    }

    public void setFaultSource(String faultSource) {
        this.faultSource = faultSource;
    }

    public String getFaultNameCn() {
        return faultNameCn;
    }

    public void setFaultNameCn(String faultNameCn) {
        this.faultNameCn = faultNameCn;
    }

    public Boolean getRunningStatus() {
        return runningStatus;
    }

    public void setRunningStatus(Boolean runningStatus) {
        this.runningStatus = runningStatus;
    }

    public String getFaultCode() {
        return faultCode;
    }

    public void setFaultCode(String faultCode) {
        this.faultCode = faultCode;
    }

    public Integer getFaultLevel() {
        return faultLevel;
    }

    public void setFaultLevel(Integer faultLevel) {
        this.faultLevel = faultLevel;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }
}
