package cc.crrc.manage.pojo.mtc;

import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.pojo.PageVO;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @ClassName MtcMechanismVariableRuleDTO
 * @Description TODO
 * @<PERSON> <PERSON>
 * @Date 2021/1/4 11:27
 **/


public class MtcMechanismVariableRuleDTO extends PageVO {

        @ApiModelProperty(value = "全局变量主键")
        @NotNull(message = "变量ID不正确", groups = {Update.class})
        private String id;
        @ApiModelProperty(value = "所属车厢", position = 2)
        private String location;
        @ApiModelProperty(value = "所属系统", position = 3)
        private String subsystem;
        @ApiModelProperty(value = "引用车辆型号表的主键（id）", position = 1)
        private String vehicleTypeId;
        @ApiModelProperty(value = "变量名称", position = 4)
        private String name;
        @ApiModelProperty(value = "使能标志位", position = 5)
        private Boolean enable;
        @ApiModelProperty(value = "变量描述", position = 6)
        private String description;
        @ApiModelProperty(value = "线路id", position = 7)
        private String  lineId;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getSubsystem() {
            return subsystem;
        }

        public void setSubsystem(String subsystem) {
            this.subsystem = subsystem;
        }

        public String getLocation() {
            return location;
        }

        public void setLocation(String location) {
            this.location = location;
        }

        public String getVehicleTypeId() {
            return vehicleTypeId;
        }

        public void setVehicleTypeId(String vehicleTypeId) {
            this.vehicleTypeId = vehicleTypeId;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Boolean getEnable() {
            return enable;
        }

        public void setEnable(Boolean enable) {
            this.enable = enable;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
}
