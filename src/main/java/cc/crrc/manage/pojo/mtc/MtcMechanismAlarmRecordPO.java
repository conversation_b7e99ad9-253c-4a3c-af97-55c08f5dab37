package cc.crrc.manage.pojo.mtc;


import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.PageVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

public class MtcMechanismAlarmRecordPO extends PageVO {

    @LogParam(description = "机理故障主键")
    @ApiModelProperty(hidden = true)
    private String id;

    @LogParam(description = "引用车辆表的主键（id）")
    private String vehicleId;

    @LogParam(description = "引用预警规则表的主键（id）")
    @ApiModelProperty(hidden = true)
    private String alarmRuleId;

    @LogParam(description = "机理故障快照，保存故障时刻的参数")
    @ApiModelProperty(hidden = true)
    private String alarmSnapshot;

    @LogParam(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(example = "2018-01-01 12:00:00")
    private Date startTime;

    @LogParam(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(example = "2018-01-01 12:00:00",hidden = true)
    private Date endTime;

    @LogParam(description = "机理故障状态")
    @ApiModelProperty(hidden = true)
    private String status;

    @LogParam(description = "修改时间")
    @ApiModelProperty(hidden = true)
    private Date modifyTime;

    @ApiModelProperty(hidden = true)
    @LogParam(description = "修改用户编号")
    private String modifyBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getAlarmRuleId() {
        return alarmRuleId;
    }

    public void setAlarmRuleId(String alarmRuleId) {
        this.alarmRuleId = alarmRuleId;
    }

    public String getAlarmSnapshot() {
        return alarmSnapshot;
    }

    public void setAlarmSnapshot(String alarmSnapshot) {
        this.alarmSnapshot = alarmSnapshot;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }
}
