package cc.crrc.manage.pojo.mtc;

import cc.crrc.manage.common.annotation.LogParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @FileName MtcAlarmWarningVO
 * @Description 告警和预警VO类
 * <AUTHOR> yuxi
 * @Date 2020/6/2 15:46
 **/
public class MtcAlarmWarningVO extends MtcAlarmWarningPO {
    @LogParam(description = "手柄级位（牵引）")
    private Float tracCmd;
    @LogParam(description = "手柄级位（值）")
    private Float effDmd;
    @LogParam(description = "司机室激活端A1")
    private Integer keyA1;
    @LogParam(description = "司机室激活端A2")
    private Integer keyA2;
    @LogParam(description = "终点站id")
    private Long endStationId;
    @LogParam(description = "构型位置")
    private String structureCode;
    //故障原因
    private String faultReason;
    //正线建议
    private String fdr;
    //检修建议
    private String overhaulSuggestion;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(example = "2018-01-01 12:00:00")
    @LogParam(description = "故障实际发生时间end 筛选时间用")
    private Date startTimeEnd;
    @JsonIgnore
    @LogParam(description = "实时监控中在线车辆的vehicleCode集合")
    private List<String> onlineVehicles;

    @ApiModelProperty(hidden = true)
    @LogParam(description = "解除状态")
    private Integer endStatus;

    private String faultMode;

    public String getFaultMode() {
        return faultMode;
    }

    public void setFaultMode(String faultMode) {
        this.faultMode = faultMode;
    }

    public List<String> getOnlineVehicles() {
        return onlineVehicles;
    }

    public void setOnlineVehicles(List<String> onlineVehicles) {
        this.onlineVehicles = onlineVehicles;
    }

    public Float getTracCmd() {
        return tracCmd;
    }

    public void setTracCmd(Float tracCmd) {
        this.tracCmd = tracCmd;
    }

    public Float getEffDmd() {
        return effDmd;
    }

    public void setEffDmd(Float effDmd) {
        this.effDmd = effDmd;
    }

    public Integer getKeyA1() {
        return keyA1;
    }

    public void setKeyA1(Integer keyA1) {
        this.keyA1 = keyA1;
    }

    public Integer getKeyA2() {
        return keyA2;
    }

    public void setKeyA2(Integer keyA2) {
        this.keyA2 = keyA2;
    }

    public Long getEndStationId() {
        return endStationId;
    }

    public void setEndStationId(Long endStationId) {
        this.endStationId = endStationId;
    }

    public String getStructureCode() {
        return structureCode;
    }

    public void setStructureCode(String structureCode) {
        this.structureCode = structureCode;
    }

    public Date getStartTimeEnd() {
        return startTimeEnd;
    }

    public void setStartTimeEnd(Date startTimeEnd) {
        this.startTimeEnd = startTimeEnd;
    }

    public String getFaultReason() {
        return faultReason;
    }

    public void setFaultReason(String faultReason) {
        this.faultReason = faultReason;
    }

    public String getFdr() {
        return fdr;
    }

    public void setFdr(String fdr) {
        this.fdr = fdr;
    }

    public String getOverhaulSuggestion() {
        return overhaulSuggestion;
    }

    public void setOverhaulSuggestion(String overhaulSuggestion) {
        this.overhaulSuggestion = overhaulSuggestion;
    }

    public Integer getEndStatus() {
        return endStatus;
    }

    public void setEndStatus(Integer endStatus) {
        this.endStatus = endStatus;
    }

    public MtcAlarmWarningVO build(int pageNum, int pageSize, String faultSource, String vehicleId, String faultNameCn) {
        this.setPageNumber(pageNum);
        this.setPageSize(pageSize);
        this.setVehicleId(faultSource);
        this.setVehicleId(vehicleId);
        this.setFaultNameCn(faultNameCn);
        return this;
    }


}
