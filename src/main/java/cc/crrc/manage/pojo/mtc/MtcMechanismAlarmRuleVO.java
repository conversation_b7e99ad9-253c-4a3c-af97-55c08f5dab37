package cc.crrc.manage.pojo.mtc;


import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Update;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @Author: Li <PERSON>g
 * @Date: 2019-12-23
 */
public class MtcMechanismAlarmRuleVO  {
    @LogParam(description = "机理规则主键")
    @NotNull(message = "机理ID不正确", groups = {Update.class})
    private String id;
    @LogParam(description = "引用车辆型号表的主键（id）")
    private String vehicleTypeId;
    @LogParam(description = "引用故障类型表的业务主键（fault_type_key）")
    private String faultTypeKey;
    @LogParam(description = "所属车厢")
    private String location;
    @LogParam(description = "所属系统id")
    private String subsystem;
    @LogParam(description = "规则名称")
    private String name;
    @LogParam(description = "使能标志位")
    private Boolean enable;
    @LogParam(description = "规则描述")
    private String description;
    @LogParam(description = "图片url")
    private String imageUrl;
    private String subSystemName;
    private String vehicleTypeName;
    private String faultName;
    private String lineId;

    private String threedCode;

    @ApiModelProperty(value = "规则内容，JSON格式")
    private String content;

    private String signalCycle;

    //2022-04-25 新需求 增加机理规则测试状态
    private Boolean testStatus;//机理规则测试状态

    //2022-06-06 新需求 增加机理规则测试状态
    private Boolean encryptionStatus;

    @LogParam(description = "规则配置是否弹窗")
    private  String puwStatue;

    public Boolean getEncryptionStatus() {
        return encryptionStatus;
    }

    public void setEncryptionStatus(Boolean encryptionStatus) {
        this.encryptionStatus = encryptionStatus;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getFaultName() {
        return faultName;
    }

    public void setFaultName(String faultName) {
        this.faultName = faultName;
    }

    public String getSubSystemName() {
        return subSystemName;
    }

    public void setSubSystemName(String subSystemName) {
        this.subSystemName = subSystemName;
    }

    public String getVehicleTypeName() {
        return vehicleTypeName;
    }

    public void setVehicleTypeName(String vehicleTypeName) {
        this.vehicleTypeName = vehicleTypeName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getFaultTypeKey() {
        return faultTypeKey;
    }

    public void setFaultTypeKey(String faultTypeKey) {
        this.faultTypeKey = faultTypeKey;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getThreedCode() {
        return threedCode;
    }

    public void setThreedCode(String threedCode) {
        this.threedCode = threedCode;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getSignalCycle() {
        return signalCycle;
    }

    public void setSignalCycle(String signalCycle) {
        this.signalCycle = signalCycle;
    }

    public Boolean getTestStatus() {
        return testStatus;
    }

    public void setTestStatus(Boolean testStatus) {
        this.testStatus = testStatus;
    }

    public String getPuwStatue() {
        return puwStatue;
    }

    public void setPuwStatue(String puwStatue) {
        this.puwStatue = puwStatue;
    }
}
