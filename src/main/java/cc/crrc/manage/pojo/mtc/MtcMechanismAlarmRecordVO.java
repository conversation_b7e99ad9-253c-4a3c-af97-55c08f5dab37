package cc.crrc.manage.pojo.mtc;

import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

public class MtcMechanismAlarmRecordVO {
    @LogParam(description = "机理故障主键")
    @ApiModelProperty(hidden = true)
    private String id;

    @LogParam(description = "引用车辆表的主键（id）")
    private String vehicleId;

    @LogParam(description = "引用预警规则表的主键（id）")
    @ApiModelProperty(hidden = true)
    private String alarmRuleId;

    @LogParam(description = "机理故障快照，保存故障时刻的参数")
    private String alarmSnapshot;

    @LogParam(description = "开始时间")
    private Date startTime;

    @LogParam(description = "结束时间")
    private Date endTime;

    @LogParam(description = "机理故障状态")
    private String status;

    @LogParam(description = "修改时间")
    private Date modifyTime;

    @LogParam(description = "修改用户编号")
    private String modifyBy;

    @LogParam(description = "线路名称")
    private String lineName;//线路名称

    @LogParam(description = "线路id")
    private String lineId;//线路id

    @LogParam(description = "车辆名称中文")
    private String vehicleNameCn;//车辆名称

    @LogParam(description = "车辆名称英文")
    private String vehicleNamen;//车辆名称英文

    @LogParam(description = "车辆编码")
    private String vehicleCode;//车辆编码

    @LogParam(description = "故障车厢")
    private String location;//故障车厢

    @LogParam(description = "故障系统")
    private String subsystem;//故障系统

    @LogParam(description = "故障系统名称")
    private String label;//故障系统名称

    @LogParam(description = "故障代码")
    private String faultCode;//故障代码

    @LogParam(description = "故障名称中文")
    private String faultNameCn;//故障名称中文

    @LogParam(description = "故障名称英文")
    private String faultNameEn;//故障名称英文

    @LogParam(description = "故障等级")
    private Integer faultLevel;//故障等级

    @LogParam(description = "车辆类型id")
    private Integer vehicleTypeId;//车辆类型id

    @LogParam(description = "当前站名称")
    private String currentStationName;

    @LogParam(description = "下一站名称")
    private String nextStationName;
    @LogParam(description = "持续时间")
    private String duration;
    @LogParam(description = "机理故障规则名称")
    private String mechanismAlarmRuleName;
    @LogParam(description = "机理故障规则内容")
    private String mechanismAlarmRuleContent;
    @LogParam(description = "故障等级中文名称")
    private String faultLevelName;//故障等级中文名称
    @LogParam(description = "测试机理履历")
    private Boolean testStatus;
    @LogParam(description = "是否关闭弹窗展示")
    private Boolean closeStatue;

    @LogParam(description = "故障来源")
    private String faultSource;

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getAlarmRuleId() {
        return alarmRuleId;
    }

    public void setAlarmRuleId(String alarmRuleId) {
        this.alarmRuleId = alarmRuleId;
    }

    public String getAlarmSnapshot() {
        return alarmSnapshot;
    }

    public void setAlarmSnapshot(String alarmSnapshot) {
        this.alarmSnapshot = alarmSnapshot;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getVehicleNameCn() {
        return vehicleNameCn;
    }

    public void setVehicleNameCn(String vehicleNameCn) {
        this.vehicleNameCn = vehicleNameCn;
    }

    public String getVehicleNamen() {
        return vehicleNamen;
    }

    public void setVehicleNamen(String vehicleNamen) {
        this.vehicleNamen = vehicleNamen;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getFaultCode() {
        return faultCode;
    }

    public void setFaultCode(String faultCode) {
        this.faultCode = faultCode;
    }

    public String getFaultNameCn() {
        return faultNameCn;
    }

    public void setFaultNameCn(String faultNameCn) {
        this.faultNameCn = faultNameCn;
    }

    public String getFaultNameEn() {
        return faultNameEn;
    }

    public void setFaultNameEn(String faultNameEn) {
        this.faultNameEn = faultNameEn;
    }

    public Integer getFaultLevel() {
        return faultLevel;
    }

    public void setFaultLevel(Integer faultLevel) {
        this.faultLevel = faultLevel;
    }

    public Integer getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(Integer vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getCurrentStationName() {
        return currentStationName;
    }

    public void setCurrentStationName(String currentStationName) {
        this.currentStationName = currentStationName;
    }

    public String getNextStationName() {
        return nextStationName;
    }

    public void setNextStationName(String nextStationName) {
        this.nextStationName = nextStationName;
    }

    public String getMechanismAlarmRuleName() {
        return mechanismAlarmRuleName;
    }

    public void setMechanismAlarmRuleName(String mechanismAlarmRuleName) {
        this.mechanismAlarmRuleName = mechanismAlarmRuleName;
    }

    public String getMechanismAlarmRuleContent() {
        return mechanismAlarmRuleContent;
    }

    public void setMechanismAlarmRuleContent(String mechanismAlarmRuleContent) {
        this.mechanismAlarmRuleContent = mechanismAlarmRuleContent;
    }

    public String getFaultLevelName() {
        return faultLevelName;
    }

    public void setFaultLevelName(String faultLevelName) {
        this.faultLevelName = faultLevelName;
    }

    public Boolean getTestStatus() {
        return testStatus;
    }

    public void setTestStatus(Boolean testStatus) {
        this.testStatus = testStatus;
    }

    public Boolean getCloseStatue() {
        return closeStatue;
    }

    public void setCloseStatue(Boolean closeStatue) {
        this.closeStatue = closeStatue;
    }

    public String getFaultSource() {
        return faultSource;
    }

    public void setFaultSource(String faultSource) {
        this.faultSource = faultSource;
    }
}
