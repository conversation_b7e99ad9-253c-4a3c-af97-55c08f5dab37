package cc.crrc.manage.pojo.mtc;

import cc.crrc.manage.common.annotation.group.Update;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 用来承载机理故障编辑页面信息
 * @Author: <PERSON>
 * @Date: 2019-12-23
 */
public class MtcMechanismAlarmRuleEditDTO{
    @ApiModelProperty(value = "机理规则主键")
    @NotNull(message = "机理ID不正确", groups = {Update.class})
    private String id;
    @ApiModelProperty(value = "机理图片base64")
    private String base64Str;
    @ApiModelProperty(value = "规则内容，JSON格式")
    private String content;
    @ApiModelProperty(value = "规则内容，加密状态")
    private Boolean encryptionStatus;

    public Boolean getEncryptionStatus() {
        return encryptionStatus;
    }

    public void setEncryptionStatus(Boolean encryptionStatus) {
        this.encryptionStatus = encryptionStatus;
    }

    public String getBase64Str() {
        return base64Str;
    }

    public void setBase64Str(String base64Str) {
        this.base64Str = base64Str;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
