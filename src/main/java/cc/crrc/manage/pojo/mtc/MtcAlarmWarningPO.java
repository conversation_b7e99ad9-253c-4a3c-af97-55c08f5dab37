package cc.crrc.manage.pojo.mtc;

import cc.crrc.manage.cache.CacheUtils;
import cc.crrc.manage.cache.dict.StationCache;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.PageVO;
import cc.crrc.manage.pojo.line.StationDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

import static cc.crrc.manage.websocket.service.LineSocketService.DRIVE_MODE_MAP;

/**
 * @FileName MtcAlarmWarningPO
 * @Description 告警预警实体类
 * <AUTHOR> yuxi
 * @Date 2020/6/5 13:32
 **/
public class MtcAlarmWarningPO extends PageVO {
    @ApiModelProperty(hidden = true)
    private String id;
    @ApiModelProperty(value = "车辆id")
    private String vehicleId;
    @ApiModelProperty(value = "故障主键")
    private String faultTypeKey;
    @LogParam(description = "部件id")
    private String componentId;
    @LogParam(description = "机理规则id")
    private String alarmRuleId;
    @LogParam(description = "机理故障快照")
    private String alarmSnapshot;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(example = "2018-01-01 12:00:00",value = "故障发生时间")
    @LogParam(description = "故障发生时间")
    private Date startTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(example = "2018-01-01 12:00:00",value = "故障解除时间")
    @LogParam(description = "故障结束时间")
    private Date endTime;
    @LogParam(description = "故障发生时速度")
    private Float speed;
    @LogParam(description = "故障发生时里程")
    private Long mileage;
    @LogParam(description = "当前站id")
    private String currentStationId;
    @LogParam(description = "下一站id")
    private String nextStationId;
    @LogParam(description = "手柄级位")
    private Float level;
    @LogParam(description = "司机室激活端")
    private String activeCabin;
    @LogParam(description = "车辆运行模式")
    private String mode;
    @LogParam(description = "抑制故障父节点")
    private String suppressedBy;
    @LogParam(description = "处理状态")
    private String status;
    @LogParam(description = "确认状态")
    private Integer confirm;
    @LogParam(description = "正线状态")
    private Boolean runningStatus;
    @LogParam(description = "雪崩抑制状态")
    private Boolean suppressedStatus;
    @LogParam(description = "故障来源")
    private String faultSource;
    private Date createTime;
    private Date modifyTime;
    private String modifyBy;
    @LogParam(description = "线路名称")
    private String lineName;
    @LogParam(description = "车型名称")
    private String vehicleTypeName;
    @ApiModelProperty(value = "线路Id")
    @LogParam(description = "线路Id")
    private String lineId;
    @ApiModelProperty(value = "故障车厢")
    @LogParam(description = "故障车厢")
    private String location;
    @ApiModelProperty(value = "车辆类型id")
    @LogParam(description = "车辆类型id")
    private String vehicleTypeId;
    @ApiModelProperty(value = "构型编码")
    @LogParam(description = "构型编码")
    private String vehicleStructureCode;
    @LogParam(description = "部件类型主键")
    private String componentTypeId;
    @ApiModelProperty(value = "故障子系统")
    @LogParam(description = "故障系统子系统")
    private String subsystem;
    @ApiModelProperty(value = "故障中文名称")
    @LogParam(description = "故障中文名称")
    private String faultNameCn;
    @ApiModelProperty(value = "故障英文名称")
    @LogParam(description = "故障英文名称")
    private String faultNameEn;
    @ApiModelProperty(value = "故障代码")
    @LogParam(description = "故障代码")
    private String faultCode;
    @ApiModelProperty(value = "故障级别")
    @LogParam(description = "故障级别")
    private Integer faultLevel;
    @LogParam(description = "JSON字符串(故障详情配置信号)")
    private String params;
    @LogParam(description = "当前站名称")
    private String currentStationName;
    @LogParam(description = "下一站名称")
    private String nextStationName;
    @LogParam(description = "故障系统名称")
    private String label;
    @LogParam(description = "车辆编号")
    private String vehicleCode;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getFaultTypeKey() {
        return faultTypeKey;
    }

    public void setFaultTypeKey(String faultTypeKey) {
        this.faultTypeKey = faultTypeKey;
    }

    public String getComponentId() {
        return componentId;
    }

    public void setComponentId(String componentId) {
        this.componentId = componentId;
    }

    public String getAlarmRuleId() {
        return alarmRuleId;
    }

    public void setAlarmRuleId(String alarmRuleId) {
        this.alarmRuleId = alarmRuleId;
    }

    public String getAlarmSnapshot() {
        return alarmSnapshot;
    }

    public void setAlarmSnapshot(String alarmSnapshot) {
        this.alarmSnapshot = alarmSnapshot;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Float getSpeed() {
        return speed;
    }

    public void setSpeed(Float speed) {
        this.speed = speed;
    }

    public Long getMileage() {
        return mileage;
    }

    public void setMileage(Long mileage) {
        this.mileage = mileage;
    }

    public String getCurrentStationId() {
        return currentStationId;
    }

    public void setCurrentStationId(String currentStationId) {
        this.currentStationId = currentStationId;
    }

    public String getNextStationId() {
        return nextStationId;
    }

    public void setNextStationId(String nextStationId) {
        this.nextStationId = nextStationId;
    }

    public Float getLevel() {
        return level;
    }

    public void setLevel(Float level) {
        this.level = level;
    }

    public String getActiveCabin() {
        return activeCabin;
    }

    public void setActiveCabin(String activeCabin) {
        this.activeCabin = activeCabin;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getSuppressedBy() {
        return suppressedBy;
    }

    public void setSuppressedBy(String suppressedBy) {
        this.suppressedBy = suppressedBy;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getConfirm() {
        return confirm;
    }

    public void setConfirm(Integer confirm) {
        this.confirm = confirm;
    }

    public Boolean getRunningStatus() {
        return runningStatus;
    }

    public void setRunningStatus(Boolean runningStatus) {
        this.runningStatus = runningStatus;
    }

    public Boolean getSuppressedStatus() {
        return suppressedStatus;
    }

    public void setSuppressedStatus(Boolean suppressedStatus) {
        this.suppressedStatus = suppressedStatus;
    }

    public String getFaultSource() {
        return faultSource;
    }

    public void setFaultSource(String faultSource) {
        this.faultSource = faultSource;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }
    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getVehicleTypeName() {
        return vehicleTypeName;
    }

    public void setVehicleTypeName(String vehicleTypeName) {
        this.vehicleTypeName = vehicleTypeName;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getVehicleStructureCode() {
        return vehicleStructureCode;
    }

    public void setVehicleStructureCode(String vehicleStructureCode) {
        this.vehicleStructureCode = vehicleStructureCode;
    }

    public String getComponentTypeId() {
        return componentTypeId;
    }

    public void setComponentTypeId(String componentTypeId) {
        this.componentTypeId = componentTypeId;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public String getFaultNameCn() {
        return faultNameCn;
    }

    public void setFaultNameCn(String faultNameCn) {
        this.faultNameCn = faultNameCn;
    }

    public String getFaultNameEn() {
        return faultNameEn;
    }

    public void setFaultNameEn(String faultNameEn) {
        this.faultNameEn = faultNameEn;
    }

    public String getFaultCode() {
        return faultCode;
    }

    public void setFaultCode(String faultCode) {
        this.faultCode = faultCode;
    }

    public Integer getFaultLevel() {
        return faultLevel;
    }

    public void setFaultLevel(Integer faultLevel) {
        this.faultLevel = faultLevel;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public String getCurrentStationName() {
        return currentStationName;
    }

    public void setCurrentStationName(String currentStationName) {
        this.currentStationName = currentStationName;
    }

    public String getNextStationName() {
        return nextStationName;
    }

    public void setNextStationName(String nextStationName) {
        this.nextStationName = nextStationName;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }


    /**
     * 将paramsVO对象值赋值到MtcAutoFaultRecordPO
     * @param paramsPO
     * @return
     */
    public MtcAlarmWarningPO param(ParamsPO paramsPO) {
        if (paramsPO != null) {
            this.setSpeed(paramsPO.getSpeed());
            this.setMileage(paramsPO.getMileage());
            this.setMode(DRIVE_MODE_MAP.get(paramsPO.getDriveMode()));
            Integer nextStaId = paramsPO.getNextStationId() == null ? 0 : paramsPO.getNextStationId();
            Integer endStaId = paramsPO.getEndStationId() == null ? 0 : paramsPO.getEndStationId();
            this.setCurrentStationName(stationHandler(paramsPO, StationCache.MAX_STATION_CODE.equals(endStaId) ? nextStaId - 1 : nextStaId + 1));
            this.setNextStationName(stationHandler(paramsPO, nextStaId));
            this.setActiveCabin("A1");
            if (paramsPO.getCabActOne() != null && !paramsPO.getCabActOne().equals(paramsPO.getCabActTwo()) && "1".equals(paramsPO.getCabActTwo())){
                this.setActiveCabin("A2");
            }
        }
        return this;
    }

    private String stationHandler(ParamsPO paramsPO,Integer stationId) {
        List<StationDTO> list = CacheUtils.getValue(StationCache.class, "up_" + CacheUtils.METRO_LINE_ID);
        return list.stream()
                .filter(i -> stationId.equals(i.getStationCode()))
                .map(StationDTO::getName)
                .findFirst()
                .orElse("--");
    }


}
