package cc.crrc.manage.pojo.mtc;

import cc.crrc.manage.common.utils.MechanismUtils.pojo.BaseRulePO;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

public class MtcMechanismAlarmRulePO implements BaseRulePO {
	private String id;
	private String vehicleTypeId;
	private String faultTypeKey;
	private String name;
	private String content;
	private Boolean enable;
	private String imageFile;
	private String description;
	private String modifyBy;
	private Date modifyTime;
	private String createBy;
	private Date createTime;
	//用于kafaka发送信息判断时候删除 "1"为删除 "0"为正常
	private int del;
	private String lineId; //增加线路信息
	private String signalCycle;//信号周期

	//2022-04-25 新需求 增加机理规则测试状态
	private Boolean testStatus;//机理规则测试状态
	//2022-06-06 新需求 增加机理规则测试状态
	private Boolean encryptionStatus;
	private String puwStatue;

	public Boolean getEncryptionStatus() {
		return encryptionStatus;
	}

	public void setEncryptionStatus(Boolean encryptionStatus) {
		this.encryptionStatus = encryptionStatus;
	}


	@Override
	public String getRuleId() {
		return id.toString();
	}

	@Override
	public String getRuleJsonString() {
		return content;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}


	public String getVehicleTypeId() {
		return vehicleTypeId;
	}

	public void setVehicleTypeId(String vehicleTypeId) {
		this.vehicleTypeId = vehicleTypeId;
	}


	public String getFaultTypeKey() {
		return faultTypeKey;
	}

	public void setFaultTypeKey(String faultTypeKey) {
		this.faultTypeKey = faultTypeKey;
	}


	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}


	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}


	public Boolean getEnable() {
		return enable;
	}

	public void setEnable(Boolean enable) {
		this.enable = enable;
	}


	public String getImageFile() {
		return imageFile;
	}

	public void setImageFile(String imageFile) {
		this.imageFile = imageFile;
	}


	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}


	public String getModifyBy() {
		return modifyBy;
	}

	public void setModifyBy(String modifyBy) {
		this.modifyBy = modifyBy;
	}


	public Date getModifyTime() {
		return modifyTime;
	}

	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}


	public String getCreateBy() {
		return createBy;
	}

	public void setCreateBy(String createBy) {
		this.createBy = createBy;
	}


	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public int getDel() {
		return del;
	}

	public void setDel(int del) {
		this.del = del;
	}

	public String getLineId() {
		return lineId;
	}

	public void setLineId(String lineId) {
		this.lineId = lineId;
	}

	public String getSignalCycle() {
		return signalCycle;
	}

	public void setSignalCycle(String signalCycle) {
		this.signalCycle = signalCycle;
	}

	public Boolean getTestStatus() {
		return testStatus;
	}

	public void setTestStatus(Boolean testStatus) {
		this.testStatus = testStatus;
	}

	public String getPuwStatue() {
		return puwStatue;
	}

	public void setPuwStatue(String puwStatue) {
		this.puwStatue = puwStatue;
	}
}
