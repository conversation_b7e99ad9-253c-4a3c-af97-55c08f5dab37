package cc.crrc.manage.pojo.mtc;

import cc.crrc.manage.common.utils.MechanismUtils.pojo.BaseRulePO;

import java.util.Date;

/**
 * @ClassName MtcMechanismVariableRulePO
 * @Description
 * @<PERSON> <PERSON>
 * @Date 2021/1/4
 **/
public class MtcMechanismVariableRulePO implements BaseRulePO {
	private String id;
	private String vehicleTypeId;
	private String name;
	private String content;
	private Boolean enable;
	private String imageFile;
	private String description;
	private String modifyBy;
	private Date modifyTime;
	private String createBy;
	private Date createTime;
	private String location;
	private String subsystem;
	//用于kafaka发送信息判断时候删除 "1"为删除 "0"为正常
	private int del;
	private String lineId;
    
    @Override
    public String getRuleId() {
        return id.toString();
    }
    
    @Override
    public String getRuleJsonString() {
        return content;
    }

	public String getId() {
		return id;
	}
	
	public void setId(String id) {
		this.id = id;
	}

	public String getVehicleTypeId() {
		return vehicleTypeId;
	}

	public void setVehicleTypeId(String vehicleTypeId) {
		this.vehicleTypeId = vehicleTypeId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public Boolean getEnable() {
		return enable;
	}

	public void setEnable(Boolean enable) {
		this.enable = enable;
	}

	public String getImageFile() {
		return imageFile;
	}

	public void setImageFile(String imageFile) {
		this.imageFile = imageFile;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getModifyBy() {
		return modifyBy;
	}

	public void setModifyBy(String modifyBy) {
		this.modifyBy = modifyBy;
	}

	public Date getModifyTime() {
		return modifyTime;
	}

	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}

	public String getCreateBy() {
		return createBy;
	}

	public void setCreateBy(String createBy) {
		this.createBy = createBy;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public int getDel() {
		return del;
	}

	public void setDel(int del) {
		this.del = del;
	}

	public String getLocation() {
		return location;
	}

	public void setLocation(String location) {
		this.location = location;
	}

	public String getSubsystem() {
		return subsystem;
	}

	public void setSubsystem(String subsystem) {
		this.subsystem = subsystem;
	}

	public String getLineId() {
		return lineId;
	}

	public void setLineId(String lineId) {
		this.lineId = lineId;
	}
}
