package cc.crrc.manage.pojo.mtc;

import cc.crrc.manage.common.annotation.LogParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

public class MtcAutoFaultRecordDTO extends MtcAutoFaultRecordPO{

    @LogParam(description = "线路名称")
    @ApiModelProperty(hidden = true)
    private String lineName;//线路名称

    @LogParam(description = "线路id")
    private String lineId;//线路id

    @LogParam(description = "车辆名称中文")
    @ApiModelProperty(hidden = true)
    private String vehicleNameCn;//车辆名称

    @LogParam(description = "车辆名称英文")
    @ApiModelProperty(hidden = true)
    private String vehicleNamen;//车辆名称英文

    @LogParam(description = "车辆编码")
    private String vehicleCode;//车辆编码

    @LogParam(description = "故障车厢")
    private String location;//故障车厢

    @LogParam(description = "故障系统")
    private String subsystem;//故障系统

    @LogParam(description = "故障系统名称")
    @ApiModelProperty(hidden = true)
    private String label;//故障系统名称


    @LogParam(description = "故障代码")
    private String faultCode;//故障代码

    @LogParam(description = "故障名称中文")
    private String faultNameCn;//故障名称中文

    @LogParam(description = "故障名称英文")
    @ApiModelProperty(hidden = true)
    private String faultNameEn;//故障名称英文

    @LogParam(description = "故障等级")
    private Integer faultLevel;//故障等级

    @LogParam(description = "车辆类型id")
    private String vehicleTypeId;//车辆类型id

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(example = "2018-01-01 12:00:00")
    @LogParam(description = "故障发生时间end")
    private Date startTimeEnd;//故障实际发生时间end 筛选时间用

    @LogParam(description = "当前站名称")
    private String currentStationName;

    @LogParam(description = "下一站名称")
    private String nextStationName;

    @LogParam(description = "解除状态")
    private Long endStatus;

    //2020-03-31 lixin记录 因多个业务都涉及到查询自动上报故障 有些需要条件限制所以增加次字段
    @LogParam(description = "筛选器应用场景（线路、车辆）若场景为‘车辆’则vehicleId不为空")
    private String applicationScenario;
    //2020-05-06 lixin记录 因故障筛选器想保存多个故障等级 所以为了保证原故障查询不受影响 在此添加faultLevelList字段
    @LogParam(description = "故障等级list（筛选器使用）")
    private List<Integer> faultLevelList;

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getVehicleNameCn() {
        return vehicleNameCn;
    }

    public void setVehicleNameCn(String vehicleNameCn) {
        this.vehicleNameCn = vehicleNameCn;
    }

    public String getVehicleNamen() {
        return vehicleNamen;
    }

    public void setVehicleNamen(String vehicleNamen) {
        this.vehicleNamen = vehicleNamen;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public String getFaultCode() {
        return faultCode;
    }

    public void setFaultCode(String faultCode) {
        this.faultCode = faultCode;
    }

    public String getFaultNameCn() {
        return faultNameCn;
    }

    public void setFaultNameCn(String faultNameCn) {
        this.faultNameCn = faultNameCn;
    }

    public String getFaultNameEn() {
        return faultNameEn;
    }

    public void setFaultNameEn(String faultNameEn) {
        this.faultNameEn = faultNameEn;
    }

    public Integer getFaultLevel() {
        return faultLevel;
    }

    public void setFaultLevel(Integer faultLevel) {
        this.faultLevel = faultLevel;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }


    public Date getStartTimeEnd() {
        return startTimeEnd;
    }

    public void setStartTimeEnd(Date startTimeEnd) {
        this.startTimeEnd = startTimeEnd;
    }

    public String getCurrentStationName() {
        return currentStationName;
    }

    public void setCurrentStationName(String currentStationName) {
        this.currentStationName = currentStationName;
    }

    public String getNextStationName() {
        return nextStationName;
    }

    public void setNextStationName(String nextStationName) {
        this.nextStationName = nextStationName;
    }

    public Long getEndStatus() {
        return endStatus;
    }

    public void setEndStatus(Long endStatus) {
        this.endStatus = endStatus;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getApplicationScenario() {
        return applicationScenario;
    }

    public void setApplicationScenario(String applicationScenario) {
        this.applicationScenario = applicationScenario;
    }

    public List<Integer> getFaultLevelList() {
        return faultLevelList;
    }

    public void setFaultLevelList(List<Integer> faultLevelList) {
        this.faultLevelList = faultLevelList;
    }
}
