package cc.crrc.manage.pojo.mtc;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.pojo.PageVO;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 用来承载激励列表页面模糊查询参数，和显示机理模型信息
 * @Author: mite
 * @Date: 2019-12-23
 */
@Duplicates({
        @Duplicate(table = "mtc_mechanism_alarm_rule", message = "存在触发相同故障的机理规则名称重复！", condition = "fault_type_key='${faultTypeKey}' and name='${name}'", groups = {
                Insert.class}),
        @Duplicate(table = "mtc_mechanism_alarm_rule", message = "存在同车型下机理规则名称重复", condition = "vehicle_type_id='${vehicleTypeId}' and name='${name}'", groups = {
                Insert.class}),
        @Duplicate(table = "mtc_mechanism_alarm_rule", message = "存在触发相同故障的机理规则名称重复", condition = "fault_type_key='${faultTypeKey}' and id!='${id}' and name='${name}'", groups = {
                Update.class}),
        @Duplicate(table = "mtc_mechanism_alarm_rule", message = "存在同车型下机理规则名称重复", condition = "vehicle_type_id='${vehicleTypeId}' and id!='${id}' and name='${name}'", groups = {
                Update.class})
})
public class MtcMechanismAlarmRuleDTO extends PageVO {
    @ApiModelProperty(value = "机理规则主键")
    @NotNull
    private String id;
    @ApiModelProperty(value = "所属车厢", position = 4)
    private String location;
    @ApiModelProperty(value = "所属系统id", position = 5)
    private String subsystem;
    @ApiModelProperty(value = "所属故障中文名", position = 2)
    private String faultNameCn;
    @ApiModelProperty(value = "引用车辆型号表的主键（id）", position = 1)
    private String vehicleTypeId;
    @ApiModelProperty(value = "引用故障类型表的业务主键（fault_type_key）", position = 3)
    private String faultTypeKey;
    @ApiModelProperty(value = "规则名称", position = 6)
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
    private String name;
    @ApiModelProperty(value = "规则内容")
    private String content;
    @ApiModelProperty(value = "使能标志位", position = 7)
    private Boolean enable;
    @ApiModelProperty(value = "规则描述", position = 8)
    @Length(max = 255, message = "长度必须小于等于255",groups= {Insert.class,Update.class})
    private String description;
    @ApiModelProperty(value = "线路id", position = 9)
    private String lineId;
    @ApiModelProperty(value = "信号周期", position = 10)
    private String signalCycle;
    @ApiModelProperty(value = "机理规则测试状态", position = 10)
    private Boolean testStatus;
    @ApiModelProperty(value = "机理规则加密状态", position = 10)
    private Boolean encryptionStatus;
    @ApiModelProperty(value = "机理规则配置是否弹窗")
    private String puwStatue;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Boolean getEncryptionStatus() {
        return encryptionStatus;
    }

    public void setEncryptionStatus(Boolean encryptionStatus) {
        this.encryptionStatus = encryptionStatus;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getFaultNameCn() {
        return faultNameCn;
    }

    public void setFaultNameCn(String faultNameCn) {
        this.faultNameCn = faultNameCn;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getFaultTypeKey() {
        return faultTypeKey;
    }

    public void setFaultTypeKey(String faultTypeKey) {
        this.faultTypeKey = faultTypeKey;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSignalCycle() {
        return signalCycle;
    }

    public void setSignalCycle(String signalCycle) {
        this.signalCycle = signalCycle;
    }

    public Boolean getTestStatus() {
        return testStatus;
    }

    public void setTestStatus(Boolean testStatus) {
        this.testStatus = testStatus;
    }
    public String getPuwStatue() {
        return puwStatue;
    }

    public void setPuwStatue(String puwStatue) {
        this.puwStatue = puwStatue;
    }
}
