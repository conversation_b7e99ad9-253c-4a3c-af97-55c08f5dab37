package cc.crrc.manage.pojo.mtc;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.PageVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */

public class MtcAlarmWarningDTO extends PageVO {

//    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "车型id")
    @LogParam(description = "车辆类型id")
    private String vehicleTypeId;

    @ApiModelProperty(value = "线路id")
    @LogParam(description = "线路id")
    private String lineId;

    @ApiModelProperty(value = "故障名称 筛选条件")
    @LogParam(description = "故障中文名称")
    private String faultNameCn;

    @ApiModelProperty(value = "车辆编号 筛选条件")
    @LogParam(description = "车辆编号")
    private String vehicleCode;

    @ApiModelProperty(value = "故障类型 0：自动故障 2：人工故障")
    @LogParam(description = "故障类型")
    private String faultSource;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "查询起始时间")
    @LogParam(description = "查询起始时间")
    private Date queryTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "查询结束时间")
    @LogParam(description = "查询结束时间")
    private Date queryTimeEnd;

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public String getLineId() {
        return lineId;
    }

    public String getFaultNameCn() {
        return faultNameCn;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public String getFaultSource() {
        return faultSource;
    }

    public Date getQueryTimeStart() {
        return queryTimeStart;
    }

    public Date getQueryTimeEnd() {
        return queryTimeEnd;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public void setFaultNameCn(String faultNameCn) {
        this.faultNameCn = faultNameCn;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public void setFaultSource(String faultSource) {
        this.faultSource = faultSource;
    }

    public void setQueryTimeStart(Date queryTimeStart) {
        this.queryTimeStart = queryTimeStart;
    }

    public void setQueryTimeEnd(Date queryTimeEnd) {
        this.queryTimeEnd = queryTimeEnd;
    }
}
