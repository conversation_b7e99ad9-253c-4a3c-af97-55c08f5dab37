package cc.crrc.manage.pojo.mtc;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.PageVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;


public class MtcAutoFaultRecordPO extends PageVO {

    @LogParam(description = "故障编号id")
    @ApiModelProperty(hidden = true)
    private String id;

    @LogParam(description = "车辆编号")
    private String vehicleId;

    @LogParam(description = "引用故障类型表的业务主键")
    @ApiModelProperty(hidden = true)
    private String faultTypeKey;

    @LogParam(description = "引用部件表的主键")
    @ApiModelProperty(hidden = true)
    private String componentId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(example = "2018-01-01 12:00:00")
    @LogParam(description = "故障发生时间")
    private Date startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @LogParam(description = "故障结束时间")
    @ApiModelProperty(example = "2018-01-01 12:00:00" ,hidden = true)
    private Date endTime;

    @LogParam(description = "处理状态")
    private String status;

    @LogParam(description = "用户是否确认")
    private Integer confirm;

    /*2020-03-31 添加 字段添加*/
    @LogParam(description = "正线状态(t/f)")
    private Boolean runningStatus;

    @LogParam(description = "雪崩压制状态(t/f)")
    private Boolean suppressedStatus;

    @LogParam(description = "抑制故障父节点")
    private String suppressedBy;

    @LogParam(description = "速度")
    @ApiModelProperty(hidden = true)
    private Float speed;

    @LogParam(description = "运行里程")
    @ApiModelProperty(hidden = true)
    private Long mileage;

    @LogParam(description = "当前站 站点id")
    private Long currentStationId;

    @LogParam(description = "下一站 站点id")
    private Long nextStationId;

    @LogParam(description = "手柄级位")
    @ApiModelProperty(hidden = true)
    private Long level;

    @LogParam(description = "司机室激活端")
    @ApiModelProperty(hidden = true)
    private String activeCabin;

    @LogParam(description = "车辆运行模式")
    @ApiModelProperty(hidden = true)
    private String mode;

    @LogParam(description = "修改人")
    @ApiModelProperty(hidden = true)
    private String modifyBy;

    @LogParam(description = "修改时间")
    @ApiModelProperty(hidden = true)
    private Date modifyTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getFaultTypeKey() {
        return faultTypeKey;
    }

    public void setFaultTypeKey(String faultTypeKey) {
        this.faultTypeKey = faultTypeKey;
    }

    public String getComponentId() {
        return componentId;
    }

    public void setComponentId(String componentId) {
        this.componentId = componentId;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getConfirm() {
        return confirm;
    }

    public void setConfirm(Integer confirm) {
        this.confirm = confirm;
    }

    public Float getSpeed() {
        return speed;
    }

    public void setSpeed(Float speed) {
        this.speed = speed;
    }

    public Long getMileage() {
        return mileage;
    }

    public void setMileage(Long mileage) {
        this.mileage = mileage;
    }

    public Long getCurrentStationId() {
        return currentStationId;
    }

    public void setCurrentStationId(Long currentStationId) {
        this.currentStationId = currentStationId;
    }

    public Long getNextStationId() {
        return nextStationId;
    }

    public void setNextStationId(Long nextStationId) {
        this.nextStationId = nextStationId;
    }

    public Long getLevel() {
        return level;
    }

    public void setLevel(Long level) {
        this.level = level;
    }

    public String getActiveCabin() {
        return activeCabin;
    }

    public void setActiveCabin(String activeCabin) {
        this.activeCabin = activeCabin;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Boolean getRunningStatus() {
        return runningStatus;
    }

    public void setRunningStatus(Boolean runningStatus) {
        this.runningStatus = runningStatus;
    }

    public Boolean getSuppressedStatus() {
        return suppressedStatus;
    }

    public void setSuppressedStatus(Boolean suppressedStatus) {
        this.suppressedStatus = suppressedStatus;
    }

    public String getSuppressedBy() {
        return suppressedBy;
    }

    public void setSuppressedBy(String suppressedBy) {
        this.suppressedBy = suppressedBy;
    }
}
