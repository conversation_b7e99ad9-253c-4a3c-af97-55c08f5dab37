package cc.crrc.manage.pojo.mtc;

import cc.crrc.manage.common.annotation.group.Update;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @ClassName MtcMechanismVariableRuleEditDTO
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/1/4 13:15
 **/
public class MtcMechanismVariableRuleEditDTO {

    @ApiModelProperty(value = "全局变量主键")
    @NotNull(message = "变量ID不正确", groups = {Update.class})
    private String id;
    @ApiModelProperty(value = "变量图片base64")
    private String base64Str;
    @ApiModelProperty(value = "变量内容，JSON格式")
    private String content;

    public String getBase64Str() {
        return base64Str;
    }

    public void setBase64Str(String base64Str) {
        this.base64Str = base64Str;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
