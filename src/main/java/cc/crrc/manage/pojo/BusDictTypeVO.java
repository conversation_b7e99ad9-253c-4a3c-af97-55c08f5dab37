package cc.crrc.manage.pojo;

import io.swagger.annotations.ApiModelProperty;

public class BusDictTypeVO {

    private static final long serialVersionUID = 1L;

    private String id;
    /** 字典描述 */
    @ApiModelProperty(value = "字典描述")
    private String description;
    /** 字典类型编码 */
    @ApiModelProperty(value = "字典类型编码")
    private String code;
    /** 字典类型名称 */
    @ApiModelProperty(value = "字典类型名称")
    private String name;
    /** 字典范围 */
    @ApiModelProperty(value = "字典范围")
    private String typeLevel;
    @ApiModelProperty(value = "字典标签")
    private String label;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTypeLevel() {
        return typeLevel;
    }

    public void setTypeLevel(String typeLevel) {
        this.typeLevel = typeLevel;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
