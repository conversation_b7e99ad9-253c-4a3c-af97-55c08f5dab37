package cc.crrc.manage.pojo;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * 2019/11/13
 **/
public class SysFileVO {
    @NotBlank(message = "文件名称不能为空" ,groups= {Insert.class})
    @LogParam(description="文件名称")
    @ApiModelProperty(value = "文件名称", dataType = "Long")
    private String name;
    @NotBlank(message = "文件地址不能为空" ,groups= {Insert.class})
    @LogParam(description="文件地址")
    @ApiModelProperty(value = "文件地址", dataType = "String")
    private String url;
    @LogParam(description="类型")
    @ApiModelProperty(value = "类型", dataType = "String")
    private String type;
    @LogParam(description="文件格式")
    @ApiModelProperty(value = "文件格式", dataType = "String")
    private String format;
    @LogParam(description="文件大小")
    @ApiModelProperty(value = "文件大小", dataType = "Long")
    private Long size;
    @LogParam(description="文件哈希值")
    @ApiModelProperty(value = "文件哈希值", dataType = "String")
    private String hashCode;
    @NotBlank(message = "文件分组不能为空" ,groups= {Insert.class})
    @LogParam(description="文件分组")
    @ApiModelProperty(value = "文件分组", dataType = "String")
    private String group;
    @NotBlank(message = "文件远程地址不能为空" ,groups= {Insert.class})
    @LogParam(description="文件远程地址")
    @ApiModelProperty(value = "文件远程地址", dataType = "String")
    private String fileLocation;

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getFileLocation() {
        return fileLocation;
    }

    public void setFileLocation(String fileLocation) {
        this.fileLocation = fileLocation;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getHashCode() {
        return hashCode;
    }

    public void setHashCode(String hashCode) {
        this.hashCode = hashCode;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }
}
