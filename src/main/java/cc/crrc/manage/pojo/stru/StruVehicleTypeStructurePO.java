package cc.crrc.manage.pojo.stru;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import io.swagger.annotations.ApiModelProperty;
import net.minidev.json.annotate.JsonIgnore;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @FileName StruVehicleTypeStructure车辆构型实体类
 * <AUTHOR> yuxi
 * @Date 2019/11/15 16:31
 * @Version 1.0
 **/
@Duplicates({
        @Duplicate(table = "stru_vehicle_type_structure", message = "结构编码已存在", condition = "structure_code = '${structureCode}' AND vehicle_type_id = '${vehicleTypeId}' AND del_flag = '0'", groups = {Insert.class}),
        @Duplicate(table = "stru_vehicle_type_structure", message = "结构编码已存在", condition = "structure_code = '${structureCode}' AND vehicle_type_id = '${vehicleTypeId}' AND id != '${id}' and del_flag = '0'", groups = {Update.class})
})
public class StruVehicleTypeStructurePO {
    @LogParam(description = "构型id")
    private String id;
    @LogParam(description = "车辆型号id")
    @NotNull(message = "车辆型号id不能为空", groups = {Insert.class, Update.class})
    private String vehicleTypeId;
    @NotNull(message = "父结构编码不能为空", groups = {Insert.class, Update.class})
    @LogParam(description = "父结构编码")
    private String parentStructureCode;
    @NotNull(message = "结构编码不能为空", groups = {Insert.class, Update.class})
    @LogParam(description = "结构编码")
    private String structureCode;
    @LogParam(description = "构型中文名")
    @NotNull(message = "构型中文名不能为空", groups = {Insert.class, Update.class})
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
    private String nameCn;
    @LogParam(description = "构型英文名")
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
    private String nameEn;
    @LogParam(description = "构型英文缩写")
    @NotNull(message = "构型英文缩写不能为空", groups = {Insert.class})
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
    private String shortNameEn;
    @LogParam(description = "父节点集合，用构型英文缩写")
    @ApiModelProperty(hidden = true)
    private String structurePosition;
    @LogParam(description = "结构类型")
    private String structureType;
    @LogParam(description = "部件型号id")
    private String componentTypeId;
    @LogParam(description = "排序")
    private Long sortNumber;
    @LogParam(description = "备注")
    private String remark;
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private Date modifyTime;
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private String createBy;
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private Date createTime;
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private String modifyBy;
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private Integer delFlag;
    @ApiModelProperty(hidden = true)
    private String url;
    @ApiModelProperty(hidden = true)
    private String componentTypeName;

    //三维模型关联code
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String threedCode;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getParentStructureCode() {
        return parentStructureCode;
    }

    public void setParentStructureCode(String parentStructureCode) {
        this.parentStructureCode = parentStructureCode;
    }

    public String getStructureCode() {
        return structureCode;
    }

    public void setStructureCode(String structureCode) {
        this.structureCode = structureCode;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getShortNameEn() {
        return shortNameEn;
    }

    public void setShortNameEn(String shortNameEn) {
        this.shortNameEn = shortNameEn;
    }

    public String getStructurePosition() {
        return structurePosition;
    }

    public void setStructurePosition(String structurePosition) {
        this.structurePosition = structurePosition;
    }

    public String getStructureType() {
        return structureType;
    }

    public void setStructureType(String structureType) {
        this.structureType = structureType;
    }

    public String getComponentTypeId() {
        return componentTypeId;
    }

    public void setComponentTypeId(String componentTypeId) {
        this.componentTypeId = componentTypeId;
    }

    public Long getSortNumber() {
        return sortNumber;
    }

    public void setSortNumber(Long sortNumber) {
        this.sortNumber = sortNumber;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getComponentTypeName() {
        return componentTypeName;
    }

    public void setComponentTypeName(String componentTypeName) {
        this.componentTypeName = componentTypeName;
    }

    public String getThreedCode() {
        return threedCode;
    }

    public void setThreedCode(String threedCode) {
        this.threedCode = threedCode;
    }
}
