package cc.crrc.manage.pojo.stru;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;

import javax.validation.constraints.NotNull;

public class StruVehicleComponentRecordPO {

    @LogParam(description = "履历id")
    private String id;
    @LogParam(description = "车辆构型表id")
    @NotNull(message = "车辆构型表id不能为空", groups = {Insert.class, Update.class})
    private String vehicleStructureId;
    @LogParam(description = "部件位置")
    private String componentPosition;
    @LogParam(description = "更换前的部件编码")
    private String previousComponentCode;
    @LogParam(description = "更换后的部件编码")
    private String currentComponentCode;
    @LogParam(description = "更换人")
    private String executer;
    @LogParam(description = "更换时间")
    private String executeTime;
    @LogParam(description = "创建用户编号")
    private String createBy;
    @LogParam(description = "创建时间")
    private String createTime;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleStructureId() {
        return vehicleStructureId;
    }

    public void setVehicleStructureId(String vehicleStructureId) {
        this.vehicleStructureId = vehicleStructureId;
    }

    public String getComponentPosition() {
        return componentPosition;
    }

    public void setComponentPosition(String componentPosition) {
        this.componentPosition = componentPosition;
    }

    public String getPreviousComponentCode() {
        return previousComponentCode;
    }

    public void setPreviousComponentCode(String previousComponentCode) {
        this.previousComponentCode = previousComponentCode;
    }

    public String getCurrentComponentCode() {
        return currentComponentCode;
    }

    public void setCurrentComponentCode(String currentComponentCode) {
        this.currentComponentCode = currentComponentCode;
    }

    public String getExecuter() {
        return executer;
    }

    public void setExecuter(String executer) {
        this.executer = executer;
    }

    public String getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(String executeTime) {
        this.executeTime = executeTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
}
