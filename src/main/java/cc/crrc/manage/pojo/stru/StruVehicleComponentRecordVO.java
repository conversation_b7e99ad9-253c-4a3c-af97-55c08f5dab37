package cc.crrc.manage.pojo.stru;

import java.util.Date;

public class StruVehicleComponentRecordVO {

    // 构型编码
    private String structureCode;
    // 物资名称
    private String nameCn;
    // 部件位置
    private String componentPosition;
    // 更换前部件编码
    private String previousComponentCode;
    // 更换后部件编码
    private String currentComponentCode;
    // 更换人
    private String executer;
    // 更换时间
    private Date executeTime;

    public String getStructureCode() {
        return structureCode;
    }

    public void setStructureCode(String structureCode) {
        this.structureCode = structureCode;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getComponentPosition() {
        return componentPosition;
    }

    public void setComponentPosition(String componentPosition) {
        this.componentPosition = componentPosition;
    }

    public String getPreviousComponentCode() {
        return previousComponentCode;
    }

    public void setPreviousComponentCode(String previousComponentCode) {
        this.previousComponentCode = previousComponentCode;
    }

    public String getCurrentComponentCode() {
        return currentComponentCode;
    }

    public void setCurrentComponentCode(String currentComponentCode) {
        this.currentComponentCode = currentComponentCode;
    }

    public String getExecuter() {
        return executer;
    }

    public void setExecuter(String executer) {
        this.executer = executer;
    }

    public Date getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(Date executeTime) {
        this.executeTime = executeTime;
    }
}
