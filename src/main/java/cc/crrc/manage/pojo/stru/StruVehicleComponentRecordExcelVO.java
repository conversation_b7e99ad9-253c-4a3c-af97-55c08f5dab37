package cc.crrc.manage.pojo.stru;

import cn.afterturn.easypoi.excel.annotation.Excel;

public class StruVehicleComponentRecordExcelVO {
//    @Excel(name = "主键id")
    private String id;
    @Excel(name = "车型名称")
    private String vehicleTypeName;
    @Excel(name = "车辆编号")
    private String vehicleCode;
    @Excel(name = "构型编码")
    private String structureCode;
    @Excel(name = "物资名称")
    private String nameCn;
    @Excel(name = "所属系统")
    private String subSystemNameCn;
    @Excel(name = "部件位置")
    private String componentPosition;
    @Excel(name = "更换前部件编码")
    private String previousComponentCode;
    @Excel(name = "更换后部件编码")
    private String currentComponentCode;
    @Excel(name = "更换人")
    private String executer;
    @Excel(name = "更换时间(yyyy/MM/dd HH:mm:ss)", format = "yyyy/MM/dd HH:mm:ss")
    private String executeTime;
    @Excel(name = "标识",isColumnHidden = true)
    private String verification;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleTypeName() {
        return vehicleTypeName;
    }

    public void setVehicleTypeName(String vehicleTypeName) {
        this.vehicleTypeName = vehicleTypeName;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getStructureCode() {
        return structureCode;
    }

    public void setStructureCode(String structureCode) {
        this.structureCode = structureCode;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getSubSystemNameCn() {
        return subSystemNameCn;
    }

    public void setSubSystemNameCn(String subSystemNameCn) {
        this.subSystemNameCn = subSystemNameCn;
    }

    public String getComponentPosition() {
        return componentPosition;
    }

    public void setComponentPosition(String componentPosition) {
        this.componentPosition = componentPosition;
    }

    public String getPreviousComponentCode() {
        return previousComponentCode;
    }

    public void setPreviousComponentCode(String previousComponentCode) {
        this.previousComponentCode = previousComponentCode;
    }

    public String getCurrentComponentCode() {
        return currentComponentCode;
    }

    public void setCurrentComponentCode(String currentComponentCode) {
        this.currentComponentCode = currentComponentCode;
    }

    public String getExecuter() {
        return executer;
    }

    public void setExecuter(String executer) {
        this.executer = executer;
    }

    public String getExecuteTime() {
        return executeTime;
    }

    public void setExecuteTime(String executeTime) {
        this.executeTime = executeTime;
    }

    public String getVerification() {
        return verification;
    }

    public void setVerification(String verification) {
        this.verification = verification;
    }
}
