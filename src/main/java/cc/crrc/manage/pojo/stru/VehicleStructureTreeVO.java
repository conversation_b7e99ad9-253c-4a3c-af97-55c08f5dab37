package cc.crrc.manage.pojo.stru;

import cc.crrc.manage.common.vo.TreeNodeForStructureVO;
import io.swagger.annotations.ApiModelProperty;
import net.minidev.json.annotate.JsonIgnore;

import java.util.Date;

public class VehicleStructureTreeVO extends TreeNodeForStructureVO<VehicleStructureTreeVO> {
    private String id;
    // 车辆id
    private String vehicleId;
    // 部件id
    private String componentId;
    // 部件类型id
    private String componentTypeId;
    // 构型树结点唯一标识
    private String uniqueFlag;
    // 车辆型号中文名
    private String vehicleTypeName;
    // 启动日期
    private Date startDate;
    //删除状态  回溯历史判断使用
    private Integer delFlag;
    //回溯状态
    private Integer historyStatus;
    //3D效果图地址
    private String fbxUrl;
    //三维模型关联code
    private String threedCode;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getComponentId() {
        return componentId;
    }

    public void setComponentId(String componentId) {
        this.componentId = componentId;
    }

    public String getUniqueFlag() {
        return uniqueFlag;
    }

    public void setUniqueFlag(String uniqueFlag) {
        this.uniqueFlag = uniqueFlag;
    }

    public String getVehicleTypeName() {
        return vehicleTypeName;
    }

    public void setVehicleTypeName(String vehicleTypeName) {
        this.vehicleTypeName = vehicleTypeName;
    }

    public String getComponentTypeId() {
        return componentTypeId;
    }

    public void setComponentTypeId(String componentTypeId) {
        this.componentTypeId = componentTypeId;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Integer getHistoryStatus() {
        return historyStatus;
    }

    public void setHistoryStatus(Integer historyStatus) {
        this.historyStatus = historyStatus;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public String getFbxUrl() {
        return fbxUrl;
    }

    public void setFbxUrl(String fbxUrl) {
        this.fbxUrl = fbxUrl;
    }

    public String getThreedCode() {
        return threedCode;
    }

    public void setThreedCode(String threedCode) {
        this.threedCode = threedCode;
    }
}
