package cc.crrc.manage.pojo.stru;

import cc.crrc.manage.common.vo.TreeNodeForStructureVO;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.Date;

public class StruVehicleTypeStructureTreeVO extends TreeNodeForStructureVO<StruVehicleTypeStructureTreeVO> {
    // 构型id
    private String id;
    // 车型id
    private String vehicleTypeId;
    // 父节点集合（用构型英文名缩写）
    private String structurePosition;
    // 部件类型id
    private String componentTypeId;
    // 备注
    private String remark;
    @JsonIgnore
    private String createBy;
    @JsonIgnore
    private Date createTime;
    @JsonIgnore
    private String modifyBy;
    @JsonIgnore
    private Date modifyTime;
    private String url;
    // 构型树结点唯一标识
    private String uniqueFlag;
    // 所属车型
    private String vehicleTypeName;
    //(3d效果图)文件表主键
    private String fbxFileId;
    //3D效果图地址
    private String fbxUrl;
    //三维模型关联code
    private String threedCode;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getStructurePosition() {
        return structurePosition;
    }

    public void setStructurePosition(String structurePosition) {
        this.structurePosition = structurePosition;
    }

    public String getComponentTypeId() {
        return componentTypeId;
    }

    public void setComponentTypeId(String componentTypeId) {
        this.componentTypeId = componentTypeId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUniqueFlag() {
        return uniqueFlag;
    }

    public void setUniqueFlag(String uniqueFlag) {
        this.uniqueFlag = uniqueFlag;
    }

    public String getVehicleTypeName() {
        return vehicleTypeName;
    }

    public void setVehicleTypeName(String vehicleTypeName) {
        this.vehicleTypeName = vehicleTypeName;
    }

    public String getFbxFileId() {
        return fbxFileId;
    }

    public void setFbxFileId(String fbxFileId) {
        this.fbxFileId = fbxFileId;
    }

    public String getFbxUrl() {
        return fbxUrl;
    }

    public void setFbxUrl(String fbxUrl) {
        this.fbxUrl = fbxUrl;
    }

    public String getThreedCode() {
        return threedCode;
    }

    public void setThreedCode(String threedCode) {
        this.threedCode = threedCode;
    }
}
