package cc.crrc.manage.pojo;

import cc.crrc.manage.common.annotation.LogParam;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName SysAreaVO
 * @Description TODO
 * <AUTHOR> min<PERSON>
 * @Date 2020/8/3 20:08
 * @Version 1.0
 **/
public class SysLocationVO extends PageVO implements Serializable {
    @LogParam(description = "区域id")
    private String id;
    @LogParam(description = "父地域编号")
    private String parentId;
    @LogParam(description = "父节点集合")
    private String parentIds;
    @LogParam(description = "节点类型（根节点/子节点/叶节点）")
    private String nodeType;
    @LogParam(description = "地域中文名称")
    private String nameCn;
    @LogParam(description = "地域英文名称")
    private String nameEn;
    @LogParam(description = "邮政编码")
    private String code;
    @LogParam(description = "顺序编号")
    private Integer sortNumber;
    @LogParam(description = "经度")
    private Float longitude;
    @LogParam(description = "纬度")
    private Float latitude;

    private Boolean delFlag;
    private String createBy;
    private Date createTime;
    private String modifyBy;
    private Date modifyTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getParentIds() {
        return parentIds;
    }

    public void setParentIds(String parentIds) {
        this.parentIds = parentIds;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getSortNumber() {
        return sortNumber;
    }

    public void setSortNumber(Integer sortNumber) {
        this.sortNumber = sortNumber;
    }

    public Float getLongitude() {
        return longitude;
    }

    public void setLongitude(Float longitude) {
        this.longitude = longitude;
    }

    public Float getLatitude() {
        return latitude;
    }

    public void setLatitude(Float latitude) {
        this.latitude = latitude;
    }

    public Boolean getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Boolean delFlag) {
        this.delFlag = delFlag;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }
}
