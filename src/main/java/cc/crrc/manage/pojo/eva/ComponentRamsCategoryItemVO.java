package cc.crrc.manage.pojo.eva;

import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

public class ComponentRamsCategoryItemVO {
/**
 * @FileName ComponentRamsCategoryItemVO
 * <AUTHOR> xin
 * @Date 2020/2/25 16:39
 * @Version 1.0
 **/
    @LogParam(description = "item名称")
    @ApiModelProperty(value = "item名称", dataType = "String")
    private String itemName;
    @LogParam(description = "测试参数")
    @ApiModelProperty(value = "测试参数", dataType = "String")
    private String resultValue;
    @LogParam(description = "设计参数")
    @ApiModelProperty(value = "设计参数", dataType = "String")
    private String paramValue;
    @LogParam(description = "单位")
    @ApiModelProperty(value = "单位", dataType = "String")
    private String unit;

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getResultValue() {
        return resultValue;
    }

    public void setResultValue(String resultValue) {
        this.resultValue = resultValue;
    }

    public String getParamValue() {
        return paramValue;
    }

    public void setParamValue(String paramValue) {
        this.paramValue = paramValue;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }
}
