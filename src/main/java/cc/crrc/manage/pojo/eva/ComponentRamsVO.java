package cc.crrc.manage.pojo.eva;

import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class ComponentRamsVO {
/**
 * @FileName ComponentRamsVO
 * <AUTHOR> xin
 * @Date 2020/2/25 16:30
 * @Version 1.0
 **/
    @LogParam(description = "公司名称")
    @ApiModelProperty(value = "公司名称", dataType = "String")
    private String name;
    @LogParam(description = "部件名称")
    @ApiModelProperty(value = "部件名称", dataType = "String")
    private String nameCn;
    @LogParam(description = "部件型号编码")
    @ApiModelProperty(value = "部件型号编码", dataType = "String")
    private String productNumber;
    @LogParam(description = "类别list")
    @ApiModelProperty(value = "类别list")
    private List<ComponentRamsCategoryVO> categoryList;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getProductNumber() {
        return productNumber;
    }

    public void setProductNumber(String productNumber) {
        this.productNumber = productNumber;
    }

    public List<ComponentRamsCategoryVO> getCategoryList() {
        return categoryList;
    }

    public void setCategoryList(List<ComponentRamsCategoryVO> categoryList) {
        this.categoryList = categoryList;
    }
}
