package cc.crrc.manage.pojo.eva;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.component.RamsParameterDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class RamsfKindVO {
    @ApiModelProperty(value = "项点种类名称")
    @LogParam(description = "项点种类名称")
    private String itemCategory;
    @ApiModelProperty(value = "项点列表")
    @LogParam(description = "项点列表")
    private List<RamsParameterDTO> ramsParameterDTOList;

    public String getItemCategory() {
        return itemCategory;
    }

    public void setItemCategory(String itemCategory) {
        this.itemCategory = itemCategory;
    }

    public List<RamsParameterDTO> getRamsParameterDTOList() {
        return ramsParameterDTOList;
    }

    public void setRamsParameterDTOList(List<RamsParameterDTO> ramsParameterDTOList) {
        this.ramsParameterDTOList = ramsParameterDTOList;
    }
}
