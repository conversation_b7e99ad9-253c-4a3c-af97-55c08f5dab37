package cc.crrc.manage.pojo.eva;

import cc.crrc.manage.common.annotation.LogParam;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR>
 * 2020/02/19
 **/
public class EvaHealthResultPO {
    @LogParam(description = "健康评分数据项id")
    @ApiModelProperty(value = "健康评分数据项id", dataType = "String")
    private String id;
    @LogParam(description = "车辆编号")
    @ApiModelProperty(value = "车辆编号", dataType = "String")
    private String vehicleId;
    @LogParam(description = "车辆构型编码")
    @ApiModelProperty(value = "车辆构型编码", dataType = "String")
    private String vehicleStructureCode;
    @LogParam(description = "项点名称")
    @ApiModelProperty(value = "项点名称", dataType = "String")
    private String itemName;
    @LogParam(description = "项点值")
    @ApiModelProperty(value = "项点值", dataType = "String")
    private String itemValue;
    @LogParam(description = "评分结果类型")
    @ApiModelProperty(value = "评分结果类型", dataType = "String")
    private String type;
    @LogParam(description = "指导意见")
    @ApiModelProperty(value = "指导意见", dataType = "String")
    private String instruction;
    @JsonIgnore
    private String createBy;
    @LogParam(description = "创建时间")
    @ApiModelProperty(value = "创建时间", dataType = "Date")
    private Date createTime;
    @LogParam(description = "线路编号")
    @ApiModelProperty(value = "线路编号", dataType = "String")
    private String lineId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getVehicleStructureCode() {
        return vehicleStructureCode;
    }

    public void setVehicleStructureCode(String vehicleStructureCode) {
        this.vehicleStructureCode = vehicleStructureCode;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getItemValue() {
        return itemValue;
    }

    public void setItemValue(String itemValue) {
        this.itemValue = itemValue;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getInstruction() {
        return instruction;
    }

    public void setInstruction(String instruction) {
        this.instruction = instruction;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
}
