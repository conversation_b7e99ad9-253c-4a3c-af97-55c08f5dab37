package cc.crrc.manage.pojo.eva;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * 2020/02/20
 **/
@Duplicates({
        @Duplicate(table = "eva_health_rule", message = "同一车型的同一分类下的健康规则名称不能重复！", groups = {Insert.class},
                condition = "vehicle_type_id = '${vehicleTypeId}' AND category = '${category}' AND item_name = '${itemName}'"),
        @Duplicate(table = "eva_health_rule", message = "同一车型的同一分类下的健康规则名称不能重复！", groups = {Update.class},
                condition = "vehicle_type_id = '${vehicleTypeId}' AND category = '${category}' AND item_name = '${itemName}' AND id != '${id}'")
})
public class EvaHealthRulePO {
    @NotNull(message = "健康规则数据项id不能为空" ,groups= {Update.class})
    @LogParam(description = "健康规则数据项id")
    @ApiModelProperty(value = "健康规则数据项id", dataType = "String")
    private String id;
    @NotNull(message = "车辆型号id不能为空" ,groups= {Update.class, Insert.class})
    @LogParam(description = "车辆型号id")
    @ApiModelProperty(value = "车辆型号id", dataType = "String")
    private String vehicleTypeId;
    @NotBlank(message = "项点名称不能为空" ,groups= {Update.class,Insert.class})
    @LogParam(description = "项点名称")
    @ApiModelProperty(value = "项点名称", dataType = "String")
    private String itemName;
    @NotBlank(message = "项点值不能为空" ,groups= {Update.class,Insert.class})
    @LogParam(description = "项点值")
    @ApiModelProperty(value = "项点值", dataType = "String")
    private String itemValue;
    @LogParam(description = "单位")
    @ApiModelProperty(value = "单位", dataType = "String")
    private String unit;
    @LogParam(description = "类别")
    @ApiModelProperty(value = "类别", dataType = "String")
    private String category;
    @JsonIgnore
    private String createBy;
    @JsonIgnore
    private Date createTime;
    @JsonIgnore
    private String modifyBy;
    @JsonIgnore
    private Date modifyTime;
    @JsonIgnore
    private String remark;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getItemValue() {
        return itemValue;
    }

    public void setItemValue(String itemValue) {
        this.itemValue = itemValue;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
