package cc.crrc.manage.pojo.eva;

import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class ComponentRamsCategoryVO {
/**
 * @FileName ComponentRamsCategoryVO
 * <AUTHOR> xin
 * @Date 2020/2/25 16:33
 * @Version 1.0
 **/
    @LogParam(description = "类别名称")
    @ApiModelProperty(value = "类别名称", dataType = "String")
    private String itemCategory;
    @LogParam(description = "类别list")
    @ApiModelProperty(value = "类别list")
    private List<ComponentRamsCategoryItemVO> categoryItemList;

    public String getItemCategory() {
        return itemCategory;
    }

    public void setItemCategory(String itemCategory) {
        this.itemCategory = itemCategory;
    }

    public List<ComponentRamsCategoryItemVO> getCategoryItemList() {
        return categoryItemList;
    }

    public void setCategoryItemList(List<ComponentRamsCategoryItemVO> categoryItemList) {
        this.categoryItemList = categoryItemList;
    }
}
