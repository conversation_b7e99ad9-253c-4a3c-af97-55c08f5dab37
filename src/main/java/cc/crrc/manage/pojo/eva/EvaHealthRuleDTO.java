package cc.crrc.manage.pojo.eva;

import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

public class EvaHealthRuleDTO {
    @LogParam(description = "车辆型号id")
    @ApiModelProperty(value = "车辆型号id", dataType = "String")
    private String vehicleTypeId;
    @LogParam(description = "类别")
    @ApiModelProperty(value = "类别", dataType = "String")
    private String category;
    @LogParam(description = "线路id")
    @ApiModelProperty(value = "线路id", dataType = "String")
    private String lineId;

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
}
