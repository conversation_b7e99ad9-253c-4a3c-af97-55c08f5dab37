package cc.crrc.manage.pojo.eva;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.PageVO;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class RamsLineVo extends PageVO {
    @ApiModelProperty(value = "线路ID")
    @LogParam(description = "线路ID")
    private String lineId;
    @ApiModelProperty(value = "线路名称")
    @LogParam(description = "线路名称")
    private String lineName;
    @ApiModelProperty(value = "运行里程")
    @LogParam(description = "运行里程")
    private String totalDistance;
    @ApiModelProperty(value = "运行时间")
    @LogParam(description = "运行时间")
    private String totalDrivingTime;
    @ApiModelProperty(value = "车型及数量")
    @LogParam(description = "车型及数量")
    private List<RamsLineVehicleTypeVO> ramsLineVehicleTypeVOS ;
    @ApiModelProperty(value = "rams参数列表")
    @LogParam(description = "rams参数列表")
    private List<RamsfKindVO> ramsfKindList;

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getTotalDistance() {
        return totalDistance;
    }

    public void setTotalDistance(String totalDistance) {
        this.totalDistance = totalDistance;
    }

    public String getTotalDrivingTime() {
        return totalDrivingTime;
    }

    public void setTotalDrivingTime(String totalDrivingTime) {
        this.totalDrivingTime = totalDrivingTime;
    }

    public List<RamsLineVehicleTypeVO> getRamsLineVehicleTypeVOS() {
        return ramsLineVehicleTypeVOS;
    }

    public void setRamsLineVehicleTypeVOS(List<RamsLineVehicleTypeVO> ramsLineVehicleTypeVOS) {
        this.ramsLineVehicleTypeVOS = ramsLineVehicleTypeVOS;
    }

    public List<RamsfKindVO> getRamsfKindList() {
        return ramsfKindList;
    }

    public void setRamsfKindList(List<RamsfKindVO> ramsfKindList) {
        this.ramsfKindList = ramsfKindList;
    }
}
