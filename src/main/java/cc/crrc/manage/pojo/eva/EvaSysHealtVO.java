package cc.crrc.manage.pojo.eva;

import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

/**
 * @FileName EvaSysHealtVO
 * @Description 车辆子系统健康评分值对象
 * <AUTHOR>
 * @Date 2020/9/24 16:46
 **/
public class EvaSysHealtVO {

    @LogParam(description = "子系统名称")
    @ApiModelProperty(value = "子系统名称")
    private String sysName;
    @LogParam(description = "子系统评分")
    @ApiModelProperty(value = "子系统评分")
    private String score;
    @LogParam(description = "子系统当前故障数量")
    @ApiModelProperty(value = "子系统当前故障数量")
    private String sysFault;
    @LogParam(description = "子系统2万公里内总故障数量")
    @ApiModelProperty(value = "子系统2万公里内总故障数量")
    private String sysCountFault;
    @LogParam(description = "子系统指导意见")
    @ApiModelProperty(value = "子系统指导意见")
    private String instruction;
    @LogParam(description = "车辆编码")
    @ApiModelProperty(value = "车辆编码")
    private String vehicleCode;
    @LogParam(description = "线路id")
    @ApiModelProperty(value = "线路id")
    private String lineId;

    public String getScore() { return score; }

    public void setScore(String score) { this.score = score; }

    public String getSysFault() { return sysFault; }

    public void setSysFault(String sysFault) { this.sysFault = sysFault; }

    public String getSysCountFault() { return sysCountFault; }

    public void setSysCountFault(String sysCountFault) { this.sysCountFault = sysCountFault; }

    public String getInstruction() { return instruction; }

    public void setInstruction(String instruction) { this.instruction = instruction; }

    public String getVehicleCode() { return vehicleCode; }

    public void setVehicleCode(String vehicleCode) { this.vehicleCode = vehicleCode; }

    public String getLineId() { return lineId; }

    public void setLineId(String lineId) { this.lineId = lineId; }

    public String getSysName() { return sysName; }

    public void setSysName(String sysName) { this.sysName = sysName; }
}
