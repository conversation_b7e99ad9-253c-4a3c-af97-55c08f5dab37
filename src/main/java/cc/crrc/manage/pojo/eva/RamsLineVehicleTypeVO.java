package cc.crrc.manage.pojo.eva;

import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

public class RamsLineVehicleTypeVO  {
    @ApiModelProperty(value = "车辆id")
    @LogParam(description = "车辆id")
    private String id;
    @ApiModelProperty(value = "车辆型号名称")
    @LogParam(description = "车辆型号名称")
    private String name;
    @ApiModelProperty(value = "同个车型的车辆数量")
    @LogParam(description = "同个车型的车辆数量")
    private Integer sameVehicleTypeNumber;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSameVehicleTypeNumber() {
        return sameVehicleTypeNumber;
    }

    public void setSameVehicleTypeNumber(Integer sameVehicleTypeNumber) {
        this.sameVehicleTypeNumber = sameVehicleTypeNumber;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
