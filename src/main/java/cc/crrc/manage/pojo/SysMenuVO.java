package cc.crrc.manage.pojo;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;

import java.util.Date;
@Duplicates({
        @Duplicate(table = "sys_menu", message = "菜单编码编码已存在,请重新输入code！", groups = {Insert.class}, condition = "code = '${code}' AND del_flag = '0'"),
        @Duplicate(table = "sys_menu", message = "菜单编码编码已存在,请重新输入code！", groups = {Update.class}, condition = "code = '${code}' AND id != '${id}' AND del_flag = '0'")
})
public class SysMenuVO {
    @LogParam(description="菜单id")
    private String id;
    @LogParam(description="菜单编码")
    private String code;
    @LogParam(description="菜单标题")
    private String title;
    @LogParam(description="父id")
    private String parentId;
    @LogParam(description="菜单地址")
    private String href;
    @LogParam(description="菜单图标")
    private String icon;
    @LogParam(description="菜单类型")
    private String type;
    private String path;
    @LogParam(description="是否可用")
    private boolean enabled;
    private String createBy;
    private Date createDate;
    private String updateBy;
    private Date updateDate;
    private String tenantId;
    private String delFlag;
    private String attr1;
    @LogParam(description="菜单排序")
    private String sort;
    private boolean redirecting;
    private String beforeEnter;
    private String props;
    private String regex;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getHref() {
        return href;
    }

    public void setHref(String href) {
        this.href = href;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }


    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getAttr1() {
        return attr1;
    }

    public void setAttr1(String attr1) {
        this.attr1 = attr1;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public String getBeforeEnter() {
        return beforeEnter;
    }

    public void setBeforeEnter(String beforeEnter) {
        this.beforeEnter = beforeEnter;
    }

    public String getProps() {
        return props;
    }

    public void setProps(String props) {
        this.props = props;
    }

    public String getRegex() {
        return regex;
    }

    public void setRegex(String regex) {
        this.regex = regex;
    }

    public boolean isRedirecting() {
        return redirecting;
    }

    public void setRedirecting(boolean redirecting) {
        this.redirecting = redirecting;
    }
}
