package cc.crrc.manage.pojo.comm.protocol;

import cc.crrc.manage.common.annotation.Contained;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.BasePO;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @FileName ProtocolStatusDTO
 * <AUTHOR> shuangquan
 * @Date 2019/11/15 15:59
 **/
public class ProtocolStatusDTO extends BasePO {
    @LogParam(description = "协议ID")
    @NotNull(message = "协议ID不能为空")
    @ApiModelProperty(value = "协议ID", required = true)
    private String protocolId;

    @LogParam(description = "协议状态")
    @NotNull(message = "协议ID不能为空")
    @Contained(value = {"1", "0"}, message = "协议状态不正确")
    @ApiModelProperty(value = "协议状态(1-启用，0-禁用)", required = true)
    private Integer status;

    public String getProtocolId() {
        return protocolId;
    }

    public void setProtocolId(String protocolId) {
        this.protocolId = protocolId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

}
