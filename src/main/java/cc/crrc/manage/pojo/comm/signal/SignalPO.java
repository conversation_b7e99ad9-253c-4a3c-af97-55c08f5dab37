package cc.crrc.manage.pojo.comm.signal;

import cc.crrc.manage.common.annotation.Contained;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.pojo.BasePO;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * @FileName SignalPO
 * <AUTHOR> shuangquan
 * @Date 2019/11/14 10:45
 **/
public class SignalPO extends BasePO {
    @LogParam(description = "信号ID")
    @NotNull(message = "信号ID不正确", groups = {Update.class})
    @ApiModelProperty(value = "信号ID(新增不需要)", required = false)
    private String id;

    @LogParam(description = "信号类型")
    @Contained(value = {"0", "1"}, message = "信号类型不正确", groups = {Insert.class, Update.class})
    @NotBlank(message = "信号类型不能为空", groups = {Insert.class, Update.class})
    @ApiModelProperty(value = "信号类型(0-原始信号，1-扩展信号)", required = true)
    private String signalType;

    @LogParam(description = "协议ID")
    @NotNull(message = "协议不能为空", groups = {Insert.class, Update.class})
    @ApiModelProperty(value = "协议ID", required = true)
    private String protocolId;

    @LogParam(description = "信号中文名称")
    @NotBlank(message = "信号中文名称不能为空", groups = {Insert.class, Update.class})
    @Length(max = 128, message = "信号中文名称过长")
    @ApiModelProperty(value = "信号中文名称", required = true)
    private String nameCN;

    @LogParam(description = "信号英文名称")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", groups = {Insert.class, Update.class}, message = "只能输入英文和数字")
    @NotBlank(message = "信号英文名称不能为空", groups = {Insert.class, Update.class})
    @Length(max = 128, message = "信号英文名称过长")
    @ApiModelProperty(value = "信号英文名称", required = true)
    private String nameEN;

    @LogParam(description = "字节偏移量")
//    @Pattern(regexp = "^[0-9]*$", groups = {Insert.class, Update.class}, message = "只能输入数字")
    @Min(value = 0, message = "字节偏移量不正确", groups = {Insert.class, Update.class})
    @ApiModelProperty(value = "字节偏移量（原始信号必填）")
    private Integer byteOffset;

    @LogParam(description = "位偏移量")
//    @Pattern(regexp = "^[0-9]*$", groups = {Insert.class, Update.class}, message = "只能输入数字")
    @Min(value = 0, message = "位移量不正确", groups = {Insert.class, Update.class})
    @ApiModelProperty(value = "位偏移量（原始信号必填）")
    private Integer bitOffset;

    @LogParam(description = "解析方式")
    @NotBlank(message = "解析方式不能为空", groups = {Insert.class, Update.class})
    @ApiModelProperty(value = "解析方式", required = true)
    private String dataType;

    @LogParam(description = "信号单位")
    @Length(max = 16, message = "单位过长")
    @ApiModelProperty(value = "信号单位")
    private String unit;

    @LogParam(description = "车厢位置")
    @Length(max = 16, message = "车厢位置过长")
    @ApiModelProperty(value = "车厢位置")
    private String location;

    @LogParam(description = "子系统")
    @NotBlank(message = "子系统不能为空", groups = {Insert.class, Update.class})
    @ApiModelProperty(value = "子系统", required = true)
    private String subSystem;

    @LogParam(description = "解析脚本")
    @ApiModelProperty(value = "解析脚本")
    private String parseScript;

    @LogParam(description = "故障标识")
    @Length(max = 64, message = "故障标识过长")
    @ApiModelProperty(value = "故障标识")
    private String faultTypeKey;

    @LogParam(description = "故障触发值")
    @Contained(value = {"0", "1"}, message = "故障触发值不正确", groups = {Insert.class, Update.class})
    @ApiModelProperty(value = "故障触发值")
    private Integer triggerValue;

    @LogParam(description = "信号最大值")
    @Pattern(regexp = "^$|^[0-9]+([.][0-9]+)?$", groups = {Insert.class, Update.class}, message = "最大值格式不正确")
    @ApiModelProperty(value = "信号最大值")
    private String maxVal;

    @LogParam(description = "信号最小值")
    @Pattern(regexp = "^$|^[0-9]+([.][0-9]+)?$", groups = {Insert.class, Update.class}, message = "最小值格式不正确")
    @ApiModelProperty(value = "信号最小值")
    private String minVal;

    @LogParam(description = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

    public String getProtocolId() {
        return protocolId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSignalType() {
        return signalType;
    }

    public void setSignalType(String signalType) {
        this.signalType = signalType;
    }

    public String getNameCN() {
        return nameCN;
    }

    public void setNameCN(String nameCN) {
        this.nameCN = nameCN;
    }

    public String getNameEN() {
        return nameEN;
    }

    public void setNameEN(String nameEN) {
        this.nameEN = nameEN;
    }

    public Integer getByteOffset() {
        return byteOffset;
    }

    public void setByteOffset(Integer byteOffset) {
        this.byteOffset = byteOffset;
    }

    public Integer getBitOffset() {
        return bitOffset;
    }

    public void setBitOffset(Integer bitOffset) {
        this.bitOffset = bitOffset;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getSubSystem() {
        return subSystem;
    }

    public void setSubSystem(String subSystem) {
        this.subSystem = subSystem;
    }

    public String getParseScript() {
        return parseScript;
    }

    public void setParseScript(String parseScript) {
        this.parseScript = parseScript;
    }

    public String getFaultTypeKey() {
        return faultTypeKey;
    }

    public void setFaultTypeKey(String faultTypeKey) {
        this.faultTypeKey = faultTypeKey;
    }

    public Integer getTriggerValue() {
        return triggerValue;
    }

    public void setTriggerValue(Integer triggerValue) {
        this.triggerValue = triggerValue;
    }

    public void setProtocolId(String protocolId) {
        this.protocolId = protocolId;
    }

    public String getMaxVal() {
        return maxVal;
    }

    public void setMaxVal(String maxVal) {
        this.maxVal = maxVal;
    }

    public String getMinVal() {
        return minVal;
    }

    public void setMinVal(String minVal) {
        this.minVal = minVal;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
