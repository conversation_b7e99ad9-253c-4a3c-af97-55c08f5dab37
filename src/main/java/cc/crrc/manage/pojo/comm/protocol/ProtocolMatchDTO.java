package cc.crrc.manage.pojo.comm.protocol;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.BasePO;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @FileName ProtocolMatchDTO
 * <AUTHOR> shuangquan
 * @Date 2019/11/18 8:41
 **/
public class ProtocolMatchDTO extends BasePO {
    @NotNull(message = "协议不能为空")
    @LogParam(description = "协议ID")
    @ApiModelProperty(value = "协议ID", required = true)
    private String protocolId;
    @Valid
    @LogParam(description = "匹配车辆")
    @ApiModelProperty(value = "匹配车辆列表", required = true)
    private List<ProtocolMatchVO> vehicleList;

    public String getProtocolId() {
        return protocolId;
    }

    public void setProtocolId(String protocolId) {
        this.protocolId = protocolId;
    }

    public List<ProtocolMatchVO> getVehicleList() {
        return vehicleList;
    }

    public void setVehicleList(List<ProtocolMatchVO> vehicleList) {
        this.vehicleList = vehicleList;
    }
}
