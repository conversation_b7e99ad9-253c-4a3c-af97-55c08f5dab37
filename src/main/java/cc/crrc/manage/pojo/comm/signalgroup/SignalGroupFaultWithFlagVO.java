package cc.crrc.manage.pojo.comm.signalgroup;

import cc.crrc.manage.common.annotation.LogParam;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @author: Guowei
 * 2020-06-11
 */
public class SignalGroupFaultWithFlagVO {
    @LogParam(description = "故障信号分组分页信息")
    @ApiModelProperty(value = "故障信号分组分页信息")
    private PageInfo<SignalGroupFaultVO> pageInfo;
    @LogParam(description = "已关联故障列表")
    @ApiModelProperty(value = "已关联故障列表")
    private List<SignalGroupFaultVO> FaultTypeKeys;

    public PageInfo<SignalGroupFaultVO> getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo<SignalGroupFaultVO> pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<SignalGroupFaultVO> getFaultTypeKeys() {
        return FaultTypeKeys;
    }

    public void setFaultTypeKeys(List<SignalGroupFaultVO> faultTypeKeys) {
        FaultTypeKeys = faultTypeKeys;
    }
}
