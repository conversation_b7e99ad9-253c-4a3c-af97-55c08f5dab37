package cc.crrc.manage.pojo.comm.snowSlide;

import cc.crrc.manage.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;

/**
 * 雪崩图中故障关系的实体类
 *
 * <AUTHOR>
 * 2020/4/8
 **/
public class SnowSlideEdgePO {
    private String id;
    @JSONField(name = "source")
    private String sourceId;
    @JSONField(name = "target")
    private String targetId;
    @JSONField(name = "label")
    private String time;
    private String shape;
    private Integer enable;

    public SnowSlideEdgePO(){

    }

    public SnowSlideEdgePO(JSONObject jsonObject){
        sourceId = jsonObject.getString("source");
        targetId = jsonObject.getString("target");
        String timeStr = jsonObject.getString("label");
        if(StringUtils.isNotEmpty(timeStr)){
            time = timeStr.substring(0,timeStr.indexOf("(ms)"));
        }
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getTargetId() {
        return targetId;
    }

    public void setTargetId(String targetId) {
        this.targetId = targetId;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getShape() {
        return shape;
    }

    public void setShape(String shape) {
        this.shape = shape;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getEnable() {
        return enable;
    }

    public void setEnable(Integer enable) {
        this.enable = enable;
    }
}
