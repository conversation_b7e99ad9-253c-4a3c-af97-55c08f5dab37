package cc.crrc.manage.pojo.comm.protocol;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * @FileName ProtocolDelDTO
 * <AUTHOR> shua<PERSON>quan
 * @Date 2019/11/15 15:14
 **/
@Duplicates({
        @Duplicate(table = "comm_protocol", condition = "enable=1 and id='${protocolId}' and del_flag=0", message =
                "协议已启用，不能进行此操作")
})
public class ProtocolDelDTO {
    @LogParam(description = "协议ID")
    @NotNull(message = "协议不能为空")
    @ApiModelProperty(value = "协议ID", required = true)
    private String protocolId;


    public String getProtocolId() {
        return protocolId;
    }

    public void setProtocolId(String protocolId) {
        this.protocolId = protocolId;
    }

}
