package cc.crrc.manage.pojo.comm.snowSlide;

import cc.crrc.manage.pojo.ekb.EkbSnowSlideRelationDTO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * 雪崩图的信息传输实体类
 *
 * <AUTHOR>
 * 2020/4/8
 **/
public class SnowSlideGraphPO {
    private String faultTypeKey;
    private boolean enable;
    private String vehicleTypeId;
    private List<SnowSlideNodePO> nodeList;
    private List<SnowSlideEdgePO> edgeList;

    public SnowSlideGraphPO(){
        nodeList = new ArrayList<>();
        edgeList = new ArrayList<>();
    }

    public SnowSlideGraphPO(EkbSnowSlideRelationDTO snowSlideRelationDTO){
        faultTypeKey = snowSlideRelationDTO.getFaultTypeKey();
        enable = snowSlideRelationDTO.getEnable();
        vehicleTypeId = snowSlideRelationDTO.getVehicleTypeId();
        nodeList = new ArrayList<>();
        edgeList = new ArrayList<>();
        String faultSnowSlides = snowSlideRelationDTO.getFaultSnowSlides();
        //当前故障雪崩关系网故障关系数据
        JSONObject json = JSONObject.parseObject(faultSnowSlides);
        JSONObject jsonSource= json.getJSONObject("source");
        //解析json数据为Nodes和Edges两部分JSONArray
        JSONArray jsonNodes = jsonSource.getJSONArray("nodes");
        JSONArray jsonEdges = jsonSource.getJSONArray("edges");
        for (int i = 0; i < jsonNodes.size(); i++) {
            JSONObject nodeObject = jsonNodes.getJSONObject(i);
            nodeList.add(new SnowSlideNodePO(nodeObject));
        }

        for (int i = 0; i < jsonEdges.size(); i++) {
            JSONObject edgeObject = jsonEdges.getJSONObject(i);
            edgeList.add(new SnowSlideEdgePO(edgeObject));
        }
    }

    public String getFaultTypeKey() {
        return faultTypeKey;
    }

    public void setFaultTypeKey(String faultTypeKey) {
        this.faultTypeKey = faultTypeKey;
    }

    public boolean isEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public List<SnowSlideNodePO> getNodeList() { return nodeList; }

    public void setNodeList(List<SnowSlideNodePO> nodeList) {
        this.nodeList = nodeList;
    }

    public List<SnowSlideEdgePO> getEdgeList() { return edgeList; }

    public void setEdgeList(List<SnowSlideEdgePO> edgeList) {
        this.edgeList = edgeList;
    }
}
