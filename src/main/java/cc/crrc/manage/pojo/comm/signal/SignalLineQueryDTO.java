package cc.crrc.manage.pojo.comm.signal;

import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * 2020/3/5
 **/
public class SignalLineQueryDTO {

    @LogParam(description = "车辆编码")
    @NotNull(message = "车辆编码不能为空")
    @ApiModelProperty(value = "车辆编码", example = "SZ5_01", required = true)
    private String vehicleCode;
    
    @LogParam(description = "信号id拼接字符串")
    @NotNull(message = "信号id拼接字符串不能为空")
    @ApiModelProperty(value = "信号id拼接字符串", example = "33929,33784", dataType = "List", required = true)
    private String signalIdList;
    
    @LogParam(description = "查询开始时间")
    @NotNull(message = "开始时间不能为空")
    @ApiModelProperty(value = "查询开始时间 毫秒时间戳 2020/03/05 00:00:00", example = "1583337600000", required = true)
    private Long startTime;
    
    @LogParam(description = "查询结束时间")
    @NotNull(message = "结束时间不能为空")
    @ApiModelProperty(value = "查询结束时间 毫秒时间戳 2020/03/05 01:00:00", example = "1583341200000", required = true)
    private Long endTime;

    @LogParam(description = "抽样标志位")
    @NotNull(message = "抽样标志位不能为空")
    @ApiModelProperty(value = "抽样标志位",example = "false",required = true)
    private Boolean sample;

    @LogParam(description = "抽样时间间隔")
    @ApiModelProperty(value = "抽样时间间隔",example = "5s")
    private String interval;
    @LogParam(description = "线路名称")
    @ApiModelProperty(value = "线路名称")
    private String lineName;
    @LogParam(description = "车辆类型")
    @ApiModelProperty(value = "车辆类型")
    private String vehicleType;

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }
    
    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getSignalIdList() {
        return signalIdList;
    }

    public void setSignalIdList(String signalIdList) {
        this.signalIdList = signalIdList;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public Boolean getSample() {
        return sample;
    }

    public void setSample(Boolean sample) {
        this.sample = sample;
    }

    public String getInterval() {
        return interval;
    }

    public void setInterval(String interval) {
        this.interval = interval;
    }
}
