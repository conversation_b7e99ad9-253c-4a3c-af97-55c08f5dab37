package cc.crrc.manage.pojo.comm.signalgroup;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.PageVO;
import io.swagger.annotations.ApiModelProperty;

/**
 * @author: Guowei
 * 2020-06-08
 */
public class SignalGroupFaultQueryDTO extends PageVO {
    @LogParam(description = "故障代码")
    @ApiModelProperty(value = "故障代码")
    private String faultCode;

    @LogParam(description = "故障名称")
    @ApiModelProperty(value = "故障名称")
    private String faultTypeName;

    @LogParam(description = "车型id")
    @ApiModelProperty(value = "车型id")
    private String vehicleTypeId;

    @LogParam(description = "所属车厢")
    @ApiModelProperty(value = "所属车厢")
    private String location;

    @LogParam(description = "子系统")
    @ApiModelProperty(value = "子系统")
    private String subsystem;


    @LogParam(description = "信号分组id")
    @ApiModelProperty(value = "信号分组id", required = true)
    private String signalGroupId;

    public String getFaultCode() {
        return faultCode;
    }

    public void setFaultCode(String faultCode) {
        this.faultCode = faultCode;
    }

    public String getFaultTypeName() {
        return faultTypeName;
    }

    public void setFaultTypeName(String faultTypeName) {
        this.faultTypeName = faultTypeName;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public String getSignalGroupId() {
        return signalGroupId;
    }

    public void setSignalGroupId(String signalGroupId) {
        this.signalGroupId = signalGroupId;
    }
}
