package cc.crrc.manage.pojo.comm.protocolTypeConfig;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.pojo.BasePO;
import cc.crrc.manage.pojo.PageVO;

@Duplicates({
    @Duplicate(table = "comm_mqtt_topic_type", message = "名称已存在", condition = "name='${name}' and vehicle_type_id='${vehicleTypeId}'", groups = {
            Insert.class}),
    @Duplicate(table = "comm_mqtt_topic_type", message = "名称已存在", condition = "name='${name}' and vehicle_type_id='${vehicleTypeId}' and id!='${id}'", groups = {
            Update.class})
})

public class MqttTopicTypeDTO extends BasePO {
	private static final long serialVersionUID = 1L;

	@NotNull(message = "主题类ID不能为空" ,groups= {Update.class})
	private String id;
	@NotNull(message = "车型ID不能为空" ,groups= {Update.class,Insert.class})
	@LogParam(description="车型ID")
	private String vehicleTypeId;//
	@NotBlank(message = "主题类名称不能为空" ,groups= {Update.class,Insert.class})
	@LogParam(description="主题类名称")
	private String name;
//	@NotBlank(message = "主题类别名不能为空" ,groups= {Update.class,Insert.class})
	@LogParam(description="主题类别名")
	private String alias;//
	@NotBlank(message = "主题类类型不能为空" ,groups= {Update.class,Insert.class})
	@LogParam(description="主题类类型")
	private String type;
	@NotNull(message = "qos不能为空" ,groups= {Update.class,Insert.class})
	@LogParam(description="消息质量")
	private Integer qos;//
	private String remark;
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getVehicleTypeId() {
		return vehicleTypeId;
	}
	public void setVehicleTypeId(String vehicleTypeId) {
		this.vehicleTypeId = vehicleTypeId;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getAlias() {
		return alias;
	}
	public void setAlias(String alias) {
		this.alias = alias;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public Integer getQos() {
		return qos;
	}
	public void setQos(Integer qos) {
		this.qos = qos;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	
}
