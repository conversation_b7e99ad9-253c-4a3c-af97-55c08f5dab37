package cc.crrc.manage.pojo.comm.protocolTypeConfig;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.pojo.BasePO;
import cc.crrc.manage.pojo.PageVO;

@Duplicates({
    @Duplicate(table = "comm_tcp_packet_type", message = "名称已存在", condition = "name='${name}'", groups = {
            Insert.class}),
    @Duplicate(table = "comm_tcp_packet_type", message = "名称已存在", condition = "name='${name}' and id!='${id}'", groups = {
            Update.class})
})

public class TcpPacketTypeDTO extends BasePO {
	private static final long serialVersionUID = 1L;

	@NotNull(message = "数据包ID不能为空" ,groups= {Update.class})
	private String id;
	@NotNull(message = "车型ID不能为空" ,groups= {Update.class,Insert.class})
	@LogParam(description="车型ID")
	private String vehicleTypeId;//
	@NotBlank(message = "数据包名称不能为空" ,groups= {Update.class,Insert.class})
	@LogParam(description="数据包名称")
	private String name;
//	@NotBlank(message = "数据包别名不能为空" ,groups= {Update.class,Insert.class})
	@LogParam(description="数据包别名")
	private String alias;//
//	@NotBlank(message = "有效脚本不能为空" ,groups= {Update.class,Insert.class})
	@LogParam(description="有效脚本")
	private String validScript;
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getVehicleTypeId() {
		return vehicleTypeId;
	}
	public void setVehicleTypeId(String vehicleTypeId) {
		this.vehicleTypeId = vehicleTypeId;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getAlias() {
		return alias;
	}
	public void setAlias(String alias) {
		this.alias = alias;
	}
	public String getValidScript() {
		return validScript;
	}
	public void setValidScript(String validScript) {
		this.validScript = validScript;
	}
	
}
