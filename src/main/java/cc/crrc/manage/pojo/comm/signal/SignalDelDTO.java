package cc.crrc.manage.pojo.comm.signal;

import cc.crrc.manage.common.annotation.Contained;
import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * @FileName SignalDelDTO
 * <AUTHOR>
 * @Date 2019/11/14 19:29
 **/
public class SignalDelDTO {
    @LogParam(description = "协议ID")
    @NotNull(message = "协议不能为空")
    @ApiModelProperty(value = "协议ID", required = true)
    private String protocolId;

    @LogParam(description = "信号类型")
    @Contained(value = {"0", "1"}, message = "信号类型不正确")
    @NotNull(message = "信号型号不能为空")
    @ApiModelProperty(value = "信号类型(0-原始信号，1-扩展信号)", required = true)
    private Integer signalType;

    @LogParam(description = "信号ID")
    @NotBlank(message = "信号ID不能为空")
    @Pattern(regexp = "^[1-9][0-9]*(,[1-9][0-9]*)*$", message = "信号ID不正确")
    @ApiModelProperty(value = "信号ID(多个逗号分隔)", required = true)
    private String signalId;

    public String getProtocolId() {
        return protocolId;
    }

    public void setProtocolId(String protocolId) {
        this.protocolId = protocolId;
    }

    public Integer getSignalType() {
        return signalType;
    }

    public void setSignalType(Integer signalType) {
        this.signalType = signalType;
    }

    public String getSignalId() {
        return signalId;
    }

    public void setSignalId(String signalId) {
        this.signalId = signalId;
    }
}
