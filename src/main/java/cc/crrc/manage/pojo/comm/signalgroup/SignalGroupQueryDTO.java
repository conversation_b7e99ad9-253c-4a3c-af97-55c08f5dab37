package cc.crrc.manage.pojo.comm.signalgroup;

import cc.crrc.manage.common.annotation.Contained;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.PageVO;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @FileName SignalGroupQueryDTO
 * <AUTHOR>
 * @Date 2019/11/20 10:26
 **/
public class SignalGroupQueryDTO extends PageVO {
    @NotNull(message = "信号分组ID不正确")
    @ApiModelProperty(value = "信号分组ID", required = true)
    @LogParam(description = "信号分组ID")
    private String groupId;
    @NotNull(message = "车辆型号ID不正确")
    @ApiModelProperty(value = "车辆型号ID", required = true)
    @LogParam(description = "车辆型号ID")
    private String vehicleTypeId;
    @LogParam(description = "信号名称")
    @ApiModelProperty(value = "信号名称")
    private String name;
    @LogParam(description = "车厢位置")
    @ApiModelProperty(value = "车厢位置")
    private String location;
    @NotBlank(message = "子系统不能为空")
    @LogParam(description = "子系统")
    @ApiModelProperty(value = "子系统", required = true)
    private String subSystem;

    @NotNull(message = "信号类型不能为空")
    @Contained(value = {"0", "1"}, message = "信号类型不正确")
    @ApiModelProperty(value = "信号类型(原始信号-0，扩展信号-1)", required = true)
    @LogParam(description = "信号类型")
    private Integer signalType;

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getSubSystem() {
        return subSystem;
    }

    public void setSubSystem(String subSystem) {
        this.subSystem = subSystem;
    }

    public Integer getSignalType() {
        return signalType;
    }

    public void setSignalType(Integer signalType) {
        this.signalType = signalType;
    }
}
