package cc.crrc.manage.pojo.comm.signalgroup;

import cc.crrc.manage.common.annotation.Contained;
import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @FileName SignalGroupDelRelationPO
 * <AUTHOR> shuangquan
 * @Date 2019/11/20 11:40
 **/
public class SignalGroupDelRelationPO {
    @NotNull( message = "信号分组ID不正确")
    @LogParam(description = "信号分组ID")
    @ApiModelProperty(value = "信号分组ID", required = true)
    private String groupId;

    @Min(value = 0, message = "信号类型不正确")
    @Contained(value = {"0", "1"}, message = "信号类型不正确")
    @LogParam(description = "信号类型")
    @ApiModelProperty(value = "信号类型(0-原始信号，1-扩展信号)", required = true)
    private int signalType;
    @LogParam(description = "信号英文名称")
    @ApiModelProperty(value = "信号英文名称数组", required = true)
    private List<String> signalENNames;


    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public List<String> getSignalENNames() {
        return signalENNames;
    }

    public void setSignalENNames(List<String> signalENNames) {
        this.signalENNames = signalENNames;
    }

    public int getSignalType() {
        return signalType;
    }

    public void setSignalType(int signalType) {
        this.signalType = signalType;
    }
}
