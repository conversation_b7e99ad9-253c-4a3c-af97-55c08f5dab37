package cc.crrc.manage.pojo.comm.signalfavourites;

import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @version 1.0
 * @FileName SignalFavouritesGroupPO
 * <AUTHOR> lei
 * @Date 2021-8-5 11:39:27
 **/
public class SignalFavouritesGroupPO {

    @LogParam(description = "收藏分组id")
    @ApiModelProperty(value = "收藏分组id", required = false)
    private String id;

    @LogParam(description = "收藏名称")
    @ApiModelProperty(value = "收藏名称", required = true)
    private String name;

    @LogParam(description = "车辆型号表的主键id")
    @ApiModelProperty(value = "车辆型号表的主键id", required = true)
    private String vehicleTypeId;

    @LogParam(description = "车辆id")
    @ApiModelProperty(value = "车辆id", required = true)
    private String vehicleId;

    @LogParam(description = "创建收藏的当前用户")
    @ApiModelProperty(value = "创建收藏的当前用户", required = true)
    private String createBy;

    @LogParam(description = "创建收藏的当前时间")
    @ApiModelProperty(value = "创建收藏的当前时间", required = true)
    private long createTime;

    @LogParam(description = "修改收藏的当前用户")
    @ApiModelProperty(value = "修改收藏的当前用户")
    private String modifyBy;

    @LogParam(description = "修改收藏的当前时间")
    @ApiModelProperty(value = "修改收藏的当前时间")
    private long modifyTime;

    @LogParam(description = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

    @LogParam(description = "车辆编码")
    @ApiModelProperty(value = "车辆编码")
    private String vehicleCode;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public long getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(long modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }
}
