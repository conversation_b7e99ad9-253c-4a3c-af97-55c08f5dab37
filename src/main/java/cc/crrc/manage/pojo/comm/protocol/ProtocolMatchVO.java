package cc.crrc.manage.pojo.comm.protocol;

import cc.crrc.manage.common.annotation.Contained;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @FileName ProtocolMatchVO
 * <AUTHOR> shuangquan
 * @Date 2019/11/16 15:14
 **/
public class ProtocolMatchVO {
    @NotNull(message = "车辆ID不能为空")
    @ApiModelProperty(value = "车辆ID", required = true)
    private String vehicleId;
    @ApiModelProperty(hidden = true)
    private String vehicleName;
    @Contained(value = {"0", "1"}, message = "匹配值不正确")
    @ApiModelProperty(value = "匹配值(选中-1，未选中-0)", required = true)
    @NotBlank(message = "匹配值不能为空")
    private String checked;

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getChecked() {
        return checked;
    }

    public void setChecked(String checked) {
        this.checked = checked;
    }

    public String getVehicleName() {
        return vehicleName;
    }

    public void setVehicleName(String vehicleName) {
        this.vehicleName = vehicleName;
    }
}
