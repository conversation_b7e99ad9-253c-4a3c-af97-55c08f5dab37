package cc.crrc.manage.pojo.comm.protocol;

import io.swagger.annotations.ApiModelProperty;

/**
 * @FileName ProtocolVO
 * <AUTHOR> shuangquan
 * @Date 2019/11/12 13:10
 **/
public class ProtocolVO {
    @ApiModelProperty(value = "协议ID")
    private String id;
    private String vehicleTypeId;
    private String vehicleTypeName;
   /* private long packetTypeId;
    private String packetTypeName;*/
    private String name;
    private String version;
    private long size;
    private String endian;
    private int enable;
    private String createBy;
    private int vehicleCount;
    private String lineId;
    private String lineName;
    /*private String commType;*/

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getVehicleTypeName() {
        return vehicleTypeName;
    }

    public void setVehicleTypeName(String vehicleTypeName) {
        this.vehicleTypeName = vehicleTypeName;
    }

    /*public long getPacketTypeId() {
        return packetTypeId;
    }

    public void setPacketTypeId(long packetTypeId) {
        this.packetTypeId = packetTypeId;
    }

    public String getPacketTypeName() {
        return packetTypeName;
    }

    public void setPacketTypeName(String packetTypeName) {
        this.packetTypeName = packetTypeName;
    }*/

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public long getSize() {
        return size;
    }

    public void setSize(long size) {
        this.size = size;
    }

    public String getEndian() {
        return endian;
    }

    public void setEndian(String endian) {
        this.endian = endian;
    }

    public int getEnable() {
        return enable;
    }

    public void setEnable(int enable) {
        this.enable = enable;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public int getVehicleCount() {
        return vehicleCount;
    }

    public void setVehicleCount(int vehicleCount) {
        this.vehicleCount = vehicleCount;
    }

    /*public String getCommType() {
        return commType;
    }

    public void setCommType(String commType) {
        this.commType = commType;
    }*/

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }
}
