package cc.crrc.manage.pojo.comm.signal;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * 2020/3/6
 **/
public class SignalTreeVO {
    private String id;
    private String name;
    private Integer digitalFlag;
    private String nodeId;
    private Integer sortNumber;
    private ArrayList<SignalTreeVO> children;
    @JsonIgnore
    private String location;
    @JsonIgnore
    private String subsystem;
    @JsonIgnore
    private String subsystemName;

    public SignalTreeVO() {
        this.children = new ArrayList<>();
    }

    public SignalTreeVO(JSONObject jsonObject) {
        this.id = jsonObject.getString("id");
        this.name = jsonObject.getString("name");
        this.digitalFlag = jsonObject.getInteger("digitalFlag");
        this.nodeId = jsonObject.getString("nodeId");
        this.children = JSON.parseObject(jsonObject.getString("children"),new TypeReference<ArrayList<SignalTreeVO>>(){});
        this.location = jsonObject.getString("location");
        this.subsystem = jsonObject.getString("subsystem");
        this.subsystemName = jsonObject.getString("subsystemName");
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getDigitalFlag() {
        return digitalFlag;
    }

    public void setDigitalFlag(Integer digitalFlag) {
        this.digitalFlag = digitalFlag;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public String getSubsystemName() {
        return subsystemName;
    }

    public void setSubsystemName(String subsystemName) {
        this.subsystemName = subsystemName;
    }

    public ArrayList<SignalTreeVO> getChildren() {
        return children;
    }

    public void setChildren(ArrayList<SignalTreeVO> children) {
        this.children = children;
    }

    public Integer getSortNumber() {
        return sortNumber;
    }

    public void setSortNumber(Integer sortNumber) {
        this.sortNumber = sortNumber;
    }
}
