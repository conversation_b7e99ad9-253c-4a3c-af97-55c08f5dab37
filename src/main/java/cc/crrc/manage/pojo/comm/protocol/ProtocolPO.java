package cc.crrc.manage.pojo.comm.protocol;

import cc.crrc.manage.common.annotation.Contained;
import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.pojo.BasePO;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * @FileName ProtocolPO
 * <AUTHOR> shuangquan
 * @Date 2019/11/13 10:04
 **/
@Duplicates({
       /* @Duplicate(table = "comm_protocol", condition = "vehicle_type_id=${vehicleTypeId} and " +
                "(tcp_packet_type_id=${commTypeId} or mqtt_topic_type_id=${commTypeId}) and del_flag=0", message =
                "协议数据包类型已存在"),*/
        @Duplicate(table = "comm_protocol", condition = "vehicle_type_id='${vehicleTypeId}' and " +
                " name ='${name}'and del_flag=0", message = "协议名称已存在")
})
public class ProtocolPO extends BasePO {

    @ApiModelProperty(hidden = true)
    private String id;

    @LogParam(description = "车辆型号")
    @NotNull(message = "车辆型号不能为空")
    @ApiModelProperty(value = "车辆型号ID", required = true)
    private String vehicleTypeId;

    /*@NotBlank(message = "车辆型号通信方式不能为空")
    @Contained(value = {"TCP", "MQTT"}, message = "车辆型号通信方式不正确")
    @LogParam(description = "通信方式")
    @ApiModelProperty(value = "通信方式（TCP，MQTT）", required = true)
    private String commType;*/

    @NotBlank(message = "协议名称不能为空")
    @Length(max = 128, message = "协议名称过长")
    @LogParam(description = "协议名称")
    @ApiModelProperty(value = "协议名称", required = true)
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class, Update.class})
    private String name;

    @NotBlank(message = "协议版本不能为空")
    @Pattern(regexp = "^[0-9]+(\\.[0-9]+)+$", message = "协议版本格式不正确")
    @LogParam(description = "协议版本")
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
    @ApiModelProperty(value = "协议版本", required = true)
    private String version;

    @LogParam(description = "数据包大小")
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
    private Integer size;

    @LogParam(description = "开端模式")
    @NotBlank(message = "开端模式不能为空")
    @Contained(value = {"BigEndian", "LittleEndian"}, message = "开端模式不正确")
    @ApiModelProperty(value = "开端模式", required = true)
    private String endian;

   /* @LogParam(description = "通讯类型ID")
    @NotNull(message = "通讯类型ID不能为空")
    @Min(value = 1, message = "通讯类型ID不正确")
    @ApiModelProperty(value = "通讯类型ID", required = true)
    private Long commTypeId;*/

    @LogParam(description = "备注")
    private String remark;

    @LogParam(description = "线路id")
    private String lineId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    /*public String getCommType() {
        return commType;
    }

    public void setCommType(String commType) {
        this.commType = commType;
    }*/

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public String getEndian() {
        return endian;
    }

    public void setEndian(String endian) {
        this.endian = endian;
    }

    /*public Long getCommTypeId() {
        return commTypeId;
    }

    public void setCommTypeId(Long commTypeId) {
        this.commTypeId = commTypeId;
    }*/

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
}
