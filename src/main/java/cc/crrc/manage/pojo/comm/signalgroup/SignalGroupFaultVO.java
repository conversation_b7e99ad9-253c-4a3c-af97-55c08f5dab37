package cc.crrc.manage.pojo.comm.signalgroup;

import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

/**
 * @author: Guowei
 * 2020-06-08
 */
public class SignalGroupFaultVO {
    @LogParam(description = "故障代码key")
    @ApiModelProperty(value = "故障代码key")
    private String faultTypeKey;

    @LogParam(description = "故障中文名称")
    @ApiModelProperty(value = "故障中文名称")
    private String faultTypeNameCn;

    @LogParam(description = "故障英文名称")
    @ApiModelProperty(value = "故障英文名称")
    private String faultTypeNameEn;

    @LogParam(description = "车型id")
    @ApiModelProperty(value = "车型id", required = true)
    private String vehicleTypeId;

    @LogParam(description = "故障代码code")
    @ApiModelProperty(value = "故障代码code")
    private String faultCode;

    @LogParam(description = "车厢")
    @ApiModelProperty(value = "车厢")
    private String location;

    @LogParam(description = "故障系统")
    @ApiModelProperty(value = "故障系统")
    private String subsystem;

    @LogParam(description = "构型编码")
    @ApiModelProperty(value = "构型编码")
    private String vehicleStructureCode;

    @LogParam(description = "故障等级")
    @ApiModelProperty(value = "故障等级")
    private String faultLevel;

    @LogParam(description = "关联标识 0-未选中，1-选中")
    @ApiModelProperty(value = "关联标识")
    private Integer flag;

    @LogParam(description = "信号分组id")
    @ApiModelProperty(value = "信号分组id")
    private String signalGroupId;

    public String getSignalGroupId() {
        return signalGroupId;
    }

    public void setSignalGroupId(String signalGroupId) {
        this.signalGroupId = signalGroupId;
    }

    public String getFaultCode() {
        return faultCode;
    }

    public void setFaultCode(String faultCode) {
        this.faultCode = faultCode;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public String getVehicleStructureCode() {
        return vehicleStructureCode;
    }

    public void setVehicleStructureCode(String vehicleStructureCode) {
        this.vehicleStructureCode = vehicleStructureCode;
    }

    public String getFaultLevel() {
        return faultLevel;
    }

    public void setFaultLevel(String faultLevel) {
        this.faultLevel = faultLevel;
    }

    public String getFaultTypeKey() {
        return faultTypeKey;
    }

    public void setFaultTypeKey(String faultTypeKey) {
        this.faultTypeKey = faultTypeKey;
    }

    public String getFaultTypeNameCn() {
        return faultTypeNameCn;
    }

    public void setFaultTypeNameCn(String faultTypeNameCn) {
        this.faultTypeNameCn = faultTypeNameCn;
    }

    public String getFaultTypeNameEn() {
        return faultTypeNameEn;
    }

    public void setFaultTypeNameEn(String faultTypeNameEn) {
        this.faultTypeNameEn = faultTypeNameEn;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public Integer getFlag() {
        return flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }
}
