package cc.crrc.manage.pojo.comm.protocol;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.NotFound;
import cc.crrc.manage.pojo.BasePO;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * @FileName ProtocolUpgradeDTO
 * <AUTHOR> shuangquan
 * @Date 2019/11/16 11:24
 **/
@NotFound(table = "comm_protocol", condition = "id='${protocolId}' and del_flag=0")
public class ProtocolUpgradeDTO extends BasePO {
    @LogParam(description = "升级协议ID")
    @ApiModelProperty(hidden = true)
    private String id;

    @NotNull(message = "协议ID不正确")
    @LogParam(description = "协议ID")
    @ApiModelProperty(value = "协议ID", required = true)
    private String protocolId;

    @LogParam(description = "协议版本")
    @NotBlank(message = "协议版本不能为空")
    @Pattern(regexp = "^[0-9]+(\\.[0-9]+)+$", message = "协议版本格式不正确")
    @Length(max = 32, message = "版本过长")
    @ApiModelProperty(value = "协议版本", required = true)
    private String version;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProtocolId() {
        return protocolId;
    }

    public void setProtocolId(String protocolId) {
        this.protocolId = protocolId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
