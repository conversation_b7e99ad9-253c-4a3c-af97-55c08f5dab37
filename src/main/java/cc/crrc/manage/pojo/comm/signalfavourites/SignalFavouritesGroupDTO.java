package cc.crrc.manage.pojo.comm.signalfavourites;

import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @version 1.0
 * @FileName SignalFavouritesGroupDTO
 * <AUTHOR> lei
 * @Date 2021-8-5 11:39:27
 **/
public class SignalFavouritesGroupDTO {
    @LogParam(description = "收藏名称")
    @ApiModelProperty(value = "收藏名称", required = true)
    private String name;

    @LogParam(description = "车辆型号表的主键id")
    @ApiModelProperty(value = "车辆型号表的主键id", required = true)
    private String vehicleTypeId;

    @LogParam(description = "车辆id")
    @ApiModelProperty(value = "车辆id", required = true)
    private String vehicleId;

    @LogParam(description = "创建收藏的当前用户")
    @ApiModelProperty(value = "创建收藏的当前用户", required = false)
    private String createBy;

    @ApiModelProperty(value = "创建收藏的当前时间", required = false)
    @LogParam(description = "创建收藏的当前时间")
    private long createTime;

    @LogParam(description = "车辆编码")
    @ApiModelProperty(value = "车辆编码", required = true)
    private String vehicleCode;

    @LogParam(description = "收藏信号列表")
    @ApiModelProperty(value = "收藏信号列表", required = true)
    private List<SignalsFavouritesDTO> signalList;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public List<SignalsFavouritesDTO> getSignalList() {
        return signalList;
    }

    public void setSignalList(List<SignalsFavouritesDTO> signalList) {
        this.signalList = signalList;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }
}
