package cc.crrc.manage.pojo.comm.signalgroup;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.NotFound;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.pojo.BasePO;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @FileName SignalGroupPO
 * <AUTHOR> shuangquan
 * @Date 2019/11/19 14:03
 **/
@Duplicates({
        @Duplicate(table = "comm_signal_group", groups = {Insert.class}, condition = "name='${name}' and " +
                "vehicle_type_id='${vehicleTypeId}'", message = "信号分组已存在"),
        @Duplicate(table = "comm_signal_group", groups = {Update.class}, condition = "name='${name}' and " +
                "vehicle_type_id='${vehicleTypeId}' and id!='${id}'", message = "信号分组已存在")})
@NotFound(table = "comm_signal_group", condition = "vehicle_type_id='${vehicleTypeId}' and id='${id}'", groups =
        {Update.class})
public class SignalGroupPO extends BasePO {
    @LogParam(description = "信号分组ID")
    @NotNull( message = "信号分组ID不正确", groups = {Update.class})
    @ApiModelProperty(value = "信号分组ID（新增不需要）")
    private String id;

    @LogParam(description = "车辆型号ID")
    @NotNull(message = "车辆型号ID不正确", groups = {Insert.class, Update.class})
    @ApiModelProperty(value = "车辆型号ID", required = true)
    private String vehicleTypeId;
    @LogParam(description = "信号分组名称")
    @NotBlank(message = "信号分组名称不能为空", groups = {Insert.class, Update.class})
    @Length(max = 64, message = "信号分组名称过长")
    @ApiModelProperty(value = "信号分组名称", required = true)
    private String name;
    @LogParam(description = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
