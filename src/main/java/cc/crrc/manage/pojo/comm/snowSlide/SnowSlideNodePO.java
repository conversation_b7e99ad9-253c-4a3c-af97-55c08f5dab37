package cc.crrc.manage.pojo.comm.snowSlide;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;

/**
 * 雪崩图中故障节点的实体类
 *
 * <AUTHOR>
 * 2020/4/8
 **/
public class SnowSlideNodePO {
    @J<PERSON>NField(name = "alarmId")
    private String faultTypeKey;
    @JSONField(name = "label")
    private String faultNameCn;
    @JSONField(name = "id")
    private String nodeId;
    private String shape;
    @JSONField(name = "img")
    private String imgStr;

    public SnowSlideNodePO(){

    }

    public SnowSlideNodePO(JSONObject jsonObject){
        faultTypeKey = jsonObject.getString("alarmId");
        faultNameCn = jsonObject.getString("label");
        nodeId = jsonObject.getString("id");
    }

    public String getFaultTypeKey() {
        return faultTypeKey;
    }

    public void setFaultTypeKey(String faultTypeKey) {
        this.faultTypeKey = faultTypeKey;
    }

    public String getFaultNameCn() {
        return faultNameCn;
    }

    public void setFaultNameCn(String faultNameCn) {
        this.faultNameCn = faultNameCn;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) { this.nodeId = nodeId; }

    public String getShape() {
        return shape;
    }

    public void setShape(String shape) {
        this.shape = shape;
    }

    public String getImgStr() { return imgStr; }

    public void setImgStr(String imgStr) { this.imgStr = imgStr; }
}
