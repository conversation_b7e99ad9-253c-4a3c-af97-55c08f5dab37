package cc.crrc.manage.pojo.comm.signalfavourites;

import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @version 1.0
 * @FileName SignalFavouritesGroupVO
 * <AUTHOR> lei
 * @Date 2021-8-5 11:39:27
 **/
public class SignalFavouritesGroupVO {
    @LogParam(description = "收藏分组id")
    @ApiModelProperty(value = "收藏分组id", required = true)
    private String id;

    @LogParam(description = "收藏名称")
    @ApiModelProperty(value = "收藏名称", required = true)
    private String groupName;

    @LogParam(description = "车辆编码")
    @ApiModelProperty(value = "车辆编码", required = true)
    private String vehicleCode;

    @LogParam(description = "收藏信号列表")
    @ApiModelProperty(value = "收藏名称列表", required = true)
    private List<SignalsFavouritesVO> signalsFavouritesVOs;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public List<SignalsFavouritesVO> getSignalsFavouritesVOs() {
        return signalsFavouritesVOs;
    }

    public void setSignalsFavouritesVOs(List<SignalsFavouritesVO> signalsFavouritesVOs) {
        this.signalsFavouritesVOs = signalsFavouritesVOs;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }
}
