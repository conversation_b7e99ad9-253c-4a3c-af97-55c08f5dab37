package cc.crrc.manage.pojo.comm.signalfavourites;

import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

/**
 * @version 1.0
 * @FileName SignalsFavouritesDTO
 * <AUTHOR> lei
 * @Date 2021-8-5 11:39:27
 **/
public class SignalsFavouritesDTO {

    @LogParam(description = "收藏分组id")
    @ApiModelProperty(value = "收藏分组id", required = true)
    private String groupId;

    @LogParam(description = "信号id")
    @ApiModelProperty(value = "信号id", required = true)
    private String signalId;

    @LogParam(description = "节点id")
    @ApiModelProperty(value = "节点id", required = true)
    private String nodeId;

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getSignalId() {
        return signalId;
    }

    public void setSignalId(String signalId) {
        this.signalId = signalId;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public SignalsFavouritesDTO() {

    }

    public SignalsFavouritesDTO(String groupId, String signalId, String nodeId) {
        this.groupId = groupId;
        this.signalId = signalId;
        this.nodeId = nodeId;
    }
}
