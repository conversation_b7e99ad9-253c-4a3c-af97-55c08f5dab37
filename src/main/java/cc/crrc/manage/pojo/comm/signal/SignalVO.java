package cc.crrc.manage.pojo.comm.signal;

/**
 * @FileName SignalVO
 * <AUTHOR> shuangquan
 * @Date 2019/11/13 14:06
 **/
public class SignalVO {
    private int signalType;
    private String id;
    private String protocolId;
    private String nameCN;
    private String nameEN;
    private String byteOffset;
    private String bitOffset;
    private String dataType;
    private String unit;
    private String location;
    private String subsystem;
    private String pasrseScript;
    private String triggerValue;
    private String maxVal;
    private String minVal;
    private String createBy;
    private String vehicleTypeId;
    private String faultCode;
    private String faultNameCN;

    //2020-10-13 杭临线 DDU-RIOM 需要关联查询板卡中信号的线号
    private String number;

    private String nodeId;

    private String digitalFlag;

    public int getSignalType() {
        return signalType;
    }

    public void setSignalType(int signalType) {
        this.signalType = signalType;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProtocolId() {
        return protocolId;
    }

    public void setProtocolId(String protocolId) {
        this.protocolId = protocolId;
    }

    public String getNameCN() {
        return nameCN;
    }

    public void setNameCN(String nameCN) {
        this.nameCN = nameCN;
    }

    public String getNameEN() {
        return nameEN;
    }

    public void setNameEN(String nameEN) {
        this.nameEN = nameEN;
    }

    public String getByteOffset() {
        return byteOffset;
    }

    public void setByteOffset(String byteOffset) {
        this.byteOffset = byteOffset;
    }

    public String getBitOffset() {
        return bitOffset;
    }

    public void setBitOffset(String bitOffset) {
        this.bitOffset = bitOffset;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public String getPasrseScript() {
        return pasrseScript;
    }

    public void setPasrseScript(String pasrseScript) {
        this.pasrseScript = pasrseScript;
    }

    public String getTriggerValue() {
        return triggerValue;
    }

    public void setTriggerValue(String triggerValue) {
        this.triggerValue = triggerValue;
    }

    public String getMaxVal() {
        return maxVal;
    }

    public void setMaxVal(String maxVal) {
        this.maxVal = maxVal;
    }

    public String getMinVal() {
        return minVal;
    }

    public void setMinVal(String minVal) {
        this.minVal = minVal;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getFaultCode() {
        return faultCode;
    }

    public void setFaultCode(String faultCode) {
        this.faultCode = faultCode;
    }

    public String getFaultNameCN() {
        return faultNameCN;
    }

    public void setFaultNameCN(String faultNameCN) {
        this.faultNameCN = faultNameCN;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public String getDigitalFlag() {
        return digitalFlag;
    }

    public void setDigitalFlag(String digitalFlag) {
        this.digitalFlag = digitalFlag;
    }
}
