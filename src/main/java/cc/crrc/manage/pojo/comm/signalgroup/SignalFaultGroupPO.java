package cc.crrc.manage.pojo.comm.signalgroup;

import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @author: Guowei
 * 2020-06-08
 */
public class SignalFaultGroupPO {
    @LogParam(description = "信号分组id")
    @ApiModelProperty(value = "信号分组id", required = true)
    private String signalGroupId;
    @LogParam(description = "故障类型")
    @ApiModelProperty(value = "故障类型", required = true)
    private List<String> faultTypeKey;


    public String getSignalGroupId() {
        return signalGroupId;
    }

    public void setSignalGroupId(String signalGroupId) {
        this.signalGroupId = signalGroupId;
    }

    public List<String> getFaultTypeKey() {
        return faultTypeKey;
    }

    public void setFaultTypeKey(List<String> faultTypeKey) {
        this.faultTypeKey = faultTypeKey;
    }
}
