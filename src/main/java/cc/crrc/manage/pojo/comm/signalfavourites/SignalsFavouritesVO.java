package cc.crrc.manage.pojo.comm.signalfavourites;

import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @version 1.0
 * @FileName SignalsFavouritesVO
 * <AUTHOR> lei
 * @Date 2021-8-5 11:39:27
 **/
public class SignalsFavouritesVO {
    @LogParam(description = "id")
    @ApiModelProperty(value = "id", required = true)
    private String id;

    @LogParam(description = "收藏分组id")
    @ApiModelProperty(value = "收藏分组id", required = true)
    private String groupId;

    @LogParam(description = "收藏分组名称")
    @ApiModelProperty(value = "收藏分组名称", required = true)
    private String groupName;

    @LogParam(description = "信号id")
    @ApiModelProperty(value = "信号id", required = true)
    private String signalId;

    @LogParam(description = "信号名称")
    @ApiModelProperty(value = "信号名称", required = true)
    private String signalName;

    @LogParam(description = "节点id")
    @ApiModelProperty(value = "节点id", required = true)
    private String nodeId;

    private Integer digitalFlag;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getSignalId() {
        return signalId;
    }

    public void setSignalId(String signalId) {
        this.signalId = signalId;
    }

    public String getSignalName() {
        return signalName;
    }

    public void setSignalName(String signalName) {
        this.signalName = signalName;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }

    public Integer getDigitalFlag() {
        return digitalFlag;
    }

    public void setDigitalFlag(Integer digitalFlag) {
        this.digitalFlag = digitalFlag;
    }
}
