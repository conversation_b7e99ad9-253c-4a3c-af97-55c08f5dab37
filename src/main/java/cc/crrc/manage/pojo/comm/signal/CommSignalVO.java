package cc.crrc.manage.pojo.comm.signal;

import java.io.Serializable;

/**
 * @Author: Li <PERSON>
 * @Date: 2019-12-27
 */
public class CommSignalVO implements Serializable {
    private String id;
    private String protocolId;
    private String protocolName;
    private String nameCn;
    private String nameEn;
    private String dataType;
    private String unit;
    private String location;
    private String subsystem;
    private String subsystemName;

    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getProtocolId() {
        return protocolId;
    }
    
    public void setProtocolId(String protocolId) {
        this.protocolId = protocolId;
    }
    
    public String getProtocolName() {
        return protocolName;
    }
    
    public void setProtocolName(String protocolName) {
        this.protocolName = protocolName;
    }
    
    public String getNameCn() {
        return nameCn;
    }
    
    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }
    
    public String getNameEn() {
        return nameEn;
    }
    
    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }
    
    public String getDataType() {
        return dataType;
    }
    
    public void setDataType(String dataType) {
        this.dataType = dataType;
    }
    
    public String getUnit() {
        return unit;
    }
    
    public void setUnit(String unit) {
        this.unit = unit;
    }
    
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
    
    public String getSubsystem() {
        return subsystem;
    }
    
    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }
    
    public String getSubsystemName() {
        return subsystemName;
    }
    
    public void setSubsystemName(String subsystemName) {
        this.subsystemName = subsystemName;
    }
}
