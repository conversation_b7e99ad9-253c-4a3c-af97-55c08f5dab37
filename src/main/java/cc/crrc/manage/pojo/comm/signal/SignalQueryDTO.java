package cc.crrc.manage.pojo.comm.signal;

import cc.crrc.manage.common.annotation.Contained;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.PageVO;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * @FileName SignalQueryDTO
 * <AUTHOR>
 * @Date 2019/11/13 14:19
 **/
public class SignalQueryDTO extends PageVO {
    @LogParam(description = "协议ID")
    @NotNull(message = "协议不能为空")
    @ApiModelProperty(value = "协议ID)", required = true)
    private String protocolId;

    @LogParam(description = "信号类型")
    @Contained(value = {"0", "1"}, message = "信号类型不正确")
    @ApiModelProperty(value = "信号类型（0-原始信号，1-扩展信号）")
    private Integer signalType;

    @LogParam(description = "信号名称")
    @ApiModelProperty(value = "信号名称")
    private String name;
    @LogParam(description = "字节偏移量")
    @ApiModelProperty(value = "字节偏移量")
    private Integer byteOffset;
    @LogParam(description = "子系统")
    @ApiModelProperty(value = "子系统")
    private String subSystem;
    @LogParam(description = "车厢位置")
    @ApiModelProperty(value = "车厢位置")
    private String location;
    @LogParam(description = "故障编码")
    @ApiModelProperty(value = "故障编码")
    private String faultCode;

    public String getProtocolId() {
        return protocolId;
    }

    public void setProtocolId(String protocolId) {
        this.protocolId = protocolId;
    }

    public Integer getSignalType() {
        return signalType;
    }

    public void setSignalType(Integer signalType) {
        this.signalType = signalType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getByteOffset() {
        return byteOffset;
    }

    public void setByteOffset(Integer byteOffset) {
        this.byteOffset = byteOffset;
    }

    public String getSubSystem() {
        return subSystem;
    }

    public void setSubSystem(String subSystem) {
        this.subSystem = subSystem;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getFaultCode() {
        return faultCode;
    }

    public void setFaultCode(String faultCode) {
        this.faultCode = faultCode;
    }
}
