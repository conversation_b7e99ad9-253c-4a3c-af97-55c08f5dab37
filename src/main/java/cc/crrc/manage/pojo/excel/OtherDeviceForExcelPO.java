package cc.crrc.manage.pojo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

public class OtherDeviceForExcelPO implements Serializable {

    private static final long serialVersionUID = -3509349687369136728L;

    @ExcelIgnore
    private String id;
    @NotNull
    @Excel(name = "列车编号" ,isImportField = "vehicleCode", orderNum = "0")
    private String vehicleCode;
    @NotNull
    @Excel(name = "设备类型" ,isImportField = "typeCn", orderNum = "2")
    private String typeCn;
    @NotNull
    @Excel(name = "设备名称" ,isImportField = "nameCn", orderNum = "1")
    private String nameCn;
    @NotNull
    @Excel(name = "设备位置" ,isImportField = "location", orderNum = "4")
    private String location;
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "起始日期" ,isImportField = "changeTime", orderNum = "5", format = "yyyy-MM-dd")
    private Date changeTime;
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "有效日期" ,isImportField = "warningTime", orderNum = "6", format = "yyyy-MM-dd")
    private Date warningTime;
    @NotNull
    @Excel(name = "百分比" ,isImportField = "percent", orderNum = "7")
    private String percent;
    @NotNull
    @Excel(name = "车厢" ,isImportField = "carriage", orderNum = "3")
    private String carriage;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getTypeCn() {
        return typeCn;
    }

    public void setTypeCn(String typeCn) {
        this.typeCn = typeCn;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Date getChangeTime() {
        return changeTime;
    }

    public void setChangeTime(Date changeTime) {
        this.changeTime = changeTime;
    }

    public Date getWarningTime() {
        return warningTime;
    }

    public void setWarningTime(Date warningTime) {
        this.warningTime = warningTime;
    }

    public String getPercent() {
        return percent;
    }

    public void setPercent(String percent) {
        this.percent = percent;
    }

    public String getCarriage() {
        return carriage;
    }

    public void setCarriage(String carriage) {
        this.carriage = carriage;
    }
}
