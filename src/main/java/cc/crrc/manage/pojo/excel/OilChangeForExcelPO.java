package cc.crrc.manage.pojo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

public class OilChangeForExcelPO implements Serializable {

    private static final long serialVersionUID = 5086762855590737519L;

    @ExcelIgnore
    private String id;
    @NotNull
    @Excel(name = "列车编号" ,isImportField = "vehicleCode", orderNum = "0")
    private String vehicleCode;
    @NotNull
    @Excel(name = "部件类型" ,isImportField = "typeCn", orderNum = "2")
    private String typeCn;
    @NotNull
    @Excel(name = "部件名称" ,isImportField = "nameCn", orderNum = "1")
    private String nameCn;
    @NotNull
    @Excel(name = "车厢" ,isImportField = "location", orderNum = "3")
    private String location;
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "更换时间" ,isImportField = "changeTime", orderNum = "4", format = "yyyy-MM-dd")
    private Date changeTime;
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "预警时间" ,isImportField = "warningTime", orderNum = "5", format = "yyyy-MM-dd")
    private Date warningTime;
    @NotNull
    @Excel(name = "时间百分比" ,isImportField = "percent", orderNum = "6")
    private String percent;
    @Excel(name = "里程百分比" ,isImportField = "mileagePercent", orderNum = "9")
    private String mileagePercent;
    @Excel(name = "运行里程" ,isImportField = "runningMileage", orderNum = "7")
    private Long runningMileage;
    @Excel(name = "预警里程" ,isImportField = "warningMileage", orderNum = "8")
    private Long warningMileage;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getTypeCn() {
        return typeCn;
    }

    public void setTypeCn(String typeCn) {
        this.typeCn = typeCn;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Date getChangeTime() {
        return changeTime;
    }

    public void setChangeTime(Date changeTime) {
        this.changeTime = changeTime;
    }

    public Date getWarningTime() {
        return warningTime;
    }

    public void setWarningTime(Date warningTime) {
        this.warningTime = warningTime;
    }

    public String getPercent() {
        return percent;
    }

    public void setPercent(String percent) {
        this.percent = percent;
    }

    public String getMileagePercent() {
        return mileagePercent;
    }

    public void setMileagePercent(String mileagePercent) {
        this.mileagePercent = mileagePercent;
    }

    public Long getRunningMileage() {
        return runningMileage;
    }

    public void setRunningMileage(Long runningMileage) {
        this.runningMileage = runningMileage;
    }

    public Long getWarningMileage() {
        return warningMileage;
    }

    public void setWarningMileage(Long warningMileage) {
        this.warningMileage = warningMileage;
    }
}
