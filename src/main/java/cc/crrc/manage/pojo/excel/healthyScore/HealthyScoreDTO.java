package cc.crrc.manage.pojo.excel.healthyScore;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.sql.Date;

public class HealthyScoreDTO {
    @Excel(name = "时间",  orderNum = "1",width = 20)
    private Date time;
//    @Excel(name = "车辆",  orderNum = "1",width = 20)
//    private String vehicleCode;
    @Excel(name = "ATC系统",  orderNum = "1",width = 20)
    private String ATC;
    @Excel(name = "LCU系统",  orderNum = "1",width = 20)
    private String LCU;
;    @Excel(name = "RIOM系统",  orderNum = "1",width = 20)
    private String RIOM;
    @Excel(name = "VCU-DDU",  orderNum = "1",width = 20)
    private String VCU;
    @Excel(name = "乘客信息系统",  orderNum = "1",width = 20)
    private String PIS;
    @Excel(name = "制动系统",  orderNum = "1",width = 20)
    private String BCU;
    @Excel(name = "弓网系统",  orderNum = "1",width = 20)
    private String PCMU;
    @Excel(name = "火灾系统",  orderNum = "1",width = 20)
    private String FAU;
    @Excel(name = "牵引系统",  orderNum = "1",width = 20)
    private String DCU;
    @Excel(name = "空调系统",  orderNum = "1",width = 20)
    private String HVAC;
    @Excel(name = "能耗记录仪系统",  orderNum = "1",width = 20)
    private String ECR;
    @Excel(name = "蓄电池检测系统",  orderNum = "1",width = 20)
    private String BMS;
    @Excel(name = "走行部系统",  orderNum = "1",width = 20)
    private String BDS;
    @Excel(name = "车门系统",  orderNum = "1",width = 20)
    private String EDCU;
    @Excel(name = "轨道几何&线路巡检系统",  orderNum = "1",width = 20)
    private String TLDS;
    @Excel(name = "辅助系统",  orderNum = "1",width = 20)
    private String SIV;
    @Excel(name = "障碍物检测系统",  orderNum = "1",width = 20)
    private String ODS;
    @Excel(name = "驾驶系统",  orderNum = "1",width = 20)
    private String DRV;

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public String getATC() {
        return ATC;
    }

    public void setATC(String ATC) {
        this.ATC = ATC;
    }

    public String getLCU() {
        return LCU;
    }

    public void setLCU(String LCU) {
        this.LCU = LCU;
    }

    public String getRIOM() {
        return RIOM;
    }

    public void setRIOM(String RIOM) {
        this.RIOM = RIOM;
    }

    public String getVCU() {
        return VCU;
    }

    public void setVCU(String VCU) {
        this.VCU = VCU;
    }

    public String getPIS() {
        return PIS;
    }

    public void setPIS(String PIS) {
        this.PIS = PIS;
    }

    public String getBCU() {
        return BCU;
    }

    public void setBCU(String BCU) {
        this.BCU = BCU;
    }

    public String getPCMU() {
        return PCMU;
    }

    public void setPCMU(String PCMU) {
        this.PCMU = PCMU;
    }

    public String getFAU() {
        return FAU;
    }

    public void setFAU(String FAU) {
        this.FAU = FAU;
    }

    public String getDCU() {
        return DCU;
    }

    public void setDCU(String DCU) {
        this.DCU = DCU;
    }

    public String getHVAC() {
        return HVAC;
    }

    public void setHVAC(String HVAC) {
        this.HVAC = HVAC;
    }

    public String getECR() {
        return ECR;
    }

    public void setECR(String ECR) {
        this.ECR = ECR;
    }

    public String getBMS() {
        return BMS;
    }

    public void setBMS(String BMS) {
        this.BMS = BMS;
    }

    public String getBDS() {
        return BDS;
    }

    public void setBDS(String BDS) {
        this.BDS = BDS;
    }

    public String getEDCU() {
        return EDCU;
    }

    public void setEDCU(String EDCU) {
        this.EDCU = EDCU;
    }

    public String getTLDS() {
        return TLDS;
    }

    public void setTLDS(String TLDS) {
        this.TLDS = TLDS;
    }

    public String getSIV() {
        return SIV;
    }

    public void setSIV(String SIV) {
        this.SIV = SIV;
    }

    public String getODS() {
        return ODS;
    }

    public void setODS(String ODS) {
        this.ODS = ODS;
    }

    public String getDRV() {
        return DRV;
    }

    public void setDRV(String DRV) {
        this.DRV = DRV;
    }
}
