package cc.crrc.manage.pojo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * @FileName StruVehicleTypeStructureForExcelPO
 * <AUTHOR> xin
 * @Date 2020/4/20 9:05
 * @Version 1.0
 **/
public class StruVehicleTypeStructureForExcelPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelIgnore
    private String id;
    @NotNull
    @Excel(name = "车辆型号" ,isImportField = "vehicle_type_id", orderNum = "2")
    private String vehicleTypeId;
    @Excel(name = "父结构编码" ,isImportField = "parent_structure_code", orderNum = "3")
    private String parentStructureCode;
    @NotNull
    @Excel(name = "结构编码" ,isImportField = "structure_code", orderNum = "4")
    private String structureCode;
    @NotNull
    @Excel(name = "结构中文名称" ,isImportField = "name_cn", orderNum = "5")
    private String nameCn;
    @Excel(name = "结构英文名称" ,isImportField = "name_en", orderNum = "6")
    private String nameEn;
    @NotNull
    @Excel(name = "结构英文名称缩写" ,isImportField = "short_name_en", orderNum = "7")
    private String shortNameEn;
    @Excel(name = "父节点集合，用构型英文缩写" ,isImportField = "structure_position", orderNum = "8")
    private String structurePosition;
    @Excel(name = "结构类型，车辆/车厢/子系统/部件/位置" ,isImportField = "structure_type", orderNum = "9")
    private String structureType;
    @Excel(name = "部件型号表的主键（id）" ,isImportField = "component_type_id", orderNum = "10")
    private String componentTypeId;
    @Excel(name = "顺序编号" ,isImportField = "sort_number", orderNum = "11")
    private Long sortNumber;
    @Excel(name = "备注" ,isImportField = "remark", orderNum = "12")
    private String remark;

    @ExcelIgnore
    private Date modifyTime;
    @ExcelIgnore
    private String createBy;
    @ExcelIgnore
    private Date createTime;
    @ExcelIgnore
    private String modifyBy;
    @ExcelIgnore
    private Integer delFlag;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getParentStructureCode() {
        return parentStructureCode;
    }

    public void setParentStructureCode(String parentStructureCode) {
        this.parentStructureCode = parentStructureCode;
    }

    public String getStructureCode() {
        return structureCode;
    }

    public void setStructureCode(String structureCode) {
        this.structureCode = structureCode;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getShortNameEn() {
        return shortNameEn;
    }

    public void setShortNameEn(String shortNameEn) {
        this.shortNameEn = shortNameEn;
    }

    public String getStructurePosition() {
        return structurePosition;
    }

    public void setStructurePosition(String structurePosition) {
        this.structurePosition = structurePosition;
    }

    public String getStructureType() {
        return structureType;
    }

    public void setStructureType(String structureType) {
        this.structureType = structureType;
    }

    public String getComponentTypeId() {
        return componentTypeId;
    }

    public void setComponentTypeId(String componentTypeId) {
        this.componentTypeId = componentTypeId;
    }

    public Long getSortNumber() {
        return sortNumber;
    }

    public void setSortNumber(Long sortNumber) {
        this.sortNumber = sortNumber;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }
}
