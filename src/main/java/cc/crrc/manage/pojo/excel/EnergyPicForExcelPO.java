package cc.crrc.manage.pojo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;

import javax.validation.constraints.NotNull;

public class EnergyPicForExcelPO
{

    private static final long serialVersionUID = 1L;

    @NotNull
    @Excel(name = "能耗类型" ,isImportField = "name", orderNum = "0",width =15)
    private String name;
    @NotNull
    @Excel(name = "能耗值(kw‧h)" ,isImportField = "value", orderNum = "1",width =15)
    private String value;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
