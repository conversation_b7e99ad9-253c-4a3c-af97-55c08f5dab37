package cc.crrc.manage.pojo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

public class TotalEnergyTrendForExcelPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull
    @Excel(name = "时间" ,isImportField = "time", orderNum = "0",width=20)
    private String time;
    @NotNull
    @Excel(name = "总能耗(kw‧h)" ,isImportField = "accumlation", orderNum = "1",width=20)
    private String accumlation;

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getAccumlation() {
        return accumlation;
    }

    public void setAccumlation(String accumlation) {
        this.accumlation = accumlation;
    }
}
