package cc.crrc.manage.pojo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

public class WashVehicleForExcelPO implements Serializable {

    private static final long serialVersionUID = 4455625455067819925L;

    @ExcelIgnore
    private String id;
    @NotNull
    @Excel(name = "列车编号" ,isImportField = "vehicleCode", orderNum = "0")
    private String vehicleCode;
    @NotNull
    @Excel(name = "洗车模式" ,isImportField = "modeCn", orderNum = "1")
    private String modeCn;
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "洗车时间" ,isImportField = "changeTime", orderNum = "2", format = "yyyy-MM-dd")
    private Date changeTime;
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "预警时间" ,isImportField = "warningTime", orderNum = "3", format = "yyyy-MM-dd")
    private Date warningTime;
    @NotNull
    @Excel(name = "百分比" ,isImportField = "percent", orderNum = "4")
    private String percent;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getModeCn() {
        return modeCn;
    }

    public void setModeCn(String modeCn) {
        this.modeCn = modeCn;
    }

    public Date getChangeTime() {
        return changeTime;
    }

    public void setChangeTime(Date changeTime) {
        this.changeTime = changeTime;
    }

    public Date getWarningTime() {
        return warningTime;
    }

    public void setWarningTime(Date warningTime) {
        this.warningTime = warningTime;
    }

    public String getPercent() {
        return percent;
    }

    public void setPercent(String percent) {
        this.percent = percent;
    }
}
