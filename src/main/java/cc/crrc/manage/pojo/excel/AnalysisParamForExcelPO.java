package cc.crrc.manage.pojo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import java.io.Serializable;
import javax.validation.constraints.NotNull;

    public class AnalysisParamForExcelPO implements Serializable {
        private static final long serialVersionUID = 1L;

        @NotNull
        @Excel(name = "时间" , orderNum = "1",width=20)
        private String time;
        @Excel(name = "辅助能耗(kw‧h)",  orderNum = "2",width = 20)
        private String cudDisegy;
        @Excel(name = "牵引能耗(kw‧h)",  orderNum = "3",width = 20)
        private String cudProegy;
        @Excel(name = "再生能耗(kw‧h)",  orderNum = "4",width = 20)
        private String cudRegegy;


        public String getTime() {
            return time;
        }

        public void setTime(String time) {
            this.time = time;
        }

        public String getCudDisegy() {
            return cudDisegy;
        }

        public void setCudDisegy(String cudDisegy) {
            this.cudDisegy = cudDisegy;
        }

        public String getCudProegy() {
            return cudProegy;
        }

        public void setCudProegy(String cudProegy) {
            this.cudProegy = cudProegy;
        }

        public String getCudRegegy() {
            return cudRegegy;
        }

        public void setCudRegegy(String cudRegegy) {
            this.cudRegegy = cudRegegy;
        }
    }
