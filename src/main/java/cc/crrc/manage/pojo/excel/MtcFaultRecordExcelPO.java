package cc.crrc.manage.pojo.excel;

import cc.crrc.manage.common.annotation.LogParam;
import cn.afterturn.easypoi.excel.annotation.Excel;

import java.util.Date;

public class MtcFaultRecordExcelPO {

    @LogParam(description = "故障名称")
    @Excel(name = "故障名称",  orderNum = "1")
    private String faultNameCn;

    @Excel(name = "故障发生时间",  orderNum = "2" ,format = "yyyy-MM-dd HH:mm:ss.SSS")
    private Date startTime;

    @Excel(name = "车辆编码",  orderNum = "3")
    private String vehicleCode;

    @LogParam(description = "故障位置")
    @Excel(name = "故障位置",  orderNum = "4")
    private String location;

    @LogParam(description = "故障系统")
    @Excel(name = "故障系统",  orderNum = "5")
    private String subsystem;

    @Excel(name = "故障等级",  orderNum = "6")
    private String faultLevel;

    @Excel(name = "确认状态",  orderNum = "7")
    private String confirm;

    @Excel(name = "系统负责人",  orderNum = "8")
    private String sysResponsibleBy;

    @Excel(name = "处理人",  orderNum = "9")
    private String treatmentBy;

    @Excel(name = "处理单位",  orderNum = "10")
    private String treatmentCompany;

    @Excel(name = "处理时间", format = "yyyy-MM-dd HH:mm:ss.SSS",  orderNum = "11")
    private Date treatmentTime;

    @Excel(name = "故障类型",  orderNum = "12")
    private String faultSource;

    public void setFaultNameCn(String faultNameCn) {
        this.faultNameCn = faultNameCn;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public void setFaultLevel(String faultLevel) {
        this.faultLevel = faultLevel;
    }

    public void setConfirm(String confirm) {
        this.confirm = confirm;
    }

    public void setSysResponsibleBy(String sysResponsibleBy) {
        this.sysResponsibleBy = sysResponsibleBy;
    }

    public void setTreatmentBy(String treatmentBy) {
        this.treatmentBy = treatmentBy;
    }

    public void setTreatmentCompany(String treatmentCompany) {
        this.treatmentCompany = treatmentCompany;
    }

    public void setTreatmentTime(Date treatmentTime) {
        this.treatmentTime = treatmentTime;
    }

    public void setFaultSource(String faultSource) {
        this.faultSource = faultSource;
    }

    public String getFaultNameCn() {
        return faultNameCn;
    }

    public Date getStartTime() {
        return startTime;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public String getLocation() {
        return location;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public String getFaultLevel() {
        return faultLevel;
    }

    public String getConfirm() {
        return confirm;
    }

    public String getSysResponsibleBy() {
        return sysResponsibleBy;
    }

    public String getTreatmentBy() {
        return treatmentBy;
    }

    public String getTreatmentCompany() {
        return treatmentCompany;
    }

    public Date getTreatmentTime() {
        return treatmentTime;
    }

    public String getFaultSource() {
        return faultSource;
    }
}
