package cc.crrc.manage.pojo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

public class LineDistanceForExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull
    @Excel(name = "线路" ,isImportField = "trainId", orderNum = "0",width =15)
    private String trainId;

    @NotNull
    @Excel(name = "总里程(km)" ,isImportField = "totalDistance", orderNum = "1",width =15)
    private String totalDistance;

    public String getTrainId() {
        return trainId;
    }

    public void setTrainId(String trainId) {
        this.trainId = trainId;
    }

    public String getTotalDistance() {
        return totalDistance;
    }

    public void setTotalDistance(String totalDistance) {
        this.totalDistance = totalDistance;
    }
}
