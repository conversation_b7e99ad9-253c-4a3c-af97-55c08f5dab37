package cc.crrc.manage.pojo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * @FileName EkbFaultTypeForExcelPO
 * <AUTHOR>
 * @Date 2020年4月23日
 * @Version 1.0
 **/
public class EkbFaultTypeForExcelPO implements Serializable, IExcelModel, IExcelDataModel {

    private static final long serialVersionUID = 1L;

    @ExcelIgnore
    private String id;

    @Excel(name = "线路" ,isImportField = "lineId", orderNum = "2")
    @NotNull(message = "线路不能为空")
    private String lineId;

    @Excel(name = "车辆型号" ,isImportField = "vehicle_type_id", orderNum = "3")
    @NotNull(message = "车辆型号不能为空")
    private String vehicleTypeId;

    @Excel(name = "故障类型主键" ,isImportField = "fault_type_key", orderNum = "4",isColumnHidden = true)
    @NotNull(message = "故障类型主键不能为空")
    private String faultTypeKey;

    @Excel(name = "车辆构型编码" ,isImportField = "vehicle_structure_code", orderNum = "5")
    private String vehicleStructureCode;


    @Excel(name = "故障中文名称" ,isImportField = "name_cn", orderNum = "6")
    @NotNull(message = "故障中文名称不能为空")
    private String nameCn;


    @Excel(name = "故障英文名称" ,isImportField = "name_en", orderNum = "7")
    @NotNull(message = "故障英文名称不能为空")
    private String nameEn;


    @Excel(name = "故障编码" ,isImportField = "fault_code", orderNum = "8")
    @NotNull(message = "故障编码不能为空")
    private String faultCode;


    @Excel(name = "故障级别" ,isImportField = "fault_level", orderNum = "9")
    @NotNull(message = "故障级别不能为空")
    private Integer faultLevel;

    @Excel(name = "故障所属子系统" ,isImportField = "subsystem", orderNum = "10")
    private String subsystem;

    @ExcelIgnore
    private Date modifyTime;

    @ExcelIgnore
    private String createBy;

    @ExcelIgnore
    private Date createTime;

    @ExcelIgnore
    private String modifyBy;

    @Excel(name = "故障描述" ,isImportField = "description", orderNum = "11")
    private String description;

    @Excel(name = "故障所属车厢" ,isImportField = "location", orderNum = "12")
    private String location;

    private boolean enable;

    @Excel(name = "应急措施json" ,isImportField = "measure_json", orderNum = "13")
    private String measureJson;

    @Excel(name = "正线状态建议" ,isImportField = "frontline_disposal_recommendations", orderNum = "14")//2021-05-11
    private String frontlineDisposalRecommendations;

    @Excel(name = "检修建议" ,isImportField = "overhaul_suggestions", orderNum = "15")
    private String overhaulSuggestions;

    @Excel(name = "故障类型" ,isImportField = "fault_category", orderNum = "16")
    private String faultCategory;

    @Excel(name = "故障原因",isImportField = "fault_reason",orderNum = "17")
    private String faultReason;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getFaultReason() {
        return faultReason;
    }

    public void setFaultReason(String faultReason) {
        this.faultReason = faultReason;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getFaultTypeKey() {
        return faultTypeKey;
    }

    public void setFaultTypeKey(String faultTypeKey) {
        this.faultTypeKey = faultTypeKey;
    }

    public String getVehicleStructureCode() {
        return vehicleStructureCode;
    }

    public void setVehicleStructureCode(String vehicleStructureCode) {
        this.vehicleStructureCode = vehicleStructureCode;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getFaultCode() {
        return faultCode;
    }

    public void setFaultCode(String faultCode) {
        this.faultCode = faultCode;
    }

    public Integer getFaultLevel() {
        return faultLevel;
    }

    public void setFaultLevel(Integer faultLevel) {
        this.faultLevel = faultLevel;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public boolean isEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public String getMeasureJson() {
        return measureJson;
    }

    public void setMeasureJson(String measureJson) {
        this.measureJson = measureJson;
    }

    public String getFrontlineDisposalRecommendations() {
        return frontlineDisposalRecommendations;
    }

    public void setFrontlineDisposalRecommendations(String frontlineDisposalRecommendations) {
        this.frontlineDisposalRecommendations = frontlineDisposalRecommendations;
    }

    public String getOverhaulSuggestions() {
        return overhaulSuggestions;
    }

    public void setOverhaulSuggestions(String overhaulSuggestions) {
        this.overhaulSuggestions = overhaulSuggestions;
    }

    public String getFaultCategory() {
        return faultCategory;
    }

    public void setFaultCategory(String faultCategory) {
        this.faultCategory = faultCategory;
    }

    public EkbFaultTypeForExcelPO() {
    }

    public EkbFaultTypeForExcelPO(String id, String lineId, String vehicleTypeId, String faultTypeKey, String vehicleStructureCode, String nameCn, String nameEn, String faultCode, Integer faultLevel, String subsystem, Date modifyTime, String createBy, Date createTime, String modifyBy, String description, String location, boolean enable, String measureJson, String frontlineDisposalRecommendations, String overhaulSuggestions, String faultCategory, String faultReason) {
        this.id = id;
        this.lineId = lineId;
        this.vehicleTypeId = vehicleTypeId;
        this.faultTypeKey = faultTypeKey;
        this.vehicleStructureCode = vehicleStructureCode;
        this.nameCn = nameCn;
        this.nameEn = nameEn;
        this.faultCode = faultCode;
        this.faultLevel = faultLevel;
        this.subsystem = subsystem;
        this.modifyTime = modifyTime;
        this.createBy = createBy;
        this.createTime = createTime;
        this.modifyBy = modifyBy;
        this.description = description;
        this.location = location;
        this.enable = enable;
        this.measureJson = measureJson;
        this.frontlineDisposalRecommendations = frontlineDisposalRecommendations;
        this.overhaulSuggestions = overhaulSuggestions;
        this.faultCategory = faultCategory;
        this.faultReason = faultReason;
    }
    //错误信息
    private String errorMsg;

    //错误行号
    private int rowNum;


    @Override
    public int getRowNum() {
        return rowNum;
    }

    @Override
    public void setRowNum(int rowNum) {
        this.rowNum = rowNum;
    }

    @Override
    public String getErrorMsg() {
        return errorMsg;
    }

    @Override
    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}
