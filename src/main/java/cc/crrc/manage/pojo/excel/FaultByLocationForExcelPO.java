package cc.crrc.manage.pojo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

public class FaultByLocationForExcelPO implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull
    @Excel(name = "故障所属车厢" ,isImportField = "location", orderNum = "0",width = 30)
    private String location;

    @NotNull
    @Excel(name = "故障数量" ,isImportField = "location", orderNum = "1",width = 30)
    private String count;

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }
}
