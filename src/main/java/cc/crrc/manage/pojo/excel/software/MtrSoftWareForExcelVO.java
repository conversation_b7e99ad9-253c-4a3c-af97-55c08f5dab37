package cc.crrc.manage.pojo.excel.software;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;

import java.util.Date;

public class MtrSoftWareForExcelVO {
    @ExcelIgnore
    private String id;
    @Excel(name = "软件名称", isImportField = "name", orderNum = "0", width = 20)
    private String name;
    @Excel(name = "软件版本号", isImportField = "version", orderNum = "6", width = 10)
    private String version;
    @Excel(name = "描述", isImportField = "description", orderNum = "7", width = 32)
    private String description;
    @Excel(name = "构型编码", isImportField = "vehicle_structure_code", orderNum = "5", width = 20)
    private String vehicleStructureCode;
    @Excel(name = "车辆编号", isImportField = "vehicle_code", orderNum = "4", width = 10)
    private String vehicleCode;
    @Excel(name = "软件生效时间", isImportField = "effective_time", orderNum = "8", width = 20, exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date effectiveTime;//生效日期
    @Excel(name = "更新时间", isImportField = "modify_time", orderNum = "9", width = 20, exportFormat = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime;
    @Excel(name = "软件英文名", isImportField = "signal_name_en", orderNum = "1", width = 30)
    private String signalNameEn;
    @Excel(name = "更新人", isImportField = "operator", orderNum = "10", width = 10)
    private String operator;//操作者
    @Excel(name = "线路", isImportField = "lineName", orderNum = "2", width = 10)
    private String lineName;
    @Excel(name = "车型", isImportField = "vehicleTypeName", orderNum = "3", width = 15)
    private String vehicleTypeName;
    @ExcelIgnore
    private String endTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }


    public String getVehicleStructureCode() {
        return vehicleStructureCode;
    }

    public void setVehicleStructureCode(String vehicleStructureCode) {
        this.vehicleStructureCode = vehicleStructureCode;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public Date getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(Date effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getSignalNameEn() {
        return signalNameEn;
    }

    public void setSignalNameEn(String signalNameEn) {
        this.signalNameEn = signalNameEn;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }


    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }


    public String getVehicleTypeName() {
        return vehicleTypeName;
    }

    public void setVehicleTypeName(String vehicleTypeName) {
        this.vehicleTypeName = vehicleTypeName;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
