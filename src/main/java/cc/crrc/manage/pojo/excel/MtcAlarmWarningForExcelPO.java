package cc.crrc.manage.pojo.excel;

import cc.crrc.manage.common.annotation.LogParam;
import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

public class MtcAlarmWarningForExcelPO {

    @LogParam(description = "故障名称")
    @Excel(name = "故障名称",  orderNum = "1")
    private String faultNameCn;

    @Excel(name = "故障代码",  orderNum = "2")
    private String faultCode;

    @Excel(name = "故障等级",  orderNum = "3")
    private String faultLevel;

    @Excel(name = "车辆编码",  orderNum = "4")
    private String vehicleCode;

    @LogParam(description = "故障位置")
    @Excel(name = "故障位置", orderNum = "5")
    private String location;

    @LogParam(description = "故障系统")
    @Excel(name = "故障系统", orderNum = "6")
    private String subsystem;

    @Excel(name = "正线状态", orderNum = "7")
    private String runningStatus;

    @Excel(name = "发生时间", orderNum = "8", format = "yyyy-MM-dd HH:mm:ss.SSS")
    private Date startTime;

    @Excel(name = "结束时间", orderNum = "9", format = "yyyy-MM-dd HH:mm:ss.SSS")
    private Date endTime;

    @Excel(name = "故障类型",  orderNum = "10")
    private String faultSource;

    @Excel(name = "故障英文名",  orderNum = "11")
    private String faultNameEn;

    public String getFaultNameCn() {
        return faultNameCn;
    }

    public void setFaultNameCn(String faultNameCn) {
        this.faultNameCn = faultNameCn;
    }

    public String getFaultCode() {
        return faultCode;
    }

    public void setFaultCode(String faultCode) {
        this.faultCode = faultCode;
    }

    public String getFaultLevel() {
        return faultLevel;
    }

    public void setFaultLevel(String faultLevel) {
        this.faultLevel = faultLevel;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public String isRunningStatus() {
        return runningStatus;
    }

    public void setRunningStatus(String runningStatus) {
        this.runningStatus = runningStatus;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getFaultSource() {
        return faultSource;
    }

    public void setFaultSource(String faultSource) {
        this.faultSource = faultSource;
    }

    public String getFaultNameEn() {
        return faultNameEn;
    }

    public void setFaultNameEn(String faultNameEn) {
        this.faultNameEn = faultNameEn;
    }
}
