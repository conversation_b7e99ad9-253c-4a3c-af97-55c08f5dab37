package cc.crrc.manage.pojo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

public class LineAllExcelPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull
    @Excel(name = "线路" ,isImportField = "trainId", orderNum = "0",width =15)
    private String trainId;

    @NotNull
    @Excel(name = "总能耗(kw‧h)" ,isImportField = "totalEngy", orderNum = "1",width =15)
    private String totalEngy;

    @NotNull
    @Excel(name = "牵引能耗(kw‧h)" ,isImportField = "proEngy", orderNum = "2",width =15)
    private String proEngy;

    @NotNull
    @Excel(name = "辅助能耗(kw‧h)" ,isImportField = "aceEngy", orderNum = "3",width =15)
    private String aceEngy;

    @NotNull
    @Excel(name = "再生能耗(kw‧h)" ,isImportField = "aceEngy", orderNum = "4",width =15)
    private String regEngy;



    public String getTotalEngy() {
        return totalEngy;
    }

    public void setTotalEngy(String totalEngy) {
        this.totalEngy = totalEngy;
    }

    public String getProEngy() {
        return proEngy;
    }

    public void setProEngy(String proEngy) {
        this.proEngy = proEngy;
    }

    public String getAceEngy() {
        return aceEngy;
    }

    public void setAceEngy(String aceEngy) {
        this.aceEngy = aceEngy;
    }

    public String getRegEngy() {
        return regEngy;
    }

    public void setRegEngy(String regEngy) {
        this.regEngy = regEngy;
    }

    public String getTrainId() {
        return trainId;
    }

    public void setTrainId(String trainId) {
        this.trainId = trainId;
    }
}
