package cc.crrc.manage.pojo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

public class FaultByDateForExcelPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull
    @Excel(name = "时间" ,isImportField = "time", orderNum = "0",width = 20)
    private String time;

    @NotNull
    @Excel(name = "故障数量" ,isImportField = "count", orderNum = "1",width = 20)
    private String count;

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getCount() {
        return count;
    }

    public void setCount(String count) {
        this.count = count;
    }
}
