package cc.crrc.manage.pojo.excel.healthyScore;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.sql.Date;

public class LineHealthyScoreDTO {
    @Excel(name = "时间",  orderNum = "1",width = 20)
    private Date time;
    @Excel(name = "线路名称",  orderNum = "1",width = 20)
    private String lineName;
    @Excel(name = "线路评分",  orderNum = "1",width = 20)
    private String lineScore;

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getLineScore() {
        return lineScore;
    }

    public void setLineScore(String lineScore) {
        this.lineScore = lineScore;
    }
}
