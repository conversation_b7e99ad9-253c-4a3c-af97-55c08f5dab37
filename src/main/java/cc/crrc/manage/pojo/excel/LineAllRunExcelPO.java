package cc.crrc.manage.pojo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

public class LineAllRunExcelPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull
    @Excel(name = "线路所属车(列)" ,isImportField = "num", orderNum = "2" ,width=15,mergeRely=3)
    private Integer num;

    @NotNull
    @Excel(name = "名称" ,isImportField = "type", orderNum = "0" ,width=20)
    private String type;

    @NotNull
    @Excel(name = "值" ,isImportField = "value", orderNum = "1",width=15)
    private String value;


    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }


    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }
}
