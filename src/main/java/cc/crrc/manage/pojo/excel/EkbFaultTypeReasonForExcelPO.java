package cc.crrc.manage.pojo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: Guowei
 * 2020-04-23
 */
public class EkbFaultTypeReasonForExcelPO implements Serializable {
    private static final long serialVersionUID = 1L;
    @NotNull
    @Excel(name = "故障类型编码(faultTypeKey)",  orderNum = "1", isImportField= "true", width = 15)
    private String faultTypeKey;
    @NotNull
    @Excel(name = "故障原因编码(faultReasonKey),多个编码用英文分号;隔开",  orderNum = "2", isImportField= "true", width =50)
    private String faultReasonKey;
    @ExcelIgnore
    private Long initCounter;
    @ExcelIgnore
    private Long realCounter;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getFaultTypeKey() {
        return faultTypeKey;
    }

    public void setFaultTypeKey(String faultTypeKey) {
        this.faultTypeKey = faultTypeKey;
    }

    public String getFaultReasonKey() {
        return faultReasonKey;
    }

    public void setFaultReasonKey(String faultReasonKey) {
        this.faultReasonKey = faultReasonKey;
    }

    public Long getInitCounter() {
        return initCounter;
    }

    public void setInitCounter(Long initCounter) {
        this.initCounter = initCounter;
    }

    public Long getRealCounter() {
        return realCounter;
    }

    public void setRealCounter(Long realCounter) {
        this.realCounter = realCounter;
    }
}
