package cc.crrc.manage.pojo.excel;

import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.pojo.ekb.EkbFaultMeasureVO;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @FileName FaultReasonForExcelPO
 * <AUTHOR>
 * @Date 2020年4月23日
 * @Version 1.0
 **/
public class EkbFaultReasonForExcelPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelIgnore
    private String id;
    @NotNull
    @Excel(name = "车型ID" ,isImportField = "vehicle_type_id", orderNum = "2")
    private String vehicleTypeId;
    @Excel(name = "故障原因业务主键" ,isImportField = "reason_key", orderNum = "3")
    private String reasonKey;
    @NotNull
    @Excel(name = "故障原因编码" ,isImportField = "reason_code", orderNum = "4")
    private String reasonCode;
    @Excel(name = "原因内容" ,isImportField = "content", orderNum = "5")
    private String content;
    @Excel(name = "类别" ,isImportField = "category", orderNum = "6")
    private String category;
    @ExcelIgnore
    private Date modifyTime;
    @ExcelIgnore
    private String createBy;
    @ExcelIgnore
    private Date createTime;
    @ExcelIgnore
    private String modifyBy;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getReasonKey() {
        return reasonKey;
    }

    public void setReasonKey(String reasonKey) {
        this.reasonKey = reasonKey;
    }

    public String getReasonCode() {
        return reasonCode;
    }

    public void setReasonCode(String reasonCode) {
        this.reasonCode = reasonCode;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }
}
