package cc.crrc.manage.pojo.excel;

import cc.crrc.manage.common.annotation.LogParam;
import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;

public class AlarmLevelForExcelPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @LogParam(description = "故障系统")
    @Excel(name = "故障系统(中文名称)",  orderNum = "0",width = 20)
    private String subsystemName;//故障系统

    @Excel(name = "严重故障数量",  orderNum = "1",width = 20)
    private Long count4;
    @Excel(name = "一般故障数量",  orderNum = "2",width = 20)
    private Long count3;
    @Excel(name = "轻微故障数量",  orderNum = "3",width = 20)
    private Long count2;
    @Excel(name = "事件数量",  orderNum = "4",width = 20)
    private Long count1;


    public String getSubsystemName() {
        return subsystemName;
    }

    public void setSubsystemName(String subsystemName) {
        this.subsystemName = subsystemName;
    }

    public Long getCount4() {
        return count4;
    }

    public void setCount4(Long count4) {
        this.count4 = count4;
    }

    public Long getCount3() {
        return count3;
    }

    public void setCount3(Long count3) {
        this.count3 = count3;
    }

    public Long getCount2() {
        return count2;
    }

    public void setCount2(Long count2) {
        this.count2 = count2;
    }

    public Long getCount1() {
        return count1;
    }

    public void setCount1(Long count1) {
        this.count1 = count1;
    }
}
