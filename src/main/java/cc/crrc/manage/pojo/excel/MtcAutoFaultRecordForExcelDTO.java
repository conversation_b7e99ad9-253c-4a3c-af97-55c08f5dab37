package cc.crrc.manage.pojo.excel;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.mtc.MtcAutoFaultRecordPO;
import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

public class MtcAutoFaultRecordForExcelDTO{


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(example = "2018-01-01 12:00:00")
    @LogParam(description = "故障发生时间")
    @Excel(name = "故障发生时间(startTime)",  orderNum = "0" ,format = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @LogParam(description = "故障名称中文")
    @Excel(name = "故障名称(faultNameCn)",  orderNum = "1")
    private String faultNameCn;//故障名称中文

    @LogParam(description = "车辆编码")
    @Excel(name = "车辆",  orderNum = "2")
    private String vehicleCode;//车辆编码

    @LogParam(description = "故障位置")
    @Excel(name = "故障位置",  orderNum = "3")
    private String location;//故障车厢

    @LogParam(description = "故障系统")
    @Excel(name = "故障系统",  orderNum = "4")
    private String subsystem;//故障系统

    @LogParam(description = "故障系统")
    @Excel(name = "故障系统(中文名称)",  orderNum = "5")
    private String subsystemName;//故障系统

    @LogParam(description = "故障等级")
    @Excel(name = "故障等级",  orderNum = "6")
    private String faultLevelName;//故障等级

    @LogParam(description = "解除状态")
    @Excel(name = "故障确认状态",  orderNum = "7")
    private String confirm;


    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public String getFaultNameCn() {
        return faultNameCn;
    }

    public void setFaultNameCn(String faultNameCn) {
        this.faultNameCn = faultNameCn;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public String getFaultLevelName() {
        return faultLevelName;
    }

    public void setFaultLevelName(String faultLevelName) {
        this.faultLevelName = faultLevelName;
    }

    public String getConfirm() {
        return confirm;
    }

    public void setConfirm(String confirm) {
        this.confirm = confirm;
    }

    public String getSubsystemName() {
        return subsystemName;
    }

    public void setSubsystemName(String subsystemName) {
        this.subsystemName = subsystemName;
    }
}
