package cc.crrc.manage.pojo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

public class LifeForecastForExcelPO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ExcelIgnore
    private String id;
    @NotNull
    @Excel(name = "线路名称" ,isImportField = "lineName", orderNum = "0")
    private String lineName;
    @NotNull
    @Excel(name = "车辆编号" ,isImportField = "vehicleCode", orderNum = "1")
    private String vehicleCode;
    @NotNull
    @Excel(name = "部件类型" ,isImportField = "typeCn", orderNum = "3")
    private String typeCn;
    @NotNull
    @Excel(name = "部件名称" ,isImportField = "nameCn", orderNum = "2")
    private String nameCn;
    @NotNull
    @Excel(name = "部件位置" ,isImportField = "assemblyLocation", orderNum = "4")
    private String assemblyLocation;
    @NotNull
    @Excel(name = "装车时间" ,isImportField = "assemblyTime", orderNum = "5")
    private String assemblyTime;
    @NotNull
    @Excel(name = "电器寿命（操作周期数）" ,isImportField = "remainingLife", orderNum = "7")
    private String remainingLife;
    @NotNull
    @Excel(name = "已使用（操作周期数）" ,isImportField = "operationCnt", orderNum = "8")
    private String operationCnt;
    @NotNull
    @Excel(name = "使用百分比" ,isImportField = "usePercent", orderNum = "9")
    private String usePercent;
    @NotNull
    @Excel(name = "更换次数" ,isImportField = "replaceTimes", orderNum = "6")
    private String replaceTimes;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTypeCn() {
        return typeCn;
    }

    public void setTypeCn(String typeCn) {
        this.typeCn = typeCn;
    }

    public String getAssemblyLocation() {
        return assemblyLocation;
    }

    public void setAssemblyLocation(String assemblyLocation) {
        this.assemblyLocation = assemblyLocation;
    }

    public String getAssemblyTime() {
        return assemblyTime;
    }

    public void setAssemblyTime(String assemblyTime) {
        this.assemblyTime = assemblyTime;
    }

    public String getRemainingLife() {
        return remainingLife;
    }

    public void setRemainingLife(String remainingLife) {
        this.remainingLife = remainingLife;
    }

    public String getOperationCnt() {
        return operationCnt;
    }

    public void setOperationCnt(String operationCnt) {
        this.operationCnt = operationCnt;
    }


    public String getUsePercent() {
        return usePercent;
    }

    public void setUsePercent(String usePercent) {
        this.usePercent = usePercent;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getReplaceTimes() {
        return replaceTimes;
    }

    public void setReplaceTimes(String replaceTimes) {
        this.replaceTimes = replaceTimes;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }
}
