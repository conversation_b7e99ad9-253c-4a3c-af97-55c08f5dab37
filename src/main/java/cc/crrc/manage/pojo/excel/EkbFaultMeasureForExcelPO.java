package cc.crrc.manage.pojo.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * @FileName EkbFaultMeasureForExcelPO
 * <AUTHOR>
 * @Date 2020年4月23日
 * @Version 1.0
 **/

public class EkbFaultMeasureForExcelPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelIgnore
    private String id;
    @NotNull
    @Excel(name = "车型ID" ,isImportField = "vehicle_type_id", orderNum = "2")
    private String vehicleTypeId;
    @NotNull
    @Excel(name = "故障解决措施业务主键" ,isImportField = "measure_key", orderNum = "3")
    private String measureKey;
    @Excel(name = "故障解决措施编码" ,isImportField = "measure_code", orderNum = "4")
    private String measureCode;
    @Excel(name = "故障措施内容" ,isImportField = "content", orderNum = "5")
    private String content;
    @Excel(name = "类别" ,isImportField = "category", orderNum = "6")
    private String category;
    @ExcelIgnore
    private String createBy;
    @ExcelIgnore
    private Date createTime;
    @ExcelIgnore
    private String modifyBy;
    @ExcelIgnore
    private Date modifyTime;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getMeasureKey() {
        return measureKey;
    }

    public void setMeasureKey(String measureKey) {
        this.measureKey = measureKey;
    }

    public String getMeasureCode() {
        return measureCode;
    }

    public void setMeasureCode(String measureCode) {
        this.measureCode = measureCode;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }
}
