package cc.crrc.manage.pojo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

public class BusDictVO extends PageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键id */
    @ApiModelProperty(value = "主键id")
    private String id;
    /** 排序 */
    @ApiModelProperty(value = "排序")
    private Long sortNumber;
    /** 字典标签 */
    @ApiModelProperty(value = "字典标签")
    private String label;
    /** 字典值 */
    @ApiModelProperty(value = "字典值")
    private String value;
    /** 描述 */
    @ApiModelProperty(value = "描述")
    private String description;
    /** 字典类型code */
    @ApiModelProperty(value = "字典类型code")
    private String typeCode;
    /** 线路id */
    @ApiModelProperty(value = "线路id")
    private String lineId;
    /** 标签code */
    @ApiModelProperty(value = "标签code")
    private String code;
    /** 删除标识 */
    @ApiModelProperty(value = "删除标识")
    private String delFlag;

    @ApiModelProperty(value = "中文名称")
    private String nameCn;
    @ApiModelProperty(value = "英文名称")
    private String nameEn;

    @ApiModelProperty
    private String VehicleTypeId;

    @ApiModelProperty
    private String vehicleTypeName;

    public String getVehicleTypeId() {
        return VehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        VehicleTypeId = vehicleTypeId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getSortNumber() {
        return sortNumber;
    }

    public void setSortNumber(Long sortNumber) {
        this.sortNumber = sortNumber;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getVehicleTypeName() {
        return vehicleTypeName;
    }

    public void setVehicleTypeName(String vehicleTypeName) {
        this.vehicleTypeName = vehicleTypeName;
    }
}
