package cc.crrc.manage.pojo;

import io.swagger.annotations.ApiModelProperty;

public class BusDictTypeQuery extends PageVO{

    private static final long serialVersionUID = 1L;

    /** 字典描述 */
    @ApiModelProperty(value = "字典描述")
    private String description;

    /** 删除标识 1-删除 0-未删除 */
    private String delFlag;

    /** 字典类型编码 */
    @ApiModelProperty(value = "字典类型编码")
    private String code;

    /** 字典类型名称 */
    @ApiModelProperty(value = "字典类型名称")
    private String name;

    /** 字典范围 */
    @ApiModelProperty(value = "字典范围")
    private String typeLevel;

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTypeLevel() {
        return typeLevel;
    }

    public void setTypeLevel(String typeLevel) {
        this.typeLevel = typeLevel;
    }
}
