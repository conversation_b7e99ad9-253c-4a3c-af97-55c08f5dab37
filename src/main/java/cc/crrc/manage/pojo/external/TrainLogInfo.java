package cc.crrc.manage.pojo.external;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

//@Duplicates({
//        @Duplicate(table = "mtr_station", message = "站点id已存在", condition = "sta_id='${staId}' and metro_line_id='${metroLineId}' and del_flag=0", groups = {
//                Insert.class}),
//        @Duplicate(table = "mtr_station", message = "站点id已存在", condition = "sta_id='${staId}' and id!='${id}' and metro_line_id='${metroLineId}' and del_flag=0", groups = {
//                Update.class})
//})
public class TrainLogInfo {

    private String id;

    private String vehicleCode;

    private Date traceTime;

    private String traceFile;

    private Integer stationCode;

    private Integer direction;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public Date getTraceTime() {
        return traceTime;
    }

    public void setTraceTime(Date traceTime) {
        this.traceTime = traceTime;
    }

    public String getTraceFile() {
        return traceFile;
    }

    public void setTraceFile(String traceFile) {
        this.traceFile = traceFile;
    }

    public Integer getStationCode() {
        return stationCode;
    }

    public void setStationCode(Integer stationCode) {
        this.stationCode = stationCode;
    }

    public Integer getDirection() {
        return direction;
    }

    public void setDirection(Integer direction) {
        this.direction = direction;
    }
}
