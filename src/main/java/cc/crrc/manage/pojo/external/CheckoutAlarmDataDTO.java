package cc.crrc.manage.pojo.external;

import cc.crrc.manage.pojo.PageVO;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;


public class CheckoutAlarmDataDTO extends PageVO {

    private String lineId;

    private String vehicleCode;

    private Integer stationCode;

    private Integer status;

    private String alarmLevel;

    public String getAlarmLevel() {
        return alarmLevel;
    }

    public void setAlarmLevel(String alarmLevel) {
        this.alarmLevel = alarmLevel;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date traceTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date traceTimeEnd;

    private Integer monirorProject;

    private Integer pointTypeCode;

    private String  partCode;


    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public Integer getStationCode() {
        return stationCode;
    }

    public void setStationCode(Integer stationCode) {
        this.stationCode = stationCode;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getTraceTimeStart() {
        return traceTimeStart;
    }

    public void setTraceTimeStart(Date traceTimeStart) {
        this.traceTimeStart = traceTimeStart;
    }

    public Date getTraceTimeEnd() {
        return traceTimeEnd;
    }

    public void setTraceTimeEnd(Date traceTimeEnd) {
        this.traceTimeEnd = traceTimeEnd;
    }

    public Integer getMonirorProject() {
        return monirorProject;
    }

    public void setMonirorProject(Integer monirorProject) {
        this.monirorProject = monirorProject;
    }

    public Integer getPointTypeCode() {
        return pointTypeCode;
    }

    public void setPointTypeCode(Integer pointTypeCode) {
        this.pointTypeCode = pointTypeCode;
    }

    public String getPartCode() {
        return partCode;
    }

    public void setPartCode(String partCode) {
        this.partCode = partCode;
    }
}
