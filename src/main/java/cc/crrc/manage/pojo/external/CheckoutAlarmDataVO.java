package cc.crrc.manage.pojo.external;

import cc.crrc.manage.pojo.PageVO;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;


public class CheckoutAlarmDataVO extends PageVO {

    private String id;

    private String vehicleCode;

    private Integer status;

    private String statusCn;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date alarmTime;

    private Integer pointTypeCode;

    private String alarmLevel;

    public String getAlarmLevel() {
        return alarmLevel;
    }

    public void setAlarmLevel(String alarmLevel) {
        this.alarmLevel = alarmLevel;
    }

    private Integer partCode;

    private String partCn;

    private String pointTypeCn;

    private Double dataValue;

    private Long alarmId;

    private String picture;

    private Integer stationCode;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStatusCn() {
        return statusCn;
    }

    public void setStatusCn(String statusCn) {
        this.statusCn = statusCn;
    }

    public Date getAlarmTime() {
        return alarmTime;
    }

    public void setAlarmTime(Date alarmTime) {
        this.alarmTime = alarmTime;
    }

    public Integer getPointTypeCode() {
        return pointTypeCode;
    }

    public void setPointTypeCode(Integer pointTypeCode) {
        this.pointTypeCode = pointTypeCode;
    }

    public Integer getPartCode() {
        return partCode;
    }

    public void setPartCode(Integer partCode) {
        this.partCode = partCode;
    }

    public String getPartCn() {
        return partCn;
    }

    public void setPartCn(String partCn) {
        this.partCn = partCn;
    }

    public String getPointTypeCn() {
        return pointTypeCn;
    }

    public void setPointTypeCn(String pointTypeCn) {
        this.pointTypeCn = pointTypeCn;
    }

    public Double getDataValue() {
        return dataValue;
    }

    public void setDataValue(Double dataValue) {
        this.dataValue = dataValue;
    }

    public Long getAlarmId() {
        return alarmId;
    }

    public void setAlarmId(Long alarmId) {
        this.alarmId = alarmId;
    }

    public String getPicture() {
        return picture;
    }

    public void setPicture(String picture) {
        this.picture = picture;
    }

    public Integer getStationCode() {
        return stationCode;
    }

    public void setStationCode(Integer stationCode) {
        this.stationCode = stationCode;
    }
}
