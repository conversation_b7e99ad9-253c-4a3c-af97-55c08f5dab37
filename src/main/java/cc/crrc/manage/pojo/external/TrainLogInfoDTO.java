package cc.crrc.manage.pojo.external;

import cc.crrc.manage.common.utils.Page;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

//@Duplicates({
//        @Duplicate(table = "mtr_station", message = "站点id已存在", condition = "sta_id='${staId}' and metro_line_id='${metroLineId}' and del_flag=0", groups = {
//                Insert.class}),
//        @Duplicate(table = "mtr_station", message = "站点id已存在", condition = "sta_id='${staId}' and id!='${id}' and metro_line_id='${metroLineId}' and del_flag=0", groups = {
//                Update.class})
//})
public class TrainLogInfoDTO extends Page {

    private String lineId;

    private String vehicleCode;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date traceTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date traceTimeEnd;

    private Integer stationCode;

    private Integer monirorProject;


    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public Date getTraceTimeStart() {
        return traceTimeStart;
    }

    public void setTraceTimeStart(Date traceTimeStart) {
        this.traceTimeStart = traceTimeStart;
    }

    public Date getTraceTimeEnd() {
        return traceTimeEnd;
    }

    public void setTraceTimeEnd(Date traceTimeEnd) {
        this.traceTimeEnd = traceTimeEnd;
    }

    public Integer getStationCode() {
        return stationCode;
    }

    public void setStationCode(Integer stationCode) {
        this.stationCode = stationCode;
    }

    public Integer getMonirorProject() {
        return monirorProject;
    }

    public void setMonirorProject(Integer monirorProject) {
        this.monirorProject = monirorProject;
    }
}
