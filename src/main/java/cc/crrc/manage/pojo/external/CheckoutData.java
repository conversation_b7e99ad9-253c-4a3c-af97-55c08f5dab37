package cc.crrc.manage.pojo.external;

import java.util.Date;

//@Duplicates({
//        @Duplicate(table = "mtr_station", message = "站点id已存在", condition = "sta_id='${staId}' and metro_line_id='${metroLineId}' and del_flag=0", groups = {
//                Insert.class}),
//        @Duplicate(table = "mtr_station", message = "站点id已存在", condition = "sta_id='${staId}' and id!='${id}' and metro_line_id='${metroLineId}' and del_flag=0", groups = {
//                Update.class})
//})
public class CheckoutData {

    private String id;

    private Long trainLogId;

    private String trainPassInfoId;

    private Integer pointTypeCode;

    private Integer partCode;

    private Integer deviceTypeCode;

    private Double dataValue;

    private Long alarmId;

    private Integer alarmLevel;

    private Integer status;

    private String partType;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getTrainLogId() {
        return trainLogId;
    }

    public void setTrainLogId(Long trainLogId) {
        this.trainLogId = trainLogId;
    }

    public String getTrainPassInfoId() {
        return trainPassInfoId;
    }

    public void setTrainPassInfoId(String trainPassInfoId) {
        this.trainPassInfoId = trainPassInfoId;
    }

    public Integer getPointTypeCode() {
        return pointTypeCode;
    }

    public void setPointTypeCode(Integer pointTypeCode) {
        this.pointTypeCode = pointTypeCode;
    }

    public Integer getPartCode() {
        return partCode;
    }

    public void setPartCode(Integer partCode) {
        this.partCode = partCode;
    }

    public Integer getDeviceTypeCode() {
        return deviceTypeCode;
    }

    public void setDeviceTypeCode(Integer deviceTypeCode) {
        this.deviceTypeCode = deviceTypeCode;
    }

    public Double getDataValue() {
        return dataValue;
    }

    public void setDataValue(Double dataValue) {
        this.dataValue = dataValue;
    }

    public Long getAlarmId() {
        return alarmId;
    }

    public void setAlarmId(Long alarmId) {
        this.alarmId = alarmId;
    }

    public Integer getAlarmLevel() {
        return alarmLevel;
    }

    public void setAlarmLevel(Integer alarmLevel) {
        this.alarmLevel = alarmLevel;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getPartType() {
        return partType;
    }

    public void setPartType(String partType) {
        this.partType = partType;
    }
}
