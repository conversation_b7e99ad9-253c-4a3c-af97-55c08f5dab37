package cc.crrc.manage.pojo;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * @FileName SysDictTypePO
 * <AUTHOR> yuxi
 * @Date 2019/10/16 11:07
 * @Version 1.0
 **/
@Duplicates({
        @Duplicate(table = "sys_dict_type", condition = "id = '${id}' and del_flag = '0'", groups = Insert.class, message = "ID已存在"),
        @Duplicate(table = "sys_dict_type", condition = "id != '${id}' and type = '${type}' and del_flag = '0'", groups = Update.class, message = "字典类型已存在"),
        @Duplicate(table = "sys_dict_type", condition = "type = '${type}' and del_flag = '0'", groups = Insert.class, message = "字典类型已存在")
})
public class SysDictTypePO extends PageVO implements Serializable {
    private static final long serialVersionUID = 1L;
    private String id;
    @LogParam(description = "字典类型")
    @NotBlank(message = "字典类型不能为空")
    @Length(max = 255,message = "字典类型长度不能超过255个字符",groups = {Insert.class, Update.class})
    private String type;
    @LogParam(description = "描述")
    private String description;
    @LogParam(description = "字典类型等级范围")
    private String typeLevel;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Date createTime;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String createBy;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Date modifyTime;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String modifyBy;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String delFlag;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getTypeLevel() {
        return typeLevel;
    }

    public void setTypeLevel(String typeLevel) {
        this.typeLevel = typeLevel;
    }
}
