package cc.crrc.manage.pojo.od;

import cc.crrc.manage.common.annotation.LogParam;

/**
 * @FileName OdOperationPlanDetailPO
 * @Description 运营计划决策详情实体类
 * <AUTHOR> yuxi
 * @Date 2020/5/7 15:02
 **/
public class OdOperationPlanDetailPO {
    private String id;
    @LogParam(description = "外键，引用运维决策表主键")
    private String operationDecisionId;
    @LogParam(description = "序列号")
    private String serialNumber;
    @LogParam(description = "投运车辆编号")
    private String serviceVehicleCode;
    @LogParam(description = "开始时间")
    private String startTime;
    @LogParam(description = "结束时间")
    private String endTime;
    @LogParam(description = "投运数量")
    private Integer serviceCount;
    @LogParam(description = "发车时间间隔")
    private Long timeInterval;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOperationDecisionId() {
        return operationDecisionId;
    }

    public void setOperationDecisionId(String operationDecisionId) {
        this.operationDecisionId = operationDecisionId;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getServiceVehicleCode() {
        return serviceVehicleCode;
    }

    public void setServiceVehicleCode(String serviceVehicleCode) {
        this.serviceVehicleCode = serviceVehicleCode;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getServiceCount() {
        return serviceCount;
    }

    public void setServiceCount(Integer serviceCount) {
        this.serviceCount = serviceCount;
    }

    public Long getTimeInterval() {
        return timeInterval;
    }

    public void setTimeInterval(Long timeInterval) {
        this.timeInterval = timeInterval;
    }
}
