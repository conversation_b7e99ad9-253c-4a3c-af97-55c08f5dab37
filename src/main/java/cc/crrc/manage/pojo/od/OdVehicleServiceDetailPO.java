package cc.crrc.manage.pojo.od;

import cc.crrc.manage.common.annotation.LogParam;

/**
 * @FileName OdVehicleServiceDetailPO
 * @Description 车辆投运决策详情实体类
 * <AUTHOR> yuxi
 * @Date 2020/5/7 15:03
 **/
public class OdVehicleServiceDetailPO {
    private String id;
    @LogParam(description = "外键，引用运维决策表主键")
    private String operationDecisionId;
    @LogParam(description = "车辆编号")
    private String vehicleCode;
    @LogParam(description = "车辆id")
    private String vehicleId;
    @LogParam(description = "是否投运")
    private String whetherService;
    @LogParam(description = "优先级")
    private String priority;
    @LogParam(description = "车辆健康度")
    private Integer healthDegree;
    @LogParam(description = "决策依据")
    private String decisionBasis;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOperationDecisionId() {
        return operationDecisionId;
    }

    public void setOperationDecisionId(String operationDecisionId) {
        this.operationDecisionId = operationDecisionId;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getWhetherService() {
        return whetherService;
    }

    public void setWhetherService(String whetherService) {
        this.whetherService = whetherService;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public Integer getHealthDegree() {
        return healthDegree;
    }

    public void setHealthDegree(Integer healthDegree) {
        this.healthDegree = healthDegree;
    }

    public String getDecisionBasis() {
        return decisionBasis;
    }

    public void setDecisionBasis(String decisionBasis) {
        this.decisionBasis = decisionBasis;
    }
}
