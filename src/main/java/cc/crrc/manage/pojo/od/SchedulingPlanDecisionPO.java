package cc.crrc.manage.pojo.od;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.IntegerRange;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.pojo.PageVO;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;


import java.awt.*;
import java.util.Date;

/**
 * @FileName SchedulingPlanDecisionPO
 * @Description 行车调度计划决策实体类
 * @<PERSON> <PERSON>
 * @Date 2020/7/30 9:50
 **/
public class SchedulingPlanDecisionPO extends PageVO{

    @ApiModelProperty(value = "主键id")
    @LogParam(description="主键id")
    private String id;
    @ApiModelProperty(value = "调度决策编码 后台生成,时间段+该时间段决策数量+1")
    @LogParam(description="调度决策编码 后台生成,时间段+该时间段决策数量+1")
    private String schedulingCode;
    @ApiModelProperty(value = "决策开始时间")
    @LogParam(description="决策开始时间")
    private String startTime;
    @ApiModelProperty(value = "决策结束时间")
    @LogParam(description="决策结束时间")
    private String endTime;
    @ApiModelProperty(value = "车辆投运数量 单位：辆")
    @LogParam(description="车辆投运数量 单位：辆")
    private Integer commissioningCount;
    @ApiModelProperty(value = "发车时间间隔 单位：分钟")
    @LogParam(description="发车时间间隔 单位：分钟")
    private Integer intervalTime;
    @ApiModelProperty(value = "外键,线路id")
    @LogParam(description="外键,线路id")
    private String lineId;
    @ApiModelProperty(value = "线路名称")
    @LogParam(description="线路名称")
    private String lineName;
    @ApiModelProperty(value = "线路里程 单位:km")
    @LogParam(description="线路里程 单位:km")
    private Integer lineMileage;
    @ApiModelProperty(value = "平均车速 km/h")
    @LogParam(description="平均车速 km/h")
    @IntegerRange(max = 150, min = 1, message = "平均车速数值范围介于1~150km/h", groups = {Insert.class, Update.class})
    private Long vehicleSpeed;
    @ApiModelProperty(value = "始发站调头时间：单位分钟")
    @LogParam(description="始发站调头时间：单位分钟")
    @IntegerRange(max = 120, min = 1, message = "始发站调头时间数值范围介于1~120分钟", groups = {Insert.class, Update.class})
    private Long startSwitchingTime;
    @ApiModelProperty(value = "终点站调头时间：单位分钟")
    @LogParam(description="终点站调头时间：单位分钟")
    @IntegerRange(max = 120, min = 1, message = "终点站调头时间数值范围介于1~120分钟", groups = {Insert.class, Update.class})
    private Long endSwitchingTime;
    @ApiModelProperty(value = "乘车率")
    @LogParam(description="乘车率")
    private Float rideRate;
    @ApiModelProperty(value = "设定时间段平均车辆数Z")
    @LogParam(description="设定时间段平均车辆数Z")
    @IntegerRange(max = 11, min = 1, message = "设定时间段平均车辆数范围介于1~11之间", groups = {Insert.class, Update.class})
    private Long historyRideCount;
    @ApiModelProperty(value = "决策类型")
    @LogParam(description="决策类型")
    private String decisionType;
    @ApiModelProperty(value = "决策描述")
    @LogParam(description="决策描述")
    @Length(max = 255,message = "决策描述内容长度过长需小于或等于255")
    private String remark;
    @ApiModelProperty(value = "推送状态 0:不推送  1:推送")
    @LogParam(description="推送状态 0:不推送  1:推送")
    private String pushStatus;
    @ApiModelProperty(value = "决策起始时间")
    @LogParam(description="决策起始时间")
    private String startEndTime;
    @ApiModelProperty(value = "推荐决策")
    @LogParam(description="推荐决策")
    private String recommendDecision;
    @ApiModelProperty(hidden = true)
    @LogParam(description="有效性 1:有效 0:无效")
    private Integer valid;
    @ApiModelProperty(hidden = true)
    @LogParam(description="创建用户编号")
    private String craeteBy;
    @ApiModelProperty(hidden = true)
    @LogParam(description="创建时间")
    private Date createTime;
    @ApiModelProperty(hidden = true)
    @LogParam(description="修改用户编号")
    private String modifyBy;
    @ApiModelProperty(hidden = true)
    @LogParam(description="修改时间")
    private Date modifyTime;

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public String getSchedulingCode() { return schedulingCode; }

    public void setSchedulingCode(String schedulingCode) { this.schedulingCode = schedulingCode; }

    public String getStartTime() { return startTime; }

    public void setStartTime(String startTime) { this.startTime = startTime; }

    public String getEndTime() { return endTime; }

    public void setEndTime(String endTime) { this.endTime = endTime; }

    public Integer getCommissioningCount() { return commissioningCount; }

    public void setCommissioningCount(Integer commissioningCount) { this.commissioningCount = commissioningCount; }

    public Integer getIntervalTime() { return intervalTime; }

    public void setIntervalTime(Integer intervalTime) { this.intervalTime = intervalTime; }

    public String getLineId() { return lineId; }

    public void setLineId(String lineId) { this.lineId = lineId; }

    public Long getVehicleSpeed() { return vehicleSpeed; }

    public void setVehicleSpeed(Long vehicleSpeed) { this.vehicleSpeed = vehicleSpeed; }

    public Long getStartSwitchingTime() { return startSwitchingTime; }

    public void setStartSwitchingTime(Long startSwitchingTime) { this.startSwitchingTime = startSwitchingTime; }

    public Long getEndSwitchingTime() { return endSwitchingTime; }

    public void setEndSwitchingTime(Long endSwitchingTime) { this.endSwitchingTime = endSwitchingTime; }

    public Float getRideRate() { return rideRate; }

    public void setRideRate(Float rideRate) { this.rideRate = rideRate; }

    public Long getHistoryRideCount() { return historyRideCount; }

    public void setHistoryRideCount(Long historyRideCount) { this.historyRideCount = historyRideCount; }

    public String getRemark() { return remark; }

    public void setRemark(String remark) { this.remark = remark; }

    public Integer getValid() { return valid; }

    public void setValid(Integer valid) { this.valid = valid; }

    public String getCraeteBy() { return craeteBy; }

    public void setCraeteBy(String craeteBy) { this.craeteBy = craeteBy; }

    public Date getCreateTime() { return createTime; }

    public void setCreateTime(Date createTime) { this.createTime = createTime; }

    public String getModifyBy() { return modifyBy; }

    public void setModifyBy(String modifyBy) { this.modifyBy = modifyBy; }

    public Date getModifyTime() { return modifyTime; }

    public void setModifyTime(Date modifyTime) { this.modifyTime = modifyTime; }

    public String getLineName() { return lineName; }

    public void setLineName(String lineName) { this.lineName = lineName; }

    public Integer getLineMileage() { return lineMileage; }

    public void setLineMileage(Integer lineMileage) { this.lineMileage = lineMileage; }

    public String getDecisionType() { return decisionType; }

    public void setDecisionType(String decisionType) { this.decisionType = decisionType; }

    public String getPushStatus() { return pushStatus; }

    public void setPushStatus(String pushStatus) { this.pushStatus = pushStatus; }

    public String getStartEndTime() { return startEndTime; }

    public void setStartEndTime(String startEndTime) { this.startEndTime = startEndTime; }

    public String getRecommendDecision() { return recommendDecision; }

    public void setRecommendDecision(String recommendDecision) { this.recommendDecision = recommendDecision; }


}
