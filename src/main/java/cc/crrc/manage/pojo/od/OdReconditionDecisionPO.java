package cc.crrc.manage.pojo.od;


import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.PageVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * @ClassName OdReconditionDecisionPO
 * @Description 车辆检修决策实体类
 * <AUTHOR> ming<PERSON>an
 * @Date 2020/8/10 17:02
 * @Version 1.0
 **/

public class OdReconditionDecisionPO extends PageVO{

    @ApiModelProperty(value = "主键id")
    @LogParam(description = "主键id")
    private String id;
    @ApiModelProperty(value = "决策对象")
    @LogParam(description = "决策对象")
    private String substance;
    @ApiModelProperty(value = "决策编码")
    @LogParam(description = "决策编码")
    private String schedulingCode;
    @ApiModelProperty(value = "推荐决策(决策内容)")
    @LogParam(description = "推荐决策(决策内容)")
    private String recommendDecision;
    @ApiModelProperty(value = "状态标识 0:待处理  1:延后  2:完成 3:忽略")
    @LogParam(description = "状态标识 0:待处理  1:延后  2:完成 3:忽略")
    private String status;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "触发时间 初始值为生成时间")
    @LogParam(description = "触发时间 初始值为生成时间")
    private Date triggerTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    @LogParam(description = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "备注")
    @LogParam(description = "备注")
    private String remark;
    @ApiModelProperty(value = "提交状态 0: 未提交 1:已提交  该字段保留，目前没有使用场景")
    @LogParam(description = "提交状态 0: 未提交 1:已提交  该字段保留，目前没有使用场景")
    private String submitStatus;
    @ApiModelProperty(value = "所属子系统")
    @LogParam(description = "所属子系统")
    private String subsystem;
    @ApiModelProperty(value = "所在车厢")
    @LogParam(description = "所在车厢")
    private String location;

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public String getSubstance() { return substance; }

    public void setSubstance(String substance) { this.substance = substance; }

    public String getSchedulingCode() { return schedulingCode; }

    public void setSchedulingCode(String schedulingCode) { this.schedulingCode = schedulingCode; }

    public String getRecommendDecision() { return recommendDecision; }

    public void setRecommendDecision(String recommendDecision) { this.recommendDecision = recommendDecision; }

    public String getStatus() { return status; }

    public void setStatus(String status) { this.status = status; }

    public Date getTriggerTime() { return triggerTime; }

    public void setTriggerTime(Date triggerTime) { this.triggerTime = triggerTime; }

    public Date getCreateTime() { return createTime; }

    public void setCreateTime(Date createTime) { this.createTime = createTime; }

    public String getRemark() { return remark; }

    public void setRemark(String remark) { this.remark = remark; }

    public String getSubmitStatus() { return submitStatus; }

    public void setSubmitStatus(String submitStatus) { this.submitStatus = submitStatus; }

    public String getSubsystem() { return subsystem; }

    public void setSubsystem(String subsystem) { this.subsystem = subsystem; }

    public String getLocation() { return location; }

    public void setLocation(String location) { this.location = location; }


}