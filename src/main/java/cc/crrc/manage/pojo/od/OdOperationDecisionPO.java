package cc.crrc.manage.pojo.od;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.PageVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * @FileName OdOperationDecisionPO
 * @Description 决策总览实体类
 * <AUTHOR> yuxi
 * @Date 2020/5/7 15:01
 **/
public class OdOperationDecisionPO extends PageVO {
    @ApiModelProperty(value = "主键id")
    @LogParam(description = "主键id")
    private String id;
    @ApiModelProperty(value = "决策id 详细决策表的主键id")
    @LogParam(description = "决策id 详细决策表的主键id")
    private String decisionId;
    @ApiModelProperty(value = "决策编号")
    @LogParam(description = "决策编号")
    private String schedulingCode;
    @ApiModelProperty(value = "决策类型：车辆检修决策...")
    @LogParam(description = "决策类型：车辆检修决策...")
    private String decisionType;
    @ApiModelProperty(value = "决策内容")
    @LogParam(description = "决策内容")
    private String recommendDecision;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "推送时间")
    @LogParam(description = "推送时间")
    private Date pushTime;
    @ApiModelProperty(value = "状态标识 0:待处理  1:延后  2:完成 3:忽略")
    @LogParam(description = "状态标识 0:待处理  1:延后  2:完成 3:忽略")
    private String status;
    @ApiModelProperty(value = "决策对象")
    @LogParam(description = "决策对象")
    private String decisionSubstance;
    @ApiModelProperty(value = "所属子系统")
    @LogParam(description = "所属子系统")
    private String subsystem;
    @ApiModelProperty(value = "所在车厢")
    @LogParam(description = "所在车厢")
    private String location;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "推送起始时间")
    @LogParam(description = "推送起始时间")
    private  Date startPushTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "推送结束时间")
    @LogParam(description = "推送结束时间")
    private  Date endPushTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "操作时间")
    @LogParam(description = "操作时间")
    private Date operateTime;
    @ApiModelProperty(value = "操作员")
    @LogParam(description = "操作员")
    private String operateBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDecisionId() {
        return decisionId;
    }

    public void setDecisionId(String decisionId) {
        this.decisionId = decisionId;
    }

    public String getSchedulingCode() {
        return schedulingCode;
    }

    public void setSchedulingCode(String schedulingCode) {
        this.schedulingCode = schedulingCode;
    }

    public String getDecisionType() {
        return decisionType;
    }

    public void setDecisionType(String decisionType) {
        this.decisionType = decisionType;
    }

    public String getRecommendDecision() {
        return recommendDecision;
    }

    public void setRecommendDecision(String recommendDecision) {
        this.recommendDecision = recommendDecision;
    }

    public Date getPushTime() {
        return pushTime;
    }

    public void setPushTime(Date pushTime) {
        this.pushTime = pushTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDecisionSubstance() {
        return decisionSubstance;
    }

    public void setDecisionSubstance(String decisionSubstance) {
        this.decisionSubstance = decisionSubstance;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Date getStartPushTime() {
        return startPushTime;
    }

    public void setStartPushTime(Date startPushTime) {
        this.startPushTime = startPushTime;
    }

    public Date getEndPushTime() {
        return endPushTime;
    }

    public void setEndPushTime(Date endPushTime) {
        this.endPushTime = endPushTime;
    }

    public Date getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
    }

    public String getOperateBy() {
        return operateBy;
    }

    public void setOperateBy(String operateBy) {
        this.operateBy = operateBy;
    }
}
