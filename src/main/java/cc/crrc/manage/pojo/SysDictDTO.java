package cc.crrc.manage.pojo;

import cc.crrc.manage.common.annotation.LogParam;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * @FileName SysDictVO
 * <AUTHOR> yuxi
 * @Date 2019/6/14 11:15
 * @Version 1.0
 **/
public class  SysDictDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;
    @LogParam(description="排序")
    @NotNull(message = "排序不能为空")
    private String sortNumber;
    @LogParam(description="标签编码")
    @ApiModelProperty(required = true)
    @NotBlank(message = "标签编码不能为空")
    private String code;
    @LogParam(description="标签")
    @ApiModelProperty(required = true)
    @NotBlank(message = "标签不能为空")
    private String label;
    @LogParam(description="键值")
    @ApiModelProperty(required = true)
    @NotBlank(message = "键值不能为空")
    private String value;
    @LogParam(description="类型id")
    @ApiModelProperty(required = true)
    @NotNull(message = "类型id不能为空")
    private String typeId;
    @LogParam(description="描述")
    private String description;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Date modifyTime;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String modifyBy;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Date createTime;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String createBy;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String delFlag;

    @LogParam(description="线路id")
    private String lineId;
    @LogParam(description="线路名称")
    private String lineName;
    @LogParam(description="车型id")
    private String vehicleTypeId;
    @ApiModelProperty(hidden = true)
    @LogParam(description="车型名称")
    private String vehicleTypeName;
    @LogParam(description="字典类型code")
    private String typeCode;
    @LogParam(description="车辆id")
    private String vehicleId;



    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSortNumber() {
        return sortNumber;
    }

    public void setSortNumber(String sortNumber) {
        this.sortNumber = sortNumber;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getTypeId() {
        return typeId;
    }

    public void setTypeId(String typeId) {
        this.typeId = typeId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getVehicleTypeName() {
        return vehicleTypeName;
    }

    public void setVehicleTypeName(String vehicleTypeName) {
        this.vehicleTypeName = vehicleTypeName;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }
}
