package cc.crrc.manage.pojo.analysis;

import cc.crrc.manage.common.annotation.LogParam;

public class SysAccessStatisticsVO extends SysAccessStatisticsPO{

    @LogParam(description="开始时间")
    private String startTime;
    @LogParam(description="结束时间")
    private String endTime;
    @LogParam(description="浏览器")
    private String browser;
    @LogParam(description="系统")
    private String operatingSystem;

    @LogParam(description="浏览器count数值")
    private int count;
    @LogParam(description="当前用户id")//用于传值当前登入系统的用户id 然后给后端做筛选（根据当前用户所分配的菜单进行查询）
    private String UserId;

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getBrowser() {
        return browser;
    }

    public void setBrowser(String browser) {
        this.browser = browser;
    }

    public String getOperatingSystem() {
        return operatingSystem;
    }

    public void setOperatingSystem(String operatingSystem) {
        this.operatingSystem = operatingSystem;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    @Override
    public String getUserId() {
        return UserId;
    }

    @Override
    public void setUserId(String userId) {
        UserId = userId;
    }
}
