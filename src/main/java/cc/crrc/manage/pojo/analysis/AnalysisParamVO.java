package cc.crrc.manage.pojo.analysis;

import cc.crrc.manage.pojo.PageVO;

public class AnalysisParamVO extends PageVO {

    //再生能耗
    private final String ECR_UD_HIST_FDB = "ECR_udHistFDB";
    //牵引能耗
    private final String ECR_UD_HIST_MOTOR = "ECR_udHistMotor";
    //辅助能耗
    private final String ECR_UD_HIST_AUX = "ECR_udHistAUX";
    //充电机能耗
    private final String ECR_UD_HIST_BCH = "ECR_udHistBCH";
    //列车总能耗
    private final String ECR_UD_HIST_TTL = "ECR_udHistTTL";
    //列车总运行时间
    private final String TMS_UD_TOTAL_RUN_TIME = "TMS_udTotalRunTime";
    //列车运行总里程
    private final String ECR_UD_RUN_DISTANCE = "ECR_udRunDistance";

    private String startTime;
    private String endTime;
    private String vehicleCode;
    private String lineId;
    private String collectionDay;
    private String vehicleTypeId;
    private String vehicleId;
    private String time;
    private String accumlation;
    private String lineName;
    private String name;

    public String getECR_UD_HIST_FDB() {
        return ECR_UD_HIST_FDB;
    }

    public String getECR_UD_HIST_Motor() {
        return ECR_UD_HIST_MOTOR;
    }

    public String getECR_UD_HIST_AUX() {
        return ECR_UD_HIST_AUX;
    }

    public String getECR_UD_HIST_BCH() {
        return ECR_UD_HIST_BCH;
    }

    public String getECR_UD_HIST_TTL() {
        return ECR_UD_HIST_TTL;
    }

    public String getTMS_UD_TOTAL_RUN_TIME() {
        return TMS_UD_TOTAL_RUN_TIME;
    }

    public String getECR_UD_RUN_DISTANCE() {
        return ECR_UD_RUN_DISTANCE;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getCollectionDay() {
        return collectionDay;
    }

    public void setCollectionDay(String collectionDay) {
        this.collectionDay = collectionDay;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getAccumlation() {
        return accumlation;
    }

    public void setAccumlation(String accumlation) {
        this.accumlation = accumlation;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


}
