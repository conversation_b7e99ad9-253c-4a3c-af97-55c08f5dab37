package cc.crrc.manage.pojo.analysis;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.PageVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

public class SysAccessStatisticsPO extends PageVO {
    @LogParam(description = "id")
    private String id;
    @LogParam(description = "菜单名称")
    private String menuName;
    @LogParam(description = "菜单code")
    private String menuCode;
    @LogParam(description = "父级菜单code")
    private String parentMenuCode;
    @LogParam(description = "用户id")
    private String userId;
    @ApiModelProperty(example = "2021-06-25 12:00:00")
    @LogParam(description = "操作时间")
    private String date;
    @LogParam(description = "用户名字")
    private String name;
    @LogParam(description = "访问ip")
    private String host;
    @LogParam(description = "登录名")
    private String username;
    @LogParam(description = "浏览器信息")
    private String userAgent;
    @LogParam(description = "线路id")
    private String lineId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMenuName() {
        return menuName;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }

    public String getMenuCode() {
        return menuCode;
    }

    public void setMenuCode(String menuCode) {
        this.menuCode = menuCode;
    }

    public String getParentMenuCode() {
        return parentMenuCode;
    }

    public void setParentMenuCode(String parentMenuCode) {
        this.parentMenuCode = parentMenuCode;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
}
