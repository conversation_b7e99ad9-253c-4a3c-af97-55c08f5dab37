package cc.crrc.manage.pojo.analysis;


import cc.crrc.manage.pojo.PageVO;


/**
 * 故障类
 *
 * <AUTHOR>
 * @date 2020/12/17  13:46
 */
public class AutoEkbVO extends PageVO {

    //发生时间
    private String time;
    //故障名称
    private String faultNameCN;
    //车辆
    private String vehicle;
    //车厢
    private String location;
    //故障系统
    private String subsystem;
    //等级
    private String faultLevel;
    //发生次数
    private int count;

    public int getCount() {
		return count;
	}

	public void setCount(int count) {
		this.count = count;
	}

	public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getFaultNameCN() {
        return faultNameCN;
    }

    public void setFaultNameCN(String faultNameCN) {
        this.faultNameCN = faultNameCN;
    }

    public String getVehicle() {
        return vehicle;
    }

    public void setVehicle(String vehicle) {
        this.vehicle = vehicle;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public String getFaultLevel() {
        return faultLevel;
    }

    public void setFaultLevel(String faultLevel) {
        this.faultLevel = faultLevel;
    }
}
