package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.IntegerRange;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.pojo.PageVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName WheelMeasurementPO
 * <AUTHOR> yongqing
 * @Date 2022/3/16 14:30
 * @Version 1.0
 */
@Duplicates({
        @Duplicate(table = "mtr_wheel_measurement_vehicle", message = "车辆编号和里程数不能重复！",
                condition = "vehicle_code='${vehicleCode}' and mileage='${mileage}' and del_flag='0'",
                groups = {Insert.class}),
        @Duplicate(table = "mtr_wheel_measurement_vehicle", message = "已存在暂存状态的数据！",
                condition = "vehicle_code='${vehicleCode}' and '${status}'='0' and status='0' and del_flag='0'",
                groups = {Insert.class}),
        @Duplicate(table = "mtr_wheel_measurement_vehicle", message = "已存在暂存状态的数据！",
                condition = "vehicle_code='${vehicleCode}' and status='0' and '${status}'='0' and id!='${id}' and del_flag='0'",
                groups = {Update.class}),
        @Duplicate(table = "mtr_wheel_measurement_vehicle", message = "车辆编号和里程数不能重复！",
                condition = "vehicle_code='${vehicleCode}' and mileage='${mileage}' and id!='${id}' and del_flag='0'",
                groups = {Update.class})
})
public class WheelMeasurementPO extends PageVO {
    @LogParam(description = "主键id")
    private String id;
    @NotNull(message = "状态不能为空" ,groups= {Update.class,Insert.class})
    @LogParam(description = "状态")
    private String status;
    @NotNull(message = "车辆编号不能为空" ,groups= {Update.class,Insert.class})
    @LogParam(description = "车辆编号")
    private String vehicleCode;
    @NotNull(message = "里程不能为空" ,groups= {Update.class,Insert.class})
    @LogParam(description = "里程")
    @IntegerRange(max = 4294967295L, message = "运行里程范围0~4294967295km", groups= {Update.class,Insert.class})
    private Long mileage;
    @LogParam(description = "记录人")
    private String recorder;
    @LogParam(description = "检查日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String checkDate;
    @LogParam(description = "测量数据列表")
    private List<WheelMeasurementDataPO> tableData;
    @LogParam(description = "创建者")
    private String createBy;
    @LogParam(description = "修改者")
    private String modifyBy;
    @LogParam(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonIgnore
    private String startTime;
    @LogParam(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonIgnore
    private String endTime;
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public Long getMileage() {
        return mileage;
    }

    public void setMileage(Long mileage) {
        this.mileage = mileage;
    }

    public String getRecorder() {
        return recorder;
    }

    public void setRecorder(String recorder) {
        this.recorder = recorder;
    }

    public String getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(String checkDate) {
        this.checkDate = checkDate;
    }

    public List<WheelMeasurementDataPO> getTableData() {
        return tableData;
    }

    public void setTableData(List<WheelMeasurementDataPO> tableData) {
        this.tableData = tableData;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
