package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * 2019/12/3
 **/
@Duplicates({
        @Duplicate(table = "mtr_vehicle", message = "车辆中文名、英文名和车辆编号不能重复！", groups = {Insert.class},
                condition = "(name_cn = '${nameCn}' OR name_en = '${nameEn}' OR vehicle_code = '${vehicleCode}') AND del_flag = 0"),
        @Duplicate(table = "mtr_vehicle", message = "车辆中文名、英文名和车辆编号不能重复！", groups = {Update.class},
                condition = "(name_cn = '${nameCn}' OR name_en = '${nameEn}' OR vehicle_code = '${vehicleCode}') AND id != '${id}' AND del_flag = 0")
})
public class MtrVehiclePO {
    @NotNull(message = "车辆id不能为空", groups = {Update.class})
    @LogParam(description = "车辆id")
    @ApiModelProperty(value = "车辆id", dataType = "String")
    private String id;
    @LogParam(description = "车辆中文名")
    @ApiModelProperty(value = "车辆中文名", dataType = "String")
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
    private String nameCn;
    @NotNull(message = "车辆英文名不能为空！", groups = {Insert.class,Update.class})
    @LogParam(description = "车辆英文名")
    @ApiModelProperty(value = "车辆英文名", dataType = "String")
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
    private String nameEn;
    @NotNull(message = "车辆编码不能为空！", groups = {Insert.class,Update.class})
    @LogParam(description = "车辆编码")
    @ApiModelProperty(value = "车辆编码", dataType = "String")
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
    private String vehicleCode;
    @LogParam(description = "车辆编码缩写")
    @ApiModelProperty(value = "车辆编码缩写", dataType = "String")
    private String vehicleCodeAbbr;
    @LogParam(description = "车辆所属线路id")
    @ApiModelProperty(value = "车辆所属线路id", dataType = "String")
    private String metroLineId;
    @JsonIgnore
    private Integer delFlag;
    @JsonIgnore
    private String createBy;
    @JsonIgnore
    private Date createTime;
    @JsonIgnore
    private String modifyBy;
    @JsonIgnore
    private Date modifyTime;
    @LogParam(description = "车辆备注")
    @ApiModelProperty(value = "车辆备注", dataType = "String")
    private String remark;
    @LogParam(description = "出厂日期")
    @ApiModelProperty(value = "出厂日期", dataType = "Date")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date productionDate;
    @LogParam(description = "交车日期")
    @ApiModelProperty(value = "交车日期", dataType = "Date")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date deliveryDate;
    @LogParam(description = "质保期")
    @ApiModelProperty(value = "质保期", dataType = "Integer")
    private Integer guaranteePeriod;
    //添加车辆类型和行驶里程字段,用于故障上报-人工上报 zhangzhijian 2020-7-28
    @LogParam(description = "车辆类型")
    private String vehicleTypeCode;
    @LogParam(description = "行驶里程")
    private String mileage;
    @LogParam(description = "车辆在线状态")
    @ApiModelProperty(value = "车辆在线状态")
    private Boolean onlineStatus;
    @LogParam(description = "时序数据库存储组ID")
    @ApiModelProperty(value = "时序数据库存储组ID", dataType = "Integer")
    private Integer vehicleStorageGroup;

    @LogParam(description = "车辆配置状态")
    @ApiModelProperty(value = "车辆配置状态", dataType = "boolean")
    private boolean trainsDeploy;
    private String  lineName;
    private Boolean vehicleOnlineStatus;
    private String vehicleTypeId;
    private String vehicleTypeName;

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getMetroLineId() {
        return metroLineId;
    }

    public void setMetroLineId(String metroLineId) {
        this.metroLineId = metroLineId;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getProductionDate() {
        return productionDate;
    }

    public void setProductionDate(Date productionDate) {
        this.productionDate = productionDate;
    }

    public Date getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(Date deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public Integer getGuaranteePeriod() {
        return guaranteePeriod;
    }

    public void setGuaranteePeriod(Integer guaranteePeriod) {
        this.guaranteePeriod = guaranteePeriod;
    }

    public String getVehicleTypeCode() { return vehicleTypeCode; }

    public void setVehicleTypeCode(String vehicleTypeCode) { this.vehicleTypeCode = vehicleTypeCode; }

    public String getMileage() {
        return mileage;
    }

    public void setMileage(String mileage) {
        this.mileage = mileage;
    }

    public String getVehicleCodeAbbr() { return vehicleCodeAbbr; }

    public void setVehicleCodeAbbr(String vehicleCodeAbbr) { this.vehicleCodeAbbr = vehicleCodeAbbr; }

    public Boolean getOnlineStatus() { return onlineStatus; }

    public void setOnlineStatus(Boolean onlineStatus) { this.onlineStatus = onlineStatus; }

    public Integer getVehicleStorageGroup() {
        return vehicleStorageGroup;
    }

    public void setVehicleStorageGroup(Integer vehicleStorageGroup) {
        this.vehicleStorageGroup = vehicleStorageGroup;
    }

    public boolean isTrainsDeploy() {
        return trainsDeploy;
    }

    public void setTrainsDeploy(boolean trainsDeploy) {
        this.trainsDeploy = trainsDeploy;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public Boolean getVehicleOnlineStatus() {
        return vehicleOnlineStatus;
    }

    public void setVehicleOnlineStatus(Boolean vehicleOnlineStatus) {
        this.vehicleOnlineStatus = vehicleOnlineStatus;
    }

    public String getVehicleTypeName() {
        return vehicleTypeName;
    }

    public void setVehicleTypeName(String vehicleTypeName) {
        this.vehicleTypeName = vehicleTypeName;
    }
}
