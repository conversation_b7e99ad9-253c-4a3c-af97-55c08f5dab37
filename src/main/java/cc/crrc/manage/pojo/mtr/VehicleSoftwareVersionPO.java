package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.PageVO;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;


/**
 * <AUTHOR>
 * 2019/11/18
 **/
public class VehicleSoftwareVersionPO extends PageVO {

    @ApiModelProperty(value="车载软件ID")
    @LogParam(description="车载软件ID")
    private String id;

    @ApiModelProperty(value="车载软件名称")
    @LogParam(description="车载软件名称")
    private String softwareName;

    @ApiModelProperty(value="软件版本")
    @LogParam(description="软件版本")
    private String version;

    @ApiModelProperty(value="版本描述")
    @LogParam(description="版本描述")
    private String description;

    @ApiModelProperty(value="用户ID")
    @LogParam(description="用户ID")
    private String userId;

    @ApiModelProperty(value="更新人")
    @LogParam(description="更新人")
    private String modifyByName;

    @ApiModelProperty(value="车辆型号ID")
    @LogParam(description="车辆型号ID")
    private String vehicleTypeId;

    @ApiModelProperty(value="车辆型号")
    @LogParam(description="车辆型号")
    private String vehicleTypeName;

    @ApiModelProperty(value="车辆ID")
    @LogParam(description="车辆ID")
    private String vehicleId;

    @ApiModelProperty(value="车辆名字")
    @LogParam(description="车辆名字")
    private String vehicleName;


    @ApiModelProperty(value="部件型号ID")
    @LogParam(description="部件型号ID")
    private String componentTypeId;

    @ApiModelProperty(value="部件型号名称")
    @LogParam(description="部件型号名称")
    private String componentTypeName;

    @ApiModelProperty(value="计划更新软件时间")
    @LogParam(description="计划更新软件时间")
    private Date planTime;

    @ApiModelProperty(value="软件实际更新时间")
    @LogParam(description="软件实际更新时间")
    private Date realUpdateTime;

    @ApiModelProperty(value="软件版本数据更新时间")
    @LogParam(description="软件版本数据更新时间")
    private Date modifyTime;

    @ApiModelProperty(value="线路Id")
    @LogParam(description="线路Id")
    private String lineId;

    @ApiModelProperty(value="线路名称")
    @LogParam(description="线路名称")
    private String lineName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSoftwareName() {
        return softwareName;
    }

    public void setSoftwareName(String softwareName) {
        this.softwareName = softwareName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getModifyByName() {
        return modifyByName;
    }

    public void setModifyByName(String modifyByName) {
        this.modifyByName = modifyByName;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getVehicleTypeName() {
        return vehicleTypeName;
    }

    public void setVehicleTypeName(String vehicleTypeName) {
        this.vehicleTypeName = vehicleTypeName;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getVehicleName() {
        return vehicleName;
    }

    public void setVehicleName(String vehicleName) {
        this.vehicleName = vehicleName;
    }

    public String getComponentTypeId() {
        return componentTypeId;
    }

    public void setComponentTypeId(String componentTypeId) {
        this.componentTypeId = componentTypeId;
    }

    public String getComponentTypeName() {
        return componentTypeName;
    }

    public void setComponentTypeName(String componentTypeName) {
        this.componentTypeName = componentTypeName;
    }

    public Date getPlanTime() {
        return planTime;
    }

    public void setPlanTime(Date planTime) {
        this.planTime = planTime;
    }

    public Date getRealUpdateTime() {
        return realUpdateTime;
    }

    public void setRealUpdateTime(Date realUpdateTime) {
        this.realUpdateTime = realUpdateTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }
}
