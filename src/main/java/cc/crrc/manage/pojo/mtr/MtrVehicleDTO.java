package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.PageVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

public class MtrVehicleDTO extends PageVO {
    @ApiModelProperty(value="车辆名称")
    @LogParam(description="车辆名称")
    private String name;
    @ApiModelProperty(value="车辆编码")
    @LogParam(description="车辆编码")
    private String vehicleCode;
    @ApiModelProperty(value="车辆型号id")
    @LogParam(description="车辆型号id")
    private String vehicleTypeId;
    @JsonIgnore
    @ApiModelProperty(value="线路id")
    @LogParam(description="线路id")
    private String metroLineId;
    @ApiModelProperty(value="车辆ID")
    @LogParam(description="车辆ID")
    private String vehicleId;
    @ApiModelProperty(value="构型id list")
    @LogParam(description="构型id list")
    private String idList;

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getMetroLineId() {
        return metroLineId;
    }

    public void setMetroLineId(String metroLineId) {
        this.metroLineId = metroLineId;
    }

    public String getIdList() {
        return idList;
    }

    public void setIdList(String idList) {
        this.idList = idList;
    }
}
