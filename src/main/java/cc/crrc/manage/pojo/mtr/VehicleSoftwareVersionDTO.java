package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.PageVO;
import io.swagger.annotations.ApiModelProperty;


/**
 * <AUTHOR>
 * 2019/11/18
 **/
public class VehicleSoftwareVersionDTO extends PageVO {

    @ApiModelProperty(value="车载软件名称")
    @LogParam(description="车载软件名称")
    private String softwareName;

    @ApiModelProperty(value="软件版本")
    @LogParam(description="软件版本")
    private String version;

    @ApiModelProperty(value="更新人")
    @LogParam(description="更新人")
    private String modifyByName;

    @ApiModelProperty(value="车辆型号ID")
    @LogParam(description="车辆型号ID")
    private String vehicleTypeId;

    @ApiModelProperty(value="车辆ID")
    @LogParam(description="车辆ID")
    private String vehicleId;

    @ApiModelProperty(value="部件型号ID")
    @LogParam(description="部件型号ID")
    private String componentTypeId;
    
    @ApiModelProperty(value="起始时间")
    @LogParam(description="起始时间")
    private String startTime;
    @ApiModelProperty(value="结束时间")
    @LogParam(description="结束时间")
    private String endTime;

    @ApiModelProperty(value="线路Id")
    @LogParam(description="线路Id")
    private String lineId;

    @ApiModelProperty(value="线路名称")
    @LogParam(description="线路名称")
    private String lineName;

    public String getSoftwareName() {
        return softwareName;
    }

    public void setSoftwareName(String softwareName) {
        this.softwareName = softwareName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getModifyByName() {
        return modifyByName;
    }

    public void setModifyByName(String modifyByName) {
        this.modifyByName = modifyByName;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getComponentTypeId() {
        return componentTypeId;
    }

    public void setComponentTypeId(String componentTypeId) {
        this.componentTypeId = componentTypeId;
    }

	public String getStartTime() {
		return startTime;
	}

	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
}
