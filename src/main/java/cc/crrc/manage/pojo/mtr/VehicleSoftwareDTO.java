package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.PageVO;
import io.swagger.annotations.ApiModelProperty;


/**
 * <AUTHOR>
 * 2019/11/18
 **/
public class VehicleSoftwareDTO extends PageVO {

    @ApiModelProperty(value="车载软件名称")
    @LogParam(description="车载软件名称")
    private String softwareName;


    @ApiModelProperty(value="车载软件ID")
    @LogParam(description="车载软件ID")
    private String id;

    @ApiModelProperty(value="软件版本")
    @LogParam(description="软件版本")
    private String version;

    @ApiModelProperty(value="版本描述")
    @LogParam(description="版本描述")
    private String description;

    @ApiModelProperty(value="制造商ID")
    @LogParam(description="制造商ID")
    private String manufacturerId;

    @ApiModelProperty(value="制造商")
    @LogParam(description="制造商")
    private String manufacturerName;

    @ApiModelProperty(value="更新人")
    @LogParam(description="更新人")
    private String createByName;

    @ApiModelProperty(value="车辆型号ID")
    @LogParam(description="车辆型号ID")
    private String vehicleTypeId;

    @ApiModelProperty(value="车辆型号")
    @LogParam(description="车辆型号")
    private String vehicleTypeName;

    @ApiModelProperty(value="部件型号ID")
    @LogParam(description="部件型号ID")
    private String componentTypeId;

    @ApiModelProperty(value="部件型号名称")
    @LogParam(description="部件型号名称")
    private String componentTypeName;

    @ApiModelProperty(value="线路ID")
    @LogParam(description="线路ID")
    private String lineId;

    @ApiModelProperty(value="线路名称")
    @LogParam(description="线路名称")
    private String lineName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getManufacturerId() {
        return manufacturerId;
    }

    public void setManufacturerId(String manufacturerId) {
        this.manufacturerId = manufacturerId;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getComponentTypeId() {
        return componentTypeId;
    }

    public void setComponentTypeId(String componentTypeId) {
        this.componentTypeId = componentTypeId;
    }

    public String getSoftwareName() {
        return softwareName;
    }

    public void setSoftwareName(String softwareName) {
        this.softwareName = softwareName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getManufacturerName() {
        return manufacturerName;
    }

    public void setManufacturerName(String manufacturerName) {
        this.manufacturerName = manufacturerName;
    }

    public String getCreateByName() {
        return createByName;
    }

    public void setCreateByName(String createByName) {
        this.createByName = createByName;
    }

    public String getVehicleTypeName() {
        return vehicleTypeName;
    }

    public void setVehicleTypeName(String vehicleTypeName) {
        this.vehicleTypeName = vehicleTypeName;
    }

    public String getComponentTypeName() {
        return componentTypeName;
    }

    public void setComponentTypeName(String componentTypeName) {
        this.componentTypeName = componentTypeName;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }
}
