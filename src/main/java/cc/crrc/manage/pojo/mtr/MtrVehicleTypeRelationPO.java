package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

/**
 * <AUTHOR>
 * 2019/12/3
 **/
public class MtrVehicleTypeRelationPO {
    @LogParam(description="车辆id")
    @ApiModelProperty(value="车辆id",dataType = "String")
    private String vehicleId;
    @LogParam(description="车辆类型id")
    @ApiModelProperty(value="车辆类型id",dataType = "String")
    private String vehicleTypeId;
    @LogParam(description="生效日期")
    @ApiModelProperty(value="生效日期",dataType = "Date")
    private Date startDate;
    @LogParam(description="失效日期")
    @ApiModelProperty(value="失效日期",dataType = "Date")
    private Date endDate;
    @LogParam(description="有效标识位")
    @ApiModelProperty(value="有效标识位",dataType = "Integer")
    private Integer valid;

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }
}
