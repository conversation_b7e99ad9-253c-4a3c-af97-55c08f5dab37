package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.LogParam;

/**
 * @ClassName FlawDetectionProductVO
 * @Description:
 * <AUTHOR>
 * @Date 2022/3/25 10:54
 * @Version 1.0
 */
public class FlawDetectionProductVO {
    @LogParam(description = "主键id")
    private String id;
    @LogParam(description = "产品编号")
    private String number;
    @LogParam(description = "有无裂缝")
    private String crack;
    @LogParam(description = "数量")
    private String quantity;
    @LogParam(description = "日期")
    private String date;
    @LogParam(description = "评定结果")
    private String result;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getCrack() {
        return crack;
    }

    public void setCrack(String crack) {
        this.crack = crack;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }
}
