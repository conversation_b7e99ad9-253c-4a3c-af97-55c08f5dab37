package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;


/**
 * <AUTHOR>
 * 2019/11/28
 **/
public class ConfigSoftwareComponentPO {

    @ApiModelProperty(value="车载软件ID")
    @LogParam(description="车载软件ID")
    private String softwareId;

    @ApiModelProperty(value="车辆ID")
    @LogParam(description="车辆ID")
    private String vehicleId;

    @ApiModelProperty(value="车辆中文名称")
    @LogParam(description="车辆中文名称")
    private String vehicleName;

    @ApiModelProperty(value="车辆结构名称")
    @LogParam(description="车辆结构名称")
    private String vehicleTypeStructureName;


    @ApiModelProperty(value="结构类型")
    @LogParam(description="结构类型")
    private String structureType;

    @ApiModelProperty(value="部件序列号")
    @LogParam(description="部件序列号")
    private String serialNumber;

    @ApiModelProperty(value="部件型号ID")
    @LogParam(description="部件型号ID")
    private String componentTypeId;

    @ApiModelProperty(value="部件型号名称")
    @LogParam(description="部件型号名称")
    private String componentTypeName;

    @ApiModelProperty(value="部件型号类别")
    @LogParam(description="部件型号类别")
    private String catalog;

    @ApiModelProperty(value="部件ID")
    @LogParam(description="部件ID")
    private String componentId;

    @ApiModelProperty(value="部件名称")
    @LogParam(description="部件名称")
    private String componentName;

    public String getSoftwareId() {
        return softwareId;
    }

    public void setSoftwareId(String softwareId) {
        this.softwareId = softwareId;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getVehicleName() {
        return vehicleName;
    }

    public void setVehicleName(String vehicleName) {
        this.vehicleName = vehicleName;
    }

    public String getVehicleTypeStructureName() {
        return vehicleTypeStructureName;
    }

    public void setVehicleTypeStructureName(String vehicleTypeStructureName) {
        this.vehicleTypeStructureName = vehicleTypeStructureName;
    }

    public String getStructureType() {
        return structureType;
    }

    public void setStructureType(String structureType) {
        this.structureType = structureType;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getComponentTypeId() {
        return componentTypeId;
    }

    public void setComponentTypeId(String componentTypeId) {
        this.componentTypeId = componentTypeId;
    }

    public String getComponentTypeName() {
        return componentTypeName;
    }

    public void setComponentTypeName(String componentTypeName) {
        this.componentTypeName = componentTypeName;
    }

    public String getCatalog() {
        return catalog;
    }

    public void setCatalog(String catalog) {
        this.catalog = catalog;
    }

    public String getComponentId() {
        return componentId;
    }

    public void setComponentId(String componentId) {
        this.componentId = componentId;
    }

    public String getComponentName() {
        return componentName;
    }

    public void setComponentName(String componentName) {
        this.componentName = componentName;
    }
}
