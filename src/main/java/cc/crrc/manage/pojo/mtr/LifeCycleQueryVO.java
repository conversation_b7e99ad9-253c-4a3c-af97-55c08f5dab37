package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.ArrayList;

/**
 * @Description 车辆管理-生命周期
 * @author: Guo<PERSON>
 * 2020-05-07
 */
public class LifeCycleQueryVO implements Serializable {
    @ApiModelProperty(value = "构型码")
    @LogParam(description = "构型码")
    String structureCode;
    @ApiModelProperty(value = "车辆id")
    @LogParam(description = "车辆id")
    String vehicleId;
    @ApiModelProperty(value = "履历类型")
    @LogParam(description = "履历类型")
    ArrayList<String> type;
    @ApiModelProperty(value = "筛选开始时间")
    @LogParam(description = "筛选开始时间")
    String beginTime;
    @ApiModelProperty(value = "筛选结束时间")
    @LogParam(description = "筛选结束时间")
    String endTime;

    public String getStructureCode() {
        return structureCode;
    }

    public void setStructureCode(String structureCode) {
        this.structureCode = structureCode;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public ArrayList<String> getType() {
        return type;
    }

    public void setType(ArrayList<String> type) {
        this.type = type;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
