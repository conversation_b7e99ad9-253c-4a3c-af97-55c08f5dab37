package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.pojo.SysFileVO;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

public class ManufacturerFileDTO extends SysFileVO {
    @NotNull(message = "制造商id不能为空" ,groups= {Insert.class})
    @ApiModelProperty(value="制造商id",dataType = "String")
    @LogParam(description="制造商id")
    private String ManufacturerId;

    public String getManufacturerId() {
        return ManufacturerId;
    }

    public void setManufacturerId(String manufacturerId) {
        ManufacturerId = manufacturerId;
    }
}
