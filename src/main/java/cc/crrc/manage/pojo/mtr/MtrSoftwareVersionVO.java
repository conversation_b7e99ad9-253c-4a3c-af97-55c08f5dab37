package cc.crrc.manage.pojo.mtr;

import java.sql.Timestamp;
import java.util.List;

public class MtrSoftwareVersionVO {

	private String id;
	
    private String maxversion;

    private String maxrank;

    private String name;

    private String vehicleCode;

    private String subsystem;

    private String location;

    private String isRed;

    private Timestamp createTime;

    private List<MtrSoftwareDorVersionVO> dorInfoList;

    private String signalNameEn;
    
    private String oldVersion;

    private String lineId;
    
    public String getOldVersion() {
		return oldVersion;
	}

	public void setOldVersion(String oldVersion) {
		this.oldVersion = oldVersion;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getSignalNameEn() {
		return signalNameEn;
	}

	public void setSignalNameEn(String signalNameEn) {
		this.signalNameEn = signalNameEn;
	}

	public List<MtrSoftwareDorVersionVO> getDorInfoList() {
        return dorInfoList;
    }

    public void setDorInfoList(List<MtrSoftwareDorVersionVO> dorInfoList) {
        this.dorInfoList = dorInfoList;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMaxversion() {
        return maxversion;
    }

    public void setMaxversion(String maxversion) {
        this.maxversion = maxversion;
    }

    public String getMaxrank() {
        return maxrank;
    }

    public void setMaxrank(String maxrank) {
        this.maxrank = maxrank;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getIsRed() {
        return isRed;
    }

    public void setIsRed(String isRed) {
        this.isRed = isRed;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
}
