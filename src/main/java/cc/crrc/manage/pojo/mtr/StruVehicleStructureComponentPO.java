package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.group.Update;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @FileName StruVehicleStructureComponentPO
 * <AUTHOR> xin
 * @Date 2020/3/18 10:48
 * @Version 1.0
 **/
public class StruVehicleStructureComponentPO {

    // 构型id
	@NotNull(message = "id不能为空" ,groups= {Update.class})
    private String id;
    // 部件id
    private String componentId;
    // 车辆id
    private String vehicleId;
    // 父结构编码
    private String parentStructureCode;
    // 结构编码
    private String structureCode;
    // 记录生效日期
    private Date assemblyTime;
    // 记录失效日期
    private Date disassemblyTime;
    // 部件有效标识
    private int valid;
    // 结构中文名
    @NotBlank(message = "构型中文名称不能为空" ,groups= {Update.class})
    private String nameCn;
    // 结构英文名
    private String nameEn;
    // 结构英文名缩写
    @NotBlank(message = "构型英文缩写不能为空" ,groups= {Update.class})
    private String shortNameEn;
    // 父节点集合（用构型英文名缩写）
    private String structurePosition;
    // 结构类型（车厢/子系统/部件）
    private String structureType;
    // 部件类型id
    private String componentTypeId;
    // 顺序编号
    private Long sortNumber;
    // 记录生效日期
    private Date startDate;
    // 记录失效日期
    private Date endDate;
    // 备注
    private String remark;
    @JsonIgnore
    private String createBy;
    @JsonIgnore
    private Date createTime;
    @JsonIgnore
    private String modifyBy;
    @JsonIgnore
    private Date modifyTime;
    //删除状态
    private Integer delFlag;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getComponentId() {
        return componentId;
    }

    public void setComponentId(String componentId) {
        this.componentId = componentId;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getParentStructureCode() {
        return parentStructureCode;
    }

    public void setParentStructureCode(String parentStructureCode) {
        this.parentStructureCode = parentStructureCode;
    }

    public String getStructureCode() {
        return structureCode;
    }

    public void setStructureCode(String structureCode) {
        this.structureCode = structureCode;
    }

    public Date getAssemblyTime() {
        return assemblyTime;
    }

    public void setAssemblyTime(Date assemblyTime) {
        this.assemblyTime = assemblyTime;
    }

    public Date getDisassemblyTime() {
        return disassemblyTime;
    }

    public void setDisassemblyTime(Date disassemblyTime) {
        this.disassemblyTime = disassemblyTime;
    }

    public int getValid() {
        return valid;
    }

    public void setValid(int valid) {
        this.valid = valid;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getShortNameEn() {
        return shortNameEn;
    }

    public void setShortNameEn(String shortNameEn) {
        this.shortNameEn = shortNameEn;
    }

    public String getStructurePosition() {
        return structurePosition;
    }

    public void setStructurePosition(String structurePosition) {
        this.structurePosition = structurePosition;
    }

    public String getStructureType() {
        return structureType;
    }

    public void setStructureType(String structureType) {
        this.structureType = structureType;
    }

    public String getComponentTypeId() {
        return componentTypeId;
    }

    public void setComponentTypeId(String componentTypeId) {
        this.componentTypeId = componentTypeId;
    }

    public Long getSortNumber() {
        return sortNumber;
    }

    public void setSortNumber(Long sortNumber) {
        this.sortNumber = sortNumber;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

}
