package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.pojo.excel.StruVehicleTypeStructureForExcelPO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @FileName MtrVehicleTypeStructureVO
 * <AUTHOR> yuxi
 * @Date 2019/12/13 16:17
 * @Version 1.0
 **/
@Duplicates({
        @Duplicate(table = "mtr_vehicle_type", message = "车辆型号名称已存在！", groups = {Insert.class}, condition = "name = '${name}' AND del_flag = '0'"),
        @Duplicate(table = "mtr_vehicle_type", message = "车辆型号名称已存在！", groups = {Update.class}, condition = "name = '${name}' AND id != '${vehicleTypeId}' AND del_flag = '0'"),
        @Duplicate(table = "stru_vehicle_type_structure", message = "结构编码已存在", condition = "structure_code = '${structureCode}' AND vehicle_type_id = '${vehicleTypeId}' AND del_flag = '0'", groups = {Insert.class}),
        @Duplicate(table = "stru_vehicle_type_structure", message = "结构编码已存在", condition = "structure_code = '${structureCode}' AND vehicle_type_id = '${vehicleTypeId}' AND id != '${structureId}' and del_flag = '0'", groups = {Update.class})
})
public class MtrVehicleTypeStructureVO {
    @LogParam(description = "构型id")
    private String structureId;
    @LogParam(description = "车辆型号名称")
    @NotBlank(message = "车辆型号名称不能为空！", groups = {Insert.class, Update.class})
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
    private String name;
    @LogParam(description = "车辆类型")
    @NotBlank(message = "车辆类型不能为空！", groups = {Insert.class, Update.class})
    private String type;
    @LogParam(description = "编组数量")
    @NotNull(message = "编组数量不能为空！", groups = {Insert.class, Update.class})
    private Integer marshallingNumber;
    @LogParam(description = "制造商主键id")
    private String manufacturerId;
    @LogParam(description = "通信方式")
    private String commType;
    @LogParam(description = "车辆型号id")
    private String vehicleTypeId;
    @ApiModelProperty(hidden = true)
    @LogParam(description = "父结构编码")
    private String parentStructureCode;
    @NotBlank(message = "结构编码不能为空！", groups = {Insert.class, Update.class})
    @LogParam(description = "结构编码")
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
    private String structureCode;
    @ApiModelProperty(hidden = true)
    @LogParam(description = "构型中文名")
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
    private String nameCn;
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
    @LogParam(description = "构型英文名")
    private String nameEn;
    @LogParam(description = "构型英文缩写")
    @NotNull(message = "构型英文缩写不能为空", groups = {Insert.class, Update.class})
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
    private String shortNameEn;
    @LogParam(description = "父节点集合，用构型英文缩写")
    @ApiModelProperty(hidden = true)
    private String structurePosition;
    @ApiModelProperty(hidden = true)
    @LogParam(description = "结构类型")
    private String structureType;
    @ApiModelProperty(hidden = true)
    @LogParam(description = "节点类型")
    private String nodeType;
    @LogParam(description = "部件型号id")
    private String componentTypeId;
    @LogParam(description = "排序")
    private Long sortNumber;
    @LogParam(description = "车型code提供fracas使用")
    private String vehicleTypeCode;
    @ApiModelProperty(example = "2018-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @LogParam(description = "记录生效日期")
    private Date startDate;
    @LogParam(description = "备注")
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private String remark;
    @LogParam(description = "线路id")
    private String lineId;

    private List<StruVehicleTypeStructureForExcelPO> struVehicleTypeStructureForExcelList;

    public String getStructureId() {
        return structureId;
    }

    public void setStructureId(String structureId) {
        this.structureId = structureId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getMarshallingNumber() {
        return marshallingNumber;
    }

    public void setMarshallingNumber(Integer marshallingNumber) {
        this.marshallingNumber = marshallingNumber;
    }

    public String getManufacturerId() {
        return manufacturerId;
    }

    public void setManufacturerId(String manufacturerId) {
        this.manufacturerId = manufacturerId;
    }

    public String getCommType() {
        return commType;
    }

    public void setCommType(String commType) {
        this.commType = commType;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getParentStructureCode() {
        return parentStructureCode;
    }

    public void setParentStructureCode(String parentStructureCode) {
        this.parentStructureCode = parentStructureCode;
    }

    public String getStructureCode() {
        return structureCode;
    }

    public void setStructureCode(String structureCode) {
        this.structureCode = structureCode;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getShortNameEn() {
        return shortNameEn;
    }

    public void setShortNameEn(String shortNameEn) {
        this.shortNameEn = shortNameEn;
    }

    public String getStructurePosition() {
        return structurePosition;
    }

    public void setStructurePosition(String structurePosition) {
        this.structurePosition = structurePosition;
    }

    public String getStructureType() {
        return structureType;
    }

    public void setStructureType(String structureType) {
        this.structureType = structureType;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }

    public String getComponentTypeId() {
        return componentTypeId;
    }

    public void setComponentTypeId(String componentTypeId) {
        this.componentTypeId = componentTypeId;
    }

    public Long getSortNumber() {
        return sortNumber;
    }

    public void setSortNumber(Long sortNumber) {
        this.sortNumber = sortNumber;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getVehicleTypeCode() {
        return vehicleTypeCode;
    }

    public void setVehicleTypeCode(String vehicleTypeCode) {
        this.vehicleTypeCode = vehicleTypeCode;
    }

    public List<StruVehicleTypeStructureForExcelPO> getStruVehicleTypeStructureForExcelList() {
        return struVehicleTypeStructureForExcelList;
    }

    public void setStruVehicleTypeStructureForExcelList(List<StruVehicleTypeStructureForExcelPO> struVehicleTypeStructureForExcelList) {
        this.struVehicleTypeStructureForExcelList = struVehicleTypeStructureForExcelList;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
}
