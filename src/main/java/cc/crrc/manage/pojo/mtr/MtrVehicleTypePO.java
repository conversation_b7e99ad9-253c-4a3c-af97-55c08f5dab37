package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @FileName MtrVehicleTypePO
 * <AUTHOR> yuxi
 * @Date 2019/11/16 14:33
 * @Version 1.0
 **/
@Duplicates({
        @Duplicate(table = "mtr_vehicle_type", message = "车辆型号名称已存在！", groups = {Insert.class}, condition = "name = '${name}'"),
        @Duplicate(table = "mtr_vehicle_type", message = "车辆型号名称已存在！", groups = {Update.class}, condition = "name = '${name}' AND id != '${id}'")
})
public class MtrVehicleTypePO {
    @LogParam(description = "车型id")
    private String id;
    @LogParam(description = "车辆型号名称")
    @NotBlank(message = "车辆型号名称不能为空！", groups = {Insert.class})
    private String name;
    @LogParam(description = "车辆类型")
    @NotBlank(message = "车辆类型不能为空！", groups = {Insert.class})
    private String type;
    @LogParam(description = "编组数量")
    @NotNull(message = "编组数量不能为空！", groups = {Insert.class})
    private Integer marshallingNumber;
    @LogParam(description = "制造商主键id")
    private String manufacturerId;
    @LogParam(description = "通信方式")
    private String commType;
    @LogParam(description = "车型code提供fracas使用")
    private String vehicleTypeCode;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Integer delFlag;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String createBy;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Date createTime;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String modifyBy;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Date modifyTime;
    @LogParam(description = "备注")
    private String remark;
    @LogParam(description = "线路id")
    private String lineId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getMarshallingNumber() {
        return marshallingNumber;
    }

    public void setMarshallingNumber(Integer marshallingNumber) {
        this.marshallingNumber = marshallingNumber;
    }

    public String getManufacturerId() {
        return manufacturerId;
    }

    public void setManufacturerId(String manufacturerId) {
        this.manufacturerId = manufacturerId;
    }

    public String getCommType() {
        return commType;
    }

    public void setCommType(String commType) {
        this.commType = commType;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getVehicleTypeCode() {
        return vehicleTypeCode;
    }

    public void setVehicleTypeCode(String vehicleTypeCode) {
        this.vehicleTypeCode = vehicleTypeCode;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
}
