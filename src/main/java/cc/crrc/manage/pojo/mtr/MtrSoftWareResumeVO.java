package cc.crrc.manage.pojo.mtr;

import java.util.Date;

public class MtrSoftWareResumeVO {
    private String sonId;
    private String sonName;
    private String sonVersion;
    private String sonDescription;
    private String sonCreateBy;
    private Date sonCreateTime;
    private String sonModifyBy;
    private String sonModifyTime;
    private String sonVehicleStructureCode;
    private String sonVehicleCode;
    private Date sonEffectiveTime;//生效日期
    private Date sonEndTime;
    private String sonSignalNameEn;
    private String sonOperator;//操作者
    private String sonLineId;
    private String sonLineName;
    private String sonVehicleTypeId;
    private String sonVehicleTypeName;

    public String getSonId() {
        return sonId;
    }

    public void setSonId(String sonId) {
        this.sonId = sonId;
    }

    public String getSonName() {
        return sonName;
    }

    public void setSonName(String sonName) {
        this.sonName = sonName;
    }

    public String getSonVersion() {
        return sonVersion;
    }

    public void setSonVersion(String sonVersion) {
        this.sonVersion = sonVersion;
    }

    public String getSonDescription() {
        return sonDescription;
    }

    public void setSonDescription(String sonDescription) {
        this.sonDescription = sonDescription;
    }

    public String getSonCreateBy() {
        return sonCreateBy;
    }

    public void setSonCreateBy(String sonCreateBy) {
        this.sonCreateBy = sonCreateBy;
    }

    public Date getSonCreateTime() {
        return sonCreateTime;
    }

    public void setSonCreateTime(Date sonCreateTime) {
        this.sonCreateTime = sonCreateTime;
    }

    public String getSonModifyBy() {
        return sonModifyBy;
    }

    public void setSonModifyBy(String sonModifyBy) {
        this.sonModifyBy = sonModifyBy;
    }

    public String getSonModifyTime() {
        return sonModifyTime;
    }

    public void setSonModifyTime(String sonModifyTime) {
        this.sonModifyTime = sonModifyTime;
    }

    public String getSonVehicleStructureCode() {
        return sonVehicleStructureCode;
    }

    public void setSonVehicleStructureCode(String sonVehicleStructureCode) {
        this.sonVehicleStructureCode = sonVehicleStructureCode;
    }

    public String getSonVehicleCode() {
        return sonVehicleCode;
    }

    public void setSonVehicleCode(String sonVehicleCode) {
        this.sonVehicleCode = sonVehicleCode;
    }

    public Date getSonEffectiveTime() {
        return sonEffectiveTime;
    }

    public void setSonEffectiveTime(Date sonEffectiveTime) {
        this.sonEffectiveTime = sonEffectiveTime;
    }

    public Date getSonEndTime() {
        return sonEndTime;
    }

    public void setSonEndTime(Date sonEndTime) {
        this.sonEndTime = sonEndTime;
    }

    public String getSonSignalNameEn() {
        return sonSignalNameEn;
    }

    public void setSonSignalNameEn(String sonSignalNameEn) {
        this.sonSignalNameEn = sonSignalNameEn;
    }

    public String getSonOperator() {
        return sonOperator;
    }

    public void setSonOperator(String sonOperator) {
        this.sonOperator = sonOperator;
    }

    public String getSonLineId() {
        return sonLineId;
    }

    public void setSonLineId(String sonLineId) {
        this.sonLineId = sonLineId;
    }

    public String getSonLineName() {
        return sonLineName;
    }

    public void setSonLineName(String sonLineName) {
        this.sonLineName = sonLineName;
    }

    public String getSonVehicleTypeId() {
        return sonVehicleTypeId;
    }

    public void setSonVehicleTypeId(String sonVehicleTypeId) {
        this.sonVehicleTypeId = sonVehicleTypeId;
    }

    public String getSonVehicleTypeName() {
        return sonVehicleTypeName;
    }

    public void setSonVehicleTypeName(String sonVehicleTypeName) {
        this.sonVehicleTypeName = sonVehicleTypeName;
    }
}
