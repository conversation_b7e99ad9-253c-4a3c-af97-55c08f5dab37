package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.pojo.PageVO;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import java.io.Serializable;
import java.util.Date;

/**
 * null
 *
 * @TableName mtr_software_mapping
 */
public class MtrSoftwareVersionMappingVO extends PageVO implements Serializable {
    /**
     * 主键
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    private String id;

    /**
     * 软件版本英文名（同软件不同版本 此值唯一）
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    private String signalNameEn;

    /**
     * 软件中文名称
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    private String name;

    /**
     * 采集信号（存在一个信号 或者多个信号的情况）
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    private String signalList;

    /**
     * 关联线路id
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    private String lineId;

    /**
     * 关联车型id
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    private String vehicleTypeId;

    /**
     * 关联车型构型编码
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    private String structureCode;

    /**
     * 创建用户
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    private String createBy;

    /**
     * 创建时间
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    private Date createTime;

    /**
     * 修改用户编号
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    private String modifyBy;

    /**
     * 修改时间
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    private Date modifyTime;

    /**
     * 逻辑删除标志，1：逻辑删除，0：未删除
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    private Short delFlag;

    /**
     * 构型中文名称
     */
    private String structureName;

    /**
     * 车型中文名称
     */
    private String vehicleTypeName;

    /**
     * 线路名称
     */
    private String lineName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table mtr_software_mapping
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    private static final long serialVersionUID = 1L;

    private String subsystem;
    private String location;

    /**
     * 主键
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public String getId() {
        return id;
    }

    /**
     * 主键
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 软件版本英文名（同软件不同版本 此值唯一）
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public String getSignalNameEn() {
        return signalNameEn;
    }

    /**
     * 软件版本英文名（同软件不同版本 此值唯一）
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public void setSignalNameEn(String signalNameEn) {
        this.signalNameEn = signalNameEn;
    }

    /**
     * 软件中文名称
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public String getName() {
        return name;
    }

    /**
     * 软件中文名称
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 采集信号（存在一个信号 或者多个信号的情况）
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public String getSignalList() {
        return signalList;
    }

    /**
     * 采集信号（存在一个信号 或者多个信号的情况）
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public void setSignalList(String signalList) {
        this.signalList = signalList;
    }

    /**
     * 关联线路id
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public String getLineId() {
        return lineId;
    }

    /**
     * 关联线路id
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    /**
     * 关联车型id
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    /**
     * 关联车型id
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    /**
     * 关联车型构型编码
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public String getStructureCode() {
        return structureCode;
    }

    /**
     * 关联车型构型编码
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public void setStructureCode(String structureCode) {
        this.structureCode = structureCode;
    }

    /**
     * 创建用户
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public String getCreateBy() {
        return createBy;
    }

    /**
     * 创建用户
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    /**
     * 创建时间
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 修改用户编号
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public String getModifyBy() {
        return modifyBy;
    }

    /**
     * 修改用户编号
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    /**
     * 修改时间
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public Date getModifyTime() {
        return modifyTime;
    }

    /**
     * 修改时间
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    /**
     * 逻辑删除标志，1：逻辑删除，0：未删除
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public Short getDelFlag() {
        return delFlag;
    }

    /**
     * 逻辑删除标志，1：逻辑删除，0：未删除
     *
     * @mbg.generated 2021-11-08 15:18:00
     */
    public void setDelFlag(Short delFlag) {
        this.delFlag = delFlag;
    }

    public String getStructureName() {
        return structureName;
    }

    public void setStructureName(String structureName) {
        this.structureName = structureName;
    }

    public String getVehicleTypeName() {
        return vehicleTypeName;
    }

    public void setVehicleTypeName(String vehicleTypeName) {
        this.vehicleTypeName = vehicleTypeName;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
}