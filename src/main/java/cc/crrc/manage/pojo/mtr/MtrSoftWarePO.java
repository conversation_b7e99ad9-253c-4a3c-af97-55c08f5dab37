package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import java.util.Date;

public class MtrSoftWarePO {
    private String id;
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class, Update.class})
    private String name;
    private String version;
    private String description;
    private String createBy;
    private Date createTime;
    private String softwareNameCn;
    private String modifyBy;
    private String modifyTime;
    private String vehicleStructureCode;
    private String vehicleCode;
    //生效日期
    private Date effectiveTime;
    //失效日期
    private Date endTime;
    private Date updateTime;
    private String signalNameEn;
    private String softwareNameEn;
    //操作者
    private String operator;
    //线路id
    private String lineId;
    private String subsystem;
    private String location;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getSoftwareNameCn() {
        return softwareNameCn;
    }

    public void setSoftwareNameCn(String softwareNameCn) {
        this.softwareNameCn = softwareNameCn;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getVehicleStructureCode() {
        return vehicleStructureCode;
    }

    public void setVehicleStructureCode(String vehicleStructureCode) {
        this.vehicleStructureCode = vehicleStructureCode;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public Date getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(Date effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getSignalNameEn() {
        return signalNameEn;
    }

    public void setSignalNameEn(String signalNameEn) {
        this.signalNameEn = signalNameEn;
    }

    public String getSoftwareNameEn() {
        return softwareNameEn;
    }

    public void setSoftwareNameEn(String softwareNameEn) {
        this.softwareNameEn = softwareNameEn;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    @Override
    public String toString() {
        return "线路："+this.getLineId()+"  车号："+this.getVehicleCode()+"  信号："+this.getSignalNameEn()+"  版本："+this.getVersion();
    }
}
