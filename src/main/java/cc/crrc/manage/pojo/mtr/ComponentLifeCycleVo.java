package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

public class ComponentLifeCycleVo {
	@ApiModelProperty(value = "部件ID")
	@LogParam(description = "部件ID")
	private String id;
	@ApiModelProperty(value = "制造商名称")
	@LogParam(description = "制造商名称")
	private String manufacturerName;
	@ApiModelProperty(value = "中文名")
	@LogParam(description = "中文名")
	private String nameCn;
	@ApiModelProperty(value = "英文名")
	@LogParam(description = "英文名")
	private String nameEn;
	@ApiModelProperty(value = "产品编号")
	@LogParam(description = "产品编号")
	private String productNumber;
	@ApiModelProperty(value = "产品类别")
	@LogParam(description = "产品类别")
	private String catalog;
	@ApiModelProperty(value = "序列号")
	@LogParam(description = "序列号")
	private String serialNumber;
	@ApiModelProperty(value = "生产日期")
	@LogParam(description = "生产日期")
	private String productionDate;
	@ApiModelProperty(value = "换上时间")
	@LogParam(description = "换上时间")
	private String assemblyTime;
	@ApiModelProperty(value = "换下时间")
	@LogParam(description = "换下时间")
	private String disassemblyTime;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getManufacturerName() {
		return manufacturerName;
	}

	public void setManufacturerName(String manufacturerName) {
		this.manufacturerName = manufacturerName;
	}

	public String getNameCn() {
		return nameCn;
	}

	public void setNameCn(String nameCn) {
		this.nameCn = nameCn;
	}

	public String getNameEn() {
		return nameEn;
	}

	public void setNameEn(String nameEn) {
		this.nameEn = nameEn;
	}

	public String getProductNumber() {
		return productNumber;
	}

	public void setProductNumber(String productNumber) {
		this.productNumber = productNumber;
	}

	public String getCatalog() {
		return catalog;
	}

	public void setCatalog(String catalog) {
		this.catalog = catalog;
	}

	public String getSerialNumber() {
		return serialNumber;
	}

	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}

	public String getProductionDate() {
		return productionDate;
	}

	public void setProductionDate(String productionDate) {
		this.productionDate = productionDate;
	}

	public String getAssemblyTime() {
		return assemblyTime;
	}

	public void setAssemblyTime(String assemblyTime) {
		this.assemblyTime = assemblyTime;
	}

	public String getDisassemblyTime() {
		return disassemblyTime;
	}

	public void setDisassemblyTime(String disassemblyTime) {
		this.disassemblyTime = disassemblyTime;
	}

}
