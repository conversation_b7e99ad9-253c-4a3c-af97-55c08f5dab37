package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.LogParam;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @ClassName WheelMeasurementDataVO
 * @Description: 测量数据
 * <AUTHOR>
 * @Date 2022/3/15 15:54
 * @Version 1.0
 */
public class WheelMeasurementDataVO {
    @LogParam(description = "车厢")
    private String nameCn;
    @LogParam(description = "转向架")
    private String frame;
    @LogParam(description = "轴")
    private String axle;
    @LogParam(description = "轴位置")
    private String location;
    @LogParam(description = "轮径")
    private Float wheelDiameter;
    @LogParam(description = "轮缘厚度")
    private Float flangeThickness;
    @LogParam(description = "轮缘高度")
    private Float flangeHeight;
    @LogParam(description = "qR值")
    private Float QR;
    @LogParam(description = "轮径跳动")
    private Float diameterRunout;
    @LogParam(description = "内侧距")
    private Float insideDistance;
    private String labelVehicle;
    private String labelAxle;
    private String labelLocation;
    /**
     * @Description 添加查询结果是否在范围内(0-是 1-否)
     * @date 2022/3/31
     */
    private String diametersRange;
    private String thicknessRange;
    private String heightRange;
    private String qrRange;
    private String runoutRange;
    private String insideRange;

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getAxle() {
        return axle;
    }

    public void setAxle(String axle) {
        this.axle = axle;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Float getWheelDiameter() {
        return wheelDiameter;
    }

    public void setWheelDiameter(Float wheelDiameter) {
        this.wheelDiameter = wheelDiameter;
    }

    public Float getFlangeThickness() {
        return flangeThickness;
    }

    public void setFlangeThickness(Float flangeThickness) {
        this.flangeThickness = flangeThickness;
    }

    public Float getFlangeHeight() {
        return flangeHeight;
    }

    public void setFlangeHeight(Float flangeHeight) {
        this.flangeHeight = flangeHeight;
    }
    @JsonProperty("QR")
    public Float getQR() {
        return QR;
    }

    public void setQR(Float QR) {
        this.QR = QR;
    }

    public Float getDiameterRunout() {
        return diameterRunout;
    }

    public void setDiameterRunout(Float diameterRunout) {
        this.diameterRunout = diameterRunout;
    }

    public Float getInsideDistance() {
        return insideDistance;
    }

    public void setInsideDistance(Float insideDistance) {
        this.insideDistance = insideDistance;
    }

    public String getLabelVehicle() {
        return labelVehicle;
    }

    public void setLabelVehicle(String labelVehicle) {
        this.labelVehicle = labelVehicle;
    }

    public String getLabelAxle() {
        return labelAxle;
    }

    public void setLabelAxle(String labelAxle) {
        this.labelAxle = labelAxle;
    }

    public String getLabelLocation() {
        return labelLocation;
    }

    public void setLabelLocation(String labelLocation) {
        this.labelLocation = labelLocation;
    }

    public String getFrame() {
        return frame;
    }

    public void setFrame(String frame) {
        this.frame = frame;
    }

    public String getDiametersRange() {
        return diametersRange;
    }

    public void setDiametersRange(String diametersRange) {
        this.diametersRange = diametersRange;
    }

    public String getThicknessRange() {
        return thicknessRange;
    }

    public void setThicknessRange(String thicknessRange) {
        this.thicknessRange = thicknessRange;
    }

    public String getHeightRange() {
        return heightRange;
    }

    public void setHeightRange(String heightRange) {
        this.heightRange = heightRange;
    }

    public String getQrRange() {
        return qrRange;
    }

    public void setQrRange(String qrRange) {
        this.qrRange = qrRange;
    }

    public String getRunoutRange() {
        return runoutRange;
    }

    public void setRunoutRange(String runoutRange) {
        this.runoutRange = runoutRange;
    }

    public String getInsideRange() {
        return insideRange;
    }

    public void setInsideRange(String insideRange) {
        this.insideRange = insideRange;
    }
}
