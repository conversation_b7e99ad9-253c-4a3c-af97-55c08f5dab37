package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.PageVO;
import io.swagger.annotations.ApiModelProperty;

/**
 * @FileName MtrVehicleTypeVO
 * <AUTHOR> yuxi
 * @Date 2019/11/18 10:34
 * @Version 1.0
 **/
public class MtrVehicleTypeVO extends PageVO {
    @LogParam(description = "车型id")
    @ApiModelProperty(hidden = true)
    private String id;
    @LogParam(description = "车辆型号名称")
    private String name;
    @LogParam(description = "车辆类型")
    private String type;
    @LogParam(description = "编组数量")
    private Integer marshallingNumber;
    @LogParam(description = "制造商主键id")
    private String manufacturerId;
    @LogParam(description = "通信方式")
    private String commType;
    @LogParam(description = "备注")
    private String remark;
    @LogParam(description = "制造商名称")
    private String manufacturerName;
    @LogParam(description = "车型code提供fracas使用")
    private String vehicleTypeCode;
    @LogParam(description = "线路id")
    private String lineId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getMarshallingNumber() {
        return marshallingNumber;
    }

    public void setMarshallingNumber(Integer marshallingNumber) {
        this.marshallingNumber = marshallingNumber;
    }

    public String getManufacturerId() {
        return manufacturerId;
    }

    public void setManufacturerId(String manufacturerId) {
        this.manufacturerId = manufacturerId;
    }

    public String getCommType() {
        return commType;
    }

    public void setCommType(String commType) {
        this.commType = commType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getManufacturerName() {
        return manufacturerName;
    }

    public void setManufacturerName(String manufacturerName) {
        this.manufacturerName = manufacturerName;
    }

    public String getVehicleTypeCode() {
        return vehicleTypeCode;
    }

    public void setVehicleTypeCode(String vehicleTypeCode) {
        this.vehicleTypeCode = vehicleTypeCode;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
}
