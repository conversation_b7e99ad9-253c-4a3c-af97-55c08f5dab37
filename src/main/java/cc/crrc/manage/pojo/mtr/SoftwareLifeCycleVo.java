package cc.crrc.manage.pojo.mtr;


import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

public class SoftwareLifeCycleVo {	
	 @ApiModelProperty(value="软件名称")
	 @LogParam(description="软件名称")	
	 private String name;
	 @ApiModelProperty(value="软件版本号")
	 @LogParam(description="软件版本号")	
	 private String version;
	 @ApiModelProperty(value="更新日期")
	 @LogParam(description="更新日期")	
	 private String updateTime;
	 @ApiModelProperty(value="当前状态")
	 @LogParam(description="当前状态")
	 private boolean valid;
	 @ApiModelProperty(value="制造商名称")
	 @LogParam(description="制造商名称")	
	 private String manufacturername;
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getVersion() {
		return version;
	}
	public void setVersion(String version) {
		this.version = version;
	}
	public String getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}
	public boolean isValid() {
		return valid;
	}
	public void setValid(boolean valid) {
		this.valid = valid;
	}
	public String getManufacturername() {
		return manufacturername;
	}
	public void setManufacturername(String manufacturername) {
		this.manufacturername = manufacturername;
	}

	 
}
