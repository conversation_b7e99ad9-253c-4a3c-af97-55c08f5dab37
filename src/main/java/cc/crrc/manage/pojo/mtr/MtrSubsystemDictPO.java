package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @FileName MtrSubsystemDictPO
 * <AUTHOR> yuxi
 * @Date 2019/11/19 11:31
 * @Version 1.0
 **/
public class MtrSubsystemDictPO {
    @LogParam(description = "车辆子系统字典id")
    @ApiModelProperty(hidden = true)
    private String id;
    @LogParam(description = "车型id")
    @NotNull(message = "车辆id不能为空", groups = {Insert.class})
    private String vehicleId;
    @NotBlank(message = "简称不能为空", groups = {Insert.class})
    @LogParam(description = "简称")
    private String code;
    @NotBlank(message = "编码不能为空", groups = {Insert.class})
    @LogParam(description = "编码")
    private String value;
    @NotBlank(message = "标签不能为空", groups = {Insert.class})
    @LogParam(description = "标签")
    private String label;
    @NotBlank(message = "类型不能为空", groups = {Insert.class})
    @LogParam(description = "类型")
    private String type;
    @LogParam(description = "描述")
    private String description;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String createBy;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Date createTime;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String modifyBy;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Date modifyTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }
}
