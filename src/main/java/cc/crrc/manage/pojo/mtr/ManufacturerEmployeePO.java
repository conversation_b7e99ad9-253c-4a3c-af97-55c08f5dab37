package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.Contained;
import cc.crrc.manage.common.annotation.Email;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.Phone;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * 2019/11/09
 **/
public class ManufacturerEmployeePO {
    @NotNull(message = "雇员id不能为空" ,groups= {Update.class})
    @ApiModelProperty(value="雇员id",dataType = "String")
    @LogParam(description="雇员id")
    private String id;
    @NotBlank(message = "姓名不能为空" ,groups= {Insert.class})
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
    @ApiModelProperty(value="姓名")
    @LogParam(description="姓名")
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25")
    private String name;
    @Contained(value = {"男", "女"}, message = "性别信息存在异常",groups= {Insert.class,Update.class})
    @NotBlank(message = "性别不能为空" ,groups= {Insert.class})
    @ApiModelProperty(value="性别")
    @LogParam(description="性别")
    private String gender;
    @NotNull(message = "所属制造商id不能为空" ,groups= {Insert.class})
    @ApiModelProperty(value="所属制造商id",dataType = "String")
    @LogParam(description="所属制造商id")
    private String manufacturerId;
    @Email(groups= {Insert.class,Update.class})
    @ApiModelProperty(value="电子邮箱")
    @LogParam(description="电子邮箱")
    private String email;
    @Phone(groups= {Insert.class,Update.class})
    @ApiModelProperty(value="联系电话")
    @LogParam(description="联系电话")
    private String phone;
    @ApiModelProperty(value="简介")
    @LogParam(description="简介")
    private String profile;
    @ApiModelProperty(value="职位")
    @LogParam(description="职位")
    private String type;
    @JsonIgnore
    private String createBy;
    @JsonIgnore
    private Date createTime;
    @JsonIgnore
    private String modifyBy;
    @JsonIgnore
    private Date modifyTime;
    @JsonIgnore
    private String remark;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getManufacturerId() {
        return manufacturerId;
    }

    public void setManufacturerId(String manufacturerId) {
        this.manufacturerId = manufacturerId;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
