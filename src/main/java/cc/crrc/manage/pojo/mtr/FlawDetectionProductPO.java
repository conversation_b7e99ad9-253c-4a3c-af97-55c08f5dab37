package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.LogParam;

/**
 * @ClassName FlawDetectionProductPO
 * @Description:
 * <AUTHOR>
 * @Date 2022/3/25 15:19
 * @Version 1.0
 */
public class FlawDetectionProductPO {
    @LogParam(description = "主键id")
    private String id;
    @LogParam(description = "探伤记录表关联id")
    private String flawDetectionId;
    @LogParam(description = "探伤方式")
    private String type;
    @LogParam(description = "产品编号")
    private String number;
    @LogParam(description = "有无裂缝")
    private String crack;
    @LogParam(description = "数量")
    private String quantity;
    @LogParam(description = "日期")
    private String date;
    @LogParam(description = "评定结果")
    private String result;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFlawDetectionId() {
        return flawDetectionId;
    }

    public void setFlawDetectionId(String flawDetectionId) {
        this.flawDetectionId = flawDetectionId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getCrack() {
        return crack;
    }

    public void setCrack(String crack) {
        this.crack = crack;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }
}
