package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.LogParam;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @ClassName WheelMeasurementDataPO
 * <AUTHOR>
 * @Date 2022/3/16 14:49
 * @Version 1.0
 */
public class WheelMeasurementDataPO {
    @LogParam(description = "主键id")
    private String id;
    @LogParam(description = "测量车辆表主键id")
    private String measurementVehicleId;
    @LogParam(description = "车厢")
    private String nameCn;
    @LogParam(description = "转向架")
    private String frame;
    @LogParam(description = "轴")
    private String axle;
    @LogParam(description = "轴位置")
    private String location;
    @LogParam(description = "轮径")
    private Float wheelDiameter;
    @LogParam(description = "轮缘厚度")
    private Float flangeThickness;
    @LogParam(description = "轮缘高度")
    private Float flangeHeight;
    @JsonProperty(value = "QR")
    private Float qR;
    @LogParam(description = "轮径跳动")
    private Float diameterRunout;
    @LogParam(description = "内侧距")
    private Float insideDistance;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMeasurementVehicleId() {
        return measurementVehicleId;
    }

    public void setMeasurementVehicleId(String measurementVehicleId) {
        this.measurementVehicleId = measurementVehicleId;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getFrame() {
        return frame;
    }

    public void setFrame(String frame) {
        this.frame = frame;
    }

    public String getAxle() {
        return axle;
    }

    public void setAxle(String axle) {
        this.axle = axle;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Float getWheelDiameter() {
        return wheelDiameter;
    }

    public void setWheelDiameter(Float wheelDiameter) {
        this.wheelDiameter = wheelDiameter;
    }

    public Float getFlangeThickness() {
        return flangeThickness;
    }

    public void setFlangeThickness(Float flangeThickness) {
        this.flangeThickness = flangeThickness;
    }

    public Float getFlangeHeight() {
        return flangeHeight;
    }

    public void setFlangeHeight(Float flangeHeight) {
        this.flangeHeight = flangeHeight;
    }

    public Float getqR() {
        return qR;
    }

    public void setqR(Float qR) {
        this.qR = qR;
    }

    public Float getDiameterRunout() {
        return diameterRunout;
    }

    public void setDiameterRunout(Float diameterRunout) {
        this.diameterRunout = diameterRunout;
    }

    public Float getInsideDistance() {
        return insideDistance;
    }

    public void setInsideDistance(Float insideDistance) {
        this.insideDistance = insideDistance;
    }
}
