package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.LogParam;

import java.util.List;

/**
 * @ClassName FlawDetectionVO
 * <AUTHOR>
 * @Date 2022/3/25 10:16
 * @Version 1.0
 */
public class FlawDetectionVO {
    @LogParam(description = "主键id")
    private String id;
    @LogParam(description = "原车号")
    private String original;
    @LogParam(description = "现装车号")
    private String present;
    @LogParam(description = "检验方式")
    private String type;
    @LogParam(description = "产品名称")
    private String product;
    @LogParam(description = "基础信息")
    private String basicInfo;
    @LogParam(description = "作业情况")
    private String situation;
    @LogParam(description = "自检")
    private String selfCheck;
    @LogParam(description = "自检日期")
    private String selfCheckDate;
    @LogParam(description = "互检")
    private String mutualCheck;
    @LogParam(description = "互检日期")
    private String mutualCheckDate;
    @LogParam(description = "专检")
    private String specialCheck;
    @LogParam(description = "专检日期")
    private String specialCheckDate;
    @LogParam(description = "质量负责人")
    private String director;
    @LogParam(description = "检查日期")
    private String checkDate;
    @LogParam(description = "检验结果")
    private String checkResult;
    @LogParam(description = "产品录入")
    private List<FlawDetectionProductVO> productInfo;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOriginal() {
        return original;
    }

    public void setOriginal(String original) {
        this.original = original;
    }

    public String getPresent() {
        return present;
    }

    public void setPresent(String present) {
        this.present = present;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getBasicInfo() {
        return basicInfo;
    }

    public void setBasicInfo(String basicInfo) {
        this.basicInfo = basicInfo;
    }

    public String getSituation() {
        return situation;
    }

    public void setSituation(String situation) {
        this.situation = situation;
    }

    public String getSelfCheck() {
        return selfCheck;
    }

    public void setSelfCheck(String selfCheck) {
        this.selfCheck = selfCheck;
    }

    public String getSelfCheckDate() {
        return selfCheckDate;
    }

    public void setSelfCheckDate(String selfCheckDate) {
        this.selfCheckDate = selfCheckDate;
    }

    public String getMutualCheck() {
        return mutualCheck;
    }

    public void setMutualCheck(String mutualCheck) {
        this.mutualCheck = mutualCheck;
    }

    public String getMutualCheckDate() {
        return mutualCheckDate;
    }

    public void setMutualCheckDate(String mutualCheckDate) {
        this.mutualCheckDate = mutualCheckDate;
    }

    public String getSpecialCheck() {
        return specialCheck;
    }

    public void setSpecialCheck(String specialCheck) {
        this.specialCheck = specialCheck;
    }

    public String getSpecialCheckDate() {
        return specialCheckDate;
    }

    public void setSpecialCheckDate(String specialCheckDate) {
        this.specialCheckDate = specialCheckDate;
    }

    public String getDirector() {
        return director;
    }

    public void setDirector(String director) {
        this.director = director;
    }

    public String getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(String checkDate) {
        this.checkDate = checkDate;
    }

    public String getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(String checkResult) {
        this.checkResult = checkResult;
    }

    public List<FlawDetectionProductVO> getProductInfo() {
        return productInfo;
    }

    public void setProductInfo(List<FlawDetectionProductVO> productInfo) {
        this.productInfo = productInfo;
    }
}
