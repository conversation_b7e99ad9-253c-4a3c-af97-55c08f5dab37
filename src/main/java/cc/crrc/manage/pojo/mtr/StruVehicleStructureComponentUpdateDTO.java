package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.group.Update;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

public class StruVehicleStructureComponentUpdateDTO {

    // 构型id
    @NotNull(message = "id不能为空" ,groups= {Update.class})
    private String id;
    // 结构中文名
    @NotBlank(message = "构型中文名称不能为空" ,groups= {Update.class})
    private String nameCn;
    // 结构英文名
    private String nameEn;
    // 结构英文名缩写
    @NotBlank(message = "构型英文缩写不能为空" ,groups= {Update.class})
    private String shortNameEn;
    // 顺序编号
    private Long sortNumber;
    @JsonIgnore
    private String modifyBy;
    @JsonIgnore
    private Date modifyTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getShortNameEn() {
        return shortNameEn;
    }

    public void setShortNameEn(String shortNameEn) {
        this.shortNameEn = shortNameEn;
    }

    public Long getSortNumber() {
        return sortNumber;
    }

    public void setSortNumber(Long sortNumber) {
        this.sortNumber = sortNumber;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }
}
