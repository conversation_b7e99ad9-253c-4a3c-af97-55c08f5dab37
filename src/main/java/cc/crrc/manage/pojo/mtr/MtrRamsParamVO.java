package cc.crrc.manage.pojo.mtr;

import java.util.Date;

import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * 2020/2/19
 **/
public class MtrRamsParamVO {
	   
	    @LogParam(description="id")
	    @ApiModelProperty(value="id",dataType = "String")
	    private String id;
	  
	    @ApiModelProperty(value="制造商名称")
	    @LogParam(description="制造商名称")
	    private String vehicleTypeId;
	    
	    @ApiModelProperty(value="外键")
	    @LogParam(description="外键")
	    private String componentTypeId;
	   
	    @ApiModelProperty(value="项目名称")
	    @LogParam(description="项目名称")
	    private String itemName;
	    
	    @ApiModelProperty(value="项目值")
	    @LogParam(description="项目值")
	    private String itemValue;
	    
	    @ApiModelProperty(value="项目单位")
	    @LogParam(description="项目单位")
	    private String unit;
	    
	    @ApiModelProperty(value="创建用户编号")
	    @LogParam(description="创建用户编号")
	    private String createBy;
	    
	    
	    @ApiModelProperty(value="创建时间")
	    @LogParam(description="创建时间")
	    private Date createTime;
	    
	    @ApiModelProperty(value="修改用户编码")
	    @LogParam(description="修改用户编码")
	    private String modifyBy;
	    
	    @ApiModelProperty(value="修改时间")
	    @LogParam(description="修改时间")
	    private Date modifyTime;

	    @ApiModelProperty(value="部件名称")
	    @LogParam(description="部件名称")
	    private String nameCn;
	    
		public String getId() {
			return id;
		}

		public void setId(String id) {
			this.id = id;
		}

		public String getVehicleTypeId() {
			return vehicleTypeId;
		}

		public void setVehicleTypeId(String vehicleTypeId) {
			this.vehicleTypeId = vehicleTypeId;
		}

		public String getComponentTypeId() {
			return componentTypeId;
		}

		public void setComponentTypeId(String componentTypeId) {
			this.componentTypeId = componentTypeId;
		}

		public String getItemName() {
			return itemName;
		}

		public void setItemName(String itemName) {
			this.itemName = itemName;
		}

		public String getItemValue() {
			return itemValue;
		}

		public void setItemValue(String itemValue) {
			this.itemValue = itemValue;
		}

		public String getUnit() {
			return unit;
		}

		public void setUnit(String unit) {
			this.unit = unit;
		}

		public String getCreateBy() {
			return createBy;
		}

		public void setCreateBy(String createBy) {
			this.createBy = createBy;
		}

		public Date getCreateTime() {
			return createTime;
		}

		public void setCreateTime(Date createTime) {
			this.createTime = createTime;
		}

		public String getModifyBy() {
			return modifyBy;
		}

		public void setModifyBy(String modifyBy) {
			this.modifyBy = modifyBy;
		}

		public Date getModifyTime() {
			return modifyTime;
		}

		public void setModifyTime(Date modifyTime) {
			this.modifyTime = modifyTime;
		}

		public String getNameCn() {
			return nameCn;
		}

		public void setNameCn(String nameCn) {
			this.nameCn = nameCn;
		}
	    
	    
}
