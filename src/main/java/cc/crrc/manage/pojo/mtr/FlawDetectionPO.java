package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.Contained;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.pojo.PageVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @ClassName FlawDetectionPO
 * <AUTHOR>
 * @Date 2022/3/25 9:59
 * @Version 1.0
 */
public class FlawDetectionPO extends PageVO {
    @LogParam(description = "主键id")
    private String id;
    @LogParam(description = "原车号")
    private String original;
    @LogParam(description = "现装车号")
    private String present;
    @Contained(value = {"1", "2", "3"}, message = "探伤方式存在异常", groups = {Insert.class})
    @NotBlank(message = "探伤方式不能为空", groups = {Insert.class})
    @LogParam(description = "探伤方式")
    private String type;
    @LogParam(description = "基础信息")
    private String basicInfo;
    @LogParam(description = "作业情况")
    private String situation;
    @LogParam(description = "自检")
    private String selfCheck;
    @LogParam(description = "自检日期")
    private String selfCheckDate;
    @LogParam(description = "互检")
    private String mutualCheck;
    @LogParam(description = "互检日期")
    private String mutualCheckDate;
    @LogParam(description = "专检")
    private String specialCheck;
    @LogParam(description = "专检日期")
    private String specialCheckDate;
    @LogParam(description = "质量负责人")
    private String director;
    @LogParam(description = "质量检查日期")
    private String checkDate;
    @LogParam(description = "检查结果(0-不合格 1-合格)")
    private String checkResult;
    @LogParam(description = "产品录入")
    private List<FlawDetectionProductPO> productInfo;
    @LogParam(description = "创建者")
    private String createBy;
    @LogParam(description = "修改者")
    private String modifyBy;
    @LogParam(description = "产品名称")
    private String product;
    @LogParam(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonIgnore
    private String startTime;
    @LogParam(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonIgnore
    private String endTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getOriginal() {
        return original;
    }

    public void setOriginal(String original) {
        this.original = original;
    }

    public String getPresent() {
        return present;
    }

    public void setPresent(String present) {
        this.present = present;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getBasicInfo() {
        return basicInfo;
    }

    public void setBasicInfo(String basicInfo) {
        this.basicInfo = basicInfo;
    }

    public String getSituation() {
        return situation;
    }

    public void setSituation(String situation) {
        this.situation = situation;
    }

    public String getSelfCheck() {
        return selfCheck;
    }

    public void setSelfCheck(String selfCheck) {
        this.selfCheck = selfCheck;
    }

    public String getSelfCheckDate() {
        return selfCheckDate;
    }

    public void setSelfCheckDate(String selfCheckDate) {
        this.selfCheckDate = selfCheckDate;
    }

    public String getMutualCheck() {
        return mutualCheck;
    }

    public void setMutualCheck(String mutualCheck) {
        this.mutualCheck = mutualCheck;
    }

    public String getMutualCheckDate() {
        return mutualCheckDate;
    }

    public void setMutualCheckDate(String mutualCheckDate) {
        this.mutualCheckDate = mutualCheckDate;
    }

    public String getSpecialCheck() {
        return specialCheck;
    }

    public void setSpecialCheck(String specialCheck) {
        this.specialCheck = specialCheck;
    }

    public String getSpecialCheckDate() {
        return specialCheckDate;
    }

    public void setSpecialCheckDate(String specialCheckDate) {
        this.specialCheckDate = specialCheckDate;
    }

    public String getDirector() {
        return director;
    }

    public void setDirector(String director) {
        this.director = director;
    }

    public String getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(String checkDate) {
        this.checkDate = checkDate;
    }

    public String getCheckResult() {
        return checkResult;
    }

    public void setCheckResult(String checkResult) {
        this.checkResult = checkResult;
    }

    public List<FlawDetectionProductPO> getProductInfo() {
        return productInfo;
    }

    public void setProductInfo(List<FlawDetectionProductPO> productInfo) {
        this.productInfo = productInfo;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}