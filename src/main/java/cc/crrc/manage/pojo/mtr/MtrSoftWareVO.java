package cc.crrc.manage.pojo.mtr;

import java.util.Date;
import java.util.List;

public class MtrSoftWareVO {
    private String id;
    private String name;
    private String version;
    private String description;
    private String createBy;
    private Date createTime;
    private String modifyBy;
    private String modifyTime;
    private String vehicleStructureCode;
    private String vehicleCode;
    private Date effectiveTime;//生效日期
    private Date endTime;
    private String signalNameEn;
    private String operator;//操作者
    private String lineId;
    private String lineName;
    private String vehicleTypeId;
    private String vehicleTypeName;
    private List<MtrSoftWareResumeVO> children;//历史履历版本

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getVehicleStructureCode() {
        return vehicleStructureCode;
    }

    public void setVehicleStructureCode(String vehicleStructureCode) {
        this.vehicleStructureCode = vehicleStructureCode;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public Date getEffectiveTime() {
        return effectiveTime;
    }

    public void setEffectiveTime(Date effectiveTime) {
        this.effectiveTime = effectiveTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getSignalNameEn() {
        return signalNameEn;
    }

    public void setSignalNameEn(String signalNameEn) {
        this.signalNameEn = signalNameEn;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getVehicleTypeName() {
        return vehicleTypeName;
    }

    public void setVehicleTypeName(String vehicleTypeName) {
        this.vehicleTypeName = vehicleTypeName;
    }

    public List<MtrSoftWareResumeVO> getChildren() {
        return children;
    }

    public void setChildren(List<MtrSoftWareResumeVO> children) {
        this.children = children;
    }
}
