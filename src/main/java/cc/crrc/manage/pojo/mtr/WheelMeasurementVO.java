package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.LogParam;

/**
 * @ClassName WheelMeasurementVO
 * <AUTHOR>
 * @Date 2022/3/15 13:09
 * @Version 1.0
 */

public class WheelMeasurementVO {

    @LogParam(description = "主键id")
    private String id;
    @LogParam(description = "车辆编号")
    private String vehicleCode;
    @LogParam(description = "里程")
    private String mileage;
    @LogParam(description = "状态")
    private String status;
    @LogParam(description = "检查日期")
    private String checkDate;
    @LogParam(description = "记录人")
    private String recorder;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getMileage() {
        return mileage;
    }

    public void setMileage(String mileage) {
        this.mileage = mileage;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(String checkDate) {
        this.checkDate = checkDate;
    }

    public String getRecorder() {
        return recorder;
    }

    public void setRecorder(String recorder) {
        this.recorder = recorder;
    }
}
