package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.Email;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.Phone;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * 2019/11/09
 **/
public class ManufacturerPO {
    @NotNull(message = "制造商id不能为空" ,groups= {Update.class})
    @LogParam(description="制造商id")
    @ApiModelProperty(value="制造商id",dataType = "String")
    private String id;
    @NotBlank(message = "制造商名称不能为空" ,groups= {Update.class,Insert.class})
    @ApiModelProperty(value="制造商名称")
    @LogParam(description="制造商名称")
    private String name;
    @Email(groups= {Insert.class,Update.class})
    @ApiModelProperty(value="电子邮箱")
    @LogParam(description="电子邮箱")
    private String email;
    @Phone(groups= {Insert.class,Update.class})
    @ApiModelProperty(value="联系电话")
    @LogParam(description="联系电话")
    private String phone;
    @ApiModelProperty(value="网址")
    @LogParam(description="网址")
    private String website;
    @ApiModelProperty(value="简介")
    @LogParam(description="简介")
    private String profile;
    @ApiModelProperty(value="地址")
    @LogParam(description="地址")
    private String address;
    @JsonIgnore
    private String createBy;
    @JsonIgnore
    private Date createTime;
    @JsonIgnore
    private String modifyBy;
    @JsonIgnore
    private Date modifyTime;
    @JsonIgnore
    private String remark;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
