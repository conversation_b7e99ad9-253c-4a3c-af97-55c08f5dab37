package cc.crrc.manage.pojo.mtr;

import cc.crrc.manage.common.annotation.LogParam;
import io.swagger.annotations.ApiModelProperty;

public class MtrRamsParamDTO {
    @ApiModelProperty(value = "车型ID")
    @LogParam(description = "车型ID")
    private String  vehicleTypeId;
    @ApiModelProperty(value = "项点名称")
    @LogParam(description = "项点名称")
    private String  itemName;
    @ApiModelProperty(value = "项点数值")
    @LogParam(description = "项点数值")
    private String  itemValue;
    @ApiModelProperty(value = "项点单位")
    @LogParam(description = "项点单位")
    private String  itemUnit;

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getItemValue() {
        return itemValue;
    }

    public void setItemValue(String itemValue) {
        this.itemValue = itemValue;
    }

    public String getItemUnit() {
        return itemUnit;
    }

    public void setItemUnit(String itemUnit) {
        this.itemUnit = itemUnit;
    }
}
