package cc.crrc.manage.pojo.ekb;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

public class EkbBasePO {
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String createBy;//创建用户编号
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Date createTime;//创建时间
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String modifyBy;//修改用户编号
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Date modifyTime;//修改时间
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String remark;//备注

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
