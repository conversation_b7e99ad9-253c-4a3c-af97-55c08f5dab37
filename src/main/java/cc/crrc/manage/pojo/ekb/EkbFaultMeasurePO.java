package cc.crrc.manage.pojo.ekb;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * @FileName EkbFaultMeasurePO
 * <AUTHOR> yuxi
 * @Date 2019/11/9 15:47
 * @Version 1.0
 **/
@Duplicates({
        @Duplicate(table = "ekb_fault_measure", message = "故障措施编码已存在！", groups = {Insert.class}, condition = "measure_code = '${measureCode}'"),
        @Duplicate(table = "ekb_fault_measure", message = "故障措施编码已存在！", groups = {Update.class}, condition = "measure_code = '${measureCode}' AND id != '${id}'")
})
public class EkbFaultMeasurePO implements Serializable {
    private String id;
    @NotNull(message = "车型id不能为空")
    private String vehicleTypeId;
    @JsonIgnore
    private String measureKey;
    @LogParam(description = "故障措施编码")
    @NotBlank(message = "故障措施编码不能为空", groups = {Insert.class, Update.class})
    private String measureCode;
    @LogParam(description = "故障措施内容")
    @NotBlank(message = "故障措施内容不能为空", groups = {Insert.class, Update.class})
    private String content;
    @LogParam(description = "故障措施类别")
    @NotBlank(message = "故障措施类别不能为空", groups = {Insert.class, Update.class})
    private String category;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String createBy;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Date createTime;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String modifyBy;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Date modifyTime;
    @LogParam(description = "线路id")
    private String lineId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getMeasureKey() {
        return measureKey;
    }

    public void setMeasureKey(String measureKey) {
        this.measureKey = measureKey;
    }

    public String getMeasureCode() {
        return measureCode;
    }

    public void setMeasureCode(String measureCode) {
        this.measureCode = measureCode;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
}
