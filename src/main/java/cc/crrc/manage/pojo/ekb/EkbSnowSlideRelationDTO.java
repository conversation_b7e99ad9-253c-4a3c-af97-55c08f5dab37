package cc.crrc.manage.pojo.ekb;

import cc.crrc.manage.common.annotation.LogParam;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

public class EkbSnowSlideRelationDTO implements Serializable {

    private static final long serialVersionUID = 8665640108305088630L;
    @LogParam(description = "故障类型主键")
    @NotBlank(message = "故障类型主键 不能为空")
    private String faultTypeKey;
    @LogParam(description = "故障使能状态")
    @NotBlank(message = "故障使能状态 不能为空")
    private Boolean enable;
    @LogParam(description = "车辆型号")
    @NotNull(message = "车辆型号不能为空")
    private String vehicleTypeId;
    @LogParam(description = "雪崩关系网json数据")
    private String faultSnowSlides;
    @LogParam(description = "线路id")
    private String lineId;


    public String getFaultTypeKey() {
        return faultTypeKey;
    }

    public void setFaultTypeKey(String faultTypeKey) {
        this.faultTypeKey = faultTypeKey;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getFaultSnowSlides() {
        return faultSnowSlides;
    }

    public void setFaultSnowSlides(String faultSnowSlides) {
        this.faultSnowSlides = faultSnowSlides;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
}
