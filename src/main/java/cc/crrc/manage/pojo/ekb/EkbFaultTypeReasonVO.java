package cc.crrc.manage.pojo.ekb;

public class EkbFaultTypeReasonVO {
    private String faultReasonKey;
    private String faultTypeKey;
    private Long initCounter;
    private Long realCounter;

    public String getFaultReasonKey() {
        return faultReasonKey;
    }

    public void setFaultReasonKey(String faultReasonKey) {
        this.faultReasonKey = faultReasonKey;
    }

    public String getFaultTypeKey() {
        return faultTypeKey;
    }

    public void setFaultTypeKey(String faultTypeKey) {
        this.faultTypeKey = faultTypeKey;
    }

    public Long getInitCounter() {
        return initCounter;
    }

    public void setInitCounter(Long initCounter) {
        this.initCounter = initCounter;
    }

    public Long getRealCounter() {
        return realCounter;
    }

    public void setRealCounter(Long realCounter) {
        this.realCounter = realCounter;
    }
}
