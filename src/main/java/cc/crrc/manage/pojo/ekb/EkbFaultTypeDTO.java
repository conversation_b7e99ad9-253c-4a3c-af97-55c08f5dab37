package cc.crrc.manage.pojo.ekb;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.pojo.PageVO;
import cc.crrc.manage.pojo.SysFilePO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

//@Duplicates({
//        @Duplicate(table = "ekb_fault_type", message = "故障编码已存在", condition = "fault_code='${faultCode}' and del_flag" +
//                " = '0'", groups = {
//                Insert.class}),
//        @Duplicate(table = "ekb_fault_type", message = "故障编码已存在", condition = "fault_code='${faultCode}' and " +
//                "id!=${id} and del_flag = '0'", groups = {
//                Update.class})
//})//20210519 因为故障编码规范固定  同一个编码对应多个故障 所以暂时去掉故障编码校验
public class EkbFaultTypeDTO extends PageVO implements Serializable {
    private static final long serialVersionUID = 1L;
    @LogParam(description = "故障类型id")
    private String id;
    @LogParam(description = "车辆型号id")
    @NotNull(message = "车辆型号id不能为空", groups = {Insert.class, Update.class})
    private String vehicleTypeId;
    private String faultTypeKey;
    @LogParam(description = "车辆构型编码")
    private String vehicleStructureCode;
    @LogParam(description = "故障类型中文名")
    @NotBlank(message = "中文名 不能为空", groups = {Insert.class, Update.class})
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
    private String nameCn;
    @LogParam(description = "故障类型中英文合体")
    private String nameCnE;
    @LogParam(description = "故障类型英文名")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", groups = {Insert.class, Update.class}, message = "只能输入英文和数字")
    @NotBlank(message = "英文名 不能为空", groups = {Insert.class, Update.class})
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
    private String nameEn;
    @LogParam(description = "故障编码")
    @Pattern(regexp = "^(?!_)(?!.*?_$)[a-zA-Z0-9_]+$", groups = {Insert.class, Update.class}, message = "只能输入字母、数字和'_'且不能以'_'开头和结尾")
    @NotBlank(message = "故障编码 不能为空", groups = {Insert.class, Update.class})
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
    private String faultCode;
    @LogParam(description = "故障级别")
    @NotNull(message = "故障级别 不能为空", groups = {Insert.class, Update.class})
    private Integer faultLevel;
    @LogParam(description = "故障系统")
    private String subsystem;
    @JsonIgnore
    private Integer delFlag;
    @JsonIgnore
    private String createBy;
    @JsonIgnore
    private Date createTime;
    @JsonIgnore
    private String modifyBy;
    @JsonIgnore
    private Date modifyTime;
    @LogParam(description = "故障类型描述")
    private String description;
    @LogParam(description = "故障所属车厢")
    private String location;
    // 故障原因（模糊查询传参用）
    @LogParam(description = "使能状态")
    private Boolean enable;//2020年3月26日	16点03分	房明宽	新增 雪崩 使能状态(t/f)
    @LogParam(description = "故障模式")
    private String faultMode;
    @LogParam(description = "正线状态建议")
    private String frontlineDisposalRecommendations;
    @LogParam(description = "检修建议")
    private String overhaulSuggestions;
    @LogParam(description = "故障原因")
    private String faultReason;
    @LogParam(description = "模型编码")
    private String modelCode;
    @LogParam(description = "应急措施json")//2021-01-11
    private String measureJson;
    @LogParam(description = "构型id")
    private String componentId;
    // 故障措施（模糊查询传参用）
    private String faultMeasure;
    // 故障原因list
    private List<EkbFaultReasonDTO> faultReasonList;
    // 文件list
    private List<SysFilePO> sysFileList;
    //车辆型号名称
    @ApiModelProperty(hidden = true)
    private String vehicleType;
    //系统名称
    @ApiModelProperty(hidden = true)
    private String assCarSystem;
    @LogParam(description = "车辆构型名称")
    @ApiModelProperty(hidden = true)
    private String vehicleStructureName;
    @LogParam(description = "线路id")
    private String lineId;

    @LogParam(description = "故障种类：1自动，2手动")
    private Integer faultCategory;
    
	public Integer getFaultCategory() {
		return faultCategory;
	}

	public void setFaultCategory(Integer faultCategory) {
		this.faultCategory = faultCategory;
	}

	public String getNameCnE() {
        return nameCnE;
    }

    public void setNameCnE(String nameCnE) {
        this.nameCnE = nameCnE;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getFaultTypeKey() {
        return faultTypeKey;
    }

    public void setFaultTypeKey(String faultTypeKey) {
        this.faultTypeKey = faultTypeKey;
    }

    public String getVehicleStructureCode() {
        return vehicleStructureCode;
    }

    public void setVehicleStructureCode(String vehicleStructureCode) {
        this.vehicleStructureCode = vehicleStructureCode;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getFaultCode() {
        return faultCode;
    }

    public void setFaultCode(String faultCode) {
        this.faultCode = faultCode;
    }

    public Integer getFaultLevel() {
        return faultLevel;
    }

    public void setFaultLevel(Integer faultLevel) {
        this.faultLevel = faultLevel;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getFaultReason() {
        return faultReason;
    }

    public void setFaultReason(String faultReason) {
        this.faultReason = faultReason;
    }

    public String getFaultMeasure() {
        return faultMeasure;
    }

    public void setFaultMeasure(String faultMeasure) {
        this.faultMeasure = faultMeasure;
    }

    public List<EkbFaultReasonDTO> getFaultReasonList() {
        return faultReasonList;
    }

    public void setFaultReasonList(List<EkbFaultReasonDTO> faultReasonList) {
        this.faultReasonList = faultReasonList;
    }

    public List<SysFilePO> getSysFileList() {
        return sysFileList;
    }

    public void setSysFileList(List<SysFilePO> sysFileList) {
        this.sysFileList = sysFileList;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getAssCarSystem() {
        return assCarSystem;
    }

    public void setAssCarSystem(String assCarSystem) {
        this.assCarSystem = assCarSystem;
    }

    public String getVehicleStructureName() {
        return vehicleStructureName;
    }

    public void setVehicleStructureName(String vehicleStructureName) {
        this.vehicleStructureName = vehicleStructureName;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getFaultMode() {
        return faultMode;
    }

    public void setFaultMode(String faultMode) {
        this.faultMode = faultMode;
    }

    public String getFrontlineDisposalRecommendations() {
        return frontlineDisposalRecommendations;
    }

    public void setFrontlineDisposalRecommendations(String frontlineDisposalRecommendations) {
        this.frontlineDisposalRecommendations = frontlineDisposalRecommendations;
    }

    public String getOverhaulSuggestions() {
        return overhaulSuggestions;
    }

    public void setOverhaulSuggestions(String overhaulSuggestions) {
        this.overhaulSuggestions = overhaulSuggestions;
    }

    public String getModelCode() {
        return modelCode;
    }

    public void setModelCode(String modelCode) {
        this.modelCode = modelCode;
    }

    public String getMeasureJson() {
        return measureJson;
    }

    public void setMeasureJson(String measureJson) {
        this.measureJson = measureJson;
    }

    public String getComponentId() {
        return componentId;
    }

    public void setComponentId(String componentId) {
        this.componentId = componentId;
    }
}
