package cc.crrc.manage.pojo.ekb;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.pojo.PageVO;
import io.swagger.annotations.ApiModelProperty;

/**
 * @FileName EkbFaultMeasureVO
 * <AUTHOR> yuxi
 * @Date 2019/11/9 10:09
 * @Version 1.0
 **/
public class EkbFaultMeasureVO extends PageVO {
    @ApiModelProperty(hidden = true)
    @LogParam(description = "故障措施id")
    private String id;
    @LogParam(description = "车型主键id")
    private String vehicleTypeId;
    @LogParam(description = "故障措施业务主键")
    private String measureKey;
    @LogParam(description = "故障措施编码")
    private String measureCode;
    @LogParam(description = "故障措施内容")
    private String content;
    @LogParam(description = "故障措施类别")
    private String category;
    @LogParam(description = "初始计数")
    private Long initCounter;
    @LogParam(description = "实际计数")
    private Long realCounter;
    @ApiModelProperty(hidden = true)
    @LogParam(description = "车型名称")
    private String vehicleType;
    @LogParam(description = "线路id")
    private String lineId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getMeasureKey() {
        return measureKey;
    }

    public void setMeasureKey(String measureKey) {
        this.measureKey = measureKey;
    }

    public String getMeasureCode() {
        return measureCode;
    }

    public void setMeasureCode(String measureCode) {
        this.measureCode = measureCode;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Long getInitCounter() {
        return initCounter;
    }

    public void setInitCounter(Long initCounter) {
        this.initCounter = initCounter;
    }

    public Long getRealCounter() {
        return realCounter;
    }

    public void setRealCounter(Long realCounter) {
        this.realCounter = realCounter;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
}
