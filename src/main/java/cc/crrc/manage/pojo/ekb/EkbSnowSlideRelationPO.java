package cc.crrc.manage.pojo.ekb;

import cc.crrc.manage.common.annotation.LogParam;
import com.alibaba.fastjson.JSONObject;

import java.io.Serializable;
import java.util.List;

public class EkbSnowSlideRelationPO implements Serializable {

    private static final long serialVersionUID = 288980487368209173L;

    @LogParam(description = "车型名称")
    private String vehicleTypeName;
    @LogParam(description = "雪崩网数据")
    private JSONObject snowSlideRelation;
    @LogParam(description = "故障数")
    private Integer faultNum;
    @LogParam(description = "层级")
    private Integer  hierarchy;
    @LogParam(description = "相关故障中文名")
    private List<String> nameCns;
    @LogParam(description = "故障中文名拼接串")
    private String nameCnSplice;
    @LogParam(description = "相关故障故障代码")
    private String faultCodes;

    public String getVehicleTypeName() { return vehicleTypeName; }

    public void setVehicleTypeName(String vehicleTypeName) { this.vehicleTypeName = vehicleTypeName; }

    public JSONObject getSnowSlideRelation() { return snowSlideRelation; }

    public void setSnowSlideRelation(JSONObject snowSlideRelation) { this.snowSlideRelation = snowSlideRelation; }

    public Integer getFaultNum() { return faultNum; }

    public void setFaultNum(Integer faultNum) { this.faultNum = faultNum; }

    public Integer getHierarchy() { return hierarchy; }

    public void setHierarchy(Integer hierarchy) { this.hierarchy = hierarchy; }

    public List<String> getNameCns() { return nameCns; }

    public void setNameCns(List<String> nameCns) { this.nameCns = nameCns; }

    public String getFaultCodes() { return faultCodes; }

    public void setFaultCodes(String faultCodes) { this.faultCodes = faultCodes; }

    public String getNameCnSplice() { return nameCnSplice; }

    public void setNameCnSplice(String nameCnSplice) { this.nameCnSplice = nameCnSplice; }

}
