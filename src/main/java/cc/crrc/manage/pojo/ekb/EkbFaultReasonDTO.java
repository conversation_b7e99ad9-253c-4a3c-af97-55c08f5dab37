package cc.crrc.manage.pojo.ekb;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.pojo.PageVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Duplicates({
        @Duplicate(table = "ekb_fault_reason", message = "原因编码已存在", condition = "reason_code='${reasonCode}'", groups = {
                Insert.class}),
        @Duplicate(table = "ekb_fault_reason", message = "原因编码已存在", condition = "reason_code='${reasonCode}' and id!='${id}'", groups = {
                Update.class})
})
public class EkbFaultReasonDTO extends PageVO implements Serializable {
    @LogParam(description = "故障原因id")
    private String id;
    @NotNull(message = "车型id 不能为空", groups = {Insert.class, Update.class})
    @LogParam(description = "车型id")
    private String vehicleTypeId;
    @LogParam(description = "故障原因业务主键")
    private String reasonKey;
    @NotBlank(message = "故障原因编码 不能为空", groups = {Insert.class, Update.class})
    @LogParam(description = "故障原因编码")
    private String reasonCode;
    @LogParam(description = "故障原因内容")
    private String content;
    @LogParam(description = "故障原因类别")
    private String category;
    @JsonIgnore/*swagger上不显示*/
    private String createBy;
    @JsonIgnore/*swagger上不显示*/
    private Date createTime;
    @JsonIgnore/*swagger上不显示*/
    private String modifyBy;
    @JsonIgnore/*swagger上不显示*/
    private Date modifyTime;
    // 故障措施list
    private List<EkbFaultMeasureVO> faultMeasureList;
    //初始计数
    private Long initCounter;
    // 实际发生次数
    private Long realCounter;
    //车辆型号名称
    @ApiModelProperty(hidden = true)
    private String vehicleType;
    //2020/4/17 guowei fracas 添加故障名称筛选
    @LogParam(description = "故障名称")
    private String nameCn;
    @LogParam(description = "线路id")
    private String lineId;

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getReasonKey() {
        return reasonKey;
    }

    public void setReasonKey(String reasonKey) {
        this.reasonKey = reasonKey;
    }

    public String getReasonCode() {
        return reasonCode;
    }

    public void setReasonCode(String reasonCode) {
        this.reasonCode = reasonCode;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public List<EkbFaultMeasureVO> getFaultMeasureList() {
        return faultMeasureList;
    }

    public void setFaultMeasureList(List<EkbFaultMeasureVO> faultMeasureList) {
        this.faultMeasureList = faultMeasureList;
    }

    public Long getInitCounter() {
        return initCounter;
    }

    public void setInitCounter(Long initCounter) {
        this.initCounter = initCounter;
    }

    public Long getRealCounter() {
        return realCounter;
    }

    public void setRealCounter(Long realCounter) {
        this.realCounter = realCounter;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
}
