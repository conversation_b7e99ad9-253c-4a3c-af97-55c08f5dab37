package cc.crrc.manage.pojo.ekb;

import cc.crrc.manage.common.annotation.LogParam;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

public class EkbFaultSnowSlideDTO implements Serializable {

    private static final long serialVersionUID = -30704057472469094L;

    @LogParam(description = "车辆型号id")
    private String vehicleTypeId;
    @LogParam(description = "故障编码")
    private String faultCode;
    @LogParam(description = "故障类型中文名")
    private String nameCn;
    @LogParam(description = "当前页数")
    @NotNull(message = "当前页数不能为空")
    private Integer pageNum;
    @LogParam(description = "每页数据大小")
    @NotNull(message = "每页数据大小不能为空")
    private Integer pageSize;
    @LogParam(description = "线路id")
    private String lineId;

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getFaultCode() {
        return faultCode;
    }

    public void setFaultCode(String faultCode) {
        this.faultCode = faultCode;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
}
