package cc.crrc.manage.pojo.ekb;

import cc.crrc.manage.common.annotation.LogParam;

import javax.validation.constraints.NotNull;

public class EkbFaultSnowSlidePO extends EkbBasePO {
    private String id;//主键
    @NotNull(message = "车型id不能为空")
    private String vehicleTypeId;
    @LogParam(description = "父节点故障类型主键")
    private String parentFaultTypeKey;
    @LogParam(description = "当前节点故障类型主键")
    private String faultTypeKey;
    private Integer correlationTime;//相关时间，单位毫秒
    private Boolean enable;//使能状态
    private String nameCn;//当前故障中文名称
    private String parentNameCn;//父故障中文名称
    private String faultCode;////当前故障代码
    private String parentFaultCode;//父故障故障代码
    @LogParam(description = "车型名称")
    private String vehicleTypeName;
    private String lineId;


    public String getVehicleTypeName() {
        return vehicleTypeName;
    }

    public void setVehicleTypeName(String vehicleTypeName) {
        this.vehicleTypeName = vehicleTypeName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getParentFaultTypeKey() {
        return parentFaultTypeKey;
    }

    public void setParentFaultTypeKey(String parentFaultTypeKey) {
        this.parentFaultTypeKey = parentFaultTypeKey;
    }

    public String getFaultTypeKey() {
        return faultTypeKey;
    }

    public void setFaultTypeKey(String faultTypeKey) {
        this.faultTypeKey = faultTypeKey;
    }

    public Integer getCorrelationTime() {
        return correlationTime;
    }

    public void setCorrelationTime(Integer correlationTime) {
        this.correlationTime = correlationTime;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getParentNameCn() {
        return parentNameCn;
    }

    public void setParentNameCn(String parentNameCn) {
        this.parentNameCn = parentNameCn;
    }

    public String getFaultCode() {
        return faultCode;
    }

    public void setFaultCode(String faultCode) {
        this.faultCode = faultCode;
    }

    public String getParentFaultCode() {
        return parentFaultCode;
    }

    public void setParentFaultCode(String parentFaultCode) {
        this.parentFaultCode = parentFaultCode;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }
}
