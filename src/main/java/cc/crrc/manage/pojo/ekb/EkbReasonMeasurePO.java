package cc.crrc.manage.pojo.ekb;

public class EkbReasonMeasurePO {
    private String faultTypeKey;
    private String faultReasonKey;
    private Long initCounter;
    private Long realCounter;
    private String faultMeasureKey;

    public String getFaultTypeKey() {
        return faultTypeKey;
    }

    public void setFaultTypeKey(String faultTypeKey) {
        this.faultTypeKey = faultTypeKey;
    }

    public String getFaultReasonKey() {
        return faultReasonKey;
    }

    public void setFaultReasonKey(String faultReasonKey) {
        this.faultReasonKey = faultReasonKey;
    }

    public Long getInitCounter() {
        return initCounter;
    }

    public void setInitCounter(Long initCounter) {
        this.initCounter = initCounter;
    }

    public Long getRealCounter() {
        return realCounter;
    }

    public void setRealCounter(Long realCounter) {
        this.realCounter = realCounter;
    }

    public String getFaultMeasureKey() {
        return faultMeasureKey;
    }

    public void setFaultMeasureKey(String faultMeasureKey) {
        this.faultMeasureKey = faultMeasureKey;
    }
}
