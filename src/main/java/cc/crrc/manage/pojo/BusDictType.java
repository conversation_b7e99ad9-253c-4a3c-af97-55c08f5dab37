package cc.crrc.manage.pojo;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
@Duplicates({
        @Duplicate(table = "bus_dict_type", message = "code重复!", condition = "code='${code}' and del_flag='0'", groups = {
                Insert.class}),
        @Duplicate(table = "bus_dict_type", message = "code重复!", condition = "code='${code}' and id!='${id}' and del_flag='0'", groups = {
                Update.class})
})
public class BusDictType {

    private static final long serialVersionUID=1L;

    private String id;
    /** 字典描述 */
    @ApiModelProperty(value = "字典描述")
    private String description;
    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /** 创建人 */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /** 最后修改时间 */
    @ApiModelProperty(value = "最后修改时间")
    private Date lastModifyTime;
    /** 最后修改人 */
    @ApiModelProperty(value = "最后修改人")
    private String lastModifyBy;
    /** 删除标识 1-删除 0-未删除 */
    @ApiModelProperty(value = "删除标识 1-删除 0-未删除")
    private String delFlag;
    /** 字典类型编码 */
    @ApiModelProperty(value = "字典类型编码")
    private String code;
    /** 字典类型名称 */
    @ApiModelProperty(value = "字典类型名称")
    private String name;
    /** 字典范围 */
    @ApiModelProperty(value = "字典范围")
    private String typeLevel;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    public String getLastModifyBy() {
        return lastModifyBy;
    }

    public void setLastModifyBy(String lastModifyBy) {
        this.lastModifyBy = lastModifyBy;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTypeLevel() {
        return typeLevel;
    }

    public void setTypeLevel(String typeLevel) {
        this.typeLevel = typeLevel;
    }
}
