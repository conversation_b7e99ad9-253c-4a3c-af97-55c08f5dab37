package cc.crrc.manage.pojo;


import cc.crrc.manage.common.annotation.LogParam;

/**
 * 角色和菜单关联 sys_role_menu
 * 
 * <AUTHOR>
 */
public class SysRoleMenu
{
    /** 角色ID */
    @LogParam(description="角色id")
    private String roleId;
    
    /** 菜单ID */
    private String menuId;

    public String getRoleId()
    {
        return roleId;
    }

    public void setRoleId(String roleId)
    {
        this.roleId = roleId;
    }

    public String getMenuId()
    {
        return menuId;
    }

    public void setMenuId(String menuId)
    {
        this.menuId = menuId;
    }

   /* @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("roleId", getRoleId())
            .append("menuId", getMenuId())
            .toString();
    }*/
}
