package cc.crrc.manage.pojo.draw;


import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Duplicates({
        @Duplicate(table = "draw_line_config", message = "区段id已存在！", groups = {Insert.class},
                condition = "(segment_id = '${segmentId}' AND line_id = '${lineId}' AND type = '${type}') AND del_flag = '0'"),
        @Duplicate(table = "draw_line_config", message = "区段id已存在！", groups = {Update.class},
                condition = "(segment_id = '${segmentId}' AND line_id = '${lineId}' AND type = '${type}') AND id != '${id}' AND del_flag = '0'")
})
public class DrawLineConfigDTO {

    private String id;
    @NotNull(message = "区段id不能为空！", groups = {Insert.class,Update.class})
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
    @LogParam(description = "区段id")
    private String segmentId;
    @NotNull(message = "当前站不能为空！", groups = {Insert.class,Update.class})
    @LogParam(description = "当前站")
    private String currentSta;
    @LogParam(description = "当前站坐标")
    private String coordinate;
    @LogParam(description = "json")
    @Length(max = 255,min = 1, message = "Json内容过长，需小于或等于255个字符",groups= {Insert.class,Update.class})
    private String json;
    @LogParam(description = "path")
    @Length(max = 4096, min = 1, message = "区段Path内容过长，必须大于等于1或小于等于4096",groups= {Insert.class,Update.class})
    private String path;
    @LogParam(description = "上下行")
    private String direction;
    @LogParam(description = "线路id")
    private String lineId;
    @LogParam(description = "类型")
    private String type;
    @LogParam(description = "排序")
    private Integer sort;
    @JsonIgnore
    private String createBy;
    @JsonIgnore
    private Date createTime;
    @JsonIgnore
    private String modifyBy;
    @JsonIgnore
    private String modifyTime;
    @JsonIgnore
    private String delFlag;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSegmentId() {
        return segmentId;
    }

    public void setSegmentId(String segmentId) {
        this.segmentId = segmentId;
    }

    public String getCurrentSta() {
        return currentSta;
    }

    public void setCurrentSta(String currentSta) {
        this.currentSta = currentSta;
    }

    public String getCoordinate() {
        return coordinate;
    }

    public void setCoordinate(String coordinate) {
        this.coordinate = coordinate;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }
}
