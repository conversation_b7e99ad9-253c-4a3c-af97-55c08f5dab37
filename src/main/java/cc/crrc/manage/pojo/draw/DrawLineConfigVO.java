package cc.crrc.manage.pojo.draw;

import cc.crrc.manage.common.annotation.LogParam;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.validator.constraints.Length;

import java.util.Date;

public class DrawLineConfigVO {

    private String id;
    @LogParam(description = "区段id")
    private String segmentId;
    @LogParam(description = "当前站")
    private String currentSta;
    @LogParam(description = "当前站坐标")
    @Length(max = 255,message = "当前站坐标内容过长，需小于或等于255个字符")
    private String coordinate;
    @LogParam(description = "json")
    @Length(max = 255,message = "Json内容过长，需小于或等于255个字符")
    private String json;
    @LogParam(description = "path")
    @Length(max = 4096,message = "区段Path内容过长，需小于或等于4096个字符")
    private String path;
    @LogParam(description = "上下行")
    private String direction;
    @LogParam(description = "线路id")
    private String lineId;
    @LogParam(description = "类型")
    private String type;
    @LogParam(description = "排序")
    private Integer sort;
    @JsonIgnore
    private String createBy;
    @JsonIgnore
    private Date createTime;
    @JsonIgnore
    private String modifyBy;
    @JsonIgnore
    private String modifyTime;
    @JsonIgnore
    private String delFlag;




    /*===========关联字段=============*/
    private String lineName;
    private String typeName;
    private String currentStaName;





    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSegmentId() {
        return segmentId;
    }

    public void setSegmentId(String segmentId) {
        this.segmentId = segmentId;
    }

    public String getCurrentSta() {
        return currentSta;
    }

    public void setCurrentSta(String currentSta) {
        this.currentSta = currentSta;
    }

    public String getCoordinate() {
        return coordinate;
    }

    public void setCoordinate(String coordinate) {
        this.coordinate = coordinate;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getCurrentStaName() {
        return currentStaName;
    }

    public void setCurrentStaName(String currentStaName) {
        this.currentStaName = currentStaName;
    }
}
