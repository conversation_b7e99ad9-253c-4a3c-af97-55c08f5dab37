package cc.crrc.manage.pojo;

import cc.crrc.manage.common.annotation.LogParam;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

public class SysLogDTO extends PageVO {
    @LogParam(description="日志编号")
    private String id;
    @LogParam(description="操作菜单")
    private String menu;
    @LogParam(description="用户操作")
    private String opt;
    @LogParam(description="访问地址")
    private String uri;
    @LogParam(description="用户id")
    private String userId;
    @LogParam(description="日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String date;
    @LogParam(description="真实姓名")
    private String name;
    @LogParam(description="IP地址")
    private String host;
    @LogParam(description="IP地址")
    private String ip;
    @LogParam(description="操作结果")
    private String status;
    @LogParam(description="用户名")
    private String userName;
    @LogParam(description="参数")
    private String params;
    @LogParam(description="浏览器参数")
    private String userAgent;
    @LogParam(description="异常信息")
    private String exception;
    @LogParam(description="操作类型")
    private String optType;
    @LogParam(description="响应时间")
    private Long responseTime;
    @LogParam(description="菜单path")
    private String menuPath;

    @LogParam(description="请求时间")
    private Date requestTime;

    @LogParam(description="开始时间")
    private String startTime;
    @LogParam(description="结束时间")
    private String endTime;
    @LogParam(description="浏览器")
    private String browser;
    @LogParam(description="操作系统")
    private String operatingSystem;
    @LogParam(description="ignore")
    private int count;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMenu() {
        return menu;
    }

    public void setMenu(String menu) {
        this.menu = menu;
    }

    public String getOpt() {
        return opt;
    }

    public void setOpt(String opt) {
        this.opt = opt;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getException() {
        return exception;
    }

    public void setException(String exception) {
        this.exception = exception;
    }

    public String getOptType() {
        return optType;
    }

    public void setOptType(String optType) {
        this.optType = optType;
    }

    public Long getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(Long responseTime) {
        this.responseTime = responseTime;
    }

    public Date getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(Date requestTime) {
        this.requestTime = requestTime;
    }

    public String getMenuPath() {
        return menuPath;
    }

    public void setMenuPath(String menuPath) {
        this.menuPath = menuPath;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getBrowser() {
        return browser;
    }

    public void setBrowser(String browser) {
        this.browser = browser;
    }

    public String getOperatingSystem() {
        return operatingSystem;
    }

    public void setOperatingSystem(String operatingSystem) {
        this.operatingSystem = operatingSystem;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }
}
