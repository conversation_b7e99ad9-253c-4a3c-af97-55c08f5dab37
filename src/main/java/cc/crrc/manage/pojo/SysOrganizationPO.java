package cc.crrc.manage.pojo;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

import java.io.Serializable;

/**
 * @FileName SysOrganizationPO
 * <AUTHOR> yuxi
 * @Date 2019/10/12 13:06
 * @Version 1.0
 **/
public class SysOrganizationPO implements Serializable {
    private static final Long serialVersionUID = 1L;

    private String id;
    @LogParam(description = "父节点id")
    @NotNull(message = "父id不能为空")
    private String parentId;
    @LogParam(description = "节点类型")
    private String nodeType;
    @LogParam(description = "组织名称")
    @NotBlank(message = "组织名称不能为空")
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25")
    private String name;
    @LogParam(description = "英文名称")
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25")
    private String nameEn;
    @LogParam(description = "组织编码")
    @NotBlank(message = "组织编码不能为空")
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25")
    private String code;
    @LogParam(description = "组织类型")
    private String type;
    @LogParam(description = "组织归属地id")
    private String locationId;
    @LogParam(description = "备注")
    private String remark;
    @LogParam(description = "复选框标识")
    private String selectBox;
    
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private Date modifyTime;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String modifyBy;
	@JsonIgnore
    @ApiModelProperty(hidden = true)
    private Date createTime;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String createBy;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String delFlag;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getLocationId() {
        return locationId;
    }

    public void setLocationId(String locationId) {
        this.locationId = locationId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }
    public String getSelectBox() {
  		return selectBox;
  	}
  	public void setSelectBox(String selectBox) {
  		this.selectBox = selectBox;
  	}
}
