package cc.crrc.manage.pojo;

import cc.crrc.manage.common.annotation.LogParam;

import java.util.Date;

/**
 * @ClassName SysAreaVO
 * @Description TODO
 * @<PERSON> <PERSON>
 * @Date 2020/8/3 20:08
 * @Version 1.0
 **/
public class SysAreaVO {
    @LogParam(description = "地区Id")
    private String areaId;
    @LogParam(description = "地区编码")
    private String areaCode;
    @LogParam(description = "地区名")
    private String areaName;
    @LogParam(description = "地区级别（1:省份province,2:市city,3:区县district,4:街道street）")
    private Integer level;
    @LogParam(description = "城市编码")
    private String cityCode;
    @LogParam(description = "城市中心点（即：经纬度坐标）")
    private String center;
    @LogParam(description = "地区父节点")
    private String parentId;

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCenter() {
        return center;
    }

    public void setCenter(String center) {
        this.center = center;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }
}
