package cc.crrc.manage.pojo.component;

import java.util.Date;

public class ComponentDetailVO extends ComponentDTO {

    private String vehicleId;
    private String nameCn;
    private String softwareName;
    private String softwareVersion;
    private Date softwareUpdateTime;
    private String fileId;
    private String pictureURL;
    private String componentTypeNameEn;
    private String productNumber;
    private String catalog;

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getSoftwareName() {
        return softwareName;
    }

    public void setSoftwareName(String softwareName) {
        this.softwareName = softwareName;
    }

    public String getSoftwareVersion() {
        return softwareVersion;
    }

    public void setSoftwareVersion(String softwareVersion) {
        this.softwareVersion = softwareVersion;
    }

    public Date getSoftwareUpdateTime() {
        return softwareUpdateTime;
    }

    public void setSoftwareUpdateTime(Date softwareUpdateTime) {
        this.softwareUpdateTime = softwareUpdateTime;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getPictureURL() {
        return pictureURL;
    }

    public void setPictureURL(String pictureURL) {
        this.pictureURL = pictureURL;
    }

    public String getComponentTypeNameEn() {
        return componentTypeNameEn;
    }

    public void setComponentTypeNameEn(String componentTypeNameEn) {
        this.componentTypeNameEn = componentTypeNameEn;
    }

    public String getProductNumber() {
        return productNumber;
    }

    public void setProductNumber(String productNumber) {
        this.productNumber = productNumber;
    }

    public String getCatalog() {
        return catalog;
    }

    public void setCatalog(String catalog) {
        this.catalog = catalog;
    }
}
