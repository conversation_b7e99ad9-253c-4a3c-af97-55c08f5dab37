package cc.crrc.manage.pojo.component;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.pojo.BasePO;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Duplicates({
        @Duplicate(table = "mtr_rams_param", message = "RAMS参数已存在", condition = "item_name ='${itemName}' and component_type_id = '${comTypeId}' and vehicle_type_id = '${vehicleTypeId}'",
                groups = {Insert.class}),
        @Duplicate(table = "mtr_rams_param", message = "RAMS参数已存在", condition = "item_name ='${itemName}' and component_type_id = '${comTypeId}' and vehicle_type_id = '${vehicleTypeId}' and id != '${id}'",
                groups = {Update.class})
})
public class RamsParameterDTO extends BasePO {
    @NotNull(message = "RAMS参数ID不能为空", groups = {Update.class})
    @LogParam(description = "RAMS参数ID")
    @ApiModelProperty(value = "RAMS参数ID", dataType = "String")
    private String id;
    @LogParam(description = "部件类型ID")
    @ApiModelProperty(value = "部件类型ID", dataType = "String")
    private String comTypeId;
    @LogParam(description = "车型ID")
    @ApiModelProperty(value = "车型ID", dataType = "String")
    private String vehicleTypeId;
    @NotBlank(message = "RAMS参数名称不能为空", groups = {Update.class, Insert.class})
    @LogParam(description = "RAMS参数名称")
    @ApiModelProperty(value = "RAMS参数名称", dataType = "String")
    private String itemName;
    @NotBlank(message = "RAMS参数值不能为空", groups = {Update.class, Insert.class})
    @LogParam(description = "RAMS参数值")
    @ApiModelProperty(value = "RAMS参数值", dataType = "String")
    private String itemValue;
    @NotBlank(message = "RAMS参数单位不能为空", groups = {Update.class, Insert.class})
    @LogParam(description = "RAMS参数单位")
    @ApiModelProperty(value = "RAMS参数单位", dataType = "String")
    private String unit;
    @LogParam(description = "RAMS参数类别")
    @ApiModelProperty(value = "RAMS参数类别", dataType = "String")
    private String itemCategory;

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getComTypeId() {
        return comTypeId;
    }

    public void setComTypeId(String comTypeId) {
        this.comTypeId = comTypeId;
    }

    public String getItemName() {
        return itemName;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getItemValue() {
        return itemValue;
    }

    public void setItemValue(String itemValue) {
        this.itemValue = itemValue;
    }

    public String getItemCategory() {
        return itemCategory;
    }

    public void setItemCategory(String itemCategory) {
        this.itemCategory = itemCategory;
    }
}
