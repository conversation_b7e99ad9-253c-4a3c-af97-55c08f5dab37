package cc.crrc.manage.pojo.component;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.pojo.BasePO;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Duplicates({
        @Duplicate(table = "mtr_operating_param", message = "运行参数已存在", condition = "item_name_cn ='${itemNameCn}' and component_id = '${componentId}' and vehicle_id = '${vehicleId}'",
                groups = {Insert.class}),
        @Duplicate(table = "mtr_operating_param", message = "运行参数已存在", condition = "item_name_cn ='${itemNameCn}' and component_id = '${componentId}' and vehicle_id = '${vehicleId}' and id != '${id}'",
                groups = {Update.class})
})
public class OperatingParameterDTO extends BasePO {
    @NotNull(message = "运行参数ID不能为空" ,groups= {Update.class})
    @LogParam(description="运行参数ID")
    @ApiModelProperty(value = "运行参数ID", dataType = "String")
    private String id;
    @LogParam(description="部件ID")
    @ApiModelProperty(value = "部件ID", dataType = "String")
    private String componentId;
    @LogParam(description="车辆ID")
    @ApiModelProperty(value = "车辆ID", dataType = "String")
    private String vehicleId;
    @NotBlank(message = "运行参数中文名称不能为空" ,groups= {Update.class, Insert.class})
    @LogParam(description="运行参数中文名称")
    @ApiModelProperty(value = "运行参数中文名称", dataType = "String")
    private String itemNameCn;
    @LogParam(description="运行参数英文名称")
    @ApiModelProperty(value = "运行参数英文名称", dataType = "String")
    private String itemNameEn;
    @NotBlank(message = "运行参数值不能为空" ,groups= {Update.class,Insert.class})
    @LogParam(description="运行参数值")
    @ApiModelProperty(value = "运行参数值", dataType = "String")
    private String itemValue;
    @LogParam(description="运行参数单位")
    @ApiModelProperty(value = "运行参数单位", dataType = "String")
    private String unit;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getComponentId() {
        return componentId;
    }

    public void setComponentId(String componentId) {
        this.componentId = componentId;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getItemNameCn() {
        return itemNameCn;
    }

    public void setItemNameCn(String itemNameCn) {
        this.itemNameCn = itemNameCn;
    }

    public String getItemNameEn() {
        return itemNameEn;
    }

    public void setItemNameEn(String itemNameEn) {
        this.itemNameEn = itemNameEn;
    }

    public String getItemValue() {
        return itemValue;
    }

    public void setItemValue(String itemValue) {
        this.itemValue = itemValue;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }
}
