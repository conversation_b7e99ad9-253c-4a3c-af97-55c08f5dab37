package cc.crrc.manage.pojo.component;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.pojo.PageVO;

@Duplicates({
    @Duplicate(table = "stru_component", message = "部件序列号已存在", condition = "serial_number='${serialNumber}'", groups = {
            Insert.class}),
    @Duplicate(table = "stru_component", message = "部件序列号已存在", condition = "serial_number='${serialNumber}' and id!='${id}' and del_flag = '0'", groups = {
            Update.class})
})

public class ComponentDTO extends PageVO {
	private static final long serialVersionUID = 1L;
	
	@NotNull(message = "部件ID不能为空" ,groups= {Update.class})
	private String id;
	@NotNull(message = "部件类型ID不能为空" ,groups= {Update.class,Insert.class})
	@LogParam(description="部件类型ID")
	private String componentTypeId;//
	@LogParam(description="部件类型名称")
	private String componentTypeName;
	@NotBlank(message = "部件序列号不能为空" ,groups= {Update.class,Insert.class})
	@LogParam(description="部件序列号")
	private String serialNumber;//
	@NotBlank(message = "部件出厂日期" ,groups= {Update.class,Insert.class})
	@LogParam(description="部件生产日期")
	private String productionDate;
	private Integer delFlag;
	private String remark;
    @LogParam(description = "创建人")
    private String createBy;
    @LogParam(description = "修改人")
    private String modifyBy;
    // 2020年8月14日 房明宽 增加 车型ID、车型名称、线路ID、线路名称
	private String vehicleTypeId;
	private String vehicleTypeName;
	private String lineId;
	private String lineName;

	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getComponentTypeId() {
		return componentTypeId;
	}
	public void setComponentTypeId(String componentTypeId) {
		this.componentTypeId = componentTypeId;
	}
	public String getSerialNumber() {
		return serialNumber;
	}
	public String getComponentTypeName() {
		return componentTypeName;
	}
	public void setComponentTypeName(String componentTypeName) {
		this.componentTypeName = componentTypeName;
	}
	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}
	public String getProductionDate() {
		return productionDate;
	}
	public void setProductionDate(String productionDate) {
		this.productionDate = productionDate;
	}
	public Integer getDelFlag() {
		return delFlag;
	}
	public void setDelFlag(Integer delFlag) {
		this.delFlag = delFlag;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getCreateBy() {
		return createBy;
	}
	public void setCreateBy(String createBy) {
		this.createBy = createBy;
	}
	public String getModifyBy() {
		return modifyBy;
	}
	public void setModifyBy(String modifyBy) {
		this.modifyBy = modifyBy;
	}

	public String getVehicleTypeName() {
		return vehicleTypeName;
	}

	public void setVehicleTypeName(String vehicleTypeName) {
		this.vehicleTypeName = vehicleTypeName;
	}

	public String getVehicleTypeId() {
		return vehicleTypeId;
	}

	public void setVehicleTypeId(String vehicleTypeId) {
		this.vehicleTypeId = vehicleTypeId;
	}

	public String getLineId() {
		return lineId;
	}

	public void setLineId(String lineId) {
		this.lineId = lineId;
	}

	public String getLineName() {
		return lineName;
	}

	public void setLineName(String lineName) {
		this.lineName = lineName;
	}
}
