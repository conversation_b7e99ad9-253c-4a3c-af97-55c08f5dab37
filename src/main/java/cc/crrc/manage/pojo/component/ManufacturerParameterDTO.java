package cc.crrc.manage.pojo.component;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.pojo.BasePO;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Duplicates({
        @Duplicate(table = "mtr_manufacturing_param", message = "制造参数已存在", condition = "item_name ='${itemName}' and component_id = '${componentId}' and vehicle_id = '${vehicleId}'",
                groups = {Insert.class}),
        @Duplicate(table = "mtr_manufacturing_param", message = "制造参数已存在", condition = "item_name ='${itemName}' and component_id = '${componentId}' and vehicle_id = '${vehicleId}' and id != '${id}'",
                groups = {Update.class})
})
public class ManufacturerParameterDTO extends BasePO {
    @NotNull(message = "制造参数ID不能为空", groups = {Update.class})
    @LogParam(description = "制造参数ID")
    @ApiModelProperty(value = "制造参数ID", dataType = "Long")
    private String id;
    @LogParam(description = "部件ID")
    @ApiModelProperty(value = "部件ID", dataType = "Long")
    private String componentId;
    @LogParam(description = "车辆ID")
    @ApiModelProperty(value = "车辆ID", dataType = "Long")
    private String vehicleId;
    @NotBlank(message = "制造参数名称不能为空", groups = {Update.class, Insert.class})
    @LogParam(description = "制造参数名称")
    @ApiModelProperty(value = "制造参数名称", dataType = "String")
    private String itemName;
    @NotBlank(message = "制造参数值不能为空", groups = {Update.class, Insert.class})
    @LogParam(description = "制造参数值")
    @ApiModelProperty(value = "制造参数值", dataType = "String")
    private String itemValue;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getComponentId() {
        return componentId;
    }

    public void setComponentId(String componentId) {
        this.componentId = componentId;
    }

    public String getItemName() {
        return itemName;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getItemValue() {
        return itemValue;
    }

    public void setItemValue(String itemValue) {
        this.itemValue = itemValue;
    }

}
