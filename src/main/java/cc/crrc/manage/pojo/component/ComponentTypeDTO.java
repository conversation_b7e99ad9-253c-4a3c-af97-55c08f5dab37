package cc.crrc.manage.pojo.component;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.pojo.PageVO;
import cc.crrc.manage.pojo.SysFilePO;
import org.hibernate.validator.constraints.Length;

@Duplicates({
    @Duplicate(table = "stru_component_type", message = "产品编号已存在", condition = "product_number='${productNumber}'", groups = {
            Insert.class}),
    @Duplicate(table = "stru_component_type", message = "产品编号已存在", condition = "product_number='${productNumber}' and id!='${id}' and del_flag = '0'", groups = {
            Update.class})
})
public class ComponentTypeDTO extends PageVO {
	private static final long serialVersionUID = 1L;
	
	@NotNull(message = "部件型号ID不能为空" ,groups= {Update.class})
	private String id;
	@NotBlank(message = "部件型号中文名不能为空" ,groups= {Update.class,Insert.class})
	@LogParam(description="部件型号中文名")
	@Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
	private String nameCn;
	@NotBlank(message = "部件型号英文名不能为空" ,groups= {Update.class,Insert.class})
	@LogParam(description="部件型号英文名")
	@Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
	private String nameEn;
	@NotBlank(message = "产品编号不能为空" ,groups= {Update.class,Insert.class})
	@LogParam(description="产品编号")
	@Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
	private String productNumber;
	@NotNull(message = "制造商不能为空" ,groups= {Update.class,Insert.class})
	@LogParam(description = "制造商ID")
	private String manufacturerId;
	private String manufacturerName;
	@NotBlank(message = "部件型号分类不能为空" ,groups= {Update.class,Insert.class})
	@LogParam(description="部件型号分类")
	@Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
	private String catalog;
	@LogParam(description="部件型号关联人员")
	private List<String> employeeId;
	private Integer delFlag;
	private String createBy;
	private String createTime;
	private String modifyBy;
	private String modifyTime;
	private String remark;
	// 2020年8月14日 房明宽新增 线路ID、车型ID
	@LogParam(description="线路ID")
	private Long lineId;
	@LogParam(description="车型ID")
	private String vehicleTypeId;
	
	private List<SysFilePO> sysFileList;
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getNameCn() {
		return nameCn;
	}
	public void setNameCn(String nameCn) {
		this.nameCn = nameCn;
	}
	public String getNameEn() {
		return nameEn;
	}
	public void setNameEn(String nameEn) {
		this.nameEn = nameEn;
	}
	public String getProductNumber() {
		return productNumber;
	}
	public void setProductNumber(String productNumber) {
		this.productNumber = productNumber;
	}
	public String getManufacturerId() {
		return manufacturerId;
	}
	public void setManufacturerId(String manufacturerId) {
		this.manufacturerId = manufacturerId;
	}
	public String getCatalog() {
		return catalog;
	}
	public String getManufacturerName() {
		return manufacturerName;
	}
	public void setManufacturerName(String manufacturerName) {
		this.manufacturerName = manufacturerName;
	}
	public void setCatalog(String catalog) {
		this.catalog = catalog;
	}
	public List<String> getEmployeeId() {
		return employeeId;
	}
	public void setEmployeeId(List<String> employeeId) {
		this.employeeId = employeeId;
	}
	public Integer getDelFlag() {
		return delFlag;
	}
	public void setDelFlag(Integer delFlag) {
		this.delFlag = delFlag;
	}
	public String getCreateBy() {
		return createBy;
	}
	public void setCreateBy(String createBy) {
		this.createBy = createBy;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public String getModifyBy() {
		return modifyBy;
	}
	public void setModifyBy(String modifyBy) {
		this.modifyBy = modifyBy;
	}
	public String getModifyTime() {
		return modifyTime;
	}
	public void setModifyTime(String modifyTime) {
		this.modifyTime = modifyTime;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Long getLineId() {
		return lineId;
	}

	public void setLineId(Long lineId) {
		this.lineId = lineId;
	}

	public String getVehicleTypeId() {
		return vehicleTypeId;
	}

	public void setVehicleTypeId(String vehicleTypeId) {
		this.vehicleTypeId = vehicleTypeId;
	}

	public List<SysFilePO> getSysFileList() {
		return sysFileList;
	}

	public void setSysFileList(List<SysFilePO> sysFileList) {
		this.sysFileList = sysFileList;
	}

}
