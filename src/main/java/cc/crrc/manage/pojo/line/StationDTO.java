package cc.crrc.manage.pojo.line;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
@Duplicates({
        @Duplicate(table = "mtr_station", message = "站点id已存在", condition = "sta_id='${staId}' and metro_line_id='${metroLineId}' and del_flag=0", groups = {
                Insert.class}),
        @Duplicate(table = "mtr_station", message = "站点id已存在", condition = "sta_id='${staId}' and id!='${id}' and metro_line_id='${metroLineId}' and del_flag=0", groups = {
                Update.class})
})
public class StationDTO {
    private static final long serialVersionUID = 1L;
    @NotNull(message = "主键ID不能为空" ,groups= {Update.class})
    private String id;
    @NotNull(message = "站点编号不能为空" ,groups= {Update.class,Insert.class})
    @LogParam(description="线路编号")
    private Integer stationCode;
    @NotBlank(message = "站点名称不能为空" ,groups= {Update.class,Insert.class})
    @LogParam(description="站点名称")
    private String name;
    @NotNull(message = "顺序编号不能为空" ,groups= {Update.class})
    private Integer sortNumber;
    @NotNull(message = "上一站顺序编号不能为空" ,groups= {Insert.class})
    private Integer lastSortNumber;
    @NotNull(message = "距离不能为空" ,groups= {Update.class,Insert.class})
    @LogParam(description="始发站距离")
    private Integer departureDistance;
    @LogParam(description="启用状态")
    private Integer status;
    @LogParam(description="站点列表")
    private List<StationDTO> stations;
    @NotNull(message = "线路编号不能为空" ,groups= {Update.class,Insert.class})
    private String metroLineId;
    private String createBy;
    private String createTime;
    private String modifyBy;
    private String modifyTime;
    private String remark;
    private Integer delFlag;

    //21-08-11 lixin增加车站逻辑
    private String type;//车站类型
    private String direction;//上下行
    @NotNull(message = "站点id不能为空" ,groups= {Update.class,Insert.class})
    private String staId;//上下行车站id

    private String directionStatus;//是否区分上下行：1：区分，2：不区分


    public String getId() {
        return id;
    }
    public void setId(String id) {
        this.id = id;
    }
    public Integer getStationCode() {
        return stationCode;
    }
    public void setStationCode(Integer stationCode) {
        this.stationCode = stationCode;
    }
    public String getName() {
        if(name==null)
            return "";
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public Integer getSortNumber() {
        return sortNumber;
    }
    public void setSortNumber(Integer sortNumber) {
        this.sortNumber = sortNumber;
    }
    public Integer getDepartureDistance() {
        return departureDistance;
    }
    public Integer getLastSortNumber() {
        return lastSortNumber;
    }
    public void setLastSortNumber(Integer lastSortNumber) {
        this.lastSortNumber = lastSortNumber;
    }
    public void setDepartureDistance(Integer departureDistance) {
        this.departureDistance = departureDistance;
    }
    public Integer getStatus() {
        return status;
    }
    public void setStatus(Integer status) {
        this.status = status;
    }
    public String getMetroLineId() {
        return metroLineId;
    }
    public List<StationDTO> getStations() {
        return stations;
    }
    public void setStations(List<StationDTO> stations) {
        this.stations = stations;
    }
    public void setMetroLineId(String metroLineId) {
        this.metroLineId = metroLineId;
    }
    public String getCreateBy() {
        return createBy;
    }
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    public String getCreateTime() {
        return createTime;
    }
    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
    public String getModifyBy() {
        return modifyBy;
    }
    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }
    public String getModifyTime() {
        return modifyTime;
    }
    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }
    public String getRemark() {
        return remark;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public String getStaId() {
        return staId;
    }

    public void setStaId(String staId) {
        this.staId = staId;
    }

    public StationDTO(@NotBlank(message = "站点名称不能为空", groups = {Update.class, Insert.class}) String name) {
        this.name = name;
    }
    public StationDTO(){};

    public String getDirectionStatus() {
        return directionStatus;
    }

    public void setDirectionStatus(String directionStatus) {
        this.directionStatus = directionStatus;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }
}
