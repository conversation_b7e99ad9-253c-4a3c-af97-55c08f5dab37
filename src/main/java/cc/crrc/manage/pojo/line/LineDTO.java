package cc.crrc.manage.pojo.line;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Duplicates({
        @Duplicate(table = "mtr_line", message = "线路名称已存在！", condition = "name='${name}' and del_flag='0'", groups = {Insert.class}),
        @Duplicate(table = "mtr_line", message = "线路名称已存在！", condition = "name='${name}' and id!='${id}' and del_flag='0'", groups = {Update.class})
})
public class LineDTO {
    private static final long serialVersionUID = 1L;
    @NotNull(message = "线路ID不能为空", groups = {Update.class})
    private String id;
    @NotBlank(message = "线路名称不能为空", groups = {Insert.class, Update.class})
    @LogParam(description = "线路名称")
    @Length(max = 25, min = 1, message = "长度必须大于等于1或小于等于25",groups= {Insert.class,Update.class})
    private String name;
    @NotNull(message = "线路所在地ID不能为空", groups = {Insert.class, Update.class})
    @LogParam(description = "所在地id")
    private String locationId;
    @LogParam(description = "所在地")
    private String locationName;
    @NotNull(message = "线路里程不能为空", groups = {Insert.class, Update.class})
    @LogParam(description = "线路里程")
    private Integer mileage;
    //@NotNull(message = "线路精度不能为空", groups = {Insert.class, Update.class})
    @LogParam(description = "精度")
    private Float longitude;
    //@NotNull(message = "线路纬度不能为空", groups = {Insert.class, Update.class})
    @LogParam(description = "纬度")
    private Float latitude;
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private String createBy;
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private String createTime;
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private String modifyBy;
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private String modifyTime;
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    @LogParam(description = "备注")
    private String remark;
    @LogParam(description = "线路颜色")
    private String color;
    @LogParam(description = "部门ID（通过逗号分割）")
    private String organizationsId;
    @LogParam(description = "部门名称（通过逗号分割）")
    private String organizationsName;
    @LogParam(description = "判断是否分上下行：1：分上下行，2：不分")
    private String directionStatus;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLocationId() {
        return locationId;
    }

    public void setLocationId(String locationId) {
        this.locationId = locationId;
    }

    public String getLocationName() {
        return locationName;
    }

    public void setLocationName(String locationName) {
        this.locationName = locationName;
    }

    public Integer getMileage() {
        return mileage;
    }

    public void setMileage(Integer mileage) {
        this.mileage = mileage;
    }

    public Float getLongitude() {
        return longitude;
    }

    public void setLongitude(Float longitude) {
        this.longitude = longitude;
    }

    public Float getLatitude() {
        return latitude;
    }

    public void setLatitude(Float latitude) {
        this.latitude = latitude;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getOrganizationsId() {
        return organizationsId;
    }

    public void setOrganizationsId(String organizationsId) {
        this.organizationsId = organizationsId;
    }

    public String getOrganizationsName() {
        return organizationsName;
    }

    public void setOrganizationsName(String organizationsName) {
        this.organizationsName = organizationsName;
    }

    public String getDirectionStatus() {
        return directionStatus;
    }

    public void setDirectionStatus(String directionStatus) {
        this.directionStatus = directionStatus;
    }
}
