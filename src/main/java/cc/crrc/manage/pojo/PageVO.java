package cc.crrc.manage.pojo;


import io.swagger.annotations.ApiModelProperty;

/**
 * @ClassName: PageVO
 * @Description: 分页展示层
 */
public class PageVO {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "起始页数")
    private int pageNumber;
    @ApiModelProperty(value = "分页条数")
    private int pageSize;
    @ApiModelProperty(value = "排序方式")
    private String orderBy = "";

    /**
     * @return pageNumber
     */

    public int getPageNumber() {
        return pageNumber;
    }

    /**
     * @param pageNumber{bare_field_name} to set
     */

    public void setPageNumber(int pageNumber) {
        this.pageNumber = pageNumber;
    }

    /**
     * @return pageSize
     */

    public int getPageSize() {
        return pageSize;
    }

    /**
     * @param pageSize{bare_field_name} to set
     */

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }


    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }
}
