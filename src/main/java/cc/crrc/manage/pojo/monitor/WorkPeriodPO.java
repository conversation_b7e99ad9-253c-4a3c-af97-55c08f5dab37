package cc.crrc.manage.pojo.monitor;


import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description 工作周期PO类
 * @Date 2021/9/17
 **/
public class WorkPeriodPO implements Serializable {

    private static final long serialVersionUID = -1868260799377612271L;

    @LogParam(description = "主键")
    private String id;
    @LogParam(description = "唯一id")
    private String uniqueId;
    @LogParam(description = "线路id")
    private String lineId;
    @LogParam(description = "车型id")
    private String vehicleTypeId;
    @LogParam(description = "车辆编码")
    private String vehicleCode;
    @LogParam(description = "位置")
    private String location;
    @LogParam(description = "所属部件类型")
    private String componentType;
    @LogParam(description = "中文名")
    @Length(max = 25, min = 1, message = "部件名称长度必须大于等于1且小于等于25",groups= {Insert.class, Update.class})
    private String nameCn;
    @LogParam(description = "周期")
    private String period;
    @LogParam(description = "洗车模式")
    private String mode;
    @LogParam(description = "操作者")
    private String operator;
    @LogParam(description = "操作时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date operateTime;
    @LogParam(description = "预警时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date warningTime;
    @LogParam(description = "变更时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date changeTime;
    @LogParam(description = "工作类型")
    private String workType;
    @LogParam(description = "备注")
    private String remark;
    @LogParam(description = "创建人")
    private String createBy;
    @LogParam(description = "创建时间")
    private Date createTime;
    @LogParam(description = "修改人")
    private String modifyBy;
    @LogParam(description = "修改时间")
    private Date modifyTime;
    @LogParam(description = "车厢")
    private String carriage;

    //2022-03-08 lixin添加 换油周期增加里程预警业务
    @LogParam(description = "初始里程")
    private Long initialMileage;
    @LogParam(description = "使用里程")
    private Long runningMileage;
    @LogParam(description = "预警里程")
    private Long warningMileage;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getComponentType() {
        return componentType;
    }

    public void setComponentType(String componentType) {
        this.componentType = componentType;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
    }

    public Date getWarningTime() {
        return warningTime;
    }

    public void setWarningTime(Date warningTime) {
        this.warningTime = warningTime;
    }

    public Date getChangeTime() {
        return changeTime;
    }

    public void setChangeTime(Date changeTime) {
        this.changeTime = changeTime;
    }

    public String getWorkType() {
        return workType;
    }

    public void setWorkType(String workType) {
        this.workType = workType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getCarriage() {
        return carriage;
    }

    public void setCarriage(String carriage) {
        this.carriage = carriage;
    }

    public Long getInitialMileage() {
        return initialMileage;
    }

    public void setInitialMileage(Long initialMileage) {
        this.initialMileage = initialMileage;
    }

    public Long getRunningMileage() {
        return runningMileage;
    }

    public void setRunningMileage(Long runningMileage) {
        this.runningMileage = runningMileage;
    }

    public Long getWarningMileage() {
        return warningMileage;
    }

    public void setWarningMileage(Long warningMileage) {
        this.warningMileage = warningMileage;
    }
}
