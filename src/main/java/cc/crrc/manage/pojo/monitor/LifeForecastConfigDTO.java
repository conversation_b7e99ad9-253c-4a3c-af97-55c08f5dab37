package cc.crrc.manage.pojo.monitor;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.pojo.PageVO;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

public class LifeForecastConfigDTO extends PageVO implements Serializable {

    private static final long serialVersionUID = -1566977819035040120L;
    @LogParam(description = "车型id")
    private String vehicleTypeId;
    @LogParam(description = "部件中文名称")
    private String nameCn;
    @LogParam(description = "部件类型,继电器relay/接触器contactor")
    private String type;
    @LogParam(description = "构型编码")
    private String structureCode;
    @LogParam(description = "大数据处理计算类型(time、count、distance)")
    private String calculationType;
    @LogParam(description = "线路id")
    private String lineId;
    @NotNull(message = "部件种类 0：寿命预测 1：普通部件", groups = {Insert.class, Update.class})
    @LogParam(description = "部件种类 0：寿命预测 1：普通部件")
    private Integer componentKind;
    @LogParam(description = "部件英文名称")
    @Length(max = 25, min = 1, message = "部件名称长度必须大于等于1且小于等于25",groups= {Insert.class, Update.class})
    private String nameEn;
    @LogParam(description = "部件编码")
    @Length(max = 25, min = 1, message = "部件名称长度必须大于等于1且小于等于25",groups= {Insert.class, Update.class})
    private String productNumber;
    @LogParam(description = "部件生产商id")
    private String manufacturerId;
    @LogParam(description = "部件生产商名称")
    private String manufacturerName;


    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStructureCode() {
        return structureCode;
    }

    public void setStructureCode(String structureCode) {
        this.structureCode = structureCode;
    }

    public String getCalculationType() {
        return calculationType;
    }

    public void setCalculationType(String calculationType) {
        this.calculationType = calculationType;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }


    public Integer getComponentKind() {
        return componentKind;
    }

    public void setComponentKind(Integer componentKind) {
        this.componentKind = componentKind;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getProductNumber() {
        return productNumber;
    }

    public void setProductNumber(String productNumber) {
        this.productNumber = productNumber;
    }

    public String getManufacturerId() {
        return manufacturerId;
    }

    public void setManufacturerId(String manufacturerId) {
        this.manufacturerId = manufacturerId;
    }

    public String getManufacturerName() {
        return manufacturerName;
    }

    public void setManufacturerName(String manufacturerName) {
        this.manufacturerName = manufacturerName;
    }
}
