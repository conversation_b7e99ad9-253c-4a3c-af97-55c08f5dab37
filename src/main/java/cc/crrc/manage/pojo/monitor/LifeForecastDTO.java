package cc.crrc.manage.pojo.monitor;

import cc.crrc.manage.pojo.PageVO;

import java.io.Serializable;

public class LifeForecastDTO extends PageVO implements Serializable {
    private static final long serialVersionUID = -9118299022707036199L;

    private RelayContactorPO relayContactorPO;
    private String usePercent;
    private Integer lifeWarning;
    private String vehicleCode;
    private String componentType;
    private String lineId;
    //todo 后更正为必传项
    private Integer componentKind;
    private String structureCode;




    public RelayContactorPO getRelayContactorPO() {
        return relayContactorPO;
    }

    public void setRelayContactorPO(RelayContactorPO relayContactorPO) {
        this.relayContactorPO = relayContactorPO;
    }

    public String getUsePercent() {
        return usePercent;
    }

    public void setUsePercent(String usePercent) {
        this.usePercent = usePercent;
    }

    public Integer getLifeWarning() {
        return lifeWarning;
    }

    public void setLifeWarning(Integer lifeWarning) {
        this.lifeWarning = lifeWarning;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getComponentType() {
        return componentType;
    }

    public void setComponentType(String componentType) {
        this.componentType = componentType;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public Integer getComponentKind() {
        return componentKind;
    }

    public void setComponentKind(Integer componentKind) {
        this.componentKind = componentKind;
    }

    public String getStructureCode() {
        return structureCode;
    }

    public void setStructureCode(String structureCode) {
        this.structureCode = structureCode;
    }
}
