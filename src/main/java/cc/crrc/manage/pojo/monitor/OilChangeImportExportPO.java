package cc.crrc.manage.pojo.monitor;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

public class OilChangeImportExportPO implements Serializable {

    private static final long serialVersionUID = 5086762855590519L;
    @ExcelIgnore
    private String id;
    @NotNull
    @Excel(name = "车型编号" ,isImportField = "true", orderNum = "0")
    private String vehicleTypeId;
    @NotNull
    @Excel(name = "车辆编号" ,isImportField = "true", orderNum = "1")
    private String vehicleCode;
    @NotNull
    @Excel(name = "部件名称" ,isImportField = "true", orderNum = "2")
    private String nameCn;
    @NotNull
    @Excel(name = "部件类型" ,isImportField = "true", orderNum = "3")
    private String typeCn;
    @NotNull
    @Excel(name = "部件位置" ,isImportField = "true", orderNum = "4")
    private String location;
    @NotNull
    @Excel(name = "更换时间" ,isImportField = "true", orderNum = "5")
    private String changeTime;
    /*@NotNull
    @Excel(name = "更换周期" ,isImportField = "period", orderNum = "6")
    private String period;*/
    @NotNull
    @Excel(name = "预警时间" ,isImportField = "true", orderNum = "6")
    private String warningTime;
    @Excel(name = "备注" ,isImportField = "true", orderNum = "8")
    private String remark;
    @NotNull
    @Excel(name = "预警里程" ,isImportField = "true", orderNum = "7")
    private String warningMileage;

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getTypeCn() {
        return typeCn;
    }

    public void setTypeCn(String typeCn) {
        this.typeCn = typeCn;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getChangeTime() {
        return changeTime;
    }

    public void setChangeTime(String changeTime) {
        this.changeTime = changeTime;
    }

    /*public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }*/

    public String getWarningTime() {
        return warningTime;
    }

    public void setWarningTime(String warningTime) {
        this.warningTime = warningTime;
    }

    public String getWarningMileage() {
        return warningMileage;
    }

    public void setWarningMileage(String warningMileage) {
        this.warningMileage = warningMileage;
    }
}
