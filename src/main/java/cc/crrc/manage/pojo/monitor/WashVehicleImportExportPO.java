package cc.crrc.manage.pojo.monitor;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

public class WashVehicleImportExportPO implements Serializable {

    private static final long serialVersionUID = 747566254550672925L;

    @ExcelIgnore
    private String id;
    @NotNull
    @Excel(name = "车辆编号" ,isImportField = "true", orderNum = "0")
    private String vehicleCode;
    @NotNull
    @Excel(name = "洗车模式" ,isImportField = "true", orderNum = "1")
    private String modeCn;
    @NotNull
    @Excel(name = "洗车时间" ,isImportField = "true", orderNum = "2")
    private String changeTime;
    /*@NotNull
    @Excel(name = "洗车周期" ,isImportField = "period", orderNum = "3")
    private String period;*/
    @NotNull
    @Excel(name = "预警时间" ,isImportField = "true", orderNum = "3")
    private String warningTime;
    @Excel(name = "备注" ,isImportField = "true", orderNum = "4")
    private String remark;

    /*public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }*/

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getModeCn() {
        return modeCn;
    }

    public void setModeCn(String modeCn) {
        this.modeCn = modeCn;
    }

    public String getChangeTime() {
        return changeTime;
    }

    public void setChangeTime(String changeTime) {
        this.changeTime = changeTime;
    }

    public String getWarningTime() {
        return warningTime;
    }

    public void setWarningTime(String warningTime) {
        this.warningTime = warningTime;
    }


}
