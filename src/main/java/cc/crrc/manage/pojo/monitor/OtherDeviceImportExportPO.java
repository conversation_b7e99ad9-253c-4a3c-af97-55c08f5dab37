package cc.crrc.manage.pojo.monitor;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

public class OtherDeviceImportExportPO implements Serializable {

    private static final long serialVersionUID = 3443123455789L;
    @ExcelIgnore
    private String id;
    @NotNull
    @Excel(name = "设备名称" ,isImportField = "true", orderNum = "0")
    private String equipment;
    @NotNull
    @Excel(name = "设备类型" ,isImportField = "true", orderNum = "1")
    private String typeCn;
    @NotNull
    @Excel(name = "列车编号" ,isImportField = "true", orderNum = "2")
    private String vehicleCode;
    @NotNull
    @Excel(name = "车厢" ,isImportField = "true", orderNum = "3")
    private String carriage;
    @NotNull
    @Excel(name = "设备位置" ,isImportField = "true", orderNum = "4")
    private String location;
    @NotNull
    @Excel(name = "起始日期" ,isImportField = "true", orderNum = "5")
    private String changeTime;
    /*@Excel(name = "有效周期" ,isImportField = "period", orderNum = "5")
    private String period;*/
    @NotNull
    @Excel(name = "有效日期" ,isImportField = "true", orderNum = "6")
    private String warningTime;
    @Excel(name = "备注" ,isImportField = "true", orderNum = "7")
    private String remark;

    public String getRemark() {
        return remark;
    }
    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getEquipment() {
        return equipment;
    }

    public void setEquipment(String equipment) {
        this.equipment = equipment;
    }

    public String getCarriage() {
        return carriage;
    }

    public void setCarriage(String carriage) {
        this.carriage = carriage;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getChangeTime() {
        return changeTime;
    }

    public void setChangeTime(String changeTime) {
        this.changeTime = changeTime;
    }

    /*public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }*/

    public String getWarningTime() {
        return warningTime;
    }

    public void setWarningTime(String warningTime) {
        this.warningTime = warningTime;
    }

    public String getTypeCn() {
        return typeCn;
    }

    public void setTypeCn(String typeCn) {
        this.typeCn = typeCn;
    }
}
