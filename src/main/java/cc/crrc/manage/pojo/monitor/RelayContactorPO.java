package cc.crrc.manage.pojo.monitor;

import cc.crrc.manage.common.annotation.Duplicate;
import cc.crrc.manage.common.annotation.Duplicates;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import java.io.Serializable;


@Duplicates({
        @Duplicate(table = "relay_contactor", message = "部件名称已存在！", condition = "mtr_vehicle_type_id='${mtrVehicleTypeId}' and name_cn='${nameCn}' and comm_original_signal_id='${commOriginalSignalId}' and del_flag = 0 ", groups = {
                Insert.class}),
        @Duplicate(table = "relay_contactor", message = "部件名称已存在！", condition = "mtr_vehicle_type_id='${mtrVehicleTypeId}' and name_cn='${nameCn}' and comm_original_signal_id='${commOriginalSignalId}'  and id!='${id}' and del_flag = 0", groups = {Update.class})
})
public class RelayContactorPO implements Serializable {
    private static final long serialVersionUID = 238118892224086353L;
    private String snowId;
    @LogParam(description = "主键")
    private String id;

    //2021-05-27 lixin 增加
    @LogParam(description = "信号id")
    private String commOriginalSignalId;
    @LogParam(description = "信号英文名")
    private String commOriginalSignalNameEn;
    @LogParam(description = "电气寿命")
    private Long electricalLife;
    @LogParam(description = "列车类型")
    private String mtrVehicleTypeId;
    @LogParam(description = "车型中文名")
    private String vehicleTypeNameCn;
    @LogParam(description = "使用百分比")
    private String usePercent;

    @Excel(name = "部件名称", orderNum = "0")
    @LogParam(description = "部件中文名称")
    @Length(max = 25, min = 1, message = "部件名称长度必须大于等于1且小于等于25",groups= {Insert.class, Update.class})
    private String nameCn;
    @LogParam(description = "部件类型,继电器relay/接触器contactor")
    private String type;
    @LogParam(description = "部件类型中文名称")
    private String typeCn;
    @LogParam(description = "继电器或接触器仅30天平均每日开合次数")
    private Long operationAvg;
    @LogParam(description = "车辆编号")
    private String vehicleCode;
    @Excel(name = "已使用", orderNum = "1")
    @LogParam(description = "继电器或接触器开合次数,已使用")
    private Long operationCnt;
    @LogParam(description = "继电器或接触器剩余寿命，单位：天")
    private Long remainingLife;
    @Excel(name = "安装时间", orderNum = "3" )
    @LogParam(description = "安装时间")
    private String assemblyTime;
    @LogParam(description = "安装位置")
    private String assemblyLocation;
    @Excel(name = "更换人员", orderNum = "4")
    @LogParam(description = "安装人")
    private String installer;
    @LogParam(description = "备注")
    private String remark;
    @LogParam(description = "构型编码")
    private String structureCode;
    @LogParam(description = "大数据处理计算类型(time、count、distance)")
    private String calculationType;
    @LogParam(description = "计算类型中文名")
    private String calculationTypeCn;
    @LogParam(description = "符合条件的触发值（非必填）")
    private Integer triggerValue;
    @LogParam(description = "符合的计算方式（大于 等于 不等于）")
    private String calculationMethod;
    @LogParam(description = "计算方式中文名")
    private String calculationMethodCn;
    @LogParam(description = "线路id")
    private String lineId;
    @LogParam(description = "线路中文名")
    private String lineName;
    @LogParam(description = "单位")
    @Length(max = 25,  message = "单位长度必须大于等于0且小于等于25",groups= {Insert.class, Update.class})
    @Excel(name = "单位", orderNum = "2")
    private String unit;
    @JsonIgnore
    private Integer delFlag;
    @LogParam(description = "更换次数")
    private Long replaceTimes;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String createBy;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String createTime;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String modifyBy;
    @JsonIgnore
    @ApiModelProperty(hidden = true)
    private String modifyTime;


    private Integer componentKind;
    @Length(max = 25, min = 1, message = "部件名称长度必须大于等于1且小于等于25",groups= {Insert.class, Update.class})
    private String nameEn;
    @Length(max = 25, min = 1, message = "部件名称长度必须大于等于1且小于等于25",groups= {Insert.class, Update.class})
    private String productNumber;
    private String manufacturerId;
    @LogParam(description = "部件生产商名称")
    private String manufacturerName;




    public String getCommOriginalSignalId() {
        return commOriginalSignalId;
    }

    public void setCommOriginalSignalId(String commOriginalSignalId) {
        this.commOriginalSignalId = commOriginalSignalId;
    }

    public String getCommOriginalSignalNameEn() {
        return commOriginalSignalNameEn;
    }

    public void setCommOriginalSignalNameEn(String commOriginalSignalNameEn) {
        this.commOriginalSignalNameEn = commOriginalSignalNameEn;
    }

    public Long getElectricalLife() {
        return electricalLife;
    }

    public void setElectricalLife(Long electricalLife) {
        this.electricalLife = electricalLife;
    }

    public String getMtrVehicleTypeId() {
        return mtrVehicleTypeId;
    }

    public void setMtrVehicleTypeId(String mtrVehicleTypeId) {
        this.mtrVehicleTypeId = mtrVehicleTypeId;
    }

    public String getSnowId() {
        return snowId;
    }

    public void setSnowId(String snowId) {
        this.snowId = snowId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getOperationAvg() {
        return operationAvg;
    }

    public void setOperationAvg(Long operationAvg) {
        this.operationAvg = operationAvg;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public Long getOperationCnt() {
        return operationCnt;
    }

    public void setOperationCnt(Long operationCnt) {
        this.operationCnt = operationCnt;
    }

    public Long getRemainingLife() {
        return remainingLife;
    }

    public void setRemainingLife(Long remainingLife) {
        this.remainingLife = remainingLife;
    }

    public String getAssemblyTime() {
        return assemblyTime;
    }

    public void setAssemblyTime(String assemblyTime) {
        this.assemblyTime = assemblyTime;
    }

    public String getAssemblyLocation() {
        return assemblyLocation;
    }

    public void setAssemblyLocation(String assemblyLocation) {
        this.assemblyLocation = assemblyLocation;
    }

    public String getInstaller() {
        return installer;
    }

    public void setInstaller(String installer) {
        this.installer = installer;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getStructureCode() {
        return structureCode;
    }

    public void setStructureCode(String structureCode) {
        this.structureCode = structureCode;
    }

    public String getCalculationType() {
        return calculationType;
    }

    public void setCalculationType(String calculationType) {
        this.calculationType = calculationType;
    }

    public Integer getTriggerValue() {
        return triggerValue;
    }

    public void setTriggerValue(Integer triggerValue) {
        this.triggerValue = triggerValue;
    }

    public String getCalculationMethod() {
        return calculationMethod;
    }

    public void setCalculationMethod(String calculationMethod) {
        this.calculationMethod = calculationMethod;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Integer getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Integer delFlag) {
        this.delFlag = delFlag;
    }

    public Long getReplaceTimes() {
        return replaceTimes;
    }

    public void setReplaceTimes(Long replaceTimes) {
        this.replaceTimes = replaceTimes;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public String getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(String modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getTypeCn() {
        return typeCn;
    }

    public void setTypeCn(String typeCn) {
        this.typeCn = typeCn;
    }

    public String getUsePercent() {
        return usePercent;
    }

    public void setUsePercent(String usePercent) {
        this.usePercent = usePercent;
    }

    public String getVehicleTypeNameCn() {
        return vehicleTypeNameCn;
    }

    public void setVehicleTypeNameCn(String vehicleTypeNameCn) {
        this.vehicleTypeNameCn = vehicleTypeNameCn;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getCalculationTypeCn() {
        return calculationTypeCn;
    }

    public void setCalculationTypeCn(String calculationTypeCn) {
        this.calculationTypeCn = calculationTypeCn;
    }

    public String getCalculationMethodCn() {
        return calculationMethodCn;
    }

    public void setCalculationMethodCn(String calculationMethodCn) {
        this.calculationMethodCn = calculationMethodCn;
    }


    public Integer getComponentKind() {
        return componentKind;
    }

    public void setComponentKind(Integer componentKind) {
        this.componentKind = componentKind;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getProductNumber() {
        return productNumber;
    }

    public void setProductNumber(String productNumber) {
        this.productNumber = productNumber;
    }

    public String getManufacturerId() {
        return manufacturerId;
    }

    public void setManufacturerId(String manufacturerId) {
        this.manufacturerId = manufacturerId;
    }

    public String getManufacturerName() {
        return manufacturerName;
    }

    public void setManufacturerName(String manufacturerName) {
        this.manufacturerName = manufacturerName;
    }
}
