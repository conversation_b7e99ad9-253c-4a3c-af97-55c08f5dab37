package cc.crrc.manage.pojo.monitor;

import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import org.hibernate.validator.constraints.Length;

import java.util.Date;

public class RelayContactorLifePO {

    //初始化relay_contactor_life 使用
    //2021-0527-lixin
    private String id;
    private String relayContactorId;
    private Long operationCnt;
    private Long remainingLife;
    private String vehicleCode;
    private Date assemblyTime;
    private String assemblyLocation;
    private Integer valid;
    private String remark;
    private String usepercent;
    private String productNumber;
    private String manufacturerId;
    private String installer;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRelayContactorId() {
        return relayContactorId;
    }

    public void setRelayContactorId(String relayContactorId) {
        this.relayContactorId = relayContactorId;
    }

    public Long getOperationCnt() {
        return operationCnt;
    }

    public void setOperationCnt(Long operationCnt) {
        this.operationCnt = operationCnt;
    }

    public Long getRemainingLife() {
        return remainingLife;
    }

    public void setRemainingLife(Long remainingLife) {
        this.remainingLife = remainingLife;
    }

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public Date getAssemblyTime() {
        return assemblyTime;
    }

    public void setAssemblyTime(Date assemblyTime) {
        this.assemblyTime = assemblyTime;
    }

    public String getAssemblyLocation() {
        return assemblyLocation;
    }

    public void setAssemblyLocation(String assemblyLocation) {
        this.assemblyLocation = assemblyLocation;
    }

    public Integer getValid() {
        return valid;
    }

    public void setValid(Integer valid) {
        this.valid = valid;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getUsepercent() {
        return usepercent;
    }

    public void setUsepercent(String usepercent) {
        this.usepercent = usepercent;
    }

    public String getProductNumber() {
        return productNumber;
    }

    public void setProductNumber(String productNumber) {
        this.productNumber = productNumber;
    }

    public String getManufacturerId() {
        return manufacturerId;
    }

    public void setManufacturerId(String manufacturerId) {
        this.manufacturerId = manufacturerId;
    }

    public String getInstaller() {
        return installer;
    }

    public void setInstaller(String installer) {
        this.installer = installer;
    }
}
