package cc.crrc.manage.pojo.monitor;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

public class WashVehicleHistoryForExcelPO implements Serializable {

    private static final long serialVersionUID = 8876922080479252214L;

    @NotNull
    @Excel(name = "列车编号" ,isImportField = "vehicleCode", orderNum = "0")
    private String vehicleCode;
    @NotNull
    @Excel(name = "洗车模式" ,isImportField = "modeCn", orderNum = "1")
    private String modeCn;
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "洗车时间" ,isImportField = "changeTime", orderNum = "2", format = "yyyy-MM-dd")
    private Date changeTime;
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "预警时间" ,isImportField = "warningTime", orderNum = "3", format = "yyyy-MM-dd")
    private Date warningTime;
    @NotNull
    @Excel(name = "洗车人员" ,isImportField = "operator", orderNum = "4")
    private String operator;

    public String getVehicleCode() {
        return vehicleCode;
    }

    public void setVehicleCode(String vehicleCode) {
        this.vehicleCode = vehicleCode;
    }

    public String getModeCn() {
        return modeCn;
    }

    public void setModeCn(String modeCn) {
        this.modeCn = modeCn;
    }

    public Date getChangeTime() {
        return changeTime;
    }

    public void setChangeTime(Date changeTime) {
        this.changeTime = changeTime;
    }

    public Date getWarningTime() {
        return warningTime;
    }

    public void setWarningTime(Date warningTime) {
        this.warningTime = warningTime;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}
