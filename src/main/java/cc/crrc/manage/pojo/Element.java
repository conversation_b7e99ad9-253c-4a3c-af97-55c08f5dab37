package cc.crrc.manage.pojo;
import cc.crrc.manage.common.annotation.LogParam;

import java.util.Date;

public class Element {
    @LogParam(description="资源id")
    private String id;
    @LogParam(description="资源编码")
    private String elementCode;
    @LogParam(description="资源类型")
    private String elementType;
    @LogParam(description="资源名称")
    private String elementName;
    @LogParam(description="资源路径")
    private String elementUri;
    @LogParam(description="所属菜单id")
    private String menuId;
    private String parentId;
    private String path;
    @LogParam(description="资源方法")
    private String method;
    @LogParam(description="资源备注")
    private String remarks;
    private String createBy;
    private Date createDate;
    private String updateBy;
    private Date updateDate;
    private String tenantId;
    private String delFlag;
    private String menuName;
    //资源权限关系id
    private String elementRoleId;
    
    //返回前端是否被勾选 heshenglun 2020-8-26
    private String selectBox;
    //当前正在编辑的角色的roleId heshenglun 2020-8-26
    private String roleId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getElementCode() {
        return elementCode;
    }

    public void setElementCode(String elementCode) {
        this.elementCode = elementCode;
    }

    public String getElementType() {
        return elementType;
    }

    public void setElementType(String elementType) {
        this.elementType = elementType;
    }

    public String getElementName() {
        return elementName;
    }

    public void setElementName(String elementName) {
        this.elementName = elementName;
    }

    public String getElementUri() {
        return elementUri;
    }

    public void setElementUri(String elementUri) {
        this.elementUri = elementUri;
    }

    public String getMenuId() {
        return menuId;
    }

    public void setMenuId(String menuId) {
        this.menuId = menuId;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getMenuName() {
        return menuName;
    }

    public void setMenuName(String menuName) {
        this.menuName = menuName;
    }

    public String getElementRoleId() {
        return elementRoleId;
    }

    public void setElementRoleId(String elementRoleId) {
        this.elementRoleId = elementRoleId;
    }

	public String getSelectBox() {
		return selectBox;
	}

	public void setSelectBox(String selectBox) {
		this.selectBox = selectBox;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}
	
	
    
    
    
    
}
