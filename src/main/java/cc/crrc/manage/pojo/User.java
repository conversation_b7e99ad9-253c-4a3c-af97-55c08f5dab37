package cc.crrc.manage.pojo;

import cc.crrc.manage.common.annotation.Email;
import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.common.annotation.Phone;
import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;

import static cc.crrc.manage.common.utils.CodecUtils.regexp;

public class User extends PageVO implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(hidden = true)
    @LogParam(description="用户id")
    private String id;
    @NotBlank(message = "登录名不能为空")
    @NotNull(message = "登录名不能为空")
    @LogParam(description="用户名")
    private String username;
    @LogParam(description="密码")
    @Pattern(regexp = regexp,message = "密码必须同时包含字母、数字、和下列所示的!@#$%^&*_-特殊字符",groups = {Insert.class, Update.class})
    @Length(min = 6, max = 40, message = "密码长度介于6~40位之间", groups = {Insert.class, Update.class})
    private String password;
    @LogParam(description="昵称")
    private String name;
    @LogParam(description="生日")
    private String birthday;
    @LogParam(description="地址")
    private String address;
    @Phone(groups= {Insert.class, Update.class})
    @LogParam(description="移动电话")
    private String mobilePhone;
    @LogParam(description="座机")
    private String telPhone;
    @Email(groups= {Insert.class,Update.class})
    @LogParam(description="邮箱")
    private String email;
    @LogParam(description="性别")
    private String sex;
    private String type;
    @LogParam(description="备注")
    private String remarks;
    @ApiModelProperty(hidden = true)
    private String createBy;
    @ApiModelProperty(hidden = true)
    private Date createDate;
    @ApiModelProperty(hidden = true)
    private String updateBy;
    @ApiModelProperty(hidden = true)
    private Date updateDate;
    @ApiModelProperty(hidden = true)
    private String delFlag;
    @ApiModelProperty(hidden = true)
    private String tenantId;
    private String organizationId;
    private String isSuperAdmin;
    //@LogParam(description="角色id")
    private String roleId;
    @LogParam(description="角色编码")
    private String roleCode;
    @LogParam(description="角色名称")
    private String roleName;
    //组织id(涉及多个则以,分隔)
    @LogParam(description="组织ids")
    private String organizationIds;
    //组织id(涉及多个则以,分隔)
    @LogParam(description="组织名称")
    private String organizationNames;
    //头像地址
    @LogParam(description="头像名称")
    private String image;
    //角色组
    @LogParam(description="角色组")
    private String[] roleIds;
    //用户的角色id字符串(通过roleIds进行拼接,以;间隔)zhangzhijian 2020-6-22
    @LogParam(description="角色组")
    private String userRoleIds;

    public String[] getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(String[] roleIds) {
        this.roleIds = roleIds;
    }

    public String getImage() {
        return image;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public String getOrganizationNames() {
        return organizationNames;
    }

    public void setOrganizationNames(String organizationNames) {
        this.organizationNames = organizationNames;
    }

    public String getOrganizationIds() {
        return organizationIds;
    }

    public void setOrganizationIds(String organizationIds) {
        this.organizationIds = organizationIds;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getTelPhone() {
        return telPhone;
    }

    public void setTelPhone(String telPhone) {
        this.telPhone = telPhone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    public String getIsSuperAdmin() {
        return isSuperAdmin;
    }

    public void setIsSuperAdmin(String isSuperAdmin) {
        this.isSuperAdmin = isSuperAdmin;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getUserRoleIds() { return userRoleIds; }

    public void setUserRoleIds(String userRoleIds) { this.userRoleIds = userRoleIds; }
}