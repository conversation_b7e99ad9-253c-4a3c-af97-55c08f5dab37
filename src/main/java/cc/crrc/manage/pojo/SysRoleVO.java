package cc.crrc.manage.pojo;

import cc.crrc.manage.common.annotation.LogParam;
import org.hibernate.validator.constraints.Length;

import java.util.Date;
import java.util.List;

public class SysRoleVO extends PageVO{
    @LogParam(description="角色id")
    private String id;
    @LogParam(description="角色编码")
    private String roleCode;
    @LogParam(description="角色名称")
    private String roleName;

    private String parentId;

    private String rolePath;

    private String roleType;
    @LogParam(description="角色备注")
    @Length(max = 255,message = "备注名称长度过长，长度需小于255。")
    private String remarks;

    private String createBy;

    private Date createDate;

    private String updateBy;

    private Date updateDate;

    private String tenantId;

    private String delFlag;
    @LogParam(description="角色关联的菜单列表")
    private String selectedMenuIds; 
    
    /** 菜单组 */
    private String[] menuIds;
    
    //menu对应element的集合  heshenglun 2020-8-25 
    private List<Menu> menuForm;
    

    /** 部门组 */
    private String[] deptIds;

    private String roleTypeName;
    /*董哥说需要数据权限相关字段*/
    @LogParam(description="数据权限")
    private String dataPermission;

    //2020-6-22 zhangzhijian 添加角色默认首页 菜单id
    @LogParam(description="角色默认首页菜单id")
    private String homeMenuId;
    //2020-6-22 zhangzhijain 添加默认首页 菜单title
    @LogParam(description="角色默认首页菜单title")
    private String homeMenuTitle;

    /*数据权限的中文名字*/
    private String label;

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getDataPermission() {
        return dataPermission;
    }

    public void setDataPermission(String dataPermission) {
        this.dataPermission = dataPermission;
    }

    public String getSelectedMenuIds() {
        return selectedMenuIds;
    }

    public void setSelectedMenuIds(String selectedMenuIds) {
        this.selectedMenuIds = selectedMenuIds;
    }

    public String getRoleTypeName() {
        return roleTypeName;
    }

    public void setRoleTypeName(String roleTypeName) {
        this.roleTypeName = roleTypeName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getRolePath() {
        return rolePath;
    }

    public void setRolePath(String rolePath) {
        this.rolePath = rolePath;
    }

    public String getRoleType() {
        return roleType;
    }

    public void setRoleType(String roleType) {
        this.roleType = roleType;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String[] getMenuIds() {
        return menuIds;
    }

    public void setMenuIds(String[] menuIds) {
        this.menuIds = menuIds;
    }

    public String[] getDeptIds() {
        return deptIds;
    }

    public void setDeptIds(String[] deptIds) {
        this.deptIds = deptIds;
    }

    public String getHomeMenuId() { return homeMenuId; }

    public void setHomeMenuId(String homeMenuId) { this.homeMenuId = homeMenuId; }

    public String getHomeMenuTitle() { return homeMenuTitle; }

    public void setHomeMenuTitle(String homeMenuTitle) { this.homeMenuTitle = homeMenuTitle; }

	public List<Menu> getMenuForm() {
		return menuForm;
	}

	public void setMenuForm(List<Menu> menuForm) {
		this.menuForm = menuForm;
	}
    
    
    
    
    
}
