package cc.crrc.manage.pojo;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

public class BusDict {

    private static final long serialVersionUID=1L;

    /** 主键id */
    @ApiModelProperty(value = "主键id")
    private String id;
    /** 排序 */
    @ApiModelProperty(value = "排序")
    private Long sortNumber;
    /** 字典标签 */
    @ApiModelProperty(value = "字典标签")
    private String label;
    /** 字典值 */
    @ApiModelProperty(value = "字典值")
    private String value;
    /** 描述 */
    @ApiModelProperty(value = "描述")
    private String description;
    /** 最后修改时间 */
    @ApiModelProperty(value = "最后修改时间")
    private Date lastModifyTime;
    /** 最后修改人 */
    @ApiModelProperty(value = "最后修改人")
    private String lastModifyBy;
    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /** 创建人 */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /** 删除标识 0-未删除  1-删除 */
    @ApiModelProperty(value = "删除标识 0-未删除  1-删除")
    private String delFlag;
    /** 字典类型code */
    @ApiModelProperty(value = "字典类型code")
    private String typeCode;
    /** 线路id */
    @ApiModelProperty(value = "线路id")
    private String lineId;
    /** 标签code */
    @ApiModelProperty(value = "标签code")
    private String code;

    /** 车型id */
    @ApiModelProperty(value = "车型id")
    private String vehicleTypeId;

    /** 车型名称 */
    @ApiModelProperty(value = "车型名称")
    private String vehicleTypeName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getSortNumber() {
        return sortNumber;
    }

    public void setSortNumber(Long sortNumber) {
        this.sortNumber = sortNumber;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getLastModifyTime() {
        return lastModifyTime;
    }

    public void setLastModifyTime(Date lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    public String getLastModifyBy() {
        return lastModifyBy;
    }

    public void setLastModifyBy(String lastModifyBy) {
        this.lastModifyBy = lastModifyBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getVehicleTypeName() {
        return vehicleTypeName;
    }

    public void setVehicleTypeName(String vehicleTypeName) {
        this.vehicleTypeName = vehicleTypeName;
    }
}
