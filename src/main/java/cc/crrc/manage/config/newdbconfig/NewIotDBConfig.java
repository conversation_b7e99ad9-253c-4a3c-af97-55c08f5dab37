package cc.crrc.manage.config.newdbconfig;

import cc.crrc.manage.common.utils.Constants;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(value = "tsdbflag",havingValue = Constants.IOTDB)
public class NewIotDBConfig{


    @Value("${iotdb.driver-class-name}")
    private String driverName;
    @Value("${iotdb.master.url}")
    private String url;
    @Value("${iotdb.master.username}")
    private String userName;
    @Value("${iotdb.master.password}")
    private String password;
    @Value("${iotdb.slave.url}")
    private String slaveUrl;
    @Value("${iotdb.slave.username}")
    private String slaveUserName;
    @Value("${iotdb.slave.password}")
    private String slavePassword;




}
