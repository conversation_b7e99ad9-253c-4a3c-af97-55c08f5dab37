package cc.crrc.manage.config.newdbconfig;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

public class NewIotDBUtils implements NewTSDBUtiles{
    private static final Logger LOG = LoggerFactory.getLogger(NewIotDBUtils.class);

    public NewIotDBUtils(String driverName) {
        try {
            Class.forName(driverName);
        } catch (ClassNotFoundException e) {
            LOG.error("驱动" + driverName + "注册失败：", e.getMessage());
        }
    }


    public static Connection getConnection(String type){
        if ("MASTER".equals(type))
            return null;
        return null;
    }

    public static void release(ResultSet rs, Statement st, Connection conn){
        try {
            if (rs != null)
                rs.close();
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            try {
                if (st != null)
                    st.close();
            } catch (SQLException e) {
                e.printStackTrace();
            } finally {
                try {
                    if (conn != null)
                        conn.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
