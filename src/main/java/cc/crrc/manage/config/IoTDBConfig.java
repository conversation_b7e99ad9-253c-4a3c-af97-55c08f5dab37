package cc.crrc.manage.config;

import cc.crrc.manage.common.utils.Constants;
import cc.crrc.manage.common.utils.tsdb.TSDBUtils;
import cc.crrc.manage.common.utils.tsdb.iotdb.IoTDBUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnProperty(value = "tsdbflag",havingValue = Constants.IOTDB)
public class IoTDBConfig {
    @Value("${iotdb.driver-class-name:'org.apache.iotdb.jdbc.IoTDBDriver'}")
    private String driverClass;
    @Value("${iotdb.master.url:''}")
    private String url1;
    @Value("${iotdb.master.username:''}")
    private String userName1;
    @Value("${iotdb.master.password:''}")
    private String password1;
    @Value("${iotdb.slave.url:''}")
    private String url2;
    @Value("${iotdb.slave.username:''}")
    private String userName2;
    @Value("${iotdb.slave.password:''}")
    private String password2;

    @Bean
    public TSDBUtils ioTDBUtils() {
        return new IoTDBUtils(userName1, password1, url1,userName2, password2, url2, driverClass);
    }
}
