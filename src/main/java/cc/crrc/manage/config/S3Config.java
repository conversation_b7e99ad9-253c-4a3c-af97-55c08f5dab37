package cc.crrc.manage.config;

import io.minio.MinioClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName S3Config
 * @Description
 * @<PERSON> <PERSON>
 * @Date 2023/10/25 13:42
 **/
@Configuration
public class S3Config {
    @Value("${crrc.storage.s3.endpoint}")
    private String endpoint;
    @Value("${crrc.storage.s3.accessKey}")
    private String accessKey;
    @Value("${crrc.storage.s3.secretKey}")
    private String secretKey;
    @Value("${crrc.storage.s3.defaultBucket}")
    private String defaultBucket;

    public S3Config() {
    }

    @Bean
    public MinioClient minioClient() {
        return MinioClient.builder()
                .endpoint(endpoint)
                .credentials(accessKey, secretKey)
                .build();
    }

    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getDefaultBucket() {
        return defaultBucket;
    }

    public void setDefaultBucket(String defaultBucket) {
        this.defaultBucket = defaultBucket;
    }
}
