//package cc.crrc.manage.config;
//
//import cc.crrc.manage.common.utils.Constants;
//import cc.crrc.manage.common.utils.tsdb.TSDBUtils;
//import cc.crrc.manage.common.utils.tsdb.influxdb.InfluxDbUtils;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.context.annotation.Bean;
//import org.springframework.stereotype.Component;
//
///**
// * @Author: Li Caisheng
// * @Date: 2019-12-30
// */
//@Component
//@ConditionalOnProperty(value = "tsdbflag",havingValue = Constants.INFLUXDB)
//public class InfluxDbConfig {
//
//    @Value("${influx.url:''}")
//    private String influxDBUrl;
//
//    @Value("${influx.user:''}")
//    private String userName;
//
//    @Value("${influx.password:''}")
//    private String password;
//
//    @Value("${influx.database:''}")
//    private String database;
//
//    @Bean
//    public TSDBUtils influxDbUtils() {
//        return new InfluxDbUtils(userName, password, influxDBUrl, database);
//    }
//}
