package cc.crrc.manage.config;

import cc.crrc.manage.datascope.DataScopeInterceptor;
import com.github.pagehelper.PageInterceptor;
import org.mybatis.spring.boot.autoconfigure.ConfigurationCustomizer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

@Configuration
public class DataScopeConfig {
    @Value("${mybatis.dialect}")
    private String helperDialect;

    @Bean
    public ConfigurationCustomizer configurationCustomizer() {
        return new ConfigurationCustomizer() {
            public void customize(org.apache.ibatis.session.Configuration configuration) {
                configuration.setMapUnderscoreToCamelCase(true);
                PageInterceptor pageInterceptor = new PageInterceptor();
                Properties p = new Properties();
                p.getProperty("helperDialect", helperDialect);
                pageInterceptor.setProperties(p);
                configuration.addInterceptor(pageInterceptor);
                configuration.addInterceptor(new DataScopeInterceptor());
            }
        };
    }
}
