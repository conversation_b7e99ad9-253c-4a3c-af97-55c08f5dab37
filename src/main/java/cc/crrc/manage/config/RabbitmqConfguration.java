package cc.crrc.manage.config;

import cc.crrc.manage.mq.DefaultErrorHandler;
import cc.crrc.manage.mq.RabbitmqChannelListener;
import cc.crrc.manage.mq.RabbitmqProperties;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;

import java.util.UUID;

/**
 * @FileName RabbitmqConfguration
 * <AUTHOR> shuangquan
 * @Date 2019/12/5 17:00
 **/
//@Configuration
public class RabbitmqConfguration {
    private static final String STRATEGY_PREFIX = "PHM_";
    @Autowired
    private RabbitmqProperties rabbitmqProperties;


    @Bean
    public ConnectionFactory connectionFactory() {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(rabbitmqProperties.getHost());
        connectionFactory.setPort(rabbitmqProperties.getPort());
        connectionFactory.setUsername(rabbitmqProperties.getUsername());
        connectionFactory.setPassword(rabbitmqProperties.getPassword());
        return connectionFactory;
    }

    @Bean
    public SimpleMessageListenerContainer messageListenerContainer(ConnectionFactory connectionFactory) {
        SimpleMessageListenerContainer simpleMessageListenerContainer = new SimpleMessageListenerContainer();
        simpleMessageListenerContainer.setConnectionFactory(connectionFactory);
        simpleMessageListenerContainer.addQueueNames(rabbitmqProperties.getQueueName());
        simpleMessageListenerContainer.setPrefetchCount(rabbitmqProperties.getFetchCount());
        simpleMessageListenerContainer.setConcurrentConsumers(rabbitmqProperties.getConcurrentConsumers());
        simpleMessageListenerContainer.setMaxConcurrentConsumers(rabbitmqProperties.getMaxConsumers());
        simpleMessageListenerContainer.setConsumerTagStrategy((queue) -> STRATEGY_PREFIX.concat(queue).concat("_").concat(UUID.randomUUID().toString()));
        simpleMessageListenerContainer.setMessageListener(new RabbitmqChannelListener());
        simpleMessageListenerContainer.setErrorHandler(new DefaultErrorHandler());
        return simpleMessageListenerContainer;
    }

}
