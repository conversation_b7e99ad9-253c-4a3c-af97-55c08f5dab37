package cc.crrc.manage.config;

import org.hibernate.validator.HibernateValidator;
import org.hibernate.validator.HibernateValidatorConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.validation.Validator;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;
import org.springframework.validation.beanvalidation.MethodValidationPostProcessor;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * @FileName ValidationConfiguration
 * <AUTHOR> shuangquan
 * @Date 2019/10/31 11:35
 **/
@Configuration
public class ValidationConfiguration implements WebMvcConfigurer {
    private static final String VALIDATION_FAIL_FAST = "true";

    @Override
    public Validator getValidator() {
        return validator();
    }

    @Bean
    public MethodValidationPostProcessor methodValidationPostProcessor() {
        MethodValidationPostProcessor postProcessor = new MethodValidationPostProcessor();
        postProcessor.setValidator(validator());
        return postProcessor;
    }

    @Bean
    public LocalValidatorFactoryBean validator() {
        LocalValidatorFactoryBean validator = new LocalValidatorFactoryBean();
        validator.setProviderClass(HibernateValidator.class);
        Map<String, String> config = new HashMap<>();
        config.put(HibernateValidatorConfiguration.FAIL_FAST, VALIDATION_FAIL_FAST);
        validator.setValidationPropertyMap(config);
        return validator;
    }

    // Use for @Duplicate
    @Bean
    public JdbcTemplate jdbcTemplate(DataSource dataSource) {
        JdbcTemplate template = new JdbcTemplate(dataSource);
        return template;
    }
}
