package cc.crrc.manage.config;

import cc.crrc.manage.common.utils.Constants;
import cc.crrc.manage.security.JwtUtils;
import cc.crrc.manage.security.SecurityProperties;
import cc.crrc.manage.security.filter.AuthenticationFilter;
import cc.crrc.manage.security.filter.DevelopAuthenticationTokenFilter;
import cc.crrc.manage.security.filter.JwtAuthenticationTokenFilter;
import cc.crrc.manage.security.handler.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;


@Configuration
@EnableWebSecurity
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {

    private final UserDetailsService authenticationUserDetailService;
    private final AuthenticationSuccessHandler authenticationSuccessHandler;
    private final AuthenticationFailureHandler authenticationFailureHandler;
    private final AuthenticationEntryPoint authenticationEntryPoint;
    private final OptionLogoutHandler optionLogoutHandler;
    @Autowired
    private Environment env;
    @Autowired
    private SecurityProperties securityProperties;
    @Autowired
    private JwtUtils jwtUtils;


    @Autowired
    public WebSecurityConfig(
            @Qualifier("AuthenticationUserDetailService") UserDetailsService authenticationUserDetailService,
            FormLoginAuthenticationSuccessHandler formLoginAuthenticationSuccessHandler,
            FormLoginAuthenticationFailureHandler formLoginAuthenticationFailureHandler,
            JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint,
            OptionLogoutHandler optionLogoutHandler) {
        this.authenticationUserDetailService = authenticationUserDetailService;
        this.authenticationFailureHandler = formLoginAuthenticationFailureHandler;
        this.authenticationSuccessHandler = formLoginAuthenticationSuccessHandler;
        this.authenticationEntryPoint = jwtAuthenticationEntryPoint;
        this.optionLogoutHandler = optionLogoutHandler;

    }

    /**
     * 注入自定义PermissionEvaluator
     */
    @Bean
    public DaoAuthenticationProvider daoAuthenticationProvider() {
        DaoAuthenticationProvider provider = new DaoAuthenticationProvider();
        provider.setUserDetailsService(authenticationUserDetailService);
        provider.setPasswordEncoder(passwordEncoder());
        provider.setHideUserNotFoundExceptions(false);
        return provider;
    }

    /**
     * 配置登录验证逻辑
     */
    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        //auth.userDetailsService(authenticationUserDetailService).passwordEncoder(passwordEncoder());
        auth.authenticationProvider(daoAuthenticationProvider());
    }


    /**
     * 配置security的控制逻辑
     */
    @Override
    protected void configure(HttpSecurity httpSecurity) throws Exception {
        httpSecurity
                //配置登录地址
                .formLogin().loginProcessingUrl(Constants.LOGIN)
                //配置登录成功自定义处理类
                .successHandler(authenticationSuccessHandler)
                //配置登录失败自定义处理类
                .failureHandler(authenticationFailureHandler).and()
                .exceptionHandling().accessDeniedHandler(authenticationAccessDeniedHandler()).and()
                //配置登出地址
                .logout().addLogoutHandler(optionLogoutHandler).logoutRequestMatcher(new AntPathRequestMatcher(
                Constants.LOGOUT)).logoutSuccessHandler(jsonLogoutSuccessHandler()).and()
                .csrf().disable()
                .cors().and()
                //配置没有权限自定义处理类
                .exceptionHandling().authenticationEntryPoint(authenticationEntryPoint).and()
                // 基于Token不需要session
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS).and()
                .authorizeRequests()
                //不进行权限验证的请求或资源(从配置文件中读取)
                .antMatchers(HttpMethod.OPTIONS, Constants.ALL_PATH).permitAll()
                //其他的需要登陆后才能访问
                .anyRequest().authenticated().and();
        httpSecurity.headers().cacheControl();
        String active_profile = env.getProperty("spring.profiles.active");
        AuthenticationFilter authenticationFilter;
        if (!Constants.LOCAL_PROFILE.equals(active_profile)) {
            authenticationFilter = new JwtAuthenticationTokenFilter(securityProperties, jwtUtils);
        } else {
            authenticationFilter = new DevelopAuthenticationTokenFilter();
        }
        httpSecurity.addFilterBefore(authenticationFilter, UsernamePasswordAuthenticationFilter.class);
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public RestAuthenticationAccessDeniedHandler authenticationAccessDeniedHandler() {
        return new RestAuthenticationAccessDeniedHandler();
    }

    @Bean
    public JsonLogoutSuccessHandler jsonLogoutSuccessHandler() {
        return new JsonLogoutSuccessHandler();
    }

    @Override
    public void configure(WebSecurity web) {
        web.ignoring().antMatchers(securityProperties.getIgnoreUrl());
    }

    @Bean
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }
}