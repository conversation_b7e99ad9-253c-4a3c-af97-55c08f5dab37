package cc.crrc.manage.config;

import cc.crrc.manage.common.utils.Constants;
import cc.crrc.manage.properties.CorsProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.util.CollectionUtils;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.sql.DataSource;
import java.math.BigDecimal;
import java.util.List;

/**
 * @FileName WebMvcConfig
 * <AUTHOR> shuangquan
 * @Date 2019/9/25 15:21
 **/
@Configuration
@Order(0)
@EnableWebMvc
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private CorsProperties corsProperties;
    //@Autowired
    //private AuthenticationInterceptor authenticationInterceptor;

    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
        // test 此地址为后端保存图片的资源地址配置 暂未使用
        registry.addResourceHandler("/image/**").addResourceLocations("file:D:/IDEAworkplace/phm-base/src/main" +
                "/resources/static/img/head/");
    }

    /*public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(authenticationInterceptor).addPathPatterns(Constants.ALL_PATH).excludePathPatterns(
                "/swagger-ui.html", "/swagger-resources/**", "/error", "/webjars/**", "/user/getUserInfo", "/sysMenu" +
                        "/selectSysMenuTreeList","/user/getAll","/sysRole/getSysRole","/user/front/menus");
    }*/

//    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
//        MappingJackson2HttpMessageConverter jackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter();
//        ObjectMapper objectMapper = new ObjectMapper();
//        SimpleModule simpleModule = new SimpleModule();
//        simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
//        simpleModule.addSerializer(Long.TYPE, ToStringSerializer.instance);
//        simpleModule.addSerializer(BigDecimal.class, ToStringSerializer.instance);
//        objectMapper.registerModule(simpleModule);
//        jackson2HttpMessageConverter.setObjectMapper(objectMapper);
//        converters.add(jackson2HttpMessageConverter);
//    }

    @Bean
    public PlatformTransactionManager transactionManager(DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean
    public FilterRegistrationBean corsFilterBean() {
        FilterRegistrationBean bean = new FilterRegistrationBean(corsFilter());
        bean.setOrder(0);
        return bean;
    }

    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
        if (CollectionUtils.isEmpty(corsProperties.getAllowedOrigins())) {
            config.addAllowedOrigin(Constants.ALL);
        } else {
            config.setAllowedOrigins(corsProperties.getAllowedOrigins());
        }
        if (CollectionUtils.isEmpty(corsProperties.getAllowedHeaders())) {
            config.addAllowedHeader(Constants.ALL);
        } else {
            config.setAllowedHeaders(corsProperties.getAllowedHeaders());
        }
        if (CollectionUtils.isEmpty(corsProperties.getAllowedMethods())) {
            config.addAllowedMethod(Constants.ALL);
        } else {
            config.setAllowedMethods(corsProperties.getAllowedMethods());
        }
        if (!CollectionUtils.isEmpty(corsProperties.getExposedHeaders())) {
            config.setExposedHeaders(corsProperties.getExposedHeaders());
        }
        config.setMaxAge(corsProperties.getMaxAge());
        config.setAllowCredentials(corsProperties.isAllowCredentials());
        source.registerCorsConfiguration(Constants.ALL_PATH, config);
        return new CorsFilter(source);
    }
}
