package cc.crrc.manage.config;

import cc.crrc.manage.common.utils.Constants;
import cc.crrc.manage.common.utils.tsdb.TSDBUtils;
import cc.crrc.manage.common.utils.tsdb.openTSDB.OpenTSDBUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnProperty(value = "tsdbflag",havingValue = Constants.OPENTSDB)
public class OpenTSDBConfig {
    @Value("${opentsdb.url:''}")
    private String OpenTSDBUrl;

    @Bean
    public TSDBUtils openTSDBUtils() {
        return new OpenTSDBUtils(OpenTSDBUrl);
    }
}
