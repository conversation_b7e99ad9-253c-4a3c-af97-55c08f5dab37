package cc.crrc.manage.security;

import cc.crrc.manage.common.utils.JsonUtils;
import io.jsonwebtoken.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Component
@Lazy(value = false)
public class JwtUtils {

    private static final String CLAIM_KEY_USER_JSON = "user_json";
    private static final String CLAIM_KEY_IP = "ip_address";
    @Autowired
    private SecurityProperties securityProperties;

    private static final SignatureAlgorithm SIGNATURE_ALGORITHM = SignatureAlgorithm.HS256;


    public String generateAccessToken(UserDetail userDetail, String ipAddress) {
        Map<String, Object> claims = generateClaims(userDetail, ipAddress);
        return generateAccessToken(userDetail.getUsername(), claims);
    }

    public Boolean isTokenExpired(String token) throws JwtException, IllegalArgumentException {
        final Date expiration = getExpirationDateFromToken(token);
        return expiration.before(new Date());
    }

    public UserDetail getUserFromToken(String token) throws JwtException, IllegalArgumentException {
        final Claims claims = getClaimsFromToken(token);
        return JsonUtils.parse((String) claims.get(CLAIM_KEY_USER_JSON), UserDetail.class);
    }

    public String getTokenIp(String token) throws JwtException, IllegalArgumentException {
        final Claims claims = getClaimsFromToken(token);
        return claims.get(CLAIM_KEY_IP).toString();
    }

    public Date getExpirationDateFromToken(String token) throws JwtException, IllegalArgumentException {
        Date expiration;
        final Claims claims = getClaimsFromToken(token);
        expiration = claims.getExpiration();
        return expiration;
    }

    private static Date generateExpirationDate(long expiration) {
        return new Date(System.currentTimeMillis() + expiration * 1000);
    }


    private static Map<String, Object> generateClaims(UserDetail userDetail, String ipAddress) {
        userDetail.setPassword("************");
        Map<String, Object> claims = new HashMap<>(2);
        claims.put(CLAIM_KEY_USER_JSON, JsonUtils.serialize(userDetail));
        claims.put(CLAIM_KEY_IP, ipAddress);
        return claims;
    }

    private String generateAccessToken(String subject, Map<String, Object> claims) {
        return generateToken(subject, claims, securityProperties.getExpiration());
    }

    private String generateToken(String subject, Map<String, Object> claims, long expiration) {
        String token = Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setId(UUID.randomUUID().toString())
                .setIssuedAt(new Date())
                .setExpiration(generateExpirationDate(expiration))
                .compressWith(CompressionCodecs.DEFLATE)
                .signWith(SIGNATURE_ALGORITHM, securityProperties.getSecret())
                .compact();
        return token;
    }

    private Claims getClaimsFromToken(String token) throws JwtException, IllegalArgumentException {
        Claims claims;
        try {
            claims = Jwts.parser()
                    .setSigningKey(securityProperties.getSecret())
                    .parseClaimsJws(token)
                    .getBody();
        }catch(ExpiredJwtException e){
            claims = e.getClaims();
        }
        return claims;
    }

}
