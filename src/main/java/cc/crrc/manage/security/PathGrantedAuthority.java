package cc.crrc.manage.security;

import org.springframework.security.core.GrantedAuthority;

/**
 * @FileName PathGrantedAuthority
 * <AUTHOR> shuangquan
 * @Date 2019/9/24 15:37
 **/
public class PathGrantedAuthority implements GrantedAuthority {

    private String id;
    private String path;
    private String method;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    @Override
    public String getAuthority() {

        return path;
    }

    public void setAuthority(String path) {
        this.path = path;
    }
}
