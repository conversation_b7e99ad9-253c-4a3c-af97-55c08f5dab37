package cc.crrc.manage.security.encryption;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.HashMap;
import java.util.Map;


/**
 * @projectName: guiyang2
 * @package: cc.crrc.manage.security.encryption
 * @className: RSAUtil
 * @author: lixin
 * @description: TODO
 * @date: 2022/6/9 14:42
 * @version: 1.0
 */
public class RSAUtil {
    private static final String CHARSET_NAME = "utf-8";
    private static final String TYPE = "RSA";

    public static final String PUBKEY = "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJ0x86/kTdYE72P9Ery5W0B63BN3U5OtWTI20BwA/6BNQj2yAtbw6KvdYx1B6br8WFaJrj7vkn/hvfQd/6od8HcCAwEAAQ==";
    public static final String PRIKEY = "MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAnTHzr+RN1gTvY/0SvLlbQHrcE3dTk61ZMjbQHAD/oE1CPbIC1vDoq91jHUHpuvxYVomuPu+Sf+G99B3/qh3wdwIDAQABAkBKq37BL6bVohSBH6b15Op2z6WYzZby0GZNVwI2OzcyUqBfIIek/JUH2q6cKhz6L/WNhCOk7YdyGW6bWQfyrTqpAiEA5jl8atczWPehWxb6HQ81NU7bNkuIZFVqLXFcXUPhZkMCIQCuy1qqlFV6jNKQQu3/7sP5FlJRGFc7dTOXzoEahQw7vQIhAMGbLEcI7HRv4rCglPslE+DdjLmPMYb20ZCfmUYRVJbTAiBfM9UzGU2hTPgWqM2WWwaUMYsuLjK86K3sHgaFWjf6hQIgFC/AjxeluM75BGc+qCdR7umBVvXZI+V0TRqQu1ZIs9U=";

    /**
     * 密钥长度 于原文长度对应 以及越长速度越慢
     */
    private final static int KEY_SIZE = 512;
    /**
     * 用于封装随机产生的公钥与私钥
     */
    private static Map<Integer, String> keyMap = new HashMap<Integer, String>();

    /**
     * 随机生成密钥对
     */
    public static void genKeyPair() throws NoSuchAlgorithmException, IOException {
        // KeyPairGenerator类用于生成公钥和私钥对，基于RSA算法生成对象
        KeyPairGenerator keyPairGen = KeyPairGenerator.getInstance("RSA");
        // 初始化密钥对生成器
        keyPairGen.initialize(KEY_SIZE, new SecureRandom());
        // 生成一个密钥对，保存在keyPair中
        KeyPair keyPair = keyPairGen.generateKeyPair();
        // 得到私钥
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
        // 得到公钥
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
        String publicKeyString = Base64.encodeBase64String(publicKey.getEncoded());
        // 得到私钥字符串
        String privateKeyString = Base64.encodeBase64String(privateKey.getEncoded());
        // 将公钥和私钥保存到Map
        //0表示公钥
        keyMap.put(0, publicKeyString);
        //1表示私钥
        keyMap.put(1, privateKeyString);
        OutputStreamWriter osw = new OutputStreamWriter(new FileOutputStream("PubKey.key"));
        osw.write(publicKeyString);
        osw.flush();
        osw.close();
        OutputStreamWriter osw2 = new OutputStreamWriter(new FileOutputStream("PriKey.key"));
        osw2.write(privateKeyString);
        osw2.flush();
        osw2.close();
    }



    /**
     * RSA公钥加密
     *
     * @param str       加密字符串
     * @param publicKey 公钥
     * @return 密文
     * @throws Exception 加密过程中的异常信息
     */
    public static String encrypt(String str, String publicKey) throws Exception {
        //base64编码的公钥
        byte[] decoded = Base64.decodeBase64(publicKey);
        RSAPublicKey pubKey = (RSAPublicKey) KeyFactory.getInstance(TYPE).generatePublic(new X509EncodedKeySpec(decoded));
        //RSA加密
        Cipher cipher = Cipher.getInstance(TYPE);
        cipher.init(Cipher.ENCRYPT_MODE, pubKey);
        String outStr = Base64.encodeBase64String(cipher.doFinal(str.getBytes(CHARSET_NAME)));
        return outStr;
    }

    /**
     * RSA私钥解密
     *
     * @param str        加密字符串
     * @param privateKey 私钥
     * @return 铭文
     * @throws Exception 解密过程中的异常信息
     */
    public static String decrypt(String str, String privateKey) throws Exception {
        //64位解码加密后的字符串
        byte[] inputByte = Base64.decodeBase64(str.getBytes(CHARSET_NAME));
        //base64编码的私钥
        byte[] decoded = Base64.decodeBase64(privateKey);
        RSAPrivateKey priKey = (RSAPrivateKey) KeyFactory.getInstance(TYPE).generatePrivate(new PKCS8EncodedKeySpec(decoded));
        //RSA解密
        Cipher cipher = Cipher.getInstance(TYPE);
        cipher.init(Cipher.DECRYPT_MODE, priKey);
        String outStr = new String(cipher.doFinal(inputByte));
        return outStr;
    }
    public static void main(String[] args) throws Exception {
//        生成公钥私钥
//        genKeyPair();



        String publicKey = "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJ0x86/kTdYE72P9Ery5W0B63BN3U5OtWTI20BwA/6BNQj2yAtbw6KvdYx1B6br8WFaJrj7vkn/hvfQd/6od8HcCAwEAAQ==";
        String privateKey = "MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAnTHzr+RN1gTvY/0SvLlbQHrcE3dTk61ZMjbQHAD/oE1CPbIC1vDoq91jHUHpuvxYVomuPu+Sf+G99B3/qh3wdwIDAQABAkBKq37BL6bVohSBH6b15Op2z6WYzZby0GZNVwI2OzcyUqBfIIek/JUH2q6cKhz6L/WNhCOk7YdyGW6bWQfyrTqpAiEA5jl8atczWPehWxb6HQ81NU7bNkuIZFVqLXFcXUPhZkMCIQCuy1qqlFV6jNKQQu3/7sP5FlJRGFc7dTOXzoEahQw7vQIhAMGbLEcI7HRv4rCglPslE+DdjLmPMYb20ZCfmUYRVJbTAiBfM9UzGU2hTPgWqM2WWwaUMYsuLjK86K3sHgaFWjf6hQIgFC/AjxeluM75BGc+qCdR7umBVvXZI+V0TRqQu1ZIs9U=";
        String text = "hello world";
        String encodeText = encrypt(text, publicKey);
        System.out.println("加密后：" + encodeText);
        System.out.println("解密后：" + decrypt(encodeText, privateKey));
    }


}
