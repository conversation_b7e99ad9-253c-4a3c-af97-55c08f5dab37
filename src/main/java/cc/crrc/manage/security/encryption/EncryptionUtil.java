package cc.crrc.manage.security.encryption;


import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;
import java.security.SecureRandom;
import java.util.Base64;


/**
 * @projectName: guiyang2
 * @package: cc.crrc.manage.security.encryption
 * @className: DESUtil
 * @author: lixin
 * @description: TODO
 * @date: 2022/6/9 14:41
 * @version: 1.0
 */
public class EncryptionUtil {


    public static final String UNICODE = "utf-8";
    public static final String KEY = "jszcszkj";
    private final static byte[] buffer = {'j', 's', 'z', 'c', 's', 'z', 'k', 'j'};
    private static final String KEY_TYPE_AES = "AES";
    private static final String KEY_TYPE_DES = "DES";


    public static Key setKey(String strKey) {
        Key key = null;
        try {

            SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
            random.setSeed(strKey.getBytes());

            KeyGenerator generator = KeyGenerator.getInstance(KEY_TYPE_DES);
            generator.init(128, random); // 根据参数生成key
            key = generator.generateKey();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return key;
    }

    /**
     * @param source  编码内容
     * @param key     密钥
     * @param charSet 编码格式
     * @return
     */
    public static String encrypt(String source, String key, String charSet) {
        String encrypt = null;
        try {
            byte[] ret = encrypt(source.getBytes(charSet), key);
            encrypt = new String(Base64.getEncoder().encode(ret));
        } catch (Exception e) {
            e.printStackTrace();
            encrypt = null;
        }
        return encrypt;
    }

    /**
     * @param encryptedData 解码内容
     * @param key           密钥
     * @param charSet       编码格式
     * @return
     */
    public static String decrypt(String encryptedData, String key, String charSet) {
        String descryptedData = null;
        try {
            byte[] ret = descrypt(Base64.getDecoder().decode(encryptedData.getBytes()), key);
            descryptedData = new String(ret, charSet);
        } catch (Exception e) {
            e.printStackTrace();
            descryptedData = null;
        }
        return descryptedData;
    }

    private static byte[] encrypt(byte[] primaryData, String key) {
        try {
            byte[] raw = key.getBytes("UTF-8");
            SecretKey keyByte = new SecretKeySpec(raw, KEY_TYPE_DES);

            Cipher cipher = Cipher.getInstance(KEY_TYPE_DES); // Cipher对象实际完成加密操作
            cipher.init(Cipher.ENCRYPT_MODE, keyByte); // 用密钥初始化Cipher对象(加密)
            return cipher.doFinal(primaryData);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private static byte[] descrypt(byte[] encryptedData, String key) {
        try {
            byte[] raw = key.getBytes("UTF-8");
            SecretKey keyByte = new SecretKeySpec(raw, KEY_TYPE_DES);

            Cipher cipher = Cipher.getInstance(KEY_TYPE_DES); // Cipher对象实际完成解密操作
            cipher.init(Cipher.DECRYPT_MODE, keyByte); // 用密钥初始化Cipher对象(解密)
            return cipher.doFinal(encryptedData);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static void main(String[] args) {
        String code = "hello world";
        String key = "jszcszkj";
        String unicode = "utf-8";
        String encrypt = encrypt(code, key, unicode);
        String decrypt = decrypt(encrypt, key, unicode);
        System.out.println("原内容：" + code);
        System.out.println("加密：" + encrypt);
        System.out.println("解密：" + decrypt);
    }

}
