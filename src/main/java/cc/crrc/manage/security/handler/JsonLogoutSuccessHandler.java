package cc.crrc.manage.security.handler;

import cc.crrc.manage.common.response.Result;
import cc.crrc.manage.common.utils.Constants;
import cc.crrc.manage.common.utils.WebUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @FileName JsonLogoutSuccessHandler
 * <AUTHOR> shua<PERSON>
 * @Date 2019/9/25 13:07
 **/
public class JsonLogoutSuccessHandler implements LogoutSuccessHandler {
    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response,
                                Authentication authentication) throws IOException, ServletException {
        WebUtils.write(response, Result.builder().success(Constants.SUCCESS).build());
    }
}
