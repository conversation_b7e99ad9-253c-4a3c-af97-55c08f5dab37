package cc.crrc.manage.security.handler;

import cc.crrc.manage.common.annotation.log.SystemLogEnum;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.response.Result;
import cc.crrc.manage.common.utils.*;
import cc.crrc.manage.pojo.SysLogDTO;
import cc.crrc.manage.service.SysLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;

/**
 * @FileName FormLoginAuthenticationFailureHandler
 * <AUTHOR> shuangquan
 * @Date 2019/9/24 9:00
 **/
@Component
@Lazy(value = false)
public class FormLoginAuthenticationFailureHandler implements AuthenticationFailureHandler {
    private final static String REDIS_USER_LOG_KEY = "%s:%s";

    private final static Logger logger = LoggerFactory.getLogger(FormLoginAuthenticationFailureHandler.class);
    @Value("${login.bad.user.count}")
    private int userMaxCount;
    @Value("${login.bad.user.interval}")
    private int userBadInterval;
    @Value("${login.bad.ip.count}")
    private int ipMaxCount;
    @Value("${login.bad.ip.interval}")
    private int ipBadInterval;
    @Autowired
    private SysLogService sysLogService;

    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
                                        AuthenticationException exception) throws IOException, ServletException {
        logger.warn("Authentication Failure");
        /*try {
            log(request, exception);
        } catch (RuntimeException e) {
            logger.error("Login log record error {}", e.getMessage());
        }*/
        writeFailureCount(request);
        WebUtils.write(response,
                Result.builder().success(Constants.FAILURE).error(ExceptionInfoEnum.AUTHENTICATION_EXCEPTION.getErrorCode(),
                        Constants.BAD_CREDENTIALS_MESSAGE).build());
    }

    private void writeFailureCount(HttpServletRequest request){
        String username = request.getParameter(Constants.USER_NAME);
        String failureKey = String.format(Constants.REDIS_USER_FAILURE_COUNT_KEY,username);
        Object value = RedisUtils.get(failureKey);
        if (value != null) {
            RedisUtils.set(failureKey, (int) value+1, userBadInterval);
        } else {
            RedisUtils.set(failureKey, 1, userBadInterval);
        }
    }

    private void log(HttpServletRequest request, AuthenticationException exception) {
        String ip = WebUtils.getIpAddress(request);
        boolean flag = false;
        String username = null;
        String message = null;
        if (exception instanceof UsernameNotFoundException) {
            if (count(ip, ipBadInterval) >= ipMaxCount) {
                flag = true;
                message = String.format(Constants.IP_BAD_ACCESS_MESSAGE, ipMaxCount);
                clear(ip);
            }
        } else {
            username = request.getParameter("username");
            String userkey = String.format(REDIS_USER_LOG_KEY, ip, username);
            if (count(userkey, userBadInterval) >= userMaxCount) {
                flag = true;
                message = String.format(Constants.USER_BAD_ACCESS_MESSAGE, userMaxCount);
                clear(userkey);
            }
        }
        if (flag) {
            String userAgent = WebUtils.getUserAgent(request);
            SysLogDTO dto = build(ip, username, message, userAgent);
            dto.setException(String.format(Constants.USER_BAD_ACCESS_MESSAGE, ipMaxCount));
            sysLogService.insertLog(dto);
        }
    }

    private void clear(String key) {
        RedisUtils.del(key);
    }

    private SysLogDTO build(String ip, String username, String message, String userAgent) {

        Date date = new Date();

        SysLogDTO dto = new SysLogDTO();
        // 雪花算法添加主键
        dto.setId(PrimaryKeyGenerator.generatorId());
        dto.setIp(ip);
        dto.setOpt(Constants.USER_LOGIN_MESSAGE);
        dto.setStatus("0");
        dto.setUri(Constants.LOGIN);
        dto.setUserAgent(userAgent);
        dto.setException(message);
        dto.setUserName(username);
        dto.setUserId(UserUtils.getUserId());
        dto.setName(UserUtils.getName());
        dto.setRequestTime(date);
        dto.setOptType(SystemLogEnum.LOGIN.value());
        return dto;
    }

    private int count(String key, long expire) {
        Object value = RedisUtils.get(key);
        int count;
        if (value != null) {
            count = (int) value;
            count++;
            RedisUtils.set(key, count, RedisUtils.getExpire(key));
        } else {
            RedisUtils.set(key, 1, expire);
            count = 1;
        }
        return count;
    }
}
