package cc.crrc.manage.security.handler;

import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class FooWebSocketHandler extends TextWebSocketHandler {

    private final static List<WebSocketSession> sessions = new ArrayList<>();

    public static List<WebSocketSession> allSessions(){
        return new ArrayList<>(sessions);
    }


    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        //在这里自定义消息处理...
    }

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        
        //保存所有会话
        sessions.add(session);
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {

        if(!sessions.isEmpty()){
            // remove session after closed
            for (Iterator<WebSocketSession> iterator = sessions.iterator(); iterator.hasNext();){

                WebSocketSession s = iterator.next();

                if(session.getId().equals(s.getId())){
                    iterator.remove();
                }
            }
        }
    }
}