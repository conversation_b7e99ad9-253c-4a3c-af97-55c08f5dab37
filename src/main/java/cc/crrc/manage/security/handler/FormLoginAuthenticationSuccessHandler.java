package cc.crrc.manage.security.handler;

import cc.crrc.manage.common.response.Result;
import cc.crrc.manage.common.utils.*;
import cc.crrc.manage.pojo.SysLogDTO;
import cc.crrc.manage.security.JwtUtils;
import cc.crrc.manage.security.SecurityProperties;
import cc.crrc.manage.security.UserDetail;
import cc.crrc.manage.service.SysLogService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @FileName FormLoginAuthenticationSuccessHandler
 * <AUTHOR> shuangquan
 * @Date 2019/9/24 8:55
 **/
@Component
@Lazy(value = false)
public class FormLoginAuthenticationSuccessHandler implements AuthenticationSuccessHandler {

    private final static Logger logger = LoggerFactory.getLogger(FormLoginAuthenticationFailureHandler.class);

    @Autowired
    private SecurityProperties securityProperties;

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private SysLogService sysLogService;

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
                                        Authentication authentication) throws IOException, ServletException {
        logger.info("Authentication Success");
        String accessToken;
        String userKey = Constants.REDIS_USER_TOKEN_KEY_PREFIX.concat(String.valueOf(UserUtils.getUserId()));
        //String authorityKey = Constants.REDIS_USER_AUTHRITY_KEY_PREFIX.concat(String.valueOf(UserUtils.getUserId()));
        String ipAddress = WebUtils.getIpAddress(request);
        accessToken = (String) RedisUtils.get(userKey);
        if (!StringUtils.isEmpty(accessToken) && !jwtUtils.isTokenExpired(accessToken)
//                && ipAddress.equals(jwtUtils.getTokenIp(accessToken))
        ) {
            RedisUtils.expire(userKey, securityProperties.getSessionTimeout());
            //RedisUtils.expire(authorityKey, securityProperties.getSessionTimeout() + 10);
        } else {
            UserDetail user = UserUtils.getUser();
            //List<PathGrantedAuthority> pathGrantedAuthorityList = (List<PathGrantedAuthority>) user.getAuthorities();
            //String authorityJson = JsonUtils.serialize(pathGrantedAuthorityList);
            //user.setAuthorities(null);
            accessToken = jwtUtils.generateAccessToken(user, ipAddress);
            RedisUtils.set(userKey, accessToken, securityProperties.getSessionTimeout());
            //RedisUtils.set(authorityKey, authorityJson, securityProperties.getSessionTimeout() + 10);
        }
        Map<String, String> map = new HashMap<>(1);
        map.put(Constants.TOKEN_KEY, accessToken);
//        log(request);
        //登录成功删除错误次数累计
        RedisUtils.del(String.format(Constants.REDIS_USER_FAILURE_COUNT_KEY,UserUtils.getUsername()));
        WebUtils.write(response, Result.builder().success(Constants.SUCCESS).body(map).build());
    }

    private void log(HttpServletRequest request) {
        String ip = WebUtils.getIpAddress(request);
        String userAgent = WebUtils.getUserAgent(request);
        SysLogDTO dto = build(ip, userAgent);
        sysLogService.insertLog(dto);
    }

    private SysLogDTO build(String ip, String userAgent) {
        Date date = new Date();

        SysLogDTO dto = new SysLogDTO();
        // 雪花算法添加主键
        dto.setId(PrimaryKeyGenerator.generatorId());
        dto.setIp(ip);
        dto.setOpt(Constants.USER_LOGIN_MESSAGE);
        dto.setStatus("1");
        dto.setUri(Constants.LOGIN);
        dto.setUserAgent(userAgent);
        dto.setUserName(UserUtils.getUsername());
        dto.setUserId(UserUtils.getUserId());
        //dto.setOrganizationId(UserUtils.getOrganizationId());
        dto.setName(UserUtils.getName());
        dto.setRequestTime(date);
        dto.setRequestTime(date);
//        dto.setMenuId(Constants.LOGIN_MENU_ID);
//        dto.setMenuCode(Constants.LOGIN_MENU_CODE);
        return dto;
    }
}
