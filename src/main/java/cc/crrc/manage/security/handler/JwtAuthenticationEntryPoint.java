package cc.crrc.manage.security.handler;


import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.response.Result;
import cc.crrc.manage.common.utils.Constants;
import cc.crrc.manage.common.utils.WebUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;

@Component
@Lazy(value = false)
public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint, Serializable {
    private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationEntryPoint.class);
    private static final String LOGIN_EXCEPTION = "操作失败，请联系管理员";
    private static final long serialVersionUID = -8970718410437077606L;

    @Override
    public void commence(HttpServletRequest request,
                         HttpServletResponse response,
                         AuthenticationException authException) throws IOException {
        logger.warn(authException.getMessage());
        WebUtils.write(response,
                Result.builder().success(Constants.FAILURE).error(ExceptionInfoEnum.AUTHENTICATION_EXCEPTION.getErrorCode(), LOGIN_EXCEPTION).build());
    }
}
