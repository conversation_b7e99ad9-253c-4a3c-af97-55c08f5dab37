package cc.crrc.manage.security.handler;

import cc.crrc.manage.common.utils.Constants;
import cc.crrc.manage.common.utils.RedisUtils;
import cc.crrc.manage.security.JwtUtils;
import cc.crrc.manage.security.SecurityProperties;
import cc.crrc.manage.security.UserDetail;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutHandler;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @FileName OptionLogoutHandler
 * <AUTHOR> shuangquan
 * @Date 2019/9/25 13:13
 **/
@Component
public class OptionLogoutHandler implements LogoutHandler {
    private final static Logger logger = LoggerFactory.getLogger(OptionLogoutHandler.class);
    @Autowired
    private SecurityProperties securityProperties;
    @Autowired
    private JwtUtils jwtUtils;

    @Override
    public void logout(HttpServletRequest request, HttpServletResponse response, Authentication authentication) {
        try {
            String accessToken = request.getHeader(securityProperties.getHeader());
            UserDetail user = jwtUtils.getUserFromToken(accessToken);
            RedisUtils.del(Constants.REDIS_USER_TOKEN_KEY_PREFIX.concat(String.valueOf(user.getId())));
        } catch (Exception e) {
            logger.error("Logout error: {}", e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }
}
