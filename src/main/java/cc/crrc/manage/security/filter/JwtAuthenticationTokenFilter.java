package cc.crrc.manage.security.filter;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.response.Result;
import cc.crrc.manage.common.utils.Constants;
import cc.crrc.manage.common.utils.RedisUtils;
import cc.crrc.manage.common.utils.WebUtils;
import cc.crrc.manage.security.JwtUtils;
import cc.crrc.manage.security.SecurityProperties;
import cc.crrc.manage.security.UserDetail;
import io.jsonwebtoken.JwtException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;


public class JwtAuthenticationTokenFilter extends AuthenticationFilter {
    private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationTokenFilter.class);
    private final static Map<String, String> message = new HashMap<>();
    private final static String TOKEN_INVALID_KEY = "token_invalid_message_";
    private final static String TOKEN_PARAMETER = "access_token";

    private SecurityProperties securityProperties;

    private JwtUtils jwtUtils;

    public JwtAuthenticationTokenFilter(SecurityProperties securityProperties, JwtUtils jwtUtils) {
        this.jwtUtils = jwtUtils;
        this.securityProperties = securityProperties;
        message.put("token_invalid_message_1", "登录已过期，请重新登录！");
        message.put("token_invalid_message_2", "登录已超时或已退出，请重新登录！");
        message.put("token_invalid_message_3", "无效认证，请重新登录！");
        message.put("token_invalid_message_4", "账号已在其他机器登录，请重新登录！");
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException {
        if (Constants.LOGIN.equals(WebUtils.getRealPath(request))) {
            Object value = RedisUtils.get(String.format(Constants.REDIS_USER_FAILURE_COUNT_KEY,request.getParameter(Constants.USER_NAME)));
            if(value!=null && (int)value>=Constants.USER_FAILURE_MAX){
                WebUtils.write(response,
                        Result.builder().success(Constants.FAILURE).error(ExceptionInfoEnum.AUTHENTICATION_EXCEPTION.getErrorCode(),Constants.USER_LOCK_MESSAGE).build());
            }else {
                chain.doFilter(request, response);
            }
        } else {
            String auth_token = request.getParameter(TOKEN_PARAMETER);
            if (StringUtils.isEmpty(auth_token)) {
                auth_token = request.getHeader(securityProperties.getHeader());
            }
            if (!StringUtils.isEmpty(auth_token)) {
                try {
                    String ipAddress = WebUtils.getIpAddress(request);
                    logger.info("Access Ip：{}", ipAddress);
                    int flag = checkToken(auth_token, ipAddress);
                    if (flag == 0) {
                        UserDetail userDetail = jwtUtils.getUserFromToken(auth_token);
                        UsernamePasswordAuthenticationToken authentication =
                                new UsernamePasswordAuthenticationToken(userDetail, null, userDetail.getAuthorities());
                        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                        SecurityContextHolder.getContext().setAuthentication(authentication);
                        chain.doFilter(request, response);
                    } else {
                        WebUtils.write(response,
                                Result.builder().success(Constants.FAILURE).error(ExceptionInfoEnum.AUTHENTICATION_EXCEPTION.getErrorCode(),
                                        message.get(TOKEN_INVALID_KEY.concat(String.valueOf(flag)))).build());
                    }
                } catch (JwtException | IllegalArgumentException e) {
                    WebUtils.write(response,
                            Result.builder().success(Constants.FAILURE).error(ExceptionInfoEnum.AUTHENTICATION_EXCEPTION.getErrorCode(),
                                    message.get(TOKEN_INVALID_KEY.concat(String.valueOf(Constants.NUMBER_THREE)))).build());
                }
            } else {
                WebUtils.write(response,
                        Result.builder().success(Constants.FAILURE).error(ExceptionInfoEnum.AUTHENTICATION_EXCEPTION.getErrorCode(), "请登录后访问！").build());
            }
        }

    }

    /**
     * @param token
     * @return 0-通过 1-过期 2-超时 3-token无效 4-IP地址不匹配
     */
    private int checkToken(String token, String ipAddress) throws JwtException, IllegalArgumentException {
        if (jwtUtils.isTokenExpired(token)) {
            return Constants.NUMBER_ONE;
        }
        UserDetail user = jwtUtils.getUserFromToken(token);
        String userId = String.valueOf(user.getId());
        String userKey = Constants.REDIS_USER_TOKEN_KEY_PREFIX.concat(userId);
        //String authorityKey = Constants.REDIS_USER_AUTHRITY_KEY_PREFIX.concat(userId);
        String redisToken = (String) RedisUtils.get(userKey);
        if (redisToken == null) {
            return Constants.NUMBER_TWO;
        }
       /* if (!ipAddress.equals(jwtUtils.getTokenIp(redisToken))) {
            return Constants.NUMBER_FOUR;
        }*/
        if (!token.equals(redisToken)) {
            return Constants.NUMBER_THREE;
        }
        RedisUtils.expire(userKey, securityProperties.getSessionTimeout());
        //RedisUtils.expire(authorityKey, securityProperties.getSessionTimeout() + 10);
        return Constants.NUMBER_ZERO;
    }

}
