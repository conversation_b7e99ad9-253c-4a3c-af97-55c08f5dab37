package cc.crrc.manage.security.filter;

import cc.crrc.manage.security.UserDetail;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @FileName DevelopAuthenticationTokenFilter
 * <AUTHOR> shuangquan
 * @Date 2019/9/26 16:00
 **/
//@Component
//@Profile("dev")
public class DevelopAuthenticationTokenFilter extends AuthenticationFilter {
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
                                    Fi<PERSON><PERSON>hain filterChain) throws ServletException, IOException {
        String userId = request.getParameter("userdId");
        UserDetail userDetail = null;
        if (StringUtils.isEmpty(userId)) {
            userDetail = new UserDetail();
            userDetail.setId("0");
            userDetail.setUsername("admin");
            userDetail.setName("admin");
            //userDetail.setOrganizationId(0L);
            userDetail.setIsSuperAdmin("1");
        }
        UsernamePasswordAuthenticationToken authentication =
                new UsernamePasswordAuthenticationToken(userDetail, null, userDetail.getAuthorities());
        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
        SecurityContextHolder.getContext().setAuthentication(authentication);
        filterChain.doFilter(request, response);
    }
}
