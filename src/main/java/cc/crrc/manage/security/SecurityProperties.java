package cc.crrc.manage.security;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @FileName SecurityProperties
 * <AUTHOR> shuangquan
 * @Date 2019/9/25 9:50
 **/
@ConfigurationProperties(prefix = "security")
@Configuration
public class SecurityProperties {
    private String header;
    private String secret;
    private long expiration;
    private long sessionTimeout;
    private String[] ignoreUrl;

    public String getHeader() {
        return header;
    }

    public void setHeader(String header) {
        this.header = header;
    }

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public long getExpiration() {
        return expiration;
    }

    public void setExpiration(long expiration) {
        this.expiration = expiration;
    }

    public long getSessionTimeout() {
        return sessionTimeout;
    }

    public void setSessionTimeout(long sessionTimeout) {
        this.sessionTimeout = sessionTimeout;
    }

    public String[] getIgnoreUrl() {
        return ignoreUrl;
    }

    public void setIgnoreUrl(String[] ignoreUrl) {
        this.ignoreUrl = ignoreUrl;
    }
}
