package cc.crrc.manage.mq;

import cc.crrc.manage.common.utils.SpringBeanUtils;
import cc.crrc.manage.mq.process.QueueProcesser;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.core.ChannelAwareMessageListener;

/**
 * @FileName RabbitmqChannelListener
 * <AUTHOR> shuangquan
 * @Date 2019/12/5 17:20
 **/
public class RabbitmqChannelListener implements ChannelAwareMessageListener {
    private static final String PROCESSER_SUFFIX = "_processer";
    private static final Logger logger = LoggerFactory.getLogger(RabbitmqChannelListener.class);

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String queueName = message.getMessageProperties().getConsumerQueue();
        String beanName = queueName.concat(PROCESSER_SUFFIX);
        QueueProcesser processer = findProcesser(message);
        if (processer == null) {
            logger.warn("Not found Processer Bean [{}]", beanName);
            return;
        }
        processer.process(message.getBody(), channel);
    }

    private QueueProcesser findProcesser(Message message) {
        String queueName = message.getMessageProperties().getConsumerQueue();
        String beanName = queueName.concat(PROCESSER_SUFFIX);
        QueueProcesser processer = SpringBeanUtils.getBean(beanName);
        return processer;
    }
}
