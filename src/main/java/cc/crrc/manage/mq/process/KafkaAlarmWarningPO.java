package cc.crrc.manage.mq.process;


import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

public class KafkaAlarmWarningPO {

    //正线值
    private static final String MAIN_TRACK_VALUE = "2";

    //主键id
    private String id;
    //对应车辆编号
    private String assetCode;
    //故障编码
    private String faultType;
    //操作类型 fix修改  new 新增
    private String action;
    //时间(新增为startTime值，修改为endTime值)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date date;
    private JSONObject params;
    private String paramStr;
    //0：故障  1：预警
    private String faultSource;
    //机理规则id
    private String alarmRuleId;
    //机理故障快照
    private JSONObject snapshot;
    private String snapshotStr;
    //车辆id
    private String vehicleId;
    //线路id
    private String lineId;
    //车辆类型id
    private String vehicleTypeId;
    //构型id
    private String componentId;
    //处理状态
    private String status;
    //故障中文名
    private String faultNameCn;
    //故障英文名
    private String faultNameEn;
    //构型编码
    private String vehicleStructureCode;
    //故障系统
    private String subsystem;
    //故障位置
    private String location;
    //故障编码
    private String faultCode;
    //故障等级
    private Integer faultLevel;
    //解除状态 0未解除 1解除
    private Integer endStatus;
    //正线true 非正线false
    private Boolean runningStatus;
    //大数据发来的，不知道什么作用
    private Date currentTime;
    //大数据发来的，yyMMddHHmmss格式时间
    private String time;
    //信号id
    private String signalId;

    private Boolean closeStatue;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAssetCode() {
        return assetCode;
    }

    public void setAssetCode(String assetCode) {
        this.assetCode = assetCode;
    }

    public String getFaultType() {
        return faultType;
    }

    public void setFaultType(String faultType) {
        this.faultType = faultType;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public JSONObject getParams() {
        return params;
    }

    public void setParams(JSONObject params) {
        this.params = params;
    }

    public String getFaultSource() {
        return faultSource;
    }

    public void setFaultSource(String faultSource) {
        this.faultSource = faultSource;
    }

    public String getAlarmRuleId() {
        return alarmRuleId;
    }

    public void setAlarmRuleId(String alarmRuleId) {
        this.alarmRuleId = alarmRuleId;
    }

    public JSONObject getSnapshot() {
        return snapshot;
    }

    public void setSnapshot(JSONObject snapshot) {
        this.snapshot = snapshot;
    }

    public String getVehicleId() {
        return vehicleId;
    }

    public void setVehicleId(String vehicleId) {
        this.vehicleId = vehicleId;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getVehicleTypeId() {
        return vehicleTypeId;
    }

    public void setVehicleTypeId(String vehicleTypeId) {
        this.vehicleTypeId = vehicleTypeId;
    }

    public String getComponentId() {
        return componentId;
    }

    public void setComponentId(String componentId) {
        this.componentId = componentId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getFaultNameCn() {
        return faultNameCn;
    }

    public void setFaultNameCn(String faultNameCn) {
        this.faultNameCn = faultNameCn;
    }

    public String getFaultNameEn() {
        return faultNameEn;
    }

    public void setFaultNameEn(String faultNameEn) {
        this.faultNameEn = faultNameEn;
    }

    public String getVehicleStructureCode() {
        return vehicleStructureCode;
    }

    public void setVehicleStructureCode(String vehicleStructureCode) {
        this.vehicleStructureCode = vehicleStructureCode;
    }

    public String getSubsystem() {
        return subsystem;
    }

    public void setSubsystem(String subsystem) {
        this.subsystem = subsystem;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getFaultCode() {
        return faultCode;
    }

    public void setFaultCode(String faultCode) {
        this.faultCode = faultCode;
    }

    public Integer getFaultLevel() {
        return faultLevel;
    }

    public void setFaultLevel(Integer faultLevel) {
        this.faultLevel = faultLevel;
    }

    public Integer getEndStatus() {
        return endStatus;
    }

    public void setEndStatus(Integer endStatus) {
        this.endStatus = endStatus;
    }

    public String getParamStr() {
        return paramStr;
    }

    public void setParamStr(String paramStr) {
        this.paramStr = paramStr;
    }

    public String getSnapshotStr() {
        return snapshotStr;
    }

    public void setSnapshotStr(String snapshotStr) {
        this.snapshotStr = snapshotStr;
    }

    public Boolean getRunningStatus() {
        return runningStatus;
    }

    public void setRunningStatus(Boolean runningStatus) {
        this.runningStatus = runningStatus;
    }

    public Date getCurrentTime() {
        return currentTime;
    }

    public void setCurrentTime(Date currentTime) {
        this.currentTime = currentTime;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getSignalId() {
        return signalId;
    }

    public void setSignalId(String signalId) {
        this.signalId = signalId;
    }

    public Boolean getCloseStatue() {
        return closeStatue;
    }

    public void setCloseStatue(Boolean closeStatue) {
        this.closeStatue = closeStatue;
    }

    /**
     * 根据车辆实体类builder AlarmWarning实体类
     *
     * @param mtrVehiclePO
     * @return KafkaAlarmWarningPO
     */
    public KafkaAlarmWarningPO build(MtrVehiclePO mtrVehiclePO) {
        if (mtrVehiclePO == null) {
            return this;
        }
        this.setVehicleId(mtrVehiclePO.getId());
        this.setLineId(mtrVehiclePO.getMetroLineId());
        this.setVehicleTypeId(mtrVehiclePO.getVehicleTypeId());
        return this;
    }

    /**
     * 根据故障字典实体类builder AlarmWarning实体类
     *
     * @param ekbFaultTypeDTO
     * @return KafkaAlarmWarningPO
     */
    public KafkaAlarmWarningPO build(EkbFaultTypeDTO ekbFaultTypeDTO) {
        if (ekbFaultTypeDTO == null) {
            return this;
        }
        this.setFaultNameCn(ekbFaultTypeDTO.getNameCn());
        this.setFaultNameEn(ekbFaultTypeDTO.getNameEn());
        this.setComponentId(ekbFaultTypeDTO.getComponentId());
        this.setVehicleStructureCode(ekbFaultTypeDTO.getVehicleStructureCode());
        this.setSubsystem(ekbFaultTypeDTO.getSubsystem());
        this.setLocation(ekbFaultTypeDTO.getLocation());
        this.setFaultLevel(ekbFaultTypeDTO.getFaultLevel());
        this.setFaultCode(ekbFaultTypeDTO.getFaultCode());
        return this;
    }

    /**
     * builder AlarmWarning实体类 基础数据
     *
     * @return KafkaAlarmWarningPO
     */
    public KafkaAlarmWarningPO build() {
        //正线运行状态不可用，默认全部为正线运行
//        this.setRunningStatus(MAIN_TRACK_VALUE.equals(this.getParams().get("runningStatus")));
        this.setRunningStatus(true);
        this.setId(PrimaryKeyGenerator.generatorId());
        this.setStatus("0");
        this.setParamStr(params != null ? params.toJSONString() : null);
        this.setSnapshotStr(snapshot != null ? snapshot.toJSONString() : null);
        return this;
    }

}
