package cc.crrc.manage.mq.process;

import cc.crrc.manage.cache.CacheUtils;
import cc.crrc.manage.cache.dict.DictCache;
import cc.crrc.manage.common.utils.JsonUtils;
import cc.crrc.manage.common.utils.LoggerUtils;
import cc.crrc.manage.common.utils.ThrowableUtil;
import cc.crrc.manage.pojo.SysDictVO;
import cc.crrc.manage.pojo.mtr.MtrSoftWarePO;
import cc.crrc.manage.service.mtr.software.MtrSoftWareService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class KafkaSoftWareVersionConsumer {

    private static final Logger logger = LoggerFactory.getLogger("software.version.update");
    public static final String AUTO_FLAG = "0";

    @Autowired
    private MtrSoftWareService softWareService;

    @KafkaListener(topics = "${kafkaTopicName}", groupId = "${spring.kafka.group_id}")
    public void listen(ConsumerRecord<?, ?> record, Consumer consumer) {
        logger.info(" partitions ={} ,topic = {}, offset = {}, value = {}", record.partition(), record.topic(), record.offset(), record.value());
        try {
            process((String) record.value());
        } catch (Exception e) {
            LoggerUtils.error(logger, ThrowableUtil.getStackTrace(e));
            logger.error(ThrowableUtil.getStackTrace(e));
        }
        consumer.commitAsync();
    }

    /**
     * kafka消息解析成实体类
     *
     * @param msg kafka消息
     */
    public void process(String msg) {
        MtrSoftWarePO mtrSoftWarePO = JsonUtils.parse(msg, MtrSoftWarePO.class);
        mtrSoftWarePO.setEffectiveTime(mtrSoftWarePO.getUpdateTime());
        mtrSoftWarePO.setName(mtrSoftWarePO.getSoftwareNameCn());
        mtrSoftWarePO.setSignalNameEn(mtrSoftWarePO.getSoftwareNameEn());
        processSoftWare(mtrSoftWarePO);
    }

    /**
     * 根据手动和自动类型处理软件版本更新策略
     *
     * @param mtrSoftWarePO
     */
    public void processSoftWare(MtrSoftWarePO mtrSoftWarePO) {
        List<SysDictVO> list = CacheUtils.getValue(DictCache.class, String.join("_", DictCache.SOFTWARE_MANUAL_FLAG, CacheUtils.METRO_LINE_ID, CacheUtils.VEHICLE_TYPE_ID));
        String value = CollectionUtils.isNotEmpty(list) ? list.get(0).getValue() : "0";
        if (AUTO_FLAG.equals(value)) {
            softWareService.updateSoftWareInfo(false, null, mtrSoftWarePO);
            logger.info("自动更新成功--{}", mtrSoftWarePO.toString());
        } else {
            softWareService.insertSoftwareManualHistory(mtrSoftWarePO);
            logger.info("手动历史插入成功--{}", mtrSoftWarePO);
        }
    }

}
