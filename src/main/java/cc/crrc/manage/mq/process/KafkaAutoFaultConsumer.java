package cc.crrc.manage.mq.process;


import cc.crrc.manage.common.utils.JsonUtils;
import cc.crrc.manage.common.utils.LoggerUtils;
import cc.crrc.manage.common.utils.ThrowableUtil;
import cc.crrc.manage.service.mtc.MtcAlarmWarningService;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.List;

/**
 * <AUTHOR> zhang
 */
@Component
public class KafkaAutoFaultConsumer {

    private static final Logger logger = LoggerFactory.getLogger(KafkaAutoFaultConsumer.class);
    private static final String FAULT_NEW_VALUE = "new";
    private static final String FAULT_FIX_VALUE = "fix";

    @KafkaListener(topics = "${spring.kafka.topic_id}", groupId = "${spring.kafka.group_id}")
    public void listen(List<ConsumerRecord<?, String>> records, Consumer consumer) {
        StopWatch sw = new StopWatch();
        logger.info("故障处理开始---------------------");
        for (ConsumerRecord<?, String> record : records) {
            long offset = record.offset();
            sw.start(String.valueOf(offset));
            logger.info("Offset {}---开始,", offset);
            String value = record.value();
            logger.info("Topic = {},Partition = {}, value = {}", record.topic(), record.partition(), value);
            KafkaAlarmWarningPO warningPO = JsonUtils.parse(value, KafkaAlarmWarningPO.class);
            boolean processResult = process(warningPO);
            if (processResult) {
                logger.error("Offset {}---异常, processResult {}", offset, processResult);
            }
            sw.stop();
            logger.info("Offset {}---结束，耗时{}ms", record.offset(), sw.getLastTaskTimeMillis());
        }
        logger.info("故障处理结束---------------------本次Poll {} 条，总耗时 {} ms", records.size(), sw.getTotalTimeMillis());
    }

    @Autowired
    MtcAlarmWarningService alarmWarningService;

    public boolean process(KafkaAlarmWarningPO warningPO) {
        try {
            if (FAULT_NEW_VALUE.equals(warningPO.getAction())) {
                alarmWarningService.insertAlarmWarning(warningPO);
            } else if (FAULT_FIX_VALUE.equals(warningPO.getAction())) {
                alarmWarningService.removeAlarmWarning(warningPO);
            }
            return false;
        } catch (Exception e) {
            LoggerUtils.error(logger, ThrowableUtil.getStackTrace(e));
            return true;
        }
    }

}
