package cc.crrc.manage.mq.process;

import cc.crrc.manage.common.utils.SpringBeanUtils;
import cc.crrc.manage.config.KafkaNuoLiConfiguration;
import cc.crrc.manage.service.external.TracksideOnlineMonitorService;
import com.alibaba.fastjson.JSONObject;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 诺丽轨旁检出数据
 * @Date 2023/8/23
 **/
@Component
public class KafkaCheckoutDataConsumer {

    private static final Logger logger = LoggerFactory.getLogger(KafkaCheckoutDataConsumer.class);
    private static final Object bean = SpringBeanUtils.getBean(KafkaNuoLiConfiguration.class);
    @Autowired
    TracksideOnlineMonitorService tracksideOnlineMonitorService;

    @KafkaListener(topics = "${spring.kafka.topic_id_cod}", groupId = "${spring.kafka.group_id_nuoli}", containerFactory = "secondKafkaListenerContainerFactory")
    public void listen(List<ConsumerRecord<?, String>> records) {
        StopWatch sw = new StopWatch();
        logger.info("检出数据处理开始---------------------");
        for (ConsumerRecord<?, String> record : records) {
            long offset = record.offset();
            sw.start(String.valueOf(offset));
            logger.info("Offset {}---开始,", offset);
            String value = record.value();
            logger.info("Topic = {},Partition = {}, value = {}", record.topic(), record.partition(), value);
            Boolean result = tracksideOnlineMonitorService.deviceCheckoutData(JSONObject.parseObject(value));
            if (!result) {
                logger.error("Offset {}---异常, processResult {}", offset, result);
            }
            sw.stop();
            logger.info("Offset {}---结束，耗时{}ms", record.offset(), sw.getLastTaskTimeMillis());
        }
        logger.info("检出数据处理结束---------------------本次Poll {} 条，总耗时 {} ms", records.size(), sw.getTotalTimeMillis());
    }

}
