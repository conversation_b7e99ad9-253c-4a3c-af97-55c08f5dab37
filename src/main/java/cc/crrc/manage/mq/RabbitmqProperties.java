package cc.crrc.manage.mq;

/**
 * @FileName RabbitmqProperties
 * <AUTHOR> shuangquan
 * @Date 2019/12/5 17:01
 **/
//@ConfigurationProperties(prefix = "rabbitmq")
//@Configuration
public class RabbitmqProperties {
    private String host;
    private int port;
    private String username;
    private String password;
    private Integer concurrentConsumers = 1;
    private Integer maxConsumers = 5;
    private String[] queueName;
    private Integer fetchCount = 1;

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getConcurrentConsumers() {
        return concurrentConsumers;
    }

    public void setConcurrentConsumers(Integer concurrentConsumers) {
        this.concurrentConsumers = concurrentConsumers;
    }

    public Integer getMaxConsumers() {
        return maxConsumers;
    }

    public void setMaxConsumers(Integer maxConsumers) {
        this.maxConsumers = maxConsumers;
    }

    public String[] getQueueName() {
        return queueName;
    }

    public void setQueueName(String[] queueName) {
        this.queueName = queueName;
    }

    public Integer getFetchCount() {
        return fetchCount;
    }

    public void setFetchCount(Integer fetchCount) {
        this.fetchCount = fetchCount;
    }
}
