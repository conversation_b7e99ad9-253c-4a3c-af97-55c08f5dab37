package cc.crrc.manage.service;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.SysDictMapping;
import cc.crrc.manage.mapper.SysDictTypeMapping;
import cc.crrc.manage.pojo.SysDictTypePO;
import cc.crrc.manage.pojo.SysDictVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;

/**
 * @FileName SysDictTypeService
 * <AUTHOR> yuxi
 * @Date 2019/10/16 11:13
 * @Version 1.0
 **/
@Service
public class SysDictTypeService {
    private final Logger logger = LoggerFactory.getLogger(SysDictTypeService.class);
    @Autowired
    private SysDictTypeMapping dictTypeMapping;
    @Autowired
    private SysDictMapping dictMapping;

    /**
     * @return java.util.List<cc.crrc.manage.pojo.SysDictTypePO>
     * @Description 根据类型名称筛选字典类型
     * <AUTHOR> yuxi
     * @Date 14:56 2019/10/16
     * @Param [name]
     **/
    public PageInfo<SysDictTypePO> listByType(SysDictTypePO dictType) {
        try {
            //分页
            int currentPage = dictType.getPageNumber();
            int pageSize = dictType.getPageSize();
            PageHelper.startPage(currentPage, pageSize);
            //获取数据
            List<SysDictTypePO> dictTypePOList = dictTypeMapping.listByType(dictType);
            return new PageInfo<>(dictTypePOList);
        } catch (DataAccessException e) {
            logger.error("Method[listByType] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @return java.util.List<cc.crrc.manage.pojo.SysDictTypePO>
     * @Description 更新字典类型
     * <AUTHOR> yuxi
     * @Date 11:33 2019/10/16
     * @Param []
     **/
    public int updateDictType(SysDictTypePO dictType) {
        try {
            dictType.setModifyBy(UserUtils.getUserId());
            return dictTypeMapping.updateDictType(dictType);
        } catch (DataAccessException e) {
            logger.error("Method[updateDictType] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    /**
     * @return java.util.List<cc.crrc.manage.pojo.SysDictTypePO>
     * @Description 删除字典类型
     * <AUTHOR> yuxi
     * @Date 11:33 2019/10/16
     * @Param []
     **/
    public int deleteDictType(String id) {
        try {
            // 判断该类型是否在字典数据中存在
            List<SysDictVO> dicts = dictMapping.listByTypeId(id);
            if (!CollectionUtils.isEmpty(dicts)) {
                throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION.getErrorCode(), "数据字典中引用了该类型，不允许删除！");
            }
            SysDictTypePO dictType = new SysDictTypePO();
            String userId = UserUtils.getUserId();
            Date date = new Date();
            Timestamp timestamp = new Timestamp(date.getTime());
            dictType.setId(id);
            dictType.setModifyBy(userId);
            dictType.setModifyTime(timestamp);
            dictType.setDelFlag("1");
            return dictTypeMapping.updateDictType(dictType);
        } catch (DataAccessException e) {
            logger.error("Method[deleteDictType] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    /**
     * @return java.util.List<cc.crrc.manage.pojo.SysDictTypePO>
     * @Description 新增字典类型
     * <AUTHOR> yuxi
     * @Date 11:34 2019/10/16
     * @Param []
     **/
    public int addDictType(SysDictTypePO dictType) {
        try {
            String userId = UserUtils.getUserId();
            dictType.setModifyBy(userId);
            dictType.setCreateBy(userId);
//            //雪花算法添加主键
//            dictType.setId(PrimaryKeyGenerator.generatorId());
            return dictTypeMapping.addDictType(dictType);
        } catch (DataAccessException e) {
            logger.error("Method[addDictType] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }
}
