package cc.crrc.manage.service.external;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.*;
import cc.crrc.manage.mapper.external.TracksideOnlineMonitorMapping;
import cc.crrc.manage.pojo.external.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.minio.MinioClient;
import io.minio.RemoveObjectArgs;
import io.minio.errors.*;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.util.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName TracksideOnlineMonitorService
 * @Description
 * <AUTHOR> kangjian
 * @Date 2023/8/18 10:45
 **/
@Service
public class TracksideOnlineMonitorService {

    private final Logger logger = LoggerFactory.getLogger(TracksideOnlineMonitorService.class);

    @Autowired
    private TracksideOnlineMonitorMapping tracksideOnlineMonitorMapping;

    private static final String BUCKET_NAME = "shaoxing2-nuoli";
    private static final String CONCAT_SYMBOL = "_";
    private static final String FILE_DOT = ".";
    private static final String PATH_SEP = "/";
    private static final String WHEELS = "wheels";
    private static final String PANTOGRAPH = "pantograph";

    @Transactional
    public Boolean deviceCheckoutData(JSONObject checkoutData) {
        try {
            JSONObject trainLogInfo = checkoutData.getJSONObject("trainLogInfo");
            TrainLogInfo info = new TrainLogInfo();
            String id = PrimaryKeyGenerator.generatorId();
            info.setId(id);
            info.setVehicleCode(trainLogInfo.getString("trainNO"));
            info.setTraceTime(trainLogInfo.getDate("tranceTime"));
            info.setTraceFile(trainLogInfo.getString("trace_file"));
            info.setStationCode(trainLogInfo.getInteger("stationCode"));
            info.setDirection(trainLogInfo.getInteger("direction"));
            tracksideOnlineMonitorMapping.insertTrainLogInfo(info);

            JSONArray checkOutData = checkoutData.getJSONArray("checkOutData");
            List<CheckoutData> dataList = JsonUtils.parseList(checkOutData.toJSONString(), CheckoutData.class);
            if (CollectionUtils.isNotEmpty(dataList)) {
                dataList.forEach(o -> {
                    o.setId(PrimaryKeyGenerator.generatorId());
                    o.setTrainPassInfoId(id);
                    if (o.getDeviceTypeCode() == 302) {
                        if (o.getPointTypeCode() == 6 || o.getPointTypeCode() == 9) {
                            o.setPartType("part_code_axle");
                        } else if (o.getPointTypeCode() == 10) {
                            o.setPartType("part_code_bogie");
                        } else if (o.getPointTypeCode() == 11) {
                            o.setPartType("part_code_carriage");
                        } else {
                            o.setPartType("part_code_wheel");
                        }
                    } else {
                        o.setPartType("part_code_pan");
                    }
                });
                tracksideOnlineMonitorMapping.batchInsertCheckoutData(dataList);
            }
            return true;
        } catch (Exception e) {
            LoggerUtils.error(logger, e);
            return false;
        }
    }

    @Transactional
    public Boolean deviceAlarmData(JSONObject alarmData) {
        try {
            JSONObject trainLogInfo = alarmData.getJSONObject("trainLogInfo");
            TrainLogInfo info = new TrainLogInfo();
            String id = PrimaryKeyGenerator.generatorId();
            info.setId(id);
            info.setVehicleCode(trainLogInfo.getString("trainNO"));
            info.setTraceTime(trainLogInfo.getDate("tranceTime"));
            info.setTraceFile(trainLogInfo.getString("trace_file"));
            info.setStationCode(trainLogInfo.getInteger("stationCode"));
            info.setDirection(trainLogInfo.getInteger("direction"));
            tracksideOnlineMonitorMapping.insertTrainLogInfo(info);

            JSONArray checkOutData = alarmData.getJSONArray("alarmData");
            List<CheckoutData> dataList = JsonUtils.parseList(checkOutData.toJSONString(), CheckoutData.class);
            if (CollectionUtils.isNotEmpty(dataList)) {
                dataList.forEach(o -> {
                    o.setId(PrimaryKeyGenerator.generatorId());
                    o.setTrainPassInfoId(id);
                    if (o.getDeviceTypeCode() == 302) {
                        if (o.getPointTypeCode() == 6 || o.getPointTypeCode() == 9) {
                            o.setPartType("part_code_axle");
                        } else if (o.getPointTypeCode() == 10) {
                            o.setPartType("part_code_bogie");
                        } else if (o.getPointTypeCode() == 11) {
                            o.setPartType("part_code_carriage");
                        } else {
                            o.setPartType("part_code_wheel");
                        }
                    } else {
                        o.setPartType("part_code_pan");
                    }
                });
                tracksideOnlineMonitorMapping.batchInsertCheckoutData(dataList);
            }
            return true;
        } catch (Exception e) {
            LoggerUtils.error(logger, e);
            return false;
        }
    }


    public Object listTrainLogInfo(TrainLogInfoDTO trainLogInfoDTO) {
        try {
            PageHelper.startPage(trainLogInfoDTO.getPageNum(), trainLogInfoDTO.getPageSize());
            List<TrainLogInfo> list = tracksideOnlineMonitorMapping.listTrainLogInfo(trainLogInfoDTO);
            return new PageInfo<>(list);
        } catch (Exception e) {
            logger.error("Method[listTrainLogInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object listWheelInfo(String id) {
        Map<Integer, List<CheckoutData>> result = new LinkedHashMap<>();
        LinkedList<Integer> list = new LinkedList<>();
        for (int i = 0; i < 32; i++) {
            list.add(i);
        }
        List<CheckoutData> wheelList = tracksideOnlineMonitorMapping.listCheckoutDataWheel(id);
        List<CheckoutData> axleList = tracksideOnlineMonitorMapping.listCheckoutDataAxle(id);
        List<CheckoutData> subList1 = axleList.subList(0, 16);
        List<CheckoutData> subList2 = axleList.subList(16, 32);
        List<CheckoutData> subList3 = axleList.subList(32, 40);
        List<CheckoutData> subList4 = axleList.subList(40, 44);
        List<CheckoutData> collect1 = subList1.stream().map(o -> Collections.nCopies(2, o)).flatMap(List::stream).collect(Collectors.toList());
        List<CheckoutData> collect2 = subList2.stream().map(o -> Collections.nCopies(2, o)).flatMap(List::stream).collect(Collectors.toList());
        List<CheckoutData> collect3 = subList3.stream().map(o -> Collections.nCopies(4, o)).flatMap(List::stream).collect(Collectors.toList());
        List<CheckoutData> collect4 = subList4.stream().map(o -> Collections.nCopies(8, o)).flatMap(List::stream).collect(Collectors.toList());
        list.forEach(o -> {
            List<CheckoutData> subList = wheelList.subList(o * 6, o * 6 + 6);
            ArrayList<CheckoutData> newArrayList = Lists.newArrayList(subList);
            newArrayList.add(collect1.get(o));
            newArrayList.add(collect2.get(o));
            newArrayList.add(collect3.get(o));
            newArrayList.add(collect4.get(o));
            result.put(o, newArrayList);
        });
        return result;
    }

    public Object wheelCheckPointDict() {
        return tracksideOnlineMonitorMapping.wheelCheckPointDict();
    }

    public Object panCheckPointDict() {
        return tracksideOnlineMonitorMapping.panCheckPointDict();
    }

    public Object listPanInfo(String id) {
        Map<String, List<CheckoutData>> result = new LinkedHashMap<>();
        List<CheckoutData> panList = tracksideOnlineMonitorMapping.listCheckoutDataPan(id);
        List<CheckoutData> pan1 = panList.subList(0, 14);
        List<CheckoutData> pan2 = panList.subList(14, 28);
        result.put("MP1", pan1);
        result.put("MP2", pan2);
        return result;
    }

    public Object wheelPartDict() {
        return tracksideOnlineMonitorMapping.wheelPartDict();
    }

    public Object listAlarmInfo(CheckoutAlarmDataDTO checkoutAlarmDataDTO) {
        try {
            PageHelper.startPage(checkoutAlarmDataDTO.getPageNumber(), checkoutAlarmDataDTO.getPageSize());
            List<CheckoutAlarmDataVO> list = tracksideOnlineMonitorMapping.listAlarmInfo(checkoutAlarmDataDTO);
            return new PageInfo<>(list);
        } catch (Exception e) {
            logger.error("Method[listAlarmInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }

    }

    public Object panPartDict() {
        return tracksideOnlineMonitorMapping.panPartDict();
    }

    public Object getWheelImages(String traceFile, String location) {
        List<String> components;
        if (StringUtils.isEmpty(location)) {
            components = tracksideOnlineMonitorMapping.getComponents();
        } else {
            components = tracksideOnlineMonitorMapping.getComponentsByLocation(location);
        }
        List<String> urlList = new ArrayList<>();
        try {
            MinioUtils minioUtils = new MinioUtils();
            List<String> list = new ArrayList<>();
            for (String component : components) {
                List<String> urls = minioUtils.listObjectNames(BUCKET_NAME, traceFile + PATH_SEP + WHEELS + CONCAT_SYMBOL + component+ PATH_SEP);
                list.addAll(urls);
            }
            if (CollectionUtils.isNotEmpty(list)) {
                for (String url : list) {
                    String decode = URLDecoder.decode(url, "UTF-8");
                    urlList.add(decode);
                }
                urlList = urlList.stream().distinct().collect(Collectors.toList());
            }
        } catch (Exception e) {
            logger.error("Method[getWheelImages] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
        return urlList;
    }

    public Object getPanImages(String traceFile) {
        List<String> components = tracksideOnlineMonitorMapping.getPantograph();
        List<String> urlList = new ArrayList<>();
        try {
            MinioUtils minioUtils = new MinioUtils();
            List<String> list = new ArrayList<>();
            for (String component : components) {
                List<String> urls = minioUtils.listObjectNames(BUCKET_NAME, traceFile + PATH_SEP + PANTOGRAPH + CONCAT_SYMBOL + component+ PATH_SEP);
                list.addAll(urls);
            }
            if (CollectionUtils.isNotEmpty(list)) {
                for (String url : list) {
                    String decode = URLDecoder.decode(url, "UTF-8");
                    urlList.add(decode);
                }
                urlList = urlList.stream().distinct().collect(Collectors.toList());
            }
        } catch (Exception e) {
            logger.error("Method[getPanImages] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
        return urlList;
    }


    public Object getAlarmWheelImages(String traceFile, String location, String content) {
        String components;
        if (StringUtils.isEmpty(location)) {
            return new ArrayList<>();
        } else {
            if (location.contains("轴") || location.contains("转")) {
                return new ArrayList<>();
            } else {
                components = tracksideOnlineMonitorMapping.getComponentsByLocationSecond(location);
            }
        }
        List<String> urlList = new ArrayList<>();
        try {
            MinioUtils minioUtils = new MinioUtils();

            List<String> urls = minioUtils.listObjectNames(BUCKET_NAME, traceFile + PATH_SEP + WHEELS + CONCAT_SYMBOL + components+ PATH_SEP);
            if (urls.size() > 0) {

                for (int i = 0; i < urls.size(); i++) {
                    if (urls.get(i).contains("ms") && content.contains("缘")) {
                        urlList.add(URLDecoder.decode(urls.get(i), "UTF-8"));
                    } else if (urls.get(i).contains("sf") && !content.contains("缘")) {
                        urlList.add(URLDecoder.decode(urls.get(i), "UTF-8"));
                    }
                }
            } else {
                return new ArrayList<>();
            }

        } catch (Exception e) {
            logger.error("Method[getAlarmWheelImages] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
        urlList = urlList.stream().distinct().collect(Collectors.toList());
        return urlList;
    }

    public Object getAlarmPanImages(String traceFile, String partCn) {
        String components = tracksideOnlineMonitorMapping.getPantographSecond(partCn);
        List<String> urlList = new ArrayList<>();
        try {
            MinioUtils minioUtils = new MinioUtils();

            List<String> urls = minioUtils.listObjectNames(BUCKET_NAME, traceFile + PATH_SEP + PANTOGRAPH + CONCAT_SYMBOL + components+ PATH_SEP);
            if (CollectionUtils.isNotEmpty(urls)) {
                for (String url : urls) {
                    String decode = URLDecoder.decode(url, "UTF-8");
                    urlList.add(decode);
                }
            } else {
                return new ArrayList<>();
            }

        } catch (Exception e) {
            logger.error("Method[getAlarmPanImages] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
        urlList = urlList.stream().distinct().collect(Collectors.toList());
        return urlList;
    }


    public Object locationDict() {
        return tracksideOnlineMonitorMapping.locationDict();
    }
}
