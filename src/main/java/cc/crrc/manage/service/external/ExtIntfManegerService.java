package cc.crrc.manage.service.external;


import cc.crrc.manage.service.SysDictService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

@Service
public class ExtIntfManegerService {
    private final Logger logger = LoggerFactory.getLogger(ExtIntfManegerService.class);
    private final static String ORIGINAL_KEY = "littleBastard";
    private final static String ENCRY_PROC = "DES";
    private final static byte[] buffer = {'j','s','z','c','s','z','k','j'};
    public Object addToken(String requester, String intfUrl) {

        return null;
    }

    //加密
    public static String encript(String clearText,String encryProc) throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException {
        Cipher cipher = Cipher.getInstance(encryProc);
        cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(buffer, encryProc));
        //用加密工具类对象对明文进行加密-->密文
        byte[] doFinal = cipher.doFinal(clearText.getBytes());
        //密文转换成base64编码，解决密文乱码问题
        byte[] encode = Base64.getEncoder().encode(doFinal);
        return new String(encode);
    }

    //解密
    public static String decript(String cipherText, String originKey,String encryProc) throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException {
        Cipher cipher = Cipher.getInstance("DES");
        cipher.init(Cipher.DECRYPT_MODE,new SecretKeySpec(buffer, encryProc));
        //注意：因为密文是用Base64转换后的，所以在doFinal以前要用Base64转一下
        byte[] decode = Base64.getDecoder().decode(cipherText);
        byte[] doFinal = cipher.doFinal(decode);
        return new String(doFinal);
    }

    public static void main(String[] args) throws Exception {
        long l = System.currentTimeMillis();
        String clearText = "db_interface_getdata";
        String cipherText = encript(clearText, ENCRY_PROC);
        System.out.println("cipherText = " + cipherText);
        String text = decript(cipherText, ORIGINAL_KEY, ENCRY_PROC);
        System.out.println("clearText = " + text);
        System.out.println("time = " + (System.currentTimeMillis() - l) + "ms");
    }

    //
}
