package cc.crrc.manage.service.draw;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UUIDUtils;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.SysDictMapping;
import cc.crrc.manage.mapper.draw.DrawLineConfigMapping;
import cc.crrc.manage.mapper.line.LineMapping;
import cc.crrc.manage.pojo.SysAreaVO;
import cc.crrc.manage.pojo.SysDictDTO;
import cc.crrc.manage.pojo.SysDictVO;
import cc.crrc.manage.pojo.draw.DrawLineConfigDTO;
import cc.crrc.manage.pojo.draw.DrawLineConfigVO;
import cc.crrc.manage.pojo.line.StationDTO;
import net.minidev.json.JSONObject;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


@Service
public class DrawLineConfigService {

    private final Logger logger = LoggerFactory.getLogger(DrawLineConfigService.class);
    @Autowired
    private DrawLineConfigMapping drawLineConfigMapping;
    @Autowired
    private LineMapping lineMapping;
    @Autowired
    private SysDictMapping sysDictMapping;


    public Object addDrawLine(DrawLineConfigDTO drawLineConfigDTO) {
        try{
            String userId = UserUtils.getUserId();
            //雪花算法添加主键
            drawLineConfigDTO.setId(PrimaryKeyGenerator.generatorId());
            drawLineConfigDTO.setDelFlag("0");
            drawLineConfigDTO.setCreateBy(userId);
            drawLineConfigDTO.setModifyBy(userId);
            return drawLineConfigMapping.addDrawLine(drawLineConfigDTO);
        }catch (Exception e){
            logger.error("Method[addDrawLine] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    public Object updateDrawLine(DrawLineConfigDTO drawLineConfigDTO) {
        try{
            String userId = UserUtils.getUserId();
            drawLineConfigDTO.setModifyBy(userId);
            return drawLineConfigMapping.updateDrawLine(drawLineConfigDTO);
        }catch (Exception e){
            logger.error("Method[updateDrawLine] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    public Object deleteDrawLine(String id) {
        try{
            DrawLineConfigDTO drawLineConfigDTO = new DrawLineConfigDTO();
            String userId = UserUtils.getUserId();
            drawLineConfigDTO.setModifyBy(userId);
            drawLineConfigDTO.setDelFlag("1");
            drawLineConfigDTO.setId(id);
            return drawLineConfigMapping.updateDrawLine(drawLineConfigDTO);
        }catch (Exception e){
            logger.error("Method[updateDrawLine] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    public Object getDrawLineInfoById(String id) {
        try{
            return drawLineConfigMapping.getDrawLineInfoById(id);
        }catch (Exception e){
            logger.error("Method[updateDrawLine] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object getDrawLineListForConfig(DrawLineConfigDTO drawLineConfigDTO) {
        try{
            return drawLineConfigMapping.getDrawLineListForConfig(drawLineConfigDTO);
        }catch (Exception e){
            logger.error("Method[getDrawLineListForConfig] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object getDrawLineListForMonitor(String lineId, String type) {
        try{
            //分别取上行和下行的数据拼在一起返回前端
            DrawLineConfigDTO up = new DrawLineConfigDTO();
            up.setLineId(lineId);
            up.setType(type);
            up.setDirection("up");
            DrawLineConfigDTO down  = new DrawLineConfigDTO();
            down.setLineId(lineId);
            down.setType(type);
            down.setDirection("down");
            List<DrawLineConfigVO> upList = drawLineConfigMapping.getDrawLineListForConfig(up);
            List<DrawLineConfigVO> downList = drawLineConfigMapping.getDrawLineListForConfig(down);
            JSONObject obj = new JSONObject();
            obj.put("up",upList);
            obj.put("down",downList);
            return obj;
        }catch (Exception e){
            logger.error("Method[getDrawLineListForConfig] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object copyDrawLine(String id) {
        try{
            DrawLineConfigVO vo =  drawLineConfigMapping.getDrawLineInfoById(id);
            if(null==vo){
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(),"数据复制异常！");
            }
            DrawLineConfigDTO dto = new DrawLineConfigDTO();
            dto.setLineId(vo.getLineId());
            dto.setDirection(vo.getDirection());
            dto.setType(vo.getType());
            if((UUIDUtils.generateShortUuid() +"_copy").length()>25){
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(),"区段id长度超过25！");
            }
            dto.setSegmentId(UUIDUtils.generateShortUuid() +"_copy");
            dto.setCurrentSta(vo.getCurrentSta());
            dto.setCoordinate(vo.getCoordinate());
            dto.setJson(vo.getJson());
            dto.setPath(vo.getPath());
            dto.setSort(vo.getSort());
            dto.setId(PrimaryKeyGenerator.generatorId());
            return addDrawLine(dto);
        }catch (Exception e){
            logger.error("Method[getDrawLineListForConfig] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION,e.getMessage());
        }
    }

    public Object getStaListForDrawLine(String lineId) {
        try{
            return lineMapping.getStationsForDrawLine(lineId);
        }catch (Exception e){
            logger.error("Method[getDrawLineListForConfig] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    @Transactional
    public Object copyDrawLineForInit(String lineId) {
        try{
            //第一步 查询该线路下的绘制区段类型
            String typeCode = "draw_line_type";
            SysDictDTO sysDictDTO = new SysDictDTO();
            sysDictDTO.setTypeCode(typeCode);
            sysDictDTO.setLineId(lineId);
            List<SysDictVO> dictList =sysDictMapping.listDictUniversal(sysDictDTO);

            //第二步 查询该线路下的车站
            StationDTO stationDTO = new StationDTO();
            stationDTO.setMetroLineId(lineId);
            List<StationDTO> stationList =lineMapping.getStations(stationDTO);
            String drawLineType = "";

            //第三步 将处理好的数据批量插入
            List<DrawLineConfigDTO> drawLineList = new ArrayList<>();

            for (SysDictVO sysDictVO:dictList){
                drawLineType = sysDictVO.getCode();
                int sort = 1;
                for(StationDTO stationInfo:stationList){
                    DrawLineConfigDTO  drawLineConfigDTO = new DrawLineConfigDTO();
                    String SegmentId = stationInfo.getStaId()+"-"+stationInfo.getDirection();

                    drawLineConfigDTO.setId(PrimaryKeyGenerator.generatorId());
                    drawLineConfigDTO.setSegmentId(SegmentId);
                    drawLineConfigDTO.setCurrentSta(stationInfo.getStaId());
                    drawLineConfigDTO.setCoordinate("1");
                    drawLineConfigDTO.setJson("{}");
                    drawLineConfigDTO.setPath("1");
                    drawLineConfigDTO.setDirection(stationInfo.getDirection());
                    drawLineConfigDTO.setLineId(lineId);
                    drawLineConfigDTO.setType(sysDictVO.getCode());
                    drawLineConfigDTO.setType(drawLineType);
                    drawLineConfigDTO.setSort(sort);
                    drawLineConfigDTO.setCreateBy(UserUtils.getUserId());
                    drawLineConfigDTO.setModifyBy(UserUtils.getUserId());
                    sort++;
                    drawLineList.add(drawLineConfigDTO);
                }
            }
            //数据插入
            int count = drawLineConfigMapping.batchInsert(drawLineList);

        }catch (Exception e){
            logger.error(e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(),"绘制区段初始化数据失败");
        }

        return "";
    }

    public List<DrawLineConfigVO> getDrawInfoList(String lineId,String drawLineType) {
        return drawLineConfigMapping.getDrawLineInfoList(lineId,drawLineType);
    }
}
