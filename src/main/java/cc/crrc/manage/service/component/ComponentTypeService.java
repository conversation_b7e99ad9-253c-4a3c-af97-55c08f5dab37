package cc.crrc.manage.service.component;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.component.ComponentTypeMapping;
import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.component.ComponentTypeDTO;
import cc.crrc.manage.pojo.component.ComponentTypeDetailVO;
import cc.crrc.manage.service.SysFileService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

import static cc.crrc.manage.common.utils.Constants.STRUCTURE_PICTURE_FILE_FORMAT;
import static cc.crrc.manage.common.utils.Constants.STRUCTURE_PICTURE_FILE_TYPE;

@Service
public class ComponentTypeService {
    private final Logger logger = LoggerFactory.getLogger(ComponentTypeService.class);
    @Autowired
    private ComponentTypeMapping comMapping;
    @Autowired
    private SysFileService sysFileService;

    /**
     * @return cc.crrc.manage.pojo.component.ComponentTypeDetailVO
     * @Description 根据id查询部件类型信息
     * <AUTHOR> yuxi
     * @Date 16:54 2019/12/14
     * @Param [id]
     **/
    public ComponentTypeDetailVO getComponentTypeById(String id) {
        return comMapping.getComponentTypeById(id, STRUCTURE_PICTURE_FILE_TYPE);
    }

    // 2020年8月14日 房明宽 增加 线路名称和车型名称 查询条件
    public PageInfo<ComponentTypeDTO> getComponentTypes(ComponentTypeDTO comDTO) {
        int currentPage = comDTO.getPageNumber();
        int pageSize = comDTO.getPageSize();
        PageHelper.startPage(currentPage, pageSize);
        List<ComponentTypeDTO> comTypes = comMapping.getComponentTypes(comDTO);
        return new PageInfo<>(comTypes);
    }

    // 2020年8月14日 房明宽 增加部件型号
    public Object insertComType(ComponentTypeDTO comDTO) {
        comDTO.setId(PrimaryKeyGenerator.generatorId());
        comDTO.setCreateBy(UserUtils.getUser().getId());
        int result = comMapping.insertComType(comDTO);
        if (result > 0) {
            return "SUCCESS";
        }
        throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
    }

    // 2020年8月14日 房明宽 相比之前多了车型id和线路id
    public Object updateComType(ComponentTypeDTO comDTO) {
        comDTO.setModifyBy(UserUtils.getUser().getId());
        ComponentTypeDetailVO componentTypeDetailVO = getComponentTypeById(comDTO.getId());
        if (!comDTO.getManufacturerId().equals(componentTypeDetailVO.getManufacturerId())) {
            comMapping.delEmployee(comDTO.getId());
        }
        int result = comMapping.updateComType(comDTO);
        if (result > 0) {
            return "SUCCESS";
        }
        throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
    }

    public Object delComType(String ids) {
        // TODO Auto-generated method stub
        String[] idsArr = ids.split(",");
//        Long[] idsList = new Long[idsArr.length];
//        for (int i = 0, len = idsArr.length; i < len; i++) {
//            idsList[i] = new Long(idsArr[i]);
//        }
        for (String id : idsArr) {
            int result = comMapping.delComType(id);
            if (result == 0) {
                throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
            }
        }
        return "SUCCESS";
    }

    @Transactional(rollbackFor = Exception.class)
    public Object assEmployee(ComponentTypeDTO comDTO) {
        // TODO Auto-generated method stub
        comMapping.delEmployee(comDTO.getId());
        try {
            for (String empId : comDTO.getEmployeeId()) {
                comMapping.assEmployee(comDTO.getId(), empId);
            }
        } catch (Exception e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
        return "SUCCESS";
    }

    public Object getEmployees(ComponentTypeDTO comDTO) {
        // TODO Auto-generated method stub
        return comMapping.getEmployees(comDTO);
    }

    /**
     * 保存部件类型的相关文件
     *
     * @param sysFile 文件信息
     * @param id      部件类型id
     * @return java.lang.Object
     * <AUTHOR> GuoYang
     * 2019/12/18
     **/
    @Transactional(rollbackFor = Exception.class)
    public void insertFile(SysFilePO sysFile, String id) {
        try {
            // 构型图片上传限制
            if (STRUCTURE_PICTURE_FILE_TYPE.equals(sysFile.getType())) {
                List<String> temp = Arrays.asList(STRUCTURE_PICTURE_FILE_FORMAT);
                if (!temp.contains(sysFile.getFormat().toLowerCase())) {
                    throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION, "不支持上传该图片类型！");
                }
                List<SysFilePO> sysFilePOList = selectlFile(id);
                for (SysFilePO sysFilePO : sysFilePOList) {
                    if (STRUCTURE_PICTURE_FILE_TYPE.equals(sysFilePO.getType()) && sysFilePO.getDelFlag() == 0) {
                        throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION, "不可上传多张同类型图片！");
                    }
                }
            }
            //文件属性存入 sysFile表
            String fileId = sysFileService.addSysFile(sysFile);
            int result = comMapping.saveFaultTypeFile(fileId, id);
            if (result == 0) {
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
            }
        } catch (DataAccessException e) {
            logger.error("Method[insertFile] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    public Object delFile(String id) {
        // TODO Auto-generated method stub
        try {
            sysFileService.deleteSysFile(id);
            comMapping.delFile(id);
        } catch (Exception e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
        return "SUCCESS";
    }

    public List<SysFilePO> selectlFile(String componentTypeId) {
        // TODO Auto-generated method stub
        return comMapping.selectlFile(componentTypeId);
    }

    public Object insertMtrComponentTypeContacts(String manufacturerEmployeeId, String componentTypeId) {
        // TODO Auto-generated method stub
        try {
            int count = comMapping.validCount(manufacturerEmployeeId, componentTypeId);
            if (count > 0) {
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "该雇员已存在");
            }
            comMapping.assEmployee(componentTypeId, manufacturerEmployeeId);
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
        return "SUCCESS";
    }

    public Object deleteMtrComponentTypeContacts(String manufacturerEmployeeId, String componentTypeId) {
        try {
            comMapping.deleteMtrComponentTypeContacts(manufacturerEmployeeId, componentTypeId);
        } catch (Exception e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
        return "SUCCESS";
    }

}
