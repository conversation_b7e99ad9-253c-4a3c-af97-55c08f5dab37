package cc.crrc.manage.service.component;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.component.ComponentMapping;
import cc.crrc.manage.mapper.mtr.StruVehicleStructureComponentMapping;
import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.component.ComponentDTO;
import cc.crrc.manage.pojo.component.ComponentDetailVO;
import cc.crrc.manage.service.SysFileService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static cc.crrc.manage.common.utils.Constants.STRUCTURE_PICTURE_FILE_TYPE;

@Service
public class ComponentService {
    private final Logger logger = LoggerFactory.getLogger(ComponentService.class);
    @Autowired
    private ComponentMapping comMapping;
    @Autowired
    private SysFileService sysFileService;
    @Autowired
    private StruVehicleStructureComponentMapping struVehicleStructureComponentMapping;

    public PageInfo<ComponentDTO> getComponents(ComponentDTO componentDTO) {
        int currentPage = componentDTO.getPageNumber();
        int pageSize = componentDTO.getPageSize();
        PageHelper.startPage(currentPage, pageSize);
        List<ComponentDTO> components = comMapping.getComponents(componentDTO);
        // TODO Auto-generated method stub
        return new PageInfo<>(components);
    }

    /**
     * 根据部件id，查询部件详细信息
     *
     * @param componentId 部件id
     * @return cc.crrc.manage.pojo.component.ComponentDetailVO
     * <AUTHOR> GuoYang
     * 2019/12/17
     **/
    public ComponentDetailVO getComponentDetailById(String componentId) {
        try {
            return comMapping.getComponentDetailById(componentId, STRUCTURE_PICTURE_FILE_TYPE);
        } catch (DataAccessException e) {
            logger.error("Method[getComponentDetailById] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object insertComponents(ComponentDTO componentDTO) {
        // TODO Auto-generated method stub
        componentDTO.setId(PrimaryKeyGenerator.generatorId());
        componentDTO.setCreateBy(UserUtils.getUser().getId());
        int result = comMapping.insertComponents(componentDTO);
        if (result > 0) {
            return "SUCCESS";
        }
        throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
    }

    public Object updateComponents(ComponentDTO componentDTO) {
        // TODO Auto-generated method stub
        componentDTO.setModifyBy(UserUtils.getUser().getId());
        int result = comMapping.updateComponents(componentDTO);
        if (result > 0) {
            return "SUCCESS";
        }
        throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
    }

    public Object delComponents(String ids) {
        // TODO Auto-generated method stub
        String[] idsArr = ids.split(",");
//        Long[] idsList = new Long[idsArr.length];
//        for (int i = 0, len = idsArr.length; i < len; i++) {
//            idsList[i] = new Long(idsArr[i]);
//        }
        for (String id : idsArr) {
            int result = comMapping.delComponents(id);
            if (result == 0) {
                throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
            }
        }
        return "SUCCESS";
    }


    public Object insertFile(SysFilePO sysFiles, String id) {
        // TODO Auto-generated method stub
        //文件属性存入 sysFile表
        sysFileService.addSysFile(sysFiles);
        String fileId = sysFiles.getId();
        int result = comMapping.saveFaultTypeFile(fileId, id);
        if (result == 0) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
        return "SUCCESS";
    }

    public Object delFile(String id) {
        // TODO Auto-generated method stub
        try {
            sysFileService.deleteSysFile(id);
            comMapping.delFile(id);
        } catch (Exception e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
        return "SUCCESS";
    }

    public Object selectlFile(String componentId) {
        // TODO Auto-generated method stub
        List<SysFilePO> files = comMapping.selectlFile(componentId);
        return files;
    }

    public Object getComponentsByTypeId(String componentTypeId) {
        List<ComponentDTO> components = new ArrayList<>();
        if (componentTypeId == null) {
            return components;
        }
        components = comMapping.getComponentsByTypeId(componentTypeId);
        return components;
    }

    public Object bindComponent(String componentTypeId, String vehicleId, String componentId, String structureCode) {
        // TODO Auto-generated method stub
        try {
            struVehicleStructureComponentMapping.bindComponent(componentTypeId, vehicleId, componentId, structureCode);
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "绑定部件失败");
        }
        return "SUCCCESS";
    }

//	@Transactional(rollbackFor = {RestApiException.class})
//	public Object changeComponent(Long oldComponentId, Long vehicleId, Long componentId, String vehicleStructureCode) {
//		// TODO Auto-generated method stub
//		try {
//			comMapping.changeComponent(vehicleId, oldComponentId, vehicleStructureCode);
//			comMapping.bindComponent(vehicleId, componentId, vehicleStructureCode);
//		} catch (DataAccessException e) {
//			throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION.getErrorCode(),"更换部件失败");
//		}
//		return "SUCCCESS";
//	}
}
