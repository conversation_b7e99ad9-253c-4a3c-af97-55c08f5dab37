package cc.crrc.manage.service;


import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UUIDUtils;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.SysRoleTypeMapping;
import cc.crrc.manage.pojo.SysRoleTypeVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class SysRoleTypeService {
    @Autowired
    private SysRoleTypeMapping mapping;


    public Object saveSysRoleType(SysRoleTypeVO sysRoleTypeVO) {
        List<SysRoleTypeVO> list = mapping.getSysRoleType(sysRoleTypeVO);
        if (list.size() > 0) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "编码或者名称重复，请重新输入");
        }
        //雪花算法添加主键
        String id = String.valueOf(PrimaryKeyGenerator.generatorId());
        sysRoleTypeVO.setId(id);
        sysRoleTypeVO.setCreateBy(UserUtils.getUser().getName());
        try {
            mapping.saveSysRoleType(sysRoleTypeVO);
        } catch (Exception e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "数据插入失败");
        }
        return "保存成功";
    }

    @Transactional
    public Object deleteSysRoleTypeById(SysRoleTypeVO sysRoleTypeVO) {
        int delStatus = mapping.deleteSysRoleTypeById(sysRoleTypeVO);
        if (delStatus > 0) {
            return "删除成功";
        } else {
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION.getErrorCode(), "删除失败");

        }

    }

    public Object updateSysRoleTypeById(SysRoleTypeVO sysRoleTypeVO) {
        List<SysRoleTypeVO> list = mapping.getSysRoleType(sysRoleTypeVO);
        if (list.size() > 1) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "编码或者名称重复，请重新输入");
        }
        sysRoleTypeVO.setUpdateBy(UserUtils.getUser().getName());
        int updateStatus = mapping.updateSysRoleTypeById(sysRoleTypeVO);
        if (updateStatus > 0) {
            return "修改成功";
        } else {
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION.getErrorCode(), "修改失败");
        }
    }

    public Object selectSysRoleType(SysRoleTypeVO sysRoleTypeVO) {
        //分页
        int currentPage = sysRoleTypeVO.getPageNumber();
        int pageSize = sysRoleTypeVO.getPageSize();
        PageHelper.startPage(currentPage, pageSize);

        List<SysRoleTypeVO> list = mapping.selectSysRoleType(sysRoleTypeVO);
        if (currentPage == 0 && pageSize == 0) {
            return list;
        } else {
            PageHelper.startPage(currentPage, pageSize);
            PageInfo<SysRoleTypeVO> pageInfo = new PageInfo<SysRoleTypeVO>(list);
            return pageInfo;
        }

    }

    public SysRoleTypeVO selectSysRoleTypeById(SysRoleTypeVO sysRoleTypeVO) {
        SysRoleTypeVO sysRoleType = mapping.selectSysRoleTypeById(sysRoleTypeVO);
        return sysRoleType;
    }
}
