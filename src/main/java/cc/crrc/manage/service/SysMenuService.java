package cc.crrc.manage.service;


import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.TreeUtil;
import cc.crrc.manage.common.utils.UUIDUtils;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.SysMenuMapping;
import cc.crrc.manage.pojo.MenuTree;
import cc.crrc.manage.pojo.SysMenuVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Service
public class SysMenuService {
    @Autowired
    private SysMenuMapping mapping;


    public Object saveSysMenu(SysMenuVO sysMenuVO) {


        //获取当前用户进行权限判断
        String user = UserUtils.getUser().getIsSuperAdmin();
//        if (!user.equals("1")) {
//            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "越权操作，只有超级管理员才能添加或修改数据！");
//        }

        //雪花算法添加主键
        sysMenuVO.setId(String.valueOf(PrimaryKeyGenerator.generatorId()));
        sysMenuVO.setCreateBy(user);
        try {
            mapping.saveSysMenu(sysMenuVO);
        } catch (Exception e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "新增失败:" + e.getMessage());
        }
        return "success";
    }

    public Object deleteSysMenuById(SysMenuVO sysMenuVO) {
        //获取当前用户进行权限判断
        String user = UserUtils.getUser().getIsSuperAdmin();
//        if (!user.equals("1")) {
//            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION.getErrorCode(), "越权操作，只有超级管理员才能添加或修改数据！");
//        }
        sysMenuVO.setUpdateBy(user);
        sysMenuVO.setDelFlag("1");
        try {
            mapping.deleteSysMenuById(sysMenuVO);
        } catch (Exception e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION.getErrorCode(), "删除失败:" + e.getMessage());
        }
        return "success";
    }

    public Object updateSysMenu(SysMenuVO sysMenuVO) {
        String user = UserUtils.getUser().getIsSuperAdmin();
//        if (!user.equals("1")) {
//            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION.getErrorCode(), "越权操作，只有超级管理员才能添加或修改数据！");
//        }
        sysMenuVO.setUpdateBy(user);
        try {
            mapping.updateSysMenu(sysMenuVO);
        } catch (Exception e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION.getErrorCode(), "更新失败:" + e.getMessage());
        }
        return "success";
    }

    public List<SysMenuVO> selectSysMenuById(SysMenuVO sysMenuVO) {
        List<SysMenuVO> list = mapping.selectSysMenuById(sysMenuVO);
        return list;
    }

    public List<MenuTree> selectSysMenuTreeList() {
        List<SysMenuVO> menus = mapping.selectSysMenuTreeList();
        List<MenuTree> menuTree = getMenuTree(menus, "-1");
        return menuTree;
    }

    //获取树形结构的json串
    private List<MenuTree> getMenuTree(List<SysMenuVO> menus, String root) {
        List<MenuTree> trees = new ArrayList<MenuTree>();
        MenuTree node = null;
        for (SysMenuVO menu : menus) {
            node = new MenuTree();
            BeanUtils.copyProperties(menu, node);
            node.setLabel(node.getTitle());
            trees.add(node);
        }
        List<MenuTree> bulid = TreeUtil.bulid(trees, root, new Comparator<MenuTree>() {
            @Override
            public int compare(MenuTree o1, MenuTree o2) {
                return o1.getSort().compareTo(o2.getSort());
            }
        });
        return bulid;
    }

    public Object selectSysMenuTreeListByRoleId(String roleId) {

        //通过token获取roleId  暂时tocken里没有 需要输入roleID
        List<SysMenuVO> menus = mapping.selectSysMenuTreeListByRoleId(roleId);
        List<MenuTree> menuTree = getMenuTree(menus, "-1");
        return menuTree;
    }

    //获取当前用户的组织结构
    public Object selectSysMenuTreeListByUser() {
        String userId = UserUtils.getUserId();
        List<SysMenuVO> usermenus = mapping.selectSysMenuTreeListByUsername(userId);
        List<MenuTree> userMenuTree = getMenuTree(usermenus, "-1");
        return userMenuTree;


    }


    //获取选择完权限后的首页结构树
    public Object selectSysMenuTreeListByHomePage(List<String> MenuIds) {
        List<SysMenuVO> menus = mapping.selectSysMenuTreeListByHomePage(MenuIds);
        List<MenuTree> homePageMenuTree = getMenuTree(menus, "-1");
        return homePageMenuTree;


    }


}
