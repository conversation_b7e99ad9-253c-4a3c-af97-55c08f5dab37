package cc.crrc.manage.service;

import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.EntitlementMapping;
import cc.crrc.manage.pojo.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class EntitlementService {

    @Autowired
    private UserService userService;

    @Autowired
    private EntitlementMapping entitlementMapping;

    public UserInfo getUserInfo() throws Exception {

//        String username = "admin";//此处为写死的  缺少获取用户id的逻辑
        String username = UserUtils.getUser().getUsername();

        if (username == null) {
            return null;
        }
        UserInfo user = this.getUserByUsername(username);
        UserInfo userInfo = new UserInfo();
        BeanUtils.copyProperties(user, userInfo);
        List<RoleInfo> entitlementInfos = this.getEntitlementByUsername(username);
        Stream<RoleInfo> menus = entitlementInfos.parallelStream().filter((permission) -> {
            return "menu".equals(permission.getType());
        });
        userInfo.setMenus(menus.collect(Collectors.toList()));
        Stream<RoleInfo> elements = entitlementInfos.parallelStream().filter((permission) -> {
            return "element".equals(permission.getType());
        });
        userInfo.setElements(elements.collect(Collectors.toList()));
        //查询用户角色默认首页菜单信息 2020-6-22 zhangzhijian
        SysMenuVO sysMenuVO = findHomeMenuByUserId(UserUtils.getUserId());
        //设置首页菜单的编码和排序号 2020-6-22 zhangzhijian
        userInfo.setMenuCode(sysMenuVO.getCode());
        userInfo.setMenuSort(sysMenuVO.getSort());
        return userInfo;
    }

    /**
     * @Description 通过用户id查询用户权限最大的角色的默认首页菜单
     * @Name findHomeMenuByUserId
     * @Param userId 用户id
     * @Return  SysMenuVO 返回菜单信息
     * <AUTHOR> zhijian
     * @Date 2020/6/22 8：47
     */
    private SysMenuVO findHomeMenuByUserId(String userId) {
        SysMenuVO sysMenuVO = new SysMenuVO();
        if ("1".equals(userService.selectUserInfoById(userId).getIsSuperAdmin())) {//1代表超级管理员标识
            sysMenuVO.setCode("lineMonitor");
            sysMenuVO.setSort("10-1");
            return sysMenuVO;
        }
        //非超级管理员需要查询菜单
        List<SysRoleVO> sysRoleVOList = entitlementMapping.findRoleIdByUserId(userId);
        if(sysRoleVOList != null && sysRoleVOList.size()>0){
            //只查询最大权限的角色默认首页菜单
            List<Menu> menus = entitlementMapping.findMenuByHomeMenuId(sysRoleVOList.get(0).getHomeMenuId());
            if(menus != null && menus.size()>0){
                sysMenuVO.setCode(menus.get(0).getCode());
                sysMenuVO.setSort(menus.get(0).getSort());
            }
        }
        return sysMenuVO;
    }

    //    @Cacheable(key = "menu")
    public List<Menu> getMenusByUsername() {
        List<Menu> allMenu = entitlementMapping.getAllMenu();
        return allMenu;
    }

    public List<RoleInfo> getEntitlementByUsername(String username) {
        //查询
//        List<Menu> menus = this.getUserMenuByUserId("a1a2cb17b4f24e50bbde2f1b8a233bcb");//1为admin 的userid
        List<Menu> menus = this.getUserMenuByUserId(UserUtils.getUserId());
        List<RoleInfo> result = new ArrayList<RoleInfo>();

        for (Menu menu : menus) {
            if (StringUtils.isBlank(menu.getHref())) {
                menu.setHref("/" + menu.getCode());
            }
            RoleInfo roleInfo = new RoleInfo();
            roleInfo.setCode(menu.getCode());
            roleInfo.setType("menu");
            roleInfo.setName("访问");
            String uri = menu.getHref();
            if (!uri.startsWith("/")) {
                uri = "/" + uri;
            }
            roleInfo.setUri(uri);
            roleInfo.setMethod("GET");
            result.add(roleInfo);
            roleInfo.setMenu(menu.getTitle());
        }

//        List<Element> elements = this.getUserElementByUserId("a1a2cb17b4f24e50bbde2f1b8a233bcb");//1为admin 的userid
        List<Element> elements = this.getUserElementByUserId(UserUtils.getUserId());//1为admin 的userid//董哥 需要改

        for (Element element : elements) {
            RoleInfo roleelementInfo = new RoleInfo();
            roleelementInfo.setCode(element.getElementCode());
            roleelementInfo.setType("element");
            roleelementInfo.setUri(element.getElementUri());
            roleelementInfo.setMethod(element.getMethod());
            roleelementInfo.setName(element.getElementName());
            roleelementInfo.setMenu(element.getMenuName());
            result.add(roleelementInfo);
        }
        return result;
    }

    public UserInfo getUserByUsername(String username) {
        UserInfo info = new UserInfo();
        User user = userService.getUserByUsername(username);
        BeanUtils.copyProperties(user, info);
        info.setId(user.getId());
        info.setImage(user.getImage());
        return info;
    }

    public List<Menu> getUserMenuByUserId(String userId) {
        if ("1".equals(userService.selectUserInfoById(userId).getIsSuperAdmin())) {//1代表超级管理员标识
            List<Menu> menus = entitlementMapping.getAllMenu();
            return menus;
        }
        List<Menu> menus = entitlementMapping.getMenuByUserId(userId);
        return menus;
    }

    @Cacheable(key = "menu")
    public List<Element> getUserElementByUserId(String userId) {
        if ("1".equals(userService.selectUserInfoById(userId).getIsSuperAdmin())) {//1代表超级管理员标识
            List<Element> elements = entitlementMapping.getAllElement();
            return elements;
        }
        List<Element> elements = entitlementMapping.getAllElementByUserId(userId);
        return elements;
    }


}
