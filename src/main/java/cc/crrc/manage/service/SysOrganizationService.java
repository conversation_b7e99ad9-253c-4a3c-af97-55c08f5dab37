package cc.crrc.manage.service;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.TreeUtil;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.SysOrganizationMapping;
import cc.crrc.manage.mapper.SysOrganizationUserMapping;
import cc.crrc.manage.mapper.UserMapping;
import cc.crrc.manage.pojo.OrganizationTreeVO;
import cc.crrc.manage.pojo.SysOrganizationPO;
import cc.crrc.manage.pojo.SysOrganizationUserPO;
import cc.crrc.manage.pojo.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.*;

/**
 * @FileName SysOrganizationService
 * <AUTHOR> yuxi
 * @Date 2019/10/12 13:11
 * @Version 1.0
 **/
@Service
public class SysOrganizationService {
    private final Logger logger = LoggerFactory.getLogger(SysOrganizationService.class);
    @Autowired
    private SysOrganizationMapping organizationMapping;
    @Autowired
    private SysOrganizationUserMapping organizationUserMapping;
    @Autowired
    private UserMapping userMapping;

    /**
     * @return java.util.List<cc.crrc.manage.pojo.OrganizationTreeVO>
     * @Description 取得所有组织
     * <AUTHOR> yuxi
     * @Date 13:50 2019/10/12
     * @Param []
     **/
    public List<OrganizationTreeVO> listOrganizations() {
        try {
            //获取所有组织数据
            List<SysOrganizationPO> organizationList = organizationMapping.listOrganizations();

            if (CollectionUtils.isEmpty(organizationList)) {
                return null;
            }
            // 组织集合数据建树
            return getOrganizationsTree(organizationList);
        } catch (DataAccessException e) {
            logger.error("Method[listOrganizations] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @return java.util.List<cc.crrc.manage.pojo.OrganizationTreeVO>
     * @Description 组织数据建树
     * <AUTHOR> yuxi
     * @Date 14:34 2019/10/12
     * @Param [organizationList, root]
     **/
    private List<OrganizationTreeVO> getOrganizationsTree(List<SysOrganizationPO> organizationList) {
        List<OrganizationTreeVO> trees = new ArrayList<>();
        OrganizationTreeVO node;
        String parentName;
        for (SysOrganizationPO organization : organizationList) {
            node = new OrganizationTreeVO();
            BeanUtils.copyProperties(organization, node);
            // 设置id
            node.setId(String.valueOf(organization.getId()));
            // 设置父id
            node.setParentId(String.valueOf(organization.getParentId()));
            // 设置部门名称
            node.setName(organization.getName());
            // 设置父级部门名称
            parentName = organizationMapping.getParentNameByParentId(organization.getParentId());
            node.setParentName(StringUtils.isEmpty(parentName) ? "" : parentName);
            trees.add(node);
        }
        return TreeUtil.bulid(trees, "0", (Comparator<OrganizationTreeVO>) (t1, t2) -> 0);
    }

    /**
     * @return cc.crrc.manage.pojo.SysOrganizationPO
     * @Description 根据id查询组织
     * <AUTHOR> yuxi
     * @Date 15:47 2019/10/12
     * @Param [id]
     **/
    public SysOrganizationPO getOrganizationById(String id) {
        try {
            return organizationMapping.getOrganizationById(id);
        } catch (DataAccessException e) {
            logger.error("Method[getOrganizationById] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @return cc.crrc.manage.pojo.SysOrganizationPO
     * @Description 根据id查询组织
     * <AUTHOR> yuxi
     * @Date 15:47 2019/10/12
     * @Param [id]
     **/
    public SysOrganizationPO getOrganizationByName(String name) {
        try {
            return organizationMapping.getOrganizationByName(name);
        } catch (DataAccessException e) {
            logger.error("Method[getOrganizationByName] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }


    /**
     * @return int
     * @Description 根据名称查询组织数量
     * <AUTHOR> yuxi
     * @Date 9:41 2019/10/14
     * @Param [name]
     **/
    public int getOrganizationCountByName(String name) {
        try {
            return organizationMapping.getOrganizationCountByName(name);
        } catch (DataAccessException e) {
            logger.error("Method[getOrganizationCountByName] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @return cc.crrc.manage.pojo.SysOrganizationPO
     * @Description 根据组织编码查询组织数量
     * <AUTHOR> yuxi
     * @Date 16:38 2019/10/12
     * @Param [name]
     **/
    public int getOrganizationCountByCode(String code) {
        try {
            return organizationMapping.getOrganizationCountByCode(code);
        } catch (DataAccessException e) {
            logger.error("Method[getOrganizationCountByCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @return int
     * @Description 新增组织
     * <AUTHOR> yuxi
     * @Date 16:01 2019/10/12
     * @Param [organizationPO]
     **/
    public int addOrganization(SysOrganizationPO organization) {
        try {
            Date date = new Date();
            Timestamp t = new Timestamp(date.getTime());
            String userId = UserUtils.getUserId();
            // 插入时间、用户id、删除标识
            organization.setModifyTime(t);
            organization.setModifyBy(userId);
            organization.setCreateTime(t);
            organization.setCreateBy(userId);
            organization.setDelFlag("0");
            //雪花算法添加主键
            organization.setId(PrimaryKeyGenerator.generatorId());
            String organizationId = organization.getId();
            //新增添加逻辑，因组织查询时需要查询组织用户表，
            organizationMapping.addOrganizationUser(userId, organizationId);
            return organizationMapping.addOrganization(organization);
        } catch (DataAccessException e) {
            logger.error("Method[addOrganization] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * @return java.lang.Object
     * @Description 更新组织
     * <AUTHOR> yuxi
     * @Date 16:30 2019/10/12
     * @Param [organizationPO]
     **/
    public int updateOrganization(SysOrganizationPO organization) {
        try {
            Date date = new Date();
            String userId = UserUtils.getUserId();
            // 更新时间、用户id
            organization.setModifyTime(date);
            organization.setModifyBy(userId);
            //校验是否关联到子节点
            List<SysOrganizationPO> organizationList = organizationMapping.listOrganizationsUser(userId);
            Set<String> set = new HashSet<>();
            set.add(organization.getId());
            for (int i = 0; i < organizationList.size(); i++) {
                if (set.contains(organizationList.get(i).getParentId()) && !set.contains(organizationList.get(i).getId())) {
                    set.add(organizationList.get(i).getId());
                    i = 0;
                }
            }
            if (set.contains(organization.getParentId())) {
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "不可关联到子组织！");
            }
            return organizationMapping.updateOrganization(organization);
        } catch (DataAccessException e) {
            logger.error("Method[updateOrganization] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    /**
     * @return int
     * @Description 删除组织
     * <AUTHOR> yuxi
     * @Date 16:52 2019/10/12
     * @Param [id]
     **/
    public int deleteOrganization(String id) {
        try {
            // 校验组织部门下是否存在子部门
            int countSub = organizationMapping.countSubOrganization(id);
            if (countSub > 0) {
                throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION.getErrorCode(), "该组织下存在子部门，不允许删除！");
            }
            // 校验组织部门下是否存在用户
            int countUser = organizationUserMapping.countUserByOrganizationId(id);
            // 根据organizationId查询userid
            List<SysOrganizationUserPO> sysOrganizationUserPOS = organizationUserMapping.UserIdByOrganizationId(id);
            String userid = "";
            for (SysOrganizationUserPO sysOrganizationUserPO : sysOrganizationUserPOS) {
                userid = sysOrganizationUserPO.getUserId();
            }
            String appUserId = UserUtils.getUserId();
            //根据organizationId查询userid再跟当前所登录的用户进行对比判断，如果存在则此条数据为组织用户数据
            if (userid.equals(appUserId)) {
                countUser = 0;
            }
            //新增判断，因为组织部门查询时会用到OrganizationId，只有组织关系用户和用户表都有此ID才代表该组织下存在用户
            User user = userMapping.selectUserInfoById(userid);
            if (countUser > 0 && user != null) {
                throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION.getErrorCode(), "该组织下存在用户，不允许删除！");
            }

            Date date = new Date();
            String userId = UserUtils.getUserId();
            SysOrganizationPO organization = new SysOrganizationPO();
            // 更新时间、用户id、删除标识
            organization.setId(id);
            organization.setModifyTime(date);
            organization.setModifyBy(userId);
            organization.setDelFlag("1");
            return organizationMapping.updateOrganization(organization);
        } catch (DataAccessException e) {
            logger.error("Method[deleteOrganization] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    /**
     * @return void
     * @Description 新增组织用户关系数据
     * <AUTHOR> yuxi
     * @Date 11:02 2019/10/15
     * @Param [organizationIds, userId]
     **/
    public void addOrganizationUserRelation(String organizationIds, String userId) {
        try {
            if (!"".equals(organizationIds) && organizationIds != null) {
                // 拆分该用户待更新的组织id字符串
                String[] organizationIdArr = organizationIds.split(",");
                SysOrganizationUserPO organizationUserRelation;
                for (String organizationId : organizationIdArr) {
                    organizationUserRelation = new SysOrganizationUserPO();
                    organizationUserRelation.setOrganizationId(organizationId);
                    organizationUserRelation.setUserId(userId);
                    organizationUserMapping.addOrganizationUser(organizationUserRelation);
                }
            }
        } catch (DataAccessException e) {
            logger.error("Method[addOrganizationUser] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * @return void
     * @Description 更新用户组织关系
     * <AUTHOR> yuxi
     * @Date 18:30 2019/10/14
     * @Param [organizationIds, userId]
     **/
    public void updateOrganizationUserRelation(String organizationIdsNew, String userId) {
        try {
            // 查询原有的组织用户关系数据并删除
            List<SysOrganizationUserPO> organizationUserRelationsOrigin = organizationUserMapping.listOrganizationUserRelationsByUserId(userId);
            for (SysOrganizationUserPO relation : organizationUserRelationsOrigin) {
                organizationUserMapping.deleteOrganizationUserRelation(relation);
            }
            // 删除原有的关系数据后新增
            addOrganizationUserRelation(organizationIdsNew, userId);
        } catch (DataAccessException e) {
            logger.error("Method[updateOrganizationUser] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * @throws
     * @Title: listOrganizationsUser
     * @Description: 用户的组织数据
     * @param: @return
     * @return: List<OrganizationTreeVO>
     * @date: 2020年8月14日 下午3:47:37
     * @author: Heshenglun
     */
    public List<OrganizationTreeVO> listOrganizationsUser() {
        try {
            // 获取当前用户的userId
            String userId = UserUtils.getUserId();
            //获取所有组织数据
//            List<SysOrganizationPO> organizationList = organizationMapping.listOrganizationsUser(userId);
            List<SysOrganizationPO> organizationList = organizationMapping.listOrganizationsByUserAndNotId(userId, "0");
            List<String> selectBox = organizationMapping.listSelectBox(userId);
            for (SysOrganizationPO a : organizationList) {
                for (String b : selectBox) {
                    if (a.getId().equals(b)) {
                        a.setSelectBox("1");
                    }
                }
            }
            if (CollectionUtils.isEmpty(organizationList)) {
                return null;
            }
            // 组织集合数据建树
            return getOrganizationsTree(organizationList);
        } catch (DataAccessException e) {
            logger.error("Method[listOrganizations] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 修改组织根据id查找可关联的结构
     *
     * @param id
     * @return
     */
    public List<OrganizationTreeVO> getOrganizationTreeUser(String id) {
        try {
            // 获取当前用户的userId
            String userId = UserUtils.getUserId();
            //获取所有组织数据
            List<SysOrganizationPO> organizationList = organizationMapping.listOrganizationsByUserAndNotId(userId, id);
            List<String> selectBox = organizationMapping.listSelectBox(userId);
            for (SysOrganizationPO a : organizationList) {
                for (String b : selectBox) {
                    if (a.getId().equals(b) && !a.getId().equals(id)) {
                        a.setSelectBox("1");
                    }
                }
            }
 /*            Set<String> set =new HashSet<>();
            //根据id排查子节点，删除不可关联的节点
            for (int i = 0; i <organizationList.size() ; i++) {
                if(set.contains(organizationList.get(i).getParentId()) || organizationList.get(i).getId().equals(id)){
                    set.add(organizationList.get(i).getId());
                    organizationList.remove(i);
                    i=0;
                }
            }
*/
            if (CollectionUtils.isEmpty(organizationList)) {
                return new ArrayList<>();
            }
            // 组织集合数据建树
            return getOrganizationsTree(organizationList);
        } catch (Exception e) {
            logger.error("Method[getOrganizationTreeUser] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

}
