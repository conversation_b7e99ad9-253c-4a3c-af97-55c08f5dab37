package cc.crrc.manage.service;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.SysAreaMapping;
import cc.crrc.manage.mapper.SysOrganizationMapping;
import cc.crrc.manage.mapper.line.LineMapping;
import cc.crrc.manage.pojo.SysLocationVO;
import cc.crrc.manage.pojo.SysOrganizationPO;
import cc.crrc.manage.pojo.line.LineDTO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * @ClassName SysAreaServicer
 * <AUTHOR>
 * @Date 2020/8/3 19:59
 * @Version 1.0
 **/
@Service
public class SysAreaService {
    private final Logger logger = LoggerFactory.getLogger(SysDictService.class);
    @Autowired
    private SysAreaMapping areaMapping;
    @Autowired
    private LineMapping lineMapping;
    @Autowired
    private SysOrganizationMapping organizationMapping;

    /**
     * @return int
     * @Description 新建区域
     * <AUTHOR> mingkuan
     * @Date 2020/8/4
     * @Param [sysLocationVO]
     **/
    public Object saveSysArea(SysLocationVO sysLocationVO) {
        try {
            String nameCn = sysLocationVO.getNameCn();
            int count = areaMapping.getCountByNameCn(nameCn);
            if (count > 0) {
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "区域已存在！");
            }
            String userId = UserUtils.getUserId();
            Date date = new Date();
            Timestamp t = new Timestamp(date.getTime());
            sysLocationVO.setDelFlag(false);
            sysLocationVO.setCreateTime(t);
            sysLocationVO.setCreateBy(userId);
            sysLocationVO.setModifyTime(t);
            sysLocationVO.setModifyBy(userId);
            //雪花算法添加主键
            sysLocationVO.setId(PrimaryKeyGenerator.generatorId());
            return areaMapping.saveSysArea(sysLocationVO);
        } catch (Exception e) {
            logger.error("Method[saveSysArea] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * @return void
     * @Description 删除区域
     * <AUTHOR> mingkuan
     * @Date 2020/8/4
     * @Param
     **/
    public int deleteSysArea(String id) {
        try {
            String userId = UserUtils.getUserId();
            Date date = new Date();
            Timestamp t = new Timestamp(date.getTime());
            SysLocationVO sysLocationVO = new SysLocationVO();
            sysLocationVO.setId(id);
            sysLocationVO.setDelFlag(true);
            sysLocationVO.setModifyTime(t);
            sysLocationVO.setModifyBy(userId);
            return areaMapping.deleteSysArea(sysLocationVO);
        } catch (DataAccessException e) {
            logger.error("Method[deleteSysArea] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    /**
     * @return int
     * @Description 更新区域信息
     * <AUTHOR> mingkuan
     * @Date 2020/8/4
     * @Param [sysLocationVO]
     **/
    public Object updateSysArea(SysLocationVO sysLocationVO) {
        try {
//            String nameCn = SysLocationVO.getNameCn();
//            int count = areaMapping.getCountByNameCn(nameCn);
//            if (count == 0) {
//                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "该区域不存在！");
//            }
            Date date = new Date();
            Timestamp t = new Timestamp(date.getTime());
            sysLocationVO.setModifyTime(t);
            sysLocationVO.setModifyBy(UserUtils.getUserId());
            return areaMapping.updateSysArea(sysLocationVO);
        } catch (DataAccessException e) {
            logger.error("Method[updateSysArea] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    /**
     * @Description 分页查询区域列表
     * <AUTHOR> mingkuan
     * @Date 2020/8/4
     * @Param
     **/
    public PageInfo<SysLocationVO> getSysArea(SysLocationVO sysLocationVO) {
        try {
            // 分页
            int currentPage = sysLocationVO.getPageNumber();
            int pageSize = sysLocationVO.getPageSize();
            PageHelper.startPage(currentPage, pageSize);
            // 获取数据
            List<SysLocationVO> areaList = areaMapping.getSysAreaList(sysLocationVO);
            return new PageInfo<>(areaList);
        } catch (DataAccessException e) {
            logger.error("Method[getSysArea] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @Description 查询市级列表(带模糊查询)
     * <AUTHOR> mingkuan
     * @Date 2020/8/4
     * @Param
     **/
    public Object getCityList(String areaName) {
        try {
            return areaMapping.getCityListByAreaName(areaName);
        } catch (DataAccessException e) {
            logger.error("Method[getCityList] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @Description 查询当前用户的区域列表
     * @Return java.lang.Object
     * <AUTHOR> yuxi
     * @Date 11:31 2020/8/13
     * @Param []
     **/
    public Object getLocationList() {
        try {
            return areaMapping.getLocationList(UserUtils.getUserId());
        } catch (DataAccessException e) {
            logger.error("Method[getLocationList] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @Description 查询当前区域下的线路列表
     * @Return java.lang.Object
     * <AUTHOR> yuxi
     * @Date 11:49 2020/8/13
     * @Param [locationId]
     **/
    public Object getLineList(String locationId) {
        try {
            LineDTO line = new LineDTO();
            line.setLocationId(locationId);
            List<SysOrganizationPO> organizationList = organizationMapping.listOrganizationsUserTraversalDown(UserUtils.getUserId());
            return lineMapping.getLines(line, organizationList, UserUtils.getUserId());
        } catch (DataAccessException e) {
            logger.error("Method[getLineList] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @Description 查询当前线路下的车型列表
     * @Return java.lang.Object
     * <AUTHOR> yuxi
     * @Date 11:49 2020/8/13
     * @Param [locationId]
     **/
    public Object getVehicleTypeList(String lineId) {
        try {
            /*if(null==lineId){
                lineId = Long.valueOf(0);
            }*/
            return areaMapping.getVehicleTypeList(lineId);
        } catch (DataAccessException e) {
            logger.error("Method[getVehicleTypeList] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @Description 查询车辆列表
     * @Return java.lang.Object
     * <AUTHOR> yuxi
     * @Date 11:49 2020/8/13
     * @Param [locationId]
     **/
    public Object getVehicleList(String lineId, String vehicleTypeId) {
        try {
            return areaMapping.getVehicleList(lineId, vehicleTypeId);
        } catch (DataAccessException e) {
            logger.error("Method[getVehicleList] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }
}
