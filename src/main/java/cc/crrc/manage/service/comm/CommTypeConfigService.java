package cc.crrc.manage.service.comm;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.comm.CommTypeConfigMapping;
import cc.crrc.manage.mapper.mtr.MtrVehicleTypeMapping;
import cc.crrc.manage.pojo.comm.protocolTypeConfig.MqttTopicTypeDTO;
import cc.crrc.manage.pojo.comm.protocolTypeConfig.TcpPacketTypeDTO;
import cc.crrc.manage.pojo.mtr.MtrVehicleTypeVO;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CommTypeConfigService {
	@Autowired
	private CommTypeConfigMapping commMapping;
	@Autowired
	private MtrVehicleTypeMapping vTypeMapping;
	
	public Object getPacketType(Integer currentPage,Integer pageSize,String vehicleTypeId) {
		JSONObject json = new JSONObject();
		MtrVehicleTypeVO vType = vTypeMapping.getVehicleTypeById(vehicleTypeId);
		json.put("commType", vType.getCommType());
		if(currentPage != null && pageSize !=null) {
			PageHelper.startPage(currentPage, pageSize);
		}
		if("MQTT".equals(vType.getCommType())) {
			List<MqttTopicTypeDTO> mqttType = commMapping.getMqttTopicType(vehicleTypeId);
			if(currentPage != null && pageSize !=null) {
				PageInfo<MqttTopicTypeDTO> pageInfo = new PageInfo<>(mqttType);
				json.put("packetType", pageInfo);				
			}else {
				json.put("packetType", mqttType);								
			}
			return json;
		}
		if("TCP".equals(vType.getCommType())) {
			List<TcpPacketTypeDTO> tcpType = commMapping.getTcpPacketType(vehicleTypeId);
			if(currentPage != null && pageSize !=null) {
				PageInfo<TcpPacketTypeDTO> pageInfo = new PageInfo<>(tcpType);
				json.put("packetType", pageInfo);
			}else {
				json.put("packetType", tcpType);
			}
			return json;
		}
		throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
	}

	public Object addTcpPacketType(TcpPacketTypeDTO tcpDTO) {
		tcpDTO.setId(PrimaryKeyGenerator.generatorId());
		tcpDTO.setCreateBy(UserUtils.getUserId());
		try {
			commMapping.addTcpPacketType(tcpDTO);
		} catch (Exception e) {
			throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
		}
		return "SUCCESS";
	}

	public Object addMqttTopicType(MqttTopicTypeDTO mqttDTO) {
		mqttDTO.setId(PrimaryKeyGenerator.generatorId());
		mqttDTO.setCreateBy(UserUtils.getUserId());
		try {
			commMapping.addMqttTopicType(mqttDTO);
		} catch (Exception e) {
			throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
		}
		return "SUCCESS";
	}

	public Object updateTcpPacketType(TcpPacketTypeDTO tcpDTO) {
		tcpDTO.setModifyBy(UserUtils.getUserId());
		try {
			commMapping.updateTcpPacketType(tcpDTO);
		} catch (Exception e) {
			throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
		}
		return "SUCCESS";
	}

	public Object updateMqttTopicType(MqttTopicTypeDTO mqttDTO) {
		mqttDTO.setModifyBy(UserUtils.getUserId());
		try {
			commMapping.updateMqttTopicType(mqttDTO);
		} catch (Exception e) {
			throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
		}
		return "SUCCESS";
	}

	public Object delPacketType(String vehicleTypeId, String id) {
		try {
			MtrVehicleTypeVO vType = vTypeMapping.getVehicleTypeById(vehicleTypeId);
			if("MQTT".equals(vType.getCommType())) {
				commMapping.delMqttTopicType(id);
			}
			if("TCP".equals(vType.getCommType())) {
				commMapping.delTcpPacketType(id);
			}
		} catch (Exception e) {
			throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
		}
		return "SUCCESS";
	}

}
