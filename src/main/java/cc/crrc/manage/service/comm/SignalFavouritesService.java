package cc.crrc.manage.service.comm;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.comm.SignalFavouritesMapping;
import cc.crrc.manage.mapper.mtr.MtrVehicleTypeMapping;
import cc.crrc.manage.pojo.comm.signalfavourites.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @FileName 针对当前用户操作的信号查询中：
 * 某一车型，某一车辆的添加的全部收藏信号操作功能
 * <AUTHOR> lei
 * @Date 2021-8-5 11:49:52
 **/
@Service
public class SignalFavouritesService {
    private static final Logger logger = LoggerFactory.getLogger(SignalFavouritesService.class);

    @Autowired
    private SignalFavouritesMapping signalFavouritesMapping;
    @Autowired
    private MtrVehicleTypeMapping mtrVehicleTypeMapping;

    /**
     * 获取当前用户信号收藏列表
     *
     * @return
     */
    public List<SignalFavouritesGroupVO> signalFavouritesGroupList() {
        try {
            List<SignalFavouritesGroupVO> list = signalFavouritesMapping.signalFavouritesGroupList(UserUtils.getUserId());
            list.stream().forEach((SignalFavouritesGroupVO vo) -> {
                vo.setSignalsFavouritesVOs(this.signalsFavouritesList(vo.getId()));
            });
            return list;
        } catch (DataAccessException e) {
            logger.error("[favouritesCollectList] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 根据收藏分组id获取当前收藏夹下的信号列表
     *
     * @param groupId
     * @return
     */
    public List<SignalsFavouritesVO> signalsFavouritesList(String groupId) {
        try {
            return signalFavouritesMapping.signalsFavouritesList(groupId);
        } catch (DataAccessException e) {
            logger.error("[favouritesCollectSignalsList] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @param signalFavouritesGroupDTO
     * @desc : 保存收藏组表以及收藏组表信号的关联关系列表
     */
    @Transactional(rollbackFor = {RestApiException.class})
    public void saveSignalsFavouritesAndGroup(SignalFavouritesGroupDTO signalFavouritesGroupDTO) {
        try {
            // 保存收藏组表
            SignalFavouritesGroupPO groupPO = new SignalFavouritesGroupPO();
            BeanUtils.copyProperties(signalFavouritesGroupDTO, groupPO);
            groupPO.setCreateBy(UserUtils.getUserId());

            //2021-12-03 新版信号查询 没有车型下拉框 但是该部分需要车型 所以我们增加判断如果 传参中没有车型id
            //那么我们通过 车辆code 进行查找 手动添加
            if(StringUtils.isEmpty(signalFavouritesGroupDTO.getVehicleTypeId())){
                String vehicleTypeId = mtrVehicleTypeMapping.getVehicleTypeIdByVehicleCode(signalFavouritesGroupDTO.getVehicleCode());
                if(StringUtils.isEmpty(vehicleTypeId)){
                    throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
                }
                groupPO.setVehicleTypeId(vehicleTypeId);
            }
            //雪花算法添加主键
            groupPO.setId(PrimaryKeyGenerator.generatorId());
            signalFavouritesMapping.saveSignalFavouritesGroup(groupPO);

            // 保存收藏表组关联的信号表
            final String groupId = groupPO.getId();
            List<SignalsFavouritesPO> poList = signalFavouritesGroupDTO.getSignalList().stream().map(
                    dto -> {
                        return new SignalsFavouritesPO(PrimaryKeyGenerator.generatorId(),groupId, dto.getSignalId(), dto.getNodeId());
                    }
            ).collect(Collectors.toList());
            signalFavouritesMapping.saveSignalsFavourites(poList);
        } catch (DataAccessException e) {
            logger.error("[saveSignalsFavouritesAndGroup] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * 删除收藏组表以及收藏组表信号的关联关系列表
     *
     * @param id
     */
    @Transactional(rollbackFor = {RestApiException.class})
    public void deleteSignalsFavouritesAndGroup(String id) {
        try {
            int signalsCount = signalFavouritesMapping.deleteSignalFavouritesGroup(id);
            if (signalsCount == 0) {
                logger.error("[deleteSignalFavouritesGroup]：没找到数据，删除失败");
                throw new RestApiException(ExceptionInfoEnum.DATA_NOT_FOUND_EXCEPTION);
            }
            int collectCount = signalFavouritesMapping.deleteSignalsFavourites(id);
            if (collectCount == 0) {
                logger.error("[deleteSignalsFavourites]：没找到数据，删除失败");
                throw new RestApiException(ExceptionInfoEnum.DATA_NOT_FOUND_EXCEPTION);
            }
        } catch (DataAccessException e) {
            logger.error("[deleteSignalsFavouritesAndGroup] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    /**
     * 根据校验信号收藏名称判断是否存在重复名称
     *
     * @param name
     * @return
     */
    public boolean signalsFavouritesGroupNameIsExists(String name) {
        try {
            return signalFavouritesMapping.signalsFavouritesGroupNameIsExists(name, UserUtils.getUserId()) > 0;
        } catch (DataAccessException e) {
            logger.error("[signalsFavouritesGroupNameIsExists] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }
}
