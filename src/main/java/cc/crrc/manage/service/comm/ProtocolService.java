package cc.crrc.manage.service.comm;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.mapper.comm.ProtocolMapping;
import cc.crrc.manage.mapper.comm.SignalMapping;
import cc.crrc.manage.pojo.comm.protocol.*;
import cc.crrc.manage.pojo.comm.signal.SignalVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @FileName ProtocolService
 * <AUTHOR> shuangquan
 * @Date 2019/11/12 13:28
 **/
@Service
public class ProtocolService {
    private static final String TCP_TYPE = "TCP";
    private static final String MQTT_TYPE = "MQTT";
    private static final int DISABLED_STATUS = 0;
    private static final int ENABLED_STATUS = 1;
    private static final String CHECKED_STATUS = "1";
    private static final String UN_CHECKED_STATUS = "0";
    private static final Logger logger = LoggerFactory.getLogger(ProtocolService.class);

    @Autowired
    private ProtocolMapping mapping;

    @Autowired
    private SignalMapping signalMapping;

    public PageInfo<ProtocolVO> list(String vehicleTypeId, int pageNumber, int pageSize) {
        try {
            PageHelper.startPage(pageNumber, pageSize);
            if (StringUtils.isEmpty(vehicleTypeId)) {
                return new PageInfo<>(mapping.list(null));
            } else {
                return new PageInfo<>(mapping.list(vehicleTypeId));
            }
        } catch (DataAccessException e) {
            logger.error("[list] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        } catch (NumberFormatException e) {
            logger.error("[list] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.PARAMETER_VALIDATION_EXCEPTION.getErrorCode(), "车辆型号不正确");
        }
    }

    // 2020年8月14日 房明宽 获取协议列表 相比之前 查询条件新增 线路ID
    public PageInfo<ProtocolVO> getProtocolList(String vehicleTypeId, int pageNumber, int pageSize, String lineId) {// 增加lineId
        try {
            PageHelper.startPage(pageNumber, pageSize);
            if (StringUtils.isEmpty(vehicleTypeId)) {// 增加lineId
                return new PageInfo<>(mapping.getProtocolList(null, lineId));// 增加lineId
            } else {
                return new PageInfo<>(mapping.getProtocolList(vehicleTypeId, lineId));
            }
        } catch (DataAccessException e) {
            logger.error("[getProtocolList] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        } catch (NumberFormatException e) {
            logger.error("[getProtocolList] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.PARAMETER_VALIDATION_EXCEPTION.getErrorCode(), "车辆型号不正确");
        }
    }

    @Transactional(rollbackFor = {RestApiException.class})
    public void saveProtocol(ProtocolPO protocolPO) {
        try {
            //protocolPO.setCreateBy(UserUtils.getUserId());
            protocolPO.setId(PrimaryKeyGenerator.generatorId());
            mapping.saveProtocol(protocolPO);
            mapping.insertTcpProtocolPacketRelation(protocolPO.getVehicleTypeId(), protocolPO.getId(),
                    protocolPO.getCreateBy());
        } catch (DataAccessException e) {
            logger.error("[saveProtocol] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    public void deleteProtocol(ProtocolDelDTO protocolDelDTO) {
        try {
            ProtocolVO protocolVO = mapping.queryDetail(protocolDelDTO.getProtocolId());
            if (protocolVO == null) {
                throw new RestApiException(ExceptionInfoEnum.DATA_NOT_FOUND_EXCEPTION);
            }
            checkProtocolUsed( protocolDelDTO.getProtocolId());
            mapping.deleteProtocol(protocolDelDTO);
        } catch (DataAccessException e) {
            logger.error("[deleteProtocol] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }

    }

    public void updateStatus(ProtocolStatusDTO protocolStatusDTO) {
        try {
            ProtocolVO protocolVO = mapping.queryDetail(protocolStatusDTO.getProtocolId());
            if (protocolStatusDTO.getStatus() == DISABLED_STATUS) {
                checkProtocolUsed(protocolStatusDTO.getProtocolId());
            }
            if (protocolStatusDTO.getStatus() == ENABLED_STATUS) {
                List<SignalVO> signalList = signalMapping.validateSignal(protocolStatusDTO.getProtocolId());
                if (!CollectionUtils.isEmpty(signalList)) {
                    throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION, "该协议未通过校验，无法启用");
                }
                //增加校验逻辑 如果同车型下已有激活协议 不可以激活另一个新的协议 2021-07-02 lixin
                int enableProNum = mapping.selectEnableProNum(protocolStatusDTO.getProtocolId());
                if (enableProNum>0) {
                    throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION, "该车型下已有激活协议!");
                }
            }
            mapping.updateStatus(protocolStatusDTO);
        } catch (DataAccessException e) {
            logger.error("[updateStatus] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    /*@Transactional(rollbackFor = {RestApiException.class})
    public void upgradeProtocol(ProtocolUpgradeDTO protocolUpgradeDTO) {
        try {
            ProtocolVO protocolVO = mapping.queryDetail(protocolUpgradeDTO.getProtocolId());
            if (protocolVO.getVersion().compareTo(protocolUpgradeDTO.getVersion()) >= 0) {
                throw new RestApiException(ExceptionInfoEnum.PARAMETER_VALIDATION_EXCEPTION.getErrorCode(),
                        "升级版本号必须大于原版本");
            }
            mapping.upgradeProtocol(protocolUpgradeDTO);
            signalMapping.copyOriginalSignal(protocolUpgradeDTO.getId(), protocolUpgradeDTO.getProtocolId(),
                    protocolUpgradeDTO.getCreateBy());
            signalMapping.copyExtendSignal(protocolUpgradeDTO.getId(), protocolUpgradeDTO.getProtocolId(),
                    protocolUpgradeDTO.getCreateBy());
        } catch (DataAccessException e) {
            logger.error("[upgradeProtocol] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }*/

    public List<ProtocolMatchVO> queryMatchVehicle(String protocolId) {
        try {
            ProtocolVO protocolVO = mapping.queryDetail(protocolId);
            if (protocolVO == null) {
                throw new RestApiException(ExceptionInfoEnum.DATA_NOT_FOUND_EXCEPTION);
            }
            return mapping.queryMatchVehicle(protocolId, protocolVO.getVehicleTypeId());
        } catch (DataAccessException e) {
            logger.error("[queryMatchVehicle] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    @Transactional(rollbackFor = {RestApiException.class})
    public void match(ProtocolMatchDTO protocolMatchDTO) {
        try {
            ProtocolVO protocolVO = null;
            List<ProtocolMatchVO> invaidVehicleList =
                    protocolMatchDTO.getVehicleList().parallelStream().filter(matchvo -> UN_CHECKED_STATUS.equals(matchvo.getChecked())).collect(Collectors.toList());
            List<ProtocolMatchVO> matchVehicleList =
                    protocolMatchDTO.getVehicleList().parallelStream().filter(matchvo -> CHECKED_STATUS.equals(matchvo.getChecked())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(invaidVehicleList)) {
                protocolVO = mapping.queryDetail(protocolMatchDTO.getProtocolId());
                mapping.invalidVehicleProtocol(protocolMatchDTO.getProtocolId(),
                        invaidVehicleList, protocolMatchDTO.getModifyBy());
            }
            if (!CollectionUtils.isEmpty(matchVehicleList)) {
                if (protocolVO == null) {
                    protocolVO = mapping.queryDetail(protocolMatchDTO.getProtocolId());
                }
                mapping.updateInvaidByPacket(protocolMatchDTO.getProtocolId(),
                        matchVehicleList, protocolMatchDTO.getModifyBy());

                    mapping.matchTcpVehicle(protocolMatchDTO.getProtocolId(),
                            matchVehicleList
                            , protocolMatchDTO.getCreateBy());
            }
        } catch (DataAccessException e) {
            logger.error("[queryMatchVehicle] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_OPERATION_EXCEPTION);
        }

    }

    public Map<String, Object> validateSignal(String protocolId) {
        Map<String, Object> result = new HashMap<>();
        List<SignalVO> signalList = signalMapping.validateSignal(protocolId);
        if (CollectionUtils.isEmpty(signalList)) {
            result.put("result", 1);
            result.put("message", "故障代码与触发值一致性校验成功！");
        } else {
            String reason = signalList.parallelStream().map(vo -> vo.getNameEN()).collect(Collectors.joining(","));
            result.put("result", 0);
            result.put("message", "故障代码与触发值一致性校验失败！");
            result.put("reason", "信号名称为[" + reason + "]的触发值存在空值！");
        }
        return result;
    }

    private void checkProtocolUsed(String protocolId) {
        int count = mapping.queryProtocolByTcp(protocolId);
        if (count > 0) {
            throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION.getErrorCode(), "协议使用中，不能进行此操作");
        }
    }


}
