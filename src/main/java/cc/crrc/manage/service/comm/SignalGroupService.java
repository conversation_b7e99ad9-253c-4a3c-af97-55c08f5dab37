package cc.crrc.manage.service.comm;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.mapper.comm.SignalGroupMapping;
import cc.crrc.manage.pojo.comm.signal.SignalVO;
import cc.crrc.manage.pojo.comm.signalgroup.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @FileName SignalGroupService
 * <AUTHOR> shuangquan
 * @Date 2019/11/19 13:36
 **/
@Service
public class SignalGroupService {
    private static final Logger logger = LoggerFactory.getLogger(SignalGroupService.class);
    @Autowired
    private SignalGroupMapping signalGroupMapping;

    public PageInfo<SignalGroupVO> list(String mtrLineId , String strVehicleTypeId, String name, int pageNumber, int pageSize) {
//        long vehicleTypeId = 0;
//        long lineId = 0;
//        if (!StringUtils.isEmpty(strVehicleTypeId)) {
//            vehicleTypeId = Long.parseLong(strVehicleTypeId);
//        }
//        if (!StringUtils.isEmpty(mtrLineId)) {
//            lineId = Long.parseLong(mtrLineId);
//        }
        PageHelper.startPage(pageNumber, pageSize);
        try {
            return new PageInfo<>(signalGroupMapping.list(mtrLineId,strVehicleTypeId, name));
        } catch (DataAccessException e) {
            logger.error("[list] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public void saveSignalGroup(SignalGroupPO signalGroupPO) {
        try {
            signalGroupPO.setId(PrimaryKeyGenerator.generatorId());
            signalGroupMapping.saveSignalGroup(signalGroupPO);
        } catch (DataAccessException e) {
            logger.error("[saveSignalGroup] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    public void updateSignalGroup(SignalGroupPO signalGroupPO) {
        try {
            signalGroupMapping.updateSignalGroup(signalGroupPO);
        } catch (DataAccessException e) {
            logger.error("[updateSignalGroup] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    @Transactional(rollbackFor = {RestApiException.class})
    public void deleteSignalGroup(String id) {
        try {
            int count = signalGroupMapping.deleteSignalGroup(id);
            if (count == 0) {
                throw new RestApiException(ExceptionInfoEnum.DATA_NOT_FOUND_EXCEPTION);
            }
            signalGroupMapping.deleteSignalGroupExtend(id);
            signalGroupMapping.deleteSignalGroupOriginal(id);
        } catch (DataAccessException e) {
            logger.error("[deleteSignalGroup] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    public PageInfo<SignalVO> listSignal(SignalGroupQueryDTO signalGroupQueryDTO) {
        try {
            PageHelper.startPage(signalGroupQueryDTO.getPageNumber(), signalGroupQueryDTO.getPageSize());
            return new PageInfo<>(signalGroupMapping.listSignal(signalGroupQueryDTO));
        } catch (DataAccessException e) {
            logger.error("[listSignal] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public PageInfo<SignalVO> listAllSignal(SignalGroupQueryDTO signalGroupQueryDTO) {
        try {
            PageHelper.startPage(signalGroupQueryDTO.getPageNumber(), signalGroupQueryDTO.getPageSize());
            return new PageInfo<>(signalGroupMapping.listAllSignal(signalGroupQueryDTO));
        } catch (DataAccessException e) {
            logger.error("[listAllSignal] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public void saveSignalGroupRelation(SignalGroupRelationPO signalGroupRelationPO) {
        try {
            signalGroupMapping.saveSignalGroupRelation(signalGroupRelationPO);
        } catch (DataAccessException e) {
            logger.error("[saveSignalGroupRelation] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    public void deleteRelation(SignalGroupDelRelationPO signalGroupDelRelationPO) {
        try {
            signalGroupMapping.deleteRelation(signalGroupDelRelationPO);
        } catch (DataAccessException e) {
            logger.error("[deleteRelation] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    /**
     * @Description
     * @Return com.github.pagehelper.PageInfo<cc.crrc.manage.pojo.comm.signalgroup.SignalGroupFaultVO>
     * <AUTHOR> wei
     * @Date 15:00 2020/6/8
     * @Param [signalGroupFaultQueryDTO]
     **/
    public SignalGroupFaultWithFlagVO listFault(SignalGroupFaultQueryDTO signalGroupFaultQueryDTO) {
        try {
            SignalGroupFaultWithFlagVO signalGroupFaultWithFlagVO = new SignalGroupFaultWithFlagVO();
            PageHelper.startPage(signalGroupFaultQueryDTO.getPageNumber(), signalGroupFaultQueryDTO.getPageSize());
            PageInfo<SignalGroupFaultVO> pageInfo = new PageInfo<>(signalGroupMapping.listFault(signalGroupFaultQueryDTO));
            List<SignalGroupFaultVO> listRelated  =signalGroupMapping.listRelated(signalGroupFaultQueryDTO.getSignalGroupId());
            signalGroupFaultWithFlagVO.setPageInfo(pageInfo);
            signalGroupFaultWithFlagVO.setFaultTypeKeys(listRelated);
            return signalGroupFaultWithFlagVO;
        } catch (DataAccessException e) {
            logger.error("[listFault] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @Description 新增信号分组与故障关联关系
     * @Return void
     * <AUTHOR> wei
     * @Date 15:56 2020/6/8
     * @Param [signalFaultGroupPO]
     **/
    public void insert(SignalFaultGroupPO signalFaultGroupPO) {
        try {
            signalGroupMapping.deleteRelatedBeforeInsert(signalFaultGroupPO.getSignalGroupId());
            if (signalFaultGroupPO.getFaultTypeKey().size() > 0) {
                signalGroupMapping.insert(signalFaultGroupPO.getSignalGroupId(), signalFaultGroupPO.getFaultTypeKey());
            }
        } catch (DataAccessException e) {
            logger.error("[insert] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }
}
