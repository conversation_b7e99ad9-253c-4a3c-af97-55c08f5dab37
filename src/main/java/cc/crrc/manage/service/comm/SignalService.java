package cc.crrc.manage.service.comm;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.mapper.comm.ProtocolMapping;
import cc.crrc.manage.mapper.comm.SignalMapping;
import cc.crrc.manage.pojo.comm.protocol.ProtocolVO;
import cc.crrc.manage.pojo.comm.signal.SignalDelDTO;
import cc.crrc.manage.pojo.comm.signal.SignalPO;
import cc.crrc.manage.pojo.comm.signal.SignalQueryDTO;
import cc.crrc.manage.pojo.comm.signal.SignalVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

/**
 * @FileName SignalService
 * <AUTHOR> shuangquan
 * @Date 2019/11/13 14:01
 **/
@Service
public class SignalService {

    private static final Logger logger = LoggerFactory.getLogger(SignalService.class);
    private static final String ORIGINAL_SIGNAL_TYPE = "0";
    private static final String EXTEND_SIGNAL_TYPE = "1";
    @Autowired
    private SignalMapping signalMapping;
    @Autowired
    private ProtocolMapping protocolMapping;

    public PageInfo<SignalVO> list(SignalQueryDTO signalQueryDTO) {
        try {
            PageHelper.startPage(signalQueryDTO.getPageNumber(), signalQueryDTO.getPageSize());
            return new PageInfo<>(signalMapping.list(signalQueryDTO));
        } catch (DataAccessException e) {
            logger.error("[list] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public void deleteSignal(SignalDelDTO signalDelDTO) {
        checkProtocol(signalDelDTO.getProtocolId());
        int count = signalMapping.deleteSignal(signalDelDTO.getSignalId().split(","), signalDelDTO.getSignalType());
        if (count == 0) {
            throw new RestApiException(ExceptionInfoEnum.DATA_NOT_FOUND_EXCEPTION);
        }
    }

    public void saveSignal(SignalPO signalPO) {
        try {
            checkSignal(signalPO);
            checkProtocol(signalPO.getProtocolId());
            int count = signalMapping.queryDuplicate(signalPO);
            if (count > 0) {
                throw new RestApiException(ExceptionInfoEnum.DATA_DUPLICATE_EXCEPTION.getErrorCode(), "信号已存在");
            }
            if (!StringUtils.isEmpty(signalPO.getMaxVal()) && !StringUtils.isEmpty(signalPO.getMinVal()) && signalPO.getMaxVal().compareTo(signalPO.getMinVal()) <= 0) {
                throw new RestApiException(ExceptionInfoEnum.PARAMETER_VALIDATION_EXCEPTION.getErrorCode(),
                        "最大值不能小于最小值");
            }
            signalPO.setId(PrimaryKeyGenerator.generatorId());
            if ("".equals(signalPO.getMinVal())) {
                signalPO.setMinVal(null);
            }
            if ("".equals(signalPO.getMaxVal())) {
                signalPO.setMaxVal(null);
            }
            if (ORIGINAL_SIGNAL_TYPE.equals(signalPO.getSignalType())) {
                signalMapping.saveOriginalSignal(signalPO);
            }
            if (EXTEND_SIGNAL_TYPE.equals(signalPO.getSignalType())) {
                signalMapping.saveExtendSignal(signalPO);
            }
        } catch (DataAccessException e) {
            logger.error("[saveoOriginalSignal] {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    public void updateSignal(SignalPO signalPO) {
        checkProtocol(signalPO.getProtocolId());
        int count = signalMapping.updateSignal(signalPO);
        if (count == 0) {
            throw new RestApiException(ExceptionInfoEnum.DATA_NOT_FOUND_EXCEPTION);
        }
    }


    private void checkSignal(SignalPO signalPO) {
        if (ORIGINAL_SIGNAL_TYPE.equals(signalPO.getSignalType())) {
            if (signalPO.getByteOffset() == null) {
                throw new RestApiException(ExceptionInfoEnum.PARAMETER_VALIDATION_EXCEPTION.getErrorCode(),
                        "字节偏移量不能为空");
            }
            if (signalPO.getBitOffset() == null) {
                throw new RestApiException(ExceptionInfoEnum.PARAMETER_VALIDATION_EXCEPTION.getErrorCode(),
                        "位偏移量不能为空");
            }
            if (!StringUtils.isEmpty(signalPO.getMaxVal()) && !StringUtils.isEmpty(signalPO.getMinVal())) {
                if (Double.parseDouble(signalPO.getMaxVal()) < Double.parseDouble(signalPO.getMinVal())) {
                    throw new RestApiException(ExceptionInfoEnum.PARAMETER_VALIDATION_EXCEPTION.getErrorCode(),
                            "信号最大值不能小于最小值");
                }
            }
        }
    }

    private void checkProtocol(String protocolId) {
        ProtocolVO protocolVO = protocolMapping.queryDetail(protocolId);
        if (protocolVO == null) {
            throw new RestApiException(ExceptionInfoEnum.DATA_NOT_FOUND_EXCEPTION.getErrorCode(), "协议不存在");
        }
        if (protocolVO.getEnable() == 1) {
            throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION.getErrorCode(), "协议已启用，无法进行此操作");
        }
    }
}
