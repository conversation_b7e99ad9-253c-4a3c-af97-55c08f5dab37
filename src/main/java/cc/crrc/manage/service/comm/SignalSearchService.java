package cc.crrc.manage.service.comm;

import cc.crrc.manage.cache.CacheUtils;
import cc.crrc.manage.cache.dict.DictCache;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.StringUtils;
import cc.crrc.manage.common.utils.tsdb.TSDBUtils;
import cc.crrc.manage.common.utils.tsdb.iotdb.IoTDBUtils;
import cc.crrc.manage.common.utils.tsdb.openTSDB.OpenTSDBUtils;
import cc.crrc.manage.common.utils.tsdb.openTSDB.pojo.OpenTSDBQueryResponse;
import cc.crrc.manage.mapper.comm.ProtocolMapping;
import cc.crrc.manage.mapper.comm.SignalMapping;
import cc.crrc.manage.mapper.mtr.MtrVehicleMapping;
import cc.crrc.manage.monitoringConfig.entity.MonitorConfigWtdSignalPO;
import cc.crrc.manage.monitoringConfig.service.TriggerConfigService;
import cc.crrc.manage.pojo.SysDictVO;
import cc.crrc.manage.pojo.comm.protocol.ProtocolVO;
import cc.crrc.manage.pojo.comm.signal.*;
import cc.crrc.manage.pojo.line.StationDTO;
import cc.crrc.manage.pojo.mtr.MtrVehicleDTO;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import cc.crrc.manage.pojo.mtr.MtrVehicleTypeVO;
import cc.crrc.manage.pojo.mtr.MtrVehicleVO;
import cc.crrc.manage.service.line.LineService;
import cc.crrc.manage.service.mtr.MtrVehicleService;
import cc.crrc.manage.service.mtr.MtrVehicleTypeService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR> GuoYang
 * 2020/3/2
 **/
@Service
public class SignalSearchService {
    @Autowired
    private MtrVehicleTypeService mtrVehicleTypeService;
    @Autowired
    private MtrVehicleService mtrVehicleService;
    @Autowired
    private SignalMapping signalMapping;
    @Autowired
    private MtrVehicleMapping mtrVehicleMapping;
    @Autowired
    private TSDBUtils tsdbUtils;

    private final Logger logger = LoggerFactory.getLogger(SignalSearchService.class);

    static final String START_TIME_RES_KEY = "startTime";
    static final String END_TIME_RES_KEY = "endTime";
    static final long ONE_SECOND = 1000L;
    static final long ONE_MINUTE = 60 * ONE_SECOND;
    static final long ONE_HOUR = 60 * ONE_MINUTE;
    static final long DEFAULT_INTERVAL = 60;


    /**
     * 查询车型列表
     *
     * @return java.util.List<cc.crrc.manage.pojo.mtr.MtrVehicleTypeVO>
     * <AUTHOR> GuoYang
     * 2020/3/2
     **/
    public List<MtrVehicleTypeVO> listVehicleType() {
        try {
            return mtrVehicleTypeService.listVehicleType().getList();
        } catch (DataAccessException e) {
            logger.error("Method[listVehicleType] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 根据车型id，查询车辆列表
     *
     * @param vehicleTypeId 车型id
     * @return java.util.List<cc.crrc.manage.pojo.mtr.MtrVehicleVO>
     * <AUTHOR> GuoYang
     * 2020/3/2
     **/
    public List<MtrVehiclePO> listVehicleByVehicleTypeId(String vehicleTypeId) {
        try {
            return mtrVehicleService.listVehicleByTypeId(vehicleTypeId);
        } catch (DataAccessException e) {
            logger.error("Method[listVehicleByVehicleTypeId] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 根据关键字，对信号进行模糊查询，并返回筛选后的信号树
     *
     * @param vehicleTypeId 车型id
     * @param searchWord    搜索关键字
     * @return java.util.ArrayList<cc.crrc.manage.pojo.comm.signal.SignalTreeVO>
     * <AUTHOR> GuoYang
     * 2020/3/9
     **/
    public ArrayList<SignalTreeVO> getSignalTreeInSearch(String vehicleTypeId, String searchWord) {
        try {
            ArrayList<SignalTreeVO> res = new ArrayList<>();
            List<SignalTreeVO> list = signalMapping.listSignalByVehicleTypeId4Tree(vehicleTypeId, null, null, searchWord);
            for (SignalTreeVO signalTreeVO : list) {
                String subsystemName = "其他";
                String subsystem = "其他";
                String location = "ALL";
                if (!StringUtils.isEmpty(signalTreeVO.getSubsystemName())) {
                    subsystemName = signalTreeVO.getSubsystemName();
                }
                if (!StringUtils.isEmpty(signalTreeVO.getSubsystem())) {
                    subsystem = signalTreeVO.getSubsystem();
                }
                if (!StringUtils.isEmpty(signalTreeVO.getLocation())) {
                    location = signalTreeVO.getLocation();
                }
                if (StringUtils.isEmpty(signalTreeVO.getLocation())) {
                    continue;
                }

                SignalTreeVO locationObject = getElemByName(res, location, location);
                ArrayList<SignalTreeVO> systemArray = locationObject.getChildren();
                SignalTreeVO systemObject = getElemByName(systemArray, subsystemName, location + "-" + subsystem);
                ArrayList<SignalTreeVO> signalArray = systemObject.getChildren();
                signalArray.add(signalTreeVO);
            }
            return res;
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 懒加载获取信号树
     *
     * @param vehicleTypeId 车辆类型Id
     * @param parentNodeId  树形结构的节点id 规则：车厢名-系统值-信号id 例：Mp1-C-31005
     * @return java.util.ArrayList<cc.crrc.manage.pojo.comm.signal.SignalTreeVO>
     * <AUTHOR> GuoYang
     * 2020/3/9
     **/
    public ArrayList<SignalTreeVO> getSignalTreeLazy(String vehicleTypeId, String parentNodeId) {
        ArrayList<SignalTreeVO> signalTree = new ArrayList<>();
        // 切分nodeId，判断树形结构级别
        String[] parentNodeArray = StringUtils.isNotEmpty(parentNodeId) ? parentNodeId.split("-") : new String[]{};
        // 默认信号树为三级 再向下请求 无数据
        if (parentNodeArray.length > 2) {
            return signalTree;
        }
        String searchLocation = parentNodeArray.length > 0 ? parentNodeArray[0] : null;
        String searchSystem = parentNodeArray.length > 1 ? parentNodeArray[1] : null;
        // 查询符合条件的信号
        List<SignalTreeVO> list = signalMapping.listSignalByVehicleTypeId4Tree(vehicleTypeId, searchLocation, searchSystem, null);
        // 进行去重、封装
        for (SignalTreeVO signalTreeVO : list) {
            SignalTreeVO resElem;
            String resElemName;
            if (searchLocation == null) {
                // 查询车厢列表
                String location = signalTreeVO.getLocation();
                String nodeId = location;
                if (StringUtils.isEmpty(location)) {
                    location = "其他位置";
                    nodeId = "unknown";
                }
                resElemName = location;
                resElem = new SignalTreeVO();
                resElem.setName(location);
                resElem.setNodeId(nodeId);
            } else if (searchSystem == null) {
                // 查询车厢下的系统列表
                String subsystemName = signalTreeVO.getSubsystemName();
                String location = signalTreeVO.getLocation();
                String nodeId = location + "-" + signalTreeVO.getSubsystem();
                if (StringUtils.isEmpty(subsystemName)) {
                    subsystemName = "其他系统";
                    nodeId = location + "-unknown";
                }
                resElemName = subsystemName;
                resElem = new SignalTreeVO();
                resElem.setName(subsystemName);
                resElem.setNodeId(nodeId);
            } else {
                // 查询车厢下的系统下的信号列表
                resElemName = signalTreeVO.getName();
                resElem = signalTreeVO;
            }
            // 已经添加过的，不重复添加
            if (hasElemNameInList(signalTree, resElemName)) {
                continue;
            }
            signalTree.add(resElem);
        }
        return signalTree;
    }

    /**
     * 查找信号树中，是否存在name属性为targetName的元素
     *
     * @param signalTree 信号树列表
     * @param targetName 目标元素的name值
     * @return boolean
     * <AUTHOR> GuoYang
     * 2020/3/3
     **/
    public boolean hasElemNameInList(ArrayList<SignalTreeVO> signalTree, String targetName) {
        for (SignalTreeVO elem : signalTree) {
            String nameInElem = elem.getName();
            if (targetName.equals(nameInElem)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 从信号树列表中中获取name字段为targetName的元素，并返回。
     * 若信号树中无期望元素，则新插入一个，并返回。
     * 新插入元素的nodeId应为defaultNodeId。
     *
     * @param signalTree    信号树
     * @param targetName    待查询元素中name字段的值
     * @param defaultNodeId 待查询元素中nodeId字段的值
     * @return cc.crrc.manage.pojo.comm.signal.SignalTreeVO
     * <AUTHOR> GuoYang
     * 2020/3/9
     **/
    public SignalTreeVO getElemByName(ArrayList<SignalTreeVO> signalTree, String targetName, String defaultNodeId) {
        for (SignalTreeVO elem : signalTree) {
            String nameInElem = elem.getName();
            if (targetName.equals(nameInElem)) {
                return elem;
            }
        }
        SignalTreeVO newElem = new SignalTreeVO();
        newElem.setName(targetName);
        newElem.setNodeId(defaultNodeId);
        signalTree.add(newElem);
        return newElem;
    }

    /**
     * 查询信号密度
     * @param signalLineQueryDTO
     * @return
     * <AUTHOR> 2022-11-09
     */
    public List<HashMap<String, Long>> getSignalInterval(SignalLineQueryDTO signalLineQueryDTO) {
        List<HashMap<String, Long>> res = new ArrayList<>();
        try {
            if (tsdbUtils instanceof IoTDBUtils) {
                final long startTime = signalLineQueryDTO.getStartTime();
                final long endTime = signalLineQueryDTO.getEndTime();
                res = getSignalIntervalFromIoTDB(startTime, endTime, signalLineQueryDTO.getVehicleCode(), signalLineQueryDTO.getSignalIdList());
            }
        } catch (Exception e) {
            logger.error("method[getSignalInterval] error {}",e.getMessage());
        }
        return res;
    }

    /**
     * 返回分段后的信号密度集合
     * @param startTime 信号查询开始时间
     * @param endTime 信号查询结束时间
     * @param vehicleCode 车辆编码
     * @param signalIdList 查询信号id集合，以","分隔
     * @return
     * <AUTHOR> 2022-11-09
     */
    private List<HashMap<String, Long>> getSignalIntervalFromIoTDB(final long startTime, final long endTime, String vehicleCode, final String signalIdList) {
        ArrayList<ArrayList<String>> dbQueryResult = querySignalIntervalFromIoTDB(startTime, endTime, vehicleCode, signalIdList);
        if (dbQueryResult == null) {
            return new ArrayList<>();
        }
        String[] signalIdArray = signalIdList.split(",");
        return intervalResultWrapper(dbQueryResult, startTime, endTime, signalIdArray.length);
    }

    /**
     * 返回信号密度
     * @param startTime 信号查询开始时间
     * @param endTime 信号查询结束时间
     * @param vehicleCode 车辆编码
     * @param signalIdList 查询信号id集合，以","分隔
     * @return
     * <AUTHOR> 2022-11-09
     */
    private ArrayList<ArrayList<String>> querySignalIntervalFromIoTDB(final long startTime, final long endTime, String vehicleCode,final String signalIdList) {
        String sqlModel = "select beginTime, endTime FROM root.interval.sg.{0} where beginTime  <= {2,number,#} and endTime  >= {1,number,#}";
        String sql = MessageFormat.format(sqlModel, vehicleCode, startTime, endTime);
        IoTDBUtils ioTDBUtils = (IoTDBUtils) tsdbUtils;
        ArrayList<ArrayList<String>> dbQueryResult = ioTDBUtils.query2IoTDB(sql);
        ArrayList<String> lastTimeOfEndTimeIsNull = querySignalIntervalLastBeginTimeFromIoTDB(startTime, endTime, vehicleCode, signalIdList);
        if (lastTimeOfEndTimeIsNull.size() != 0){
            dbQueryResult.add(lastTimeOfEndTimeIsNull);
        }
        return dbQueryResult;
    }

    /**
     * 处理interval中endTime为null的情况
     * @param startTime 信号查询开始时间
     * @param endTime 信号查询结束时间
     * @param vehicleCode 车辆编码
     * @param signalIdList 查询信号id集合，以","分隔
     * @return
     * <AUTHOR> 2022-11-09
     */
    private ArrayList<String> querySignalIntervalLastBeginTimeFromIoTDB(final long startTime,
                                                           final long endTime,
                                                           String vehicleCode,
                                                           String signalIdList) {
        IoTDBUtils ioTDBUtils = (IoTDBUtils) tsdbUtils;
        String sql = "select last beginTime,endTime from root.interval.sg." + vehicleCode;
        ArrayList<ArrayList<String>> result = ioTDBUtils.query2IoTDB(sql);
        ArrayList<String> endValue = new ArrayList<>();
        if(result.size() == 0) {
            return endValue;
        }
        long lastBeginTime = Long.parseLong(result.get(0).get(2));
        long lastEndTime = querySignalRealEndTimeFromIoTDB(vehicleCode,signalIdList);
        if (result.size() >= 1 && lastBeginTime <= endTime && lastEndTime >= startTime){
            endValue.add("" + lastBeginTime);
            endValue.add("" + lastBeginTime);
            endValue.add("" + lastEndTime);
        }
        return endValue;
    }

    /**
     * 查询origin表中信号最后的time
     * @param vehicleCode
     * @param signalIdList
     * @return
     * <AUTHOR> 2022-11-09
     */
    private long querySignalRealEndTimeFromIoTDB(String vehicleCode, String signalIdList) {
        MtrVehiclePO mtrVehiclePO = mtrVehicleMapping.getVehicleByCode(vehicleCode);
        String sql = "select last " + signalIdList + " from root.origin.sg" + mtrVehiclePO.getVehicleStorageGroup() + "." + vehicleCode;
        IoTDBUtils ioTDBUtils = (IoTDBUtils) tsdbUtils;
        ArrayList<ArrayList<String>> dbQueryResult = ioTDBUtils.query2IoTDB(sql);
        if (!dbQueryResult.isEmpty()) {
            String max = dbQueryResult.stream().map(o -> o.get(0)).distinct().max(String::compareTo).orElse("0");
            return Long.parseLong(max);
        }
        return 0;
    }

    //信号分段大小 缓存中的key值
    private final static String SIGNALE_QUERY_SEGMENT_DICT_KEY = "signal_query_segment_24_129";

    /**
     * 将iotdb查询到的数据间隔进行封装。
     *
     * @param dbQueryResult 查询得到的数据集
     * @return java.util.ArrayList<java.lang.String [ ]>
     * <AUTHOR> GuoYang
     * 2020/6/12
     **/
    private List<HashMap<String, Long>> intervalResultWrapper(ArrayList<ArrayList<String>> dbQueryResult,
                                                              long startTime,
                                                              long endTime,
                                                              int signalLength) {
        List<HashMap<String, Long>> res = new ArrayList<>();
        //处理分段间隔
        List<SysDictVO> list = CacheUtils.getValue(DictCache.class, SIGNALE_QUERY_SEGMENT_DICT_KEY);
        String interval = list.stream()
                .filter(i -> {
                    String[] replace = i.getLabel().split("-");
                    return replace.length == 2 && Integer.parseInt(replace[0]) <= signalLength && Integer.parseInt(replace[1]) >= signalLength;
                })
                .map(SysDictVO::getValue)
                .findFirst().orElse(String.valueOf(DEFAULT_INTERVAL));
        for (ArrayList<String> rowInfo : dbQueryResult) {
            String startStr = rowInfo.get(1);
            String endStr = rowInfo.get(2);
            if (startStr == null || endStr == null) {
                continue;
            }
            long originStartTime = Long.parseLong(startStr);
            long originEndTime = Long.parseLong(endStr);
            originStartTime = Math.max(originStartTime, startTime);
            originEndTime = Math.min(originEndTime, endTime);
            timeToCut(res, originStartTime, originEndTime, Long.parseLong(interval) * ONE_MINUTE);
        }
        return res;
    }

    /**
     * 信号密度动态分段
     * @param res 结果集合
     * @param originStartTime 原时间密度起始时间
     * @param originEndTime 原时间密度结束时间
     * @param interval 分段大小
     */
    private void timeToCut(List<HashMap<String, Long>> res, long originStartTime, long originEndTime, final long interval) {
        long startTime = originStartTime;
        while (interval != 0 && (originEndTime - startTime) > interval){
            HashMap<String, Long> timeRange = new HashMap<>(2);
            timeRange.put(START_TIME_RES_KEY, startTime);
            startTime += interval;
            timeRange.put(END_TIME_RES_KEY, startTime);
            res.add(timeRange);
        }
        HashMap<String, Long> timeRange = new HashMap<>(2);
        timeRange.put(START_TIME_RES_KEY, startTime);
        timeRange.put(END_TIME_RES_KEY, originEndTime);
        res.add(timeRange);
    }

    /**
     * 先获取站点信息，再查询信号历史数据并与站点信息进行整合
     *
     * @param signalLineQueryDTO 信号查询条件DTO
     * @return com.alibaba.fastjson.JSONArray
     * <AUTHOR> GuoYang
     * 2020/3/5
     * <p>
     * 取消车辆站点信息查询
     * <AUTHOR> GuoYang
     * 2021/8/6
     **/
    public JSONObject getSignalHistoryLine(SignalLineQueryDTO signalLineQueryDTO) {
        JSONObject res = new JSONObject();
        if (tsdbUtils instanceof IoTDBUtils) {
            res = getSignalHistoryFromIotDB(signalLineQueryDTO);
        }
        return res;
    }

    /**
     * 从IotDB中，查询信号历史数据
     *
     * @param signalLineQueryDTO 信号查询条件DTO
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR> GuoYang
     * 2020/6/10
     * <p>
     * 取消车辆站点信息处理
     * <AUTHOR> GuoYang
     * 2021/8/6
     **/
    private JSONObject getSignalHistoryFromIotDB(SignalLineQueryDTO signalLineQueryDTO) {
        JSONObject res = new JSONObject();
        JSONArray lines = new JSONArray();
        JSONArray station = new JSONArray();
        // 查询openTSDB数据库
        ArrayList<ArrayList<String>> dbQueryResult = querySignalHistoryFromIoTDB(signalLineQueryDTO);
        String[] idArr = signalLineQueryDTO.getSignalIdList().split(",");
        ArrayList<OpenTSDBQueryResponse> openTSDBQueryResponseList = dbQueryResult2OpenTSDBQueryResponse(dbQueryResult, idArr);
        for (OpenTSDBQueryResponse queryResponse : openTSDBQueryResponseList) {
            JSONObject aResult = new JSONObject();
            String vid = queryResponse.getVId();
            aResult.put("key", vid);
            JSONArray varValue = OpenTSDBUtils.formatOpenTSDBDps(queryResponse);
            aResult.put("value", varValue);
            lines.add(aResult);
        }
        res.put("lines", lines);
        res.put("station", station);
        return res;
    }

    /**
     * 从iotdb数据库中查询目标区间内的信号数据
     *
     * @param signalLineQueryDTO 信号查询条件DTO
     * @return java.util.ArrayList<java.util.ArrayList < java.lang.String>>
     * <AUTHOR> GuoYang
     * 2020/6/15
     **/
    private ArrayList<ArrayList<String>> querySignalHistoryFromIoTDB(SignalLineQueryDTO signalLineQueryDTO) {
        long endTime = signalLineQueryDTO.getEndTime();
        long startTime = signalLineQueryDTO.getStartTime();
        String vehicleCode = signalLineQueryDTO.getVehicleCode();
        String[] idArr = signalLineQueryDTO.getSignalIdList().split(",");
        Boolean sample = signalLineQueryDTO.getSample();
        MtrVehiclePO mtrVehiclePO = mtrVehicleMapping.getVehicleByCode(vehicleCode);
        int vehicleStorageGroup = mtrVehiclePO.getVehicleStorageGroup();
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < idArr.length; i++) {
//            if (sample) {
//                stringBuilder.append("last_value(").append(idArr[i]).append(")");
//            } else {
            stringBuilder.append(idArr[i]);
//            }
            if (i != idArr.length - 1) {
                stringBuilder.append(",");
            }
        }
        String selectKey = stringBuilder.toString();
        String sql = null;
        if (sample) {
            String sampleSqlModel = "SELECT {0} FROM root.sample.sg.{1} where time <= {4,number,#} and time >= {3,number,#}";
            sql = MessageFormat.format(sampleSqlModel, selectKey, vehicleCode, vehicleStorageGroup, startTime, endTime);
        } else {
            String originSqlModel = "SELECT {0} FROM root.origin.sg{2,number,#}.{1} where time <= {4,number,#} and time >= {3,number,#}";
            sql = MessageFormat.format(originSqlModel, selectKey, vehicleCode, vehicleStorageGroup, startTime, endTime);
        }
        IoTDBUtils ioTDBUtils = (IoTDBUtils) tsdbUtils;
        return ioTDBUtils.query2IoTDB(sql);
    }

    /**
     * 将iotdb查询到的数据封装成OpenTSDBQueryResponse类，便于复用opentsdb的处理函数。
     * （可将opentsdb和iotdb的处理函数改为中性通用函数）
     *
     * @param dbQueryResult iotdb查询到的数据
     * @param idArr         带查询的信号id组
     * @return java.util.ArrayList<cc.crrc.manage.common.utils.OpenTSDB.pojo.OpenTSDBQueryResponse>
     * <AUTHOR> GuoYang
     * 2020/6/15
     **/
    private ArrayList<OpenTSDBQueryResponse> dbQueryResult2OpenTSDBQueryResponse(ArrayList<ArrayList<String>> dbQueryResult, String[] idArr) {
        ArrayList<OpenTSDBQueryResponse> res = new ArrayList<>();
        ArrayList<HashMap<String, Double>> tempMapList = new ArrayList<>();
        for (String ignored : idArr) {
            tempMapList.add(new HashMap<>());
        }
        for (ArrayList<String> rowInfo : dbQueryResult) {
            String timeStamp = rowInfo.get(0);
            for (int i = 1; i < rowInfo.size(); i++) {
                String signalValue = rowInfo.get(i);
                if (signalValue != null) {
                    if ("false".equalsIgnoreCase(signalValue)) {
                        signalValue = "0";
                    }
                    if ("true".equalsIgnoreCase(signalValue)) {
                        signalValue = "1";
                    }
                    tempMapList.get(i - 1).put(timeStamp, Double.valueOf(signalValue));
                }
            }
        }
        for (int i = 0; i < idArr.length; i++) {
            OpenTSDBQueryResponse signalValues = new OpenTSDBQueryResponse();
            signalValues.setVId(idArr[i]);
            signalValues.setDps(tempMapList.get(i));
            res.add(signalValues);
        }
        return res;
    }

    /**
     * 将时序库中查询得到的站点信号数据进行包装处理。
     *
     * @param queryResponse  时序库中，对于站点信号查询的返回
     * @param stationCodeMap 站点code和站点名称的映射表
     * @param endTime        本次信号查询区间的结束时间，用于处理最后一包数据
     * @return com.alibaba.fastjson.JSONArray
     * <AUTHOR> GuoYang
     * 2020/3/9
     **/
    private JSONArray processStationSignal(OpenTSDBQueryResponse queryResponse, HashMap<String, String> stationCodeMap, Long endTime) {
        JSONArray res = new JSONArray();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        HashMap<String, Double> dpsMap = queryResponse.getDps();
        TreeSet<String> keySet = new TreeSet<>(dpsMap.keySet());
        Double lastSignal = (double) 0;
        String lastTimeInStation = "0";
        Double tempEndStation = (double) 0;
        JSONObject tempSection = new JSONObject();
        for (String key : keySet) {
            Double currentSignal = dpsMap.get(key);
            if (!currentSignal.equals((double) 0)) {
                if (!lastSignal.equals(currentSignal)) {
                    if (!tempEndStation.equals(currentSignal)) {
                        if (!tempSection.isEmpty()) {
                            String stationCode = String.valueOf(tempEndStation.intValue());
                            String stationName = stationCodeMap.getOrDefault(stationCode, stationCode);
                            tempSection.put("stationName", stationName);
                            res.add(tempSection);
                            tempSection = new JSONObject();
                        }
                        tempSection.put(START_TIME_RES_KEY, sdf.format(new Date(Long.parseLong(key))));
                    }
                }
                lastTimeInStation = key;
            } else {
                if (!lastSignal.equals(currentSignal)) {
                    tempSection.put(END_TIME_RES_KEY, sdf.format(new Date(Long.parseLong(lastTimeInStation))));
                    tempEndStation = lastSignal;
                }
            }
            lastSignal = currentSignal;
        }
        if (!tempSection.isEmpty()) {
            Double lastStation = tempEndStation;
            if (!tempSection.containsKey(END_TIME_RES_KEY)) {
                tempSection.put(END_TIME_RES_KEY, sdf.format(new Date(endTime)));
                lastStation = lastSignal;
            }
            String stationCode = String.valueOf(lastStation.intValue());
            String stationName = stationCodeMap.getOrDefault(stationCode, stationCode);
            tempSection.put("stationName", stationName);
            res.add(tempSection);
        }
        return res;
    }

    /**
     * 将从数据库中查询得到的站点信息，处理成站点code和站点名称的映射表，并返回。
     *
     * @param stationDTOList 数据库中查询得到的站点信息列表
     * @return java.util.HashMap<java.lang.String, java.lang.String>
     * <AUTHOR> GuoYang
     * 2020/3/9
     **/
    private HashMap<String, String> getStationCodeMap(List<StationDTO> stationDTOList) {
        HashMap<String, String> res = new HashMap<>(64);
        for (StationDTO stationDTO : stationDTOList) {
            String stationCode = stationDTO.getStationCode().toString();
            String name = stationDTO.getName();
            res.put(stationCode, name);
        }
        return res;
    }

    public XSSFWorkbook exportSignalExcel(SignalLineQueryDTO signalLineQueryPO) {
        //创建一个excel文件
        XSSFWorkbook xssfWorkbook = new XSSFWorkbook();
        //从idotdb获取信号数据，因为idotdb取出的数据本身就是表格状的数据，因此并未使用查询后封装好的数据
        ArrayList<ArrayList<String>> res = querySignalHistoryFromIoTDB(signalLineQueryPO);
        //创建工作簿
        XSSFSheet sheet = xssfWorkbook.createSheet();
        sheet.setDefaultColumnWidth(15);
        sheet.setDefaultRowHeight((short) 500);
        //第一行数据展示收索条件
        XSSFRow title = sheet.createRow(0);
        title.createCell(0).setCellValue(signalLineQueryPO.getLineName());
        title.createCell(1).setCellValue(signalLineQueryPO.getVehicleType());
        title.createCell(2).setCellValue(signalLineQueryPO.getVehicleCode());
        Date startTime = new Date(signalLineQueryPO.getStartTime());
        Date endTime = new Date(signalLineQueryPO.getEndTime());
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String time = df.format(startTime) + "~" + df.format(endTime);
        title.createCell(3).setCellValue(time);
        if (signalLineQueryPO.getSample()) {
            title.createCell(4).setCellValue("抽样数据");
        } else {
            title.createCell(4).setCellValue("原始数据");
        }
        if (signalLineQueryPO.getInterval() != null) {
            title.createCell(5).setCellValue("间隔：" + signalLineQueryPO.getInterval());
        }
        if (res != null) {
            //第二行数据填写表头
            XSSFRow titleRow = sheet.createRow(1);
            //查询出id对应信号名称
            List<Map<String, Object>> nameList = signalMapping.getNameCnByIds(signalLineQueryPO.getSignalIdList().split(","));
            //第二行第一格固定填写time
            titleRow.createCell(0).setCellValue("time");
            //第二格开始按查询顺序填入信号对应的名称
            String[] arr = signalLineQueryPO.getSignalIdList().split(",");
            for (int i = 0; i < arr.length; i++) {
                //遍历名称id键值列表，按照传入信号id顺序，在表格中依次填入相应信号的名称
                for (int j = 0; j < nameList.size(); j++) {
                    if (nameList.get(j).get("id").equals(arr[i])) {
                        titleRow.createCell(i + 1).setCellValue(nameList.get(j).get("namecn").toString());
                    }
                }
            }
            //将idotdb中查询出的信号数据，填入表格中，因为查询出的值展示顺序和信号源顺序相同，因此只需依次填入即可
            for (int j = 0; j < res.size(); j++) {
                int lastRowNum = sheet.getLastRowNum();
                XSSFRow data1Row = sheet.createRow(lastRowNum + 1);
                for (int i = 0; i < res.get(j).size(); i++) {

                    //2021-12-08 lixin 根据测试要求增加时间戳 格式化处理 时间戳处理
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss.SSS");
                    if (0 == i) {
                        Date date = new Date(Long.parseLong(res.get(j).get(i)));
                        res.get(j).set(i, sdf.format(date));
                    }

                    XSSFCell cell1 = data1Row.createCell(i);
                    cell1.setCellValue(res.get(j).get(i));
                }
            }
        }
        return xssfWorkbook;
    }

    public Object getSignalBySysGroup(String vehicleCode, String carList) {
        try {
            List<String> cars = Arrays.asList(carList.split(","));
            //第一步：查询出车辆、车厢list条件筛选下 拥有的系统个数
            List<HashMap<String, Object>> sysList = signalMapping.getSysListForSignalSearch(vehicleCode, cars);
            return sysList;
        } catch (Exception e) {
            logger.error("Method[getSignalBySysGroup] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object structureList(String vehicleCode) {
        List<String> structureList;
        try {
            structureList = signalMapping.structureList(vehicleCode);
            return structureList;
        } catch (Exception e) {
            logger.error("Method[structureList] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object findLikeWtdSignalByInputName(String vehicleCode, String inputName) {
        List<MonitorConfigWtdSignalPO> wtdSignalPOList = new ArrayList<>();
        try {
            String protocolId = signalMapping.selectProtocolIdByVehicleCode(vehicleCode);
            if (null == protocolId) {
                return null;
            }
            wtdSignalPOList = signalMapping.findLikeWtdSignalByInputName(inputName, protocolId);
        } catch (Exception e) {
            logger.error("Method[findLikeWtdSignalByInputName] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION, "信号变量名称查询失败");
        }
        return wtdSignalPOList;
    }


    public Object getSignalBySysAndLocation(String vehicleCode, String carList,
                                            String subsystem, String inputName) {
        try {
            List<String> cars = Arrays.asList(carList.split(","));
            List<SignalVO> list = signalMapping.signalListBySysAndLocation(vehicleCode, subsystem, cars, inputName);
            return list;
        } catch (Exception e) {
            logger.error("Method[getSignalBySysAndLocation] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }
}
