package cc.crrc.manage.service;

import cc.crrc.manage.common.utils.Constants;
import cc.crrc.manage.mapper.AuthenticationMapping;
import cc.crrc.manage.security.UserDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @FileName AuthenticationUserDetailService
 * <AUTHOR> shuangquan
 * @Date 2019/9/26 14:12
 **/
@Service("AuthenticationUserDetailService")
public class AuthenticationUserDetailService implements UserDetailsService {
    @Autowired
    private AuthenticationMapping authenticationMapping;

    @Override
    public UserDetail loadUserByUsername(String username) throws UsernameNotFoundException {
        UserDetail user = authenticationMapping.loadUserByUsername(username);
        if (user == null) {
            throw new UsernameNotFoundException(Constants.USER_NAME_NOT_FOUND);
        }
        //List<PathGrantedAuthority> authorityList = authenticationMapping.loadUserAuthority(user.getRoleId());
        //user.setAuthorities(authorityList);
        return user;
    }

    public List<Long> loadOrganization(Long userId) {
        return authenticationMapping.loadOrganization(userId);
    }

    public List<Long> loadSelfOrganization(Long userId) {
        return authenticationMapping.loadSelfOrganization(userId);
    }
}
