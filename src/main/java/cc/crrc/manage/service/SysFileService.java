package cc.crrc.manage.service;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.controller.SysFileController;
import cc.crrc.manage.mapper.SysFileMapping;
import cc.crrc.manage.pojo.SysFilePO;
import com.github.pagehelper.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysFileService {

    private static Logger logger = LoggerFactory.getLogger(SysFileController.class);
    @Autowired
    private SysFileMapping sysFileMapping;


    /**
     * 添加文件信息至sys_file表。
     * 删除标志不传时，默认为0。
     *
     * <AUTHOR>
     * 2019/11/13
     **/
    public String addSysFile(SysFilePO sysFilePO) {
        try {
            String currentId = UserUtils.getUserId();
            sysFilePO.setId(PrimaryKeyGenerator.generatorId());
            sysFilePO.setCreateBy(currentId);
            sysFilePO.setModifyBy(currentId);
            if (sysFilePO.getDelFlag() == null) {
                sysFilePO.setDelFlag(0);
            }
            sysFileMapping.addSysFile(sysFilePO);
            return sysFilePO.getId();
        } catch (DataAccessException e) {
            logger.error("Method[addSysFile] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }
    /**
     * @return
     * @Description 通过fileId 删除文件记录
     * <AUTHOR>
     * @Date 11:08 2019/11/15
     * @Param [fileId]
     **/
    public Object deleteSysFile(String fileId) {
        try {
            return sysFileMapping.deleteSysFile(fileId);
        } catch (DataAccessException e) {
            logger.error("Method[deleteSysFile] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }
    /**
     * @return List<SysFilePO>
     * @Description 通过故障类型主键faultTypeKey 查询文件list
     * <AUTHOR>
     * @Date 11:08 2019/11/14
     * @Param [faultTypeKey]
     **/
    public List<SysFilePO> getSysFileListByFaultTypeKey(String faultTypeKey){
        try {
            if(StringUtil.isEmpty(faultTypeKey)){
                throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(),"faultTypeKey为空!");
            }
            return sysFileMapping.getSysFileListByFaultTypeKey(faultTypeKey);
        } catch (DataAccessException e) {
            logger.error("Method[getSysFileListByFaultTypeKey] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }

    }

}
