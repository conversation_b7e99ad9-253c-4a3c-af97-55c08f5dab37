package cc.crrc.manage.service.line;

import cc.crrc.manage.common.annotation.group.Insert;
import cc.crrc.manage.common.annotation.group.Update;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.StringUtils;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.SysOrganizationMapping;
import cc.crrc.manage.mapper.line.LineMapping;
import cc.crrc.manage.pojo.SysOrganizationPO;
import cc.crrc.manage.pojo.line.LineDTO;
import cc.crrc.manage.pojo.line.OrgMetroLineDTO;
import cc.crrc.manage.pojo.line.StationDTO;
import com.alibaba.fastjson.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import javax.validation.*;
import java.util.*;

@Service
public class LineService {
    @Autowired
    private LineMapping lineMapping;
    @Autowired
    private SysOrganizationMapping organizationMapping;

    /**
     * @Description 新增线路
     * @Return java.lang.Object
     * @Date 10:35 2020/8/12
     * @Param [lineDTO]
     **/
    @Transactional(rollbackFor = Exception.class)
    public Object addLines(LineDTO line) {
        int result;
        try {
            line.setId(PrimaryKeyGenerator.generatorId());
            line.setCreateBy(UserUtils.getUser().getId());
            result = lineMapping.addLine(line);
            //添加线路与组织部门关系
            addOrgMetroLine(line);
        } catch (Exception e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
        if (result == 0) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
        return result;
    }

    /**
     * @Description 删除线路（逻辑删）
     * @Return java.lang.Object
     * @Date 10:43 2020/8/12
     * @Param [lineDTO]
     **/
    @Transactional(rollbackFor = Exception.class)
    public Object deleteLines(String id) {
        int result;
        try {
            String userId = UserUtils.getUser().getId();
            result = lineMapping.deleteLine(id, userId);
            //删除线路的组织关系
            if (result != 0) {
                lineMapping.deleteOrganizationLine(id);
            }
            //删除线路下所有站点信息
            StationDTO stationDTO = new StationDTO();
            stationDTO.setMetroLineId(id);
            List<StationDTO> list = lineMapping.getStations(stationDTO);
            for(StationDTO sta:list){
                lineMapping.deleteStation(sta);
            }
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
        return result;
    }

    /**
     * @Description 修改线路
     * @Return java.lang.Object
     * @Date 10:54 2020/8/12
     * @Param [lineDTO]
     **/
    @Transactional(rollbackFor = Exception.class)
    public Object updateLines(LineDTO line) {
        int result;
        try {
            line.setModifyBy(UserUtils.getUser().getId());
            result = lineMapping.updateLine(line);
            if (result != 0) {
                //删除原有的线路与组织部门关系
                lineMapping.deleteOrganizationLine(line.getId());
                //更新线路与组织部门关系
                addOrgMetroLine(line);
            }
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
        return result;
    }

    /**
     * @Description 线路列表
     * @Return java.lang.Object
     * @Date 11:11 2020/8/12
     * @Param [lineDTO]
     **/
    public Object getLines(LineDTO lineDTO) {
        List<SysOrganizationPO> organizationList = organizationMapping.listOrganizationsUser(UserUtils.getUserId());
        return lineMapping.getLines(lineDTO, organizationList, UserUtils.getUser().getId());
    }

    //地域
    public Object getLocations() {
        return lineMapping.getLocations();
    }

    /**
     * @Description 添加部门和线路关系
     * @Return java.lang.Object
     * @Date 10:39 2020/8/12
     * @Param [lineDTO]
     **/
    public void addOrgMetroLine(LineDTO line) {
        try {
            String organizationsId = line.getOrganizationsId();
            OrgMetroLineDTO metroLine = new OrgMetroLineDTO();
            if (StringUtils.isNotEmpty(organizationsId)) {
                String[] orgIdArr = organizationsId.split(",");
                if (orgIdArr.length > 0) {
                    for (String orgId : orgIdArr) {
                        metroLine.setOrganizationId(orgId);
                        metroLine.setMetroLineId(line.getId());
                        lineMapping.addOrgMetroLine(metroLine);
                    }
                }
            }
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "线路组织部门关联失败！");
        }
    }

    //站点（线路监控）
    public List<StationDTO> getStations(StationDTO stationDTO) {
        LineDTO line = lineMapping.getLineById(stationDTO.getMetroLineId());
        if("2".equals(line.getDirectionStatus())){//不需要区分上下行
            stationDTO.setDirection("normal");
        }else {//需要区分上下行(默认只取上行)
            stationDTO.setDirection("up");
        }return lineMapping.getStations(stationDTO);
    }

    //站点（站点配置）
    public List<StationDTO> getAllStations(StationDTO stationDTO) {
        if("2".equals(stationDTO.getDirectionStatus())){//不需要区分上下行
            stationDTO.setDirection("normal");
            List<StationDTO> list = lineMapping.getStations(stationDTO);
            //为方便前端传参，对此处数据进行整理
            List<StationDTO> resultList = new ArrayList<>();
            for(StationDTO station:list){
                List<StationDTO> list1 = new ArrayList<>();
                list1.add(station);
                StationDTO sta = new StationDTO();
                sta.setStations(list1);
                resultList.add(sta);
            }
            return resultList;
        }else {//需要区分上下行
            List<StationDTO> resultList = new ArrayList<>();
            stationDTO.setDirection("up");
            List<StationDTO> list1 = lineMapping.getStations(stationDTO);
            stationDTO.setDirection("down");
            List<StationDTO> list2 = lineMapping.getStations(stationDTO);
            for(StationDTO stationDTO1:list1) {
                int code1 = stationDTO1.getStationCode();
                StationDTO sta = new StationDTO();
                List<StationDTO> list = new ArrayList<>();
                list.add(stationDTO1);
                for (StationDTO stationDTO2 : list2) {
                    int code2 = stationDTO2.getStationCode();
                    if (code1 == code2) {
                        list.add(stationDTO2);
                    }
                }
                sta.setName(stationDTO1.getName());
                sta.setSortNumber(stationDTO1.getSortNumber());
                sta.setStations(list);
                resultList.add(sta);
            }
            return resultList;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Object insertStation(StationDTO stationDTO) {
        int result;
        int lastSortNumber = stationDTO.getLastSortNumber();
        StationDTO sta = new StationDTO();
        sta.setMetroLineId(stationDTO.getMetroLineId());
        sta.setDirection(stationDTO.getDirection());
        List<StationDTO> list = lineMapping.getStations(sta);
        sta.setStationCode(stationDTO.getStationCode());
        if(lineMapping.getStations(sta).size()>0){
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(),"站点编码已存在");
        }
        if (list.size() > 0) {
            for (int i = list.size() - 1; i >= 0; i--) {
                if (list.get(i).getSortNumber() > lastSortNumber) {
                    list.get(i).setSortNumber(list.get(i).getSortNumber() + 1);
                }
                lineMapping.updateStations(list.get(i));
            }
        }
        stationDTO.setCreateBy(UserUtils.getUser().getId());
        stationDTO.setSortNumber(lastSortNumber + 1);
        stationDTO.setId(PrimaryKeyGenerator.generatorId());
        try {
            result = lineMapping.insertStations(stationDTO);
        } catch (Exception e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
        if (result == 0) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
        return "SUCCESS";
    }

    @Transactional(rollbackFor = Exception.class)
    public Object insertStations(String stations) {
        JSONArray staList = JSONArray.parseArray(stations);
        for(int i=0;i<staList.size();i++){
            StationDTO stationDTO = new StationDTO();
            stationDTO.setStationCode(staList.getJSONObject(i).getInteger("stationCode"));
            stationDTO.setName(staList.getJSONObject(i).getString("name"));
            stationDTO.setDepartureDistance(staList.getJSONObject(i).getInteger("departureDistance"));
            stationDTO.setType(staList.getJSONObject(i).getString("type"));
            stationDTO.setDirection(staList.getJSONObject(i).getString("direction"));
            stationDTO.setStaId(staList.getJSONObject(i).getString("staId"));
            stationDTO.setLastSortNumber(staList.getJSONObject(i).getInteger("lastSortNumber"));
            stationDTO.setMetroLineId(staList.getJSONObject(i).getString("metroLineId"));
            String errMsg = valid(stationDTO,Insert.class);
            if(StringUtils.isNotEmpty(errMsg)){
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(),errMsg);
            }
            insertStation(stationDTO);
        }
        return "SUCCESS";
    }

    public Object updateStation(StationDTO stationDTO) {
        stationDTO.setModifyBy(UserUtils.getUser().getId());
        stationDTO.setModifyTime("true");
        //校验站点编码是否重复
        StationDTO sta = new StationDTO();
        sta.setId(stationDTO.getId());
        sta.setMetroLineId(stationDTO.getMetroLineId());
        sta.setDirection(stationDTO.getDirection());
        sta.setStationCode(stationDTO.getStationCode());
        List<StationDTO> list = lineMapping.getStations(sta);
        if (list.size() > 0) {
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION.getErrorCode(),"站点编码已存在");
        }
        int result = lineMapping.updateStations(stationDTO);
        if (result == 0) {
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
        return "SUCCESS";
    }

    public Object updateStations(String stations) {
        JSONArray staList = JSONArray.parseArray(stations);
        for(int i=0;i<staList.size();i++){
            StationDTO stationDTO = new StationDTO();
            stationDTO.setId(staList.getJSONObject(i).getString("id"));
            stationDTO.setStationCode(staList.getJSONObject(i).getInteger("stationCode"));
            stationDTO.setName(staList.getJSONObject(i).getString("name"));
            stationDTO.setDepartureDistance(staList.getJSONObject(i).getInteger("departureDistance"));
            stationDTO.setType(staList.getJSONObject(i).getString("type"));
            stationDTO.setDirection(staList.getJSONObject(i).getString("direction"));
            stationDTO.setStaId(staList.getJSONObject(i).getString("staId"));
            stationDTO.setSortNumber(staList.getJSONObject(i).getInteger("sortNumber"));
            stationDTO.setMetroLineId(staList.getJSONObject(i).getString("metroLineId"));
            stationDTO.setRemark(staList.getJSONObject(i).getString("remark"));
            stationDTO.setStatus(staList.getJSONObject(i).getInteger("status"));
            String errMsg = valid(stationDTO, Update.class);
            if(StringUtils.isNotEmpty(errMsg)){
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(),errMsg);
            }
            updateStation(stationDTO);
        }
        return "SUCCESS";
    }

    public Object deleteStation(StationDTO stationDTO) {
        int result = lineMapping.deleteStation(stationDTO);
        if (result == 0) {
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
        StationDTO sta = new StationDTO();
        sta.setMetroLineId(stationDTO.getMetroLineId());
        sta.setDirection(stationDTO.getDirection());
        List<StationDTO> list = lineMapping.getStations(sta);
        if (list.size() > 0) {
            for (StationDTO stationOlDto : list) {
                if (stationOlDto.getSortNumber() > stationDTO.getSortNumber()) {
                    stationOlDto.setSortNumber(stationOlDto.getSortNumber() - 1);
                    lineMapping.updateStations(stationOlDto);
                }
            }
        }
        return "SUCCESS";
    }

    public Object deleteStations(String stations) {
        JSONArray staList = JSONArray.parseArray(stations);
        for(int i=0;i<staList.size();i++){
            StationDTO stationDTO = new StationDTO();
            stationDTO.setId(staList.getJSONObject(i).getString("id"));
            stationDTO.setDirection(staList.getJSONObject(i).getString("direction"));
            stationDTO.setSortNumber(staList.getJSONObject(i).getInteger("sortNumber"));
            stationDTO.setMetroLineId(staList.getJSONObject(i).getString("metroLineId"));
            deleteStation(stationDTO);
        }
        return "SUCCESS";
    }

    /**
     * @Description 通过id查询线路
     * @Return java.lang.Object
     * <AUTHOR> yuxi
     * @Date 14:39 2020/8/12
     * @Param [id]
     **/
    public Object getLineById(String id) {
        HashMap<String, String> org = lineMapping.getOrganizationsByLineId(id);
        LineDTO line = lineMapping.getLineById(id);
        line.setOrganizationsId(org.get("organizationsId"));
        line.setOrganizationsName(org.get("organizationsName"));
        return line;
    }

    private <T> String valid(T t ,Class<?>... groups) {
        ValidatorFactory vf = Validation.buildDefaultValidatorFactory();
        Validator validator = vf.getValidator();
        Set<ConstraintViolation<T>> set = validator.validate(t, groups);
        String errMsg = "";
        for (ConstraintViolation<T> constraintViolation : set) {
            errMsg += constraintViolation.getMessage();
        }
        return errMsg;
    }
}
