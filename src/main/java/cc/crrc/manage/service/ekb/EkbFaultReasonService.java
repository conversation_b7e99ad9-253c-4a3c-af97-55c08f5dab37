package cc.crrc.manage.service.ekb;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.ekb.EkbFaultReasonMapping;
import cc.crrc.manage.mapper.ekb.EkbFaultTypeReasonMapping;
import cc.crrc.manage.mapper.ekb.EkbReasonMeasureMapping;
import cc.crrc.manage.pojo.ekb.EkbFaultReasonDTO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class EkbFaultReasonService {
    @Autowired
    private EkbFaultReasonMapping ekbFaultReasonMapping;
    @Autowired
    private EkbReasonMeasureMapping ekbReasonMeasureMapping;
    @Autowired
    private EkbFaultTypeReasonMapping ekbFaultTypeReasonMapping;


    public Object addEkbFaultReason(EkbFaultReasonDTO ekbFaultReasonDTO) {
        // 设置故障原因业务主键： 车辆型号加上原因编码
        ekbFaultReasonDTO.setReasonKey(ekbFaultReasonDTO.getVehicleTypeId() + ekbFaultReasonDTO.getReasonCode());
        ekbFaultReasonDTO.setId(PrimaryKeyGenerator.generatorId());
        ekbFaultReasonDTO.setCreateBy(UserUtils.getUserId());
        ekbFaultReasonDTO.setModifyBy(UserUtils.getUserId());
        return ekbFaultReasonMapping.addEkbFaultReason(ekbFaultReasonDTO);
    }

    public Object deleteEkbFaultReason(String idStr) {
        int count = 0;
        if (StringUtil.isNotEmpty(idStr)) {
            String[] idsArr = idStr.split(",");
            Long[] ids = new Long[idsArr.length];
            for (int i = 0, len = idsArr.length; i < len; i++) {
                ids[i] = new Long(idsArr[i]);
            }
            /*删除故障原因关联的措施 （删除关系）*/
            EkbFaultReasonDTO ekbFaultReason;
            for (long idNow : ids) {
                ekbFaultReason = ekbFaultReasonMapping.getEkbFaultReasonById(idNow);
                if (ekbFaultReason == null) {
                    continue;
                }
                String faultReasonKey = ekbFaultReason.getReasonKey();
                if (StringUtil.isNotEmpty(faultReasonKey)) {
                    try {
                        //删除故障类型与故障原因的关联关系
                        ekbFaultTypeReasonMapping.deleteEkbFaultTypeReasonByFaultReasonKey(faultReasonKey);
                        //删除故障原因与故障措施之间的关联关系
                        ekbReasonMeasureMapping.deleteReasonMeasure(null, faultReasonKey, null);
                    } catch (DataAccessException e) {
                        throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION.getErrorCode(), "故障原因与故障措施关联删除失败！");
                    }
                }
                ekbFaultReasonMapping.deleteEkbFaultReason(idNow);
                count++;
            }
        }
        return count;
    }

    public Object updateEkbFaultReason(EkbFaultReasonDTO ekbFaultReasonDTO) {
        return ekbFaultReasonMapping.updateEkbFaultReason(ekbFaultReasonDTO);
    }

    public Object listEkbFaultReason(EkbFaultReasonDTO ekbFaultReasonDTO) {
        try {
            //分页
            int currentPage = ekbFaultReasonDTO.getPageNumber();
            int pageSize = ekbFaultReasonDTO.getPageSize();
            PageHelper.startPage(currentPage, pageSize);
            //获取数据
            List<EkbFaultReasonDTO> resultList = ekbFaultReasonMapping.listEkbFaultReason(ekbFaultReasonDTO);
            return new PageInfo<>(resultList);
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object updateTypeReasonRealCounter(String faultTypeKey, String faultReasonKey, Long realCounter) {
        return ekbFaultReasonMapping.updateTypeReasonRealCounter(faultTypeKey, faultReasonKey, realCounter);
    }

    public Object listEkbFaultReasonVaguely(String id, String keyword) {
        return ekbFaultReasonMapping.listEkbFaultReasonVaguely(id,keyword);
    }


    public Object getEkbFaultReason(String faultTypeKey) {
        return ekbFaultReasonMapping.getEkbFaultReason(faultTypeKey);
    }
}
