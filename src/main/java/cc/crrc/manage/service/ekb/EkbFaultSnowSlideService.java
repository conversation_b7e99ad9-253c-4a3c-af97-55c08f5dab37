package cc.crrc.manage.service.ekb;


import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.*;
import cc.crrc.manage.common.vo.TreeNodeVO;
import cc.crrc.manage.mapper.ekb.EkbFaultSnowSlideMapping;
import cc.crrc.manage.mapper.ekb.EkbFaultTypeMapping;
import cc.crrc.manage.pojo.comm.snowSlide.SnowSlideEdgePO;
import cc.crrc.manage.pojo.comm.snowSlide.SnowSlideGraphPO;
import cc.crrc.manage.pojo.comm.snowSlide.SnowSlideNodePO;
import cc.crrc.manage.pojo.ekb.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;


@Service
public class EkbFaultSnowSlideService {
    private final static Logger logger = LoggerFactory.getLogger(EkbFaultSnowSlideService.class);
    @Autowired
    private EkbFaultSnowSlideMapping ekbFaultSnowSlideMapping;
    @Autowired
    private EkbFaultTypeMapping ekbFaultTypeMapping;
    @Autowired
    private EkbFaultTypeMapping faultTypeMapping;

    /**
     * 保存故障雪崩关系网
     * @param ekbSnowSlideRelationDTO
     *
     * 2021/7/29
     * <AUTHOR> GuoYang
     * 只保存当前故障及其所抑制的故障树图
     * 支持清空树图的操作方式
     */
    @Transactional(rollbackFor = Exception.class)
    public Object saveFaultSnowSlideRelation(EkbSnowSlideRelationDTO ekbSnowSlideRelationDTO) {
        Integer saveRows = null;
        try{
            if(ekbSnowSlideRelationDTO != null && StringUtils.isNotEmpty(ekbSnowSlideRelationDTO.getFaultTypeKey())){
                ekbFaultSnowSlideMapping.deleteFaultSnowSlideByKey(ekbSnowSlideRelationDTO.getFaultTypeKey());
                SnowSlideGraphPO snowSlideGraphPO = new SnowSlideGraphPO(ekbSnowSlideRelationDTO);
                String faultTypeKey = snowSlideGraphPO.getFaultTypeKey();
                List<SnowSlideNodePO> nodeList = snowSlideGraphPO.getNodeList();
                String faultTypeKeyId = getFaultTypeKeyId(nodeList,faultTypeKey);
                List<EkbFaultSnowSlidePO> listEkbFaultSnowSlidePO = foundEkbFaultSnowSlidePo(snowSlideGraphPO, faultTypeKeyId);
                if(listEkbFaultSnowSlidePO.size()!=0) {
                    //保存雪崩关系
                    saveRows = ekbFaultSnowSlideMapping.saveFaultSnowSlideRelation(listEkbFaultSnowSlidePO);
                }
            }
        }catch(DataAccessException e){
            logger.error("Method[saveFaultSnowSlideRelation] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
        return saveRows;
    }

    /**
     * 修改雪崩抑制规则的启用状态
     *
     * @param faultTypeKey 故障类型的key
     * @param enable 期望启用/禁用状态
     * @param vehicleTypeId 车辆类型id
     * <AUTHOR> GuoYang
     * 2020/4/10
     * @Remark zhangzhijian 2020-09-18修改启用/禁用规则
     * 原规则是点击开启，开启当前故障父节点和子节点的雪崩关系
     * 现修改为:点击开启/禁用，只修改父节点的使能状态，因为子节点的只修改父节点的使能状态可以在子节点处开启
     * 这样利于维护ekb_fault_type表的enable字段
     **/
    @Transactional(rollbackFor=Exception.class)
    public void changeEnableStatus(String faultTypeKey, Boolean enable,String vehicleTypeId) {
        try{
            List<EkbFaultSnowSlidePO> faultSnowSlideList = ekbFaultSnowSlideMapping.findFaultSnowSlideByKey(faultTypeKey);
            long currentCount = faultSnowSlideList.stream().filter(o -> vehicleTypeId.equals(o.getVehicleTypeId())).count();
            if (currentCount == 0 && enable){
                throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION,"该故障尚未配置抑制规则！");
            }
            String currentId = UserUtils.getUserId();
            //只修改该故障作为子节点的使能启用状态
            int successCount = ekbFaultSnowSlideMapping.changeEnableStatus(faultTypeKey,enable,vehicleTypeId,currentId);
            if (successCount != currentCount) {
                throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION,"雪崩抑制规则未能成功启用/禁用");
            }
            //修改该故障使能状态
            faultTypeMapping.updateEkbFaultTypeByKey(faultTypeKey,enable,null);
        }catch(DataAccessException e){
            logger.error("Method[changeEnableStatus] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }


    /**
     * 从节点列表中获取faultTypeKey相对应的节点Id
     * @param nodeList
     * @param faultTypeKey
     * @return
     */
    private String getFaultTypeKeyId(List<SnowSlideNodePO> nodeList, String faultTypeKey) {
        String faultTypeKeyId = null;
        if (nodeList == null || nodeList.isEmpty()) {
            return null;
        }
        for (SnowSlideNodePO snowSlideNodePO : nodeList) {
            String alarmId = snowSlideNodePO.getFaultTypeKey();
            String id = snowSlideNodePO.getNodeId();
            if (StringUtils.isNotEmpty(faultTypeKey) && faultTypeKey.equals(alarmId)) {
                faultTypeKeyId = id;
                break;
            }
        }
        return faultTypeKeyId;
    }

    /**
     * 遍历解析jsonNodes和jsonEdges成List<EkbFaultSnowSlidePO>
     * @param snowSlideGraphPO
     * @param faultTypeKeyId
     * @return
     */
    private List<EkbFaultSnowSlidePO> foundEkbFaultSnowSlidePo(SnowSlideGraphPO snowSlideGraphPO, String faultTypeKeyId) {
        List<SnowSlideEdgePO> edgeList = snowSlideGraphPO.getEdgeList();
        List<SnowSlideNodePO> nodeList = snowSlideGraphPO.getNodeList();
        String faultTypeKey = snowSlideGraphPO.getFaultTypeKey();
        String vehicleTypeId = snowSlideGraphPO.getVehicleTypeId();
        boolean enable = snowSlideGraphPO.isEnable();
        List<EkbFaultSnowSlidePO> listEkbFaultSnowSlidePO = new ArrayList<>();
        if (StringUtils.isEmpty(faultTypeKeyId)) {
            return listEkbFaultSnowSlidePO;
        }
        for (SnowSlideEdgePO snowSlideEdgePO : edgeList) {
            String sourceId = snowSlideEdgePO.getSourceId();
            String targetId = snowSlideEdgePO.getTargetId();
            if (!faultTypeKeyId.equals(sourceId)) {
                for (SnowSlideNodePO snowSlideNodePO : nodeList) {
                    String currentId = "";
                    if (snowSlideNodePO.getNodeId() != null) {
                        currentId = snowSlideNodePO.getNodeId();
                    }
                    if (StringUtils.isNotEmpty(sourceId) && sourceId.equals(currentId)) {
                        String parentKey = snowSlideNodePO.getFaultTypeKey();
                        if (StringUtils.isNotEmpty(parentKey) && snowSlideEdgePO.getTime() != null) {
                            EkbFaultSnowSlidePO ekbFaultSnowSlidePO = new EkbFaultSnowSlidePO();
                            ekbFaultSnowSlidePO.setCorrelationTime(Integer.valueOf(snowSlideEdgePO.getTime()));
                            ekbFaultSnowSlidePO.setFaultTypeKey(faultTypeKey);
                            ekbFaultSnowSlidePO.setParentFaultTypeKey(parentKey);
                            saveObject(ekbFaultSnowSlidePO, enable, vehicleTypeId);
                            listEkbFaultSnowSlidePO.add(ekbFaultSnowSlidePO);
                            break;
                        }
                    }
                }
            } else {
                for (SnowSlideNodePO snowSlideNodePO : nodeList) {
                    String currentId = snowSlideNodePO.getNodeId();
                    if (StringUtils.isNotEmpty(targetId) && targetId.equals(currentId)) {
                        String targetKey = snowSlideNodePO.getFaultTypeKey();
                        if (StringUtils.isNotEmpty(targetKey) && snowSlideEdgePO.getTime() != null) {
                            EkbFaultSnowSlidePO ekbFaultSnowSlidePO = new EkbFaultSnowSlidePO();
                            ekbFaultSnowSlidePO.setCorrelationTime(Integer.valueOf(snowSlideEdgePO.getTime()));
                            ekbFaultSnowSlidePO.setParentFaultTypeKey(faultTypeKey);
                            ekbFaultSnowSlidePO.setFaultTypeKey(targetKey);
                            saveObject(ekbFaultSnowSlidePO, enable, vehicleTypeId);
                            listEkbFaultSnowSlidePO.add(ekbFaultSnowSlidePO);
                            break;
                        }
                    }
                }
            }
        }
        return listEkbFaultSnowSlidePO;
    }

    /**
     * EkbFaultSnowSlidePO对象保存的部分属性通用方法
     * @param ekbFaultSnowSlidePO
     * @param enable
     * @param vehicleTypeId
     */
    private void saveObject(EkbFaultSnowSlidePO ekbFaultSnowSlidePO, Boolean enable, String vehicleTypeId) {
        ekbFaultSnowSlidePO.setId(PrimaryKeyGenerator.generatorId());
        ekbFaultSnowSlidePO.setEnable(enable);
        ekbFaultSnowSlidePO.setVehicleTypeId(vehicleTypeId);
        ekbFaultSnowSlidePO.setCreateBy(UserUtils.getUserId());
        ekbFaultSnowSlidePO.setModifyBy(UserUtils.getUserId());
    }

    /**
     * 根据当前faultTypeKey和nameCn 查询故障雪崩关系数据
     * @param faultTypeKey
     * @param nameCn
     * @return
     */
    public Object findFaultSnowSlideByKey(String faultTypeKey,String nameCn) {
        JSONObject sourceJson = new JSONObject();
        try{
            if(StringUtils.isNotEmpty(faultTypeKey)){
                List<EkbFaultSnowSlidePO> listEkbFaultSnowSlidePo = ekbFaultSnowSlideMapping.findFaultSnowSlideByKey(faultTypeKey);
                JSONObject obj = parseListToJson(listEkbFaultSnowSlidePo,faultTypeKey,nameCn);
                sourceJson.put("source",obj);
            }
            return sourceJson;
        }catch(DataAccessException e){
            logger.error("Method[addDict] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 将查询出来的数据封装成JSONObject格式数据（点和线信息），用于显示故障雪崩关系
     *
     *
     * @param listEkbFaultSnowSlidePo
     * @param faultTypeKey
     * @param nameCn
     * @return
     */
    private JSONObject parseListToJson(List<EkbFaultSnowSlidePO> listEkbFaultSnowSlidePo, String faultTypeKey, String nameCn) {
        JSONObject obj = new JSONObject();
        JSONArray nodesArray = new JSONArray();
        JSONArray edgesArray = new JSONArray();
        try{
            if(listEkbFaultSnowSlidePo != null && listEkbFaultSnowSlidePo.size()>0){
                for (EkbFaultSnowSlidePO faultSnowSlidePO : listEkbFaultSnowSlidePo) {
                    JSONObject nodeJson = new JSONObject();
                    JSONObject edgeJson = new JSONObject();
                    nodeJson.put("shape", Constants.NODESHAPE);
                    nodeJson.put("img",Constants.RESTSIMG);
                    edgeJson.put("shape",Constants.EDGESHAPE);
                    edgeJson.put("label",String.valueOf(faultSnowSlidePO.getCorrelationTime())+"(ms)");
                    edgeJson.put("id",UUIDUtils.generateUuid());
                    if(faultTypeKey.equals(faultSnowSlidePO.getFaultTypeKey())){
                        //faultTypeKey是当前节点,保存faultTypeKey的父节点信息
                        nodeJson.put("label",faultSnowSlidePO.getParentNameCn());
                        nodeJson.put("alarmId",faultSnowSlidePO.getParentFaultTypeKey());
                        nodeJson.put("id",faultSnowSlidePO.getParentFaultTypeKey());
                        //保存faultTypeKey和父节点的线信息
                        edgeJson.put("source",faultSnowSlidePO.getParentFaultTypeKey());
                        edgeJson.put("target",faultTypeKey);
                    }else {//faultTypeKey是父节点,保存faultTypeKey的子节点信息
                        nodeJson.put("label",faultSnowSlidePO.getNameCn());
                        nodeJson.put("alarmId",faultSnowSlidePO.getFaultTypeKey());
                        nodeJson.put("id",faultSnowSlidePO.getFaultTypeKey());
                        //保存faultTypeKey和子节点的线信息
                        edgeJson.put("source",faultTypeKey);
                        edgeJson.put("target",faultSnowSlidePO.getFaultTypeKey());
                    }
                    nodesArray.add(nodeJson);
                    edgesArray.add(edgeJson);
                }
                //保存faultTypeKey的节点信息
                JSONObject currentJson = new JSONObject();
                currentJson.put("shape",Constants.NODESHAPE);
                currentJson.put("label",nameCn);
                currentJson.put("alarmId",faultTypeKey);
                currentJson.put("id",faultTypeKey);
                currentJson.put("img",Constants.CURRENTIMG);
                nodesArray.add(currentJson);
            }
            //保存nodesArray和edgesArray
            obj.put("nodes",nodesArray);
            obj.put("edges",edgesArray);
            return obj;
        }catch(DataAccessException e){
            logger.error("Method[addDict] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION);
        }
    }

    /**
     * 校验故障雪崩规则是否有效
     *
     * @param ekbSnowSlideRelationDTO 新添加的故障雪崩规则 和 相关信息
     * <AUTHOR> GuoYang
     * 2020/4/2
     **/
    public void validSnowSlideRelation(EkbSnowSlideRelationDTO ekbSnowSlideRelationDTO) {
        try {
            SnowSlideGraphPO snowSlideGraphPO = new SnowSlideGraphPO(ekbSnowSlideRelationDTO);
            String faultTypeKey = ekbSnowSlideRelationDTO.getFaultTypeKey();
            String vehicleTypeId = ekbSnowSlideRelationDTO.getVehicleTypeId();
            List<SnowSlideNodePO> faultNodeList = snowSlideGraphPO.getNodeList();
            List<SnowSlideEdgePO> faultRelationList = snowSlideGraphPO.getEdgeList();
            String faultTypeKeyId = getFaultTypeKeyId(faultNodeList,faultTypeKey);
            // 必须有当前故障节点
            if (faultTypeKeyId == null){
                throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,
                        "校验失败,缺少当前故障的节点信息！");
            }
            // 使能状态下不可编辑
            Boolean enableStatusNow = ekbFaultTypeMapping.getEkbFaultTypeByFaultTypeKey(faultTypeKey).getEnable();
            if (enableStatusNow == null){
                enableStatusNow = false;
            }
            if (enableStatusNow) {
                throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,
                        "校验失败,启用状态不可编辑！");
            }
            if(faultNodeList.size() == 1 && faultTypeKey.equals(faultNodeList.get(0).getFaultTypeKey())){
                return;
            }
            //节点没有赋值
            boolean hasEmptyNode = faultNodeList.stream().anyMatch(o -> o.getFaultTypeKey() == null);
            if (hasEmptyNode) {
                throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,
                        "校验失败,新增节点需要设定故障名称！");
            }
            //节点之间连线判断
            if(faultNodeList.size()-faultRelationList.size()>1){
                throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,
                        "校验失败,新增节点需要与中心节点连线！");
            }
            if(faultNodeList.size()-faultRelationList.size()<1){
                throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,
                        "校验失败,有节点之间连线数大于1！");
            }
            // 故障关系必须填写最大有效抑制时间
            boolean allElemHasTime = faultRelationList.stream().noneMatch(o -> StringUtils.isEmpty(o.getTime()));
            if (!allElemHasTime) {
                throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,
                        "校验失败,故障抑制关系缺少时间！");
            }
            //判断时间字符串长度是否大于6位
            boolean allElemHasTimeLength = faultRelationList.stream().noneMatch(o -> o.getTime().length()>6);
            if (!allElemHasTimeLength) {
                throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,
                        "校验失败,时间位数不能超过6位！");
            }
            //故障关系抑制时间必须小于60s
            boolean allElemHasTimeRange = faultRelationList.stream().noneMatch(o -> Integer.valueOf(o.getTime())>60000);
            if (!allElemHasTimeRange) {
                throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,
                        "校验失败,故障抑制关系时间不能大于60000ms！");
            }
            // 故障关系必须围绕当前故障节点
            boolean allElemAroundCurrentFault = faultRelationList.stream().allMatch(o -> faultTypeKeyId.equals(o.getSourceId()));
            if (!allElemAroundCurrentFault) {
                throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,
                        "校验失败,故障关系必须以当前节点作为起始节点！");
            }
            // 校验是否有重复节点
            List<String> faultNameList = getNodeNameList(snowSlideGraphPO);
            if (faultNodeList.size() > faultNameList.size()) {
                throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,
                        "校验失败，待修改的故障关系存在重复节点！");
            }
            List<EkbFaultSnowSlidePO> newSnowSlideRules = foundEkbFaultSnowSlidePo(snowSlideGraphPO, faultTypeKeyId);
            // 校验故障级别，保障低等级故障不可抑制高等级故障
            List<EkbFaultTypeDTO> faultTypeDTOList = ekbFaultTypeMapping.getFaultTypeListByFaultTypeKey(faultNameList);
            HashMap<String, Integer> faultLevelMap = getFaultLevelMap(faultTypeDTOList);
            HashMap<String, String> nameCnMap = getNameCnMap(faultTypeDTOList);
            for (EkbFaultSnowSlidePO newSnowSlideRule : newSnowSlideRules) {
                checkFaultLevelLegal(newSnowSlideRule, faultLevelMap,nameCnMap);
            }
            // 校验是否有环
            boolean hasCycle = detectCycleInNet(newSnowSlideRules, faultTypeKey, vehicleTypeId);
            if (hasCycle) {
                throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,
                        "校验失败，待修改的故障关系存在抑制环！");
            }
        } catch (DataAccessException e) {
            logger.error("Method[validSnowSlideRelation] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }


    /**
     * 从图形信息中提取节点名称
     *
     * @param snowSlideGraphPO 保存图形信息的实体类
     * @return java.util.List<java.lang.String>
     * <AUTHOR> GuoYang
     * 2020/4/2
     **/
    private List<String> getNodeNameList(SnowSlideGraphPO snowSlideGraphPO) {
        Set<String> nodeNameSet = new HashSet<>();
        List<SnowSlideNodePO> nodeList = snowSlideGraphPO.getNodeList();
        for (SnowSlideNodePO snowSlideNodePO : nodeList) {
            nodeNameSet.add(snowSlideNodePO.getFaultTypeKey());
        }
        return new ArrayList<>(nodeNameSet);
    }

    /**
     * 从故障字典列表中提取faultTypeKey故障编码和FaultLevel故障等级的Map
     *
     * @param faultTypeDTOList 故障字典列表
     * @return java.util.HashMap<java.lang.String, java.lang.Integer>
     * <AUTHOR> GuoYang
     * 2020/4/2
     **/
    private HashMap<String, Integer> getFaultLevelMap(List<EkbFaultTypeDTO> faultTypeDTOList) {
        HashMap<String, Integer> res = new HashMap<>();
        for (EkbFaultTypeDTO ekbFaultTypeDTO : faultTypeDTOList) {
            String faultTypeKey = ekbFaultTypeDTO.getFaultTypeKey();
            Integer faultLevel = ekbFaultTypeDTO.getFaultLevel();
            res.put(faultTypeKey, faultLevel);
        }
        return res;
    }

    /**
     * 从故障字典列表中提取faultTypeKey故障编码和中文名称nameCn的Map
     * @param faultTypeDTOList 故障字典列表
     * @return java.util.HashMap<java.lang.String, java.lang.String>
     */
    private HashMap<String, String> getNameCnMap(List<EkbFaultTypeDTO> faultTypeDTOList) {
        HashMap<String, String> res = new HashMap<>();
        for (EkbFaultTypeDTO ekbFaultTypeDTO : faultTypeDTOList) {
            String faultTypeKey = ekbFaultTypeDTO.getFaultTypeKey();
            String nameCn = ekbFaultTypeDTO.getNameCn();
            res.put(faultTypeKey, nameCn);
        }
        return res;
    }

    /**
     * 校验故障雪崩图中的故障等级关系，保障低等级的故障不可抑制高等级的故障
     *
     * @param snowSlideRule 故障雪崩规则
     * @param faultLevelMap 故障编码和等级的对照map
     * @param nameCnMap 故障编码和中文名称的对照map
     * <AUTHOR> GuoYang
     * 2020/4/2
     **/
    private void checkFaultLevelLegal(EkbFaultSnowSlidePO snowSlideRule,
                                      HashMap<String, Integer> faultLevelMap,
                                      HashMap<String, String> nameCnMap) {
        String faultTypeKeyTemp = snowSlideRule.getFaultTypeKey();
        String parentFaultTypeKeyTemp = snowSlideRule.getParentFaultTypeKey();
        Integer faultLevel = faultLevelMap.get(faultTypeKeyTemp);
        Integer parentFaultLevel = faultLevelMap.get(parentFaultTypeKeyTemp);
        String nameCn = nameCnMap.get(faultTypeKeyTemp);
        String parentNameCn = nameCnMap.get(parentFaultTypeKeyTemp);
        if (faultLevel == null) {
            throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,
                    "故障[" + nameCn + "]等级存在异常！");
        }
        if (parentFaultLevel == null) {
            throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,
                    "故障[" + parentNameCn + "]等级存在异常！");
        }
        if (faultLevel > parentFaultLevel) {
            throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,
                    "[" + nameCn + "]比[" + parentNameCn + "]" + "故障等级高,无法抑制！");
        }
    }

    /**
     * 校验新修改图在雪崩网中是否有环
     * 返回：有环true，无环false。
     *
     * @param newSnowSlideRules 新添加的故障雪崩规则
     * @param faultTypeKey      本次添加的故障key
     * @param vehicleTypeId     车辆类型id
     * @return boolean
     * <AUTHOR> GuoYang
     * 2020/4/2
     **/
    private boolean detectCycleInNet(List<EkbFaultSnowSlidePO> newSnowSlideRules, String faultTypeKey, String vehicleTypeId) {
        // 获取现有的故障雪崩关系网（抛除本次新添加的部分）
        List<TreeNodeVO> snowSlideRelation = ekbFaultSnowSlideMapping.getFaultSnowSlideExceptKey(faultTypeKey, vehicleTypeId);
        // 提取父子关系，进行类型转换
        List<TreeNodeVO> newSnowSlideRelation = getRelationProperty2TreeNodeVO(newSnowSlideRules);
        snowSlideRelation.addAll(newSnowSlideRelation);
        // 获取节点信息的map，包含子节点关系
        HashMap<String, List<String>> verticesMap = getVertices(snowSlideRelation);
        for (TreeNodeVO newNode : newSnowSlideRelation) {
            // 判断新添加的关系是否无环
            String fromVertex = newNode.getParentId().toString();
            String toVertex = newNode.getId().toString();
            boolean temp = isLegalRelationWithoutCycle(fromVertex, toVertex, verticesMap);
            if (!temp) {
                return true;
            }
        }
        return false;
    }


    /**
     * 从泛型为EkbFaultSnowSlidePO的list中，提取出元素中父子关系属性，并保存为泛型为TreeNodeVO的list，进行返回。
     *
     * @param faultRelationList 故障抑制关系list
     * @return java.util.List<cc.crrc.manage.common.vo.TreeNodeVO>
     * <AUTHOR> GuoYang
     * 2020/4/2
     **/
    private List<TreeNodeVO> getRelationProperty2TreeNodeVO(List<EkbFaultSnowSlidePO> faultRelationList) {
        List<TreeNodeVO> res = new ArrayList<>();
        for (EkbFaultSnowSlidePO ekbFaultSnowSlidePO : faultRelationList) {
            TreeNodeVO temp = new TreeNodeVO();
            temp.setId(ekbFaultSnowSlidePO.getFaultTypeKey());
            temp.setParentId(ekbFaultSnowSlidePO.getParentFaultTypeKey());
            res.add(temp);
        }
        return res;
    }

    /**
     * 从保存节点关系的list中拆解出节点map（包含下级）。
     *
     * @param relationList 保存节点关系的list
     * @return java.util.HashMap<java.lang.String, java.util.List < java.lang.String>>
     * <AUTHOR> GuoYang
     * 2020/4/2
     **/
    private HashMap<String, List<String>> getVertices(List<TreeNodeVO> relationList) {
        HashMap<String, List<String>> res = new HashMap<>();
        for (TreeNodeVO relation : relationList) {
            String nodeName = String.valueOf(relation.getId());
            String parentNodeName = String.valueOf(relation.getParentId());
            if (!res.containsKey(nodeName)) {
                res.put(nodeName, new ArrayList<>());
            }
            if (!res.containsKey(parentNodeName)) {
                res.put(parentNodeName, new ArrayList<>());
            }
            res.get(parentNodeName).add(nodeName);
        }
        return res;
    }

    /**
     * 判断新添加的关系，是否会造成有向无环图中存在环的现象
     *
     * @param fromVertex  源节点
     * @param toVertex    源节点所指向的节点
     * @param verticesMap 整个网中的节点map
     * @return boolean
     * <AUTHOR> GuoYang
     * 2020/4/2
     **/
    private boolean isLegalRelationWithoutCycle(String fromVertex, String toVertex, HashMap<String, List<String>> verticesMap) {
        if (fromVertex.equals(toVertex)) {
            logger.error("edge fromVertex({}) can't equals toVertex({})", fromVertex, toVertex);
            return false;
        }
        Queue<String> queue = new LinkedList<>();
        List<String> processedList = new ArrayList<>();
        queue.add(toVertex);
        int verticesCount = verticesMap.size();
        //如果没有找到fromVertex, 表示无环;
        while (!queue.isEmpty() && verticesCount > 0) {
            verticesCount -= 1;
            String key = queue.poll();
            // 遍历后续顶点
            List<String> subsequentVertices = verticesMap.getOrDefault(key, new ArrayList<>());
            for (String subsequentVertex : subsequentVertices) {
                if (subsequentVertex.equals(fromVertex)) {
                    return false;
                }
                // 遍历过的 不重复遍历
                if (!queue.contains(subsequentVertex) && !processedList.contains(subsequentVertex)) {
                    queue.add(subsequentVertex);
                }
                processedList.add(key);
            }
        }
        return true;
    }

    /**
     * 获取雪崩网数据
     * @param ekbFaultSnowSlideDTO
     * @return
     */
    public PageInfo<EkbSnowSlideRelationPO> findFaultSnowSlideRelations(EkbFaultSnowSlideDTO ekbFaultSnowSlideDTO) {
        try{
            //根据vehicleTypeId查出入度为0的顶点和所有雪崩数据
            String vehicleTypeId = ekbFaultSnowSlideDTO.getVehicleTypeId();
            String faultCode = ekbFaultSnowSlideDTO.getFaultCode();
            String nameCn = ekbFaultSnowSlideDTO.getNameCn();
            Integer pageNum = ekbFaultSnowSlideDTO.getPageNum();
            Integer pageSize = ekbFaultSnowSlideDTO.getPageSize();
            PageInfo<EkbSnowSlideRelationPO> pageInfo = new PageInfo<>();
            ArrayList<EkbSnowSlideRelationPO> ekbSnowSlideRelations = disposeFaultSnowSlideRelations(vehicleTypeId);
            if(StringUtils.isNotEmpty(faultCode) || StringUtils.isNotEmpty(nameCn)){
                //模糊查询
                return findFuzzy(ekbFaultSnowSlideDTO,ekbSnowSlideRelations);
            }else{
                //数据分页
                List<EkbSnowSlideRelationPO> pageList = new ArrayList<>();
                Integer total = ekbSnowSlideRelations.size();
                if(pageSize == null || pageSize == 0){
                    pageSize = 10;
                }
                //总页数
                Integer pageCount = (total-1)/pageSize+1;
                if(pageNum == null || pageNum == 0){
                    pageNum = 1;
                }
                if(pageNum.equals(pageCount)){
                    pageList = ekbSnowSlideRelations.subList((pageNum-1)*pageSize,total);
                }else{
                    pageList = ekbSnowSlideRelations.subList((pageNum-1)*pageSize,pageNum*pageSize);
                }
                pageInfo.setTotal(total);
                pageInfo.setList(pageList);
                pageInfo.setPages(pageCount);
                pageInfo.setPageNum(pageNum);
                pageInfo.setPageSize(pageSize);
                pageInfo.setSize(pageList.size());
                return pageInfo;
            }
        }catch(DataAccessException e){
            logger.error("Method[listExpertKnowledgeBase] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 模糊查询
     * @param ekbFaultSnowSlideDTO
     * @param ekbSnowSlideRelations
     * @return
     */
    private PageInfo<EkbSnowSlideRelationPO> findFuzzy(EkbFaultSnowSlideDTO ekbFaultSnowSlideDTO, ArrayList<EkbSnowSlideRelationPO> ekbSnowSlideRelations) {
        ArrayList<EkbSnowSlideRelationPO> ekbList = new ArrayList<>();
        String faultCode = ekbFaultSnowSlideDTO.getFaultCode();
        String nameCn = ekbFaultSnowSlideDTO.getNameCn();
        Integer pageNum = ekbFaultSnowSlideDTO.getPageNum();
        Integer pageSize = ekbFaultSnowSlideDTO.getPageSize();
        PageInfo<EkbSnowSlideRelationPO> pageInfo = new PageInfo<>();
        if(StringUtils.isNotEmpty(faultCode) && StringUtils.isNotEmpty(nameCn)){
            for (int i = 0; i < ekbSnowSlideRelations.size(); i++) {
                if(ekbSnowSlideRelations.get(i).getFaultCodes().contains(faultCode) &&
                        ekbSnowSlideRelations.get(i).getNameCnSplice().contains(nameCn)){
                    ekbList.add(ekbSnowSlideRelations.get(i));
                }
            }
        }else if(StringUtils.isNotEmpty(faultCode)){
            for (int i = 0; i < ekbSnowSlideRelations.size(); i++) {
                if(ekbSnowSlideRelations.get(i).getFaultCodes().contains(faultCode)){
                    ekbList.add(ekbSnowSlideRelations.get(i));
                }
            }
        }else{
            for (int i = 0; i < ekbSnowSlideRelations.size(); i++) {
                if(ekbSnowSlideRelations.get(i).getNameCnSplice().contains(nameCn)){
                    ekbList.add(ekbSnowSlideRelations.get(i));
                }
            }
        }
        //分页
        List<EkbSnowSlideRelationPO> pageList;
        Integer total = ekbList.size();
        if(pageSize == null || pageSize == 0){
            pageSize = 10;
        }
        Integer pageCount = (total-1)/pageSize+1;
        if(pageNum == null || pageNum == 0){
            pageNum = 1;
        }
        if(pageNum.equals(pageCount)){
            pageList = ekbList.subList((pageNum-1)*pageSize,total);
        }else{
            pageList = ekbList.subList((pageNum-1)*pageSize,pageNum*pageSize);
        }
        pageInfo.setTotal(total);
        pageInfo.setList(pageList);
        pageInfo.setPages(pageCount);
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setSize(pageList.size());
        return pageInfo;
    }

    /**
     * 查询并处理所有雪崩数据
     * @param vehicleTypeId
     * @return
     */
    private ArrayList<EkbSnowSlideRelationPO> disposeFaultSnowSlideRelations(String vehicleTypeId) {
        //查询入度为零的parentFaultTypeKey
        List<String> listParentFaultTypeKey = ekbFaultSnowSlideMapping.findFirstParentFaultTypeKey(vehicleTypeId);
        //查询所有非顶点的数据
        List<EkbFaultSnowSlidePO> listEkbFaultSnowSlide = ekbFaultSnowSlideMapping.findListEkbFaultSnowSlide(vehicleTypeId);
        //处理FirstEkbFaultSnowSlide数据,组成雪崩关系树
        List<List<EkbFaultSnowSlidePO>> list = new ArrayList<>();
        Map<String,Integer> hierarchyMap = new HashMap<>();
        if(listParentFaultTypeKey != null && listParentFaultTypeKey.size()>0){
            for (int i = 0; i < listParentFaultTypeKey.size(); i++) {
                List<EkbFaultSnowSlidePO> listTree = new ArrayList<>();
                List<String> listFaultTypeKey = new ArrayList<>();
                String currentKey = listParentFaultTypeKey.get(i);
                listFaultTypeKey.add(currentKey);
                hierarchyMap.put(listParentFaultTypeKey.get(i),0);
                //根据parentFaultTypeKey进行深度遍历
                List<EkbFaultSnowSlidePO> listNotFirst = disposeNotFirst(listFaultTypeKey, vehicleTypeId,
                        listEkbFaultSnowSlide,hierarchyMap,currentKey);
                if(listNotFirst != null && listNotFirst.size()>0){
                    listTree.addAll(listNotFirst);
                    //保存雪崩树和对应的层级数
                    list.add(listTree);
                }
            }
        }
        //将雪崩树有交集的合并成雪崩网
        mergeListTree(list);
        //将雪崩网解析成对象给前端
        ArrayList<EkbSnowSlideRelationPO> ekbSnowSlideRelations = parseSnowSlide(list,listParentFaultTypeKey,hierarchyMap);
        if(ekbSnowSlideRelations != null && ekbSnowSlideRelations.size()>0){
            ekbSnowSlideRelations.sort(Comparator.comparing(EkbSnowSlideRelationPO::getFaultCodes));
        }
        return ekbSnowSlideRelations;
    }

    /**
     * 将数据封装成集合
     * @param list
     * @param listParentFaultTypeKey
     * @param hierarchyMap
     * @return
     */
    private ArrayList<EkbSnowSlideRelationPO> parseSnowSlide(List<List<EkbFaultSnowSlidePO>> list,
                                                             List<String> listParentFaultTypeKey,
                                                             Map<String, Integer> hierarchyMap) {
        ArrayList<EkbSnowSlideRelationPO> ekbSnowSlideRelations = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            List<EkbFaultSnowSlidePO> listPO = list.get(i);
            EkbSnowSlideRelationPO snowSlideVO = new EkbSnowSlideRelationPO();
            JSONObject json = new JSONObject();
            JSONObject jsonSource = new JSONObject();
            if(listPO != null && listPO.size()>0){
                List<SnowSlideEdgePO> edges = new ArrayList<>();
                List<SnowSlideNodePO> nodes = new ArrayList<>();
                Map<String,SnowSlideNodePO> nodeMap = new HashMap<>();
                HashMap<String,String> faultCodeMap = new HashMap<>();
                Map<String,String> NameCnMap = new HashMap<>();
                String vehicleTypeName = listPO.get(0).getVehicleTypeName();
                Integer hierarchy = 1;
                for (int j = 0; j < listPO.size(); j++) {
                    SnowSlideNodePO currentNode = new SnowSlideNodePO();
                    SnowSlideNodePO parentNode = new SnowSlideNodePO();
                    SnowSlideEdgePO edge = new SnowSlideEdgePO();
                    String faultTypeKey = listPO.get(j).getFaultTypeKey();
                    String parentFaultTypeKey = listPO.get(j).getParentFaultTypeKey();
                    //取出层级最大的值
                    for (String key : listParentFaultTypeKey) {
                        Integer currentHierarchy = hierarchyMap.get(key);
                        if(key.equals(parentFaultTypeKey) && currentHierarchy>hierarchy){
                            hierarchy = hierarchyMap.get(key);
                        }
                    }
                    //子故障节点
                    currentNode.setShape(Constants.NODESHAPE);
                    currentNode.setImgStr(Constants.RESTSIMG);
                    currentNode.setNodeId(faultTypeKey);
                    currentNode.setFaultTypeKey(faultTypeKey);
                    currentNode.setFaultNameCn(listPO.get(j).getNameCn());
                    //父故障节点
                    parentNode.setShape(Constants.NODESHAPE);
                    parentNode.setImgStr(Constants.RESTSIMG);
                    parentNode.setNodeId(parentFaultTypeKey);
                    parentNode.setFaultTypeKey(parentFaultTypeKey);
                    parentNode.setFaultNameCn(listPO.get(j).getParentNameCn());
                    //设置线信息
                    edge.setShape(Constants.EDGESHAPE);
                    edge.setId(UUIDUtils.generateUuid());
                    edge.setTime(listPO.get(j).getCorrelationTime() +"(ms)");
                    edge.setSourceId(listPO.get(j).getParentFaultTypeKey());
                    edge.setTargetId(listPO.get(j).getFaultTypeKey());
                    edge.setEnable(listPO.get(j).getEnable()?1:0);
                    //节点按照key：node保存到map中去重
                    nodeMap.put(faultTypeKey,currentNode);
                    nodeMap.put(parentFaultTypeKey,parentNode);
                    //节点名称和故障代码去重
                    faultCodeMap.put(faultTypeKey,listPO.get(j).getFaultCode());
                    faultCodeMap.put(parentFaultTypeKey,listPO.get(j).getParentFaultCode());
                    NameCnMap.put(faultTypeKey,listPO.get(j).getNameCn());
                    NameCnMap.put(parentFaultTypeKey,listPO.get(j).getParentNameCn());
                    //保存线信息
                    edges.add(edge);
                }
                //处理节点信息
                for(SnowSlideNodePO node : nodeMap.values()){
                    nodes.add(node);
                }
                //处理节点名称
                List<String> nameCns = new ArrayList<>();
                String nameCnSplice = "";
                StringBuilder builderNameCn = new StringBuilder(nameCnSplice);
                for(String nameCn : NameCnMap.values()){
                    builderNameCn.append(nameCn).append(";");
                    nameCns.add(nameCn);
                }
                nameCnSplice = builderNameCn.toString();
                //处理节点故障代码
                String faultCodes = "";
                StringBuilder builderCode = new StringBuilder(faultCodes);
                for(String faultCode : faultCodeMap.values()){
                    builderCode.append(faultCode).append(";");
                }
                faultCodes = builderCode.toString();
                //处理关系数据
                json.put("nodes",nodes);
                json.put("edges",edges);
                jsonSource.put("source",json);
                //将对象转成json格式s
                String jsonStr = JSON.toJSONString(jsonSource, SerializerFeature.DisableCircularReferenceDetect);
                JSONObject jsonSnowSlideVO = JSONObject.parseObject(jsonStr);
                //数据封装成对象
                snowSlideVO.setVehicleTypeName(vehicleTypeName);
                snowSlideVO.setSnowSlideRelation(jsonSnowSlideVO);
                snowSlideVO.setNameCns(nameCns);
                snowSlideVO.setNameCnSplice(nameCnSplice);
                snowSlideVO.setFaultNum(NameCnMap.size());
                snowSlideVO.setHierarchy(hierarchy);
                snowSlideVO.setFaultCodes(faultCodes);
            }
            ekbSnowSlideRelations.add(snowSlideVO);
        }
        return ekbSnowSlideRelations;
    }

    /**
     * 雪崩树处理，组成雪崩网
     * @param list
     */
    private void mergeListTree(List<List<EkbFaultSnowSlidePO>> list) {
        if(list != null && list.size()>0){
            first:for (int i = 0; i < list.size(); i++) {
                List<String> currentFaultTypeKeyList = new ArrayList<>();
                List<EkbFaultSnowSlidePO> currentList = new ArrayList<>();
                currentList.addAll(list.get(i));
                if(currentList != null && currentList.size()>0){
                    for (EkbFaultSnowSlidePO ekbFaultSnowSlidePO : currentList) {
                        currentFaultTypeKeyList.add(ekbFaultSnowSlidePO.getFaultTypeKey());
                        currentFaultTypeKeyList.add(ekbFaultSnowSlidePO.getParentFaultTypeKey());
                    }
                }
                second:for (int j = i+1; j < list.size(); j++) {
                    List<EkbFaultSnowSlidePO> faultSnowSlideList = new ArrayList<>();
                    faultSnowSlideList.addAll(list.get(j));
                    if(faultSnowSlideList != null && faultSnowSlideList.size()>0){
                        thirdly:for (EkbFaultSnowSlidePO ekbFaultSnowSlidePO : faultSnowSlideList) {
                            String faultKey = ekbFaultSnowSlidePO.getFaultTypeKey();
                            String parentKey = ekbFaultSnowSlidePO.getParentFaultTypeKey();
                            last:for (int k = 0; k < currentFaultTypeKeyList.size(); k++) {
                                String blag = currentFaultTypeKeyList.get(k);
                                if(StringUtils.isNotEmpty(blag) && (blag.equals(faultKey) || blag.equals(parentKey))){
                                    list.get(i).addAll(faultSnowSlideList);
                                    list.remove(j);
                                    i--;
                                    break second;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 深度遍历 查出所有雪崩树
     * @param listFaultTypeKey
     * @param vehicleTypeId
     * @param listEkbFaultSnowSlide
     * @return
     */
    private List<EkbFaultSnowSlidePO> disposeNotFirst(List<String> listFaultTypeKey,
                                                      String vehicleTypeId,
                                                      List<EkbFaultSnowSlidePO> listEkbFaultSnowSlide,
                                                      Map<String,Integer> map,
                                                      String currentKey) {
        List<EkbFaultSnowSlidePO> listAllNotFirst = new ArrayList<>();
        if(listFaultTypeKey != null && listFaultTypeKey.size()>0 && listEkbFaultSnowSlide != null){
            //通过当前key集合遍历出下一层，添加到集合后删除
            for (int i = 0; i < listFaultTypeKey.size(); i++) {
                String faultTypeKey = listFaultTypeKey.get(i);
                for (int j = 0; j < listEkbFaultSnowSlide.size(); j++) {
                    String parentFaultTypeKey = listEkbFaultSnowSlide.get(j).getParentFaultTypeKey();
                    String currentVehicleTypeId = listEkbFaultSnowSlide.get(j).getVehicleTypeId();
                    if(vehicleTypeId == null){
                        if(faultTypeKey != null && faultTypeKey.equals(parentFaultTypeKey)){
                            listAllNotFirst.add(listEkbFaultSnowSlide.get(j));
                            listEkbFaultSnowSlide.remove(j);
                            j--;
                        }
                    }else {
                        if(faultTypeKey != null && faultTypeKey.equals(parentFaultTypeKey) && vehicleTypeId.equals(currentVehicleTypeId)){
                            listAllNotFirst.add(listEkbFaultSnowSlide.get(j));
                            listEkbFaultSnowSlide.remove(j);
                            j--;
                        }
                    }
                }
            }
            map.put(currentKey,map.get(currentKey)+1);
            listFaultTypeKey.clear();
            for (int i = 0; i < listAllNotFirst.size(); i++) {
                listFaultTypeKey.add(listAllNotFirst.get(i).getFaultTypeKey());
            }
            listAllNotFirst.addAll(disposeNotFirst(listFaultTypeKey,vehicleTypeId, listEkbFaultSnowSlide,map,currentKey));
        }
        return listAllNotFirst;
    }

    /**
     * @Description 根据faultTypeKey删除故障雪崩相关数据
     * @Param faultTypeKey集合
     * @Return int
     * <AUTHOR> zhijian
     * @Date 2020/9/16 15:33
     */
    public int deleteSnowSlideByKeys(List<String> faultTypeKeyList) {
        return ekbFaultSnowSlideMapping.deleteSnowSlideByKeys(faultTypeKeyList);
    }

    public List<String> findFaultTypeKeyByParentKey(List<String> faultTypeKeyList) {
        List<String> lowerFaultTypeKey = new ArrayList<>();
        try{
            if(!CollectionUtils.isEmpty(faultTypeKeyList)){
                lowerFaultTypeKey = ekbFaultSnowSlideMapping.findFaultTypeKeyByParentKey(faultTypeKeyList);
            }
        }catch(DataAccessException e){
            logger.error("Method[listExpertKnowledgeBase] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
        return lowerFaultTypeKey;
    }
}
