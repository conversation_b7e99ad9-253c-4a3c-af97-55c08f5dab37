package cc.crrc.manage.service.ekb;

import cc.crrc.manage.common.annotation.ParamReplace;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.StringUtils;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.ekb.*;
import cc.crrc.manage.pojo.ekb.EkbFaultMeasurePO;
import cc.crrc.manage.pojo.ekb.EkbFaultMeasureVO;
import cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO;
import cc.crrc.manage.pojo.ekb.EkbReasonMeasurePO;
import cc.crrc.manage.service.SysFileService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import com.google.common.escape.Escaper;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.tomcat.util.security.Escape;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @FileName EkbFaultMeasureService
 * <AUTHOR> yuxi
 * @Date 2019/11/9 10:11
 * @Version 1.0
 **/
@Service
public class EkbFaultMeasureService {
    private final Logger logger = LoggerFactory.getLogger(EkbFaultMeasureService.class);
    @Autowired
    EkbFaultMeasureMapping faultMeasureMapping;
    @Autowired
    EkbReasonMeasureMapping reasonMeasureMapping;
    @Autowired
    EkbFaultTypeMapping faultTypeMapping;
    @Autowired
    EkbFaultTypeReasonMapping faultTypeReasonMapping;
    @Autowired
    EkbFaultReasonMapping faultReasonMapping;
    @Autowired
    SysFileService fileService;

    /**
     * @return com.github.pagehelper.PageInfo<cc.crrc.manage.pojo.ekb.EkbFaultMeasureVO>
     * @Description 查询故障措施列表
     * <AUTHOR> yuxi
     * @Date 14:18 2019/11/9
     * @Param [faultMeasure]
     **/
    public PageInfo<EkbFaultMeasureVO> listFaultMeasure(EkbFaultMeasureVO faultMeasure) {
        try {
            //分页
            int currentPage = faultMeasure.getPageNumber();
            int pageSize = faultMeasure.getPageSize();
            PageHelper.startPage(currentPage, pageSize);
            //获取数据
            List<EkbFaultMeasureVO> resultList = faultMeasureMapping.listFaultMeasure(faultMeasure);
            return new PageInfo<>(resultList);
        } catch (DataAccessException e) {
            logger.error("Method[listFaultMeasure] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @return java.util.List<cc.crrc.manage.pojo.ekb.EkbFaultMeasureVO>
     * @Description 按照措施编码、措施类别、措施内容同时模糊查询（仅新增故障类型时调用）
     * <AUTHOR> yuxi
     * @Date 15:13 2019/11/25
     * @Param [keyWord]
     **/
    public List<EkbFaultMeasureVO> listFaultMeasureVaguely(String id, String keyword) {
        try {
            return faultMeasureMapping.listFaultMeasureVaguely(id, keyword);
        } catch (DataAccessException e) {
            logger.error("Method[listFaultMeasureVaguely] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @return int
     * @Description 新增故障措施
     * <AUTHOR> yuxi
     * @Date 14:14 2019/11/9
     * @Param []
     **/
    public int insertFaultMeasure(EkbFaultMeasurePO faultMeasure) {
        try {
            // 添加主键
            faultMeasure.setId(PrimaryKeyGenerator.generatorId());
            // 生成业务主键
            String measureKey = faultMeasure.getVehicleTypeId() + "_" + faultMeasure.getMeasureCode();
            faultMeasure.setMeasureKey(measureKey);
            // 做成人和时间
            String userId = UserUtils.getUserId();
            faultMeasure.setCreateBy(userId);
            faultMeasure.setModifyBy(userId);
            return faultMeasureMapping.insertFaultMeasure(faultMeasure);
        } catch (DataAccessException e) {
            logger.error("Method[insertFaultMeasure] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * @return int
     * @Description 更新故障措施
     * <AUTHOR> yuxi
     * @Date 14:15 2019/11/9
     * @Param [faultMeasure]
     **/
    public int updateFaultMeasure(EkbFaultMeasurePO faultMeasure) {
        try {
            String userId = UserUtils.getUserId();
            faultMeasure.setModifyBy(userId);
            return faultMeasureMapping.updateFaultMeasure(faultMeasure);
        } catch (DataAccessException e) {
            logger.error("Method[updateFaultMeasure] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    /**
     * @return int
     * @Description 删除故障措施
     * <AUTHOR> yuxi
     * @Date 14:15 2019/11/9
     * @Param [id]
     **/
    public int deleteFaultMeasure(String idStr) {
        try {
            int count = 0;
            if (StringUtil.isNotEmpty(idStr)) {
                String[] idsArr = idStr.split(",");
//                Long[] ids = new Long[idsArr.length];
//                for (int i = 0, len = idsArr.length; i < len; i++) {
//                    ids[i] = new Long(idsArr[i]);
//                }
                List<String> measureList = faultMeasureMapping.findMeasureKeyByIds(idsArr);
                count = faultMeasureMapping.deleteFaultMeasure(idsArr);
                //根据measureKey集合删除故障原因和故障措施关联关系
                if(measureList != null && measureList .size()>0){
                    int rows = reasonMeasureMapping.deleteReasonMeasureByMeasureKeys(measureList);
                }
            }
            return count;
        } catch (DataAccessException e) {
            logger.error("Method[deleteFaultMeasure] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    /**
     * @return void
     * @Description 删除故障原因和故障措施关系数据
     * <AUTHOR> yuxi
     * @Date 15:46 2019/11/14
     * @Param [faultTypeKey, faultReasonKey, faultMeasureKey]
     **/
    public void deleteReasonMeasure(String faultTypeKey, String faultReasonKey, String faultMeasureKey) {
        try {
            reasonMeasureMapping.deleteReasonMeasure(faultTypeKey, faultReasonKey, faultMeasureKey);
        } catch (DataAccessException e) {
            logger.error("Method[deleteReasonMeasure] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    /**
     * @return cc.crrc.manage.pojo.ekb.EkbReasonMeasurePO
     * @Description 取得原因措施关系
     * <AUTHOR> yuxi
     * @Date 11:07 2019/11/12
     * @Param [faultTypeKey, faultReasonKey, faultMeasureKey]
     **/
    public List<EkbReasonMeasurePO> getReasonMeasure(String faultTypeKey, String faultReasonKey, String faultMeasureKey) {
        try {
            return reasonMeasureMapping.listReasonMeasure(faultTypeKey, faultReasonKey, faultMeasureKey);
        } catch (DataAccessException e) {
            logger.error("Method[getReasonMeasure] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @return int
     * @Description 更新原因措施关系
     * <AUTHOR> yuxi
     * @Date 11:08 2019/11/12
     * @Param [reasonMeasure]
     **/
    public int updateReasonMeasureRealCounter(EkbReasonMeasurePO reasonMeasure) {
        try {
            return reasonMeasureMapping.updateReasonMeasureRealCounter(reasonMeasure);
        } catch (DataAccessException e) {
            logger.error("Method[updateReasonMeasureRealCounter] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    /**
     * @return java.util.List<cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO>
     * @Description 查询故障知识库
     * <AUTHOR> yuxi
     * @Date 14:17 2019/11/12
     * @Param [faultType]
     **/
    @ParamReplace(param = {"nameCn","nameEn","faultReason","overhaulSuggestions","frontlineDisposalRecommendations"},type = EkbFaultTypeDTO.class)
    public PageInfo<EkbFaultTypeDTO> listExpertKnowledgeBase(EkbFaultTypeDTO faultTypeDTO) {
        try {
            // 分页
            int currentPage = faultTypeDTO.getPageNumber();
            int pageSize = faultTypeDTO.getPageSize();
            PageHelper.startPage(currentPage, pageSize);
            List<EkbFaultTypeDTO> faultTypeList = faultTypeMapping.listEkbFaultType(faultTypeDTO);
            return new PageInfo<>(faultTypeList);
        } catch (DataAccessException e) {
            logger.error("Method[listExpertKnowledgeBase] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    //遍历faultTypeList，取得faultTypeKey，通过faultTypeKey查询未使能个数
    public List<EkbFaultTypeDTO> setEnableStatus(List<EkbFaultTypeDTO> faultTypeList) {
        try {
            for (EkbFaultTypeDTO ekbFaultTypeDTO : faultTypeList) {
                String faultTypeKey = ekbFaultTypeDTO.getFaultTypeKey();
                List<Boolean> numOfDisabledNodes = faultTypeMapping.getNumbeOfDisabledNodes(faultTypeKey);
                if (numOfDisabledNodes != null && numOfDisabledNodes.size() > 0 &&
                        !numOfDisabledNodes.contains(false)) {
                    ekbFaultTypeDTO.setEnable(true);
                }
            }
            return faultTypeList;
        } catch (DataAccessException e) {
            logger.error("Method[setEnableStatus] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }

    }


}
