package cc.crrc.manage.service.ekb;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UUIDUtils;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.ekb.*;
import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.ekb.*;
import cc.crrc.manage.service.SysDictService;
import cc.crrc.manage.service.SysFileService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class EkbFaultTypeService {

    @Autowired
    private EkbFaultTypeMapping ekbFaultTypeMapping;
    @Autowired
    private EkbFaultTypeReasonMapping ekbFaultTypeReasonMapping;
    @Autowired
    private EkbReasonMeasureMapping ekbReasonMeasureMapping;
    @Autowired
    private EkbFaultTypeFileMapping ekbFaultTypeFileMapping;
    @Autowired
    private SysFileService sysFileService;
    @Autowired
    private EkbFaultMeasureService faultMeasureService;
    @Autowired
    private EkbFaultSnowSlideMapping ekbFaultSnowSlideMapping;

    public final static String UNIQUE_EXCEPTION_MESSAGE = "code_name_location_category_unique";

    private final Logger logger = LoggerFactory.getLogger(SysDictService.class);

    /**
     * @return java.lang.Object
     * @Description 故障类型新增
     * <AUTHOR>
     * @Date 2019/11/09
     * @Param [ekbFaultTypeDTO]
     **/
    @Transactional(rollbackFor = Exception.class)
    public Object addEkbFaultType(EkbFaultTypeDTO ekbFaultTypeDTO) {
        try {
            String faultTypeKey = UUIDUtils.generateUuid();
            /*相关业务1:故障类型基础信息填充 fault_type_key字段暂用uuid生成 暂未确定 */
            ekbFaultTypeDTO.setFaultTypeKey(faultTypeKey);
            ekbFaultTypeDTO.setId(PrimaryKeyGenerator.generatorId());
            ekbFaultTypeDTO.setCreateBy(UserUtils.getUserId());
            ekbFaultTypeDTO.setModifyBy(UserUtils.getUserId());
            ekbFaultTypeDTO.setDelFlag(0);
            //2020-3-18 lixin 故障车厢业务处理
            String[] vehicleStructureCode = ekbFaultTypeDTO.getVehicleStructureCode().split("/");
            int vehicleStructureCodeSize = vehicleStructureCode.length;
            if (vehicleStructureCodeSize == 1) {
                ekbFaultTypeDTO.setLocation(vehicleStructureCode[0]);
            } else {
                ekbFaultTypeDTO.setLocation(vehicleStructureCode[1]);
            }
            /*相关业务2:故障原因关联*/
            addAllRelation(ekbFaultTypeDTO, faultTypeKey);
            return ekbFaultTypeMapping.addEkbFaultType(ekbFaultTypeDTO);
        } catch (DataAccessException e) {
            logger.error("Method[addDict] Error:{}", e.getMessage());
            if (e.getCause().getMessage().contains(UNIQUE_EXCEPTION_MESSAGE)) {
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION,
                        "已存在相同故障(" + String.join(",",
                                "故障代码：" + ekbFaultTypeDTO.getFaultCode(),
                                "故障中文名称：" + ekbFaultTypeDTO.getNameCn(),
                                "故障位置：" + ekbFaultTypeDTO.getLocation()) + ")");
            }
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * @return java.lang.Object
     * @Description 故障类型删除
     * <AUTHOR>
     * @Date 2019/11/09
     * @Param [idStr]
     **/
    @Transactional(rollbackFor = Exception.class)
    public Object deleteEkbFaultType(String idStr) {
        int count = 0;
        if (StringUtil.isNotEmpty(idStr)) {
            String[] idsArr = idStr.split(",");
            EkbFaultTypeDTO ekbFaultTypeDTO;
            for (String idNow : idsArr) {
                try {
                    /*相关业务1:故障原因关联删除*/
                    ekbFaultTypeDTO = ekbFaultTypeMapping.getEkbFaultTypeById(idNow);
                    if (ekbFaultTypeDTO == null) {
                        continue;
                    }
                    List<EkbFaultSnowSlidePO> snowSlidePOList = ekbFaultSnowSlideMapping.getSnowSlide(ekbFaultTypeDTO.getFaultTypeKey(), null);
                    if (snowSlidePOList.size() != 0) {
                        throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION, ekbFaultTypeDTO.getFaultCode() + "存在故障抑制关系，不可删除");
                    }
                    deleteAll(ekbFaultTypeDTO);
                    /*相关业务3:逻辑删除本条故障类型*/
                    ekbFaultTypeMapping.deleteEkbFaultType(idNow);
                    count++;
                } catch (DataAccessException e) {
                    logger.error("Method[deleteEkbFaultType] Error:{}", e.getMessage());
                    throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
                }
            }
        }
        return count;
    }

    /**
     * @return java.util.List<cc.crrc.manage.pojo.ekb.EkbFaultTypeVO>
     * @Description 故障类型修改
     * <AUTHOR>
     * @Date 2019/11/09
     * @Param [ekbFaultTypeVO]
     * <p>
     * MITE changed：改为直接存，原有故障相关信息不再传进来；
     **/
    @Transactional(rollbackFor = Exception.class)
    public Object updateEkbFaultType(EkbFaultTypeDTO ekbFaultTypeDTO) {
        try {
            /*相关业务1:故障类型基础信息*/
            ekbFaultTypeDTO.setModifyBy(UserUtils.getUserId());
            //2020-3-18 lixin 故障车厢业务处理
            String[] vehicleStructureCode = ekbFaultTypeDTO.getVehicleStructureCode().split("/");
            int vehicleStructureCodeSize = vehicleStructureCode.length;
            if (vehicleStructureCodeSize == 1) {
                ekbFaultTypeDTO.setLocation("ALL");
            } else {
                ekbFaultTypeDTO.setLocation(vehicleStructureCode[1]);
            }
            /*相关业务2:先删除原有的故障原因 故障措施 故障关联文件的 关系*/
            //deleteAll(ekbFaultTypeDTO);
            /*相关业务3:故障相关原因 措施 重新新增*/
            //重新添加关系
            //String faultTypeKey = ekbFaultTypeDTO.getFaultTypeKey();
            //addAllRelation(ekbFaultTypeDTO, faultTypeKey);
            return ekbFaultTypeMapping.updateEkbFaultType(ekbFaultTypeDTO);
        } catch (DataAccessException e) {
            logger.error("Method[addDict] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    /**
     * @return void
     * @Description 添加关系集合方法  增加故障类型相关的关系  也增加了故障类型关联的sys_file文件表信息
     * <AUTHOR>
     * @Date 2019/11/14
     * @Param [ekbFaultTypeDTO, faultTypeKey]
     **/
    private void addAllRelation(EkbFaultTypeDTO ekbFaultTypeDTO, String faultTypeKey) {
        List<EkbFaultReasonDTO> faultReasonList = ekbFaultTypeDTO.getFaultReasonList();
        //如果没有Reasonlist说明此步没必要执行
        if (!CollectionUtils.isEmpty(faultReasonList)) {
            saveFaultTypeReason(faultReasonList, faultTypeKey);
        }
    }

    /**
     * @return void
     * @Description 删除集合方法  删除故障类型相关的关系
     * <AUTHOR>
     * @Date 2019/11/13
     * @Param [ekbFaultTypeDTO]
     **/
    private void deleteAll(EkbFaultTypeDTO ekbFaultTypeDTO) {
        String faultTypeKey = null;
        if (ekbFaultTypeDTO != null) {
            faultTypeKey = ekbFaultTypeDTO.getFaultTypeKey();
        }
        if (StringUtil.isNotEmpty(faultTypeKey)) {
            //删除故障类型与故障原因的关联关系
            ekbFaultTypeReasonMapping.deleteEkbFaultTypeReason(faultTypeKey);
            //删除故障类型与故障原因故障措施之间的关联关系
            ekbReasonMeasureMapping.deleteReasonMeasure(faultTypeKey, null, null);
        }
    }

    /**
     * @return java.util.List<cc.crrc.manage.pojo.ekb.EkbFaultTypeVO>
     * @Description 分页取得故障类型列表
     * <AUTHOR>
     * @Date 2019/11/09
     * @Param [ekbFaultTypeVO]
     **/
    public Object listEkbFaultType(EkbFaultTypeDTO ekbFaultTypeDTO) {
        try {
            return faultMeasureService.listExpertKnowledgeBase(ekbFaultTypeDTO);
        } catch (DataAccessException e) {
            logger.error("Method[listEkbFaultType] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @return java.lang.Object
     * @Description 保存文件与故障类型的关系 和保存文件基础信息
     * <AUTHOR>
     * @Date 2019/11/13
     * @Param [sysFile, faultTypeKey]
     * 原来是多文件记录同事保存 现在每个文件保存删除单独操作所以更新成以下方法
     **/
    @Transactional(rollbackFor = Exception.class)
    public Object saveFaultTypeFile(SysFilePO sysFile, String faultTypeKey) {
        try {
            //文件属性存入 sysFile表
            sysFileService.addSysFile(sysFile);
            //保存文件与故障类型的关系
            String fileId = sysFile.getId();
            return ekbFaultTypeFileMapping.saveFaultTypeFile(fileId, faultTypeKey);
        } catch (DataAccessException e) {
            logger.error("Method[saveFaultTypeFile] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }

    }

    /**
     * @return java.lang.Object
     * @Description 删除文件与故障类型的关系 和删除文件基础信息
     * <AUTHOR>
     * @Date 14:54 2019/11/28
     * @Param [faultTypeKey, fileId]
     **/
    @Transactional(rollbackFor = Exception.class)
    public Object deleteEkbFaultTypeFile(String faultTypeKey, String fileId) {
        try {
            //故障类型文件关联 关联关系 删除
            ekbFaultTypeFileMapping.deleteFaultTypeFile(faultTypeKey, fileId);
            //相关业务2:sysFile文件主表关联删除
            return sysFileService.deleteSysFile(fileId);
        } catch (DataAccessException e) {
            logger.error("Method[deleteEkbFaultTypeFile] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION.getErrorCode(), "删除故障类型关联文件失败");
        }
    }

    /**
     * @return void
     * @Description 循环保存 故障原因与故障类型的关系
     * <AUTHOR>
     * @Date 2019/11/12
     * @Param [faultReasonList, faultTypeKey]
     * 新增故障类型子方法 2  保存故障原因与故障类型的关系
     **/
    public Object saveFaultTypeReason(List<EkbFaultReasonDTO> faultReasonList, String faultTypeKey) {
        for (EkbFaultReasonDTO ekbFaultReason : faultReasonList) {
            String faultReasonKey = ekbFaultReason.getReasonKey();
            Long initCounter = ekbFaultReason.getInitCounter();
            Long realCounter = ekbFaultReason.getRealCounter();
            //添加本条故障原因与故障类型的关系
            if (StringUtil.isNotEmpty(faultReasonKey)) {
                try {
                    ekbFaultTypeReasonMapping.addFaultTypeReasonRelation(faultReasonKey, faultTypeKey, initCounter, realCounter);
                } catch (DataAccessException e) {
                    logger.error("Method[saveFaultTypeReason] Error:{}", e.getMessage());
                    throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "故障原因 与故障类型关联关系保存失败！");
                }
//
//                /*本条原因对应的故障措施关联逻辑*/
//                List<EkbFaultMeasureVO> ekbFaultMeasureList = ekbFaultReason.getFaultMeasureList();
//                if (!CollectionUtils.isEmpty(ekbFaultMeasureList)) {
//                    //如果ekbFaultMeasureList有值 则进行关系保存
//                    saveReasonMeasure(ekbFaultMeasureList, faultReasonKey, faultTypeKey);
//                }
            }

        }
        return "SUCCESS";
    }

    /**
     * @return void
     * @Description 循环保存 故障原因与故障措施的关系
     * <AUTHOR>
     * @Date 2019/11/12
     * @Param [ekbFaultMeasureList, faultReasonKey, faultTypeKey]
     * 新增故障类型子方法 3  保存故障原因与故障措施的关系
     **/
    public Object saveReasonMeasure(List<EkbFaultMeasureVO> ekbFaultMeasureList, String faultReasonKey, String faultTypeKey) {
        EkbReasonMeasurePO ekbReasonMeasure;
        for (EkbFaultMeasureVO ekbFaultMeasure : ekbFaultMeasureList) {
            //循环添加关系
            ekbReasonMeasure = new EkbReasonMeasurePO();
            ekbReasonMeasure.setFaultTypeKey(faultTypeKey);
            ekbReasonMeasure.setFaultReasonKey(faultReasonKey);
            ekbReasonMeasure.setRealCounter(ekbFaultMeasure.getRealCounter());
            ekbReasonMeasure.setInitCounter(ekbFaultMeasure.getInitCounter());
            String faultMeasureKey = ekbFaultMeasure.getMeasureKey();
        }
        return "SUCCESS";

    }

    public Object getEkbFaultTypeFileList(String faultTypeKey) {
        try {
            //故障类型文件关联 关联关系list
            return ekbFaultTypeFileMapping.getEkbFaultTypeFileList(faultTypeKey);
        } catch (DataAccessException e) {
            logger.error("Method[getEkbFaultTypeFileList] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询故障类型关联文件失败！");
        }
    }

    public Object getEkbFaultTypeForVehicleTypeList(String structureCode, String vehicleTypeId, int pageNumber, int pageSize) {
        try {
            //分页
            PageHelper.startPage(pageNumber, pageSize);
            //查询故障数据
            List<EkbFaultTypeDTO> listEkbFaultType = ekbFaultTypeMapping.getEkbFaultTypeForVehicleTypeList(structureCode, vehicleTypeId);
            return new PageInfo<>(listEkbFaultType);
        } catch (DataAccessException e) {
            logger.error("Method[getEkbFaultTypeForVehicleTypeList] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询车型关联的故障字典失败！");
        }
    }

    /**
     * @return java.util.List<cc.crrc.manage.pojo.ekb.EkbFaultTypeVO>
     * @Description 查询故障列表(子网节点关联故障 ， 单击节点 ， 选择故障)
     * <AUTHOR>
     * @Date 2020/03/31
     * @Param [ekbFaultTypeVO]
     **/
    public List<EkbFaultTypeDTO> listFaultname(String nameCn, String faultTypeKey) {
        try {
            String[] a = faultTypeKey.split(",");
            return ekbFaultTypeMapping.listEkbFaultTypeForSnowSlide(nameCn, a);
        } catch (DataAccessException e) {
            logger.error("Method[listEkbFaultType] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @param faultTypeKey
     * @param faultReasonKey
     * @return
     * <AUTHOR>
     */
    public Object deleteEkbFaultTypeReasonRelation(String faultTypeKey, String faultReasonKey) {
        try {
            ekbFaultTypeReasonMapping.deleteEkbFaultTypeReasonRelation(faultTypeKey, faultReasonKey);
        } catch (DataAccessException e) {
            logger.error("Method[deleteEkbFaultTypeReasonRelation] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
        return "SUCCESS";
    }

    public Object deleteEkbFaultReasonMeasureRelation(String faultTypeKey, String faultReasonKey,
                                                      String faultMeasureKey) {
        try {
            ekbFaultTypeReasonMapping.deleteEkbFaultReasonMeasureRelation(faultTypeKey, faultReasonKey, faultMeasureKey);
        } catch (DataAccessException e) {
            logger.error("Method[deleteEkbFaultReasonMeasureRelation] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
        return "SUCCESS";
    }

    public List<EkbFaultTypeDTO> getEkbFaultTypeAll() {
        return ekbFaultTypeMapping.getEkbFaultTypeAll();
    }
}
