package cc.crrc.manage.service;


import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.CodecUtils;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.UserManageMapping;
import cc.crrc.manage.pojo.User;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;


/**
 * @FileName UserManageService
 * <AUTHOR> yuxi
 * @Date 2019/6/5 9:40
 * @Version 1.0
 **/
@Service
public class UserManageService {
    private final Logger logger = LoggerFactory.getLogger(UserManageService.class);
    @Autowired
    private UserManageMapping userManageMapper;
    @Autowired
    private SysOrganizationService sysOrganizationService;
    @Autowired
    private SysRoleService sysRoleService;


    /**
     * @return com.github.pagehelper.PageInfo<cc.crrc.manage.pojo.User>
     * @Description 分页取得所有用户
     * <AUTHOR> yuxi
     * @Date 15:48 2019/6/13
     * @Param [user]
     **/
    public PageInfo<User> listUser(User user) {
        try {
            //分页
            int currentPage = Integer.valueOf(user.getPageNumber());
            int pageSize = Integer.valueOf(user.getPageSize());
            PageHelper.startPage(currentPage, pageSize);
            //获取数据
            List<User> resultlist = userManageMapper.listUser(user);
            PageInfo<User> pageInfo = new PageInfo<User>(resultlist);
            return pageInfo;
        } catch (DataAccessException e) {
            logger.error("Method[listUser] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @return cc.crrc.manage.pojo.User
     * @Description 通过username查询用户
     * <AUTHOR> yuxi
     * @Date 15:35 2019/6/6
     * @Param [user]
     **/
    public User getUserByUsername(String username) {
        try {
            return userManageMapper.getUserByUsername(username);
        } catch (DataAccessException e) {
            logger.error("Method[getUserByUsername] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @return cc.crrc.manage.pojo.User
     * @Description 通过id查询用户
     * <AUTHOR> yuxi
     * @Date 15:35 2019/6/6
     * @Param [user]
     **/
    public User getUserById(String id) {
        try {
            return userManageMapper.getUserById(id);
        } catch (DataAccessException e) {
            logger.error("Method[getUserById] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @return java.lang.Object
     * @Description 新增用户
     * <AUTHOR> yuxi
     * @Date 13:09 2019/6/5
     * @Param [user]
     **/
    @Transactional
    public Object addNewUser(User user) {
        try {
            Date date = new Date();
            // 密码加密
            String encodePassword = CodecUtils.passwordBcryptEncode(user.getPassword().trim());
            user.setPassword(encodePassword);
            user.setDelFlag("0");
            String userId = UserUtils.getUserId();
            //user.setOrganizationId(UserUtils.getOrganizationId());
            user.setCreateBy(userId);
            user.setCreateDate(date);
            user.setUpdateBy(userId);
            user.setUpdateDate(date);
            user.setIsSuperAdmin("0");
            //雪花算法添加主键
            user.setId(PrimaryKeyGenerator.generatorId());
            String organizationIds = user.getOrganizationIds();
            // 将roleIds处理成角色id字符串 zhangzhijian 2020-6-22
            user.setUserRoleIds(StringArrayToString(user));
            int a = userManageMapper.addNewUser(user);
            //分配部门
            if (organizationIds!=null && organizationIds!="") {
                sysOrganizationService.addOrganizationUserRelation(user.getOrganizationIds(), user.getId());
            }
            // 部门不能为空 heshenglun 2020-8-25
            else {
             throw new RestApiException(ExceptionInfoEnum. DATA_INSERT_EXCEPTION.getErrorCode(), "请选择部门！");
            	
            }
            //添加角色关系
            sysRoleService.addRoleUserRelation(user.getRoleIds(),user.getId());
            return a;
        } catch (DataAccessException e) {
            logger.error("Method[addNewUser] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * @return java.lang.Object
     * @Description 更新用户信息
     * <AUTHOR> yuxi
     * @Date 13:09 2019/6/5
     * @Param [user]
     **/
    public Object updateUser(User user) {
        try {
            // 密码加密
//            String encodePassword = CodecUtils.passwordBcryptEncode(user.getUsername().trim(),
//                    user.getPassword().trim());
            user.setPassword(null);
            //UserInfo loginUser = getLoginUser();
            user.setUpdateBy(UserUtils.getUserId());
            user.setUpdateDate(new Date());
            user.setTenantId(null);
            // 将roleIds处理成角色id字符串 zhangzhijian 2020-6-22
            user.setUserRoleIds(StringArrayToString(user));
            //更新 用户的组织
            String organizationIds = user.getOrganizationIds();
            if (organizationIds!=null && organizationIds!="") {
            	  sysOrganizationService.updateOrganizationUserRelation(user.getOrganizationIds(), user.getId());;
            }
            // 部门不能为空 heshenglun 2020-8-25
            else {
             throw new RestApiException(ExceptionInfoEnum. DATA_UPDATE_EXCEPTION.getErrorCode(), "请选择部门！");
            	
            }         
            //更新 角色关系
            sysRoleService.updateRoleUserRelation(user.getRoleIds(), user.getId());
            return userManageMapper.updateUser(user);
        } catch (DataAccessException e) {
            logger.error("Method[updateUser] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    /**
     * @Description 将roleIds处理成角色id字符串，以;做间隔 zhangzhijian 2020-6-22
     * @Name StringArrayToString
     * @Param User 用户信息
     * @Return 用户的所有角色id字符串
     * <AUTHOR> zhijian
     * @Date 2020/6/22 9:19
     */
    private String StringArrayToString(User user) {
        String userRoleIds = "";
        String[] roleIds = user.getRoleIds();
        if(user != null && roleIds != null && roleIds.length != 0 ){
            for (int i = 0; i < roleIds.length ; i++) {
                userRoleIds = userRoleIds + roleIds[i] + ";";
            }
        }
        return userRoleIds.substring(0,userRoleIds.length()-1);
    }

    /**
     * @return java.lang.Object
     * @Description 通过id删除用户（逻辑删除）
     * <AUTHOR> yuxi
     * @Date 14:41 2019/6/10
     * @Param [user]
     **/
    public Object deleteUserById(String id) {
        if (id.equals(0)) {
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION.getErrorCode(), "超级管理员账户不可删除");
        }
        try {
            User user = userManageMapper.getRoleIdsById(id);
            user.setUpdateBy(UserUtils.getUserId());
            user.setUpdateDate(new Date());
            user.setDelFlag("1");
            //删除 角色关系
            String[] roleIds = new String[0];
            sysRoleService.updateRoleUserRelation(roleIds,user.getId());
            //删除 用户的组织
            sysOrganizationService.updateOrganizationUserRelation(null, user.getId());
            return userManageMapper.updateUser(user);
        } catch (DataAccessException e) {
            logger.error("Method[deleteUserById] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    /**
     * @return int
     * @Description 通过username取得用户数量（用作更新用户时校验用户名重复）
     * <AUTHOR> yuxi
     * @Date 16:12 2019/6/27
     * @Param [id, username]
     **/
    public int checkUsernameExist(String id, String username, String name) {
        return userManageMapper.checkUsernameExist(id, username, name);
    }

    /**
     * @return int
     * @Description 根据部门id取得部门下用户数量
     * <AUTHOR> yuxi
     * @Date 16:12 2019/6/27
     * @Param [OrganizationId]
     **/
    public int checkDepartExistUser(String organizationId) {
        return userManageMapper.checkOrganizationExistUser(organizationId);
    }

    /**
     * 修改密码
     *
     * @param id
     * @param passwordNew
     * @return
     */
    public int updatePassword(String id, String passwordNew) {
        if(passwordNew.isEmpty()){
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
        User user = userManageMapper.getUserById(String.valueOf(id));

        // 密码加密
        String encodePassword = CodecUtils.passwordBcryptEncode(passwordNew.trim());
        user.setPassword(encodePassword);
        user.setUpdateBy(UserUtils.getUserId());
        user.setUpdateDate(new Date());
        return userManageMapper.updateUser(user);

    }

    /**
     * 重置密码
     * @param id
     * @return
     */
    public String resetPassword(String id) {
        User user = userManageMapper.getUserById(String.valueOf(id));
        // 密码加密
        String password = "";
        if ("0".equals(id)){
            user.setPassword(CodecUtils.passwordBcryptEncode("Crrc@Admin1"));
        } else {
            password = CodecUtils.randomPassword();
            user.setPassword(CodecUtils.passwordBcryptEncode(password));
        }
        user.setUpdateBy(UserUtils.getUserId());
        user.setUpdateDate(new Date());
        userManageMapper.updateUser(user);
        return password;
    }

    public Object userUpdatePassword(String id, String passwordOld, String passwordNew) {
        if(passwordNew.isEmpty()||passwordOld.isEmpty()){
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION.getErrorCode(),"新密码不能为空");
        }
        User user = userManageMapper.getUserById(String.valueOf(id));
        Boolean checkStatu = CodecUtils.passwordConfirm(passwordOld,user.getPassword());
        if(!checkStatu){
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION.getErrorCode(), "原密码错误");
        }
        // 密码加密
        String encodePassword = CodecUtils.passwordBcryptEncode(passwordNew.trim());
        user.setPassword(encodePassword);
        user.setUpdateBy(UserUtils.getUserId());
        user.setUpdateDate(new Date());
        return userManageMapper.updateUser(user);
    }

    public Object uploadImg(MultipartFile file) {
        Date date = new Date();
        if (file.isEmpty()) {
            throw new RestApiException(ExceptionInfoEnum.DATA_DUPLICATE_EXCEPTION.getErrorCode(), "上传文件为空");
        }
        try {
            BufferedImage image = ImageIO.read(file.getInputStream());
            if(image!=null){// 如果image=null 表示上传的不是图片格式
                String OriginalFileName = file.getOriginalFilename();
                String[] split = OriginalFileName.split("\\.");
                String fileName = UserUtils.getName() + "_" + new SimpleDateFormat("yyyyMMddHHmmss").format(date) + "."
                        + split[split.length - 1];
                String path = "D:/IDEAworkplace/phm-base/src/main/resources/static/img/head/";
                File dest = new File(path + fileName);
                try {
                    file.transferTo(dest);
                } catch (IOException e) {
                    e.getMessage();
                    throw new RestApiException(ExceptionInfoEnum.DATA_DUPLICATE_EXCEPTION.getErrorCode(), "文件上传失败");
                }

            }else {
                throw new RestApiException(ExceptionInfoEnum.DATA_DUPLICATE_EXCEPTION.getErrorCode(), "上传文件不是图片格式");
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;

    }

    public Void getImg(String image, HttpServletResponse response) throws IOException{
        try {
            FileInputStream headFile=new FileInputStream(image);
            int i=headFile.available();
            byte data[]=new byte[i];
            headFile.read(data);
            headFile.close();
            response.setContentType("image/*");
            OutputStream toClient=response.getOutputStream();
            toClient.write(data);
            toClient.close();
        }catch (IOException e){
            PrintWriter toClient=response.getWriter();
            response.setContentType("text/html;charset=gb2312");
            toClient.write("无法打开图片");
            toClient.close();
        }
        return null;
    }

    public Object updateUserForPersonalCenter(User user) {
        user.setUpdateBy(UserUtils.getUserId());
        user.setUpdateDate(new Date());
        return userManageMapper.updateUserForPersonalCenter(user);
    }
}
