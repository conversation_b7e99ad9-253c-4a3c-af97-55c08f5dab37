package cc.crrc.manage.service;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UUIDUtils;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.SysElementMapping;
import cc.crrc.manage.mapper.SysRoleMapping;
import cc.crrc.manage.pojo.Element;
import cc.crrc.manage.pojo.SysElementRole;
import cc.crrc.manage.pojo.SysRoleUserVO;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysElementService {
    @Autowired
    private SysElementMapping sysElementMapping;
    @Autowired
    private SysRoleMapping sysRoleMapping;

    //新增资源
    public Object addSysElement(Element element) {

        //校验elementCode是否存在
        List<Element> list = sysElementMapping.selectElement(element.getElementCode());
        if (list.size() > 0) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "资源编码重复，请重新输入资源编码");
        } else if (element.getMenuId() == null || element.getMenuId().isEmpty()) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "MenuId未输入，请重新输入MenuId");
        }
        //雪花算法添加主键
        element.setId(String.valueOf(PrimaryKeyGenerator.generatorId()));
        element.setDelFlag("0");
        int insertStatus = sysElementMapping.addSysElement(element);
        if (insertStatus > 0) {
            return "success";
        } else {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "添加资源失败");
        }
    }


    //删除资源
    public Object deleteSysElementById(Element element) {
        element.setDelFlag("1");
        int delStatus = sysElementMapping.deleteSysElementById(element);
        if (delStatus > 0) {
            return "success";
        } else {
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION.getErrorCode(), "删除失败");
        }
    }

    //更新资源
    public Object updateSysElementById(Element element) {
        //校验elementCode是否存在
        List<Element> list = sysElementMapping.selectElement(element.getElementCode());
        if (list.size() > 1) {
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION.getErrorCode(), "资源编码重复，请重新输入资源编码");
        } else if (element.getMenuId() == null || element.getMenuId().isEmpty()) {
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION.getErrorCode(), "MenuId未输入，请重新输入MenuId");
        } else if (element.getId() == null || element.getId().isEmpty()) {
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION.getErrorCode(), "id未输入，请重新输入id");
        }
        int updateStatus = sysElementMapping.updateSysElementById(element);
        if (updateStatus > 0) {
            return "success";
        } else {
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION.getErrorCode(), "更新失败");
        }
    }

    //查询资源
    public Object selectSysElement(Element element) {
        List<Element> elementList = sysElementMapping.selectSysElement(element);
        return elementList;
    }

    //为角色添加资源
    public Object addElementByRoleId(SysElementRole sysElementRole) {
        //雪花算法添加主键
        sysElementRole.setId(String.valueOf(PrimaryKeyGenerator.generatorId()));
        sysElementRole.setCreateBy(String.valueOf(UserUtils.getUserId()));
 //重复校验    添加和编辑均不需重复校验 heshenglun 2020-08-29
        List<Element> list = sysElementMapping.selectElementRole(sysElementRole);
        if (list.size() > 0) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "当前角色已分配该资源，请勿重复分配");
        }
        
//不用必须要绑定权限  heshenglun 2020-08-29
        String roleId = sysElementRole.getRoleId();
        String elementId = sysElementRole.getElementId();
        if (roleId == null || (elementId.isEmpty() || elementId == "")) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "角色绑定资源失败roleId" +
                    "或者elementId缺失");
        }
        int addRoleStatus = sysElementMapping.addElementByRoleId(sysElementRole);
        if (addRoleStatus > 0) {
            return "success";
        } else {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "角色绑定资源失败");
        }

    }

    public Object removeElementByRoleId(String elementId, String roleId) {
        int removeRoleStatus = sysElementMapping.removeElementByRoleId(elementId, roleId);
        if (removeRoleStatus > 0) {
            return removeRoleStatus;
        } else {
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION.getErrorCode(), "角色移除资源失败");
        }
    }

    public Object getAllElementByRoleId(String roleId) {
        if (roleId == null) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "roleId未输入");
        }
        List<Element> elements = sysElementMapping.getAllElementByRoleId(roleId);
        return elements;
    }
    
   
    /**
     * 
     * @Title: selectSysElementByUser   
     * @Description: 创建&编辑角色时查询当前用户以及当前角色的资源
     * @param: @param 前端传入menuId, roleId(当前编辑角色的Id)
     * @param: @return  返回权限集合
     * @return: Object
     * @date:   2020年8月26日 上午10:00:46  
     * @author: Heshenglun   
     * @throws
     */
    public Object selectSysElementByUser(Element element) {
    	//获取当前用户的userId
        String userId=UserUtils.getUserId();
        //获取当前用户的roleId集合
    	List<SysRoleUserVO> userRole=sysRoleMapping.listRoleUserRelationsByUserId(userId);
    	String menuId=element.getMenuId();
        //获取当前用户在当前选择menu下的权限
        List<Element> elementList = sysElementMapping.selectSysElementByUser(menuId,userRole);
        //获取当前角色的权限   
        List<String>  roleElementList=sysElementMapping.selectSyeElementByRoleId(element);
        for(Element a:elementList) {
        	for(String b:roleElementList) {
        	   if(a.getId().equals(b)) {
        		   a.setSelectBox("1");
        	   }
        	}
          }
        
        return elementList;
    }
    
   
    /**
     * 
     * @Title: removeEleByRoleId   
     * @Description: 删除当前编辑角色的elements   
     * @param: @param roleId
     * @param: @return      
     * @return: Object
     * @date:   2020年8月26日 上午10:59:58  
     * @author: Heshenglun   
     * @throws
     */
    public Object removeEleByRoleId(String roleId) {
        int removeRoleStatus = sysElementMapping.removeEleByRoleId(roleId);
        //根据修改时根据roleId删除不需做校验 heshenglun 2020-08-29
//        if (removeRoleStatus > 0) {
//            return removeRoleStatus;
//        } else {
//            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION.getErrorCode(), "角色移除资源失败");
//        }
        return removeRoleStatus;
    }
    
    
    
    
    
    
    
}
