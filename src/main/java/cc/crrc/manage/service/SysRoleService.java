package cc.crrc.manage.service;


import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.SysRoleMapping;
import cc.crrc.manage.pojo.Menu;
import cc.crrc.manage.pojo.SysElementRole;
import cc.crrc.manage.pojo.SysRoleMenu;
import cc.crrc.manage.pojo.SysRoleUserVO;
import cc.crrc.manage.pojo.SysRoleVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import cc.crrc.manage.service.SysElementService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class SysRoleService {
    private final Logger logger = LoggerFactory.getLogger(SysOrganizationService.class);
    @Autowired
    private SysRoleMapping mapping;
    @Autowired
    private SysElementService elementService;
 

    public List<SysRoleVO> getSysRoleByRoleType(SysRoleVO sysRoleVO) {
        List<SysRoleVO> list = mapping.getSysRoleByRoleType(sysRoleVO);
        return list;
    }

    public List<SysRoleVO> getSysRoleByDataPermission(SysRoleVO sysRoleVO) {
        List<SysRoleVO> list = mapping.getSysRoleByDataPermission(sysRoleVO);
        return list;
    }

    public Object getSysRole(SysRoleVO sysRoleVO) {
        //分页
        int currentPage = sysRoleVO.getPageNumber();
        int pageSize = sysRoleVO.getPageSize();
        PageHelper.startPage(currentPage, pageSize);
        List<SysRoleVO> list = mapping.getSysRole(sysRoleVO);
        if (currentPage == 0 && pageSize == 0) {
            return list;
        } else {
            PageHelper.startPage(currentPage, pageSize);
            PageInfo<SysRoleVO> pageInfo = new PageInfo<SysRoleVO>(list);
            return pageInfo;
        }
    }

    @Transactional
    public String saveSysRoleInfo(SysRoleVO sysRoleVO) {
//        sysRoleVO.setId(UUIDUtils.generateUuid());//String 型改成自增序列
        //雪花算法添加主键
        sysRoleVO.setId(PrimaryKeyGenerator.generatorId());
        sysRoleVO.setRolePath("/" + sysRoleVO.getRoleCode());
        sysRoleVO.setCreateBy(UserUtils.getUserId());
        sysRoleVO.setDelFlag("0");
        //重复校验
        SysRoleVO rCode = new SysRoleVO();
        SysRoleVO rName = new SysRoleVO();
        rCode.setRoleCode(sysRoleVO.getRoleCode());
        rName.setRoleName(sysRoleVO.getRoleName());
        List<SysRoleVO> roleCode = mapping.getSysRole(rCode);
        List<SysRoleVO> roleName = mapping.getSysRole(rName);
        if (roleCode.size() > 0) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "编码重复，请重新输入编码");
        }
        if (roleName.size() > 0) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "名称重复，请重新输入名称");
        }
        //校验当前菜单是否是父级菜单，父级菜单不能保存 2020-6-22 zhangzhijian
        String homeMenuId = sysRoleVO.getHomeMenuId();
        //不能为空
        if(StringUtils.isEmpty(homeMenuId)){
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "默认首页菜单不能为空，请选择子菜单");
        }
        int parentId = mapping.findCountByHomeMenuId(homeMenuId);
        //所选菜单不能有子菜单
        if(parentId > 0){
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "当前所选菜单有子菜单，请选择子菜单");
        }
        try {
            int saveStatus = mapping.saveSysRoleSonInfo(sysRoleVO);
            if (saveStatus > 0) {
                insertRoleMenu(sysRoleVO);
                 //遍历出menuForm中的element进行存入 heshenglun 2020-8-25
           for(Menu a:sysRoleVO.getMenuForm()) {
               for(String b:a.getElementIds()) {
                 SysElementRole s=new SysElementRole();
                 s.setRoleId(sysRoleVO.getId());
                 s.setElementId(b);
                 elementService.addElementByRoleId(s);
                	}
                }
       
            }
            
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
        return "保存成功";
    }
    
    @Transactional
    public String updateSysRoleInfo(SysRoleVO sysRoleVO) {
        //重复校验
        List<SysRoleVO> sysRole = mapping.selectExistSysRole(sysRoleVO);
        if (sysRole.size() > 0) {
            for (int i = 0; i < sysRole.size(); i++) {
                if (sysRole.get(i).getId().equals(sysRoleVO.getId())) {
                    continue;
                } else {
                    throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "编码或名称重复，请重新输入");
                }
            }

        }
        //数据权限 空值判断
        if(sysRoleVO.getDataPermission().isEmpty()){
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "数据权限不能为空");
        }
        //校验当前菜单是否是父级菜单，父级菜单不能保存 2020-6-22 zhangzhijian
        String homeMenuId = sysRoleVO.getHomeMenuId();
        //不能为空
        if(StringUtils.isEmpty(homeMenuId)){
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "默认首页菜单不能为空，请选择子菜单");
        }
        int parentId = mapping.findCountByHomeMenuId(homeMenuId);
        //所选菜单不能有子菜单
        if(parentId > 0){
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "当前所选菜单有子菜单，请选择子菜单");
        }
        sysRoleVO.setUpdateBy(UserUtils.getUserId());
        sysRoleVO.setRolePath("/" + sysRoleVO.getRoleCode());
        try {
            int updateStatus = mapping.updateSysRoleSonInfo(sysRoleVO);
            if (updateStatus == 0) {
                return "更新条数：" + updateStatus;
            }
            //先删除角色原有的菜单关联
            mapping.deleteRoleMenuByRoleId(sysRoleVO.getId());
            //先删除角色原有的elements heshenglun 2020-8-26
            elementService.removeEleByRoleId(sysRoleVO.getId());
            //再进行新的菜单关联
            insertRoleMenu(sysRoleVO);
            
            //遍历出menuForm中的element进行存入 heshenglun 2020-8-26
            for(Menu a:sysRoleVO.getMenuForm()) {
                for(String b:a.getElementIds()) {
                  SysElementRole s=new SysElementRole();
                  s.setRoleId(sysRoleVO.getId());
                  s.setElementId(b);
                  elementService.addElementByRoleId(s);
                 	}
                 }
            return "更新成功";
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION);
        }
    }

    public String deleteSysRoleInfo(String id) {
        if (StringUtils.isEmpty(id)) {
            return "传值为空！";
        }
        //判断是否有子角色
        String parentId = id;
        List<SysRoleVO> sysRoleInfo = mapping.getSysRoleInfoByParentId(parentId);

        if (sysRoleInfo.size() > 0) {
            return "此角色下含有子角色，请清除后再删除";
        }
        try {
            int deleteStatus = mapping.deleteSysRoleInfo(id);
            if (deleteStatus == 0) {
                return "更新条数：" + deleteStatus;
            }
            //删除角色对应的菜单关联
            mapping.deleteRoleMenuByRoleId(id);
            return "删除成功";
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION);
        }
    }

    /**
     * 通过id查询角色信息     *
     *
     * @param id
     */
    public SysRoleVO getSysRoleInfoById(String id) {
        try {
            SysRoleVO sysRoleInfo = mapping.getSysRoleInfoById(id);
            return sysRoleInfo;
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 新增角色菜单信息
     *
     * @param sysRoleVO 角色对象
     */
    public void insertRoleMenu(SysRoleVO sysRoleVO) {
        int rows = 0;
        // 新增用户与角色管理
        List<SysRoleMenu> list = new ArrayList<SysRoleMenu>();
        if (sysRoleVO.getMenuIds() == null) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "权限传值为null，请重新输入");
        }
        for (String menuId : sysRoleVO.getMenuIds()) {
            SysRoleMenu rm = new SysRoleMenu();
            rm.setRoleId(sysRoleVO.getId());
            rm.setMenuId(menuId);
            list.add(rm);
        }
        if (list.size() > 0) {
            rows = mapping.batchRoleMenu(list);
        }

        System.out.println("更新角色对应的菜单条数：" + rows);
    }

    /**
     * 查询角色对应菜单权限
     * id
     *
     * @param
     */
    public Object getSysRoleMenuById(String id) {
        String[] selectedMenuId = null;
        Map map = new HashMap<String, Object>();
        try {
            SysRoleVO sysRoleInfo = mapping.getSysRoleInfoById(id);
            String selectedMenuIds = sysRoleInfo.getSelectedMenuIds();
            if (selectedMenuIds != null && !selectedMenuIds.isEmpty()) {

                selectedMenuId = selectedMenuIds.split(",");
                System.out.println(selectedMenuId);
                map.put("selectedMenuId", selectedMenuId);
            }
            List<SysRoleMenu> list = mapping.getSysRoleMenuById(id);
            String[] menuIds = new String[list.size()];
            if (list.size() > 0) {
                for (int i = 0; i < list.size(); i++) {
                    menuIds[i] = list.get(i).getMenuId();
                }
            }
            map.put("menuIds", menuIds);

        } catch (Exception e) {
            new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
        return map;

    }

    /**
     * @return void
     * @Description 新增角色用户关系数据
     * <AUTHOR>
     * @Date  2019/11/06
     * @Param [roleIds, userId]
     **/
    public void addRoleUserRelation(String[] roleIds, String userId) {
        try {
            if (roleIds.length>0) {

                SysRoleUserVO sysRoleUserVO;
                for (String roleId : roleIds) {
                    sysRoleUserVO = new SysRoleUserVO();
                    sysRoleUserVO.setRoleId(roleId);
                    sysRoleUserVO.setUserId(userId);
                    mapping.addRoleUserRelation(sysRoleUserVO);
                }
            }
        } catch (DataAccessException e) {
            logger.error("Method[addRoleUser] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(),"用户角色关联失败！");
        }
    }

    /**
     * @return void
     * @Description 更新用户角色关系
     * <AUTHOR>
     * @Date 2019/11/06
     * @Param [roleIds, userId]
     **/
    public void updateRoleUserRelation(String[] roleIds, String userId) {
        try {
            // 查询原有的角色用户关系数据并删除
            List<SysRoleUserVO> roleUserRelationsOrigin = mapping.listRoleUserRelationsByUserId(userId);
            for (SysRoleUserVO relation : roleUserRelationsOrigin) {
                mapping.deleteRoleUserRelation(relation);
            }
            // 删除原有的关系数据后新增
            addRoleUserRelation(roleIds, userId);
        } catch (DataAccessException e) {
            logger.error("Method[updateRoleUser] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION.getErrorCode(),"更新用户角色关联失败！");
        }
    }
    
    
    /**
     * 
     * @Title: getUsetDataPermission   
     * @Description: 获取当前用户的datapermission
     * @param: @return      
     * @return: 返回data值
     * @date:   2020年8月18日 上午9:43:30  
     * @author: Heshenglun   
     * @throws
     */
    public Object getUserMaxDataPermission() {
        String userId = UserUtils.getUserId();
    	int datapermission=mapping.getMaxDataPermissionByUsername(userId);
    	return datapermission;
    }
    
    
    
    
    

}
