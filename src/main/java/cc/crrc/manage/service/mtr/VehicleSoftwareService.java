package cc.crrc.manage.service.mtr;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.mtr.VihicleSoftwareMapping;
import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.mtr.*;
import cc.crrc.manage.service.SysFileService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;



/**
 * <AUTHOR>
 * 车载软件管理服务层
 * 2019/11/18
 **/

@Service
public class VehicleSoftwareService {
    private final Logger logger = LoggerFactory.getLogger(VehicleSoftwareService.class);

    @Autowired
    private VihicleSoftwareMapping vihicleSoftwareMapping;

    @Autowired
    private SysFileService sysFileService;

    /**
     * 车载软件版本列表、条件查询
     * @param vehicleSoftwareDTO
     * <AUTHOR>
     * @return
     */
    public PageInfo<VehicleSoftwarePO> VehicleSoftwareList(VehicleSoftwareDTO vehicleSoftwareDTO) {
        try {
            int currentPage = vehicleSoftwareDTO.getPageNumber();
            int pageSize = vehicleSoftwareDTO.getPageSize();
            PageHelper.startPage(currentPage, pageSize);
            //查询列表数据
            List<VehicleSoftwarePO> vehicleSoftwareList = vihicleSoftwareMapping.VehicleSoftwareList(vehicleSoftwareDTO);
            return new PageInfo<>(vehicleSoftwareList);
        } catch (DataAccessException e) {
            logger.error("Method[VehicleSoftwareList] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 新增车载软件版本
     * @param vehicleSoftwarePO
     * <AUTHOR>
     * @return
     */
    @Transactional
    public Object addVehicleSoftware(VehicleSoftwarePO vehicleSoftwarePO) {
        try {
            //新增到主表mtr_software
            vehicleSoftwarePO.setCreateById(UserUtils.getUserId());
            vehicleSoftwarePO.setModifyById(UserUtils.getUserId());
//            vehicleSoftwarePO.setCreateById(1L);
//            vehicleSoftwarePO.setModifyById(1L);
            vehicleSoftwarePO.setId(PrimaryKeyGenerator.generatorId());
            vihicleSoftwareMapping.addVehicleSoftware(vehicleSoftwarePO);

            //新增关联表
            int componentTypeSoftwareResult = vihicleSoftwareMapping.instcomponentTypeSoftware(vehicleSoftwarePO.getVehicleTypeId(), vehicleSoftwarePO.getComponentTypeId(), vehicleSoftwarePO.getId());
            return componentTypeSoftwareResult;
        }
        catch (DataAccessException e){
            logger.error("Method[addVehicleSoftware] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * 更新车载软件版本
     * @param vehicleSoftwarePO
     * <AUTHOR>
     * @return
     */
    @Transactional
    public Object updateVehicleSoftware(VehicleSoftwarePO vehicleSoftwarePO) {
        try {
            //更新数据主表关系
        	vehicleSoftwarePO.setModifyById(UserUtils.getUserId());
            return vihicleSoftwareMapping.updateVehicleSoftware(vehicleSoftwarePO);
           //插入新关系表数据
//            return  vihicleSoftwareMapping.instcomponentTypeSoftware(vehicleSoftwarePO.getVehicleTypeId(),vehicleSoftwarePO.getComponentTypeId(),vehicleSoftwarePO.getId());
        }
        catch (DataAccessException e){
            logger.error("Method[updateVehicleSoftware] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }
    /**
     * 升级车载软件版本
     * @param vehicleSoftwarePO
     * <AUTHOR>
     * @return
     */
    @Transactional
    public Object upgradingVehicleSoftware(VehicleSoftwarePO vehicleSoftwarePO) {
        try {
            //新增到主表mtr_software
            vehicleSoftwarePO.setCreateById(UserUtils.getUserId());
            vehicleSoftwarePO.setModifyById(UserUtils.getUserId());
//            vehicleSoftwarePO.setCreateById(1L);
//            vehicleSoftwarePO.setModifyById(1L);
            vehicleSoftwarePO.setId(PrimaryKeyGenerator.generatorId());
            vihicleSoftwareMapping.addVehicleSoftware(vehicleSoftwarePO);
            //新增关联表
            int componentTypeSoftwareResult = vihicleSoftwareMapping.instcomponentTypeSoftware(vehicleSoftwarePO.getVehicleTypeId(), vehicleSoftwarePO.getComponentTypeId(), vehicleSoftwarePO.getId());
            return componentTypeSoftwareResult;
        }
        catch (DataAccessException e){
            logger.error("Method[updateVehicleSoftware] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * 匹配车载软件加载列表
     * @param componentTypeId
     * <AUTHOR>
     * @return
     */
    public Object getComponentListByTypeID(String componentTypeId) {
        try {
            return vihicleSoftwareMapping.getComponentListByTypeID(componentTypeId);
        } catch (DataAccessException e) {
            logger.error("Method[getComponentListByTypeID] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 获取部件已匹配软件列表
     * @param componentId,softwareId
     * <AUTHOR>
     * @return
     */
    public Object getComponentSoftwareListByComponentID(String componentId,String softwareId) {
        try {
            return vihicleSoftwareMapping.getComponentSoftwareListByComponentID(componentId,softwareId);
        } catch (DataAccessException e) {
            logger.error("Method[getComponentSoftListByComponentID] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 匹配车载软件版本
     * @param configSoftwareComponentPOList
     * <AUTHOR>
     * @return
     */
    public Object configVehicleSoftware(List<ConfigSoftwareComponentPO>  configSoftwareComponentPOList,String componentTypeId ) {
        try{
            //删除部件型号关联的部件版本关联信息，此方法等测试数据完成后修改和确认
            vihicleSoftwareMapping.delComponentSoftware(componentTypeId);
            if(configSoftwareComponentPOList.size()>0){
                //插入新匹配的数据
                ConfigSoftwareComponentPO configSoftwareComponentPO = new ConfigSoftwareComponentPO();
                for(int i=0;configSoftwareComponentPOList.size()>i;i++){
                    configSoftwareComponentPO=configSoftwareComponentPOList.get(i);
                    vihicleSoftwareMapping.configVehicleSoftware(configSoftwareComponentPO.getVehicleId(),configSoftwareComponentPO.getComponentId(),configSoftwareComponentPO.getSoftwareId(),PrimaryKeyGenerator.generatorId());
                }
                return configSoftwareComponentPOList.size();
            }else {
                return null;
            }

        }catch (DataAccessException e){
            logger.error("Method[listEkbFaultType] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }

    }


    /**
     * @return
     * @Description 存储车载软件的文件关联
     * <AUTHOR>
     **/
    @Transactional
    public Object saveVehicleSoftwareFile(SysFilePO sysFile, String id) {
        try{
            //文件属性存入 sysFile表
            sysFileService.addSysFile(sysFile);
            //保存文件与故障类型的关系
            String fileId = sysFile.getId();
            return vihicleSoftwareMapping.saveVehicleSoftwareFile(fileId, id);
        }catch (DataAccessException e){
            logger.error("Method[listEkbFaultType] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * @return
     * @Description 查询车载软件的文件关联by softwareId
     * <AUTHOR>
     **/

    public Object getVehicleSoftwareFile(String id) {
        try {
            return vihicleSoftwareMapping.getVehicleSoftwareFile(id);
        } catch (DataAccessException e) {
            logger.error("Method[getVehicleSoftwareFile] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @return
     * @Description 删除车载软件的文件关联by fileId
     * <AUTHOR>
     **/
    @Transactional
    public Object deleteVehicleSoftwareFile(String fileId){
        try {
            //关联关系 删除
            int i = vihicleSoftwareMapping.deleteVehicleSoftwareFile(fileId);
            if(i>0){
                return sysFileService.deleteSysFile(fileId);
            }else{
                throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
            }
            //sysFile文件主表关联删除
        } catch (DataAccessException e) {
            logger.error("Method[getVehicleSoftwareFile] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    /**
     * @return
     * @Description 删除车载软件版本
     * <AUTHOR>
     **/
    @Transactional
    public Object deleteVehicleSoftware(String softwareId,String componentTypeId,String vehicleTypeId) {
            //针对匹配的车辆软件版本的业务逻辑并无详细设计，如存在关联数据的情况下如何判定是否可以删除  || 业务逻辑本身是否允许删除已匹配的车辆部件软件版本等。
            //以下业务逻辑只在并无关联情况下进行：
            try {
                //1、查询关联表中是否存在关联数据（mtr_component_software）
                List<Map<String, String>> componentSoftwareList = vihicleSoftwareMapping.getComponentSoftware(softwareId);
                if ( componentSoftwareList.size()>0){
                   throw new RestApiException(ExceptionInfoEnum.DATA_SELECTANDCHECK_EXCEPTION);
               }
//                List<Map<String, String>> ComponentTypeSoftwareList =  vihicleSoftwareMapping.getComponentTypeSoftware(componentTypeId,softwareId,vehicleTypeId);
//                if ( ComponentTypeSoftwareList.size()>0){
//                   throw new RestApiException(ExceptionInfoEnum.DATA_SELECTANDCHECK_EXCEPTION);
//               }
                //2、删除主表数据并返回结果
                return vihicleSoftwareMapping.delSoftware(softwareId);
            } catch (DataAccessException e) {
                logger.error("Method[addDict] Error:{}", e.getMessage());
                throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
            }
    }
    
    /**
     * 车载软件版本列表、条件查询
     * @param vehicleSoftwareDTO
     * <AUTHOR>
     * @return
     */
    public PageInfo<VehicleSoftwareFileDTO> softwareList(VehicleSoftwareDTO vehicleSoftwareDTO) {
        try {
            int currentPage = vehicleSoftwareDTO.getPageNumber();
            int pageSize = vehicleSoftwareDTO.getPageSize();
            PageHelper.startPage(currentPage, pageSize);
            //查询列表数据
            List<VehicleSoftwareFileDTO> list = new ArrayList<VehicleSoftwareFileDTO>();
            List<VehicleSoftwarePO> vehicleSoftwareList = vihicleSoftwareMapping.VehicleSoftwareList(vehicleSoftwareDTO);
            for(VehicleSoftwarePO softwarePO : vehicleSoftwareList) {
            	VehicleSoftwareFileDTO softwareFileDTO = new VehicleSoftwareFileDTO();
            	BeanUtils.copyProperties(softwarePO, softwareFileDTO);
            	softwareFileDTO.setFiles( vihicleSoftwareMapping.getVehicleSoftwareFile(softwarePO.getId()));
            	list.add(softwareFileDTO);
            }
            return new PageInfo<>(list);
        } catch (DataAccessException e) {
            logger.error("Method[VehicleSoftwareList] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }
    
    /**
     * 新增车载软件版本
     * @param vehicleSoftwarePO
     * <AUTHOR>
     * @return
     */
    @Transactional
    public Object addSoftware(VehicleSoftwareFileDTO vehicleSoftwarePO) {
        try {
            //新增到主表mtr_software
            vehicleSoftwarePO.setCreateById(UserUtils.getUserId());
            vehicleSoftwarePO.setModifyById(UserUtils.getUserId());
//            vehicleSoftwarePO.setCreateById(1L);
//            vehicleSoftwarePO.setModifyById(1L);
            vehicleSoftwarePO.setId(PrimaryKeyGenerator.generatorId());
            vihicleSoftwareMapping.addVehicleSoftware(vehicleSoftwarePO);
            for(SysFilePO sysFile : vehicleSoftwarePO.getFiles()) {
            	//文件属性存入 sysFile表
            	sysFileService.addSysFile(sysFile);
            	//保存文件与故障类型的关系
                String fileId = sysFile.getId();
            	vihicleSoftwareMapping.saveVehicleSoftwareFile(fileId, vehicleSoftwarePO.getId());
            }
            //新增关联表
            int componentTypeSoftwareResult = vihicleSoftwareMapping.instcomponentTypeSoftware(vehicleSoftwarePO.getVehicleTypeId(), vehicleSoftwarePO.getComponentTypeId(), vehicleSoftwarePO.getId());
            return componentTypeSoftwareResult;
        }
        catch (DataAccessException e){
            logger.error("Method[addVehicleSoftware] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }
/****
 * <AUTHOR>
 * @param componentId
 * @return
 */
	public Object getSoftware(String componentId) {
		// TODO Auto-generated method stub
		List<VehicleSoftwarePO> vihicleSoftware =  vihicleSoftwareMapping.getSoftware(componentId);
		return vihicleSoftware;
	}
	@Transactional(rollbackFor = {RestApiException.class})
	public Object insertSoftware(VehicleSoftwareFileDTO vehicleSoftwarePO, String vehicleID, String componentId) {
		// TODO Auto-generated method stub
		 vehicleSoftwarePO.setCreateById(UserUtils.getUserId());
         vehicleSoftwarePO.setModifyById(UserUtils.getUserId());
         vehicleSoftwarePO.setId(PrimaryKeyGenerator.generatorId());
         try {
        	 vihicleSoftwareMapping.addVehicleSoftware(vehicleSoftwarePO);
        	 vihicleSoftwareMapping.instcomponentTypeSoftware(vehicleSoftwarePO.getVehicleTypeId(), vehicleSoftwarePO.getComponentTypeId(), vehicleSoftwarePO.getId());
        	 vihicleSoftwareMapping.configVehicleSoftware(vehicleID,componentId,vehicleSoftwarePO.getId(),PrimaryKeyGenerator.generatorId());
		} catch (Exception e) {
			throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
		}
         
		return "success";
	}
	/*****
	 * <AUTHOR>
	 * @param vehicleSoftwarePO
	 * @param vehicleId
	 * @param componentId
	 * @return
	 */
//	@Transactional(rollbackFor = {RestApiException.class})
//	public Object upgradingVehSoftware(VehicleSoftwarePO vehicleSoftwarePO, String vehicleId, String componentId) {
//		// TODO Auto-generated method stub
//		try {
//			vehicleSoftwarePO.setModifyById(UserUtils.getUserId());
//			vihicleSoftwareMapping.changeValid(vehicleSoftwarePO.getId(),componentId);
//			vihicleSoftwareMapping.addVehicleSoftware(vehicleSoftwarePO);
//			vihicleSoftwareMapping.instcomponentTypeSoftware(vehicleSoftwarePO.getVehicleTypeId(), vehicleSoftwarePO.getComponentTypeId(), vehicleSoftwarePO.getId());
//			vihicleSoftwareMapping.configVehicleSoftware(vehicleId,componentId,vehicleSoftwarePO.getId());
//		} catch (Exception e) {
//			// TODO: handle exception
//			throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(),"版本升级异常");
//		}
//		return "success";
//	}
/*****
 * 
 * <AUTHOR>
 * @param softwareId
 * @param componentId
 * @return
 */
	public Object deleteVehSoftware(String softwareId, String componentId) {
		// TODO Auto-generated method stub
		try {
			//vihicleSoftwareMapping.changeValid(softwareId, componentId);
		} catch (Exception e) {
			throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
		}
		return "SUCCESS";
	}
/*****
 * 
 * <AUTHOR>
 * @param componentTypeId
 * @param vehicleTypeId
 * @return
 */
	public Object getVehicleSoftwareList(String componentTypeId, String vehicleTypeId, String softwareName) {
		// TODO Auto-generated method stub
		List<VehicleSoftwarePO> vihicleSoftware =  vihicleSoftwareMapping.getVehicleSoftwareList(componentTypeId, vehicleTypeId, softwareName);
		return vihicleSoftware;
	}

	public Object configSoftware(String softwareId, String componentId, String vehicleId,String softwareName) {
		// TODO Auto-generated method stub
		try {
			vihicleSoftwareMapping.changeValid(componentId,softwareName);
			vihicleSoftwareMapping.configVehicleSoftware(vehicleId,componentId,softwareId,PrimaryKeyGenerator.generatorId());
		} catch (DataAccessException e) {
			throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(),"绑定失败");
		}
		return "SUCCESS";
	}

}
