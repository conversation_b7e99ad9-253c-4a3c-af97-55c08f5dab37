package cc.crrc.manage.service.mtr;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.StringUtils;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.component.ComponentTypeMapping;
import cc.crrc.manage.mapper.mtr.ManufacturerEmployeeMapping;
import cc.crrc.manage.mapper.mtr.MtrVehicleTypeMapping;
import cc.crrc.manage.pojo.mtr.ManufacturerEmployeePO;
import cc.crrc.manage.pojo.mtr.ManufacturerEmployeeVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.Collator;
import java.util.*;


/**
 * 制造商雇员管理服务层
 *
 * <AUTHOR>
 * 2019/11/11
 **/
@Service
public class ManufacturerEmployeeService {
    private final Logger logger = LoggerFactory.getLogger(ManufacturerEmployeeService.class);

    @Autowired
    private ManufacturerEmployeeMapping manufacturerEmployeeMapping;
    @Autowired
    private ComponentTypeMapping componentTypeMapping;
    @Autowired
    private MtrVehicleTypeMapping vehicleTypeMapping;

    private static final String GROUP_BY_EMPLOYEE_NAME = "1";

    /**
     * 添加制造商雇员
     *
     * @param manufacturerEmployeePO 雇员信息
     * <AUTHOR> GuoYang
     * 2019/11/25
     **/
    @Transactional(rollbackFor = Exception.class)
    public void addManufacturerEmployee(ManufacturerEmployeePO manufacturerEmployeePO) {
        try {
            // 添加主键
            manufacturerEmployeePO.setId(PrimaryKeyGenerator.generatorId());
            // 添加创建人信息
            String currentId = UserUtils.getUserId();
            manufacturerEmployeePO.setCreateBy(currentId);
            manufacturerEmployeePO.setModifyBy(currentId);
            // 保存雇员信息至数据库
            int successCount = manufacturerEmployeeMapping.addManufacturerEmployee(manufacturerEmployeePO);
            if (successCount != 1) {
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
            }
        } catch (DataAccessException e) {
            logger.error("Method[addManufacturerEmployee] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * 根据雇员id查询雇员信息
     *
     * @param id 雇员id
     * <AUTHOR> GuoYang
     * 2019/11/25
     **/
    public ManufacturerEmployeeVO getManufacturerEmployeeById(String id) {
        try {
            return manufacturerEmployeeMapping.getManufacturerEmployeeById(id);
        } catch (DataAccessException e) {
            logger.error("Method[getManufacturerEmployeeById] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 根据制造商id查询下属雇员信息
     *
     * @param manufacturerId 制造商id
     * <AUTHOR> GuoYang
     * 2019/11/25
     **/
    public List<ManufacturerEmployeeVO> listManufacturerEmployeeByManufacturerId(String manufacturerId) {
        try {
            return manufacturerEmployeeMapping.listManufacturerEmployeeByManufacturerId(manufacturerId);
        } catch (DataAccessException e) {
            logger.error("Method[listManufacturerEmployeeByManufacturerId] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 返回经过分组后的雇员信息。
     * 返回列表又多个分组组成，每个分组的数据结构详见本类下的<code>createGroupWithEmployee</code>方法注释。
     *
     * @param groupType 1：按雇员姓名首字母分组;其余：按雇员所属公司名分组
     * @param searchKey 模糊搜索关键字，相关搜索项为公司名、雇员名、雇员电话
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * <AUTHOR> GuoYang
     * 2019/11/15
     **/
    public List<Map<String, Object>> listManufacturerEmployee(String groupType, String searchKey) {
        try {
            // 查询雇员信息
            List<ManufacturerEmployeeVO> manufacturerEmployeeVOList = manufacturerEmployeeMapping.listManufacturerEmployee(searchKey);
            // 对查询到的雇员进行分组
            List<Map<String, Object>> res;
            if (GROUP_BY_EMPLOYEE_NAME.equals(groupType)) {
                res = groupByFirstLetterOfName(manufacturerEmployeeVOList);
            } else {
                res = groupByManufacturerName(manufacturerEmployeeVOList);
            }
            return res;
        } catch (Exception e) {
            logger.error("Method[listManufacturerEmployee] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 根据雇员id删除雇员
     *
     * @param id 雇员id
     * <AUTHOR> GuoYang
     * 2019/11/25
     **/
    @Transactional(rollbackFor = Exception.class)
    public void deleteManufacturerEmployeeById(String id) {
        try {
            // 删除雇员
            Integer successCount = manufacturerEmployeeMapping.deleteManufacturerEmployeeById(id);
            if (successCount != 1) {
                throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
            }
            // 解除雇员与部件类型的关系
            componentTypeMapping.deleteComponentTypeContactsByEmployeeId(id);
            // 解除雇员与车辆类型的关系
            vehicleTypeMapping.deleteVehicleTypeContactsByEmployeeId(id);
        } catch (DataAccessException e) {
            logger.error("Method[deleteManufacturerEmployeeById] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    /**
     * 根据制造商id删除下属雇员
     *
     * @param manufacturerId 制造商id
     * <AUTHOR> GuoYang
     * 2019/11/25
     **/
    @Transactional(rollbackFor = Exception.class)
    public void deleteManufacturerEmployeeByManufacturerId(String manufacturerId) {
        try {
            manufacturerEmployeeMapping.deleteManufacturerEmployeeByManufacturerId(manufacturerId);
        } catch (DataAccessException e) {
            logger.error("Method[deleteManufacturerEmployeeByManufacturerId] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    /**
     * 更新雇员信息
     *
     * @param manufacturerEmployeePO 雇员信息
     * <AUTHOR> GuoYang
     * 2019/11/25
     **/
    @Transactional(rollbackFor = Exception.class)
    public void updateManufacturerEmployee(ManufacturerEmployeePO manufacturerEmployeePO) {
        try {
            manufacturerEmployeePO.setModifyBy(UserUtils.getUserId());
            ManufacturerEmployeeVO employee = getManufacturerEmployeeById(manufacturerEmployeePO.getId());
            if (!manufacturerEmployeePO.getManufacturerId().equals(employee.getManufacturerId())){
                // 解除雇员与部件类型的关系
                componentTypeMapping.deleteComponentTypeContactsByEmployeeId(employee.getId());
                // 解除雇员与车辆类型的关系
                vehicleTypeMapping.deleteVehicleTypeContactsByEmployeeId(employee.getId());
            }
            Integer successCount = manufacturerEmployeeMapping.updateManufacturerEmployee(manufacturerEmployeePO);
            if (successCount != 1) {
                throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
            }
        } catch (DataAccessException e) {
            logger.error("Method[updateManufacturerEmployee] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    /**
     * 根据雇员姓名首字母进行分组。且组内雇员按姓名排序。
     * 首字母无法映射到26个英文字母的姓名，归类到 "#" 分组中。
     *
     * @param manufacturerEmployeeVOList 待分组的雇员信息列表
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * <AUTHOR> GuoYang
     * 2019/11/15
     **/
    private List<Map<String, Object>> groupByFirstLetterOfName(List<ManufacturerEmployeeVO> manufacturerEmployeeVOList) {
        List<Map<String, Object>> res = new ArrayList<>();
        // 通过提前对雇员姓名进行排序，保证分组后的组内雇员姓名有序。
        manufacturerEmployeeVOList.sort((ManufacturerEmployeeVO o1, ManufacturerEmployeeVO o2) -> Collator.getInstance(Locale.CHINESE).compare(o1.getName(), o2.getName()));
        for (ManufacturerEmployeeVO manufacturerEmployeeVO : manufacturerEmployeeVOList) {
            // 获取汉字对应的英文全拼
            char firstLetterChar = StringUtils.getFullSpell(manufacturerEmployeeVO.getName()).substring(0, 1).toCharArray()[0];
            String firstLetterString = (firstLetterChar < 91 && firstLetterChar > 64) ? String.valueOf(firstLetterChar) : "#";
            insertEmployee2Group(res, firstLetterString, manufacturerEmployeeVO);
        }
        return res;
    }

    /**
     * 根据雇员所属公司进行分组。且组内雇员按姓名排序。
     *
     * @param manufacturerEmployeeVOList 待分组的雇员信息列表
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * <AUTHOR> GuoYang
     * 2019/11/15
     **/
    private List<Map<String, Object>> groupByManufacturerName(List<ManufacturerEmployeeVO> manufacturerEmployeeVOList) {
        List<Map<String, Object>> res = new ArrayList<>();
        // 通过提前对雇员姓名进行排序，保证分组后的组内雇员姓名有序。
        manufacturerEmployeeVOList.sort((ManufacturerEmployeeVO o1, ManufacturerEmployeeVO o2) -> Collator.getInstance(Locale.CHINESE).compare(o1.getName(), o2.getName()));
        for (ManufacturerEmployeeVO manufacturerEmployeeVO : manufacturerEmployeeVOList) {
            String manufacturerName = manufacturerEmployeeVO.getManufacturerName();
            insertEmployee2Group(res, manufacturerName, manufacturerEmployeeVO);
        }
        // 根据所属制造商名称进行排序
        res.sort((Map<String, Object> o1, Map<String, Object> o2) -> Collator.getInstance(Locale.CHINESE).compare(o1.get("name"), o2.get("name")));
        return res;
    }

    /**
     * 将查询出来的雇员信息，插入到目标队列相应的分组之中。
     * 若目标队列未含有期望分组，则先创建，再插入。
     *
     * @param targetList 待插入的目标队列
     * @param groupName  分组名称（位于分组Map的name对应值中。）
     * @param employee   待插入的雇员对象
     * <AUTHOR> GuoYang
     * 2019/11/15
     **/
    private void insertEmployee2Group(List<Map<String, Object>> targetList, String groupName, ManufacturerEmployeeVO employee) {
        if (groupName == null) {
            return;
        }
        // 遍历目标队列，找到相应分组，并插入雇员。
        for (Map<String, Object> singleGroup : targetList) {
            if (groupName.equals(singleGroup.get("name"))) {
                List<ManufacturerEmployeeVO> temp = (List<ManufacturerEmployeeVO>) singleGroup.get("employees");
                temp.add(employee);
                singleGroup.put("employees", temp);
                return;
            }
        }
        createGroupWithEmployee(targetList, groupName, employee);
    }

    /**
     * 在目标队列中新建一个分组，并将雇员对象插入到新建的分组之中。
     * <p>
     * 单个分组的格式为：
     * {"name":(String)"groupName","employees":[(ManufacturerEmployeeVO)newEmployee,...]}
     *
     * @param targetList  待插入的目标队列
     * @param groupName   分组名称（位于分组Map的name对应值中。）
     * @param newEmployee 待插入的雇员对象
     * <AUTHOR> GuoYang
     * 2019/11/15
     **/
    private void createGroupWithEmployee(List<Map<String, Object>> targetList, String groupName, ManufacturerEmployeeVO newEmployee) {
        List<ManufacturerEmployeeVO> newEmployees = new ArrayList<>();
        newEmployees.add(newEmployee);
        Map<String, Object> newManufacturer = new HashMap<>();
        newManufacturer.put("name", groupName);
        newManufacturer.put("employees", newEmployees);
        targetList.add(newManufacturer);
    }

}
