package cc.crrc.manage.service.mtr;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.mtr.MtrManufacturerParameterMapping;
import cc.crrc.manage.pojo.component.ManufacturerParameterDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class MtrManufacturerParameterService {
    private final Logger logger = LoggerFactory.getLogger(MtrManufacturerParameterService.class);
    @Autowired
    private MtrManufacturerParameterMapping mtrManufacturerParameterMapping;

    public List<ManufacturerParameterDTO> getManufParam(String componentId, String vehicleId) {
        return mtrManufacturerParameterMapping.getManufParam(componentId, vehicleId);
    }

    @Transactional
    public Object insertManufParam(ManufacturerParameterDTO manufParam) {
        checkParamDateValid(manufParam);
        manufParam.setId(PrimaryKeyGenerator.generatorId());
        manufParam.setCreateBy(UserUtils.getUser().getId());
        int result = mtrManufacturerParameterMapping.insertManufParam(manufParam);
        if (result > 0)
            return "SUCCESS";
        throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
    }

    @Transactional
    public Object updateManufParam(ManufacturerParameterDTO manufParam) {
        manufParam.setModifyBy(UserUtils.getUser().getId());
        int result = mtrManufacturerParameterMapping.updateManufParam(manufParam);
        if (result > 0)
            return "SUCCESS";
        throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
    }

    @Transactional
    public Object deleteManufParam(String id) {
        int result = mtrManufacturerParameterMapping.deleteManufParam(id);
        if (result > 0)
            return "SUCCESS";
        throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
    }

    @Transactional
    public void clearManufParam(String componentId,String vehicleId){
        try {
            List<ManufacturerParameterDTO> manufacturerParameterDTOList = getManufParam(componentId,vehicleId);
            for (ManufacturerParameterDTO manufacturerParameterDTO:manufacturerParameterDTOList) {
                deleteManufParam(manufacturerParameterDTO.getId());
            }
        }catch (DataAccessException e) {
            logger.error("Method[clearManufParam] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }


    private void checkParamDateValid(ManufacturerParameterDTO manufParam) {
        boolean vehicleIsNull = manufParam.getVehicleId() == null;
        boolean componentIsNull = manufParam.getComponentId() == null;
        boolean bothNull = vehicleIsNull && componentIsNull;
        boolean bothHave = !(vehicleIsNull || componentIsNull);
        if (bothHave || bothNull)
            throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION);
    }

}
