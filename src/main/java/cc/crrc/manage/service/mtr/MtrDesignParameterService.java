package cc.crrc.manage.service.mtr;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.mtr.MtrDesignParameterMapping;
import cc.crrc.manage.pojo.component.DesignParameterDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class MtrDesignParameterService {
    private final Logger logger = LoggerFactory.getLogger(MtrDesignParameterService.class);
    @Autowired
    private MtrDesignParameterMapping mtrDesignParameterMapping;


    public List<DesignParameterDTO> getDesignParam(String comTypeId, String vehicleTypeId) {
        return mtrDesignParameterMapping.getDesignParam(comTypeId,vehicleTypeId);
    }

    @Transactional
    public Object insertDesignParam(DesignParameterDTO designParam) {
        checkParamDateValid(designParam);
        designParam.setId(PrimaryKeyGenerator.generatorId());
        designParam.setCreateBy(UserUtils.getUser().getId());
        int result = mtrDesignParameterMapping.insertDesignParam(designParam);
        if(result>0)
            return "SUCCESS";
        throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
    }

    @Transactional
    public Object updateDesignParam(DesignParameterDTO designParam) {
        checkParamDateValid(designParam);
        designParam.setModifyBy(UserUtils.getUser().getId());
        int result = mtrDesignParameterMapping.updateDesignParam(designParam);
        if(result>0)
            return "SUCCESS";
        throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
    }

    @Transactional
    public Object deleteDesignParam(String id) {
        int result = mtrDesignParameterMapping.deleteDesignParam(id);
        if(result>0)
            return "SUCCESS";
        throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
    }

    @Transactional
    public void clearDesignParam(String comTypeId, String vehicleId){
        try {
            List<DesignParameterDTO> designParameterDTOList = getDesignParam(comTypeId,vehicleId);
            for (DesignParameterDTO designParameterDTO:designParameterDTOList) {
                deleteDesignParam(designParameterDTO.getId());
            }
        }catch (DataAccessException e) {
            logger.error("Method[clearDesignParam] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    private void checkParamDateValid(DesignParameterDTO designParam) {
        boolean vehicleIsNull = designParam.getVehicleTypeId() == null;
        boolean componentIsNull = designParam.getComTypeId() == null;
        boolean bothNull = vehicleIsNull && componentIsNull;
        boolean bothHave = !(vehicleIsNull || componentIsNull);
        if (bothHave || bothNull)
            throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION);
    }

}
