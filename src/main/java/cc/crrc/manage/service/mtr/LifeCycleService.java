package cc.crrc.manage.service.mtr;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.StringUtils;
import cc.crrc.manage.mapper.mtr.LifeCycleMapping;
import cc.crrc.manage.pojo.mtr.ComponentLifeCycleVo;
import cc.crrc.manage.pojo.mtr.SoftwareLifeCycleVo;
import com.alibaba.fastjson.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 车辆管理-生命周期
 * 2019/11/18
 **/
@Service
public class LifeCycleService {
    @Autowired
    private LifeCycleMapping leftCycleMapping;

    public Object getLifeCycle(String structureCode, String vehicleId, ArrayList<String> type, String beginTime, String endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        //type 1初始部件  2软件升级  3更换部件  4部件检修
        JSONArray result = new JSONArray();
        List<ComponentLifeCycleVo> componentLifeCycle = leftCycleMapping.getComponentLiftCycle(structureCode, vehicleId);
        if (componentLifeCycle.size() == 0) {
            return result;
        }
        int i = 0;

        for (ComponentLifeCycleVo componemt : componentLifeCycle) {
            JSONArray jArray = new JSONArray();
            List<SoftwareLifeCycleVo> softwareLifeCycle = leftCycleMapping.getSoftwareLiftCycle(componemt.getId());
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            if (softwareLifeCycle.size() == 0) {
                JSONArray array = new JSONArray();
                array.add(componemt.getAssemblyTime());
                array.add(1);
                try {
                    componemt.setAssemblyTime(format.format(format.parse(componemt.getAssemblyTime())));
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                array.add(componemt);
                array.add("");
                jArray.add(array);
            }
            int j = 0;
            for (SoftwareLifeCycleVo software : softwareLifeCycle) {
                JSONArray array = new JSONArray();
                if (i == 0) {
                    if (j == 0) {
                        array.add(componemt.getAssemblyTime());
                        array.add(1);
                    } else {
                        array.add(software.getUpdateTime());
                        array.add(3);
                    }
                } else {
                    if (j == 0) {
                        array.add(componemt.getAssemblyTime());
                        array.add(2);
                    } else {
                        array.add(software.getUpdateTime());
                        array.add(3);
                    }
                }
                try {
                    componemt.setAssemblyTime(format.format(format.parse(componemt.getAssemblyTime())));
                    software.setUpdateTime(format.format(format.parse(software.getUpdateTime())));
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                array.add(componemt);
                array.add(software);
                jArray.add(array);
                j++;
            }
            i++;
            //部件检修
            /*List<FraMaintainVO> fraMaintanenceComponentList = leftCycleMapping.getFraMaintanenceComponentList(componemt.getId());
            for (FraMaintainVO fraMaintainVO : fraMaintanenceComponentList) {
                JSONArray array = new JSONArray();
                array.add(sdf.format(fraMaintainVO.getCreateTime()));
                array.add(4);
                array.add(componemt);
                //先将软件信息设置为""
                array.add("");
                jArray.add(array);
            }*/
            //添加检修部件后再次进行时间排序
            jArray.sort((o1, o2) -> {
                try {
                    return Long.compare(sdf.parse(JSONArray.parseArray(o2.toString()).get(0).toString()).getTime(),
                            sdf.parse(JSONArray.parseArray(o1.toString()).get(0).toString()).getTime());
                } catch (ParseException e) {
                    throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION, e.getMessage());
                }
            });
            //向前递归寻找类型不为检修部件（type=4）的软件信息
            for (int k = 0; k < jArray.size(); k++) {
                if ("4".equals(JSONArray.parseArray(jArray.get(k).toString()).get(1).toString())) {
                    JSONArray.parseArray(jArray.get(k).toString()).set(3, recursion(jArray, k + 1));
                }
            }
            result.fluentAddAll(jArray);
        }
        //时间段、履历类型 条件筛选
        return result.stream().filter(o -> {
            if (StringUtils.isNotEmpty(beginTime)) {
                try {
                    return sdf.parse(JSONArray.parseArray(o.toString()).get(0).toString()).getTime()
                            >= sdf.parse(beginTime).getTime();
                } catch (ParseException e) {
                    throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION, e.getMessage());
                }
            } else {
                return true;
            }
        }).filter(o -> {
            if (StringUtils.isNotEmpty(endTime)) {
                try {
                    return sdf.parse(JSONArray.parseArray(o.toString()).get(0).toString()).getTime()
                            <= sdf.parse(endTime).getTime();
                } catch (ParseException e) {
                    throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION, e.getMessage());
                }
            } else {
                return true;
            }
        }).filter(o -> {
            if (type.size()!=0) {
                return type.stream().anyMatch(s ->
                    s.equals(JSONArray.parseArray(o.toString()).get(1).toString())
                );
            } else {
                return false;
            }
        }).toArray(Object[]::new);
    }

    /**
     * @Description 查询时间更早的type不为4的软件信息
     * @Return java.lang.Object
     * <AUTHOR> wei
     * @Date 16:55 2020/5/7
     * @Param [jsonArray, k]
     **/
    private Object recursion(JSONArray jsonArray, int k) {
        return "4".equals(JSONArray.parseArray(jsonArray.get(k).toString()).get(1))
                ? recursion(jsonArray, k + 1) : JSONArray.parseArray(jsonArray.get(k).toString()).get(3);
    }

}
