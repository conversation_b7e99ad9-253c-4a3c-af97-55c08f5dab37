package cc.crrc.manage.service.mtr;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.mtr.MtrSubsystemDictMapping;
import cc.crrc.manage.mapper.mtr.MtrVehicleMapping;
import cc.crrc.manage.mapper.mtr.MtrVehicleTypeMapping;
import cc.crrc.manage.mapper.stru.StruVehicleTypeStructureMapping;
import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.excel.StruVehicleTypeStructureForExcelPO;
import cc.crrc.manage.pojo.mtr.*;
import cc.crrc.manage.pojo.stru.StruVehicleTypeStructurePO;
import cc.crrc.manage.service.SysFileService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static cc.crrc.manage.common.utils.Constants.STRUCTURE_PICTURE_FILE_FORMAT;
import static cc.crrc.manage.common.utils.Constants.STRUCTURE_PICTURE_FILE_TYPE;

/**
 * @FileName MtrVehicleTypeService
 * <AUTHOR> yuxi
 * @Date 2019/11/16 16:32
 * @Version 1.0
 **/
@Service
public class MtrVehicleTypeService {
    private final Logger logger = LoggerFactory.getLogger(MtrVehicleTypeService.class);
    @Autowired
    private MtrVehicleTypeMapping vehicleTypeMapping;
    @Autowired
    private MtrSubsystemDictMapping subsystemDictMapping;
    @Autowired
    private SysFileService sysFileService;
    @Autowired
    private StruVehicleTypeStructureMapping structureMapping;
    @Autowired
    private MtrVehicleMapping mtrVehicleMapping;

    public MtrVehicleTypeVO getVehicleTypeById(String vehicleTypeId) {
        return vehicleTypeMapping.getVehicleTypeById(vehicleTypeId);
    }

    /**
     * 根据车辆类型id，查询携带图片的车辆类型信息
     *
     * @param vehicleTypeId 车辆类型id
     * @return cc.crrc.manage.pojo.mtr.MtrVehicleTypeDetailVO
     * <AUTHOR> GuoYang
     * 2019/12/17
     **/
    public MtrVehicleTypeDetailVO getVehicleTypeWithPictureById(String vehicleTypeId) {
        try {
            return vehicleTypeMapping.getVehicleTypeWithPictureById(vehicleTypeId, STRUCTURE_PICTURE_FILE_TYPE);
        } catch (DataAccessException e) {
            logger.error("Method[getVehicleTypeWithPictureById] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @return java.util.List<cc.crrc.manage.pojo.mtr.MtrVehicleTypeVO>
     * @Description 取得车型列表
     * <AUTHOR> yuxi
     * @Date 11:16 2019/11/18
     * @Param []
     **/
    public PageInfo<MtrVehicleTypeVO> listVehicleType() {
        try {
            // 分页
            PageHelper.startPage(0, 0);
            List<MtrVehicleTypeVO> vehicleTypeList = vehicleTypeMapping.listVehicleType();
            return new PageInfo<>(vehicleTypeList);
        } catch (DataAccessException e) {
            logger.error("Method[listVehicleType] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @return int
     * @Description 更新车型
     * <AUTHOR> yuxi
     * @Date 11:23 2019/11/18
     * @Param [vehicleType]
     **/
    public int updateVehicleType(MtrVehicleTypeStructureVO vehicleTypeStructure) {
        try {
            String userId = UserUtils.getUserId();
            // 车型对象
            MtrVehicleTypePO vehicleType = createVehicleType(vehicleTypeStructure, userId);
            MtrVehicleTypeVO currentVehicleType = getVehicleTypeById(vehicleType.getId());
            if (!vehicleType.getManufacturerId().equals(currentVehicleType.getManufacturerId())) {
                deleteMtrVehicleTypeContacts(null, vehicleType.getId());
            }
            vehicleTypeMapping.updateVehicleType(vehicleType);
            // 构型对象
            StruVehicleTypeStructurePO structureNew = createStructure(vehicleTypeStructure, vehicleType.getId(), userId);
            // 如果更改了当前节点的structureCode，更新构型信息前先更新子节点的parentStructureCode
            StruVehicleTypeStructurePO structureOrigin = structureMapping.getStruVehicleTypeStructureById(structureNew.getId());
            if (!structureOrigin.getStructureCode().equals(structureNew.getStructureCode())) {
                structureMapping.updateChildrenStructureCode(structureOrigin.getStructureCode(), structureNew.getStructureCode(), structureNew.getVehicleTypeId());
            }
            return structureMapping.updateStruVehicleTypeStructure(structureNew);
        } catch (DataAccessException e) {
            logger.error("Method[updateVehicleType] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    /**
     * @return int
     * @Description 新增车型
     * <AUTHOR> yuxi
     * @Date 11:27 2019/11/18
     * @Param [vehicleType]
     **/
    @Transactional(rollbackFor = Exception.class)
    public int insertVehicleType(MtrVehicleTypeStructureVO vehicleTypeStructure) {
        try {
            String userId = UserUtils.getUserId();
            // 车型对象
            MtrVehicleTypePO vehicleType = createVehicleType(vehicleTypeStructure, userId);
            // 雪花算法添加主键
            vehicleType.setId(PrimaryKeyGenerator.generatorId());
            vehicleTypeMapping.insertVehicleType(vehicleType);
            // 构型对象
            StruVehicleTypeStructurePO structure = createStructure(vehicleTypeStructure, vehicleType.getId(), userId);
            // 雪花算法添加主键
            structure.setId(PrimaryKeyGenerator.generatorId());
            int addStruVehicleTypeStructureNumber = structureMapping.addStruVehicleTypeStructure(structure);
            if(addStruVehicleTypeStructureNumber>0){
                //添加 上传excel业务 如果有excel则进行excel上传操作
                List<StruVehicleTypeStructureForExcelPO> list = vehicleTypeStructure.getStruVehicleTypeStructureForExcelList();
                if(CollectionUtils.isNotEmpty(list)){
                    String vehicleTypeId = structure.getVehicleTypeId();
                    //获取新旧structureCode
                    String structureCode = structure.getStructureCode();
                    String [] structureCodeOld =list.get(0).getStructureCode().split("/");
                    String structureCodeExcelOld = structureCodeOld[0];
                    Iterator<StruVehicleTypeStructureForExcelPO> it=list.iterator();
                    while(it.hasNext()) {
                        StruVehicleTypeStructureForExcelPO next = it.next();
                        //迭代找到根节点进行删除 因为新建车型本身包括根节点 如果excel中还有根节点会出现脏数据
                        if ("root".equals(next.getParentStructureCode())) {
                            it.remove();
                        }
                        //将模板中原来的VehicleTypeId改为新建生成的VehicleTypeId
                        next.setVehicleTypeId(vehicleTypeId);
                        //将模板中原来的StructureCode改为新的StructureCode
                        String structureCodeForChange = next.getStructureCode().replace(structureCodeExcelOld,structureCode);
                        next.setStructureCode(structureCodeForChange);
                        //将模板中原来的parentStructureCode改为新的parentStructureCode
                        String parentStructureCodeForChange = next.getParentStructureCode().replace(structureCodeExcelOld,structureCode);
                        next.setParentStructureCode(parentStructureCodeForChange);
                        //将模板中原来的StructurePosition改为新的StructurePosition
                        String structurePositionForChange = next.getStructurePosition().replace(structureCodeExcelOld,structureCode);
                        next.setStructurePosition(structurePositionForChange);
                    }
                    // 雪花算法添加主键
                    for (StruVehicleTypeStructureForExcelPO s:list) {
                        s.setId(PrimaryKeyGenerator.generatorId());
                    }
                    structureMapping.addStruVehicleTypeStructureList(list);
                }
            }
            return addStruVehicleTypeStructureNumber;
        } catch (DataAccessException e) {
            logger.error("Method[insertVehicleType] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * @return cc.crrc.manage.pojo.mtr.MtrVehicleTypePO
     * @Description 设置车型对象
     * <AUTHOR> yuxi
     * @Date 10:21 2019/12/14
     * @Param [vehicleTypeStructure, userId]
     **/
    private MtrVehicleTypePO createVehicleType(MtrVehicleTypeStructureVO vehicleTypeStructure, String userId) {
        MtrVehicleTypePO vehicleType = new MtrVehicleTypePO();
        vehicleType.setId(vehicleTypeStructure.getVehicleTypeId());
        vehicleType.setName(vehicleTypeStructure.getName());
        vehicleType.setType(vehicleTypeStructure.getType());
        vehicleType.setMarshallingNumber(vehicleTypeStructure.getMarshallingNumber());
        vehicleType.setManufacturerId(vehicleTypeStructure.getManufacturerId());
        vehicleType.setCommType(vehicleTypeStructure.getCommType());
        vehicleType.setRemark(vehicleTypeStructure.getRemark());
        vehicleType.setLineId(vehicleTypeStructure.getLineId());
        vehicleType.setCreateBy(userId);
        vehicleType.setModifyBy(userId);
        vehicleType.setVehicleTypeCode(vehicleTypeStructure.getVehicleTypeCode());
        return vehicleType;
    }

    /**
     * @return cc.crrc.manage.pojo.stru.StruVehicleTypeStructurePO
     * @Description 设置构型对象
     * <AUTHOR> yuxi
     * @Date 10:21 2019/12/14
     * @Param [vehicleTypeStructure, userId]
     **/
    private StruVehicleTypeStructurePO createStructure(MtrVehicleTypeStructureVO vehicleTypeStructure, String vehicleTypeId, String userId) {
        StruVehicleTypeStructurePO structure = new StruVehicleTypeStructurePO();
        structure.setId(vehicleTypeStructure.getStructureId());
        structure.setVehicleTypeId(vehicleTypeId);
        structure.setParentStructureCode("root");
        structure.setNameCn(vehicleTypeStructure.getName());
        structure.setNameEn(vehicleTypeStructure.getNameEn());
        structure.setShortNameEn(vehicleTypeStructure.getShortNameEn());
        structure.setStructurePosition(vehicleTypeStructure.getShortNameEn());
        structure.setStructureCode(vehicleTypeStructure.getStructureCode());
        structure.setStructureType("车型");
        structure.setSortNumber(vehicleTypeStructure.getSortNumber());
        structure.setCreateBy(userId);
        structure.setModifyBy(userId);
        return structure;
    }

    /**
     * @return int
     * @Description 删除车型
     * <AUTHOR> yuxi
     * @Date 11:29 2019/11/18
     * @Param [vehicleType]
     **/
    @Transactional(rollbackFor = Exception.class)
    public int deleteVehicleType(String id) {
        try {
            // 2020/04/06 Jin Guoyang 取消 判断车型下是否存在构型 限制
//            List<StruVehicleTypeStructureTreeVO> structureList = structureMapping.selectStructureTreeList(id);
//            if (!CollectionUtils.isEmpty(structureList) && structureList.size() > 1) {
//                throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION.getErrorCode(), "该构型含有子节点，请确认无子节点后再进行删除！");
//            }
            // 2020/04/06 Jin Guoyang 车型下绑定车辆时，不允许删除。
            MtrVehicleDTO vehicleDTO = new MtrVehicleDTO();
            vehicleDTO.setVehicleTypeId(id);
            List<MtrVehicleVO> vehicleVOList = mtrVehicleMapping.listVehicleInfo(vehicleDTO);
            if (!vehicleVOList.isEmpty()) {
                throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION, "该车型下存在绑定车辆！");
            }
            String userId = UserUtils.getUserId();
            return vehicleTypeMapping.deleteVehicleType(userId, id);
        } catch (DataAccessException e) {
            logger.error("Method[deleteVehicleType] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    /**
     * @return int
     * @Description 根据车型id删除子系统字典
     * <AUTHOR> yuxi
     * @Date 16:05 2019/11/19
     * @Param [vehicleTypeId]
     **/
    public int deleteSubsystemDict(String vehicleTypeId,String nameCn) {
        try {
            return subsystemDictMapping.deleteSubsystemDict(vehicleTypeId,nameCn);
        } catch (DataAccessException e) {
            logger.error("Method[deleteSubsystemDict] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    /**
     * 保存车辆类型的相关文件
     *
     * @param sysFile       文件信息
     * @param vehicleTypeId 车型id
     * <AUTHOR> GuoYang
     * 2019/12/18
     **/
    @Transactional(rollbackFor = Exception.class)
    public void insertVehicleTypeFile(SysFilePO sysFile, String vehicleTypeId) {
        try {
            // 构型图片上传限制
            if (STRUCTURE_PICTURE_FILE_TYPE.equals(sysFile.getType())) {
                List<String> temp = Arrays.asList(STRUCTURE_PICTURE_FILE_FORMAT);
                if (!temp.contains(sysFile.getFormat().toLowerCase())) {
                    throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION, "不支持上传该图片类型！");
                }
                List<SysFilePO> sysFilePOList = listVehicleTypeFile(vehicleTypeId);
                for (SysFilePO sysFilePO : sysFilePOList) {
                    if (STRUCTURE_PICTURE_FILE_TYPE.equals(sysFilePO.getType()) && sysFilePO.getDelFlag() == 0) {
                        throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION, "不可上传多张同类型图片！");
                    }
                }
            }
            // 保存文件
            String fileId = sysFileService.addSysFile(sysFile);
            // 保存车型文件关系
            vehicleTypeMapping.insertVehicleTypeFileRelation(vehicleTypeId, fileId);
        } catch (DataAccessException e) {
            logger.error("Method[insertVehicleTypeFile] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * @return int
     * @Description 删除车型文件
     * <AUTHOR> yuxi
     * @Date 9:26 2019/11/29
     * @Param [sysFiles, id]
     **/
    @Transactional(rollbackFor = Exception.class)
    public int deleteVehicleTypeFileRelation(String vehicleTypeId, String fileId) {
        try {
            // 删除文件
            sysFileService.deleteSysFile(fileId);
            // 删除车型文件关系
            return vehicleTypeMapping.deleteVehicleTypeFileRelation(vehicleTypeId, fileId);
        } catch (DataAccessException e) {
            logger.error("Method[deleteVehicleTypeFileRelation] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    /**
     * @return int
     * @Description 根据车型id获取车型文件列表
     * <AUTHOR> yuxi
     * @Date 9:26 2019/11/29
     * @Param [sysFiles, id]
     **/
    public List<SysFilePO> listVehicleTypeFile(String vehicleTypeId) {
        try {
            return vehicleTypeMapping.listVehicleTypeFile(vehicleTypeId);
        } catch (DataAccessException e) {
            logger.error("Method[listVehicleTypeFile] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object insertMtrVehicleTypeContacts(String manufacturerEmployeeId, String vehicleTypeId) {
        try {
            // TODO Auto-generated method stub
            int count = vehicleTypeMapping.validCount(manufacturerEmployeeId, vehicleTypeId);
            if (count > 0) {
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "该雇员已存在");
            }
            vehicleTypeMapping.insertMtrVehicleTypeContacts(manufacturerEmployeeId, vehicleTypeId);
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
        return "SUCCESS";
    }

    public Object deleteMtrVehicleTypeContacts(String manufacturerEmployeeId, String vehicleTypeId) {
        // TODO Auto-generated method stub
        try {
            vehicleTypeMapping.deleteMtrVehicleTypeContacts(manufacturerEmployeeId, vehicleTypeId);
        } catch (Exception e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
        return "SUCCESS";
    }

    public Object selectEmployees(String vehicleTypeId) {
        return vehicleTypeMapping.selectEmployees(vehicleTypeId);
    }



    /*
     * @return
     * @Description 上传Excel 同步车型构型列表
     * <AUTHOR> xin
     * @Date 16:37 2020/5/7
     * @Param
     **/
    @Transactional
    public Object updateVehicleTypeByExcel(MtrVehicleTypeStructureVO vehicleTypeStructure) {
        String vehicleTypeId = vehicleTypeStructure.getVehicleTypeId();
        String structureCode = vehicleTypeStructure.getStructureCode();
        List<StruVehicleTypeStructureForExcelPO> struVehicleTypeStructureForExcelList = vehicleTypeStructure.getStruVehicleTypeStructureForExcelList();
        //查询被同步的车型现有的数据
        List<StruVehicleTypeStructureForExcelPO> list = structureMapping.getStruVehicleTypeStructureForExcel(vehicleTypeId,null);
        try {
            if(CollectionUtils.isNotEmpty(struVehicleTypeStructureForExcelList)){
                //步骤一：处理从excel出来的数据
                changeStrctureCodeStruList(vehicleTypeId, structureCode, struVehicleTypeStructureForExcelList);
                //步骤二：完整性校验（断层）首先查出被同步车型所有数据
                checkIntegrity(struVehicleTypeStructureForExcelList,list);
                //为增加效率 声明一个listAdd保存需要新增的数据 一起新增
                List<StruVehicleTypeStructureForExcelPO> listAdd = new ArrayList<>();
                for(StruVehicleTypeStructureForExcelPO struVehicleTypeStructure:struVehicleTypeStructureForExcelList) {
                    //检查每条记录StructureCode是否存在 存在就更新部分内容不存在则进行新增
                    //首先声明一个循环中使用的structureCode
                    String structureCodeForForEach = struVehicleTypeStructure.getStructureCode();
                    Boolean structureCodeUpdateStatus = false;//判断记录是需要更新 还是新增
                    Boolean strcCodeUpdateStatus = false;
                    String id = null;
                    for(StruVehicleTypeStructureForExcelPO structureCodestatus:list){
                        if(structureCodestatus.getStructureCode().contains(structureCodeForForEach)){
                            if(
                                    Objects.equals(structureCodestatus.getNameCn(),struVehicleTypeStructure.getNameCn())&&
                                    Objects.equals(structureCodestatus.getComponentTypeId(),struVehicleTypeStructure.getComponentTypeId())&&
                                    Objects.equals(structureCodestatus.getSortNumber(),struVehicleTypeStructure.getSortNumber())&&
                                    Objects.equals(structureCodestatus.getRemark(),struVehicleTypeStructure.getRemark())&&
                                    Objects.equals(structureCodestatus.getNameEn(),struVehicleTypeStructure.getNameEn())&&
                                    Objects.equals(structureCodestatus.getShortNameEn(),struVehicleTypeStructure.getShortNameEn())&&
                                    Objects.equals(structureCodestatus.getStructureType(),struVehicleTypeStructure.getStructureType())
                            ){
                                strcCodeUpdateStatus =true;
                                break;
                            }else {
                                structureCodeUpdateStatus =true;
                                id = structureCodestatus.getId();
                                break;
                            }
                        }
                    }
                    //如果记录已存在且没有更新 则跳出此层循环
                    if(strcCodeUpdateStatus){
                        continue;
                    }
                    if (structureCodeUpdateStatus) {
                        StruVehicleTypeStructurePO po = new StruVehicleTypeStructurePO();
                        //将刚刚查询到已经存在的记录id传值进去
                        po.setId(id);
                        po.setNameCn(struVehicleTypeStructure.getNameCn());
                        po.setComponentTypeId(struVehicleTypeStructure.getComponentTypeId());
                        po.setSortNumber(struVehicleTypeStructure.getSortNumber());
                        po.setRemark(struVehicleTypeStructure.getRemark());
                        po.setNameEn(struVehicleTypeStructure.getNameEn());
                        po.setShortNameEn(struVehicleTypeStructure.getShortNameEn());
                        po.setStructureType(struVehicleTypeStructure.getStructureType());
                        structureMapping.updateStruVehicleTypeStructure(po);
                    } else {
                        //如果通过第一步校验 且不重复则进行新增
                        //雪花算法添加主键
                        struVehicleTypeStructure.setId(PrimaryKeyGenerator.generatorId());
                        listAdd.add(struVehicleTypeStructure);
                    }
                }
                //将上面循环得到的需要新增的数据 统一新增
                if(CollectionUtils.isNotEmpty(listAdd)){
                    //新增操作
                    structureMapping.addStruVehicleTypeStructureList(listAdd);
                }
                //更新之后查询当前车型构型数据然后对新的车厢记录的中文名称做重复校验
                List<StruVehicleTypeStructureForExcelPO> listCheckLocation = structureMapping.getStruVehicleTypeStructureForExcel(vehicleTypeId,null);
                //存储excel中所有车厢数据 为 车厢记录的中文名称做重复校验使用
                List<String>listLocation = new ArrayList<>();
                Iterator<StruVehicleTypeStructureForExcelPO> it=listCheckLocation.iterator();
                while(it.hasNext()) {
                    StruVehicleTypeStructureForExcelPO next = it.next();
                    //如果structure_type是车厢时 将此条数据加到listLocation 如果重复则报错
                    if ("车厢".equals(next.getStructureType())) {
                        if(listLocation.contains(next.getNameCn())){
                            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION, "车厢中文名称重复:"+next.getNameCn());
                        }
                        listLocation.add(next.getNameCn());
                    }
                }
            }
            else {
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION, "excel中没有数据！");
            }
        } catch (Exception e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION,e.getMessage());
        }
        return "SUCCESS";
    }

    //校验完整性
    private void checkIntegrity(List<StruVehicleTypeStructureForExcelPO> struVehicleTypeStructureForExcelList,List<StruVehicleTypeStructureForExcelPO>list) {

        ArrayList<StruVehicleTypeStructureForExcelPO> listAll =new ArrayList<>();
        //21-11-17 梳理逻辑 判断传入的两组数据不为空 如果为空在之后的循环逻辑会报错（lx jgy 讨论所得）
        //21-11-17 之所以要在listAll 将原有数据和新数据同时存入 主要是担心新数据不全 导致校验失败
        Boolean mergeStatus = listAll.addAll(list);
        Boolean mergeStatus1 = listAll.addAll(struVehicleTypeStructureForExcelList);
        if(!mergeStatus&&mergeStatus1){
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION, "完整性校验前期准备失败！");
        }
        Boolean verifyIntegrity = true;//验证完整性 status 为true时则说明不完整
        for(StruVehicleTypeStructureForExcelPO struVehicleTypeStructure:struVehicleTypeStructureForExcelList){
            //检查每条记录的ParentStructureCode是否存在如果存在则进行下一步校验 不存在立即回滚报错
            for(StruVehicleTypeStructureForExcelPO a:listAll){
                if(struVehicleTypeStructure.getParentStructureCode().equals(a.getStructureCode())){
                    verifyIntegrity = false;
                    continue;
                }
            }
            if(verifyIntegrity){
                throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION,
                        "此节点"+"'"+struVehicleTypeStructure.getNameCn()+"'"+"缺少父节点！"+struVehicleTypeStructure.getStructureCode());
            }
        }
    }

    //处理从excel出来的数据
    private void changeStrctureCodeStruList(String vehicleTypeId, String structureCode, List<StruVehicleTypeStructureForExcelPO> struVehicleTypeStructureForExcelList) {
        //获取旧structureCode
        String [] structureCodeOld =struVehicleTypeStructureForExcelList.get(0).getStructureCode().split("/");
        String structureCodeExcelOld = structureCodeOld[0];
        //获取新structureCode
        String[]structureCodeNew = structureCode.split("/");
        String structureCodeExcelNew = structureCodeNew[0];

        //存储excel中所有车厢数据 为 车厢记录的中文名称做重复校验使用
        List<String>listLocation = new ArrayList<>();

        Iterator<StruVehicleTypeStructureForExcelPO> it=struVehicleTypeStructureForExcelList.iterator();
        while(it.hasNext()) {
            StruVehicleTypeStructureForExcelPO next = it.next();
            //迭代找到根节点进行删除 因为新建车型本身包括根节点 如果excel中还有根节点会出现脏数据
            if ("root".equals(next.getParentStructureCode())) {
                it.remove();
            }
            //将模板中原来的VehicleTypeId改为新建生成的VehicleTypeId
            next.setVehicleTypeId(vehicleTypeId);
            //将模板中原来的StructureCode改为新的StructureCode
            String structureCodeForChange = next.getStructureCode().replace(structureCodeExcelOld,structureCodeExcelNew);
            next.setStructureCode(structureCodeForChange);
            //将模板中原来的parentStructureCode改为新的parentStructureCode
            String parentStructureCodeForChange = next.getParentStructureCode().replace(structureCodeExcelOld,structureCodeExcelNew);
            next.setParentStructureCode(parentStructureCodeForChange);
            //将模板中原来的StructurePosition改为新的StructurePosition
            String structurePositionForChange = next.getStructurePosition().replace(structureCodeExcelOld,structureCodeExcelNew);
            next.setStructurePosition(structurePositionForChange);
            //如果structure_type是车厢时 将此条数据加到listLocation 如果重复则报错
            if ("车厢".equals(next.getStructureType())) {
                if(listLocation.contains(next.getNameCn())){
                    throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION, "车厢中文名称重复:"+next.getNameCn());
                }
                    listLocation.add(next.getNameCn());
            }
        }
    }
}
