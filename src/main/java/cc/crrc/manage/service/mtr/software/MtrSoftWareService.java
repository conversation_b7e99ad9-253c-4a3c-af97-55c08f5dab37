package cc.crrc.manage.service.mtr.software;

import cc.crrc.manage.cache.CacheUtils;
import cc.crrc.manage.cache.dict.DictCache;
import cc.crrc.manage.cache.dict.VehicleCache;
import cc.crrc.manage.common.annotation.ParamReplace;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.LoggerUtils;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.mtr.MtrSoftWareMapping;
import cc.crrc.manage.pojo.SysDictVO;
import cc.crrc.manage.pojo.mtr.*;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static cc.crrc.manage.mq.process.KafkaSoftWareVersionConsumer.AUTO_FLAG;


/**
 * 制造商管理
 *
 * <AUTHOR>
 * 2021/09/17
 **/
@Service
public class MtrSoftWareService {
    private static final Logger logger = LoggerFactory.getLogger(MtrSoftWareService.class);
    private static final Logger VERSION_LOGGER = LoggerFactory.getLogger("software.version.update");
    private final static int TRAIN_LOCATION_NUMBER = 4;
    private final static List<Integer> POSITIVE_SEQUENCE_INDEX = Arrays.asList(1, 3, 5, 7, 0, 2, 4, 6);

    @Autowired
    private MtrSoftWareMapping softWareMapping;

    @Transactional(rollbackFor = Exception.class)
    public void updateSoftWareInfo(boolean manualConfirm, Integer confirmStatus, MtrSoftWarePO... mtrSoftWares) {
        //查询是否存在同线路、同车辆、同软件、同版本的记录
        for (MtrSoftWarePO mtrSoftWarePO : mtrSoftWares) {
            //如果是手动确认 更新手动历史状态
            if (manualConfirm) {
                mtrSoftWarePO.setOperator(UserUtils.getUserId());
                mtrSoftWarePO.setEffectiveTime(new Date());
                softWareMapping.updateManualSoftware(mtrSoftWarePO.getId(), confirmStatus);
                //手动忽略
                if (confirmStatus == 2) {
                    continue;
                }
            }
            int count = softWareMapping.duplicationChecking(mtrSoftWarePO);
            //若不存在就往之前的版本录入入结束时间，并插入当前的版本
            if (count == 0) {
                softWareMapping.updateSoftWareForEndTime(mtrSoftWarePO);
                //根据车辆code查询出线路id 一起入库
                mtrSoftWarePO.setId(PrimaryKeyGenerator.generatorId());
                mtrSoftWarePO.setEndTime(null);
                mtrSoftWarePO.setCreateBy("0");
                mtrSoftWarePO.setModifyBy("0");
                softWareMapping.insertSoftWareInfo(mtrSoftWarePO);
            } else {
                VERSION_LOGGER.info("版本已存在--{}", mtrSoftWarePO.toString());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertSoftwareManualHistory(MtrSoftWarePO mtrSoftWarePO) {
        mtrSoftWarePO.setId(PrimaryKeyGenerator.generatorId());
        mtrSoftWarePO.setCreateBy("0");
        mtrSoftWarePO.setModifyBy("0");
        mtrSoftWarePO.setEndTime(null);
        softWareMapping.insertSoftwareManualHistory(mtrSoftWarePO);
    }

    @ParamReplace(param = {"name", "signalNameEn", "version", "operator"}, type = MtrSoftWareDTO.class)
    public Object softWareInfoList(MtrSoftWareDTO mtrSoftWareDTO) {
        try {
            //分页
            PageHelper.startPage(mtrSoftWareDTO.getPageNumber(), mtrSoftWareDTO.getPageSize());
            //查询当前版本列表数据
            List<MtrSoftWareVO> list = softWareMapping.softWareInfoList(mtrSoftWareDTO);
            for (MtrSoftWareVO mtrSoftWareVO : list) {
                String vehicleCode = mtrSoftWareVO.getVehicleCode();
                String signalNameEn = mtrSoftWareVO.getSignalNameEn();
                //查询历史版本数据
                List<MtrSoftWareResumeVO> mtrSoftWareResumeVOS = softWareMapping.listHistoryVersion(vehicleCode, signalNameEn);
                if (mtrSoftWareResumeVOS.size() > 0) {
                    mtrSoftWareVO.setChildren(mtrSoftWareResumeVOS);
                } else {
                    mtrSoftWareVO.setChildren(new ArrayList<>());
                }
            }
            return new PageInfo<>(list);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "数据查询错误！");
        }
    }

    public Object updateUpdaterInfo(String id, String updater) {
        try {
            String userId = UserUtils.getUserId();
            if (updater.length() > 25) {
                throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION.getErrorCode(), "输入内容长度超过了25！请重新输入。");
            }
            return softWareMapping.updateUpdaterInfo(id, updater, userId);
        } catch (DataAccessException e) {
            logger.error("Method[updateUpdaterInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    //项目车门子系统名称
    private static final String DOR_SUBSYSTEM = "EDCU";
    private static final String DOR_SUBSYSTEM_NAME = "车门软件版本";

    public JSONObject ListAllSoftwareVersion() {
        JSONObject res = new JSONObject();
        try {
            List<MtrSoftwareVersionVO> versionList = softWareMapping.ListAllSoftwareVersion();
            // 先分离车门
//            Map<Boolean, List<MtrSoftwareVersionVO>> separateList = versionList.stream()
//                    .collect(Collectors.partitioningBy(x -> DOR_SUBSYSTEM.equals(x.getSubsystem())));
//            List<MtrSoftwareVersionVO> dorList = separateList.get(true);
//            List<MtrSoftwareVersionVO> otherList = separateList.get(false);
            //根据车辆编号分类车门数据
//            Map<String, List<MtrSoftwareVersionVO>> dorListGroupedByVehicleCode = dorList.stream()
//                    .collect(Collectors.groupingBy(MtrSoftwareVersionVO::getVehicleCode));
            //车辆最大版本list
//            List<MtrSoftwareVersionVO> finalDorList = new LinkedList<>();
//            dorListGroupedByVehicleCode.forEach((vehicleCode, item) -> {
//                //对每一个车辆编号下所属车门数据链表进行遍历，找出当前车辆号下最大的车门版本号
//                MtrSoftwareVersionVO vo = item.stream().max(this::myComparator).get();
//                //是否存在最大相同版本，存在就对最大版本数据赋红
//                Optional<MtrSoftwareVersionVO> any = item.stream().filter(x -> !x.getMaxversion().equals(vo.getMaxversion())).findAny();
//                if (any.isPresent()) {
//                    vo.setIsRed("1");
//                }
//                vo.setName(DOR_SUBSYSTEM_NAME);
//                //找出每个车的最大版本组成列表
//                finalDorList.add(vo);
//            });
            //车门最大版本加其他软件版本
//            otherList.addAll(finalDorList);
//            versionList = otherList;
            // 将结果按照名字，子系统，位置分组
            Map<String, List<MtrSoftwareVersionVO>> versionMap = versionList.stream()
                    .collect(Collectors.groupingBy(o -> String.join("_"
                            , o.getName() == null ? "--" : o.getName()
                            , o.getSubsystem() == null ? "--" : o.getSubsystem()
                            , o.getVehicleCode() == null ? "--" : o.getVehicleCode())));
            // 车号
//        List<MtrVehiclePO> vehicle = mtrVehicleMapping.listVehicleByLineIdAndVehicleTypeId("24",null);
            List<MtrVehiclePO> vehicle = CacheUtils.getValue(VehicleCache.class, CacheUtils.METRO_LINE_ID);
            res.put("carList", vehicle);
            // 处理basicList
            JSONArray basicList = new JSONArray();
            List<MtrSoftwareVersionVO> maxVersionList = new LinkedList<>();
            // 遍历取出最大版本号
            versionMap.forEach((keys, vehicleList) -> {
                maxVersionList.add(proceedVehicleList(vehicleList));
            });
            //按照子系统排序后按照名字分组
            Map<String, List<MtrSoftwareVersionVO>> versionGroupByName = maxVersionList.stream().sorted(Comparator.comparing(MtrSoftwareVersionVO::getSubsystem))
                    .collect(Collectors.groupingBy(MtrSoftwareVersionVO::getName, LinkedHashMap::new, Collectors.toList()));
            versionGroupByName.forEach((x, v) -> {
                JSONObject tmp = new JSONObject();
                //给分组赋名
                tmp.put("nameCn", v.get(0).getName());
                List<MtrSoftwareVersionVO> trueVehicleList = new LinkedList<>();
                for (int i = 0; i < vehicle.size(); i++) {
                    String name = vehicle.get(i).getVehicleCode();
                    // 判断是否已经有这辆车了
                    MtrSoftwareVersionVO exist = isExist(maxVersionList, name, v.get(0).getName());
                    if (exist == null) {
                        //没有就赋空
                        MtrSoftwareVersionVO vo = new MtrSoftwareVersionVO();
                        vo.setVehicleCode(name);
                        trueVehicleList.add(vo);
                    } else {
                        // 如果有存入
                        trueVehicleList.add(exist);
                    }
                }
                tmp.put("list", trueVehicleList);
                basicList.add(tmp);
            });
            res.put("basicList", basicList);
        } catch (DataAccessException e) {
            LoggerUtils.error(logger, e.getMessage());
//            logger.error("method[ListAllSoftwareVersion] 异常 error {}", e.getMessage());
        }
        return res;
    }

    /**
     * 重构的查询所有软件版本
     *
     * @return
     */
    public JSONObject RefactorListAllSoftwareVersion() {
        JSONObject res = new JSONObject();
        JSONArray basicList = new JSONArray();
        try {
            List<MtrSoftwareVersionVO> versionList = softWareMapping.ListAllSoftwareVersion();
            //车门子系统下的数据存在不同名字，更换为相同的方便统一比较版本
            versionList = versionList.stream().peek(v -> {
                if (DOR_SUBSYSTEM.equals(v.getSubsystem())) {
                    v.setName(DOR_SUBSYSTEM_NAME);
                }
            }).collect(Collectors.toList());
            // 将结果按照名字，子系统，位置分组
            Map<String, List<MtrSoftwareVersionVO>> versionMap = versionList.stream()
                    .collect(Collectors.groupingBy(o -> String.join("_"
                            , o.getName() == null ? "--" : o.getName()
                            , o.getSubsystem() == null ? "--" : o.getSubsystem()
                            , o.getVehicleCode() == null ? "--" : o.getVehicleCode())));
            // 遍历取出最大版本号
            List<MtrSoftwareVersionVO> maxVersionList = new LinkedList<>();
            //缓存中获取车辆信息
            List<MtrVehiclePO> vehicle = CacheUtils.getValue(VehicleCache.class, CacheUtils.METRO_LINE_ID);
            //将分组好的数据遍历取出最大版本号，并且将有冲突的赋红
            versionMap.forEach((keys, vehicleList) -> {
                //将各个分类的最大版本数据收集起来
                maxVersionList.add(proceedVehicleList(vehicleList));
            });
            // 一个名称分类下存在多个车辆的数据
            //按照名称分类，按照子系统排序
            Map<String, List<MtrSoftwareVersionVO>> collect = maxVersionList.stream().sorted(Comparator.comparing(MtrSoftwareVersionVO::getSubsystem))
                    .collect(Collectors.groupingBy(MtrSoftwareVersionVO::getName, LinkedHashMap::new, Collectors.toList()));
            //遍历分类数据，将名称分类下没有版本数据的其余车辆数据补全
            collect.forEach((name, list) -> {
                //新建一个存放所有车辆数据的列表
                ArrayList<MtrSoftwareVersionVO> vehicleVersionList = new ArrayList<>(vehicle.size());
                //将存在版本数据的车辆取出按照编号分组
                Map<String, List<MtrSoftwareVersionVO>> maxVersion = list.stream().collect(Collectors.groupingBy(MtrSoftwareVersionVO::getVehicleCode));
                //遍历所有车辆
                vehicle.forEach(v -> {
                    //如果存在有版本数据的车辆就添加该数据
                    if (maxVersion.containsKey(v.getVehicleCode())) {
                        vehicleVersionList.add(maxVersion.get(v.getVehicleCode()).get(0));
                    } else {
                        //不存在数据的车辆就添加该车辆的空数据
                        MtrSoftwareVersionVO vo = new MtrSoftwareVersionVO();
                        vo.setVehicleCode(v.getVehicleCode());
                        vehicleVersionList.add(vo);
                    }
                });
                //构建返回数据结构
                JSONObject object = new JSONObject();
                object.put("nameCn", name);
                object.put("list", vehicleVersionList);
                basicList.add(object);
            });
            res.put("carList", vehicle);
            res.put("basicList", basicList);
        } catch (DataAccessException e) {
            logger.error("method[RefactorListAllSoftwareVersion] 异常 error {}", e.getMessage());
        }
        return res;
    }

    /**
     * 调整车门顺序
     *
     * @param list
     * @return
     */
    public List<MtrSoftwareVersionVO> adjustDorSeq(List<MtrSoftwareVersionVO> list, int locationNum) {
        if (list.size() != POSITIVE_SEQUENCE_INDEX.size()) {
            return list;
        }
        // 先整成 2 4 6 8 1 3 5 7的顺序,默认传来的数据是 1 2 3 4 5 6 7 8
        List<MtrSoftwareVersionVO> newList = new LinkedList<>();
        for (int i = 0; i < POSITIVE_SEQUENCE_INDEX.size(); i++) {
            newList.add(list.get(POSITIVE_SEQUENCE_INDEX.get(i)));
        }
        if (locationNum < TRAIN_LOCATION_NUMBER / 2) {
            // 2 4 6 8 1 3 5 7
            return newList;
        } else {
            // 7 5 3 1 8 6 4 2
            Collections.reverse(newList);
            return newList;
        }
    }

    public List<MtrSoftwareVersionVO> ListSoftwareVersionByLocation(String vehicleCode, String subsystem, String softwareName) {
        List<MtrSoftwareVersionVO> versionList;
        // 判断是否全部车厢都有 A1,B1,B2,A2
        List<SysDictVO> list = CacheUtils.getValue(DictCache.class,
                StringUtils.join(DictCache.TRAIN_ORGANIZE, "_", CacheUtils.METRO_LINE_ID, "_", CacheUtils.VEHICLE_TYPE_ID));
        List<String> locations = list.stream()
                .filter(o -> !"ALL".equals(o.getValue()))
                .map(SysDictVO::getValue)
                .collect(Collectors.toList());
        if (subsystem.equalsIgnoreCase(DOR_SUBSYSTEM)) {
            versionList = proceedDorList(vehicleCode);
        } else {
            versionList = softWareMapping.ListSoftwareVersionByLocation(vehicleCode, subsystem, softwareName);
//            Map<String, List<MtrSoftwareVersionVO>> locationList = versionList.stream().collect(Collectors.groupingBy(MtrSoftwareVersionVO::getLocation));
//            versionList.clear();
//            locationList.forEach((location, item) -> {
//                versionList.add(item.stream().max(this::myComparator).get());
//            });
        }
        for (String location : locations) {
            Optional<MtrSoftwareVersionVO> first = versionList.stream().filter(x -> x.getLocation().equals(location)).findFirst();
            // 不存在 添加
            if (!first.isPresent()) {
                MtrSoftwareVersionVO tmp = new MtrSoftwareVersionVO();
                tmp.setName(softwareName);
                tmp.setLocation(location);
                tmp.setSubsystem(subsystem);
                tmp.setVehicleCode(vehicleCode);
                versionList.add(tmp);
            }
        }
        if ("牵引系统粘着1/2".equals(versionList.get(0).getName())) {
            Map<String, List<MtrSoftwareVersionVO>> collect = versionList.stream().collect(Collectors.groupingBy(MtrSoftwareVersionVO::getLocation));
            versionList.clear();
            collect.forEach((location, items) -> {
                if ("Mp1".equals(location)) {
                    Map<String, String> signalVersionMap = items.stream().filter(item -> item.getSignalNameEn() != null)
                            .collect(Collectors.toMap(MtrSoftwareVersionVO::getSignalNameEn, MtrSoftwareVersionVO::getMaxversion));
                    if (MapUtils.isNotEmpty(signalVersionMap)) {
                        MtrSoftwareVersionVO tmp = new MtrSoftwareVersionVO();
                        tmp.setName(softwareName);
                        tmp.setLocation(location);
                        tmp.setSubsystem(subsystem);
                        tmp.setVehicleCode(vehicleCode);
                        tmp.setMaxversion((signalVersionMap.get("DCU21_IuiAdhSWRevision") == null ? "" : signalVersionMap.get("DCU21_IuiAdhSWRevision")) + "/" + (signalVersionMap.get("DCU22_IuiAdhSWRevision") == null ? "" : signalVersionMap.get("DCU22_IuiAdhSWRevision")));
                        versionList.add(tmp);
                    } else {
                        versionList.addAll(items);
                    }
                } else if ("Mp2".equals(location)) {
                    Map<String, String> signalVersionMap = items.stream().filter(item -> item.getSignalNameEn() != null)
                            .collect(Collectors.toMap(MtrSoftwareVersionVO::getSignalNameEn, MtrSoftwareVersionVO::getMaxversion));
                    if (MapUtils.isNotEmpty(signalVersionMap)) {
                        MtrSoftwareVersionVO tmp = new MtrSoftwareVersionVO();
                        tmp.setName(softwareName);
                        tmp.setLocation(location);
                        tmp.setSubsystem(subsystem);
                        tmp.setVehicleCode(vehicleCode);
                        tmp.setMaxversion((signalVersionMap.get("DCU31_IuiAdhSWRevision") == null ? "" : signalVersionMap.get("DCU31_IuiAdhSWRevision")) + "/" + (signalVersionMap.get("DCU32_IuiAdhSWRevision") == null ? "" : signalVersionMap.get("DCU32_IuiAdhSWRevision")));
                        versionList.add(tmp);
                    } else {
                        versionList.addAll(items);
                    }
                } else {
                    versionList.addAll(items);
                }
            });
        }
        if ("牵引系统逻辑1/2".equals(versionList.get(0).getName())) {
            Map<String, List<MtrSoftwareVersionVO>> collect = versionList.stream().collect(Collectors.groupingBy(MtrSoftwareVersionVO::getLocation));
            versionList.clear();
            collect.forEach((location, items) -> {
                if ("Mp1".equals(location)) {
                    Map<String, String> signalVersionMap = items.stream().filter(item -> item.getSignalNameEn() != null)
                            .collect(Collectors.toMap(MtrSoftwareVersionVO::getSignalNameEn, MtrSoftwareVersionVO::getMaxversion));
                    if (MapUtils.isNotEmpty(signalVersionMap)) {
                        MtrSoftwareVersionVO tmp = new MtrSoftwareVersionVO();
                        tmp.setName(softwareName);
                        tmp.setLocation(location);
                        tmp.setSubsystem(subsystem);
                        tmp.setVehicleCode(vehicleCode);
                        tmp.setMaxversion((signalVersionMap.get("DCU21_IuiLglSWRevision") == null ? "" : signalVersionMap.get("DCU21_IuiLglSWRevision")) + "/" + (signalVersionMap.get("DCU22_IuiLglSWRevision") == null ? "" : signalVersionMap.get("DCU22_IuiLglSWRevision")));
                        versionList.add(tmp);
                    } else {
                        versionList.addAll(items);
                    }
                } else if ("Mp2".equals(location)) {
                    Map<String, String> signalVersionMap = items.stream().filter(item -> item.getSignalNameEn() != null)
                            .collect(Collectors.toMap(MtrSoftwareVersionVO::getSignalNameEn, MtrSoftwareVersionVO::getMaxversion));
                    if (MapUtils.isNotEmpty(signalVersionMap)) {
                        MtrSoftwareVersionVO tmp = new MtrSoftwareVersionVO();
                        tmp.setName(softwareName);
                        tmp.setLocation(location);
                        tmp.setSubsystem(subsystem);
                        tmp.setVehicleCode(vehicleCode);
                        tmp.setMaxversion((signalVersionMap.get("DCU31_IuiLglSWRevision") == null ? "" : signalVersionMap.get("DCU31_IuiLglSWRevision")) + "/" + (signalVersionMap.get("DCU32_IuiLglSWRevision") == null ? "" : signalVersionMap.get("DCU32_IuiLglSWRevision")));
                        versionList.add(tmp);
                    } else {
                        versionList.addAll(items);
                    }
                } else {
                    versionList.addAll(items);
                }
            });
        }
        if ("牵引系统逆变1/2".equals(versionList.get(0).getName())) {
            Map<String, List<MtrSoftwareVersionVO>> collect = versionList.stream().collect(Collectors.groupingBy(MtrSoftwareVersionVO::getLocation));
            versionList.clear();
            collect.forEach((location, items) -> {
                if ("Mp1".equals(location)) {
                    Map<String, String> signalVersionMap = items.stream().filter(item -> item.getSignalNameEn() != null)
                            .collect(Collectors.toMap(MtrSoftwareVersionVO::getSignalNameEn, MtrSoftwareVersionVO::getMaxversion));
                    if (MapUtils.isNotEmpty(signalVersionMap)) {
                        MtrSoftwareVersionVO tmp = new MtrSoftwareVersionVO();
                        tmp.setName(softwareName);
                        tmp.setLocation(location);
                        tmp.setSubsystem(subsystem);
                        tmp.setVehicleCode(vehicleCode);
                        tmp.setMaxversion((signalVersionMap.get("DCU21_IuiInvSWRevision") == null ? "" : signalVersionMap.get("DCU21_IuiInvSWRevision")) + "/" + (signalVersionMap.get("DCU22_IuiInvSWRevision") == null ? "" : signalVersionMap.get("DCU22_IuiInvSWRevision")));
                        versionList.add(tmp);
                    } else {
                        versionList.addAll(items);
                    }
                } else if ("Mp2".equals(location)) {
                    Map<String, String> signalVersionMap = items.stream().filter(item -> item.getSignalNameEn() != null)
                            .collect(Collectors.toMap(MtrSoftwareVersionVO::getSignalNameEn, MtrSoftwareVersionVO::getMaxversion));
                    if (MapUtils.isNotEmpty(signalVersionMap)) {
                        MtrSoftwareVersionVO tmp = new MtrSoftwareVersionVO();
                        tmp.setName(softwareName);
                        tmp.setLocation(location);
                        tmp.setSubsystem(subsystem);
                        tmp.setVehicleCode(vehicleCode);
                        tmp.setMaxversion((signalVersionMap.get("DCU31_IuiInvSWRevision") == null ? "" : signalVersionMap.get("DCU31_IuiInvSWRevision")) + "/" + (signalVersionMap.get("DCU32_IuiInvSWRevision") == null ? "" : signalVersionMap.get("DCU32_IuiInvSWRevision")));
                        versionList.add(tmp);
                    } else {
                        versionList.addAll(items);
                    }
                } else {
                    versionList.addAll(items);
                }
            });
        }
        if (!DOR_SUBSYSTEM.equalsIgnoreCase(subsystem)) {
            versionList.forEach(x -> {
                List<MtrSoftwareDorVersionVO> dorVersionVOList = new LinkedList<>();
                dorVersionVOList.add(new MtrSoftwareDorVersionVO(x.getName(), x.getMaxversion()));
                x.setDorInfoList(dorVersionVOList);
            });
        }
        return versionList;
    }

    private List<MtrSoftwareVersionVO> proceedDorList(String vehicleCode) {
        List<MtrSoftwareVersionVO> versionList = softWareMapping.ListSoftwareVersionByLocation(vehicleCode, DOR_SUBSYSTEM, null);
        Map<String, List<MtrSoftwareVersionVO>> locationGroup = versionList.stream()
                .collect(Collectors.groupingBy(x -> x.getLocation() + "_" + x.getVehicleCode()));
        // 每个车厢有8个车门，显示的时候简单相加返回
        AtomicInteger i = new AtomicInteger(0);
        List<MtrSoftwareVersionVO> result = new LinkedList<>();
        locationGroup.forEach((location, item) -> {
            List<MtrSoftwareDorVersionVO> dorVersionVOList = new LinkedList<>();
            AtomicReference<String> versionStr = new AtomicReference<>("");
            item = adjustDorSeq(item, i.get());
            item.forEach(x -> {
                dorVersionVOList.add(new MtrSoftwareDorVersionVO(x.getName(), x.getMaxversion()));
                x.setName(DOR_SUBSYSTEM_NAME);
                versionStr.set(String.join(" ", versionStr.get(), x.getMaxversion()));
                if (i.get() == TRAIN_LOCATION_NUMBER) {
                    versionStr.set(versionStr.get() + "<br/>");
                }
            });
            i.getAndIncrement();
            item.get(0).setMaxversion(versionStr.get().trim());
            item.get(0).setDorInfoList(dorVersionVOList);
            result.add(item.get(0));
        });
        return result;
    }

    public List<MtrSoftwareVersionVO> listVehicleHistoryVersion(String vehicleCode, String subsystem, String softwareName, String location) {
        return softWareMapping.listVehicleHistoryVersion(vehicleCode, subsystem, softwareName, location);
    }

    /**
     * 版本比较器
     *
     * @param x1
     * @param x2
     * @return
     */
    public int myComparator(MtrSoftwareVersionVO x1, MtrSoftwareVersionVO x2) {
        // 版本比较
        //把版本号按照小数点分割
        String[] versionsA = x1.getMaxversion().replace(" ", "").replace("\n", "").split("\\.");
        String[] versionsB = x2.getMaxversion().replace(" ", "").replace("\n", "").split("\\.");
        //找出两者之间长度最小的那个版本
        int times = Math.min(versionsA.length, versionsB.length);
        //限制遍历的次数不超过最小的数组长度
        for (int i = 0; i < times; i++) {
            float i1, i2;
            //如果没遍历到最后一位，就把当前位和下一位组成一个浮点数
            if (i == versionsA.length - 1) {
                i1 = Float.parseFloat(versionsA[i]);
            } else {
                i1 = Float.parseFloat(versionsA[i] + "." + versionsA[i + 1]);
            }
            if (i == versionsB.length - 1) {
                i2 = Float.parseFloat(versionsB[i]);
            } else {
                i2 = Float.parseFloat(versionsB[i] + "." + versionsB[i + 1]);
            }
            if (i1 > i2) {
                return 1;
            } else if (i1 < i2) {
                return -1;
            }
        }
        return 0;
    }

    /**
     * 当存在不同车厢的版本号需要判断是否变红，且保留最大的
     *
     * @param versionList
     * @return
     */
    private MtrSoftwareVersionVO proceedVehicleList(List<MtrSoftwareVersionVO> versionList) {
        // 存在的多个版本，判断是否变红，取出最大版本
        MtrSoftwareVersionVO mtrSoftwareVersionVO = versionList.stream().max(this::myComparator).get();
        // 判断是否变红
        Optional<MtrSoftwareVersionVO> any = versionList.stream().filter(x -> !x.getMaxversion().equals(mtrSoftwareVersionVO.getMaxversion())).findAny();
        if (any.isPresent()) {
            mtrSoftwareVersionVO.setIsRed("1");
        }
        return mtrSoftwareVersionVO;
    }

    // 判断是否存在
    private MtrSoftwareVersionVO isExist(List<MtrSoftwareVersionVO> vehicleList, String vehicleName, String softwareName) {
        Optional<MtrSoftwareVersionVO> any = vehicleList.stream().filter(x -> x.getVehicleCode().equals(vehicleName) && x.getName().equals(softwareName)).findAny();
        if (any.isPresent()) {
            return any.get();
        }
        return null;
    }

    // ExportUtil里的方法不管用 自定义
    private void ExportMyList(List<Map<String, Object>> data, List<MtrVehiclePO> vehicleList, HttpServletResponse response) {
        List<ExcelExportEntity> keyList = new LinkedList<>();
        keyList.add(new ExcelExportEntity("", "name"));
        vehicleList.forEach(
                o -> keyList.add(new ExcelExportEntity(o.getVehicleCode(), o.getVehicleCode()))
        );
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), keyList, data);
        try {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("车辆系统软件版本.xls", "UTF-8"));
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public void exportVehicleVersion(HttpServletRequest request, HttpServletResponse response) {
        // 准备数据
        List<Map<String, Object>> res = new LinkedList<>();
        // 获取已有数据接口
        JSONObject jsonObject = ListAllSoftwareVersion();
        JSONArray basicList = (JSONArray) jsonObject.get("basicList");
        List<MtrVehiclePO> vehicleList = (List) jsonObject.get("carList");
        basicList.forEach(x -> {
            String name = (String) ((JSONObject) x).get("nameCn");
            List vehicleSWVerList = (List) ((JSONObject) x).get("list");
            Map<String, Object> tmp = new LinkedHashMap<>();
            tmp.put("name", name);
            vehicleList.forEach(
                    o -> tmp.put(o.getVehicleCode(), getVersionByVehicleCode(vehicleSWVerList, o.getVehicleCode()))
            );
            res.add(tmp);
        });
        ExportMyList(res, vehicleList, response);
    }

    private String getVersionByVehicleCode(List<MtrSoftwareVersionVO> vehicleList, String ind) {
        Optional<MtrSoftwareVersionVO> first = vehicleList.stream().filter(x -> x.getVehicleCode().equals(ind)).findFirst();
        if (first.isPresent()) {
            return first.get().getMaxversion();
        }
        return null;
    }


    public Object listManualSoftware() {
        List<MtrSoftwareVersionVO> versionList = new ArrayList<>();
        try {
            List<SysDictVO> list = CacheUtils.getValue(DictCache.class, String.join("_", DictCache.SOFTWARE_MANUAL_FLAG, CacheUtils.METRO_LINE_ID, CacheUtils.VEHICLE_TYPE_ID));
            String value = list.stream()
                    .map(SysDictVO::getValue)
                    .findFirst().orElse("0");

            if (AUTO_FLAG.equals(value)) {
                return versionList;
            } else {
                versionList = softWareMapping.listManualSoftware();
            }
        } catch (Exception e) {
            logger.error("method[listManualSoftware] 异常 error {}", e.getMessage());
        }
        return versionList;
    }

}
