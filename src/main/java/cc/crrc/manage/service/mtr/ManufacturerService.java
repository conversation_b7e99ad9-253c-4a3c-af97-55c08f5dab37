package cc.crrc.manage.service.mtr;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.component.ComponentTypeMapping;
import cc.crrc.manage.mapper.mtr.ManufacturerFileMapping;
import cc.crrc.manage.mapper.mtr.ManufacturerMapping;
import cc.crrc.manage.mapper.mtr.MtrVehicleTypeMapping;
import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.mtr.ManufacturerDTO;
import cc.crrc.manage.pojo.mtr.ManufacturerEmployeeVO;
import cc.crrc.manage.pojo.mtr.ManufacturerPO;
import cc.crrc.manage.service.SysFileService;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 制造商雇员管理服务层
 *
 * <AUTHOR> GuoYang
 * 2019/11/11
 **/
@Service
public class ManufacturerService {
    private final Logger logger = LoggerFactory.getLogger(ManufacturerService.class);

    @Autowired
    private ManufacturerService manufacturerService;
    @Autowired
    private ManufacturerEmployeeService manufacturerEmployeeService;
    @Autowired
    private SysFileService sysFileService;
    @Autowired
    private ManufacturerMapping manufacturerMapping;
    @Autowired
    private ManufacturerFileMapping manufacturerFileMapping;
    @Autowired
    private ComponentTypeMapping componentTypeMapping;
    @Autowired
    private MtrVehicleTypeMapping vehicleTypeMapping;

    /**
     * 添加制造商
     *
     * @param manufacturerPO 待添加的制造商信息
     * <AUTHOR> GuoYang
     * 2019/11/25
     **/
    @Transactional(rollbackFor = Exception.class)
    public void addManufacturer(ManufacturerPO manufacturerPO) {
        try {
            // 添加创建人信息
            String currentId = UserUtils.getUserId();
            manufacturerPO.setCreateBy(currentId);
            manufacturerPO.setModifyBy(currentId);
            manufacturerPO.setId(PrimaryKeyGenerator.generatorId());
            // 保存制造商信息至数据库
            int successCount = manufacturerMapping.addManufacturer(manufacturerPO);
            if (successCount != 1) {
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
            }
        } catch (DataAccessException e) {
            logger.error("Method[addManufacturer] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * 根据名称和地址模糊分页查询制造商
     *
     * @param manufacturerDTO 待查询的制造商名称、地址以及分页信息
     * <AUTHOR> GuoYang
     * 2019/11/25
     **/
    public PageInfo<ManufacturerPO> listManufacturer(ManufacturerDTO manufacturerDTO) {
        try {
            // 分页，未传分页信息，默认不分页。
            int currentPage = manufacturerDTO.getPageNumber();
            int pageSize = manufacturerDTO.getPageSize();
            PageHelper.startPage(currentPage, pageSize);
            // 查询数据表
            List<ManufacturerPO> manufacturerPOList = manufacturerMapping.listManufacturer(manufacturerDTO);
            return new PageInfo<>(manufacturerPOList);
        } catch (DataAccessException e) {
            logger.error("Method[listManufacturer] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 根据制造商id查询制造商信息
     *
     * @param id 制造商id
     * <AUTHOR> GuoYang
     * 2019/11/25
     **/
    public ManufacturerPO getManufacturerById(String id) {
        try {
            return manufacturerMapping.getManufacturerById(id);
        } catch (DataAccessException e) {
            logger.error("Method[getManufacturerById] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 根据制造商id删除制造商
     * 需要其下的雇员，无对外关系方可删除。
     * 若雇员有对外关联，则需要先将雇员进行手动删除或解除关系，再删除制造商。
     *
     * @param id 制造商id
     * <AUTHOR> GuoYang
     * 2019/11/25
     **/
    @Transactional(rollbackFor = Exception.class)
    public void deleteManufacturerById(String id) {
        try {
            // 查询并校验制造商下的雇员，是否有对外关联。
            List<ManufacturerEmployeeVO> manufacturerEmployeeVOList = manufacturerEmployeeService.listManufacturerEmployeeByManufacturerId(id);
            boolean hasRelation = false;
            if(!manufacturerEmployeeVOList.isEmpty()){
                 hasRelation = isEmployeeHasRelation(manufacturerEmployeeVOList);
            }

            if (hasRelation) {
                throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION.getErrorCode(), "请先删除下属雇员，防止误删。");
            }
            String currentId = UserUtils.getUserId();
            // 删除制造商下属雇员
            manufacturerEmployeeService.deleteManufacturerEmployeeByManufacturerId(id);
            // 解除待删除制造商与其下属部件类型之间的关系
            componentTypeMapping.clearManufacturerFromComponentType(id, currentId);
            // 解除待删除制造商与其下属车辆类型之间的关系
            vehicleTypeMapping.clearManufacturerFromVehicleType(id, currentId);
            // 清除制造商下的文件
            clearManufacturerFiles(id);
            // 删除制造商信息
            Integer successCount = manufacturerMapping.deleteManufacturerById(id);
            if (successCount != 1) {
                throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
            }
        } catch (DataAccessException e) {
            logger.error("Method[deleteManufacturerById] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    /**
     * 更新制造商信息
     *
     * @param manufacturerPO 制造商信息
     * <AUTHOR> GuoYang
     * 2019/11/25
     **/
    @Transactional(rollbackFor = Exception.class)
    public void updateManufacturer(ManufacturerPO manufacturerPO) {
        try {
            manufacturerPO.setModifyBy(UserUtils.getUserId());
            Integer successCount = manufacturerMapping.updateManufacturer(manufacturerPO);
            if (successCount != 1) {
                throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
            }
        } catch (DataAccessException e) {
            logger.error("Method[updateManufacturer] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    /**
     * 保存制造商相关文件
     *
     * @param sysFile 文件信息
     * @param manufacturerId 相关联的制造商id
     * <AUTHOR> GuoYang
     * 2019/11/25
     **/
    @Transactional(rollbackFor = Exception.class)
    public void saveManufacturerFile(SysFilePO sysFile, String manufacturerId) {
        try {
            // 保存文件信息
            String fileId = sysFileService.addSysFile(sysFile);
            // 保存文件和制造商关联关系
            Integer successCount = manufacturerFileMapping.addManufacturerFile(fileId, manufacturerId);
            if (successCount != 1) {
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
            }
        } catch (DataAccessException e) {
            logger.error("Method[saveManufacturerFile] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * 删除制造商下指定文件id的文件
     *
     * @param fileId         文件id
     * @param manufacturerId 制造商id
     * <AUTHOR> GuoYang
     * 2019/11/25
     **/
    @Transactional(rollbackFor = Exception.class)
    public void deleteManufacturerFile(String fileId, String manufacturerId) {
        try {
            // 删除关联表
            manufacturerFileMapping.deleteManufacturerFileByFileId(fileId, manufacturerId);
            // 删除文件信息表
            sysFileService.deleteSysFile(fileId);
        } catch (DataAccessException e) {
            logger.error("Method[deleteManufacturerFile] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    /**
     * 删除制造商下的全部文件
     *
     * @param manufacturerId 制造商id
     * <AUTHOR> GuoYang
     * 2019/12/5
     **/
    @Transactional(rollbackFor = Exception.class)
    public void clearManufacturerFiles(String manufacturerId) {
        try {
            List<SysFilePO> sysFilePOList = getFilesByManufacturerId(manufacturerId);
            for (SysFilePO sysFilePO: sysFilePOList) {
                deleteManufacturerFile(sysFilePO.getId(),manufacturerId);
            }
        } catch (DataAccessException e) {
            logger.error("Method[clearManufacturerFiles] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    /**
     * 批量删除制造商
     *
     * @param idList         id数组，用逗号分割，如 3,4
     * <AUTHOR> GuoYang
     * 2019/11/25
     **/
    public void batchDeleteManufacturer(String idList) {
        // 分割字符串至数组
        String[] idArray = idList.split(",");
        // 用于保存批量删除失败的错误信息
        List<HashMap<String, String>> errors = new ArrayList<>();
        try {
            for (String id : idArray) {
                try {// 删除制造商
                    manufacturerService.deleteManufacturerById(id);
                } catch (RestApiException e) {
                    // 异常处理为保存错误信息，批量删除后，一并返回。
                    HashMap<String, String> errorInfo = new HashMap<>();
                    errorInfo.put("id", id);
                    errorInfo.put("errorMessage", e.getMessage());
                    errors.add(errorInfo);
                }
            }
            if (!errors.isEmpty()) {
                JSONObject res = new JSONObject();
                res.put("totalCount", idArray.length);
                res.put("failedCount", errors.size());
                res.put("errorInfo", errors);
                throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION.getErrorCode(), res.toString());
            }
        } catch (DataAccessException e) {
            logger.error("Method[batchDeleteManufacturer] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    /**
     * 查看制造商下的所有文件信息
     *
     * @param manufacturerId 制造商id
     * <AUTHOR> GuoYang
     * 2019/11/25
     **/
    public List<SysFilePO> getFilesByManufacturerId(String manufacturerId) {
        try {
            return manufacturerFileMapping.getFilesByManufacturerId(manufacturerId);
        } catch (DataAccessException e) {
            logger.error("Method[getFilesByManufacturerId] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 检查雇员是否与车辆类型或部件类型相关联。若有关，则返回true;无关，返回false。
     *
     * @param manufacturerEmployeeVOList 雇员列表
     * @return boolean
     * <AUTHOR> GuoYang
     * 2019/11/25
     **/
    private boolean isEmployeeHasRelation(List<ManufacturerEmployeeVO> manufacturerEmployeeVOList) {
        for (ManufacturerEmployeeVO employee : manufacturerEmployeeVOList) {
            // 检查雇员是否与车辆类型相关联
            List<Long> vehicleTypeIdList = vehicleTypeMapping.getVehicleTypeIdByEmployeeId(employee.getId());
            if (!vehicleTypeIdList.isEmpty()) {
                return true;
            }
            // 检查雇员是否与部件类型相关联
            List<String> componentTypeIdList = componentTypeMapping.getComponentTypeIdByEmployeeId(employee.getId());
            if (!componentTypeIdList.isEmpty()) {
                return true;
            }
        }
        return false;
    }

}
