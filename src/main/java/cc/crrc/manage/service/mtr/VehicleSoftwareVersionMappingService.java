package cc.crrc.manage.service.mtr;

import cc.crrc.manage.common.annotation.ParamReplace;
import cc.crrc.manage.common.aop.ParamHandler;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.Page;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.mtr.MtrSoftwareVersionMappingMapping;
import cc.crrc.manage.monitoringConfig.dao.MonitorConfigSignalDao;
import cc.crrc.manage.monitoringConfig.entity.MonitorConfigWtdSignalPO;
import cc.crrc.manage.monitoringConfig.service.MonitorConfigSignalService;
import cc.crrc.manage.pojo.mtr.MtrSoftwareVersionMappingVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * @Date 2021/11/8
 * <AUTHOR>
 * @Description 软件版本映射服务
 */
@Service
public class VehicleSoftwareVersionMappingService {
    private final Logger logger = LoggerFactory.getLogger(VehicleSoftwareVersionMappingService.class);

    @Autowired
    MtrSoftwareVersionMappingMapping mapping;

    @Autowired
    private MonitorConfigSignalDao monitorConfigSignalDao;


    /**
     * | 互转 ,
     *
     * @param s
     * @return
     */
    private String convertBtCommaAndSplit(String s) {
        if (s.contains("|"))
            return s.replace("|", ",");
        else return s.replace(",", "|");
    }

    /**
     * 长度检测
     *
     * @param s
     * @return true无问题 false不通过
     */
    private boolean lengthCheck(MtrSoftwareVersionMappingVO s) {
        return s.getSignalNameEn().length() < 255 && s.getName().length() < 255 && s.getSignalList().length() < 255;
    }

    /**
     * 新增版本信息
     *
     * @param softwareVO
     * @return
     */
    public int addSoftwareVersion(MtrSoftwareVersionMappingVO softwareVO) {
        // 长度检测
        if (!lengthCheck(softwareVO)) throw new RestApiException(ExceptionInfoEnum.DATA_ADD_LENGTH_BEYOND_LIMIT);
        // 校验是否存在同名的信号
        String id = mapping.getIdByEnName(softwareVO.getSignalNameEn());
        if (id != null && !id.equals("")) throw new RestApiException(ExceptionInfoEnum.DATA_ADD_EXCEPTION);
        softwareVO.setId(PrimaryKeyGenerator.generatorId());
        softwareVO.setCreateBy(UserUtils.getUserId());
        softwareVO.setCreateTime(new Date());
        softwareVO.setModifyTime(new Date());
        softwareVO.setModifyBy(UserUtils.getUserId());
        softwareVO.setSignalList(convertBtCommaAndSplit(softwareVO.getSignalList()));
        return mapping.insertSelective(softwareVO);
    }

    /**
     * 删除版本信息 - 软删除
     *
     * @param id 主键id
     * @return
     */
    public int deleteSoftwareVersionById(String id) {
        MtrSoftwareVersionMappingVO deleteVo = new MtrSoftwareVersionMappingVO();
        deleteVo.setModifyTime(new Date());
        deleteVo.setModifyBy(UserUtils.getUserId());
        deleteVo.setId(id);
        deleteVo.setDelFlag((short) 1);
        return mapping.updateByPrimaryKeySelective(deleteVo);
    }

    /**
     * 更新版本信息，如果字段为null不更新，主键id必填
     *
     * @param softwareVO
     * @return
     */
    public int updateSoftwareVersion(MtrSoftwareVersionMappingVO softwareVO) {
        // 长度检测
        if (!lengthCheck(softwareVO)) throw new RestApiException(ExceptionInfoEnum.DATA_ADD_LENGTH_BEYOND_LIMIT);
        // 校验是否存在同名的信号
        String id = mapping.getIdByEnName(softwareVO.getSignalNameEn());
        if (id != null && !"".equals(id) && !softwareVO.getId().equals(id))
            throw new RestApiException(ExceptionInfoEnum.DATA_ADD_EXCEPTION);
        softwareVO.setModifyTime(new Date());
        softwareVO.setModifyBy(UserUtils.getUserId());
        softwareVO.setSignalList(convertBtCommaAndSplit(softwareVO.getSignalList()));
        return mapping.updateByPrimaryKeySelective(softwareVO);
    }

    /**
     * 通过id查询版本信息
     *
     * @param id
     * @return
     */
    public MtrSoftwareVersionMappingVO selectById(String id) {
        return mapping.selectByPrimaryKey(id);
    }


    @ParamReplace(param = {"name", "signalNameEn", "structureName", "vehicleTypeName", "lineName"}, type = MtrSoftwareVersionMappingVO.class)
    public PageInfo<MtrSoftwareVersionMappingVO> selectByMultiCondition(MtrSoftwareVersionMappingVO softwareVO) {
        try {
            PageHelper.startPage(softwareVO.getPageNumber(), softwareVO.getPageSize());
            List<MtrSoftwareVersionMappingVO> res = mapping.selectByMultiCondition(softwareVO);
            res.forEach(x -> {
                x.setSignalList(convertBtCommaAndSplit(x.getSignalList()));
            });
            return new PageInfo<>(res);
        } catch (Exception e) {
            logger.error("Method[VehicleSoftwareVersionList] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }

    }

    /**
     * @Description 模糊查询信号变量名
     * @Param inputName 项点名称或者输入的中文名称
     * @Return List<WtdSignalPO> 信号集合
     */
    public Object dropDownSelect(String vehicleType, String inputName) {
        List<MonitorConfigWtdSignalPO> wtdSignalPOList;
        try {
            String protocolId = monitorConfigSignalDao.selectProtocolIdByVehicleType(vehicleType);
            if (null == protocolId) {
                return null;
            }
            wtdSignalPOList = monitorConfigSignalDao.findLikeWtdSignalByInputName(inputName, protocolId);
        } catch (Exception e) {
            logger.error("findLikeWtdSignalByInputName:信号变量名称查询失败");
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "数据查询错误！");
        }
        return wtdSignalPOList;
    }


}
