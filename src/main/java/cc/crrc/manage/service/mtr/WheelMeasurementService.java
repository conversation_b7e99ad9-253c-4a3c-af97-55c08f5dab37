package cc.crrc.manage.service.mtr;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.LoggerUtils;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.mtr.WheelMeasurementMapping;
import cc.crrc.manage.pojo.mtr.WheelMeasurementDataPO;
import cc.crrc.manage.pojo.mtr.WheelMeasurementDataVO;
import cc.crrc.manage.pojo.mtr.WheelMeasurementPO;
import cc.crrc.manage.pojo.mtr.WheelMeasurementVO;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName WheelMeasurementService
 * <AUTHOR> yongqing
 * @Date 2022/3/15 11:39
 * @Version 1.0
 */
@Service
public class WheelMeasurementService {
    private final Logger logger = LoggerFactory.getLogger(WheelMeasurementService.class);
    @Resource
    WheelMeasurementMapping wheelMeasurementMapping;

    /**
     * @Description 获取车辆测量信息
     * @param wheelMeasurementPO
     * @return
     */
    public PageInfo<WheelMeasurementVO> getVehicle(WheelMeasurementPO wheelMeasurementPO) {
        List<WheelMeasurementVO> wheelMeasurementVOS = null;
        try {
            int pageNumber = wheelMeasurementPO.getPageNumber();
            int pageSize = wheelMeasurementPO.getPageSize();
            PageHelper.startPage(pageNumber, pageSize);
            wheelMeasurementVOS = wheelMeasurementMapping.queryVehicle(wheelMeasurementPO);
        } catch (Exception e) {
            LoggerUtils.error(logger, e);
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
        return new PageInfo<>(wheelMeasurementVOS);
    }

    /**
     * @param measurementVehicleId
     * @return
     * @Description 获取测量数据
     */
    public List<WheelMeasurementDataVO> getMeasurementData(String measurementVehicleId) {
        List<WheelMeasurementDataVO> wheelMeasurementDataVO = null;
        try {
            wheelMeasurementDataVO = wheelMeasurementMapping.queryData(measurementVehicleId);
        } catch (Exception e) {
            LoggerUtils.error(logger, e);
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
        return wheelMeasurementDataVO;
    }

    /**
     * @param wheelMeasurementPO
     * @return
     * @Description 保存测量数据
     */
    @Transactional(rollbackFor = Exception.class)
    public Object saveWheelData(WheelMeasurementPO wheelMeasurementPO) {
        try {
            wheelMeasurementPO.setId(PrimaryKeyGenerator.generatorId());
            wheelMeasurementPO.setCreateBy(UserUtils.getUserId().toString());
            //新增记录表
            wheelMeasurementMapping.saveVehicle(wheelMeasurementPO);
            for (WheelMeasurementDataPO data : wheelMeasurementPO.getTableData()) {
                data.setId(PrimaryKeyGenerator.generatorId());
                //获取记录表id保存进数据表，作为关联字段
                data.setMeasurementVehicleId(wheelMeasurementPO.getId());
                wheelMeasurementMapping.saveData(data);
            }
        } catch (Exception e) {
            LoggerUtils.error(logger, e);
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
        return wheelMeasurementPO.getId();
    }

    /**
     * @param wheelMeasurementPO
     * @return
     * @Description 修改测量数据
     */
    @Transactional(rollbackFor = Exception.class)
    public Object changeWheelData(WheelMeasurementPO wheelMeasurementPO) {
        try {
            wheelMeasurementPO.setModifyBy(UserUtils.getUserId().toString());
            //更新记录数据
            wheelMeasurementMapping.updateVehicle(wheelMeasurementPO);
            //删除该条记录的详细测量数据
            wheelMeasurementMapping.deleteData(wheelMeasurementPO.getId());
            for (WheelMeasurementDataPO date : wheelMeasurementPO.getTableData()) {
                date.setId(PrimaryKeyGenerator.generatorId());
                date.setMeasurementVehicleId(wheelMeasurementPO.getId());
                //重新插入详细测量数据
                wheelMeasurementMapping.saveData(date);
            }
        } catch (Exception e) {
            LoggerUtils.error(logger, e);
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
        return "success";
    }

    /**
     * @return
     * @Description 逻辑删除，标志置"1"
     * @Param 测量车辆表主键id
     */
    public Object deleteWheelData(String id) {
        JSONObject jsonObject = new JSONObject();
        try {
            int i = wheelMeasurementMapping.deleteVehicle(id);
            if (i == 1) {
                jsonObject.put("result", "删除成功");
                return jsonObject;
            } else {
                jsonObject.put("result", "删除成功");
            }
        } catch (Exception e) {
            LoggerUtils.error(logger, e);
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
        return jsonObject;
    }
}
