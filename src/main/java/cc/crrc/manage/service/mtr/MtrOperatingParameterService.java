package cc.crrc.manage.service.mtr;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.mtr.MtrOperatingParameterMapping;
import cc.crrc.manage.pojo.component.OperatingParameterDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class MtrOperatingParameterService {
    private final Logger logger = LoggerFactory.getLogger(MtrOperatingParameterService.class);
    @Autowired
    private MtrOperatingParameterMapping mtrOperatingParameterMapping;

    public  List<OperatingParameterDTO> getOperatingParam(String componentId, String vehicleId) {
        return mtrOperatingParameterMapping.getOperatingParam(componentId, vehicleId);
    }

    @Transactional
    public Object insertOperatingParam(OperatingParameterDTO operatingParam) {
        checkParamDateValid(operatingParam);
        operatingParam.setId(PrimaryKeyGenerator.generatorId());
        operatingParam.setCreateBy(UserUtils.getUser().getId());
        int result = mtrOperatingParameterMapping.insertOperatingParam(operatingParam);
        if (result > 0)
            return "SUCCESS";
        throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
    }

    @Transactional
    public Object updateOperatingParam(OperatingParameterDTO operatingParam) {
        checkParamDateValid(operatingParam);
        operatingParam.setModifyBy(UserUtils.getUser().getId());
        int result = mtrOperatingParameterMapping.updateOperatingParam(operatingParam);
        if (result > 0)
            return "SUCCESS";
        throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
    }

    @Transactional
    public Object deleteOperatingParam(String id) {
        int result = mtrOperatingParameterMapping.deleteOperatingParam(id);
        if (result > 0)
            return "SUCCESS";
        throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
    }

    @Transactional
    public void clearOperatingParam(String componentId,String vehicleId){
        try {
            List<OperatingParameterDTO> operatingParameterDTOList = getOperatingParam(componentId,vehicleId);
            for (OperatingParameterDTO operatingParameterDTO:operatingParameterDTOList) {
                deleteOperatingParam(operatingParameterDTO.getId());
            }
        }catch (DataAccessException e) {
            logger.error("Method[clearOperatingParam] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    private void checkParamDateValid(OperatingParameterDTO operatingParam) {
        boolean vehicleIsNull = operatingParam.getVehicleId() == null;
        boolean componentIsNull = operatingParam.getComponentId() == null;
        boolean bothNull = vehicleIsNull && componentIsNull;
        boolean bothHave = !(vehicleIsNull || componentIsNull);
        if (bothHave || bothNull)
            throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION);
    }

}
