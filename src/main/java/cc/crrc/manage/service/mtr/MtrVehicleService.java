package cc.crrc.manage.service.mtr;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.TreeUtil;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.common.utils.vehicle.VehicleInfoUtil;
import cc.crrc.manage.mapper.comm.ProtocolMapping;
import cc.crrc.manage.mapper.mtr.MtrVehicleMapping;
import cc.crrc.manage.mapper.mtr.StruVehicleStructureComponentMapping;
import cc.crrc.manage.mapper.mtr.VehicleFileMapping;
import cc.crrc.manage.mapper.stru.StruVehicleTypeStructureMapping;
import cc.crrc.manage.pojo.SysDictVO;
import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.comm.protocol.ProtocolMatchVO;
import cc.crrc.manage.pojo.comm.protocol.ProtocolVO;
import cc.crrc.manage.pojo.mtr.*;
import cc.crrc.manage.pojo.stru.VehicleStructureTreeVO;
import cc.crrc.manage.service.SysDictService;
import cc.crrc.manage.service.SysFileService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static cc.crrc.manage.common.utils.Constants.STRUCTURE_PICTURE_FILE_TYPE;

/**
 * 车辆管理服务层
 *
 * <AUTHOR> GuoYang
 * 2019/12/3
 **/
@Service
public class MtrVehicleService {
    private final Logger logger = LoggerFactory.getLogger(MtrVehicleService.class);
    private static final String TCP_TYPE = "TCP";
    private static final String MQTT_TYPE = "MQTT";

    @Autowired
    private MtrVehicleMapping mtrVehicleMapping;
    @Autowired
    private VehicleFileMapping vehicleFileMapping;
    @Autowired
    private ProtocolMapping protocolMapping;
    @Autowired
    private SysFileService sysFileService;
    @Autowired
    private MtrVehicleService mtrVehicleService;
    @Autowired
    private MtrVehicleTypeRelationService mtrVehicleTypeRelationService;
    @Autowired
    private MtrManufacturerParameterService mtrManufacturerParameterService;
    @Autowired
    private MtrOperatingParameterService mtrOperatingParameterService;
    @Autowired
    private StruVehicleTypeStructureMapping struVehicleTypeStructureMapping;
    @Autowired
    private StruVehicleStructureComponentService struVehicleStructureComponentService;
    @Autowired
    private StruVehicleStructureComponentMapping struVehicleStructureComponentMapping;
    @Autowired
    private SysDictService sysDictService;

    /**
     * 根据名称模糊查询，根据车辆编码、车辆类型id精确查询车辆信息。（可分页）
     *
     * @param mtrVehicleDTO 车辆信息查询条件
     * @return com.github.pagehelper.PageInfo<cc.crrc.manage.pojo.mtr.MtrVehicleVO>
     * <AUTHOR> GuoYang
     * 2019/12/2
     **/
    public PageInfo<MtrVehicleVO> listVehicleInfo(MtrVehicleDTO mtrVehicleDTO) {
        try {
            // 分页，未传分页信息，默认不分页。
            int currentPage = mtrVehicleDTO.getPageNumber();
            int pageSize = mtrVehicleDTO.getPageSize();
            PageHelper.startPage(currentPage, pageSize);
            // 查询数据表
            List<MtrVehicleVO> manufacturerPOList = mtrVehicleMapping.listVehicleInfo(mtrVehicleDTO);
            // 每辆车可能有多个协议，单独查询，放入数组一起返回
            for (MtrVehicleVO mtrVehicleVO : manufacturerPOList) {
                List<ProtocolVO> protocolVOList = protocolMapping.getValidProtocolByVehicleId(mtrVehicleVO.getId(), mtrVehicleVO.getCommType());
                mtrVehicleVO.setProtocolList(protocolVOList);
            }
            return new PageInfo<>(manufacturerPOList);
        } catch (DataAccessException e) {
            logger.error("Method[listVehicleInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 查询（线路下）全部车辆
     *
     * @param metroLineId 线路id
     * @return java.util.List<cc.crrc.manage.pojo.mtr.MtrVehiclePO>
     * <AUTHOR> GuoYang
     * 2019/12/2
     **/
    public List<MtrVehiclePO> listVehicle(String metroLineId) {
        try {
            List<MtrVehiclePO> vehicleList = mtrVehicleMapping.listVehicle(metroLineId);
            for (MtrVehiclePO vehiclePO : vehicleList){
                vehiclePO.setVehicleOnlineStatus(VehicleInfoUtil.getVehicleOnlineStatus(vehiclePO.getVehicleCode()));
            }
            return vehicleList;
        } catch (DataAccessException e) {
            logger.error("Method[listVehicle] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 查询车型下全部车辆
     * @param vehicleTypeId 车型id
     * @return 车辆list
     */
    public List<MtrVehiclePO> listVehicleByTypeId(String vehicleTypeId) {
        try {
            return mtrVehicleMapping.listVehicleByTypeId(vehicleTypeId);
        } catch (DataAccessException e) {
            logger.error("Method[listVehicleByTypeId] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 根据车辆id，查询带图片的车辆基本信息
     *
     * @param vehicleId 车辆id
     * @return cc.crrc.manage.pojo.mtr.MtrVehicleDetailVO
     * <AUTHOR> GuoYang
     * 2019/12/17
     **/
    public MtrVehicleDetailVO getVehicleById(String vehicleId){
        try {
            return mtrVehicleMapping.getVehicleById(vehicleId,STRUCTURE_PICTURE_FILE_TYPE);
        } catch (DataAccessException e) {
            logger.error("Method[getVehicleById] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }

    }

    /**
     * 添加车辆信息及其关联表（车型、协议）
     *
     * @param mtrVehiclePO  车辆信息
     * @param vehicleTypeId 车辆所属车型的id
     * <AUTHOR> GuoYang
     * 2019/12/5
     **/
    @Transactional(rollbackFor = Exception.class)
    public void addVehicle(MtrVehiclePO mtrVehiclePO, String vehicleTypeId) {
        try {
            // 校验生产和交车日期顺序
            checkProductionAndDeliveryDateOrder(mtrVehiclePO);
            // 添加主键
            mtrVehiclePO.setId(PrimaryKeyGenerator.generatorId());
            // 添加创建人信息
            String currentId = UserUtils.getUserId();
            mtrVehiclePO.setCreateBy(currentId);
            mtrVehiclePO.setModifyBy(currentId);
            // 保存车辆信息至数据库
            int addSuccess = mtrVehicleMapping.addVehicle(mtrVehiclePO);
            if (addSuccess != 1) {
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
            }
            String vehicleId = mtrVehiclePO.getId();
            // 保存车辆与车型关系
            mtrVehicleTypeRelationService.addRelation(vehicleId, vehicleTypeId);
            // 获取协议id，同一包类型下筛选出最新的一个
            List<ProtocolVO> protocolVOList = protocolMapping.getLatestProtocolByVehicleType(vehicleTypeId);
            // 添加车辆与协议的关联关系
            for (ProtocolVO protocolVO : protocolVOList) {
                addProtocolRelationWithVehicle(protocolVO, vehicleId, currentId);
            }
            // lixin 保存车辆档案
            struVehicleStructureComponentService.addVehicle(vehicleTypeId,vehicleId,currentId);
        } catch (DataAccessException e) {
            logger.error("Method[addVehicle] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * 批量删除车辆
     *
     * @param idList id数组，用逗号分割，如 3,4
     * <AUTHOR> GuoYang
     * 2019/12/5
     **/
    public void batchDeleteVehicle(String idList) {
        // 分割字符串至数组
        String[] idArray = idList.split(",");
        // 用于保存批量删除失败的错误信息
        List<HashMap<String, String>> errors = new ArrayList<>();
        try {
            for (String id : idArray) {
                try {
                    // 删除单个车辆
                    mtrVehicleService.deleteVehicle(id);
                } catch (RestApiException e) {
                    // 异常处理为保存错误信息，批量删除后，一并返回。
                    HashMap<String, String> errorInfo = new HashMap<>();
                    errorInfo.put("id", id);
                    errorInfo.put("errorMessage", e.getMessage());
                    errors.add(errorInfo);
                }
            }
            if (!errors.isEmpty()) {
                JSONObject res = new JSONObject();
                res.put("totalCount", idArray.length);
                res.put("failedCount", errors.size());
                res.put("errorInfo", errors);
                throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION.getErrorCode(), res.toString());
            }
        } catch (DataAccessException e) {
            logger.error("Method[batchDeleteVehicle] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    /**
     * 车辆信息及其相关关系删除
     *
     * @param vehicleId 车辆id
     * <AUTHOR> GuoYang
     * 2019/12/5
     **/
    @Transactional(rollbackFor = Exception.class)
    public void deleteVehicle(String vehicleId) {
        try {
            String currentId = UserUtils.getUserId();
            // 清除车辆下的协议信息
            MtrVehicleVO mtrVehicleVO = mtrVehicleMapping.getVehicleById(vehicleId,null);
            //String commType = mtrVehicleVO.getCommType();
            //if (commType != null) {
            List<ProtocolMatchVO> tempList = wrapProtocolMatchVOListWithVehicleId(null, vehicleId);
            protocolMapping.updateInvaidByPacket("0", tempList, currentId);
           // }
            // 逻辑解除车型与车辆的关系
            mtrVehicleTypeRelationService.invalidRelationByVehicleId(vehicleId);
            // 清除车辆下属所有文件信息
            clearVehicleFiles(vehicleId);
            // 清除车辆下的制造参数
            mtrManufacturerParameterService.clearManufParam(null, vehicleId);
            // 清除车辆下的运行参数
            mtrOperatingParameterService.clearOperatingParam(null, vehicleId);
            // 逻辑删除车辆信息
            Integer successCount = mtrVehicleMapping.deleteVehicle(vehicleId, currentId);
            // 逻辑删除车辆档案
            struVehicleStructureComponentService.deleteStruVehicleStructureComponent(vehicleId,currentId);
            if (successCount != 1) {
                throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
            }
        } catch (DataAccessException e) {
            logger.error("Method[deleteVehicle] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    /**
     * 更新车辆信息
     *
     * @param mtrVehiclePO  车辆信息
     * @param vehicleTypeId 车辆所属车型的id
     * <AUTHOR> GuoYang
     * 2019/12/5
     **/
    @Transactional(rollbackFor=Exception.class)
    public void updateVehicle(MtrVehiclePO mtrVehiclePO, String vehicleTypeId) {
        try {
            // 校验生产和交车日期顺序
            checkProductionAndDeliveryDateOrder(mtrVehiclePO);
            // 更新车辆信息
            mtrVehiclePO.setModifyBy(UserUtils.getUserId());
            Integer successCount = mtrVehicleMapping.updateVehicle(mtrVehiclePO);
            if (successCount != 1) {
                throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
            }
            // 校验、更新车辆与车型的关联
            MtrVehicleTypeRelationPO mtrVehicleTypeRelationPO = mtrVehicleTypeRelationService.getVehicleTypeById(mtrVehiclePO.getId());
            if (mtrVehicleTypeRelationPO == null) {
                throw new RestApiException(ExceptionInfoEnum.DATA_NOT_FOUND_EXCEPTION);
            }
            mtrVehicleTypeRelationService.changeVehicleRelation(mtrVehiclePO.getId(), vehicleTypeId);
        } catch (DataAccessException e) {
            logger.error("Method[updateVehicle] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    /**
     * 添加车辆关联文件
     *
     * @param sysFile   文件信息
     * @param vehicleId 车辆id
     * <AUTHOR> GuoYang
     * 2019/12/5
     **/
    @Transactional(rollbackFor=Exception.class)
    public void saveVehicleFile(SysFilePO sysFile, String vehicleId) {
        try {
            // 保存文件信息
            String fileId = sysFileService.addSysFile(sysFile);
            // 保存文件和车辆关联关系
            Integer successCount = vehicleFileMapping.addVehicleFile(fileId, vehicleId);
            if (successCount != 1) {
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
            }
        } catch (DataAccessException e) {
            logger.error("Method[saveVehicleFile] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * 根据文件id删除车辆关联文件
     *
     * @param fileId    文件id
     * @param vehicleId 车辆id
     * <AUTHOR> GuoYang
     * 2019/12/5
     **/
    @Transactional(rollbackFor=Exception.class)
    public void deleteVehicleFile(String fileId, String vehicleId) {
        try {
            // 删除关联表
            vehicleFileMapping.deleteVehicleIdFileByFileId(fileId, vehicleId);
            // 逻辑删除文件信息表
            sysFileService.deleteSysFile(fileId);
        } catch (DataAccessException e) {
            logger.error("Method[deleteVehicleFile] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    /**
     * 删除特定车辆下的全部文件
     *
     * @param vehicleId 车辆id
     * <AUTHOR> GuoYang
     * 2019/12/5
     **/
    @Transactional(rollbackFor=Exception.class)
    public void clearVehicleFiles(String vehicleId) {
        try {
            // 获取车辆下的全部文件
            List<SysFilePO> sysFilePOList = getFilesByVehicleId(vehicleId);
            for (SysFilePO sysFilePO : sysFilePOList) {
                // 对逐个文件进行删除
                deleteVehicleFile(sysFilePO.getId(), vehicleId);
            }
        } catch (DataAccessException e) {
            logger.error("Method[clearVehicleFiles] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    /**
     * 获取特定车辆所有关联文件
     *
     * @param vehicleId 车辆id
     * @return java.util.List<cc.crrc.manage.pojo.SysFilePO>
     * <AUTHOR> GuoYang
     * 2019/12/5
     **/
    public List<SysFilePO> getFilesByVehicleId(String vehicleId) {
        try {
            return vehicleFileMapping.getFilesByVehicleId(vehicleId);
        } catch (DataAccessException e) {
            logger.error("Method[getFilesByVehicleId] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 添加车辆与协议的关联信息
     *
     * @param protocolVO 协议信息
     * @param vehicleId  车辆id
     * @param userId     当前操作的用户id
     * <AUTHOR> GuoYang
     * 2019/12/5
     **/
    @Transactional(rollbackFor=Exception.class)
    public void addProtocolRelationWithVehicle(ProtocolVO protocolVO, String vehicleId, String userId) {
        try {
            //long packetTypeId = protocolVO.getPacketTypeId();
            //if (packetTypeId == 0) {
            //    return;
           // }
            List<ProtocolMatchVO> tempList = wrapProtocolMatchVOListWithVehicleId(null, vehicleId);
            // 判断协议是mqtt还是tcp，添加至相对应的关联表中
            //if (TCP_TYPE.equals(protocolVO.getCommType())) {
            protocolMapping.matchTcpVehicle(protocolVO.getId(), tempList, userId);
            /*} else if (MQTT_TYPE.equals(protocolVO.getCommType())) {
                protocolMapping.insertMQTTTopic(vehicleId, packetTypeId, userId);
                protocolMapping.matchMqttVehicle(protocolVO.getId(), packetTypeId, tempList, userId);
            }*/
        }catch (DataAccessException e) {
            logger.error("Method[addProtocolRelationWithVehicle] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * 将车辆id封装至list中
     *
     * @param vehicleId 车辆id
     * @return java.util.List<cc.crrc.manage.pojo.comm.protocol.ProtocolMatchVO>
     * <AUTHOR> GuoYang
     * 2019/12/6
     **/
    private List<ProtocolMatchVO> wrapProtocolMatchVOListWithVehicleId(List<ProtocolMatchVO> protocolMatchVOList, String vehicleId) {
        if (protocolMatchVOList == null) {
            protocolMatchVOList = new ArrayList<>();
        }
        ProtocolMatchVO protocolMatchVO = new ProtocolMatchVO();
        protocolMatchVO.setVehicleId(vehicleId);
        protocolMatchVOList.add(protocolMatchVO);
        return protocolMatchVOList;
    }

    /**
     * 查询单个车辆的构型树
     * 提供树节点查询功能
     *
     * @param vehicleId 车辆id
     * @param nodeName  节点搜索关键字
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR> GuoYang
     * 2019/12/14
     **/
    public JSONObject getStructureByVehicleId(String vehicleId, String nodeName, String time) {
        try {
            MtrVehicleVO vehicleVO = null;
            if (StringUtil.isNotEmpty(time)) {
                 vehicleVO = mtrVehicleMapping.getVehicleByIdForHistory(vehicleId, time,null);
            }else {
                 vehicleVO = mtrVehicleMapping.getVehicleById(vehicleId,null);
            }
            if (vehicleVO == null) {
                throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION, "所选时间该车辆不存在");
            }
            String vehicleTypeId = vehicleVO.getVehicleTypeId();
            String lineId = vehicleVO.getMetroLineId();
            List<VehicleStructureTreeVO> vehicleStructureTreeVOList;
            if (StringUtil.isNotEmpty(time)) {
                vehicleStructureTreeVOList = struVehicleStructureComponentMapping.selectVehicleStructureTreeHistoryList(vehicleTypeId, lineId, vehicleId,time);
                //循环 对 回溯历史 delFlag为1 的进行标记 及其父节点也循环标记
                try{
                    addBacktrackingState(vehicleStructureTreeVOList);
                }catch (DataAccessException e){
                    logger.error("Method[getStructureByVehicleId] Error:{}", e.getMessage());
                    throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION);
                }
            }else {
                vehicleStructureTreeVOList = struVehicleStructureComponentMapping.selectVehicleStructureTreeList(vehicleTypeId, lineId, vehicleId);
            }
            List<String> uniqueFlagArr = struVehicleStructureComponentService.searchNodeUniqueFlagByNameCn(nodeName, lineId, vehicleId);
            JSONArray treeWithVehicle = buildTreeWithVehicleInfo(vehicleVO, vehicleStructureTreeVOList);
            JSONObject result = new JSONObject();
            result.put("treeList", treeWithVehicle);
            result.put("uniqueFlagArr", uniqueFlagArr);
            return result;
        } catch (DataAccessException e) {
            logger.error("Method[getStructureByVehicleId] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 构建带有车辆信息的构型树
     *
     * @param vehicleVO 车辆信息
     * @param vehicleStructureTreeVOList 车辆构型列表
     * @return com.alibaba.fastjson.JSONArray
     * <AUTHOR> GuoYang
     * 2019/12/18
     **/
    private JSONArray buildTreeWithVehicleInfo(MtrVehicleVO vehicleVO,List<VehicleStructureTreeVO> vehicleStructureTreeVOList){
        String vehicleTypeId = vehicleVO.getVehicleTypeId();
        Map<String,String> map= struVehicleTypeStructureMapping.getVehicleTypeRootStructureCode(vehicleTypeId);
        String fbxUrl =null;
        String vehicleTypeStructureCode=null;
        String threedCode=null;
        if(map.containsKey("fbxurl")){
            fbxUrl = map.get("fbxurl");
        }
        if(map.containsKey("threedCode")){
            threedCode = map.get("threedCode");
        }
        if(map.containsKey("structure_code")){
            vehicleTypeStructureCode = map.get("structure_code");
        }
        HashMap<String,Object> vehicleNode = createVehicleNode(vehicleVO,vehicleTypeStructureCode,fbxUrl,threedCode);
        List<SysDictVO> subSystemList = sysDictService.listSubSystemDict();
        List<VehicleStructureTreeVO> treeList = TreeUtil.buildForStructure(vehicleStructureTreeVOList, vehicleTypeStructureCode, null,subSystemList);
        vehicleNode.put("children",treeList);
        JSONArray treeWithVehicle = new JSONArray();
        treeWithVehicle.add(vehicleNode);
        return treeWithVehicle;
    }


    /**
     * 创建车辆信息节点
     *
     * @param vehicleVO 车辆信息
     * @param structureCode 构型编码
     * @return java.util.HashMap<java.lang.String,java.lang.Object>
     * <AUTHOR> GuoYang
     * 2019/12/18
     **/
    private HashMap<String,Object> createVehicleNode(MtrVehicleVO vehicleVO,String structureCode,String fbxUrl,String threedCode){
        HashMap<String,Object> res = new HashMap<>();
        res.put("vehicleTypeId",vehicleVO.getVehicleTypeId());
        res.put("vehicleId",vehicleVO.getId());
        res.put("nameCn",vehicleVO.getNameCn());
        res.put("nameEn",vehicleVO.getNameEn());
        res.put("vehicleCode",vehicleVO.getVehicleCode());
        res.put("metroLineId",vehicleVO.getMetroLineId());
        res.put("remark",vehicleVO.getRemark());
        res.put("structureType","车辆");
        res.put("uniqueFlag",vehicleVO.getMetroLineId() + "-" + vehicleVO.getId());
        res.put("parentStructureCode","root");
        res.put("structureCode",structureCode);
        res.put("fbxUrl",fbxUrl);
        res.put("threedCode",threedCode);
        return res;
    }

    private void addBacktrackingState (List<VehicleStructureTreeVO> vehicleStructureTreeVOList){



        //循环1 记录所有修改过得点 提供给循环2使用
        List<VehicleStructureTreeVO> aa = new ArrayList<>();
        for(VehicleStructureTreeVO vehicleStructureTreeVO:vehicleStructureTreeVOList){

            int delFlag = vehicleStructureTreeVO.getDelFlag();
            //如果删除状态为1 增进入回溯状态修改和父节点寻找
            if(delFlag==1){
                //2020-04-01
                //首先排除一边删除过的记录 现在是否又加回来了 如果又加回来了 从比对list中删除
                String vehicleId = vehicleStructureTreeVO.getVehicleId();
                String struCode = vehicleStructureTreeVO.getStructureCode().toString();
                int a  = struVehicleStructureComponentMapping.selectStruVehicleStructureComponentDif(vehicleId,struCode);
                //如果查出有 又添加回来的记录 此条记录不做标记
                if(a>0){
                    continue;
                }else{
                    vehicleStructureTreeVO.setHistoryStatus(1);
                    aa.add(vehicleStructureTreeVO);
                }

            }
        }
        //循环2 查找所有的父节点 并记录  提供给循环3使用
        List structureCode = new ArrayList();
        for(VehicleStructureTreeVO vehicleStructureTreeVOaa:aa){
            Object parentCode = vehicleStructureTreeVOaa.getParentStructureCode();
            String [] Code = parentCode.toString().split("/");
            //此处Code[0] 初始取第一位的原因是 所有车辆第一个节点Metro不需要修改状态
            String structureCodeNow = Code[0];
            for(int i = 1;i<Code.length;i++){
                structureCodeNow += "/"+Code[i];
                structureCode.add(structureCodeNow);
            }
        }
        //循环3 更改所有structureCode 与 List structureCode 中匹配的回溯状态
        for(Object structureCodeSingle :structureCode){
            for(VehicleStructureTreeVO vehicleStructureTreeVO:vehicleStructureTreeVOList){
                if(vehicleStructureTreeVO.getStructureCode().equals(structureCodeSingle)){
                    vehicleStructureTreeVO.setHistoryStatus(1);
                }
            }
        }
    }
    public Object selectVehicleStartDate(String vehicleId){
        //2020-04-07 增加返回所查询车辆存在的最早时间点
        try {
            Date vehicleStartDate = mtrVehicleMapping.selectVehicleStartDate(vehicleId);
            JSONObject result = new JSONObject();
            result.put("vehicleStartDate", vehicleStartDate);
            return result;
        }catch (DataAccessException e) {
            logger.error("Method[getStructureByVehicleId] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object listVehicleByLineIdAndVehicleTypeId(String metroLineId, String vehicleTypeId) {
        //2020-04-22 增加根据线路id和车型id(可不添)查询车辆list
        try {
            return mtrVehicleMapping.listVehicleByLineIdAndVehicleTypeId(metroLineId,vehicleTypeId);
        }catch (DataAccessException e) {
            logger.error("Method[getStructureByVehicleId] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }
    public Object listVehicleByLineIdAndVehicleTypeIdNuoLi(String metroLineId, String vehicleTypeId) {
        try {
            List<MtrVehiclePO> nuoLi= mtrVehicleMapping.listVehicleByLineIdAndVehicleTypeId(metroLineId,vehicleTypeId);
            for(MtrVehiclePO mp:nuoLi){
                String id=mp.getId();
                id=id.substring(0,2)+id.substring(3,5);
                String nameCn=mp.getNameCn();
                nameCn=nameCn.substring(0,2)+nameCn.substring(3,5);
                String vehicle=mp.getVehicleCode();
                vehicle=vehicle.substring(0,2)+vehicle.substring(3,5);
                mp.setId(id);
                mp.setNameCn(nameCn);
                mp.setVehicleCode(vehicle);
            }
            return nuoLi;
        }catch (DataAccessException e) {
            logger.error("Method[getStructureByVehicleId] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }
    /**
     * 校验生产日期必须早于或等于交车日期
     *
     * @param mtrVehiclePO 车辆信息
     * <AUTHOR> GuoYang
     * 2020/4/28
     **/
    private void checkProductionAndDeliveryDateOrder(MtrVehiclePO mtrVehiclePO){
        Date productionDate = mtrVehiclePO.getProductionDate();
        Date deliveryDate = mtrVehiclePO.getDeliveryDate();
        if (productionDate != null && deliveryDate != null){
            boolean dateIllegal = deliveryDate.before(productionDate);
            if (dateIllegal){
                throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION,"生产日期不可晚于交车日期！");
            }
        }
    }

}
