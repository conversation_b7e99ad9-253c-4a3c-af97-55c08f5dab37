package cc.crrc.manage.service.mtr;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.mtr.MtrRAMSParameterMapping;
import cc.crrc.manage.pojo.component.RamsParameterDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class MtrRAMSParameterService {
    private final Logger logger = LoggerFactory.getLogger(MtrRAMSParameterService.class);
    @Autowired
    private MtrRAMSParameterMapping mtrRAMSParameterMapping;

    public List<RamsParameterDTO> getRamsParam(String comTypeId, String vehicleTypeId) {
        return mtrRAMSParameterMapping.getRamsParam(comTypeId,vehicleTypeId);
    }

    @Transactional
    public Object insertRamsParam(RamsParameterDTO ramsParam) {
        checkParamDateValid(ramsParam);
        ramsParam.setId(PrimaryKeyGenerator.generatorId());
        ramsParam.setCreateBy(UserUtils.getUser().getId());
        int result = mtrRAMSParameterMapping.insertRamsParam(ramsParam);
        if(result>0)
            return "SUCCESS";
        throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
    }

    @Transactional
    public Object updateRamsParam(RamsParameterDTO ramsParam) {
        checkParamDateValid(ramsParam);
        ramsParam.setModifyBy(UserUtils.getUser().getId());
        int result = mtrRAMSParameterMapping.updateRamsParam(ramsParam);
        if(result>0)
            return "SUCCESS";
        throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
    }

    @Transactional
    public Object deleteRamsParam(String id) {
        int result = mtrRAMSParameterMapping.deleteRamsParam(id);
        if(result>0)
            return "SUCCESS";
        throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
    }

    @Transactional
    public void clearRamsParam(String comTypeId,String vehicleTypeId){
        try {
            List<RamsParameterDTO> ramsParameterDTOList = getRamsParam(comTypeId,vehicleTypeId);
            for (RamsParameterDTO ramsParameterDTO:ramsParameterDTOList) {
                deleteRamsParam(ramsParameterDTO.getId());
            }
        }catch (DataAccessException e) {
            logger.error("Method[clearRamsParam] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    private void checkParamDateValid(RamsParameterDTO ramsParameterDTO) {
        boolean vehicleIsNull = ramsParameterDTO.getVehicleTypeId() == null;
        boolean componentIsNull = ramsParameterDTO.getComTypeId() == null;
        boolean bothNull = vehicleIsNull && componentIsNull;
        boolean bothHave = !(vehicleIsNull || componentIsNull);
        if (bothHave || bothNull)
            throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION);
    }

}
