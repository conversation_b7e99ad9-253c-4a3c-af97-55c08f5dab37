package cc.crrc.manage.service.mtr;

import cc.crrc.manage.common.annotation.ParamReplace;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.TreeUtil;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.mtr.MtrSubsystemDictMapping;
import cc.crrc.manage.mapper.mtr.StruVehicleStructureComponentMapping;
import cc.crrc.manage.mapper.stru.StruVehicleTypeStructureMapping;
import cc.crrc.manage.pojo.SysDictVO;
import cc.crrc.manage.pojo.mtr.MtrSubsystemDictPO;
import cc.crrc.manage.pojo.mtr.StruVehicleStructureComponentPO;
import cc.crrc.manage.pojo.mtr.StruVehicleStructureComponentUpdateDTO;
import cc.crrc.manage.pojo.stru.StruVehicleTypeStructureTreeVO;
import cc.crrc.manage.service.SysDictService;
import com.github.pagehelper.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static cc.crrc.manage.common.utils.Constants.STRUCTURE_TYPE_CARRIER;
import static cc.crrc.manage.common.utils.Constants.STRUCTURE_TYPE_SYSTEM;

/**
 * @FileName StruVehicleStructureComponentService
 * <AUTHOR> xin
 * @Date 2020/3/18 9:50
 * @Version 1.0
 **/
@Service
public class StruVehicleStructureComponentService {
    private final Logger logger = LoggerFactory.getLogger(StruVehicleStructureComponentService.class);
    @Autowired
    private StruVehicleStructureComponentMapping mapping;
    @Autowired
    private StruVehicleTypeStructureMapping struVehicleTypeStructureMapping;
    @Autowired
    private SysDictService sysDictService;
    @Autowired
    private MtrSubsystemDictMapping mtrSubsystemDictMapping;
    @Autowired
    private MtrVehicleTypeService mtrVehicleTypeService;


    @Transactional(rollbackFor = Exception.class)
    public void addVehicleInfo(String idList, String vehicleId) {
    	if(idList == null || idList.isEmpty()) {
    		throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(),"请选择正确的构型分支");
    	}
        String[] idArray = idList.split(",");
        try {
            List<StruVehicleStructureComponentPO> list = new ArrayList<>();
            for(String id : idArray) {
                StruVehicleStructureComponentPO struVehicleStructureComponentPO = struVehicleTypeStructureMapping.getStruVehicleStructureById(id,vehicleId, UserUtils.getUserId());
                // 防止重复添加
                int count = mapping.valid(struVehicleStructureComponentPO.getVehicleId(),struVehicleStructureComponentPO.getStructureCode());
                if(count == 0) {
                    //雪花算法添加主键
                    struVehicleStructureComponentPO.setId(PrimaryKeyGenerator.generatorId());
                    list.add(struVehicleStructureComponentPO);
                }
            }
            if (list.isEmpty()) {
                return;
            }
            // 添加车辆档案信息
            mapping.addStruVehicleStructureComponent(list);

            // 维护车辆子系统信息
            List<String> newSystemNameList = new ArrayList<>();
            // 获取新添加的系统名称
            for (StruVehicleStructureComponentPO newStructure : list) {
                if (STRUCTURE_TYPE_SYSTEM.equals(newStructure.getStructureType())) {
                    newSystemNameList.add(newStructure.getNameCn());
                }
            }
            //2021-08-23 后端车辆的系统由字典表统一查询 暂时不采用车辆系统独立配对方式 后续有业务需求再加回 lixin
//            addSystemRelationAvoidRepeat(newSystemNameList,vehicleId);
        } catch (DataAccessException e) {
            logger.error("Method[addVehicleInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    @Transactional
    public void updateVehicleStructureBasicInfo(StruVehicleStructureComponentUpdateDTO struVehicleStructureComponentUpdateDTO) {
        struVehicleStructureComponentUpdateDTO.setModifyBy(UserUtils.getUserId());
        try {
            // 校验车厢类型的构型中文名不可重复
            StruVehicleStructureComponentPO currentNodeInfo = mapping.getStruVehicleStructureById(struVehicleStructureComponentUpdateDTO.getId());
            if (STRUCTURE_TYPE_CARRIER.equals(currentNodeInfo.getStructureType())) {
                List<String> locationNameCnList = mapping.getLocationByVehicleId(currentNodeInfo.getVehicleId(), currentNodeInfo.getNameCn());
                if (locationNameCnList.contains(struVehicleStructureComponentUpdateDTO.getNameCn())){
                    throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION,"车厢中文名不可重复！");
                }
            }
            // 更新车辆档案信息
            mapping.updateVehicleStructureBasicInfo(struVehicleStructureComponentUpdateDTO);
        } catch (DataAccessException e) {
            logger.error("Method[updateVehicleStructureBasicInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    @Transactional
    public void deleteVehicleInfo(String id) {
        try {
            StruVehicleStructureComponentPO targetStructure = mapping.getStruVehicleStructureById(id);
            // 判断是否含有子节点
            List<StruVehicleStructureComponentPO> list = mapping.getSubStructureById(id);
            if (!CollectionUtils.isEmpty(list)) {
                throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION.getErrorCode(), "该构型含有子节点，请确认无子节点后再进行删除！");
            }
            // 删除车辆档案
            mapping.deleteVehicleInfo(id,UserUtils.getUserId());
            // 删除子系统字典表相关字段
            String structureType = targetStructure.getStructureType();
            if (STRUCTURE_TYPE_SYSTEM.equals(structureType)) {
                String nameCn = targetStructure.getNameCn();
                String vehicleId = targetStructure.getVehicleId();
                int systemNumber = mapping.countSubSystemInVehicle(vehicleId,nameCn);
                if (systemNumber == 1){
                    mtrSubsystemDictMapping.deleteSubsystemDict(vehicleId, nameCn);
                }
            }
        } catch (DataAccessException e) {
            logger.error("Method[deleteVehicleInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }


    @Transactional
    public void addVehicle(String vehicleTypeId,String vehicleId, String currentId ) {
        try{
            //查询所选车型对应的基本车型构型树
            List<StruVehicleStructureComponentPO> list = struVehicleTypeStructureMapping.getVehicleTypeStructureCode(vehicleTypeId,vehicleId,currentId);
            //添加车辆关联的系统数据 表mtr_subsystem_dict
            List<String> system = new ArrayList<>();
            for(StruVehicleStructureComponentPO dict:list){
                //雪花算法添加主键
                dict.setId(PrimaryKeyGenerator.generatorId());
                String structureType = dict.getStructureType();
                String subSystem = dict.getNameCn();
                if("系统".equals(structureType) && StringUtil.isNotEmpty(subSystem)){
                    system.add(subSystem);
                }
            }
//            if(system.size()>0){
//                //添加车辆系统关系表
//                addSystemRelation(system,vehicleId);
//            }
            if(list.size()>0){
                //存储车辆档案
                mapping.addStruVehicleStructureComponent(list);
            }
        }catch (DataAccessException e) {
            logger.error("Method[addVehicle存储车辆档案] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    @Transactional
    public void deleteStruVehicleStructureComponent(String vehicleId, String currentId) {
        try{
            mapping.deleteStruVehicleStructureComponent(vehicleId,currentId);
            mtrVehicleTypeService.deleteSubsystemDict(vehicleId,null);
        }catch (DataAccessException e) {
            logger.error("Method[deleteVehicle删除车辆档案] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }


    /**
     * 查询指定构型位置下可添加的车辆构型
     *
     * @param vehicleId 车辆ID
     * @param structureCode 构型编码
     * @return java.util.List<cc.crrc.manage.pojo.stru.StruVehicleTypeStructureTreeVO>
     * <AUTHOR> GuoYang
     * 2020/3/19
     **/
    public List<StruVehicleTypeStructureTreeVO> getDiffStructureTree(String vehicleId, String structureCode) {
        try {
            if(vehicleId == null){
                throw new RestApiException(ExceptionInfoEnum.URL_PARAMETER_MISMATCH_EXCEPTION,"车辆参数不可为空");
            }
            // 默认车辆根节点
            if (structureCode == null || structureCode.isEmpty()){
                structureCode = "root";
            }

            List<StruVehicleTypeStructureTreeVO> res = new ArrayList<>();
            StruVehicleTypeStructureTreeVO root = struVehicleTypeStructureMapping.getStructureTreeByStructureCode(vehicleId,structureCode);
            // 根节点查询子系统相关信息
            HashMap<String,String> systemInfo = getStructureSystem((String)root.getStructureCode(),vehicleId);
            if (systemInfo != null) {
                root.setSubSystem(systemInfo.get("name"));
                root.setSystemKey(systemInfo.get("key"));
            }
            res.add(root);
            // 获取标准模板中存在，当前档案中不存在的构型编码列表
            List<String> diffStructures = struVehicleTypeStructureMapping.getDiffStructureListByStructureCode(vehicleId,structureCode);
            if(diffStructures.isEmpty()){
                return res;
            }
            // 获取所有待查询的结构编码，及其所有父节点的结构编码
            List<String> allDiffStructures4Tree = new ArrayList<>();
            for (String structure : diffStructures) {
                List<String> tempList = getAllNodeInBranch(structure);
                allDiffStructures4Tree.addAll(tempList);
            }
            List<StruVehicleTypeStructureTreeVO> diffStructureTree = struVehicleTypeStructureMapping.getAllowStructureTree(vehicleId,allDiffStructures4Tree);
            List<SysDictVO> subSystemList = sysDictService.listSubSystemDict();
            res = TreeUtil.buildForStructure(diffStructureTree, root.getParentStructureCode(), null,subSystemList);
            return res;
        }catch (DataAccessException e) {
            logger.error("Method[getDiffStructureTree] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 截取并获得节点的所有上级路径
     * 如：node="A1/B1/C1" 得到["A1/B1","A1"]
     *
     * @param node 节点全路径
     * @return java.util.List<java.lang.String>
     * <AUTHOR> GuoYang
     * 2020/3/26
     **/
    private List<String> getAllNodeInBranch(String node){
        ArrayList<String> resList = new ArrayList<>();
        ArrayList<Integer> indexList = new ArrayList<>();
        int index = node.indexOf("/");
        while (index >= 0) {
            indexList.add(index);
            index = node.indexOf("/", index + 1);
        }
        for (Integer tempIndex : indexList) {
            resList.add(node.substring(0,tempIndex));
        }
        resList.add(node);
        return resList;
    }

    /**
     * 获取一个构型节点所属的系统信息
     *
     * @param structureCode 车辆构型
     * @param vehicleId 车辆id
     * @return java.util.HashMap<java.lang.String,java.lang.String>
     * <AUTHOR> GuoYang
     * 2020/3/26
     **/
    public HashMap<String,String> getStructureSystem(String structureCode, String vehicleId){
        try {
            List<String> structureCodeList = getAllNodeInBranch(structureCode);
            return mapping.getStructureSystem(structureCodeList,vehicleId);
        } catch (DataAccessException e) {
            logger.error("Method[getStructureSystem] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 将新添加的系统与车辆进行绑定。
     * 执行去重操作，防止已有系统的重复添加。
     *
     * @param newSystemNameList 新添加的系统名称列表
     * @param vehicleId 车辆id
     * <AUTHOR> GuoYang
     * 2020/3/26
     **/
    private void addSystemRelationAvoidRepeat(List<String> newSystemNameList,String vehicleId){
        try {
            // 去重 获得需要添加的子系统
            List<MtrSubsystemDictPO> mtrSubsystemDictPOList = mtrSubsystemDictMapping.listSubsystemDictByVehicleId(vehicleId);
            List<String> existName = getNameFromMtrSubsystemDictPOList(mtrSubsystemDictPOList);
            List<String> systemReady2Add = getSystemReady2Add(newSystemNameList, existName);
            // 添加子系统信息
            addSystemRelation(systemReady2Add, vehicleId);
        } catch (DataAccessException e) {
            logger.error("Method[addSystemRelationAvoidRepeat] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * 获取子系统列表中的子系统名称
     *
     * @param mtrSubsystemDictPOList 子系统实体列表
     * @return java.util.List<java.lang.String>
     * <AUTHOR> GuoYang
     * 2020/3/26
     **/
    private List<String> getNameFromMtrSubsystemDictPOList(List<MtrSubsystemDictPO> mtrSubsystemDictPOList){
        List<String> res = new ArrayList<>();
        for (MtrSubsystemDictPO mtrSubsystemDictPO : mtrSubsystemDictPOList) {
            res.add(mtrSubsystemDictPO.getLabel());
        }
        return res;
    }

    /**
     * 将两个列表进行求差操作。
     * 获取待添加的新系统的系统名称。
     *
     * @param newSystem 新添加的系统名称列表
     * @param existSystem 已存在添加的系统名称列表
     * @return java.util.List<java.lang.String>
     * <AUTHOR> GuoYang
     * 2020/3/26
     **/
    private List<String> getSystemReady2Add(List<String> newSystem,List<String> existSystem){
        List<String> res = new ArrayList<>();
        for (String s : newSystem) {
            if (!existSystem.contains(s)){
                res.add(s);
            }
        }
        return res;
    }

    /**
     * 将新添加的系统与车辆进行绑定。
     *
     * @param systemNameList 待添加的系统名称列表
     * @param vehicleId 车辆id
     * <AUTHOR> GuoYang
     * 2020/3/26
     **/
    private void addSystemRelation(List<String> systemNameList,String vehicleId){
        try {
            // 获取待添加子系统的全部属性
            List<String> idListReady2Add = new ArrayList<>();
            List<SysDictVO> allSystemList = sysDictService.listSubSystemDict();
            for (String systemName : systemNameList) {
                for (SysDictVO sysDictVO : allSystemList) {
                    if (systemName.equals(sysDictVO.getLabel())) {
                        idListReady2Add.add(sysDictVO.getId());
                    }
                }
            }
            String userId = UserUtils.getUserId();
            if (!idListReady2Add.isEmpty()) {
                mtrSubsystemDictMapping.addSystemFromAllDict(vehicleId, userId, idListReady2Add,PrimaryKeyGenerator.generatorId());
            }
        } catch (DataAccessException e) {
            logger.error("Method[addSystemRelation] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * @return java.util.List<cc.crrc.manage.pojo.mtr.MtrSubsystemDictPO>
     * @Description 根据车型id取得子系统字典列表
     * <AUTHOR> yuxi
     * @Date 14:41 2019/11/19
     * @Param [vehicleTypeId]
     **/
    public List<MtrSubsystemDictPO> listSubsystemDictByVehicleId(String vehicleId) {
        try {
            return mtrSubsystemDictMapping.listSubsystemDictByVehicleId(vehicleId);
        } catch (DataAccessException e) {
            logger.error("Method[listSubsystemDictByVehicleId] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public List<String> searchNodeUniqueFlagByNameCn(String nameCn, String lineId, String vehicleId) {
        // 如果节点名称不为空，返回符合模糊查询条件的构型树结点唯一标识，前端用作展开构型树
        List<String> uniqueFlagArr = new ArrayList<>();
        if (StringUtil.isNotEmpty(nameCn)) {
            uniqueFlagArr = mapping.listUniqueFlag(nameCn, lineId, vehicleId);
        }
        return uniqueFlagArr;
    }

    public Object getLocationByVehicleCode(String vehicleCode) {
        try {
            return struVehicleTypeStructureMapping.getLocationByVehicleCode(vehicleCode);
        } catch (DataAccessException e) {
            logger.error("Method[getLocationByVehicleCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }
}
