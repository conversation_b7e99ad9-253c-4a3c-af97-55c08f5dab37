package cc.crrc.manage.service.mtr;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.mapper.mtr.VihicleSoftwareVersionMapping;
import cc.crrc.manage.pojo.mtr.VehicleSoftwareVersionDTO;
import cc.crrc.manage.pojo.mtr.VehicleSoftwareVersionPO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 * 车载软件版本履历查询服务层
 * 2019/11/18
 **/

@Service
public class VehicleSoftwareVersionService {
    private final Logger logger = LoggerFactory.getLogger(VehicleSoftwareVersionService.class);

    @Autowired
    private VihicleSoftwareVersionMapping vihicleSoftwareVersionMapping;

    /**
     * 车载软件版本履历列表、条件查询
     * @param vehicleSoftwareVersionDTO
     * <AUTHOR>
     * @return
     */
    public PageInfo<VehicleSoftwareVersionPO> VehicleSoftwareVersionList(VehicleSoftwareVersionDTO vehicleSoftwareVersionDTO) {
        try {
            int currentPage = vehicleSoftwareVersionDTO.getPageNumber();
            int pageSize = vehicleSoftwareVersionDTO.getPageSize();
            PageHelper.startPage(currentPage, pageSize);
            //查询列表数据
            List<VehicleSoftwareVersionPO> vehicleSoftwareVersionList = vihicleSoftwareVersionMapping.VehicleSoftwareVersionList(vehicleSoftwareVersionDTO);
            return new PageInfo<>(vehicleSoftwareVersionList);
        } catch (DataAccessException e) {
            logger.error("Method[VehicleSoftwareVersionList] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 车载软件版本履历列表、条件查询
     * @param   softwareId 软件版本id componentId 部件id
     * <AUTHOR>
     * @return
     */
    public PageInfo<VehicleSoftwareVersionPO> getVersionResume( String softwareId,String componentId) {
        try {

            List<VehicleSoftwareVersionPO> vehicleSoftwareVersionList = vihicleSoftwareVersionMapping.getVersionResume(softwareId,componentId);
            return new PageInfo<>(vehicleSoftwareVersionList);
        } catch (DataAccessException e) {
            logger.error("Method[getVersionResume] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }



}
