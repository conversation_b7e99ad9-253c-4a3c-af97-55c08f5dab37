package cc.crrc.manage.service.mtr;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.mapper.mtr.MtrVehicleTypeMapping;
import cc.crrc.manage.mapper.mtr.MtrVehicleTypeRelationMapping;
import cc.crrc.manage.pojo.mtr.MtrVehicleTypeRelationPO;
import cc.crrc.manage.pojo.mtr.MtrVehicleTypeVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 车辆与车辆类型关系服务层
 *
 * <AUTHOR>
 * 2019/12/3
 **/
@Service
public class MtrVehicleTypeRelationService {
    private final Logger logger = LoggerFactory.getLogger(MtrVehicleTypeRelationService.class);

    @Autowired
    private MtrVehicleTypeRelationMapping mtrVehicleTypeRelationMapping;
    @Autowired
    private MtrVehicleTypeMapping mtrVehicleTypeMapping;

    /**
     * 根据车辆id，获取该车辆的车型信息
     *
     * @param vehicleId 车辆id
     * @return cc.crrc.manage.pojo.mtr.MtrVehicleTypeRelationPO
     * <AUTHOR> GuoYang
     * 2019/12/5
     **/
    public MtrVehicleTypeRelationPO getVehicleTypeById(String vehicleId) {
        try {
            return mtrVehicleTypeRelationMapping.getVehicleTypeById(vehicleId);
        } catch (DataAccessException e) {
            logger.error("Method[getVehicleTypeById] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 获取某一车型下的全部车辆关联信息
     *
     * @param vehicleTypeId 车型id
     * @return java.util.List<cc.crrc.manage.pojo.mtr.MtrVehicleTypeRelationPO>
     * <AUTHOR> GuoYang
     * 2019/12/5
     **/
    public List<MtrVehicleTypeRelationPO> listVehicleByVehicleTypeId(Long vehicleTypeId) {
        try {
            return mtrVehicleTypeRelationMapping.listVehicleByVehicleTypeId(vehicleTypeId);
        } catch (DataAccessException e) {
            logger.error("Method[listVehicleByVehicleTypeId] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 获取某一车辆的车型更迭历史信息
     *
     * @param vehicleId 车辆id
     * @return java.util.List<cc.crrc.manage.pojo.mtr.MtrVehicleTypeRelationPO>
     * <AUTHOR> GuoYang
     * 2019/12/5
     **/
    public List<MtrVehicleTypeRelationPO> listVehicleHistory(Long vehicleId) {
        try {
            return mtrVehicleTypeRelationMapping.listVehicleHistory(vehicleId);
        } catch (DataAccessException e) {
            logger.error("Method[listVehicleHistory] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 更改车辆的车型
     *
     * @param vehicleId 车辆id
     * @param vehicleTypeId 车辆类型id
     * <AUTHOR> GuoYang
     * 2019/12/5
     **/
    @Transactional(rollbackFor=Exception.class)
    public void changeVehicleRelation(String vehicleId,String vehicleTypeId) {
        try {
            MtrVehicleTypeRelationPO mtrVehicleTypeRelationPO= getVehicleTypeById(vehicleId);
            String currentVehicleTypeId= mtrVehicleTypeRelationPO.getVehicleTypeId();
            if (!currentVehicleTypeId.equals(vehicleTypeId)) {
                // 解除当前车型关系
                invalidRelationByVehicleId(vehicleId);
                // 添加新的车型关系
                addRelation(vehicleId, vehicleTypeId);
            }
        } catch (DataAccessException e) {
            logger.error("Method[changeVehicleRelation] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 解除车辆与车型的关联关系
     *
     * @param vehicleId 车辆id
     * <AUTHOR> GuoYang
     * 2019/12/5
     **/
    public void invalidRelationByVehicleId(String vehicleId) {
        try {
            mtrVehicleTypeRelationMapping.invalidRelationByVehicleId(vehicleId);
        } catch (DataAccessException e) {
            logger.error("Method[invalidRelationByVehicleId] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 添加车辆与车型的关系
     *
     * @param vehicleId 车辆id
     * @param vehicleTypeId 车辆类型id
     * <AUTHOR> GuoYang
     * 2019/12/5
     **/
    @Transactional(rollbackFor=Exception.class)
    public void addRelation(String vehicleId, String vehicleTypeId) {
        try {
            // 信息校验
            if(vehicleTypeId == null) {
                throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION);
            }
            List<MtrVehicleTypeVO> mtrVehicleTypeVOList = mtrVehicleTypeMapping.listVehicleType();
            isContainTargetType(mtrVehicleTypeVOList,vehicleTypeId);
            // 插入车型关系
            int successCount = mtrVehicleTypeRelationMapping.addRelation(vehicleId, vehicleTypeId);
            if (successCount != 1) {
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
            }
        } catch (DataAccessException e) {
            logger.error("Method[addRelation] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 校验车型列表中，是否含有某一车型
     *
     * @param mtrVehicleTypeVOList 车型列表
     * @param vehicleTypeId 待确定的车型id
     * <AUTHOR> GuoYang
     * 2019/12/5
     **/
    public void isContainTargetType(List<MtrVehicleTypeVO> mtrVehicleTypeVOList,String vehicleTypeId){
        for (MtrVehicleTypeVO mtrVehicleTypeVO : mtrVehicleTypeVOList) {
            if (vehicleTypeId.equals(mtrVehicleTypeVO.getId())){
                return;
            }
        }
        logger.warn("Method[isContainTargetType] Warn:{}","目标车型不存在！");
        throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION);
    }

}
