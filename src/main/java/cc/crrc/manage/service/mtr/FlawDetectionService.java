package cc.crrc.manage.service.mtr;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.LoggerUtils;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.mtr.FlawDetectionMapping;
import cc.crrc.manage.pojo.mtr.FlawDetectionPO;
import cc.crrc.manage.pojo.mtr.FlawDetectionProductPO;
import cc.crrc.manage.pojo.mtr.FlawDetectionVO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName FlawDetectionService
 * @Description: 探伤记录
 * <AUTHOR> yongqing
 * @Date 2022/3/25 9:52
 * @Version 1.0
 */
@Service
public class FlawDetectionService {
    private final Logger logger = LoggerFactory.getLogger(FlawDetectionService.class);
    @Resource
    FlawDetectionMapping flawDetectionMapping;

    /**
     * @Description 查询记录列表
     * @param flawDetectionPO
     * @return
     */
    public PageInfo<FlawDetectionVO> queryFlawDetectionList(FlawDetectionPO flawDetectionPO) {
        List<FlawDetectionVO> flawDetectionVOS = null;
        try {
            PageHelper.startPage(flawDetectionPO.getPageNumber(), flawDetectionPO.getPageSize());
            flawDetectionVOS = flawDetectionMapping.queryFlawDetectionList(flawDetectionPO);
        } catch (Exception e) {
            LoggerUtils.error(logger, e);
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
        return new PageInfo<>(flawDetectionVOS);
    }

    /**
     * @param id
     * @return
     * @Description 根据id查询探伤记录详细信息
     */
    public FlawDetectionVO queryFlawDetectionInfo(String id) {
        FlawDetectionVO flawDetectionVO = null;
        try {
            //查询详细信息
            flawDetectionVO = flawDetectionMapping.queryFlawDetectionInfo(id);
            //查询产品检验结果，插入详细信息内一并返回
            flawDetectionVO.setProductInfo(flawDetectionMapping.queryFlawDetectionProduct(id));
        } catch (Exception e) {
            LoggerUtils.error(logger, e);
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
        return flawDetectionVO;
    }

    /**
     * @param flawDetectionPO
     * @return
     * @Description 添加探伤记录
     */
    @Transactional(rollbackFor = Exception.class)
    public Object saveFlawDetection(FlawDetectionPO flawDetectionPO) {
        if (flawDetectionPO == null) {
            throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION);
        }
        //从基础信息json内获取产品名称，在数据库插入这个字段，方便当作筛选条件查询
        JSONObject jsonObject = JSON.parseObject(flawDetectionPO.getBasicInfo());
        flawDetectionPO.setId(PrimaryKeyGenerator.generatorId());
        flawDetectionPO.setProduct(jsonObject.getString("product"));
        flawDetectionPO.setCreateBy(UserUtils.getUserId().toString());
        List<FlawDetectionProductPO> productInfo = null;
        try {
            flawDetectionMapping.saveFlawDetection(flawDetectionPO);
            //遍历产品检测结果数据插入到产品表
            productInfo = flawDetectionPO.getProductInfo();
            if (productInfo.size() > 0) {
                for (FlawDetectionProductPO flawDetectionProduct : productInfo) {
                    flawDetectionProduct.setId(PrimaryKeyGenerator.generatorId());
                    flawDetectionProduct.setFlawDetectionId(flawDetectionPO.getId());
                    flawDetectionProduct.setType(flawDetectionPO.getType());
                    flawDetectionMapping.saveFlawDetectionProduct(flawDetectionProduct);
                }
            }
        } catch (Exception e) {
            LoggerUtils.error(logger, e);
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
        return flawDetectionPO.getId();
    }

    /**
     * @param flawDetectionPO
     * @Description 更新探伤记录
     */
    @Transactional(rollbackFor = Exception.class)
    public Object changeFlawDetection(FlawDetectionPO flawDetectionPO) {
        //从基础信息json内获取产品名称，在数据库插入这个字段，方便当作筛选条件查询
        JSONObject jsonObject = JSON.parseObject(flawDetectionPO.getBasicInfo());
        flawDetectionPO.setProduct(jsonObject.getString("product"));
        flawDetectionPO.setModifyBy(UserUtils.getUserId().toString());
        List<FlawDetectionProductPO> productInfo;
        try {
            //修改记录信息
            flawDetectionMapping.changeFlawDetection(flawDetectionPO);
            //删除当前记录的检测结果数据，并重新插入修改后的数据
            flawDetectionMapping.deleteFlawDetectionProduct(flawDetectionPO.getId());
            productInfo = flawDetectionPO.getProductInfo();
            if (productInfo.size() > 0) {
                for (FlawDetectionProductPO flawDetectionProduct : productInfo) {
                    flawDetectionProduct.setId(PrimaryKeyGenerator.generatorId());
                    flawDetectionProduct.setFlawDetectionId(flawDetectionPO.getId());
                    flawDetectionProduct.setType(flawDetectionPO.getType());
                    flawDetectionMapping.saveFlawDetectionProduct(flawDetectionProduct);
                }
            }
        } catch (Exception e) {
            LoggerUtils.error(logger, e);
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
        return "success";
    }

    /**
     * @param id
     * @Description 删除探伤记录
     */
    public void deleteFlawDetection(String id) {
        try {
            //删除标志置"1",逻辑删除
            flawDetectionMapping.changeFlawDetectionDelFlag(id);
        } catch (Exception e) {
            LoggerUtils.error(logger, e);
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

}
