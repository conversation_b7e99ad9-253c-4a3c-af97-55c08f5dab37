package cc.crrc.manage.service.stru;

import cc.crrc.manage.common.annotation.ParamReplace;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.TreeUtil;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.SysFileMapping;
import cc.crrc.manage.mapper.component.ComponentTypeMapping;
import cc.crrc.manage.mapper.ekb.EkbFaultTypeMapping;
import cc.crrc.manage.mapper.mtr.MtrVehicleTypeMapping;
import cc.crrc.manage.mapper.stru.StruVehicleTypeStructureMapping;
import cc.crrc.manage.pojo.SysDictVO;
import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.mtr.MtrVehicleTypeVO;
import cc.crrc.manage.pojo.stru.StruVehicleTypeStructurePO;
import cc.crrc.manage.pojo.stru.StruVehicleTypeStructureTreeVO;
import cc.crrc.manage.service.SysDictService;
import cc.crrc.manage.service.SysFileService;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static cc.crrc.manage.common.utils.Constants.STRUCTURE_3D_PICTURE_FILE_FORMAT;
import static cc.crrc.manage.common.utils.Constants.STRUCTURE_TYPE_CARRIER;

/**
 * @FileName StruVehicleTypeStructureService
 * <AUTHOR> yuxi
 * @Date 2019/11/18 9:08
 * @Version 1.0
 **/
@Service
public class StruVehicleTypeStructureService {
    private final Logger logger = LoggerFactory.getLogger(StruVehicleTypeStructureService.class);

    @Autowired
    private StruVehicleTypeStructureMapping mapping;
    @Autowired
    private EkbFaultTypeMapping ekbFaultTypeMapping;
    @Autowired
    private MtrVehicleTypeMapping vehicleTypeMapping;
    @Autowired
    private ComponentTypeMapping componentTypeMapping;
    @Autowired
    private SysDictService sysDictService;
    @Autowired
    private SysFileService sysFileService;
    @Autowired
    private SysFileMapping sysFileMapping;


    /**
     * 新增车辆构型
     *
     * @param struVehicleTypeStructure 车辆构型信息
     * @return java.lang.Object
     * <AUTHOR> GuoYang
     * 2019/12/16
     **/
    @Transactional(rollbackFor = Exception.class)
    public Object addStruVehicleTypeStructure(StruVehicleTypeStructurePO struVehicleTypeStructure) {
        try {
            String userId = UserUtils.getUserId();
            struVehicleTypeStructure.setCreateBy(userId);
            struVehicleTypeStructure.setCreateTime(new Date());
            struVehicleTypeStructure.setModifyBy(userId);
            //雪花算法添加主键
            struVehicleTypeStructure.setId(PrimaryKeyGenerator.generatorId());
            String parentStructureCode = struVehicleTypeStructure.getParentStructureCode();
            if (parentStructureCode == null) {
                throw new RestApiException(ExceptionInfoEnum.URL_PARAMETER_MESSING_EXCEPTION, "缺少父节点！");
            }
            // 校验车厢节点中文名合法
            checkLocationNameCnRepeat(struVehicleTypeStructure);
            struVehicleTypeStructure.setStructurePosition(struVehicleTypeStructure.getStructureCode());
            return mapping.addStruVehicleTypeStructure(struVehicleTypeStructure);
        } catch (DataAccessException e) {
            logger.error("Method[addStruVehicleTypeStructure] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "保存数据异常！");
        }
    }

    /**
     * @return void
     * @Description 更新车辆构型
     * <AUTHOR> yuxi
     * @Date 16:12 2019/12/24
     * @Param [struVehicleTypeStructure]
     **/
    @Transactional(rollbackFor = Exception.class)
    public void updateStruVehicleTypeStructure(StruVehicleTypeStructurePO struVehicleTypeStructure) {
        try {
            String userId = UserUtils.getUserId();
            struVehicleTypeStructure.setModifyBy(userId);
            // 校验车厢节点中文名合法
            checkLocationNameCnRepeat(struVehicleTypeStructure);
            // 更新节点信息
            int successCount = mapping.updateStruVehicleTypeStructure(struVehicleTypeStructure);
            if (successCount != 1) {
                throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
            }
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    /**
     * @return net.minidev.json.JSONObject
     * @Description 获取车辆构型树形list
     * <AUTHOR>
     * @Date 2019/11/18 9:44
     * @Param [vehicleTypeId, nameCn]
     **/

    public JSONObject treeListStruVehicleTypeStructure(String vehicleTypeId, String nameCn) {
        try {
            List<String> uniqueFlagArr = new ArrayList<>();
            List<StruVehicleTypeStructureTreeVO> treeList = new ArrayList<>();
            // 取得构型list
            List<StruVehicleTypeStructureTreeVO> structures = mapping.selectStructureTreeList(vehicleTypeId);
            if (!CollectionUtils.isEmpty(structures)) {
                // 查询符合条件的节点
                uniqueFlagArr = searchNodeUniqueFlagByNameCn(vehicleTypeId, nameCn);
                // 排序并构建树list
                List<SysDictVO> subSystemList = sysDictService.listSubSystemDict();
                treeList = TreeUtil.buildForStructure(structures, "root", null,subSystemList);
            }
            // 结果集
            JSONObject result = new JSONObject();
            result.put("treeList", treeList);
            result.put("uniqueFlagArr", uniqueFlagArr);
            // 返回车型实体（编辑时用）
            MtrVehicleTypeVO vehicleType = vehicleTypeMapping.getVehicleTypeById(vehicleTypeId);
            result.put("vehicleType", vehicleType);
            return result;
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    /**
     * 查询包含搜索关键字的节点
     *
     * @param vehicleTypeId 车辆构型id
     * @param nameCn        搜索关键字
     * @return java.util.List<java.lang.String>
     * <AUTHOR> GuoYang
     * 2019/12/14
     **/

    public List<String> searchNodeUniqueFlagByNameCn(String vehicleTypeId, String nameCn) {
        // 如果节点名称不为空，返回符合模糊查询条件的构型树结点唯一标识，前端用作展开构型树
        List<String> uniqueFlagArr = new ArrayList<>();
        if (StringUtil.isNotEmpty(nameCn)) {
            uniqueFlagArr = mapping.listUniqueFlag(vehicleTypeId, nameCn);
        }
        return uniqueFlagArr;
    }

    /**
     * @return java.lang.Object
     * @Description 根据构型编码查询故障类型信息
     * <AUTHOR>
     * @Date 15:59 2019/12/6
     * @Param [structureCode]
     **/
    public Object getFaultTypeInfoByStructureCode(String structureCode) {
        try {
            return ekbFaultTypeMapping.getFaultTypeByStructureCodeAndSubsystem(structureCode, null);
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    /**
     * @return java.lang.Object
     * @Description 根据构型id查询构型信息
     * <AUTHOR> yuxi
     * @Date 15:59 2019/12/6
     * @Param [id]
     **/
    public Object getStruVehicleTypeStructureById(String id) {
        try {
            StruVehicleTypeStructurePO struVehicleTypeStructure = mapping.getStruVehicleTypeStructureById(id);
            if (struVehicleTypeStructure != null) {
                String componentTypeId = struVehicleTypeStructure.getComponentTypeId();
                SysFilePO file = new SysFilePO();
                if (componentTypeId != null) {
                    file = componentTypeMapping.getSysFileByComponentTypeId(componentTypeId);
                }
                if (file != null) {
                    struVehicleTypeStructure.setUrl(file.getUrl());
                }
                return struVehicleTypeStructure;
            } else {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("value", null);
                return jsonObject;
            }
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    public List<StruVehicleTypeStructurePO> getLocationByVehicleTypeId(String vehicleTypeId) {
        try {
            List<StruVehicleTypeStructurePO> list= mapping.getLocationByVehicleTypeId(vehicleTypeId);
            StruVehicleTypeStructurePO struVehicleTypeStructurePO = new StruVehicleTypeStructurePO();
            struVehicleTypeStructurePO.setNameCn("ALL");
            struVehicleTypeStructurePO.setNameEn("ALL");
            list.add(struVehicleTypeStructurePO);
            return list;
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    /**
     * 根据车型id查询其所有子系统
     * @param vehicleTypeId 车型id
     * @return java.util.List<java.lang.String>
     * <AUTHOR> GuoYang
     * 2020-02-20
     **/
    public List<String> getAllSystemByVehicleTypeId(Long vehicleTypeId) {
        try {
            return mapping.getAllSystemByVehicleTypeId(vehicleTypeId);
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 校验节点中文名
     * 若为车厢类型的，则中文名不可重复
     *
     * @param struVehicleTypeStructure 节点数据
     * <AUTHOR> GuoYang
     * 2020/5/11
     **/
    private void checkLocationNameCnRepeat(StruVehicleTypeStructurePO struVehicleTypeStructure){
        /*
         *  new 2020 09/22 Liu xin chen 修改车厢时 直接点修改 提示车厢中文名已存在问题
         */
        if (STRUCTURE_TYPE_CARRIER.equals(struVehicleTypeStructure.getStructureType())) {
            List<StruVehicleTypeStructurePO> locationList = getLocationByVehicleTypeId(struVehicleTypeStructure.getVehicleTypeId());
            for (StruVehicleTypeStructurePO vehicleType : locationList) {
                if (vehicleType.getNameCn().equals(struVehicleTypeStructure.getNameCn())) {
                    if (vehicleType.getId().equals(struVehicleTypeStructure.getId())) {
                        break;
                    } else {
                        throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION, "车厢中文名不可重复！");
                    }
                }

            }
        }
        /*
            old
            List<String> locationNameCnList = locationList.stream().map(StruVehicleTypeStructurePO::getNameCn).collect(Collectors.toList());
            if (locationNameCnList.contains(struVehicleTypeStructure.getNameCn())) {
                throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION, "车厢中文名不可重复！");
            }*/
    }

    /**
     * 新增3D效果图
     * 2020年6月12日
     * 房明宽
     */
    @Transactional(rollbackFor = Exception.class)
    public Object add3DRenderings(SysFilePO sysFile,String id) {
        try {
            // 3D效果图上传限制
            List<String> temp = Arrays.asList(STRUCTURE_3D_PICTURE_FILE_FORMAT);
            if (!temp.contains(sysFile.getFormat().toLowerCase())) {
                throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION, "不支持上传该文件类型！");
            }
            // 保存文件到sys_file表,取到这个文件的id
            String fbxFileId = sysFileService.addSysFile(sysFile);
            //if (fbxFileId > 0){
                // 通过车辆构型表的id筛选，保存车辆构型和这个文件id的关系
                if (mapping.saveVehicleTypeStructureFileRelation(fbxFileId,id) > 0){
                    return "保存成功";
                }else {
                    throw new RestApiException(ExceptionInfoEnum.DATA_SELECTANDCHECK_EXCEPTION);
                }
            //}
           // throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        } catch (DataAccessException e) {
            logger.error("Method[add3DRenderings] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * 删除3D效果图
     * 2020年6月12日
     * 房明宽
     */
    @Transactional(rollbackFor = Exception.class)
    public Object delete3DRenderings(String id, String fileId) {
        try {
            // 删除文件(del_flag 改成 1)
            sysFileService.deleteSysFile(fileId);
            // 删除车辆构型的fbx文件的关系（把fbx_file_id改成null）
            int deleteFbxStatus = mapping.deleteVehicleTypeStructureFileRelation(id);
            if (deleteFbxStatus > 0){
                return "删除成功";
            }else {
                throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
            }
        } catch (DataAccessException e) {
            logger.error("Method[delete3DRenderings] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    /**
     * 替换3D效果图
     * 2020年6月22日
     * fangmingkuan
     */
    @Transactional(rollbackFor = Exception.class)
    public Object replace3DRenderings(SysFilePO sysFile, String id, String fileId) {
        try {
            // 3D效果图上传限制
            List<String> temp = Arrays.asList(STRUCTURE_3D_PICTURE_FILE_FORMAT);
            if (!temp.contains(sysFile.getFormat().toLowerCase())) {
                throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION, "不支持上传该文件类型！");
            }
            // 保存文件到sys_file表
            String fbxFileId = sysFileService.addSysFile(sysFile);
            //if (fbxFileId > 0){
                // 更新车辆构型和文件id的关系
                if (mapping.updateVehicleTypeStructureFileRelation(fbxFileId,id) > 0){
                    // 删除原有文件(del_flag 改为 1)
                    int deleteStatus = sysFileMapping.deleteSysFile(fileId);
                    if( deleteStatus == 1){
                        return "替换成功";
                    }else {
                        throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
                    }
                }else {
                    throw new RestApiException(ExceptionInfoEnum.DATA_SELECTANDCHECK_EXCEPTION);
                }
           // }else {
           //     throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
           // }
        }catch (DataAccessException e) {
            logger.error("Method[replace3DRenderings] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }
}
