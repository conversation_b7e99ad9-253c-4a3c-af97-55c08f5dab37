package cc.crrc.manage.service.stru;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.mapper.stru.DigitalVehicleStruMapping;
import cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO;
import cc.crrc.manage.pojo.monitor.RelayContactorLifePO;
import cc.crrc.manage.pojo.monitor.RelayContactorPO;
import cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO;
import cc.crrc.manage.pojo.mtc.MtcAutoFaultRecordVO;
import cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRuleVO;
import cc.crrc.manage.pojo.mtr.MtrSoftWareVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.*;

@Service
public class DigitalVehicleStruService {
    private static final Logger logger = LoggerFactory.getLogger(DigitalVehicleStruService.class);
    @Autowired
    private DigitalVehicleStruMapping mapping;


    public Object mechanismAlarmRuleListByStruCode(String vehicleCode, String structureCode) {
        List<MtcMechanismAlarmRuleVO> list = new ArrayList<>();
        try {
            list = mapping.mechanismAlarmRuleListByStruCode(vehicleCode, structureCode);
        } catch (Exception e) {
            logger.error("Method[mechanismAlarmRuleListByStruCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
        return list;
    }

    public Object ekbFaultTypeListByStruCode(String vehicleCode, String structureCode,
                                             int pageNumber, int pageSize) {
        try {
            // 分页
            PageHelper.startPage(pageNumber, pageSize);
            List<EkbFaultTypeDTO> faultTypeList = mapping.listEkbFaultType(vehicleCode, structureCode);
            return new PageInfo<>(faultTypeList);
        } catch (Exception e) {
            logger.error("Method[ekbFaultTypeListByStruCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object struCodeInfoByThreedCode(String threedCode, String vehicleCode) {
        String structureCode = "";
        try {
            structureCode = mapping.struCodeInfoByThreedCode(threedCode, vehicleCode);
        } catch (Exception e) {
            logger.error("Method[struCodeInfoByThreedCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
        return structureCode;
    }

    public Object buttonListByStruCode(String vehicleCode, String structureCode) {
        HashMap<String, Object> buttonList = new HashMap<>();
        try {
            //机理规则
            List<MtcMechanismAlarmRuleVO> list = mapping.mechanismAlarmRuleListByStruCode(vehicleCode, structureCode);
            if(list.size()>0){
                buttonList.put("MtcMechanism",true);
            }else {
                buttonList.put("MtcMechanism",false);
            }
            //故障字典
            List<EkbFaultTypeDTO> faultTypeList = mapping.listEkbFaultType(vehicleCode, structureCode);
            if(faultTypeList.size()>0){
                buttonList.put("EkbFaultType",true);
            }else {
                buttonList.put("EkbFaultType",false);
            }
            //软件版本履历
            List<MtrSoftWareVO> softWareInfoList = mapping.softWareInfoListForStru(vehicleCode, structureCode);
            if(softWareInfoList.size()>0){
                buttonList.put("MtrSoftWare",true);
            }else {
                buttonList.put("MtrSoftWare",false);
            }
            //寿命预测
            List<RelayContactorPO> relayContactorPOList  = mapping.relayContactorLifeListByStruCode(vehicleCode, structureCode);
            if(relayContactorPOList.size()>0){
                buttonList.put("RelayContactor",true);
            }else {
                buttonList.put("RelayContactor",false);
            }

        } catch (Exception e) {
            logger.error("Method[struCodeInfoByThreedCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
        return buttonList;
    }

    public Object mtrSoftWareListByStruCode(String vehicleCode, String structureCode,
                                            int pageNumber, int pageSize) {
        try {
            // 分页
            PageHelper.startPage(pageNumber, pageSize);
            List<MtrSoftWareVO> softWareInfoList  = mapping.softWareInfoListForStru(vehicleCode, structureCode);
            return new PageInfo<>(softWareInfoList);
        } catch (Exception e) {
            logger.error("Method[mtrSoftWareListByStruCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object relayContactorLifeListByStruCode(String vehicleCode, String structureCode, int pageNumber, int pageSize) {
        try {
            // 分页
            PageHelper.startPage(pageNumber, pageSize);
            List<RelayContactorPO> relayContactorPOList  = mapping.relayContactorLifeListByStruCode(vehicleCode, structureCode);
            return new PageInfo<>(relayContactorPOList);
        } catch (Exception e) {
            logger.error("Method[mtrSoftWareListByStruCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }









    public Object mechanismAlarmRuleGroupCount(String vehicleCode) {
        try {
            List<HashMap<String,Object>> list = mapping.mechanismAlarmRuleGroupCount(vehicleCode);
            //用来记录那些复合3d编码的数据  统一处理后进行添加
            List<HashMap<String, Object>> listAdd= new ArrayList<>();
            for(HashMap<String, Object> info:list){
                String threedCode = String.valueOf(info.get("threedCode"));
                Boolean status = threedCode.contains(",");
                if(status){
                    List threedCodeGroup = Arrays.asList(threedCode.split(","));
                    Long count = (Long) info.get("count");
                    for(Object threedcode:threedCodeGroup){
                        HashMap<String,Object> data = new HashMap<>();
                        data.put("threedCode",threedcode);
                        data.put("count",count);
                        listAdd.add(data);
                    }
                }else {
                    continue;
                }
            }
            list.addAll(listAdd);
            return list;
        } catch (Exception e) {
            logger.error("Method[mechanismAlarmRuleGroupCount] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object mechanismAlarmRuleListByVehicleCodeAndThreedCode(String vehicleCode, String threedCode) {
        try {
            List<MtcMechanismAlarmRuleVO> list = mapping.mechanismAlarmRuleListByVehicleCodeAndThreedCode(vehicleCode,threedCode);
            return list;
        } catch (Exception e) {
            logger.error("Method[mechanismAlarmRuleListByVehicleCodeAndThreedCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object mechanismAlarmRuleListByVehicleCode(String vehicleCode) {
        try {
            List<MtcMechanismAlarmRuleVO> list = mapping.mechanismAlarmRuleListByVehicleCode(vehicleCode);
            //首先声明一个需要返回的map
            HashMap<String,List<MtcMechanismAlarmRuleVO>> map = new HashMap<>();
            //将查询出的所有机理规则记录进行循环
            for(MtcMechanismAlarmRuleVO mtcMechanismAlarmRuleVO:list){
                //如果map中含有相同的strutureCode3d 则将本条数据放入到对应的分组中
                String strutureCode3d = mtcMechanismAlarmRuleVO.getThreedCode();
                if(map.containsKey(strutureCode3d)){
                    map.get(strutureCode3d).add(mtcMechanismAlarmRuleVO);
                }else {
                    //如果没有则新建单独的map分组
                    List<MtcMechanismAlarmRuleVO> groupList = new ArrayList<>();
                    groupList.add(mtcMechanismAlarmRuleVO);
                    map.put(strutureCode3d,groupList);
                }
            }
            return map;
        } catch (Exception e) {
            logger.error("Method[mechanismAlarmRuleListByVehicleCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object autoFaultGroupCountNumByVehicleCode(String vehicleCode) {
        try {
            List<HashMap<String,Object>> list = mapping.autoFaultGroupCountNumByVehicleCode(vehicleCode);
            //用来记录那些复合3d编码的数据  统一处理后进行添加
            List<HashMap<String, Object>> listAdd= new ArrayList<>();
            for(HashMap<String, Object> info:list){
                String threedCode = String.valueOf(info.get("threedCode"));
                Boolean status = threedCode.contains(",");
                if(status){
                    List threedCodeGroup = Arrays.asList(threedCode.split(","));
                    Long count = (Long) info.get("count");
                    for(Object threedcode:threedCodeGroup){
                        HashMap<String,Object> data = new HashMap<>();
                        data.put("threedCode",threedcode);
                        data.put("count",count);
                        listAdd.add(data);
                    }
                }else {
                    continue;
                }
            }
            list.addAll(listAdd);
            return list;
        } catch (Exception e) {
            logger.error("Method[mechanismAlarmRuleListByVehicleCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }
    public Object autoFaultGroupListByVehicleCodeAndThreedCode(String vehicleCode,String threedCode) {
        try {
            //查询故障数据
            List<MtcAutoFaultRecordVO> listMtcAutoFaultRecord = mapping.autoFaultGroupListByVehicleCodeAndThreedCode(vehicleCode,threedCode);
            return listMtcAutoFaultRecord;
        } catch (Exception e) {
            logger.error("Method[autoFaultGroupListByVehicleCodeAndThreedCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }


    public Object componentGroupListByVehicleCode(String vehicleCode) {
        try {
            List<HashMap<String,Object>> list = mapping.componentGroupListByVehicleCode(vehicleCode);
            //用来记录那些复合3d编码的数据  统一处理后进行添加
            List<HashMap<String, Object>> listAdd= new ArrayList<>();
            for(HashMap<String, Object> info:list){
                String threedCode = String.valueOf(info.get("threedCode"));
                Boolean status = threedCode.contains(",");
                if(status){
                    List threedCodeGroup = Arrays.asList(threedCode.split(","));
                    Long count = (Long) info.get("count");
                    for(Object threedcode:threedCodeGroup){
                        HashMap<String,Object> data = new HashMap<>();
                        data.put("threedCode",threedcode);
                        data.put("count",count);
                        listAdd.add(data);
                    }
                }else {
                    continue;
                }
            }
            list.addAll(listAdd);
            return list;
        } catch (Exception e) {
            logger.error("Method[componentGroupListByVehicleCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object componentListGroupListByVehicleCodeAndThreedCode(String vehicleCode, String threedCode) {
        try {
            List<RelayContactorPO> componentList  = mapping.componentListGroupListByVehicleCodeAndThreedCode(vehicleCode, threedCode);
            return componentList;
        } catch (Exception e) {
            logger.error("Method[componentListGroupListByVehicleCodeAndThreedCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }
    public Object relayContactorLifeGroupListByVehicleCode(String vehicleCode) {
        try {
            List<HashMap<String,Object>> list = mapping.relayContactorLifeGroupListByVehicleCode(vehicleCode);
            //用来记录那些复合3d编码的数据  统一处理后进行添加
            List<HashMap<String, Object>> listAdd= new ArrayList<>();
            for(HashMap<String, Object> info:list){
                String threedCode = String.valueOf(info.get("threedCode"));
                Boolean status = threedCode.contains(",");
                if(status){
                    List threedCodeGroup = Arrays.asList(threedCode.split(","));
                    Long count = (Long) info.get("count");
                    for(Object threedcode:threedCodeGroup){
                        HashMap<String,Object> data = new HashMap<>();
                        data.put("threedCode",threedcode);
                        data.put("count",count);
                        listAdd.add(data);
                    }
                }else {
                    continue;
                }
            }
            list.addAll(listAdd);
            return list;
        } catch (Exception e) {
            logger.error("Method[relayContactorLifeGroupListByVehicleCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object relayContactorLifeGroupListByVehicleCodeAndThreedCode(String vehicleCode, String threedCode) {
        try {
            List<RelayContactorPO> componentList  = mapping.relayContactorLifeGroupListByVehicleCodeAndThreedCode(vehicleCode, threedCode);
            return componentList;
        } catch (Exception e) {
            logger.error("Method[relayContactorLifeGroupListByVehicleCodeAndThreedCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

















    public Object componentListByStruCode(String vehicleCode, String structureCode, int pageNumber, int pageSize) {
        try {
            // 分页
            PageHelper.startPage(pageNumber, pageSize);
            List<RelayContactorPO> componentList  = mapping.componentListByStruCode(vehicleCode, structureCode);
            return new PageInfo<>(componentList);
        } catch (Exception e) {
            logger.error("Method[componentListByStruCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object listMtcAutoFaultRecordForStru(MtcAlarmWarningVO mtcAlarmWarningVO) {
        try {
            //分页，未传分页信息，默认不分页。
            PageHelper.startPage(mtcAlarmWarningVO.getPageNumber(), mtcAlarmWarningVO.getPageSize());
            //查询故障数据
            List<MtcAutoFaultRecordVO> listMtcAutoFaultRecord = mapping.listMtcAutoFaultRecordForStru(mtcAlarmWarningVO);
            return new PageInfo<>(listMtcAutoFaultRecord);
        } catch (DataAccessException e) {
            logger.error("Method[listMtcAutoFaultRecordForStru] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object listMtcAutoFaultRecordForStruByFaulitTypeKey(MtcAlarmWarningVO mtcAlarmWarningVO) {
        try {
            //分页，未传分页信息，默认不分页。
            PageHelper.startPage(mtcAlarmWarningVO.getPageNumber(), mtcAlarmWarningVO.getPageSize());
            //查询故障数据
            List<MtcAutoFaultRecordVO> listMtcAutoFaultRecord = mapping.listMtcAutoFaultRecordForStruByFaulitTypeKey(mtcAlarmWarningVO);
            return new PageInfo<>(listMtcAutoFaultRecord);
        } catch (DataAccessException e) {
            logger.error("Method[listMtcAutoFaultRecordForStru] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object mtrSoftWareListByStruCodeLite(String vehicleCode, String structureCode, int pageNumber, int pageSize) {
        try {
            // 分页
            PageHelper.startPage(pageNumber, pageSize);
            List<MtrSoftWareVO> softWareInfoList  = mapping.mtrSoftWareListByStruCodeLite(vehicleCode, structureCode);
            return new PageInfo<>(softWareInfoList);
        } catch (Exception e) {
            logger.error("Method[mtrSoftWareListByStruCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object mtrSoftWareListByStruCodeAndSignalName(String vehicleCode, String structureCode, String signalNameEn, int pageNumber, int pageSize) {
        try {
            // 分页
            PageHelper.startPage(pageNumber, pageSize);
            List<MtrSoftWareVO> softWareInfoList  = mapping.mtrSoftWareListByStruCodeAndSignalName(vehicleCode, structureCode,signalNameEn);
            return new PageInfo<>(softWareInfoList);
        } catch (Exception e) {
            logger.error("Method[mtrSoftWareListByStruCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }



}
