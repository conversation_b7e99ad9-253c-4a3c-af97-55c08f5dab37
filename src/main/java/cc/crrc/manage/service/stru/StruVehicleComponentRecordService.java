package cc.crrc.manage.service.stru;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.StringUtils;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.common.utils.excelUtiles.EasyPoiUtil;
import cc.crrc.manage.mapper.mtr.MtrVehicleMapping;
import cc.crrc.manage.mapper.stru.StruVehicleComponentRecordMapping;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import cc.crrc.manage.pojo.stru.StruVehicleComponentRecordExcelVO;
import cc.crrc.manage.pojo.stru.StruVehicleComponentRecordVO;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 车辆构型-车辆履历 服务层
 *
 * <AUTHOR> guoyang
 * 2021/1/14
 **/
@Service
public class StruVehicleComponentRecordService {
    private final Logger logger = LoggerFactory.getLogger(StruVehicleComponentRecordService.class);

    @Autowired
    private StruVehicleComponentRecordMapping struVehicleComponentRecordMapping;
    @Autowired
    private MtrVehicleMapping mtrVehicleMapping;


    // excel相关常量
    private final int NARROW_WIDTH = 5 * 256;
    private final int MEDIUM_WIDTH = 15 * 256;
    private final int BROAD_WIDTH = 35 * 256;
    private final int DATE_WIDTH = 30 * 256;
    private final int NAME_WIDTH = 8 * 256;
    private final short HEIGHT = 7;
    private final int VERIFICATION_COLUMN = 10;
    private final int VERIFICATION_ROW = 1;
    private final String VERIFICATION_STR = "verification";
    private final String PASSWORD = "crrcdt2021";


    /**
     * 将用户上传的excel文件中的履历数据导入至数据库中
     *
     * @param file 上传文件
     * <AUTHOR> GuoYang
     * 2021/1/21
     **/
    @Transactional(rollbackFor = Exception.class)
    public void loadExcelData2DB(MultipartFile file, String vehicleCode) {
        String verification;
        List<StruVehicleComponentRecordExcelVO> importList;
        try {
            importList = EasyPoiUtil.importExcel(file, 0, 1, StruVehicleComponentRecordExcelVO.class);
        } catch (Exception e) {
            // 单元格对象化失败
            logger.error("Method[loadExcelData2DB] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.FILE_IMPORT_EXCEPTION);
        }

        try {
            verification = importList.get(VERIFICATION_ROW - 1).getVerification();
        } catch (NullPointerException e) {
            // 获取不到校验单元格
            logger.error("Method[loadExcelData2DB] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.FILE_TEMPLATE_ILLEGAL);
        }

        if (!VERIFICATION_STR.equals(verification)) {
            // 校验位不符合期望
            throw new RestApiException(ExceptionInfoEnum.FILE_TEMPLATE_ILLEGAL);
        }

        String targetVehicleCode = importList.get(0).getVehicleCode();
        if (!vehicleCode.equals(targetVehicleCode)) {
            // 车辆编号不匹配
            throw new RestApiException(ExceptionInfoEnum.FILE_IMPORT_EXCEPTION, "车辆编号不匹配！");
        }

        ArrayList<String> errorList = validExcelData(importList,vehicleCode);
        if (!errorList.isEmpty()){
            throw new RestApiException(ExceptionInfoEnum.FILE_DATA_ILLEGAL, StringUtils.list2String(errorList));
        }

        try {
            for (StruVehicleComponentRecordExcelVO s:importList) {
                //雪花算法添加主键
                s.setId(PrimaryKeyGenerator.generatorId());
            }
            String userId = UserUtils.getUserId();
            struVehicleComponentRecordMapping.batchAddRecord(importList, userId);
        } catch (DataAccessException e) {
            logger.error("Method[loadExcelData2DB] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION, "保存数据异常或无导入数据");
        }
    }

    /**
     * 校验导入的部件履历数据是否符合规则
     *
     * @param importList 待导入的数据列表
     * @param vehicleCode 车辆编码
     * @return java.util.ArrayList<java.lang.String>
     * <AUTHOR> GuoYang
     * 2021/3/30
     */
    private ArrayList<String> validExcelData(List<StruVehicleComponentRecordExcelVO> importList, String vehicleCode) {
        ArrayList<String> errorList = new ArrayList<>();
        MtrVehiclePO mtrVehiclePO = mtrVehicleMapping.getVehicleByCode(vehicleCode);
        String structureCodeRoot = importList.get(0).getStructureCode();
        List<StruVehicleComponentRecordVO> currentRecordList = getVehicleComponentRecordList(mtrVehiclePO.getId(), structureCodeRoot);

        // 按当前算法，必须排序后再比较
        currentRecordList.sort(Comparator.comparing(StruVehicleComponentRecordVO::getCurrentComponentCode)
                .thenComparing(StruVehicleComponentRecordVO::getExecuteTime));
        importList = importList.stream().filter(s -> s.getCurrentComponentCode() != null)
                .sorted(Comparator.comparing(StruVehicleComponentRecordExcelVO::getCurrentComponentCode, Comparator.nullsLast(String::compareTo))
                .thenComparing(StruVehicleComponentRecordExcelVO::getExecuteTime, Comparator.nullsLast(String::compareTo)))
                .collect(Collectors.toList());

        int currentRecordIndex = 0;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        sdf.setLenient(false);
        // 校验数据
        for (int i = 0; i < importList.size(); i++) {
            StruVehicleComponentRecordExcelVO recordExcelVO = importList.get(i);
            String structureCode = recordExcelVO.getStructureCode();

            // 先校验更换前部件编码和更换时间，否则判断两行数据相同无意义
            String currentComponentCode = recordExcelVO.getCurrentComponentCode();
            if (StringUtils.isEmpty(currentComponentCode)) {
                continue;
            }
            if (currentComponentCode.length() > 64) {
                errorList.add("构型编码为[" + structureCode + "]的数据行[更换前部件编码]字段长度超过64位。");
            }
            String executeTime = recordExcelVO.getExecuteTime();
            if (StringUtils.isEmpty(executeTime)) {
                errorList.add("构型编码为[" + structureCode + "]的数据行[更换时间]字段为空。");
                continue;
            }
            Date executeTimeDate;
            try {
                executeTimeDate = sdf.parse(executeTime);
            } catch (ParseException | NullPointerException e) {
                errorList.add("构型编码为[" + structureCode + "]的数据行[更换时间]字段日期格式不正确。");
                continue;
            }

            // 校验导入数据中是否有重复记录
            if (i < importList.size() - 1) {
                StruVehicleComponentRecordExcelVO nextRecordExcelVO = importList.get(i + 1);
                String nextCurrentComponentCode = nextRecordExcelVO.getCurrentComponentCode();
                String nextExecuteTime = nextRecordExcelVO.getExecuteTime();
                if (nextCurrentComponentCode.equals(currentComponentCode) && nextExecuteTime.equals(executeTime)) {
                    errorList.add("更换后部件编码为[" + currentComponentCode + "]的数据重复。");
                    continue;
                }
            }

            String componentPosition = recordExcelVO.getComponentPosition();
            if (componentPosition != null && componentPosition.length() > 64) {
                errorList.add("构型编码为[" + structureCode + "]的数据行[部件位置]字段长度超过64位。");
            }
            String previousComponentCode = recordExcelVO.getPreviousComponentCode();
            if (previousComponentCode != null && previousComponentCode.length() > 64) {
                errorList.add("构型编码为[" + structureCode + "]的数据行[更换后部件编码]字段长度超过64位。");
            }
            String executer = recordExcelVO.getExecuter();
            if (executer != null && executer.length() > 64) {
                errorList.add("构型编码为[" + structureCode + "]的数据行[更换人]字段长度超过64位。");
            }
            // 校验导入数据，是否已存在相同记录
            for (int j = currentRecordIndex; currentRecordIndex < currentRecordList.size(); currentRecordIndex++) {
                String nowCurrentComponentCode = currentRecordList.get(currentRecordIndex).getCurrentComponentCode();
                Date nowExecuteTime = currentRecordList.get(currentRecordIndex).getExecuteTime();
                int codeCmp = currentComponentCode.compareTo(nowCurrentComponentCode);
                int timeCmp = executeTimeDate.compareTo(nowExecuteTime);
                boolean nextImportRawFlag = codeCmp < 0 || (codeCmp == 0 && timeCmp < 0);
                if (codeCmp == 0 && timeCmp == 0) {
                    errorList.add("构型编码为[" + structureCode + "]的数据行的更换记录已存在。");
                    break;
                }
                if (nextImportRawFlag) {
                    break;
                }
            }
        }
        return errorList;
    }

    /**
     * @return void
     * <AUTHOR> kangjian
     * @Description 下载履历模板Excel
     * @Date 2021/1/15
     **/
    public void downloadExcel(String vehicleId, HttpServletResponse response) {
        List<StruVehicleComponentRecordExcelVO> list = getVehicleComponentRecordForExcel(vehicleId);
        if (list.isEmpty()){
            throw new RestApiException(ExceptionInfoEnum.FILE_EXPORT_EXCEPTION, "该车辆暂无构型，无法下载相应模板。");
        }
        // 设置行高
        ExportParams exportParams = new ExportParams();
        exportParams.setHeight(HEIGHT);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, StruVehicleComponentRecordExcelVO.class, list);
        Sheet sheet = workbook.getSheetAt(0);
        // 设置列宽
        for (int i = 0; i < VERIFICATION_COLUMN; i++) {
            int temp_width;
            if (i == 2 || i == 3) {
                temp_width = BROAD_WIDTH;
            } else if (i == 8) {
                temp_width = NAME_WIDTH;
            } else if (i == 9) {
                temp_width = DATE_WIDTH;
            } else {
                temp_width = MEDIUM_WIDTH;
            }
            sheet.setColumnWidth(i, temp_width);
        }
        // 标识单元格 第一行第十列
        sheet.getRow(VERIFICATION_ROW).createCell(VERIFICATION_COLUMN).setCellValue(VERIFICATION_STR);
        // 单元格格式 是否有锁 垂直居中
        CellStyle lockStyle = workbook.createCellStyle();
        lockStyle.setLocked(true);
        lockStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        CellStyle unlockStyle = workbook.createCellStyle();
        unlockStyle.setLocked(false);
        unlockStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("TEXT"));
        unlockStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 锁定前四列以及第一行
        int lastRowNum = sheet.getLastRowNum();
        for (int i = 0; i < lastRowNum + 1; i++) {
            for (int j = 0; j < VERIFICATION_COLUMN; j++) {
                Cell cell = sheet.getRow(i).getCell(j);
                if (i == 0) {
                    cell.setCellStyle(lockStyle);
                    continue;
                }
                if (j < 5) {
                    cell.setCellStyle(lockStyle);
                } else {
                    cell.setCellStyle(unlockStyle);
                }
            }
        }
        // sheet添加保护，一定要，否则还是可以编辑的
        sheet.protectSheet(PASSWORD);
        try {
            //创建文件名
            String fileName = "车辆履历模板.xls";
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.reset();
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            logger.error("Method[downloadExcel] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.FILE_EXPORT_EXCEPTION);
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * <AUTHOR> kangjian
     * @Description 从数据库查询车辆构型，用于导出车辆履历模板excel
     * @Date 2021/1/19
     **/
    public List<StruVehicleComponentRecordExcelVO> getVehicleComponentRecordForExcel(String vehicleId) {
        List<StruVehicleComponentRecordExcelVO> list = struVehicleComponentRecordMapping.getVehicleComponentRecordForExcel(vehicleId);
        for (StruVehicleComponentRecordExcelVO struVehicleComponentRecordExcelVO : list) {
            if (struVehicleComponentRecordExcelVO.getStructureCode().split("/").length == 3) {
                String nameCn = struVehicleComponentRecordExcelVO.getNameCn();
                String structureCode = struVehicleComponentRecordExcelVO.getStructureCode();
                struVehicleComponentRecordExcelVO.setSubSystemNameCn(nameCn);
                for (StruVehicleComponentRecordExcelVO vehicleComponentRecordExcelVO : list) {
                    if (vehicleComponentRecordExcelVO.getStructureCode().split("/").length > 3 && vehicleComponentRecordExcelVO.getStructureCode().startsWith(structureCode)) {
                        vehicleComponentRecordExcelVO.setSubSystemNameCn(nameCn);
                    }
                }
            }
        }
        return list;
    }


    /**
     * 根据车辆id和构型编码 查询该车辆构型下的所有部件履历
     *
     * @param vehicleId     车辆id
     * @param structureCode 构型编码
     * @return java.util.List<cc.crrc.manage.pojo.stru.StruVehicleComponentRecordVO>
     * <AUTHOR> GuoYang
     * 2021/1/21
     **/
    public List<StruVehicleComponentRecordVO> getVehicleComponentRecordList(String vehicleId, String structureCode) {
        try {
            return struVehicleComponentRecordMapping.getVehicleComponentRecordList(vehicleId, structureCode);
        } catch (DataAccessException e) {
            logger.error("Method[getVehicleComponentRecordList] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

}
