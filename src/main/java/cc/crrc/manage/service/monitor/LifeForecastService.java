package cc.crrc.manage.service.monitor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import cc.crrc.manage.common.annotation.ParamReplace;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.mtr.MtrVehicleMapping;
import cc.crrc.manage.pojo.monitor.LifeForecastConfigDTO;
import cc.crrc.manage.pojo.monitor.RelayContactorLifePO;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import com.github.pagehelper.PageHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageInfo;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.mapper.monitor.LifeForecastMapping;
import cc.crrc.manage.pojo.monitor.LifeForecastDTO;
import cc.crrc.manage.pojo.monitor.RelayContactorPO;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Date 2020-12-22 09:13:03
 */
@Service
public class LifeForecastService {

    @Autowired
    private LifeForecastMapping lifeForecastMapping;
    @Autowired
    private MtrVehicleMapping mtrVehicleMapping;

    private final Logger logger = LoggerFactory.getLogger(LifeForecastService.class);

   /**
    * @Description 查询接触器和继电器的寿命
    * @Name findRelayContactorBys
    * @Param lifeForecastDTO
    * @Return PageInfo<LifeForecastDTO>
    * <AUTHOR>
    * @Date 2020-12-22 09:13:09
    * @return
    */
   public PageInfo<RelayContactorPO> findRelayContactorBys(LifeForecastDTO lifeForecastDTO) {
       try {
           int currentPage = lifeForecastDTO.getPageNumber();
           int pageSize = lifeForecastDTO.getPageSize();
           PageHelper.startPage(currentPage, pageSize);
           //查询寿命预测数据
           List<RelayContactorPO> relayContactorPOList = lifeForecastMapping.findRelayContactorBys(lifeForecastDTO);
           //分页
           PageInfo<RelayContactorPO> pageInfo = new PageInfo<>(relayContactorPOList);
           return pageInfo;
       }catch(DataAccessException e){
           logger.error("Method[findRelayContactorBys] Error:{}", e.getMessage());
           throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
       }
   }

    @Transactional(rollbackFor = Exception.class)
    public Object initRelayContactorLifes(String lineId) {
        int insertCount = 0;
       try {
           //1 根据线路id 查询该线路下有多少个车辆
           List<MtrVehiclePO> list = mtrVehicleMapping.listVehicleForRelayContactor(lineId);

           //2-前期数据处理 查询生命周期表里已经被初始化过得数据集合（vehicle_code和relay_contactor_id）
           List<HashMap<String,String>> relayContactorLifeExistList = lifeForecastMapping.relayContactorExistList(lineId);

           //2 根据查询的车辆list 循环对每辆车初始化relay_contactor_life数据
           for(MtrVehiclePO mtrVehiclePO:list){
               //获取车辆类型id
               String vehicleTypeId = mtrVehiclePO.getVehicleTypeCode();
               //2-1 获取所有的继电器接触器信息 循环存入relayContactorLifePOS中 统一对单个车辆新增数据
               List<RelayContactorPO> relayContactorPOList = lifeForecastMapping.listRelayContactor(vehicleTypeId);

               // 根据relayContactorLifeExistList数据把已存在的数据 剔除掉 不存在的数据录入2021-11-30 lixin
               List<RelayContactorLifePO>relayContactorLifePOS = new ArrayList<>();
               for(RelayContactorPO relayContactorPO:relayContactorPOList){
                   String vehicleCode = mtrVehiclePO.getVehicleCode();
                   String relayContactorId = relayContactorPO.getId();
                   HashMap<String,String> map = new HashMap<>();
                   map.put("vehicleCode",vehicleCode);
                   map.put("relayContactorId",relayContactorId);
                   if(relayContactorLifeExistList.contains(map)){
                       continue;
                   }else {
                       //赋值relayContactorLifePO
                       RelayContactorLifePO relayContactorLifePO =  new RelayContactorLifePO();
                       relayContactorLifePO.setRelayContactorId(relayContactorPO.getId());
                       relayContactorLifePO.setRemainingLife(relayContactorPO.getElectricalLife());
                       relayContactorLifePO.setOperationCnt(0L);
                       relayContactorLifePO.setVehicleCode(mtrVehiclePO.getVehicleCode());
                       relayContactorLifePO.setValid(1);
                       //2021-12-13 19:09 lixin 修改
                       relayContactorLifePO.setManufacturerId(relayContactorPO.getManufacturerId());
                       relayContactorLifePO.setProductNumber(relayContactorPO.getProductNumber());
                       relayContactorLifePO.setInstaller(UserUtils.getName());
                       //todo 需要在初始化时候将位置信息填充 AssemblyLocation() 贵阳2号线暂时没有
                       //切割structureCode排在第二位的一定是车厢
                       relayContactorLifePO.setAssemblyLocation(relayContactorPO.getStructureCode().split("/")[1]);
                       //数据累加
                       relayContactorLifePOS.add(relayContactorLifePO);
                   }
               }
               if(!relayContactorLifePOS.isEmpty()){
                    insertCount = lifeForecastMapping.insertRelayContactorLife(relayContactorLifePOS);
                    if(insertCount>0){
                        insertCount = relayContactorLifePOS.size();
                    }
               }
           }

       }catch (Exception e){
           logger.error("Method[findRelayContactorBys] Error:{}", e.getMessage());
           throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
       }
       return "初始化成功:"+insertCount+" 条记录！";
    }

    public Object replaceRelay(String id) {
        try{
            String userName = UserUtils.getName();
            RelayContactorPO rPo = lifeForecastMapping.getById(id);
            rPo.setSnowId(PrimaryKeyGenerator.generatorId());
            if(rPo.getReplaceTimes() == null){
                //无更换次数时，更换次数默认0
                Long  replaceTimes = 0L;
                lifeForecastMapping.addReplaceTimes(id, userName,replaceTimes);
            }
            lifeForecastMapping.addRelayHistory(rPo);
            lifeForecastMapping.updRelayContactorLife(id, userName);
            return "success";
        }catch(DataAccessException e){
            logger.error("Method[findRelayContactorBys] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }


    public Object relayContactorLifesHistory(String id) {
        try{
            List<RelayContactorPO> lifeForecast = lifeForecastMapping.relayContactorLifesHistory(id);
            return lifeForecast;
        }catch(DataAccessException e){
            logger.error("Method[relayContactorLifesHistory] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Object insertRelayContactorInfo(RelayContactorPO relayContactorPO) {
        try {
            //重复检验
            int repeatCount = lifeForecastMapping.repeatCount(relayContactorPO);
            if (repeatCount > 0) {
                throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION, "存在相同数据，请重新输入！");
            }
            // 添加主键
            relayContactorPO.setId(PrimaryKeyGenerator.generatorId());
            // 添加创建人信息
            relayContactorPO.setCreateBy(UserUtils.getUserId());
            //添加修改人信息
            relayContactorPO.setModifyBy(UserUtils.getUserId());
            //如果为普通部件 默认初始给一个值
            if(relayContactorPO.getType().isEmpty()){
                relayContactorPO.setType("normal");
            }
            int count = lifeForecastMapping.insertRelayContactorInfo(relayContactorPO);
            if (count != 1) {
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
            }
            return "success";
        } catch (DataAccessException e) {
            logger.error("Method[insertRelayContactorInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    @Transactional(rollbackFor=Exception.class)
    public Object updateRelayContactorInfo(RelayContactorPO relayContactorPO) {
        try {
            //todo 更新时候缺少重复校验
            // 已经在实体类层面增加切面查重-lixin

            // 更新信息
            relayContactorPO.setModifyBy(UserUtils.getUserId());
            int count = lifeForecastMapping.updateRelayContactorInfo(relayContactorPO);
            if (count != 1) {
                throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
            }
            return "success";
        } catch (DataAccessException e) {
            logger.error("Method[updateRelayContactorInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Object delRelayContactorInfo(String id) {
        try {
            String userId = UserUtils.getUserId();
            int count = lifeForecastMapping.delRelayContactorInfo(id, userId);
            if (count != 1) {
                throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
            }
            return "success";
        } catch (DataAccessException e) {
            logger.error("Method[delRelayContactorInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    @ParamReplace(param = {"nameCn", "structureCode"}, type = LifeForecastConfigDTO.class)
    public Object findRelayContactorConfigList(LifeForecastConfigDTO configDTO) {
        try {
            int currentPage = configDTO.getPageNumber();
            int pageSize = configDTO.getPageSize();
            PageHelper.startPage(currentPage, pageSize);
            //查询寿命预测配置数据
            List<RelayContactorPO> relayContactorConfigList = lifeForecastMapping.findRelayContactorConfigList(configDTO);
            //分页
            PageInfo<RelayContactorPO> pageInfo = new PageInfo<>(relayContactorConfigList);
            return pageInfo;
        }catch(DataAccessException e){
            logger.error("Method[findRelayContactorConfigList] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object updateRelayContactorLife(RelayContactorLifePO relayContactorLifePO) {
        try {
            // 更新信息
            relayContactorLifePO.setInstaller(UserUtils.getName());
            int count = lifeForecastMapping.updateRelayContactorLife(relayContactorLifePO);
            if (count != 1) {
                throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
            }
            return count;
        }catch(DataAccessException e){
            logger.error("Method[findRelayContactorConfigList] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }
}