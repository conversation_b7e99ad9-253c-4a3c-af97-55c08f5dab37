package cc.crrc.manage.service.monitor;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.monitor.WashVehiclePeriodMapping;
import cc.crrc.manage.pojo.monitor.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 洗车周期
 * @Date 2021/9/17
 **/
@Service
public class WashVehiclePeriodService {

    @Autowired
    private WashVehiclePeriodMapping washVehiclePeriodMapping;

    private final Logger logger = LoggerFactory.getLogger(WashVehiclePeriodService.class);

    public Object addWashVehicleInfo(WorkPeriodPO workPeriodPO) {
        try {
            int count = washVehiclePeriodMapping.checkVehicleUnique(workPeriodPO);
            if (count > 0) {
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "已存在相同车号的记录!");
            }
            workPeriodPO.setId(PrimaryKeyGenerator.generatorId());
            workPeriodPO.setCreateBy(UserUtils.getUserId());
            workPeriodPO.setOperator(UserUtils.getName());
            workPeriodPO.setUniqueId(PrimaryKeyGenerator.generatorId());
            workPeriodPO.setWorkType("washVehicle");
            return washVehiclePeriodMapping.addWashVehicleInfo(workPeriodPO);
        } catch (DataAccessException e) {
            logger.error("Method[addWashVehicleInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    public Object updateWashVehicleInfo(WorkPeriodPO workPeriodPO) {
        try {
            String id = workPeriodPO.getId();
            WorkPeriodPO info = washVehiclePeriodMapping.getInfoById(id);
            Date oldTime = info.getOperateTime();
            Date newTime = workPeriodPO.getOperateTime();
            if (newTime.before(oldTime)) {
                throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION.getErrorCode(), "洗车时间应在上次洗车时间之后！");
            }
            info.setId(PrimaryKeyGenerator.generatorId());
            info.setMode(workPeriodPO.getMode());
            info.setPeriod(workPeriodPO.getPeriod());
            info.setOperateTime(workPeriodPO.getOperateTime());
            info.setWarningTime(workPeriodPO.getWarningTime());
            info.setRemark(workPeriodPO.getRemark());
            info.setOperator(UserUtils.getName());
            info.setCreateBy(UserUtils.getUserId());
            int changeInfo = washVehiclePeriodMapping.addWashVehicleInfo(info);
            workPeriodPO.setModifyBy(UserUtils.getUserId());
            int history = washVehiclePeriodMapping.washVehicleHistory(workPeriodPO);
            if (changeInfo != 1 || history != 1) {
                return "failed";
            }
        } catch (DataAccessException e) {
            logger.error("Method[updateWashVehicleInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
        return "success";
    }

    public Object listWashVehicleInfo(WorkPeriodVO workPeriodVO) {
        try {
            int currentPage = workPeriodVO.getPageNumber();
            int pageSize = workPeriodVO.getPageSize();
            PageHelper.startPage(currentPage, pageSize);
            Double percent = (double) 0;
            //查询洗车周期数据
            List<WorkPeriodVO> workPeriodVOList = washVehiclePeriodMapping.listWashVehicleInfo(workPeriodVO);
            for (WorkPeriodVO periodVO : workPeriodVOList) {
                Date operateTime = periodVO.getOperateTime();
                Date warningTime = periodVO.getWarningTime();
                long startTime = operateTime.getTime();
                long endTime = warningTime.getTime();
                long currentTimeMillis = System.currentTimeMillis();
                percent = (double) (currentTimeMillis - startTime)*100/(endTime - startTime);
                String[] value = String.valueOf(percent).split("\\.");
                periodVO.setPercent(value[0]);
            }
            PageInfo<WorkPeriodVO> pageInfo = new PageInfo<>(workPeriodVOList);
            return pageInfo;
        }catch(DataAccessException e){
            logger.error("Method[listWashVehicleInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object listWashVehicleHistory(String id) {
        try {
            return washVehiclePeriodMapping.listWashVehicleHistory(id);
        }catch(DataAccessException e){
            logger.error("Method[listWashVehicleHistory] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    @Transactional(rollbackFor=Exception.class)
    public Object updateCurrentInfo(WorkPeriodPO workPeriodPO) {
        try {
            workPeriodPO.setModifyBy(UserUtils.getUserId());
            int successCount = washVehiclePeriodMapping.updateCurrentInfo(workPeriodPO);
            if (successCount != 1) {
                throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
            }
            return successCount;
        }catch(DataAccessException e){
            logger.error("Method[updateCurrentInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Object deleteWashVehicleInfo(String ids) {
        try {
            String[] idArr = ids.split(",");
            for (int i = 0; i < idArr.length; i++) {
                int count = washVehiclePeriodMapping.deleteWashVehicleInfo(idArr[i], UserUtils.getUserId());
                List<WorkPeriodVO> history = washVehiclePeriodMapping.listWashVehicleHistory(idArr[i]);
                if (count != history.size()) {
                    throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
                }
            }
            return "success";
        } catch (DataAccessException e) {
            logger.error("Method[deleteWashVehicleInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }
}
