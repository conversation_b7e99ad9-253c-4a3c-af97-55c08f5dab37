package cc.crrc.manage.service.monitor;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.monitor.OilChangePeriodMapping;
import cc.crrc.manage.pojo.monitor.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

/**
 * @<PERSON> <PERSON>
 * @Description 换油周期
 * @Date 2021/9/17
 **/
@Service
public class OilChangePeriodService {

    @Autowired
    private OilChangePeriodMapping oilChangePeriodMapping;

    private final Logger logger = LoggerFactory.getLogger(OilChangePeriodService.class);


    @Transactional(rollbackFor=Exception.class)
    public Object addOilChangeInfo(WorkPeriodPO workPeriodPO) {
        try {
            int count = oilChangePeriodMapping.checkComponentUnique(workPeriodPO);
            if (count > 0) {
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "已存在相同部件的记录!");
            }
            workPeriodPO.setId(PrimaryKeyGenerator.generatorId());
            workPeriodPO.setCreateBy(UserUtils.getUserId());
            workPeriodPO.setOperator(UserUtils.getName());
            workPeriodPO.setUniqueId(PrimaryKeyGenerator.generatorId());
            workPeriodPO.setWorkType("oilChange");
            workPeriodPO.setModifyBy(UserUtils.getUserId());

            //初始里程相关逻辑lixin
            long initialMileage = getMileageByTargetTime(workPeriodPO.getOperateTime(),workPeriodPO.getVehicleCode());
            workPeriodPO.setInitialMileage(initialMileage);

            return oilChangePeriodMapping.addOilChangeInfo(workPeriodPO);
        } catch (DataAccessException e) {
            logger.error("Method[addOilChangeInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    @Transactional(rollbackFor=Exception.class)
    public Object updateOilChangeInfo(WorkPeriodPO workPeriodPO) {
        try {
            String id = workPeriodPO.getId();
            WorkPeriodPO info = oilChangePeriodMapping.getInfoById(id);
            Date oldTime = info.getOperateTime();
            Date newTime = workPeriodPO.getOperateTime();
            if (newTime.before(oldTime)) {
                throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION.getErrorCode(), "更换时间应在上次更换时间之后！");
            }
            info.setId(PrimaryKeyGenerator.generatorId());
            info.setPeriod(workPeriodPO.getPeriod());
            info.setOperateTime(workPeriodPO.getOperateTime());
            info.setWarningTime(workPeriodPO.getWarningTime());
            info.setRemark(workPeriodPO.getRemark());
            info.setOperator(UserUtils.getName());
            info.setCreateBy(UserUtils.getUserId());

            //运行里程相关逻辑lixin 两个里程计算顺序很重要 运行里程先算 因为下一步初始里程被更新了
            info.setWarningMileage(workPeriodPO.getWarningMileage());
            long Mileage = getMileageByTargetTime(newTime,workPeriodPO.getVehicleCode());
            long runningMileage = Mileage-info.getInitialMileage();
            workPeriodPO.setRunningMileage(runningMileage);
            //更换后 新的初始里程相关逻辑lixin
            if(newTime.compareTo(oldTime)!=0) {
                long initialMileage = Mileage;
                info.setInitialMileage(initialMileage);
            }

            int changeInfo = oilChangePeriodMapping.addOilChangeInfo(info);
            workPeriodPO.setModifyBy(UserUtils.getUserId());


            int history = oilChangePeriodMapping.oilChangeHistory(workPeriodPO);
            if (changeInfo != 1 || history != 1) {
                return "failed";
            }
        } catch (DataAccessException e) {
            logger.error("Method[updateOilChangeInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
        return "success";
    }

    public Object listOilChangeInfo(WorkPeriodVO workPeriodVO) {
        try {
            int currentPage = workPeriodVO.getPageNumber();
            int pageSize = workPeriodVO.getPageSize();
            PageHelper.startPage(currentPage, pageSize);
//            Double percent = (double) 0;
            //查询换油周期数据
            List<WorkPeriodVO> workPeriodVOList = oilChangePeriodMapping.listOilChangeInfo(workPeriodVO);
//            for (WorkPeriodVO periodVO : workPeriodVOList) {
//                Date operateTime = periodVO.getOperateTime();
//                Date warningTime = periodVO.getWarningTime();
//                long startTime = operateTime.getTime();
//                long endTime = warningTime.getTime();
//                long currentTimeMillis = System.currentTimeMillis();
//                percent = (double) (currentTimeMillis - startTime)*100/(endTime - startTime);
//                String[] value = String.valueOf(percent).split("\\.");
//                periodVO.setPercent(value[0]);
//            }
            PageInfo<WorkPeriodVO> pageInfo = new PageInfo<>(workPeriodVOList);
            return pageInfo;
        }catch(DataAccessException e){
            logger.error("Method[listOilChangeInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object listOilChangeHistory(String id) {
        try {
            return oilChangePeriodMapping.listOilChangeHistory(id);
        }catch(DataAccessException e){
            logger.error("Method[listOilChangeHistory] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    @Transactional(rollbackFor=Exception.class)
    public Object updateCurrentInfo(WorkPeriodPO workPeriodPO) {
        try {

            //编辑接口 如果操作时间变更，初始里程跟着变更相关逻辑lixin
            WorkPeriodPO info = oilChangePeriodMapping.getInfoById(workPeriodPO.getId());
            Date oldTime = info.getOperateTime();
            Date newTime = workPeriodPO.getOperateTime();
            if(newTime.compareTo(oldTime)!=0) {
                long initialMileage = getMileageByTargetTime(newTime,workPeriodPO.getVehicleCode());
                workPeriodPO.setInitialMileage(initialMileage);
            }

            workPeriodPO.setModifyBy(UserUtils.getUserId());
            int successCount = oilChangePeriodMapping.updateCurrentInfo(workPeriodPO);
            if (successCount != 1) {
                throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
            }
            return successCount;
        }catch(DataAccessException e){
            logger.error("Method[updateCurrentInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Object deleteOilChangeInfo(String ids) {
        try {
            String[] idArr = ids.split(",");
            for (int i = 0; i < idArr.length; i++) {
                int count = oilChangePeriodMapping.deleteOilChangeInfo(idArr[i], UserUtils.getUserId());
                List<WorkPeriodVO> history = oilChangePeriodMapping.listOilChangeHistory(idArr[i]);
                if (count != history.size()) {
                    throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
                }
            }
            return "success";
        } catch (DataAccessException e) {
            logger.error("Method[deleteOilChangeInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }


    /**
     * <AUTHOR>
     * @date 2022/3/8 09:01
     * @description 根据targetTime（目标时间）查询小于目标时间 的最大里程数
     */
    public long getMileageByTargetTime(Date targetTime,String vehicleCode){
        try {
            Long initialMileage = oilChangePeriodMapping.getMileageByTargetTime(targetTime,vehicleCode);
            if(null==initialMileage){
                initialMileage = 0L;
            }
            return initialMileage;
        }catch (Exception e){
            logger.error("Method[getMileageByTargetTime] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }

    }
}
