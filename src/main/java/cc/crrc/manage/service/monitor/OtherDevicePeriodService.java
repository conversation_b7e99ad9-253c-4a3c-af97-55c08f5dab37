package cc.crrc.manage.service.monitor;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.monitor.OtherDevicePeriodMapping;
import cc.crrc.manage.pojo.monitor.WorkPeriodPO;
import cc.crrc.manage.pojo.monitor.WorkPeriodVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 其他设备周期
 * @Date 2022/2/17
 **/
@Service
public class OtherDevicePeriodService {

    @Autowired
    private OtherDevicePeriodMapping otherDevicePeriodMapping;

    private final Logger logger = LoggerFactory.getLogger(OtherDevicePeriodService.class);


    @Transactional(rollbackFor=Exception.class)
    public Object addOtherDeviceInfo(WorkPeriodPO workPeriodPO) {
        try {
            int count = otherDevicePeriodMapping.checkComponentUnique(workPeriodPO);
            if (count > 0) {
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "已存在相同设备的记录!");
            }
            workPeriodPO.setId(PrimaryKeyGenerator.generatorId());
            workPeriodPO.setCreateBy(UserUtils.getUserId());
            workPeriodPO.setOperator(UserUtils.getName());
            workPeriodPO.setUniqueId(PrimaryKeyGenerator.generatorId());
            workPeriodPO.setWorkType("otherDevice");
            return otherDevicePeriodMapping.addOtherDeviceInfo(workPeriodPO);
        } catch (DataAccessException e) {
            logger.error("Method[addOtherDeviceInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    @Transactional(rollbackFor=Exception.class)
    public Object updateOtherDeviceInfo(WorkPeriodPO workPeriodPO) {
        try {
            String id = workPeriodPO.getId();
            WorkPeriodPO info = otherDevicePeriodMapping.getInfoById(id);
            Date oldTime = info.getOperateTime();
            Date newTime = workPeriodPO.getOperateTime();
            if (newTime.before(oldTime)) {
                throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION.getErrorCode(), "有效日期应在上次有效日期之后！");
            }
            info.setId(PrimaryKeyGenerator.generatorId());
            info.setPeriod(workPeriodPO.getPeriod());
            info.setOperateTime(workPeriodPO.getOperateTime());
            info.setWarningTime(workPeriodPO.getWarningTime());
            info.setRemark(workPeriodPO.getRemark());
            info.setOperator(UserUtils.getName());
            info.setCreateBy(UserUtils.getUserId());
            int changeInfo = otherDevicePeriodMapping.addOtherDeviceInfo(info);
            workPeriodPO.setModifyBy(UserUtils.getUserId());
            int history = otherDevicePeriodMapping.otherDeviceHistory(workPeriodPO);
            if (changeInfo != 1 || history != 1) {
                return "failed";
            }
        } catch (DataAccessException e) {
            logger.error("Method[updateOtherDeviceInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
        return "success";
    }

    public Object listOtherDeviceInfo(WorkPeriodVO workPeriodVO) {
        try {
            int currentPage = workPeriodVO.getPageNumber();
            int pageSize = workPeriodVO.getPageSize();
            PageHelper.startPage(currentPage, pageSize);
            Double percent = (double) 0;
            //查询其他设备周期数据
            List<WorkPeriodVO> workPeriodVOList = otherDevicePeriodMapping.listOtherDeviceInfo(workPeriodVO);
            for (WorkPeriodVO periodVO : workPeriodVOList) {
                Date operateTime = periodVO.getOperateTime();
                Date warningTime = periodVO.getWarningTime();
                long startTime = operateTime.getTime();
                long endTime = warningTime.getTime();
                long currentTimeMillis = System.currentTimeMillis();
                percent = (double) (currentTimeMillis - startTime)*100/(endTime - startTime);
                String[] value = String.valueOf(percent).split("\\.");
                periodVO.setPercent(value[0]);
            }
            PageInfo<WorkPeriodVO> pageInfo = new PageInfo<>(workPeriodVOList);
            return pageInfo;
        }catch(DataAccessException e){
            logger.error("Method[listOtherDeviceInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object listOtherDeviceHistory(String id) {
        try {
            return otherDevicePeriodMapping.listOtherDeviceHistory(id);
        }catch(DataAccessException e){
            logger.error("Method[listOtherDeviceHistory] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    @Transactional(rollbackFor=Exception.class)
    public Object updateCurrentInfo(WorkPeriodPO workPeriodPO) {
        try {
            workPeriodPO.setModifyBy(UserUtils.getUserId());
            int successCount = otherDevicePeriodMapping.updateCurrentInfo(workPeriodPO);
            if (successCount != 1) {
                throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
            }
            return successCount;
        }catch(DataAccessException e){
            logger.error("Method[updateCurrentInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Object deleteOtherDeviceInfo(String ids) {
        try {
            String[] idArr = ids.split(",");
            for (int i = 0; i < idArr.length; i++) {
                int count = otherDevicePeriodMapping.deleteOtherDeviceInfo(idArr[i], UserUtils.getUserId());
                List<WorkPeriodVO> history = otherDevicePeriodMapping.listOtherDeviceHistory(idArr[i]);
                if (count != history.size()) {
                    throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
                }
            }
            return "success";
        } catch (DataAccessException e) {
            logger.error("Method[deleteOtherDeviceInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }
}
