package cc.crrc.manage.service;

import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.BusDictMapping;
import cc.crrc.manage.mapper.BusDictTypeMapping;
import cc.crrc.manage.pojo.BusDictType;
import cc.crrc.manage.pojo.BusDictTypeQuery;
import cc.crrc.manage.pojo.BusDictTypeVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.Date;

@Service
public class BusDictTypeService {

    @Autowired
    private BusDictTypeMapping busDictTypeMapper;

    @Autowired
    private BusDictMapping busDictMapper;


    /**
     * 查询业务字典类型列表
     *
     * @return 业务字典类型
     */
    public PageInfo<BusDictTypeVO> selectBusDictTypeList(BusDictTypeQuery busDictTypeQuery) {
        PageHelper.startPage(busDictTypeQuery.getPageNumber(), busDictTypeQuery.getPageSize());
        return new PageInfo<>(busDictTypeMapper.selectBusDictTypeList(busDictTypeQuery));
    }

    /**
     * 新增业务字典类型
     *
     * @param busDictType 业务字典类型
     * @return 结果
     */
    public int insertBusDictType(BusDictType busDictType) {
        String userId = UserUtils.getUserId();
        busDictType.setId(PrimaryKeyGenerator.generatorId());
        Date date = new Date();
        Timestamp t = new Timestamp(date.getTime());
        busDictType.setCreateTime(t);
        busDictType.setCreateBy(userId);
        busDictType.setLastModifyBy(userId);
        //字典范围默认为线路类型
        String typeLevel = busDictType.getTypeLevel();
        if (typeLevel == null) {
            busDictType.setTypeLevel("2");
        }
        return busDictTypeMapper.insertBusDictType(busDictType);
    }

    /**
     * 修改业务字典类型
     *
     * @param busDictType 业务字典类型
     * @return 结果
     */
    @Transactional
    public int updateBusDictType(BusDictType busDictType) {
        busDictType.setLastModifyBy(UserUtils.getUserId());
        String typeLevel = busDictType.getTypeLevel();
        if (typeLevel == null) {
            busDictType.setTypeLevel("2");
        }
        return busDictTypeMapper.updateBusDictType(busDictType);
    }

    /**
     * 批量删除业务字典类型
     *
     * @param id 需要删除的业务字典类型主键
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    public int deleteBusDictTypeByIds(String id) {
        String lastModifyBy = UserUtils.getUserId();
        String lastModifyTime = String.valueOf(new Date());
        String typeCode = busDictTypeMapper.listCodeByIds(id);
        busDictMapper.deleteBusDictByTypeCode(typeCode);
        return busDictTypeMapper.deleteBusDictTypeByIds(id, lastModifyBy, lastModifyTime);
    }

  /*  @Transactional
    public Object listDictUniversal(BusDict busDict) {
        String level = busDictTypeMapper.getTypeLevel(busDict.getTypeCode());
        BusDict param = new BusDict();
        if(level.equals("1")){
            param.setTypeCode(busDict.getTypeCode());
        }else if (level.equals("2")) {
            param.setTypeCode(busDict.getTypeCode());
            String lineId = busDict.getLineId();
            param.setLineId(lineId);
        }
        List<BusDictVO> busDictVO =  busDictTypeMapper.listDictUniversal(param);
        return busDictVO;
    }
*/

}
