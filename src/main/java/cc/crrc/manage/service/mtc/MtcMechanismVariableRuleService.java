package cc.crrc.manage.service.mtc;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.*;
import cc.crrc.manage.mapper.ekb.EkbFaultTypeMapping;
import cc.crrc.manage.mapper.mtc.MtcMechanismVariableRuleMapping;
import cc.crrc.manage.mapper.mtr.MtrSubsystemDictMapping;
import cc.crrc.manage.mapper.mtr.MtrVehicleTypeMapping;
import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.mtc.MtcMechanismVariableRuleDTO;
import cc.crrc.manage.pojo.mtc.MtcMechanismVariableRuleEditDTO;
import cc.crrc.manage.pojo.mtc.MtcMechanismVariableRulePO;
import cc.crrc.manage.pojo.mtc.MtcMechanismVariableRuleVO;
import cc.crrc.manage.service.SysFileService;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.csource.fastdfs.FileInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName MtcMechanismVariableRuleService
 * @Description TODO
 * <AUTHOR> kangjian
 * @Date 2021/1/4
 **/
@Service
public class MtcMechanismVariableRuleService {
    @Autowired
    private MtcMechanismVariableRuleMapping ruleMapping;
    @Autowired
    private SysFileService sysFileService;
    @Autowired
    private MtrVehicleTypeMapping vehicleTypeMapping;
    @Autowired
    private EkbFaultTypeMapping faultTypeMapping;
    @Autowired
    private MtrSubsystemDictMapping subsystemDictMapping;

    @Autowired
    private KafkaTemplate kafkaTemplate;

    private final Logger logger = LoggerFactory.getLogger(MtcMechanismVariableRuleService.class);

    public PageInfo list(MtcMechanismVariableRuleDTO ruleDTO) {
        // 分页，未传分页信息，默认不分页。
        int currentPage = ruleDTO.getPageNumber();
        int pageSize = ruleDTO.getPageSize();
        PageHelper.startPage(currentPage, pageSize);
        //查询变量信息
        List<MtcMechanismVariableRuleVO> listMtcMechanismVariableRule = ruleMapping.list(ruleDTO);
        return new PageInfo<>(listMtcMechanismVariableRule);
    }

    public Object add(MtcMechanismVariableRuleDTO ruleDTO) {
        MtcMechanismVariableRulePO rulePO = ruleDTO2PO(ruleDTO);
        String userId = UserUtils.getUserId();
        rulePO.setCreateBy(userId);
        rulePO.setModifyBy(userId);
        //雪花算法添加主键
        rulePO.setId(PrimaryKeyGenerator.generatorId());
        if (ruleMapping.add(rulePO) == 1) {
            //用于kafka发送是否删除判断
            boolean operate=ruleDTO.getEnable();
            sendKafka(rulePO.getId(),operate);
            return "保存成功";
        } else {
            LoggerUtils.error(logger, "数据库插入失败");
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    public Object update(MtcMechanismVariableRuleDTO ruleDTO) {
        MtcMechanismVariableRulePO rulePO = ruleDTO2PO(ruleDTO);
        String userId = UserUtils.getUserId();
        rulePO.setModifyBy(userId);
        if (ruleMapping.update(rulePO) == 1) {
            boolean operate=ruleDTO.getEnable();
            sendKafka(rulePO.getId(),operate);
            return "修改成功";
        } else {
            LoggerUtils.error(logger, "数据库更新失败");
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    /**
     * 填充变量json、变量图片、创建修改信息以外的变量信息，用来将新增、编辑变量信息页面的数据写入数据库
     *
     * @param ruleDTO
     * @return
     */
    private MtcMechanismVariableRulePO ruleDTO2PO(MtcMechanismVariableRuleDTO ruleDTO) {
        MtcMechanismVariableRulePO rulePO = new MtcMechanismVariableRulePO();
        rulePO.setId(ruleDTO.getId());
        rulePO.setVehicleTypeId(ruleDTO.getVehicleTypeId());
        rulePO.setName(ruleDTO.getName());
        rulePO.setEnable(ruleDTO.getEnable());
        rulePO.setDescription(ruleDTO.getDescription());
        rulePO.setLocation(ruleDTO.getLocation());
        rulePO.setSubsystem(ruleDTO.getSubsystem());
        rulePO.setLineId(ruleDTO.getLineId());
        return rulePO;
    }

    /**
     * 保存变量的json和图片
     *
     * @param ruleEditDTO
     * @return
     */
    public Object edit(MtcMechanismVariableRuleEditDTO ruleEditDTO) {
        HashMap<String, Object> map = new HashMap<>();
        String id = ruleEditDTO.getId();
        String base64Str = ruleEditDTO.getBase64Str();
        try {
            // 保存图片到集群
            FastDFSFile file = base64ToFile(id.toString(), base64Str);
            String[] uploadResults = FastDFSClient.upload(file);
            // 保存集群图片信息到postgres
            SysFilePO sysFilePO = new SysFilePO();
            sysFilePO.setName(file.getName());
            String path = FastDFSClient.getTrackerUrl() + uploadResults[0] + "/" + uploadResults[1];
            sysFilePO.setUrl(path);
            sysFilePO.setType("Variable Image");
            sysFilePO.setFormat(file.getExt());
            FileInfo fileInfo = FastDFSClient.getFile(uploadResults[0], uploadResults[1]);//get info from fastdfs
            if (fileInfo == null) {
                return "图片文件保存失败";
            }
            sysFilePO.setSize(fileInfo.getFileSize());
            sysFilePO.setHashCode(Long.toHexString(fileInfo.getCrc32()));
            sysFilePO.setDelFlag(0);
            sysFilePO.setRemark("hash_code is crc32");
            sysFilePO.setGroup(uploadResults[0]);
            sysFilePO.setFileLocation(uploadResults[1]);
            String fileId = sysFileService.addSysFile(sysFilePO);
            // 保存图片postgres信息到表
            MtcMechanismVariableRulePO rulePO = new MtcMechanismVariableRulePO();
            rulePO.setId(id);
            rulePO.setContent(ruleEditDTO.getContent());
            rulePO.setImageFile(fileId);
            String userId = UserUtils.getUserId();
            rulePO.setModifyBy(userId);
            if (ruleMapping.update(rulePO) == 1) {
                boolean operate=true;
                sendKafka(rulePO.getId(),operate);
                return "修改成功";
            } else {
                LoggerUtils.error(logger, "数据库更新失败");
                throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
            }
        } catch (Exception e) {
            LoggerUtils.error(logger, e);
            throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION);
        }
    }

    /**
     * base64格式为data:image/{具体格式，如jpeg，png等};base64,{字符串}
     */
    private FastDFSFile base64ToFile(String fileName, String base64Str) throws Exception {
        // Base64解码
        String[] strings = base64Str.split(";base64,");
        String ext = strings[0].replaceFirst("data:image/","");
        String name = fileName + "." + ext;
        Base64.Decoder decoder = Base64.getDecoder();
        byte[] b = decoder.decode(strings[1]);
        for (int i = 0; i < b.length; ++i) {
            if (b[i] < 0) {
                b[i] += 256;
            }
        }
        return new FastDFSFile(name, b, ext);
    }


    /**
     * @Title: sendKafka
     * @Description: 发送机理规则信息至kafka中
     */
    public Object sendKafka(String id,boolean operate){
        try {
            MtcMechanismVariableRulePO mrule = ruleMapping.selectById(id);
            if(operate==true){
                mrule.setDel(0);
            }else{
                mrule.setDel(1);
            }
            String json= JSON.toJSONString(mrule);
            kafkaTemplate.send("TrainVariableRuleData",json);
            return "kafka上传成功";
        } catch (Exception e) {
            LoggerUtils.error(logger, "kafka上传失败");
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }

    }


    public Object delete(String ruleIdList) {
        if (ruleIdList == null || ruleIdList == "") {
            return 0;
        }
        String[] idsArr = ruleIdList.split(",");
        //Long[] idsList = new Long[idsArr.length];
        for (int i = 0, len = idsArr.length; i < len; i++) {
            //idsList[i] = new Long(idsArr[i]);
            boolean operate=false;
            sendKafka(idsArr[i],operate);
            ruleMapping.delete(idsArr[i]);
        }
        return "SUCCESS";
    }

    public String getJson(String id) {
        Map<String, String> map = ruleMapping.getJson(id);
        if (map != null) {
            return map.get("content");
        } else {
            return null;
        }
    }

    public Object variables(String vehicleTypeId, String location, String subsystem, String nameCn) {
        try {
            return ruleMapping.variables(vehicleTypeId, location, subsystem, nameCn);
        } catch (DataAccessException e) {
            logger.error("Method[variables] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }


}
