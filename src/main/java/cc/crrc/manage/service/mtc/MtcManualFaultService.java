package cc.crrc.manage.service.mtc;


import cc.crrc.manage.mapper.mtc.MtcManualFaultMapping;
import cc.crrc.manage.pojo.mtc.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
/**
 * <AUTHOR>
 */


@Service
public class MtcManualFaultService {

    private static final Logger logger = LoggerFactory.getLogger(MtcManualFaultService.class);
    @Autowired
    private MtcManualFaultMapping mapping;

    public List<MtcAlarmWarningVO> getManualFaultList(MtcAlarmWarningDTO codition) {
        return mapping.getManualFaultList(codition);
    }
}
