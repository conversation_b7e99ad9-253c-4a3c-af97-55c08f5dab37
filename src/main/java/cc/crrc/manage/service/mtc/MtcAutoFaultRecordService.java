package cc.crrc.manage.service.mtc;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.JsonUtils;
import cc.crrc.manage.common.utils.RedisUtils;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.common.utils.tsdb.TSDBUtils;
import cc.crrc.manage.common.utils.tsdb.iotdb.IoTDBUtils;
import cc.crrc.manage.common.vo.PageInfoExtend;
import cc.crrc.manage.mapper.mtc.MtcAutoFaultRecordMapping;
import cc.crrc.manage.mapper.mtr.MtrVehicleMapping;
import cc.crrc.manage.pojo.comm.signal.SignalPO;
import cc.crrc.manage.pojo.mtc.MtcAutoFaultRecordDTO;
import cc.crrc.manage.pojo.mtc.MtcAutoFaultRecordVO;
import cc.crrc.manage.pojo.mtc.ParamsPO;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import cc.crrc.manage.service.SysDictService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class MtcAutoFaultRecordService {

    @Autowired
    private MtcAutoFaultRecordMapping mtcAutoFaultRecordMapping;
    @Autowired
    private MtrVehicleMapping mtrVehicleMapping;
    @Autowired
    private SysDictService sysDictService;
    @Autowired
    private TSDBUtils tsdbUtils;

    private final Logger logger = LoggerFactory.getLogger(MtcAutoFaultRecordService.class);


    public Object listMtcAutoFaultRecord(MtcAutoFaultRecordDTO mtcAutoFaultRecordDTO) {
        try {
            // 分页，未传分页信息，默认不分页。
            int currentPage = mtcAutoFaultRecordDTO.getPageNumber();
            int pageSize = mtcAutoFaultRecordDTO.getPageSize();
            //统计确认故障数 confirmNumber ,未确认 notConfirmNumber, 总数totalFaultNumber
            int notConfirmNumber = 0;
            int totalFaultNumber = 0;
            PageHelper.startPage(currentPage, pageSize);
            //查询故障数据
            String applicationScenario = mtcAutoFaultRecordDTO.getApplicationScenario();
            if (StringUtil.isNotEmpty(applicationScenario)) {
                if ("train".equals(applicationScenario) && StringUtil.isEmpty(mtcAutoFaultRecordDTO.getVehicleCode())) {
                    throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "车辆监控必须传值vehicleCode!");
                }
                if ("line".equals(applicationScenario) && mtcAutoFaultRecordDTO.getLineId() == null) {
                    throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "线路监控必须传值lineId!");
                }
            }
            List<MtcAutoFaultRecordVO> listMtcAutoFaultRecord = mtcAutoFaultRecordMapping.listMtcAutoFaultRecord(mtcAutoFaultRecordDTO);
            if (StringUtil.isNotEmpty(applicationScenario) && "train".equals(applicationScenario)) {
                notConfirmNumber = mtcAutoFaultRecordMapping.countNotConfirmNumberFroTrain(mtcAutoFaultRecordDTO.getVehicleCode());
                totalFaultNumber = mtcAutoFaultRecordMapping.countFaultNumberForTrain(mtcAutoFaultRecordDTO.getVehicleCode());
            } else if (StringUtil.isNotEmpty(applicationScenario) && "line".equals(applicationScenario)) {
                notConfirmNumber = mtcAutoFaultRecordMapping.countNotConfirmNumberForLine(mtcAutoFaultRecordDTO.getLineId());
                totalFaultNumber = mtcAutoFaultRecordMapping.countFaultNumber();
            } else {
                notConfirmNumber = mtcAutoFaultRecordMapping.countNotConfirmNumber();
                totalFaultNumber = mtcAutoFaultRecordMapping.countFaultNumber();
            }

            PageInfo a = new PageInfo<>(listMtcAutoFaultRecord);
            PageInfoExtend b = new PageInfoExtend();
            //把原来查询的pageinfo信息传给 增加字段的新实体中
            BeanUtils.copyProperties(a, b);
            b.setConfirmNumber(totalFaultNumber - notConfirmNumber);
            b.setNotConfirmNumber(notConfirmNumber);
            b.setTotalFaultNumber(totalFaultNumber);
            return b;
        } catch (DataAccessException e) {
            logger.error("Method[listMtcFaultReport] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @return java.lang.Object
     * @Description //TODO
     * <AUTHOR> xin
     * @Date 11:40 2019/12/26
     * @Param [id]
     **/
    public Object getMtcAutoFaultRecordById(String id) {
        try {
            //2020/6/5 故障原因  正线处置意见  检修建议从故障编码表查询
            MtcAutoFaultRecordVO mtcAutoFaultRecordVO = mtcAutoFaultRecordMapping.getMtcAutoFaultRecordById(id);
            if (mtcAutoFaultRecordVO == null) {
                throw new RestApiException(ExceptionInfoEnum.DATA_NOT_FOUND_EXCEPTION);
            }
            mtcAutoFaultRecordVO.setSupressionFault(mtcAutoFaultRecordMapping.getSupressionFault(mtcAutoFaultRecordVO.getId()));
            return mtcAutoFaultRecordVO;
        } catch (DataAccessException e) {
            logger.error("Method[getMtcAutoFaultRecordById] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 从iotdb中查询故障发生前后，相关信号的数据
     *
     * @param mtcAutoFaultRecordVO 故障相关信息
     * @param signalPOList         故障相关联的信号
     * @return com.alibaba.fastjson.JSONArray
     * <AUTHOR> GuoYang
     * 2020/6/15
     **/
    private JSONArray querySignalGroupValuesFromIoTDB(MtcAutoFaultRecordVO mtcAutoFaultRecordVO, List<SignalPO> signalPOList) {
        JSONArray res = new JSONArray();
        Date startTime = mtcAutoFaultRecordVO.getStartTime();
        long startTimeStamp = startTime.getTime();
        // 由于sql语句采用开区间 因此前后各取90秒需要加减91秒
        long queryStartTime = startTimeStamp - 91 * 1000;
        long queryEndTime = startTimeStamp + 91 * 1000;
        String vehicleCode = mtcAutoFaultRecordVO.getVehicleCode();
        MtrVehiclePO mtrVehiclePO = mtrVehicleMapping.getVehicleByCode(vehicleCode);
        int vehicleStorageGroup = mtrVehiclePO.getVehicleStorageGroup();
        for (SignalPO signalPO : signalPOList) {
            JSONObject json = new JSONObject();
            String id = signalPO.getId();
            String originSqlModel = "SELECT {0,number,#} FROM root.origin.sg{2,number,#}.{1} where time < {4,number,#} and time > {3,number,#}";
            String sql = MessageFormat.format(originSqlModel, id, vehicleCode, vehicleStorageGroup, queryStartTime, queryEndTime);
            IoTDBUtils ioTDBUtils = (IoTDBUtils) tsdbUtils;
            ArrayList<ArrayList<String>> dbQueryResult = ioTDBUtils.query2IoTDB(sql);
            JSONArray values = new JSONArray();
            for (ArrayList<String> rowInfo : dbQueryResult) {
                JSONObject timeStampAndValue = new JSONObject();
                timeStampAndValue.put("time", rowInfo.get(0));
                timeStampAndValue.put("value", Double.valueOf(rowInfo.get(1)));
                values.add(timeStampAndValue);
            }
            String signalNameCn = signalPO.getNameCN();
            String signalNameEn = signalPO.getNameEN();
            json.put("nameCn", signalNameCn);
            json.put("nameEn", signalNameEn);
            json.put("values", values);
            res.add(json);
        }
        return res;
    }

    /**
     * @return com.alibaba.fastjson.JSONArray
     * @Description //TODO
     * <AUTHOR> xin
     * @Date 11:40 2019/12/26
     * @Param [id]
     **/
    private JSONArray querySignalGroupValuesFromRedis(MtcAutoFaultRecordVO mtcAutoFaultRecordVO, List<SignalPO> signalPOList) {
        //获取redis
        //先声明一些变量名
        String id = mtcAutoFaultRecordVO.getId();
        String redisResult = "";//承接redis总数据
        String signalNameEn = "";//信号英文名
        String signalNameCn = "";//信号中文名
        String redisTblName = "fault_signal_group:" + id;//redis对应key值
        JSONArray res = new JSONArray();

        Object redisResultObject = RedisUtils.get(redisTblName);
        if (redisResultObject != null) {
            //下面业务为读取信号对应的值
            redisResult = JSONObject.toJSONString(redisResultObject);
            for (SignalPO signalPO : signalPOList) {
                JSONObject json = new JSONObject();
                signalNameEn = signalPO.getNameEN();
                signalNameCn = signalPO.getNameCN();
                JSONArray values = (JSONArray) JSONObject.parseObject(redisResult).get(signalNameEn);
                json.put("nameCn", signalNameCn);
                json.put("nameEn", signalNameEn);
                json.put("values", values);
                res.add(json);
            }
        }
        return res;
    }

    /**
     * @return java.util.List<cc.crrc.manage.pojo.comm.signal.SignalPO>
     * @Description //TODO
     * <AUTHOR> xin
     * @Date 15:57 2019/12/26
     * @Param [faultTypeKey]
     **/
    public List<SignalPO> getCommOriginalSignalByFaultTypeKey(String faultTypeKey) {
        try {
            return mtcAutoFaultRecordMapping.getCommOriginalSignalByFaultTypeKey(faultTypeKey);
        } catch (DataAccessException e) {
            logger.error("Method[getCommOriginalSignalByFaultTypeKey] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @return java.lang.Object
     * @Description //TODO
     * <AUTHOR> xin
     * @Date 13:00 2020/2/18
     * @Param [id]
     **/
    @Transactional
    public Object confirmMtcAutoFaultRecordById(String ids) {
        try {
            String userId = UserUtils.getUserId();
            int count = 0;
            if (StringUtil.isNotEmpty(ids)) {
                String[] idsArr = ids.split(",");
                /*Long[] id = new Long[idsArr.length];
                for (int i = 0, len = idsArr.length; i < len; i++) {
                    id[i] = new Long(idsArr[i]);
                }*/
                for (String idNow : idsArr) {
                    try {
                        //判断 抑制状态  如果被抑制不能确认
                        MtcAutoFaultRecordVO mtcAutoFaultRecordVO = mtcAutoFaultRecordMapping.getMtcAutoFaultRecordById(idNow);
                        if (mtcAutoFaultRecordVO.getSuppressedStatus()) {
                            throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION.getErrorCode(), "此记录已经被抑制，不可以确认！");
                        }
                        if ("1".equals(String.valueOf(mtcAutoFaultRecordVO.getConfirm()))) {
                            throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION.getErrorCode(), "此记录已确认，不可重复确认！");
                        }
                        mtcAutoFaultRecordMapping.confirmMtcAutoFaultRecordById(idNow, userId);
                        count++;
                    } catch (DataAccessException e) {
                        logger.error("Method[addDict] Error:{}", e.getMessage());
                        throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION);
                    }
                }
            }
            return count;
        } catch (DataAccessException e) {
            logger.error("Method[getCommOriginalSignalByFaultTypeKey] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    /**
     * @return
     * @Description 提供fracas系统修改 自动上报记录的 处理状态
     * <AUTHOR> xin
     * @Date 13:47 2020/3/31
     * @Param
     **/
    public Object changeStatusFromFracas(Long id, String status, Integer confirm, Date endTime) {
        try {
            return mtcAutoFaultRecordMapping.changeStatusFromFracas(id, status, confirm, endTime);
        } catch (DataAccessException e) {
            logger.error("Method[getCommOriginalSignalByFaultTypeKey] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }


    public Object getStruVehicleStructureComponent(String lineId, String vehicleTypeId, String vehicleId, String structurePosition) {
        try {
            //声明返回json
            JSONObject json = new JSONObject();
            if (vehicleId == null || vehicleTypeId == null || lineId == null || StringUtil.isEmpty(structurePosition)) {
                throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION, "查询数据失败，缺少查询信息！");
            }
            //先查询当前节点的 uniqueFlag
            String CurrentNode = mtcAutoFaultRecordMapping.getUniqueFlag(structurePosition, vehicleId, vehicleTypeId, lineId);
            //查询当前节点父节点的 uniqueFlag
            //根据structurePosition查询当前节点的父节点parentStructureCode 然后再查父节点的uniqueFlag
            String parentStructureCode = mtcAutoFaultRecordMapping.getParentStructureCode(structurePosition, vehicleId);
            String ParentNode = mtcAutoFaultRecordMapping.getUniqueFlag(parentStructureCode, vehicleId, vehicleTypeId, lineId);
            //拼接返回值
            json.put("CurrentNode", CurrentNode);
            json.put("ParentNode", ParentNode);
            return json;
        } catch (DataAccessException e) {
            logger.error("Method[getMtcAutoFaultRecordByStructurePosition] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }
}
