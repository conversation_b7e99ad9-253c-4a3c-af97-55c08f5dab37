package cc.crrc.manage.service.mtc;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.mapper.mtc.MtcMechanismAlarmRecordMapping;
import cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRecordDTO;
import cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRecordVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MtcMechanismAlarmRecordService {
    @Autowired
    private MtcMechanismAlarmRecordMapping mapping;
    private final Logger logger = LoggerFactory.getLogger(MtcMechanismAlarmRecordService.class);

    public Object listMtcMechanismAlarmRecord(MtcMechanismAlarmRecordDTO mtcMechanismAlarmRecordDTO) {
        try {
            // 分页，未传分页信息，默认不分页。
            int currentPage = mtcMechanismAlarmRecordDTO.getPageNumber();
            int pageSize = mtcMechanismAlarmRecordDTO.getPageSize();
            PageHelper.startPage(currentPage, pageSize);
            String applicationScenario = mtcMechanismAlarmRecordDTO.getApplicationScenario();
            if(StringUtil.isNotEmpty(applicationScenario)){
                if("train".equals(applicationScenario)&&StringUtil.isEmpty(mtcMechanismAlarmRecordDTO.getVehicleCode())){
                    throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(),"车辆监控必须传值vehicleCode!");
                }
                if("line".equals(applicationScenario)&&mtcMechanismAlarmRecordDTO.getLineId()==null){
                    throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(),"线路监控必须传值lineId!");
                }
            }
            //查询机理故障
            List<MtcMechanismAlarmRecordVO> listMtcMechanismAlarmRecord = mapping.listMtcMechanismAlarmRecord(mtcMechanismAlarmRecordDTO);
            return new PageInfo<>(listMtcMechanismAlarmRecord);
        } catch (DataAccessException e) {
            logger.error("Method[listMtcMechanismAlarmRecord] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }


    public Object updateCloseStatue(String id,Boolean closeStatue) {
        int result = mapping.updateCloseStatue(id,closeStatue);
        if (result > 0)
            return "SUCCESS";
        throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
    }

}
