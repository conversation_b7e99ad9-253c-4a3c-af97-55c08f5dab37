package cc.crrc.manage.service.mtc;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.LoggerUtils;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.mapper.mtc.MtcAlarmWarningMapping;
import cc.crrc.manage.mapper.mtc.MtcFaultRecordMapping;
import cc.crrc.manage.pojo.excel.MtcFaultRecordExcelPO;
import cc.crrc.manage.pojo.mtc.MtcAlarmWarningDTO;
import cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO;
import cc.crrc.manage.pojo.mtc.MtcFaultRecordPO;
import cc.crrc.manage.pojo.mtc.MtcFaultRecordVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.util.List;
import java.util.Objects;


/**
 * 文件描述
 *
 * <AUTHOR>
 * @date 2022/08/10  16:41
 */
@Service
public class MtcFaultRecordService {
    private static final Logger logger = LoggerFactory.getLogger(MtcFaultRecordService.class);

    @Autowired
    private MtcFaultRecordMapping mtcFaultRecordMapping;
    @Autowired
    private MtcManualFaultService manualFaultService;
    @Autowired
    MtcAlarmWarningService mtcAlarmWarningService;

    @Transactional(rollbackFor = Exception.class)
    public Object addFaultRecord(MtcFaultRecordPO po) {
        Objects.requireNonNull(po.getVehicleId(), "车辆不能为空");
        Objects.requireNonNull(po.getFaultSource(), "故障类型不正确");
        try {
            po.setId(PrimaryKeyGenerator.generatorId());
            po.setConfirm(0);
            //更新故障记录表状态
            int row = mtcAlarmWarningService.updateAlarmFaultStatus(po);
            if(row == 0){
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION,"不存在此故障未上报的情况，请重新刷新页面");
            }
            mtcFaultRecordMapping.saveFaultRecord(po);
            return "保存成功！";
        } catch (Exception e) {
            logger.error("保存故障工单数据失败 {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION,e.getMessage());
        }

    }


    public Object updMtcFaultRecordConfirm(MtcFaultRecordPO mtcFaultRecordPO) {
        try {
            mtcFaultRecordPO.setConfirm(1);
            mtcFaultRecordMapping.updFaultRecordConfirm(mtcFaultRecordPO);
            return "编辑成功";
        } catch (Exception e) {
            LoggerUtils.error(logger, e);
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }


    public Object updMtcFaultRecordInfo(MtcFaultRecordPO mtcFaultRecordPO) {
        try {
            mtcFaultRecordMapping.updFaultRecord(mtcFaultRecordPO);
            return "编辑成功";
        } catch (Exception e) {
            LoggerUtils.error(logger, e);
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    public Object delMtcFaultRecord(String id) {
        try {
            mtcFaultRecordMapping.delFaultRecord(id);
            return "";
        } catch (Exception e) {
            LoggerUtils.error(logger, e);
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    public PageInfo<MtcAlarmWarningVO> findCurrentFaultList(MtcAlarmWarningDTO condition) {
        PageHelper.startPage(condition.getPageNumber(), condition.getPageSize());
        List<MtcAlarmWarningVO> result;
        try {
            if ("2".equals(condition.getFaultSource())) {
                result = manualFaultService.getManualFaultList(condition);
            } else {
                result = mtcAlarmWarningService.getAutoFaultList(condition);
            }
            return new PageInfo<>(result);
        } catch (DataAccessException e) {
            logger.error("接口 findCurrentFaultList error {}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }


    public PageInfo<MtcFaultRecordVO> listMtcFaultRecord(MtcAlarmWarningDTO condition) {
        try {
            //分页,未传分页信息,默认不分页
            PageHelper.startPage(condition.getPageNumber(), condition.getPageSize());
            List<MtcFaultRecordVO> list = mtcFaultRecordMapping.getFaultRecord(condition);
            return new PageInfo<>(list);
        } catch (Exception e) {
            LoggerUtils.error(logger, e);
            throw new RestApiException(ExceptionInfoEnum.DATA_NOT_FOUND_EXCEPTION);
        }
    }


    public List<MtcAlarmWarningVO> getFaultList(String faultSource, String vehicleId, String faultNameCn) {
        MtcAlarmWarningVO mtcAlarmWarningVO = new MtcAlarmWarningVO();
        mtcAlarmWarningVO = mtcAlarmWarningVO.build(1, 50, faultSource, vehicleId, faultNameCn);
        PageInfo<MtcAlarmWarningVO> voList = mtcAlarmWarningService.getMtcAlarmWarning(mtcAlarmWarningVO);
        return voList.getList();
    }

    public List<MtcFaultRecordExcelPO> findFaultWorkOrderList(MtcAlarmWarningDTO condition) {
        return mtcFaultRecordMapping.findFaultWorkOrderList(condition);
    }
}
