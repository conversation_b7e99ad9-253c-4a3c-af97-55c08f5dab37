package cc.crrc.manage.service.mtc;

import cc.crrc.manage.cache.CacheUtils;
import cc.crrc.manage.cache.dict.DictCache;
import cc.crrc.manage.cache.dict.EkbFaultTypeCache;
import cc.crrc.manage.cache.dict.VehicleCache;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.DateUtils;
import cc.crrc.manage.common.utils.JsonUtils;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.StringUtils;
import cc.crrc.manage.mapper.mtc.MtcAlarmWarningMapping;
import cc.crrc.manage.mq.process.KafkaAlarmWarningPO;
import cc.crrc.manage.pojo.SysDictVO;
import cc.crrc.manage.pojo.excel.MtcAlarmWarningForExcelPO;
import cc.crrc.manage.pojo.mtc.*;

import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.stream.Collectors;

import static cc.crrc.manage.cache.dict.DictCache.ASS_CAR_SYSTEM;
import static cc.crrc.manage.monitoringConfig.service.DictForMonitorService.DEFAULT_LINE_ID;
import static java.util.stream.Collectors.counting;
import static java.util.stream.Collectors.groupingBy;

/**
 * @FileName MtcAlarmWarningService
 * @Description 告警预警service
 * <AUTHOR> yuxi
 * @Date 2020/6/5 16:24
 **/
@Service
public class MtcAlarmWarningService {

    private static final Logger logger = LoggerFactory.getLogger(MtcAlarmWarningService.class);
    private static final String ALARM_FAULT_SOURCE = "0";
    private static final String WARNING_FAULT_SOURCE = "1";
    private static final String MANUAL_FAULT_SOURCE = "2";

    @Autowired
    private MtcAlarmWarningMapping alarmWarningMapping;
    @Autowired
    private MtcMechanismAlarmRuleService ruleService;
    @Autowired
    private VehicleCache vehicleCache;
    @Autowired
    private EkbFaultTypeCache ekbFaultTypeCache;


    /**
     * @Description 新增告警预警
     * @Return void
     * <AUTHOR> yuxi
     * @Date 15:01 2020/6/8
     * @Param [assetCode, troubleCode, alarmWarning]
     **/
    public int insertAlarmWarning(KafkaAlarmWarningPO warningPO) {
        try {
            warningPO.build()
                    .build(vehicleCache.getValue(warningPO.getAssetCode()))
                    .build(ekbFaultTypeCache.getValue(warningPO.getFaultType()));
            if (StringUtils.isNotEmpty(warningPO.getAlarmRuleId()) && WARNING_FAULT_SOURCE.equals(warningPO.getFaultSource())) {
                MtcMechanismAlarmRulePO rulePO = ruleService.selectById(warningPO.getAlarmRuleId());
                //测试机理规则预警插入到测试表
                if (rulePO != null && rulePO.getTestStatus()) {
                    return alarmWarningMapping.insertTestAlarmWarning(warningPO);
                }
            }
            return alarmWarningMapping.insertAlarmWarning(warningPO);
        } catch (Exception e) {
            logger.error("数据插入异常：" + e.getMessage());
        }
        return 0;

    }

    /**
     * @Description 消除告警预警
     * @Return void
     * <AUTHOR> yuxi
     * @Date 16:27 2020/6/8
     * @Param [vehicleCode, troubleCode, alarmWarning]
     **/
    public int removeAlarmWarning(KafkaAlarmWarningPO warningPO) {
        warningPO.build(vehicleCache.getValue(warningPO.getAssetCode()))
                .build(ekbFaultTypeCache.getValue(warningPO.getFaultType()));
        if (StringUtils.isNotEmpty(warningPO.getAlarmRuleId()) && WARNING_FAULT_SOURCE.equals(warningPO.getFaultSource())) {
            MtcMechanismAlarmRulePO rulePO = ruleService.selectById(warningPO.getAlarmRuleId());
            if (rulePO.getTestStatus()) {
                return alarmWarningMapping.updateTestAlarmWaring(warningPO);
            }
        }
        return alarmWarningMapping.updateAlarmWaring(warningPO);
    }

    /**
     * @Description 故障诊断-故障查询页面list
     * @Author: liu xinchen
     * @Param: [mtcAlarmWarningVO]
     * @return: com.github.pagehelper.PageInfo<cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO>
     * @Date: 2020/6/9
     */
    public PageInfo<MtcAlarmWarningVO> getMtcAlarmWarning(MtcAlarmWarningVO mtcAlarmWarningVO) {
        Objects.requireNonNull(mtcAlarmWarningVO);
        Objects.requireNonNull(mtcAlarmWarningVO.getFaultSource(), "请选择故障类型");
        try {
            PageHelper.startPage(mtcAlarmWarningVO.getPageNumber(), mtcAlarmWarningVO.getPageSize());
            List<MtcAlarmWarningVO> list;
            if (MANUAL_FAULT_SOURCE.equals(mtcAlarmWarningVO.getFaultSource())) {
                list = alarmWarningMapping.getManualAlarmWarning(mtcAlarmWarningVO);
            } else {
                list = alarmWarningMapping.getAlarmWarning(mtcAlarmWarningVO);
            }
            return new PageInfo<>(list);
        } catch (DataAccessException e) {
            logger.error("Method[getCommOriginalSignalByFaultTypeKey] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    // 2020年8月18日 房明宽
    public List<MtcAutoFaultRecordVO> getRealtimeAlarmInformationList(String modelCode, String vehicleId) {
        try {
            return alarmWarningMapping.getRealtimeAlarmInformationList(modelCode, vehicleId);
        } catch (DataAccessException e) {
            logger.error("Method[getRealtimeAlarmInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public MtcAlarmWarningPO getMtcAlarmRecordById(String id, String faultSource) {
        Objects.requireNonNull(id, "请选择要查询的故障");
        MtcAlarmWarningPO po = new MtcAlarmWarningPO();
        try {
            if (MANUAL_FAULT_SOURCE.equals(faultSource)) {
                po = alarmWarningMapping.getManualMtcAlarmRecordById(id);
            } else {
                po = alarmWarningMapping.getMtcAlarmRecordById(id);
            }
            if (po != null) {
                String subsystem = po.getSubsystem();
                List<SysDictVO> dictList = CacheUtils.getValue(DictCache.class, String.join("_", ASS_CAR_SYSTEM, po.getLineId(), po.getVehicleTypeId()));
                String label = dictList.stream()
                        .filter(i -> i.getValue().equals(subsystem))
                        .map(SysDictVO::getLabel)
                        .findFirst()
                        .orElseGet(String::new);
                po.setLabel(label);
                po.setVehicleCode(VehicleCache.CODE_VEHICLE_MAP.getOrDefault(po.getVehicleId(), new MtrVehiclePO()).getVehicleCode());
                ParamsPO paramsPO = po.getParams() != null ? JsonUtils.parse(po.getParams(), ParamsPO.class) : null;
                po.param(paramsPO);
            }
            return po;
        } catch (DataAccessException e) {
            logger.error("Method[getRealtimeAlarmInfo] Error:{}", e.getMessage());
        }
        return po;
    }

    public PageInfo<MtcAlarmWarningVO> getMoreSocketFaultList(MtcAlarmWarningVO vo) {
        try {
            PageHelper.startPage(vo.getPageNumber(), vo.getPageSize());
            //根据页面传入时间计算当日零点时间
            vo.setStartTime(vo.getStartTime() == null ? null : DateUtils.getStartOfDay(vo.getStartTime()));
            List<MtcAlarmWarningVO> faultsList = alarmWarningMapping.getMoreMtcAlarmWarningForMonitor(vo);
            return new PageInfo<>(faultsList);
        } catch (DataAccessException e) {
            logger.error("Method[getMoreSocketFaultList] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object getTestMtcAlarmWarningById(String id) {
        try {
            Objects.requireNonNull(id, "请选择要查询的故障");
            MtcAlarmWarningVO alarmWarningVO = alarmWarningMapping.getTestMtcAlarmWarningById(id);
            return alarmWarningVO;
        } catch (DataAccessException e) {
            logger.error("Method[getRealtimeAlarmInfo] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @Description 故障诊断-故障查询页面list-测试状态机理规则故障查询
     * @Author: lixin
     * @Param: [mtcAlarmWarningVO]
     * @return: com.github.pagehelper.PageInfo<cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO>
     * @Date: 2022/4/29
     */
    public Object listMtcAlarmWarningRuleTest(MtcAlarmWarningVO mtcAlarmWarningVO) {
        try {
            int currentPage = mtcAlarmWarningVO.getPageNumber();
            int pageSize = mtcAlarmWarningVO.getPageSize();
            PageHelper.startPage(currentPage, pageSize);
            mtcAlarmWarningVO.setEndTime(new Date());
            List<MtcAlarmWarningVO> mtcAlarmWarnings = alarmWarningMapping.listMtcAlarmWarningRuleTest(mtcAlarmWarningVO);
            return new PageInfo<>(mtcAlarmWarnings);
        } catch (DataAccessException e) {
            logger.error("Method[getCommOriginalSignalByFaultTypeKey] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public List<MtcAlarmWarningVO> getAutoFaultList(MtcAlarmWarningDTO codition) {
        return alarmWarningMapping.getAutoFaultList(codition);
    }

    public List<MtcAlarmWarningForExcelPO> exportFaultExcel(MtcAlarmWarningVO condition) {
        if (MANUAL_FAULT_SOURCE.equals(condition.getFaultSource())) {
            return alarmWarningMapping.exportManualFaultExcel(condition);
        }
        return alarmWarningMapping.exportAutoFaultExcel(condition);
    }

    public Object addManualAlarmFault(MtcAlarmWarningVO mtcAlarmWarningVO) {
        Objects.requireNonNull(mtcAlarmWarningVO);
        Objects.requireNonNull(mtcAlarmWarningVO.getFaultTypeKey(), "请选择要保存的故障");
        int row = 0;
        try {
            mtcAlarmWarningVO.setId(PrimaryKeyGenerator.generatorId());
            mtcAlarmWarningVO.setFaultSource(MANUAL_FAULT_SOURCE);
            mtcAlarmWarningVO.setEndStatus(mtcAlarmWarningVO.getEndTime() == null ? 0 : 1);
            row = alarmWarningMapping.addManualAlarmFault(mtcAlarmWarningVO);
        } catch (DataAccessException e) {
            logger.error("method[addAlarmFault]新增故障异常 error = {}", e.getMessage());
        }
        return row == 1 ? "success" : "failure";
    }

    /**
     * 编辑人工故障
     *
     * @param mtcAlarmWarningVO
     * @return
     */
    public Object updateManualAlarmFault(MtcAlarmWarningVO mtcAlarmWarningVO) {
        Objects.requireNonNull(mtcAlarmWarningVO);
        Objects.requireNonNull(mtcAlarmWarningVO.getFaultTypeKey(), "请选择正确的故障信息");
        Objects.requireNonNull(mtcAlarmWarningVO.getId(), "请选择要编辑的故障");
        int row = 0;
        try {
            mtcAlarmWarningVO.setEndStatus(mtcAlarmWarningVO.getEndTime() == null ? 0 : 1);
            row = alarmWarningMapping.updateManualAlarmFault(mtcAlarmWarningVO);
        } catch (DataAccessException e) {
            logger.error("method[updateManualAlarmFault]编辑故障异常 error = {}", e.getMessage());
        }
        return row == 1 ? "success" : "failure";
    }

    /**
     * 删除人工故障 支持多项删除
     *
     * @param ids id以”,“拼接成字符串
     * @return
     */
    public Object deleteManualAlarmFault(String ids) {
        Objects.requireNonNull(ids, "请至少选择一个要删除的故障");
        List<String> list = Arrays.stream(ids.split(","))
                .filter(item -> !"".equals(item))
                .collect(Collectors.toList());
        if (list == null || list.isEmpty()) {
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION, "请至少选择一个要删除的故障");
        }
        int row = 0;
        try {
            row = alarmWarningMapping.deleteManualAlarmFault(list);
        } catch (DataAccessException e) {
            logger.error("method[updateManualAlarmFault]编辑故障异常 error = {}", e.getMessage());
        }
        return row > 0 ? "成功删除了" + row + "条数据" : "删除失败";
    }

    public int updateAlarmFaultStatus(MtcFaultRecordPO po) {
        Objects.requireNonNull(po);
        Objects.requireNonNull(po.getFaultId(), "请选择要编辑的故障");
        int row = 0;
        try {
            String tableName = MANUAL_FAULT_SOURCE.equals(po.getFaultSource()) ? "mtc_manual_alarm_warning" : "mtc_alarm_warning";
            return alarmWarningMapping.updateAlarmFaultStatus(po.getFaultId(), tableName);
        } catch (DataAccessException e) {
            logger.error("method[updateAlarmFaultStatus]修改故障状态异常 error = {}", e.getMessage());
        }
        return row;
    }

    /**
     * 线路监控中 故障统计和预警统计
     *
     * @param lineId
     * @return 2023/02/14
     * <pre>情人节的浪漫</pre>
     * <pre>《The furthest distance in the world》</pre>
     * <pre>
     * The furthest distance in the world
     * is not the way from birth to the end.
     * It is when I stand in front of you
     * but you don't understand I love you.
     * </pre>
     * <pre>
     * The furthest distance in the world
     * is not when I stand in front of you
     * you don't know I love you
     * It is when my love is bewildering the soul
     * but I can't speak it out
     * </pre>
     * <pre>
     * The furthest distance in the world
     * is not that I can't say I love you.
     * It is after missing you deeply into my heart
     * I only can bury it in my heart
     * </pre>
     * <pre>
     * The furthest distance in the world
     * is not that I can't say to you I miss you
     * It is when we are falling in love
     * but we can't stay nearby
     * </pre>
     * <pre>
     * The furthest distance in the world
     * is not we love each other.
     * but can't stay together
     * It is we know our true love is breaking through the way
     * we turn a blind eye to it
     * </pre>
     * <pre>
     * So the furthest distance in the world
     * is not in two distant trees
     * It is the same rooted branches
     * but can't depend on each other in the wind
     * </pre>
     * <pre>
     * The furthest distance in the world
     * is not that can't depend on each other in the wind
     * It is in the blinking stars who only can look with each other
     * but their trade intersect.
     * </pre>
     * <pre>
     * The furthest distance in the world
     * is not in the blinking stars who only can look with each other
     * It is after the intersection
     * but they can't be found from then on afar
     * </pre>
     * <pre>
     * The furthest distance in the world
     * is not the light that is fading away.
     * It is the coincidence of us
     * are not supposed for the love.
     * </pre>
     * <pre>
     * The furthest distance in the world
     * is the love between the bird and fish.
     * One is flying in the sky,
     * the other is looking upon into the sea.</pre>
     */
    public JSONObject faultStatisticsOfLineMonitor(String lineId) {
        JSONObject returnObject = new JSONObject();
        try {
            //处理当lineId未传值的时候 使用默认值 【DEFAULT_LINE_ID】
            lineId = StringUtils.isEmpty(lineId) ? DEFAULT_LINE_ID : lineId;
            List<MtcAlarmWarningVO> list = alarmWarningMapping.getLineFaultStatistics(lineId);
            Map<String, Map<String, Map<Integer, Long>>> collect = list.stream()
                    .filter(o -> o.getFaultLevel() != null && o.getFaultLevel() > 0)
                    .collect(groupingBy(MtcAlarmWarningVO::getFaultSource, groupingBy(
                            MtcAlarmWarningVO::getVehicleCode, groupingBy(
                                    MtcAlarmWarningVO::getFaultLevel, counting()))));
            List<HashMap<String, Object>> faultStatistics = new ArrayList<>();
            List<HashMap<String, Object>> earlyWarningStatistics = new ArrayList<>();
            List<MtrVehiclePO> vehicleCodes = CacheUtils.getValue(VehicleCache.class, lineId);
            vehicleCodes.stream()
                    .map(MtrVehiclePO::getVehicleCode)
                    .forEach(o -> {
                                faultStatistics.add(faultLevelStatistics(collect, ALARM_FAULT_SOURCE, o));
                                earlyWarningStatistics.add(faultLevelStatistics(collect, WARNING_FAULT_SOURCE, o));
                            }
                    );
            returnObject.put("FaultStatistics", faultStatistics);
            returnObject.put("EarlyWarningStatistics", earlyWarningStatistics);
        } catch (DataAccessException e) {
            logger.error("method[faultStatisticsOfLineMonitor]故障和预警统计 error = {}", e.getMessage());
        }
        return returnObject;
    }

    /**
     * 封装车辆的故障或预警的故障等级分组统计数据
     *
     * @param faultList   所有的故障和告警集合
     * @param vehicleCode 车辆编码
     * @return HashMap<String, String> 车辆 故障或预警的故障等级分类统计map
     * @throws
     * <AUTHOR>
     * @date 2022-10-18
     * @version 1.1
     */
    public HashMap<String, Object> faultLevelStatistics(Map<String, Map<String, Map<Integer, Long>>> faultList, String faultSource, String vehicleCode) {
        HashMap<String, Object> modelInfo = new HashMap<>();
        modelInfo.put("vehicleCode", vehicleCode);
        Map<Integer, Long> faultLevelGroup = faultList.getOrDefault(faultSource, new HashMap<>(1)).getOrDefault(vehicleCode, new HashMap<>(1));
        modelInfo.put("SeriousFailure", faultLevelGroup.getOrDefault(3, 0L));
        modelInfo.put("GeneralFault", faultLevelGroup.getOrDefault(2, 0L));
        modelInfo.put("MinorFault", faultLevelGroup.getOrDefault(1, 0L));
        return modelInfo;
    }

    public MtcAlarmWarningPageVO getMtcAlarmWarningBySocketPage(String vehicleCode, String faultSource, Integer pageNum, Integer pageSize) {
        if (org.apache.commons.lang3.StringUtils.isAnyBlank(vehicleCode, faultSource)) {
            throw new RestApiException("1001", "车辆编码和故障源不能为空");
        }
        MtcAlarmWarningPageVO pageVO = new MtcAlarmWarningPageVO();
        //前期添加代码打印
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            pageVO.setList(new ArrayList<>());
            List<MtcAlarmWarningVO> alarmList = alarmWarningMapping.getTrainFaultListV2(vehicleCode, faultSource);
            int total = alarmList.size();
            int pages = 1;
            if (total > 0) {
                pages = (total + pageSize - 1) / pageSize;
                pageNum = Math.min(pageNum, pages);
                List<List<MtcAlarmWarningVO>> partition = Lists.partition(alarmList, pageSize);
                pageVO.setList(partition.get(pageNum - 1));
            }
            pageNum = Math.min(pageNum, pages);
            pageVO.setTotal(total);
            pageVO.setPageNum(pageNum);
            pageVO.setPageSize(pageSize);
            pageVO.setPages(pages);
            pageVO.setFaultSource(faultSource);
            stopWatch.stop();
            logger.info("vehicleCode is {} faultSource is {} time is {} result is {}", vehicleCode, faultSource, stopWatch.getTotalTimeMillis(), pageVO);
        } catch (Exception e) {
            logger.error("vehicleCode is {} faultSource is {}  error is {} ", vehicleCode, faultSource, e.getStackTrace());
        }
        return pageVO;
    }
}
