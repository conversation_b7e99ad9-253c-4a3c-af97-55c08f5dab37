package cc.crrc.manage.service.mtc;

import cc.crrc.manage.common.annotation.ParamReplace;
import cc.crrc.manage.common.entryEnum.model.*;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.*;
import cc.crrc.manage.common.vo.model.CheckParams;
import cc.crrc.manage.common.vo.model.ModelLine;
import cc.crrc.manage.common.vo.model.ModelNodeInfo;
import cc.crrc.manage.mapper.ekb.EkbFaultTypeMapping;
import cc.crrc.manage.mapper.mtc.MtcMechanismAlarmRuleMapping;
import cc.crrc.manage.mapper.mtr.MtrSubsystemDictMapping;
import cc.crrc.manage.mapper.mtr.MtrVehicleTypeMapping;
import cc.crrc.manage.pojo.SysFilePO;
import cc.crrc.manage.pojo.comm.signal.CommSignalVO;
import cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO;
import cc.crrc.manage.pojo.mtc.*;

import cc.crrc.manage.security.encryption.EncryptionUtil;
import cc.crrc.manage.service.SysFileService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.csource.fastdfs.FileInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @Author: Li Caisheng
 * @Date: 2019-12-21
 */
@Service
public class MtcMechanismAlarmRuleService {
    @Autowired
    private MtcMechanismAlarmRuleMapping ruleMapping;
    @Autowired
    private SysFileService sysFileService;
    @Autowired
    private MtrVehicleTypeMapping vehicleTypeMapping;
    @Autowired
    private EkbFaultTypeMapping faultTypeMapping;
    @Autowired
    private MtrSubsystemDictMapping subsystemDictMapping;
    @Value("${spring.kafka.topic_id_tmr}")
    private String topicIdTrainMechanismRule;

    @Autowired
    private KafkaTemplate kafkaTemplate;

    private final Logger logger = LoggerFactory.getLogger(MtcMechanismAlarmRuleService.class);


    @ParamReplace(param = {"description", "name"}, type = MtcMechanismAlarmRuleDTO.class)
    public PageInfo list(MtcMechanismAlarmRuleDTO ruleDTO) {
        if (null == ruleDTO.getVehicleTypeId()) {
            ruleDTO.setVehicleTypeId("0");
        }
        //加密状态 如果没有默认false
        if (null == ruleDTO.getEncryptionStatus()) {
            ruleDTO.setEncryptionStatus(false);
        }
        //encryp业务逻辑
        if (ruleDTO.getEncryptionStatus()) {
            MtcMechanismAlarmRuleDTO encryptionRuleFilter = new MtcMechanismAlarmRuleDTO();
            encryptionRuleFilter.setLineId(ruleDTO.getLineId());
            encryptionRuleFilter.setVehicleTypeId(ruleDTO.getVehicleTypeId());
            encryptionRuleFilter.setSignalCycle(ruleDTO.getSignalCycle());
            encryptionRuleFilter.setSubsystem(ruleDTO.getSubsystem());
            encryptionRuleFilter.setLocation(ruleDTO.getLocation());
            encryptionRuleFilter.setFaultTypeKey(ruleDTO.getFaultTypeKey());
            encryptionRuleFilter.setEncryptionStatus(ruleDTO.getEncryptionStatus());
            List<MtcMechanismAlarmRuleVO> listForFilterEncryp = ruleMapping.list(encryptionRuleFilter);
            Map<String, String> idNameMap = new HashMap<>();
            List<String> idList = new ArrayList<>();
            try {
                listForFilterEncryp.forEach(data -> {
                    if (data.getEncryptionStatus()) {
                        String encryptionName = null;
                        try {
                            encryptionName = EncryptionUtil.decrypt(data.getName(), EncryptionUtil.KEY, EncryptionUtil.UNICODE);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        data.setName(encryptionName);
                        idNameMap.put(data.getId(), encryptionName);
                    }
                    idNameMap.put(data.getId(), data.getName());
                });

                Set set = idNameMap.keySet();
                for (Object o : set) {
                    if (StringUtils.isNotEmpty(idNameMap.get(o)) &&
                            StringUtils.isNotEmpty(ruleDTO.getName()) && idNameMap.get(o).contains(ruleDTO.getName())) {
                        idList.add(o.toString());
                    }
                }
                if (idList.size() > 0) {
                    // 分页，未传分页信息，默认不分页。
                    int currentPage = ruleDTO.getPageNumber();
                    int pageSize = ruleDTO.getPageSize();
                    PageHelper.startPage(currentPage, pageSize);
                    List<MtcMechanismAlarmRuleVO> list = ruleMapping.ruleListForEncryption(idList);
                    decryptRuleList(list);
                    return new PageInfo<>(list);
                }
            } catch (Exception e) {
                logger.error(e.getMessage());
            }
        }


        // 分页，未传分页信息，默认不分页。
        int currentPage = ruleDTO.getPageNumber();
        int pageSize = ruleDTO.getPageSize();
        PageHelper.startPage(currentPage, pageSize);

        //查询机理故障
        List<MtcMechanismAlarmRuleVO> listMtcMechanismAlarmRule = new ArrayList<>();
        try {
            listMtcMechanismAlarmRule = ruleMapping.list(ruleDTO);
            listMtcMechanismAlarmRule.forEach(data -> {
                if (data.getEncryptionStatus()) {
                    String encryptionDescription = null;
                    String encryptionName = null;
                    try {
                        encryptionDescription = EncryptionUtil.decrypt(data.getDescription(), EncryptionUtil.KEY, EncryptionUtil.UNICODE);
                        encryptionName = EncryptionUtil.decrypt(data.getName(), EncryptionUtil.KEY, EncryptionUtil.UNICODE);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    data.setDescription(encryptionDescription);
                    data.setName(encryptionName);
                }
            });
        } catch (Exception e) {
            logger.error(e.getMessage());
        }

        return new PageInfo<>(listMtcMechanismAlarmRule);
    }

    public Object add(MtcMechanismAlarmRuleDTO ruleDTO) {
        ruleDTO.setId(PrimaryKeyGenerator.generatorId());
        MtcMechanismAlarmRulePO rulePO = ruleDTO2PO(ruleDTO);
        String userId = UserUtils.getUserId();
        rulePO.setCreateBy(userId);
        rulePO.setModifyBy(userId);
        rulePO.setEnable(false);
        //todo  加密操作
        if (ruleDTO.getEncryptionStatus()) {
            String encryptionDescription = EncryptionUtil.encrypt(ruleDTO.getDescription(), EncryptionUtil.KEY, EncryptionUtil.UNICODE);
            String encryptionName = EncryptionUtil.encrypt(ruleDTO.getName(), EncryptionUtil.KEY, EncryptionUtil.UNICODE);

            rulePO.setDescription(encryptionDescription);
            rulePO.setName(encryptionName);
        }
        Boolean enable = rulePO.getEnable();
        if (ruleMapping.add(rulePO) == 1) {
            //用于kafka发送是否删除判断
            boolean operate = true;
            sendKafka(rulePO.getId(), operate, enable);
            return "保存成功";
        } else {
            LoggerUtils.error(logger, "数据库插入失败");
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    public Object update(MtcMechanismAlarmRuleDTO ruleDTO) {
        MtcMechanismAlarmRulePO rulePO = ruleDTO2PO(ruleDTO);
        String userId = UserUtils.getUserId();
        rulePO.setModifyBy(userId);
        //todo  加密操作
        if (ruleDTO.getEncryptionStatus()) {
            String encryptionDescription = EncryptionUtil.encrypt(ruleDTO.getDescription(), EncryptionUtil.KEY, EncryptionUtil.UNICODE);
            String encryptionName = EncryptionUtil.encrypt(ruleDTO.getName(), EncryptionUtil.KEY, EncryptionUtil.UNICODE);
            rulePO.setDescription(encryptionDescription);
            rulePO.setName(encryptionName);
        }
        Boolean enable = ruleDTO.getEnable();
        if (ruleMapping.update(rulePO) == 1) {
            boolean operate = true;
            sendKafka(rulePO.getId(), operate, enable);
            return "修改成功";
        } else {
            LoggerUtils.error(logger, "数据库更新失败");
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    /**
     * 更新测试状态，单独接口主要为了不给kafka发送信息
     * @param id
     * @param testStatus
     * @return
     */
    public Object updateTestStatus(String id, boolean testStatus) {
        try{
            String modifyBy = UserUtils.getUserId();
            Integer row = ruleMapping.updateTestStatus(id,testStatus,modifyBy);
            return row != null && row > 0 ? "修改成功" : "修改失败";
        }catch (DataAccessException e){
            logger.error("Method[updateTestStatus] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }

    }

    /**
     * 填充树图json、树图图片、创建修改信息以外的机理信息，用来将新增、编辑机理信息页面的数据写入数据库
     *
     * @param ruleDTO
     * @return
     */
    private MtcMechanismAlarmRulePO ruleDTO2PO(MtcMechanismAlarmRuleDTO ruleDTO) {
        MtcMechanismAlarmRulePO rulePO = new MtcMechanismAlarmRulePO();
        rulePO.setId(ruleDTO.getId());
        rulePO.setVehicleTypeId(ruleDTO.getVehicleTypeId());
        rulePO.setFaultTypeKey(ruleDTO.getFaultTypeKey());
        rulePO.setName(ruleDTO.getName());
        rulePO.setContent(ruleDTO.getContent());
        rulePO.setEnable(ruleDTO.getEnable());
        rulePO.setDescription(ruleDTO.getDescription());
        rulePO.setLineId(ruleDTO.getLineId());
        rulePO.setSignalCycle(ruleDTO.getSignalCycle());
        rulePO.setTestStatus(ruleDTO.getTestStatus());
        rulePO.setEncryptionStatus(ruleDTO.getEncryptionStatus());
        rulePO.setPuwStatue(ruleDTO.getPuwStatue());
        return rulePO;
    }

    /**
     * 保存树图的json和图片
     *
     * @param ruleEditDTO
     * @return
     */
    public Object edit(MtcMechanismAlarmRuleEditDTO ruleEditDTO) {
        HashMap<String, Object> map = new HashMap<>();
        String id = ruleEditDTO.getId();
        String base64Str = ruleEditDTO.getBase64Str();

        try {
            //首先校验json是否正确
            checkJson(ruleEditDTO.getContent());
        } catch (Exception e) {
            LoggerUtils.error(logger, e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.FILE_DATA_ILLEGAL, e.getMessage());
        }
        try {
            // 保存图片到集群
            FastDFSFile file = base64ToFile(id.toString(), base64Str);
            String[] uploadResults = FastDFSClient.upload(file);
            // 保存集群图片信息到postgres
            SysFilePO sysFilePO = new SysFilePO();
            sysFilePO.setName(file.getName());
            String path = FastDFSClient.getTrackerUrl() + uploadResults[0] + "/" + uploadResults[1];
            sysFilePO.setUrl(path);
            sysFilePO.setType("Mechanism Image");
            sysFilePO.setFormat(file.getExt());
            FileInfo fileInfo = FastDFSClient.getFile(uploadResults[0], uploadResults[1]);//get info from fastdfs
            if (fileInfo == null) {
                return "图片文件保存失败";
            }
            sysFilePO.setSize(fileInfo.getFileSize());
            sysFilePO.setHashCode(Long.toHexString(fileInfo.getCrc32()));
            sysFilePO.setDelFlag(0);
            sysFilePO.setRemark("hash_code is crc32");
            sysFilePO.setGroup(uploadResults[0]);
            sysFilePO.setFileLocation(uploadResults[1]);
            String fileId = sysFileService.addSysFile(sysFilePO);
            // 保存图片postgres信息到机理表
            MtcMechanismAlarmRulePO rulePO = new MtcMechanismAlarmRulePO();
            rulePO.setId(id);
            //todo  加密操作
            if (ruleEditDTO.getEncryptionStatus()) {
                String encryptionContent = EncryptionUtil.encrypt(ruleEditDTO.getContent(), EncryptionUtil.KEY, EncryptionUtil.UNICODE);
                rulePO.setContent(encryptionContent);
            } else {
                rulePO.setContent(ruleEditDTO.getContent());
            }

            rulePO.setImageFile(fileId);
            String userId = UserUtils.getUserId();
            rulePO.setModifyBy(userId);
            //增加判断逻辑
            MtcMechanismAlarmRulePO mrule = ruleMapping.selectById(id);
            Boolean enable = mrule.getEnable();
            if (ruleMapping.update(rulePO) == 1) {
                boolean operate = true;
                sendKafka(rulePO.getId(), operate, enable);
                return "修改成功";
            } else {
                LoggerUtils.error(logger, "数据库更新失败");
                throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
            }
        } catch (Exception e) {
            LoggerUtils.error(logger, e);
            throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION);
        }
    }

    /**
     * base64格式为data:image/{具体格式，如jpeg，png等};base64,{字符串}
     */
    private FastDFSFile base64ToFile(String fileName, String base64Str) throws Exception {
        // Base64解码
        String[] strings = base64Str.split(";base64,");
        String ext = strings[0].replaceFirst("data:image/", "");
        String name = fileName + "." + ext;
        Base64.Decoder decoder = Base64.getDecoder();
        byte[] b = decoder.decode(strings[1]);
        for (int i = 0; i < b.length; ++i) {
            if (b[i] < 0) {
                b[i] += 256;
            }
        }
        return new FastDFSFile(name, b, ext);
    }


    /**
     * @throws
     * @Title: sendKafka
     * @Description: 发送机理规则信息至kafka中
     * @param: [ruleEditDTO]
     * @return: java.lang.Object
     * @date: 2020/11/3 14:25
     * @author: Heshenglun
     */
    public Object sendKafka(String id, boolean operate, boolean enable) {
        try {
            MtcMechanismAlarmRulePO mrule = ruleMapping.selectById(id);
            if (null != mrule.getEncryptionStatus() && mrule.getEncryptionStatus() && null != mrule.getContent()) {
                String decryptContent = EncryptionUtil.decrypt(mrule.getContent(), EncryptionUtil.KEY, EncryptionUtil.UNICODE);
                mrule.setContent(decryptContent);
            }
            if (operate == true) {
                //大数据根据del字段判断 机理规则是否启用 2021-06-25 lixin基于原逻辑进行修改（后续可进行重新设计迭代现有的逻辑）
                //但是del在web业务里只描述了机理规则处于删除状态(del=1)还是属于新增或修改状态（del=0）
                //所以使能enable用来判断新增或修改状态（del=0）时 机理规则是否启用 也就是大数据是否用该规则进行处理分析
                if (enable) {
                    mrule.setDel(0);
                } else {
                    mrule.setDel(1);
                }
            } else {
                mrule.setDel(1);
            }
            String json = JSON.toJSONString(mrule);
//           kafkaTemplate.send("TestRule",json);
            kafkaTemplate.send(topicIdTrainMechanismRule, json);
            return "kafka上传成功";
        } catch (Exception e) {
            LoggerUtils.error(logger, "kafka上传失败");
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION, "kafka上传失败");
        }

    }


    public Object delete(String ruleIdList) {
        if (ruleIdList == null || ruleIdList == "") {
            return 0;
        }
        String[] idsArr = ruleIdList.split(",");
        //Long[] idsList = new Long[idsArr.length];
        for (int i = 0, len = idsArr.length; i < len; i++) {
            //idsList[i] = new Long(idsArr[i]);
            boolean operate = false;
            Boolean enable = false;
            sendKafka(idsArr[i], operate, enable);
            ruleMapping.delete(idsArr[i]);
        }
        return "SUCCESS";
    }

    public String getJson(String id) {
        try {
            Map<String, Object> map = ruleMapping.getJson(id);
            if (map.containsKey("content") && map.containsKey("encryptionStatus")) {
                Boolean encryptionStatus = (Boolean) map.get("encryptionStatus");
                if (encryptionStatus) {
                    //todo 解密操作
                    if (null != map.get("content")) {
                        String decryptContent = null;
                        decryptContent = EncryptionUtil.decrypt((String) map.get("content"), EncryptionUtil.KEY, EncryptionUtil.UNICODE);
                        return decryptContent;
                    } else {
                        return null;
                    }
                } else {
                    String content = map.get("content").toString();
                    return content;
                }
            } else {
                return null;
            }
        } catch (Exception e) {
            LoggerUtils.error(logger, "查询规则数据异常！");
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object vehicleTypes() {
        return vehicleTypeMapping.listVehicleType();
    }

    public List<EkbFaultTypeDTO> faults(String vehicleTypeId, String location, String subsystem) {
        EkbFaultTypeDTO faultType = new EkbFaultTypeDTO();
        if (null == vehicleTypeId) {
            vehicleTypeId = "0";
        }
        faultType.setVehicleTypeId(vehicleTypeId);
        faultType.setLocation(location);
        faultType.setSubsystem(subsystem);
        return faultTypeMapping.listEkbFaultType(faultType);
    }

    public Object systems(String vehicleTypeId) {
        // todo jgy 此处应将vehicleTypeId改为vehicleId
        return subsystemDictMapping.listSubsystemDictByVehicleId(vehicleTypeId);
    }

    public Object booleanSignal(String vehicleTypeId, String location, String subsystem, String nameCn) {
        List<CommSignalVO> signalVOS = ruleMapping.booleanSignal(vehicleTypeId, location, subsystem, nameCn);
        return signalVOS;
    }

    public Object analogSignal(String vehicleTypeId, String location, String subsystem, String nameCn) {
            return ruleMapping.analogSignal(vehicleTypeId, location, subsystem, nameCn);
    }

    public Object faultList(String vehicleTypeId, String location, String subsystem) {
        EkbFaultTypeDTO dto = new EkbFaultTypeDTO();
        dto.setVehicleTypeId(vehicleTypeId);
        dto.setLocation(location);
        dto.setSubsystem(subsystem);
        return faultTypeMapping.faultList(dto);
    }

    public Object mechanismAlarmRuleListByStruCodeAndVehicleType(String vehicleTypeId, String structureCode, int pageNumber, int pageSize) {
        try {
            // 分页
            PageHelper.startPage(pageNumber, pageSize);
            List<MtcMechanismAlarmRuleVO> list = ruleMapping.mechanismAlarmRuleListByStruCodeAndVehicleType(vehicleTypeId, structureCode);
            return new PageInfo<>(list);
        } catch (Exception e) {
            logger.error("Method[mechanismAlarmRuleListByStruCodeAndVehicleType] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }


    public Object ruleListForCopy(String lineId, String name, Boolean encryptionStatus) {
        try {
            if (encryptionStatus) {
                MtcMechanismAlarmRuleDTO encryptionRuleFilter = new MtcMechanismAlarmRuleDTO();
                encryptionRuleFilter.setLineId(lineId);
                List<MtcMechanismAlarmRuleVO> listForFilterEncryp = ruleMapping.list(encryptionRuleFilter);
                Map<String, String> idNameMap = new HashMap<>();
                List<String> idList = new ArrayList<>();
                try {
                    listForFilterEncryp.forEach(data -> {
                        if (data.getEncryptionStatus()) {
                            String encryptionName = null;
                            try {
                                encryptionName = EncryptionUtil.decrypt(data.getName(), EncryptionUtil.KEY, EncryptionUtil.UNICODE);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            data.setName(encryptionName);
                            idNameMap.put(data.getId(), encryptionName);
                        }
                        idNameMap.put(data.getId(), data.getName());
                    });
                } catch (Exception e) {
                    logger.error(e.getMessage());
                }
                Set set = idNameMap.keySet();
                for (Object o : set) {
                    if (StringUtils.isNotEmpty(idNameMap.get(o)) &&
                            StringUtils.isNotEmpty(name) && idNameMap.get(o).contains(name)) {
                        idList.add(o.toString());
                    }
                }
                List<MtcMechanismAlarmRuleVO> list = ruleMapping.ruleListForCopyEncryption(idList);
                decryptRuleList(list);
                return list;
            }

            List<MtcMechanismAlarmRuleVO> list = ruleMapping.ruleListForCopy(lineId, name, encryptionStatus);
//            decryptRuleList(list);
            return list;
        } catch (Exception e) {
            logger.error("Method[ruleListForCopy] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public void decryptRuleList(List<MtcMechanismAlarmRuleVO> list) {
        try {
            list.forEach(data -> {
                if (data.getEncryptionStatus()) {
                    String encryptionDescription = null;
                    String encryptionName = null;
                    try {
                        encryptionDescription = EncryptionUtil.decrypt(data.getDescription(), EncryptionUtil.KEY, EncryptionUtil.UNICODE);
                        encryptionName = EncryptionUtil.decrypt(data.getName(), EncryptionUtil.KEY, EncryptionUtil.UNICODE);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    data.setDescription(encryptionDescription);
                    data.setName(encryptionName);
                }
            });
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }


    //===============机理规则校验2022-05-06======================

    /**
     * 校验j机理规则son
     *
     * @param content
     */
    public void checkJson(String content) {
        //        字符串数据转json
        JSONObject jsonObject = JSON.parseObject(content);
        //todo 校验json字符串里的内容是否变更
        JSONArray jsonArray = jsonObject.getJSONArray(KeyOfRuleJsonEnum.CELLS.getKey());

        JSONArray errors = new JSONArray();

        //把jsonArray中数据组装到checkParams
        CheckParams checkParams = handleLineData(jsonArray, errors);
        //检验故障节点数量是否正确（5存在多个故障节点、6无故障节点）
        doCheckFaultCount(checkParams, errors);

        //校验所有节点端口连接数是或否正确（1存在未连接组件 3存在1点多输入问题）
        doCheckNodePortsCount(checkParams, errors);
        //判断节点定义是否为空 （2存在未定义组件）
        doNonNullCheck(checkParams.getAllNodeMap(), errors);
        //(模拟量和数字量)
        doCheckDeviceName(checkParams.getAllNodeMap(), errors);
        //8、校验公式无法执行（只校验一种情况，常量值为0的时候，不能链接到除法算数节点的IN2端口上）（8计算公式无法执行）
        doCheckDivFormula(checkParams, errors);

        //无变量校验(7、界面无变量)
        doCheckNoVariable(checkParams, errors);

//      //校验环线（4存在环线）暂时不用
//        doCheckLoopLine(checkParams,errors);

        if (errors.size() > 0) {
            throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION.getErrorCode(),
                    String.valueOf(errors));
        }

    }


    /**
     * 组装校验数据
     *
     * @param jsonArray
     * @return
     */
    public CheckParams handleLineData(JSONArray jsonArray, JSONArray errors) {
        //jsonArray中所有线的信息
        List<ModelLine> modelLineList = new ArrayList<>();
        //节点id - 节点name
        HashMap<String, String> initialNodeMap = new HashMap<>();
        HashMap<String, ModelNodeInfo> allNodeMap = new HashMap<>();
        HashMap<String, ModelNodeInfo> allConstantNodeMap = new HashMap<>();
        HashMap<String, ModelNodeInfo> allFunctionNodeMap = new HashMap<>();

        CheckParams checkParams = new CheckParams();

        jsonArray.stream().forEach(item -> {
            JSONObject dataOfArr = (JSONObject) item;
            String nodeId = (String) dataOfArr.get(KeyOfRuleJsonEnum.ID.getKey());
            String nodeName = (String) dataOfArr.get(KeyOfRuleJsonEnum.NAME.getKey());


            if (!dataOfArr.containsKey(KeyOfRuleJsonEnum.SHAPE.getKey())) {
                errors.add("json数据有问题,节点信息没有shape字段!");
//                throw  new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION.getErrorCode(),
//                        "json数据有问题,节点信息没有shape字段");
            }
            String shape = (String) dataOfArr.get(KeyOfRuleJsonEnum.SHAPE.getKey());
            JSONObject dataJson = dataOfArr.getJSONObject(KeyOfRuleJsonEnum.DATA.getKey());

            //是否是故障节点
            if (ValueOfRuleJsonEnum.V_FAULT.getValue().equals(shape)) {
                checkParams.setFaultNodeId(nodeId);
                checkParams.setFaultNodeCount(checkParams.getFaultNodeCount() + 1);
            }


            //是否是线
            if (ValueOfRuleJsonEnum.EDGE.getValue().equals(shape)) {
                ModelLine modelLine = new ModelLine();
                JSONObject source = dataOfArr.getJSONObject(KeyOfRuleJsonEnum.SOURCE.getKey());
                JSONObject target = dataOfArr.getJSONObject(KeyOfRuleJsonEnum.TARGET.getKey());

                modelLine.setId((String) dataOfArr.get(KeyOfRuleJsonEnum.ID.getKey()));
                modelLine.setSourceCell((String) source.get(KeyOfRuleJsonEnum.CELL.getKey()));
                modelLine.setSourcePort((String) source.get(KeyOfRuleJsonEnum.PORT.getKey()));
                modelLine.setTargetCell((String) target.get(KeyOfRuleJsonEnum.CELL.getKey()));
                modelLine.setTargetPort((String) target.get(KeyOfRuleJsonEnum.PORT.getKey()));
                modelLineList.add(modelLine);
            } else {
                String type = (String) dataJson.get(KeyOfRuleJsonEnum.TYPE.getKey());

                JSONObject ports = (JSONObject) dataOfArr.get(KeyOfRuleJsonEnum.PORTS.getKey());
                JSONArray items = ports.getJSONArray(KeyOfRuleJsonEnum.ITEMS.getKey());
                //把items里的id（端口类型）装到list中
                ModelNodeInfo modelNodeInfo = new ModelNodeInfo();
                List<JSONObject> itemIdList = new ArrayList<>();
                items.forEach(data -> {
                    JSONObject jsonItem = (JSONObject) data;
                    //数据整理
                    String itemId = (String) jsonItem.get(KeyOfRuleJsonEnum.ID.getKey());
                    JSONObject itemInfo = new JSONObject();

                    if (jsonItem.containsKey(KeyOfRuleJsonEnum.ATTRS.getKey())) {
                        JSONObject attrs = (JSONObject) jsonItem.get(KeyOfRuleJsonEnum.ATTRS.getKey());
                        JSONObject text = (JSONObject) attrs.get(KeyOfRuleJsonEnum.TEXT.getKey());
                        String anchor = (String) text.get(KeyOfRuleJsonEnum.TEXT.getKey());

                        itemInfo.put("anchor", anchor);
                        itemInfo.put("ID", itemId);
                        itemIdList.add(itemInfo);
                    } else {
                        itemInfo.put("ID", itemId);
                        itemIdList.add(itemInfo);
                    }
                });
                modelNodeInfo.setItemIdList(itemIdList);
                modelNodeInfo.setId(nodeId);
                modelNodeInfo.setName(nodeName);
                modelNodeInfo.setData(dataJson);

                allNodeMap.put(nodeId, modelNodeInfo);

                //常量节点判断
                if (ModelNodeEnum.V_NUMBER.getType().equals(shape)) {
                    allConstantNodeMap.put(nodeId, modelNodeInfo);
                }
                //所有函数节点
                if (FunctionNodeEnum.isExist(type)) {
                    allFunctionNodeMap.put(nodeId, modelNodeInfo);
                }

                //判断该节点只有一个out端口
                if (items.size() == 1) {
                    JSONObject jsonData = items.getJSONObject(0);
                    if (ValueOfRuleJsonEnum.OUT.getValue().equals(jsonData.get(KeyOfRuleJsonEnum.ID.getKey()))) {
                        initialNodeMap.put(nodeId, nodeName);
                    }
                }
            }
        });

        checkParams.setAllNodeMap(allNodeMap);
        checkParams.setInitialNodeMap(initialNodeMap);
        checkParams.setModelLineList(modelLineList);
        checkParams.setAllConstantNodeMap(allConstantNodeMap);
        checkParams.setAllFunctionNodeMap(allFunctionNodeMap);

        return checkParams;
    }


    /**
     * 校验环线
     *
     * @param checkParams
     */
    public void doCheckLoopLine(CheckParams checkParams, JSONArray errors) {
        List<ModelLine> modelLineList = checkParams.getModelLineList();

        //校验环线 遍历每一个初始节点
        for (Map.Entry<String, String> entry : checkParams.getInitialNodeMap().entrySet()) {
            //用HashSet校验环线
            HashSet<String> nodeIdSet = new HashSet<>();
            String startId = entry.getKey();

            //通过起始id一直找下去，碰到闭关抛异常 或者下一个节点是故障节点退出
            findNextNode(startId, modelLineList, nodeIdSet, checkParams.getAllNodeMap(),
                    checkParams.getFaultNodeId(), errors);

        }
    }

    private void findNextNode(String nodeId, List<ModelLine> modelLineList, HashSet<String> nodeIdSet,
                              HashMap<String, ModelNodeInfo> allNodeMap, String faultNodeId, JSONArray errors) {
        modelLineList.forEach(line -> {

            //节点id，没找到下一根线，那就进行下一个起始节点的循环遍历
            if (nodeId.equals(line.getSourceCell())) {
                if (nodeIdSet.contains(nodeId)) {
                    StringBuffer errorInfo = new StringBuffer("节点名为:");

                    boolean flag = false;
                    for (String data : nodeIdSet) {
                        if (nodeId.equals(data)) {
                            flag = true;
                        }
                        if (flag) {
                            ModelNodeInfo info = allNodeMap.get(data);
                            if (info == null) {
                                throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION.getErrorCode(), "节点信息获取失败！");
                            }
                            errorInfo.append(info.getName()).append(",");
                        }
                    }


                    errorInfo.append("存在环线！");
                    errors.add(errorInfo);
                    if (errors.size() > 0) {
                        throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION.getErrorCode(),
                                String.valueOf(errors));
                    }
//                    throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION.getErrorCode(),errorInfo.toString());
                }
                nodeIdSet.add(nodeId);
                String nextNodeId = line.getTargetCell();

                if (faultNodeId.equals(nextNodeId)) {
                    return;
                }

                findNextNode(nodeId.replaceAll(nodeId, nextNodeId), modelLineList, nodeIdSet, allNodeMap, faultNodeId, errors);
            }
        });
    }


    /**
     * 校验节点端口数是否正确
     *
     * @param checkParams
     */
    public void doCheckNodePortsCount(CheckParams checkParams, JSONArray errors) {
        //校验所有节点端口的链接数是否正确
        for (Map.Entry<String, ModelNodeInfo> entry : checkParams.getAllNodeMap().entrySet()) {
            ModelNodeInfo modelNodeInfo = entry.getValue();
            String nodeId = modelNodeInfo.getId();
            String nodeName = modelNodeInfo.getName();
            modelNodeInfo.getItemIdList().forEach(data -> {
                int existCount = 0;
                for (ModelLine line : checkParams.getModelLineList()) {
                    if (nodeId.equals(line.getSourceCell()) && data.get("ID").equals(line.getSourcePort())) {
                        existCount++;
                    }
                    if (nodeId.equals(line.getTargetCell()) && data.get("ID").equals(line.getTargetPort())) {
                        existCount++;
                    }
                }
                if (existCount == 0) {
                    String errorInfo = "节点名字:'" + nodeName + "'端口:'" + data.get("anchor") + "',没有连接线！";
                    errors.add(errorInfo);
//                    throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION.getErrorCode(),errorInfo);
                }
//                此步由前端校验
//                if (existCount > 1){
//                    String errorInfo  = "节点名字:'"+nodeName + "'端口:'" + data + "',连接线大于1条！";
//                    errors.add(errorInfo);
////                    throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION.getErrorCode(),errorInfo);
//                }

            });
        }

    }

    /**
     * 校验故障节点数量
     *
     * @param checkParams
     */
    public void doCheckFaultCount(CheckParams checkParams, JSONArray errors) {
        int faultNodeCount = checkParams.getFaultNodeCount();
        if (faultNodeCount == 0) {
            errors.add("没有故障节点！");
//            throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION.getErrorCode(),"没有故障节点！");
        }
        if (faultNodeCount > 1) {
            errors.add("故障节点大于1！");
//            throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION.getErrorCode(),"故障节点大于1！");
        }

    }

    /**
     * 校验计算公式
     *
     * @param checkParams
     */
    public void doCheckDivFormula(CheckParams checkParams, JSONArray errors) {
        //jsonArray中所有线的信息
        List<ModelLine> modelLineList = checkParams.getModelLineList();
        //jsonArray中所有节点的信息
        HashMap<String, ModelNodeInfo> allNodeMap = checkParams.getAllNodeMap();
        //jsonArray中所有常量节点的信息
        HashMap<String, ModelNodeInfo> allConstantNodeMap = checkParams.getAllConstantNodeMap();
        for (HashMap.Entry<String, ModelNodeInfo> entry : allConstantNodeMap.entrySet()) {
            ModelNodeInfo info = entry.getValue();
            String id = info.getId();
            JSONObject dataJson = info.getData();
            int value = 0;
            //常量的值是0的时候在json中是整型，除此之外是String
            if (dataJson.get(KeyOfRuleJsonEnum.VALUE.getKey()) instanceof String) {
                value = Float.valueOf((String) dataJson.get(KeyOfRuleJsonEnum.VALUE.getKey())).intValue();
            }

            //常量值等于0，再考虑是不是被作为除数
            if (value != 0) {
                continue;
            }
            modelLineList.forEach(line -> {
                //判断这个线的来源节点id是否是这个常量且目标端口是IN2
                if (!id.equals(line.getSourceCell()) && !ValueOfRuleJsonEnum.IN2.getValue().equals(line.getTargetPort())) {
                    return;
                }
                String targetId = line.getTargetCell();
                ModelNodeInfo nodeInfo = allNodeMap.get(targetId);
                JSONObject targetJson = nodeInfo.getData();
                if (ModelNodeEnum.V_COUNT.getType().equals(targetJson.getString(KeyOfRuleJsonEnum.TYPE.getKey()))
                        && ValueOfRuleJsonEnum.DIV.getValue().equals(targetJson.get(KeyOfRuleJsonEnum.VALUE.getKey()))) {
                    errors.add("0不能作为除数！");
//                    throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION.getErrorCode(),"0不能作为除数！");
                }
            });
        }
    }

    /**
     * 传入类型，有的需要校验有的不需要校验
     *
     * @param allNodeMap
     */
    public void doNonNullCheck(HashMap<String, ModelNodeInfo> allNodeMap, JSONArray errors) {
        for (HashMap.Entry<String, ModelNodeInfo> entry : allNodeMap.entrySet()) {
            ModelNodeInfo modelNodeInfo = entry.getValue();
            String nodeName = modelNodeInfo.getName();
            JSONObject dataJson = modelNodeInfo.getData();


            Object value = dataJson.get(KeyOfRuleJsonEnum.VALUE.getKey());
            String type = dataJson.getString(KeyOfRuleJsonEnum.TYPE.getKey());
            //所有节点的data下必有type
            if (null == type) {
                errors.add("json数据有问题,节点信息没有type字段");
//                throw  new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION.getErrorCode(),
//                        "json数据有问题,节点信息没有type字段");
            }

            //部分节点data下面是没有value的
            if (NotCheckNullEnum.isExist(type)) {
                return;
            }

            if (null == value) {
                errors.add("节点'" + nodeName + "'的信息不能为空，请填写！");
//                throw  new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION.getErrorCode(),
//                        "节点“" + nodeName + "”的信息不能为空，请填写！");
            }
        }

    }


    /**
     * 校验无变量
     *
     * @param checkParams
     */
    public void doCheckNoVariable(CheckParams checkParams, JSONArray errors) {
        //jsonArray中所有线的信息
        List<ModelLine> modelLineList = checkParams.getModelLineList();
        //jsonArray中所有节点的信息
        HashMap<String, ModelNodeInfo> allFunctionNodeMap = checkParams.getAllFunctionNodeMap();
        //jsonArray中所有常量节点的信息
        HashMap<String, ModelNodeInfo> allConstantNodeMap = checkParams.getAllConstantNodeMap();

        for (HashMap.Entry<String, ModelNodeInfo> entry : allFunctionNodeMap.entrySet()) {
            ModelNodeInfo nodeInfo = entry.getValue();
            List<JSONObject> itemIdList = nodeInfo.getItemIdList();
            String nodeId = entry.getKey();
            String nodeName = nodeInfo.getName();

            //无变量标志
            AtomicBoolean noVariableFlag = new AtomicBoolean(false);
            itemIdList.forEach(item -> {
                //通过nodeId，item（端口类型）去匹配连接线线
                modelLineList.forEach(line -> {
                    String sourceCell = line.getSourceCell();
                    String targetCell = line.getTargetCell();
                    String port = line.getTargetPort();
                    //通过线的targetCell，只找输入端口的线，找来源是不是常量
                    if (item.get("ID").equals(port) && nodeId.equals(targetCell)) {
                        //不是常量的话，
                        if (!allConstantNodeMap.containsKey(sourceCell)) {
                            noVariableFlag.set(true);
                        }
                    }
                });
            });

            if (!noVariableFlag.get()) {
                errors.add("节点:'" + nodeName + "'，输入点无变量");
//                throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION.getErrorCode(),
//                        "节点:" + nodeName + "，输入点无变量");
            }
        }
    }


    /**
     * 判断设备名和英文是否正确
     *
     * @param allNodeMap
     */
    public void doCheckDeviceName(HashMap<String, ModelNodeInfo> allNodeMap, JSONArray errors) {
        for (HashMap.Entry<String, ModelNodeInfo> entry : allNodeMap.entrySet()) {
            ModelNodeInfo modelNodeInfo = entry.getValue();
            String nodeName = modelNodeInfo.getName();
            JSONObject dataJson = modelNodeInfo.getData();

            String type = (String) dataJson.get(KeyOfRuleJsonEnum.TYPE.getKey());
            if (ModelNodeEnum.V_A.getType().equals(type) || ModelNodeEnum.V_D.getType().equals(type)) {
                JSONObject value = dataJson.getJSONObject(KeyOfRuleJsonEnum.VALUE.getKey());
                if (null != value) {
                    String chineseName = value.getString(KeyOfRuleJsonEnum.NAME.getKey());
                    String englishName = value.getString(KeyOfRuleJsonEnum.ID.getKey());
                    if (org.apache.commons.lang3.StringUtils.isBlank(chineseName)) {
                        errors.add("节点'" + nodeName + "'的信息不能为空，请填写！");
//                    throw  new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION.getErrorCode(),
//                            "节点“" + nodeName + "”的信息不能为空，请填写！");
                    }
                    if (org.apache.commons.lang3.StringUtils.isBlank(englishName)) {
                        errors.add("节点'" + nodeName + "'的信息不能为空，请填写！");
//                    throw  new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION.getErrorCode(),
//                            "节点“" + nodeName + "”的信息不能为空，请填写！");
                    }
                } else {
                    errors.add("节点'" + nodeName + "'的信息不能为空，请填写！");
                }
            }
        }
    }

    public MtcMechanismAlarmRulePO selectById(String id){
        return ruleMapping.selectById(id);
    }
}
