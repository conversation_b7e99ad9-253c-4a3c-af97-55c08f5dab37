package cc.crrc.manage.service;

import cc.crrc.manage.cache.CacheUtils;
import cc.crrc.manage.cache.dict.BusDictCache;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.StringUtils;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.BusDictMapping;
import cc.crrc.manage.pojo.BusDict;
import cc.crrc.manage.pojo.BusDictQuery;
import cc.crrc.manage.pojo.BusDictVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;


@Service
public class BusDictService {

    @Autowired
    private BusDictMapping busDictMapper;

    /**
     * 查询业务字典
     *
     * @param typeCode 业务字典code
     * @param lineId
     * @return 业务字典
     */
    public PageInfo<BusDictVO> selectBusDictByCode(String typeCode, String lineId) {
        BusDictQuery busDictQuery = new BusDictQuery();
        PageHelper.startPage(busDictQuery.getPageNumber(), busDictQuery.getPageSize());
        return new PageInfo<>(busDictMapper.selectBusDictByCode(typeCode, lineId));
    }

    /**
     * 查询业务字典列表
     *
     * @param busDictQuery 业务字典
     * @return 业务字典
     */
    public PageInfo<BusDictVO> selectBusDictList(BusDictQuery busDictQuery) {
        PageHelper.startPage(busDictQuery.getPageNumber(), busDictQuery.getPageSize());
        List<BusDictVO> busDict = CacheUtils.getValue(BusDictCache.class, "ass_car_system_24");
        return new PageInfo<>(busDictMapper.selectBusDictList(busDictQuery));
    }

    /**
     * 新增业务字典
     *
     * @param busDict 业务字典
     * @return 结果
     */
    public int insertBusDict(BusDict busDict) {
        busDict.setId(PrimaryKeyGenerator.generatorId());
        String userId = UserUtils.getUserId();
        Date date = new Date();
        Timestamp t = new Timestamp(date.getTime());
        busDict.setDelFlag("0");
        busDict.setCreateTime(t);
        busDict.setCreateBy(userId);
        busDict.setLastModifyTime(t);
        busDict.setLastModifyBy(userId);
        return busDictMapper.insertBusDict(busDict);
    }

    /**
     * 修改业务字典
     *
     * @param busDict 业务字典
     * @return 结果
     */
    public int updateBusDict(BusDict busDict) {
        Date date = new Date();
        Timestamp t = new Timestamp(date.getTime());
        busDict.setDelFlag("0");
        busDict.setLastModifyTime(t);
        busDict.setLastModifyBy(UserUtils.getUserId());
        return busDictMapper.updateBusDict(busDict);
    }

    /**
     * 删除业务字典信息
     *
     * @param id 业务字典主键
     * @return 结果
     */
    public int deleteBusDictById(String id) {
        String lastModifyBy = UserUtils.getUserId();
        String lastModifyTime = String.valueOf(new Date());
        return busDictMapper.deleteBusDictById(id, lastModifyBy, lastModifyTime);
    }

    /**
     * @param busDictQuery 业务字典
     * @return 结果
     * @Description 查询字典类型字典通用接口
     **/
    public Object listDictUniversal(BusDictQuery busDictQuery) {
        //第一步 现根据TypeCode查询筛选范围
        String level = busDictMapper.getTypeLevel(busDictQuery.getTypeCode());
        if (StringUtils.isEmpty(level)) {
            throw new RuntimeException("未发现数据类型！");
        }
        //第二步 根据确定的 范围进行条件查询 先声明一个参数实体类
        if (StringUtils.isEmpty(busDictQuery.getTypeCode())) {
            throw new RuntimeException("缺少查询条件！");
        }
        BusDictQuery param = new BusDictQuery();
        if (level.equals("1")) {
            param.setTypeCode(busDictQuery.getTypeCode());
        } else if (level.equals("2")) {
            param.setTypeCode(busDictQuery.getTypeCode());
            String lineId = busDictQuery.getLineId();
            if (StringUtils.isEmpty(lineId)) {
                throw new RuntimeException("缺少线路信息！");
            }
            param.setLineId(lineId);
        } else {
            throw new RuntimeException("数据使用异常");
        }
        List<BusDictVO> sysDictVOS = busDictMapper.listDictUniversal(param);
        return sysDictVOS;
    }

}
