package cc.crrc.manage.service.excel;

import cc.crrc.manage.cache.CacheUtils;
import cc.crrc.manage.cache.dict.DictCache;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.common.utils.excelUtiles.EasyPoiUtil;
import cc.crrc.manage.mapper.SysDictMapping;
import cc.crrc.manage.mapper.analysis.AnalysisMapping;
import cc.crrc.manage.mapper.comm.SignalMapping;
import cc.crrc.manage.mapper.ekb.*;
import cc.crrc.manage.mapper.monitor.OilChangePeriodMapping;
import cc.crrc.manage.mapper.monitor.OtherDevicePeriodMapping;
import cc.crrc.manage.mapper.monitor.WashVehiclePeriodMapping;
import cc.crrc.manage.mapper.mtr.MtrVehicleMapping;
import cc.crrc.manage.mapper.mtr.MtrVehicleTypeMapping;
import cc.crrc.manage.pojo.SysDictDTO;
import cc.crrc.manage.pojo.SysDictVO;
import cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO;
import cc.crrc.manage.pojo.ekb.EkbReasonMeasurePO;
import cc.crrc.manage.pojo.excel.*;
import cc.crrc.manage.pojo.monitor.OilChangeImportExportPO;
import cc.crrc.manage.pojo.monitor.OtherDeviceImportExportPO;
import cc.crrc.manage.pojo.monitor.WashVehicleImportExportPO;
import cc.crrc.manage.pojo.monitor.WorkPeriodPO;
import cc.crrc.manage.pojo.mtc.MtcAlarmWarningDTO;
import cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import cc.crrc.manage.pojo.mtr.MtrVehicleTypeVO;
import cc.crrc.manage.pojo.stru.StruVehicleTypeStructurePO;
import cc.crrc.manage.service.SysDictService;
import cc.crrc.manage.service.ekb.EkbFaultTypeService;
import cc.crrc.manage.service.mtc.MtcAlarmWarningService;
import cc.crrc.manage.service.mtc.MtcFaultRecordService;
import cc.crrc.manage.service.stru.StruVehicleTypeStructureService;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;


import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static cc.crrc.manage.service.ekb.EkbFaultTypeService.UNIQUE_EXCEPTION_MESSAGE;

/**
 * @FileName ExcelService
 * <AUTHOR> xin
 * @Date 2020/4/23 9:12
 * @Version 1.0
 **/
@Service
public class ExcelService {
    private final Logger logger = LoggerFactory.getLogger(ExcelService.class);

    @Autowired
    private EkbFaultTypeMapping ekbFaultTypeMapping;
    @Autowired
    private MtcFaultRecordService mtcFaultRecordService;
    @Autowired
    private MtcAlarmWarningService mtcAlarmWarningService;
    @Autowired
    EkbFaultTypeService ekbFaultTypeService;

    /**
     * @Title: importEkbFaultForExcel
     * @Description: 故障字典导入  (包含修改信号点表的逻辑)
     * @param: [list]
     * @return: java.lang.Object
     * @date: 2021/05/17 18:35
     * @author: lixin
     */
    @Transactional(rollbackFor = Exception.class)
    public Object importEkbFaultTypeExcel(MultipartFile file) {
        //文件格式校验
        String fileName = file.getOriginalFilename();
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        if(!suffix.equals("xls")&&!suffix.equals("xlsx")){
            throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,fileName+":文件格式不正确，必须为Excel文件");
        }
        //查看 那些故障字典 没有信号对应
        List<String> errorEkbFaultTypeRecord = new ArrayList<>();
        ImportParams params = new ImportParams();
        //校验excel导入模板列名是否存在，不存在则报异常。
        String[] checkParams = {"线路","车辆型号","故障类型主键","车辆构型编码","故障中文名称","故障英文名称","故障编码","故障级别","故障所属车厢","故障类型"};
        Integer titleRows = 0;
        Integer headerRows = 1;
        params.setTitleRows(titleRows);
        params.setHeadRows(headerRows);
        //此方法为导入时，对excel中的列名进行校验，如果没有该字段，则导入失败。
        params.setImportFields(checkParams);
        List<EkbFaultTypeForExcelPO> list = new ArrayList<>();
        try {
             list = ExcelImportUtil.importExcel(file.getInputStream(), EkbFaultTypeForExcelPO.class, params);
        } catch (NoSuchElementException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION,"excel文件不能为空");
        } catch (Exception e) {
            String message = e.getMessage();
            if (message!=null){
                throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION, "故障导入文件不合法，请检查模板，或重新下载模板");
            }
        }
        String compareFaultTypeKey = "";
        String location = "";
        //处理故障表
        for (EkbFaultTypeForExcelPO excelPO : list) {
            try {
                String currentFaultTypeKey = excelPO.getFaultTypeKey().trim();
                if (excelPO.getLocation() != null&&!excelPO.getLocation().equals("")) {
                    location = location + "/" + excelPO.getLocation().trim();
                }
                if (list.size() == 0) {
                    throw new RestApiException(ExceptionInfoEnum.DATA_NOT_FOUND_EXCEPTION);
                }
                if (!compareFaultTypeKey.equals(currentFaultTypeKey)) {
                    //查询是否有同名故障
                    List<EkbFaultTypeDTO> nameList = ekbFaultTypeMapping.getFaultTypeByFaultTypeKey(currentFaultTypeKey);
                    if (nameList.size() != 0) {
                        throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION, "faultTypeKey：" + excelPO.getFaultTypeKey() + " 存在重复");
                    }
                    //保存上一个故障的location
                    ekbFaultTypeMapping.updateEkbFaultTypeByKey(compareFaultTypeKey, null, location.substring(1, location.length()));
                    //故障字典表
                    excelPO.setId(PrimaryKeyGenerator.generatorId());
                    if (excelPO.getVehicleTypeId().equals("绍兴2号线车型")) {
                        excelPO.setVehicleTypeId("129");
                    }
                    if (excelPO.getLineId().equals("绍兴2号线一期")) {
                        excelPO.setLineId("24");
                    }
                    if (excelPO.getFaultCategory().equals("人工故障")) {
                        excelPO.setFaultCategory("2");
                    } else {
                        excelPO.setFaultCategory("1");
                    }
                    ekbFaultTypeMapping.addEkbFaultTypeForExcel(excelPO);
                    //比较字段更新
                    compareFaultTypeKey = excelPO.getFaultTypeKey();
                    location = "";
                }
            } catch (DataAccessException e) {
                logger.error("Method[importEkbFaultCodeExcelNew] Error:{}", e.getMessage());
                if (e.getCause().getMessage().contains(UNIQUE_EXCEPTION_MESSAGE)) {
                    throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION,
                            "已存在相同故障(" + String.join(",",
                                    "故障代码：" + excelPO.getFaultCode(),
                                    "故障中文名称：" + excelPO.getNameCn(),
                                    "故障位置：" + excelPO.getLocation()) + ")");
                }
                throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION);
            }
        }
        return null;
    }

    public List<MtcFaultRecordExcelPO> findFaultWorkOrderList(MtcAlarmWarningDTO condition) {
        List<MtcFaultRecordExcelPO> list;
        try {
            List<MtcFaultRecordExcelPO> orderList = mtcFaultRecordService.findFaultWorkOrderList(condition);
            list = orderList.stream()
                    .sorted(Comparator.comparing(MtcFaultRecordExcelPO::getStartTime).reversed().thenComparing(MtcFaultRecordExcelPO::getConfirm))
                    .limit(10000)
                    .map(vo -> {
                        vo.setConfirm("0".equals(vo.getConfirm()) ? "未确认" : "已确认");
                        vo.setFaultSource("0".equals(vo.getFaultSource()) ? "自动故障" : "人工故障");
                        return vo;
                    })
                    .collect(Collectors.toList());
        } catch (DataAccessException e) {
            logger.error("Method[findFaultWorkOrderList] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION, "故障工单导出失败");
        }
        return list;
    }

    public List<MtcAlarmWarningForExcelPO> exportFaultExcel(MtcAlarmWarningVO condition) {
        Objects.requireNonNull(condition);
        Objects.requireNonNull(condition.getFaultSource(), "未知的故障类型");
        //缓存查询
        List<SysDictVO> faultLevel = CacheUtils.getValue(DictCache.class, StringUtils.join(DictCache.FAULT_LEVEL, "_", CacheUtils.METRO_LINE_ID, "_", CacheUtils.VEHICLE_TYPE_ID));
        List<SysDictVO> systemList = CacheUtils.getValue(DictCache.class, StringUtils.join(DictCache.ASS_CAR_SYSTEM, "_", CacheUtils.METRO_LINE_ID, "_", CacheUtils.VEHICLE_TYPE_ID));
        Map<String, String> levelMap = faultLevel.stream()
                .collect(Collectors.toMap(SysDictVO::getValue, SysDictVO::getLabel));
        Map<String, String> systemMap = systemList.stream()
                .collect(Collectors.toMap(SysDictVO::getValue, SysDictVO::getLabel));
        List<MtcAlarmWarningForExcelPO> list = mtcAlarmWarningService.exportFaultExcel(condition);
        return list.stream()
                .map(vo -> {
                    vo.setSubsystem(systemMap.getOrDefault(vo.getSubsystem(), "未知"));
                    vo.setFaultSource("2".equals(vo.getFaultSource()) ? "人工故障" : "1".equals(vo.getFaultSource()) ? "故障预警" : "自动故障");
                    vo.setFaultLevel(levelMap.getOrDefault(vo.getFaultLevel(), "未知"));
                    return vo;
                })
                .collect(Collectors.toList());
    }
}