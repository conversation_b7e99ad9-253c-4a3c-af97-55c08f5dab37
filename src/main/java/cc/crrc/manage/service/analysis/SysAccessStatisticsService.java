package cc.crrc.manage.service.analysis;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.StringUtils;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.common.utils.WebUtils;
import cc.crrc.manage.mapper.SysMenuMapping;
import cc.crrc.manage.mapper.analysis.SysAccessStatisticsMapping;
import cc.crrc.manage.pojo.SysLogDTO;
import cc.crrc.manage.pojo.SysMenuVO;
import cc.crrc.manage.pojo.analysis.SysAccessStatisticsPO;
import cc.crrc.manage.pojo.analysis.SysAccessStatisticsVO;
import cc.crrc.manage.service.SysDictService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import eu.bitwalker.useragentutils.Browser;
import eu.bitwalker.useragentutils.UserAgent;
import net.minidev.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class SysAccessStatisticsService {

    private final Logger logger = LoggerFactory.getLogger(SysDictService.class);
    @Autowired
    private SysAccessStatisticsMapping sysAccessStatisticsMapping;
    @Autowired
    private SysMenuMapping sysMenuMapping;


    public Object accessCount(String menuCode) {
        try {
            //根据menuCode 查出菜单相关数据
            SysMenuVO currentMenu = sysMenuMapping.selectMenu(menuCode);
            SysMenuVO parentMenu = sysMenuMapping.getParentMenuForAccess(currentMenu.getId());
            String parentMenuCode = parentMenu.getCode();
            String menuName = currentMenu.getTitle();
            //获取 ip 浏览器等等内容
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String userId = UserUtils.getUser().getId();
            String name = UserUtils.getUser().getName();
            String host = WebUtils.getIpAddress(request);
            String username = UserUtils.getUser().getUsername();
            String userAgent = request.getHeader("User-Agent");//浏览器信息
            //用户访问存储
            SysAccessStatisticsPO sysAccessStatisticsPO = new SysAccessStatisticsPO();
            sysAccessStatisticsPO.setId(PrimaryKeyGenerator.generatorId());
            sysAccessStatisticsPO.setMenuName(menuName);
            sysAccessStatisticsPO.setMenuCode(menuCode);
            sysAccessStatisticsPO.setParentMenuCode(parentMenuCode);
            sysAccessStatisticsPO.setUserId(userId);
            sysAccessStatisticsPO.setName(name);
            sysAccessStatisticsPO.setHost(host);
            sysAccessStatisticsPO.setUsername(username);
            sysAccessStatisticsPO.setUserAgent(userAgent);
            sysAccessStatisticsMapping.addAccessStatistics(sysAccessStatisticsPO);
            String a1  = request.getHeader("X-Forwarded-For");
            String a2  = request.getHeader("Proxy-Client-IP");
            String a3  = request.getHeader("WL-Proxy-Client-IP");
            String a4  = request.getHeader("HTTP_CLIENT_IP");
            String a5  = request.getHeader("HTTP_X_FORWARDED_FOR");
            String a6 = request.getHeader("x-real-ip");
            String a7 = request.getRemoteAddr();
            JSONObject a =  new JSONObject();
            a.put("X-Forwarded-For",a1);
            a.put("Proxy-Client-IP",a2);
            a.put("WL-Proxy-Client-IP",a3);
            a.put("HTTP_CLIENT_IP",a4);
            a.put("HTTP_X_FORWARDED_FOR",a5);
            a.put("x-real-ip",a6);
            a.put("unknow",a7);
            return a;
        } catch (Exception e) {
            logger.error("Method[addDict] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    public Object getVisitors(SysAccessStatisticsVO vo) {
        String userId = UserUtils.getUser().getId();
        vo.setUserId(userId);
        List<SysAccessStatisticsVO> accessStatisticsPOList;
        try {
            PageHelper.startPage(vo.getPageNumber(), vo.getPageSize());
            accessStatisticsPOList = sysAccessStatisticsMapping.getVisitors(vo);
            if (accessStatisticsPOList != null) {
                for (SysAccessStatisticsVO visitor : accessStatisticsPOList) {
                    UserAgent userAgent = UserAgent.parseUserAgentString(visitor.getUserAgent());
                    visitor.setBrowser(userAgent.getBrowser().getName());
                    visitor.setOperatingSystem(userAgent.getOperatingSystem().getName());
                }
            }
        } catch (Exception e) {
            logger.error("Method[addDict] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
        return new PageInfo<>(accessStatisticsPOList);
    }

    public Object getAgent(String startTime, String endTime) {
        String userId = UserUtils.getUser().getId();
        List<SysAccessStatisticsVO> agents = sysAccessStatisticsMapping.getAgent(startTime, endTime, userId);
        int chromeNum = 0, edgeNum = 0, firefoxNum = 0, ieNum = 0, safariNum = 0, operaNum = 0;
        for (SysAccessStatisticsVO agent : agents) {
            UserAgent userAgent = UserAgent.parseUserAgentString(agent.getUserAgent());
            Browser browser = userAgent.getBrowser();
            if (browser.getName().toLowerCase().indexOf("Chrome".toLowerCase()) != -1) {
                chromeNum = chromeNum + agent.getCount();
            }
            if (browser.getName().toLowerCase().indexOf("Edge".toLowerCase()) != -1) {
                edgeNum = edgeNum + agent.getCount();
            }
            if (browser.getName().toLowerCase().indexOf("Firefox".toLowerCase()) != -1) {
                firefoxNum = firefoxNum + agent.getCount();
            }
            if (browser.getName().toLowerCase().indexOf("Internet".toLowerCase()) != -1) {
                ieNum = ieNum + agent.getCount();
            }
            if (browser.getName().toLowerCase().indexOf("Safari".toLowerCase()) != -1) {
                safariNum = safariNum + agent.getCount();
            }
            if (browser.getName().toLowerCase().indexOf("Opera".toLowerCase()) != -1) {
                operaNum = operaNum + agent.getCount();
            }
        }
        JSONObject agentJson = new JSONObject();
        if (chromeNum > 0) {
            agentJson.put("Chrome", chromeNum);
        }
        if (edgeNum > 0) {
            agentJson.put("Edge", edgeNum);
        }

        if (firefoxNum > 0) {
            agentJson.put("Firefox", firefoxNum);
        }

        if (ieNum > 0) {
            agentJson.put("IE", ieNum);
        }

        if (safariNum > 0) {
            agentJson.put("Safari", safariNum);
        }

        if (operaNum > 0) {
            agentJson.put("Opera", operaNum);
        }
        System.out.println(agentJson.toString());

        return agentJson;
    }

    public List<Map<String, Object>> getCountByDate(String startTime, String endTime) {
        String userId = UserUtils.getUser().getId();
        List<Map<String, Object>> res = sysAccessStatisticsMapping.getCountByDate(startTime, endTime, userId);
        return res;
    }

    public Object getParentMenuCount(String startTime, String endTime) {
        String userId = UserUtils.getUser().getId();
        List<Map<String, Object>> res ;
        try {
            res = sysAccessStatisticsMapping.getParentMenuCount(startTime, endTime, userId);
        } catch (Exception e) {
            logger.error("Method[addDict] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
        return res;
    }

    public Object getMenuCountByDate(String parentMenuCode, String startTime, String endTime) {
        if (StringUtils.isEmpty(parentMenuCode)) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "缺少参数！");
        }
        String userId = UserUtils.getUser().getId();
        List<Map<String, Object>> res ;
        try {
           res = sysAccessStatisticsMapping.getMenuCountByDate(parentMenuCode, startTime, endTime, userId);
        } catch (Exception e) {
            logger.error("Method[addDict] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
        return res;
    }
}
