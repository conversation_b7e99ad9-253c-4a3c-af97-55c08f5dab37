package cc.crrc.manage.service.analysis;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import cc.crrc.manage.cache.CacheUtils;
import cc.crrc.manage.cache.dict.DictCache;
import cc.crrc.manage.cache.dict.VehicleCache;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.mapper.mtr.MtrVehicleMapping;
import cc.crrc.manage.pojo.SysDictVO;
import cc.crrc.manage.pojo.analysis.AnalysisParamPO;
import cc.crrc.manage.pojo.analysis.AnalysisParamVO;
import cc.crrc.manage.pojo.analysis.CollectionReportByDayDTO;
import cc.crrc.manage.pojo.excel.*;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import cc.crrc.manage.mapper.analysis.AnalysisMapping;

@Service
public class AnalysisService {

    private final static int VEHICLE_NUMBER = 11;
    @Autowired
    AnalysisMapping analysisMapping;
    @Autowired
    private MtrVehicleMapping mtrVehicleMapping;


    /**
     * @Description 各项能耗统计
     * @Param param
     **/
    public List<Map<String, String>> getEnergy(AnalysisParamVO param) {
        try {
            List<AnalysisParamPO> energy = analysisMapping.getEnergy(param);
            Map<String, String> energyMap = energy
                    .stream()
                    .collect(Collectors.toMap(AnalysisParamPO::getSignalNameEn, i -> String.format("%.1f", i.getIncreamentSum())));
            List<SysDictVO> dicts = CacheUtils.getValue(DictCache.class, "realtime_map_24_129");
            List<Map<String, String>> collect = dicts.stream()
                    .sorted(Comparator.comparing(SysDictVO::getSortNumber))
                    .map(i -> {
                        Map<String, String> map = new HashMap<>(dicts.size());
                        map.put("type", i.getLabel());
                        map.put("value", energyMap.containsKey(i.getCode()) ? energyMap.get(i.getCode()) : "0.0");
                        return map;
                    })
                    .collect(Collectors.toList());
            return collect;
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    public List<EnergyForExcelPO> getEnergyForExcel(String startTime, String endTime, String vehicleCode) {
        try {
            AnalysisParamVO param = new AnalysisParamVO();
            param.setStartTime(startTime);
            param.setEndTime(endTime);
            param.setVehicleCode(vehicleCode);
            return analysisMapping.getEnergyForExcel(param);
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    public List<MtrVehiclePO> listVehicleorLine(String name) {
        try {
            return analysisMapping.listVehicleorLine(name);
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    public List<MtrVehiclePO> listVehicleor(String vehicleId) {
        try {
            return analysisMapping.listVehicleor(vehicleId);
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    /**
     * @Description 里程趋势
     * @Param param
     **/
    public List<Map<String, String>> mileageTrend(AnalysisParamVO param) {
        try {
            return analysisMapping.mileageTrend(param);
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    public List<MileageTrendForExcelPO> mileageTrendForExcel(String startTime, String endTime, String vehicleCode) {
        try {
            AnalysisParamVO param = new AnalysisParamVO();
            param.setStartTime(startTime);
            param.setEndTime(endTime);
            param.setVehicleCode(vehicleCode);
            return analysisMapping.mileageTrendForExcel(param);
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    /**
     * @Description 总能耗趋势
     * @Param param
     **/
    public List<Map<String, String>> totalEnergyTrend(AnalysisParamVO param) {
        try {
            return analysisMapping.totalEnergyTrend(param);
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    public List<TotalEnergyTrendForExcelPO> totalEnergyTrendForExcel(String startTime, String endTime, String vehicleCode) {
        try {
            AnalysisParamVO param = new AnalysisParamVO();
            param.setStartTime(startTime);
            param.setEndTime(endTime);
            param.setVehicleCode(vehicleCode);
            return analysisMapping.totalEnergyTrendForExcel(param);
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    /**
     * @Description 各能耗趋势
     * @Param param
     **/
    public List<Map<String, String>> traConTrend(AnalysisParamVO param) {
        try {
            List<AnalysisParamPO> energy = analysisMapping.traConTrend(param);
            List<Map<String, String>> collect = energy.stream()
                    .map(i -> {
                        Map<String, String> map = new HashMap<>(energy.size());
                        map.put("time", i.getTime());
                        map.put("type", i.getLabel());
                        map.put("accumlation", i.getAccumlation());
                        return map;
                    })
                    .collect(Collectors.toList());
            return collect;
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "各能耗趋势查询数据异常！");
        }
    }

    public List<AnalysisParamForExcelPO> traConTrendForExcel(String vehicleCode, String startTime, String endTime) {
        return analysisMapping.traConTrendForExcel(vehicleCode, startTime, endTime);
    }

    /**
     * @Description 各列车数据量统计
     * @Param param
     **/
    public PageInfo<CollectionReportByDayDTO> collectionReportCount(AnalysisParamVO param) {
        try {
            int currentPage = param.getPageNumber();
            int pageSize = param.getPageSize();
            PageHelper.startPage(currentPage, pageSize);
            List<CollectionReportByDayDTO> list = analysisMapping.collectionReportCount(param);
            return new PageInfo<>(list);
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    /**
     * @throws
     * @Title: collectionReportVehicleCount
     * @Description: 线路数据统计-线路各列车数据量时间统计
     * @param: [param]
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     * @date: 2020/11/27 16:12
     * @author: Heshenglun
     */
    public List<Map<String, String>> collectionReportVehicleCount(AnalysisParamVO param) {
        try {
            return analysisMapping.collectionReportVehicleCount(param);
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    /**
     * @Description 线路各车辆运行总时间
     **/
    public List<Map<String, String>> totalRunTime(String lineId) {
        try {
            return analysisMapping.totalRunTime(lineId);
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    /**
     * @Description 线路各车辆运行总路程
     **/
    public List<Map<String, String>> totalRunDistance(String lineId) {
        try {
            return analysisMapping.totalRunDistance(lineId);
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    /**
     * @Description 各车辆的各个能耗统计
     **/
    public List<Map<String, String>> totalEngyByVehicle(String lineId) {
        try {
            List<Map<String, String>> list = analysisMapping.totalEngyByVehicle(lineId);
            return list;
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    /**
     * @Description 线路的里程趋势
     **/
    public List<Map<String, String>> lineMileageTrend(String lineId) {
        try {
            return analysisMapping.lineMileageTrend(lineId);
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    /**
     * @throws
     * @Title: getLineEnergy
     * @Description: 全网运行统计-根据线路获取所有信息
     * @param: param
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     * @date: 2020/11/5 18:34
     * @author: Heshenglun
     */
    public List<Map<String, String>> getLineEnergy(AnalysisParamVO param) {
        try {
            List<MtrVehiclePO> vehicleList = CacheUtils.getValue(VehicleCache.class, param.getLineId());
            List<SysDictVO> dicts = CacheUtils.getValue(DictCache.class, "realtime_map_" + param.getLineId() + "_129");
            List<AnalysisParamPO> energys = analysisMapping.getTotolEnergy(param.getStartTime(), param.getEndTime(), param.getLineId());
            Map<String, String> energyMap = energys.stream().collect(Collectors.toMap(AnalysisParamPO::getSignalNameEn, i -> String.format("%.1f", i.getIncreamentSum())));
            List<Map<String, String>> list = dicts.stream()
                    .sorted(Comparator.comparing(SysDictVO::getSortNumber))
                    .map(i -> {
                        Map<String, String> map = new HashMap<>(dicts.size());
                        map.put("type", i.getLabel());
                        map.put("value", energyMap.containsKey(i.getCode()) ? energyMap.get(i.getCode()) : "0.0");
                        return map;
                    })
                    .collect(Collectors.toList());
            //多少辆列车
            Map map = new HashMap();
            map.put("carNum", vehicleList.size());
            list.add(map);
            return list;
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    public List<LineAllRunExcelPO> lineAllRunExcel(String startTime, String endTime, String lineId) {
        List<Map<String, Object>> lineAlllist = analysisMapping.lineAllRunExcel(startTime, endTime, lineId);
        List<Map<String, Object>> vehicleList = mtrVehicleMapping.listVehicleExcel(lineId);
        List<LineAllRunExcelPO> list = new ArrayList<LineAllRunExcelPO>();
        LineAllRunExcelPO line = new LineAllRunExcelPO();
        line.setNum(vehicleList.size());
        list.add(line);
        for (int i = 0; i < lineAlllist.size(); i++) {
            LineAllRunExcelPO lineExcel = new LineAllRunExcelPO();
            lineExcel.setType(lineAlllist.get(i).get("type").toString());
            lineExcel.setValue(lineAlllist.get(i).get("value").toString());
            list.add(lineExcel);
        }
        return list;

    }

    /**
     * @throws
     * @Title: getLineEnergyPic
     * @Description: 全网运行统计-根据线路获取所有信息(用于饼图)
     * @param: [param]
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     * @date: 2020/11/6 11:23
     * @author: Heshenglun
     */
    public List<Map<String, String>> getLineEnergyPic(AnalysisParamVO param) {
        try {
            return analysisMapping.getTotalEnergyPic(param.getStartTime(), param.getEndTime(), param.getLineId());
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    /**
     * @throws
     * @Title: getLineAll
     * @Description: 全网运行统计-根据线路获取各个信息(时间、里程、能耗)
     * @param: param
     * @return: java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     * @date: 2020/11/6 8:45
     * @author: Heshenglun
     */
    public List<Map<String, String>> getLineAll(AnalysisParamVO param) {
        try {
            List<Map<String, String>> list = analysisMapping.getLineAll(param.getStartTime(), param.getEndTime(), param.getLineId());
            return list;
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    public List<Map<String, String>> getFaultByLocation(AnalysisParamVO param) {
        try {
            return analysisMapping.getFaultByLocation(param.getStartTime(), param.getEndTime(), param.getVehicleId());
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    public List<FaultByLocationForExcelPO> FaultByLocation(AnalysisParamVO param) {
        try {
            String startTime = param.getStartTime();
            String endTime = param.getEndTime();
            String vehicleId = param.getVehicleId();
            return analysisMapping.FaultByLocation(startTime, endTime, vehicleId);
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    public List<Map<String, String>> getFaultBySubsystem(AnalysisParamVO param) {
        try {
            String startTime = param.getStartTime();
            String endTime = param.getEndTime();
            String vehicleId = param.getVehicleId();
            return analysisMapping.getFaultBySubsystem(startTime, endTime, vehicleId);
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    public List<Map<String, String>> getFaultByLevel(AnalysisParamVO param) {
        try {
            String startTime = param.getStartTime();
            String endTime = param.getEndTime();
            String vehicleId = param.getVehicleId();
            return analysisMapping.getFaultByLevel(startTime, endTime, vehicleId);
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    public List<Map<String, String>> getFaultByDate(AnalysisParamVO param) {
        try {
            String startTime = param.getStartTime();
            String endTime = param.getEndTime();
            String vehicleId = param.getVehicleId();
            return analysisMapping.getFaultByDate(startTime, endTime, vehicleId);
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    public Map<String, Map<String, Object>> getSystemFaultByLevel(AnalysisParamVO param) {
        try {
            String startTime = param.getStartTime();
            String endTime = param.getEndTime();
            String vehicleId = param.getVehicleId();
            //获取故障数据集合
            List<Map<String, String>> list = analysisMapping.getSystemFaultByLevel(startTime, endTime, vehicleId);
            //创建需返回的Map
            Map<String, Map<String, Object>> faultMap = new HashMap<>();
            for (Map<String, String> m : list) {
                if (!faultMap.containsKey(m.get("subsystem"))) {
                    //先全部创建好
                    Map<String, Object> faultList = new LinkedHashMap<>();
                    faultList.put("subsystem", m.get("subsystem"));
                    faultList.put("subName", m.get("subname"));
                    faultList.put("faultLevel0", 0);
                    faultList.put("faultLevel1", 0);
                    faultList.put("faultLevel2", 0);
                    faultList.put("faultLevel3", 0);
                    //再进行赋值
                    StringBuffer sb = new StringBuffer("faultLevel");
                    sb.append(String.valueOf(m.get("faultlevel")));
                    faultList.put(sb.toString(), m.get("count"));
                    faultMap.put(m.get("subsystem"), faultList);
                } else {
                    StringBuffer sb = new StringBuffer("faultLevel");
                    sb.append(String.valueOf(m.get("faultlevel")));
                    Map<String, Object> faultList = faultMap.get(m.get("subsystem"));
                    faultList.put(sb.toString(), m.get("count"));
                    faultMap.put(m.get("subsystem"), faultList);
                }
            }
            return faultMap;
        } catch (DataAccessException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "查询数据异常！");
        }
    }

    /**
     * 传入当前日期，大数据统计使用流处理，每N分钟刷新数据库，所以会有当日的数据，需要提出当日的数据，只查昨天的数据
     *
     * @param traCode
     * @return
     */
    public List<HashMap<String, Object>> getVehicleHistoryData(String traCode) {
        return analysisMapping.getVehicleHistoryData(traCode, LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
    }
}
