package cc.crrc.manage.service.analysis;

import cc.crrc.manage.cache.CacheUtils;
import cc.crrc.manage.cache.dict.VehicleCache;
import cc.crrc.manage.common.utils.Constants;
import cc.crrc.manage.common.utils.DateUtils;
import cc.crrc.manage.common.utils.tsdb.TSDBUtils;
import cc.crrc.manage.common.utils.tsdb.iotdb.IoTDBUtils;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/07/05/ 13:49
 */
@Service
public class LineAnalysisService {

    @Autowired
    private TSDBUtils tsdbUtils;

    /**
     * 线路数据统计-历史回溯
     * @param time
     * @param vehicleCode
     */
    public JSONObject dataStatistics(String time, String vehicleCode){

        //日期校验-当前时间以前
        Date date = DateUtils.parseDate(time);
        if(date.compareTo(new Date()) > 0){
            return new JSONObject();
        }

        //车辆信息
        List<MtrVehiclePO> vehicleList = CacheUtils.getValue(VehicleCache.class, Constants.LINE_ID);
        Map<String, Integer> vehicleMap = vehicleList.stream().collect(Collectors.toMap(MtrVehiclePO::getVehicleCode, MtrVehiclePO::getVehicleStorageGroup));
        //存储组
        int vehicleStorageGroup = vehicleMap.get(vehicleCode);

        //开始时间
        Date startOfDay = DateUtils.getStartOfDay(date);
        long startTime = startOfDay.getTime();
        //结束时间
        Date endOfDay = DateUtils.getEndOfDay(date);
        long endTime = endOfDay.getTime();

        //查询数据
        String originSqlModel = "SELECT sum('statistics') FROM root.statistics.sg{0,number,#}.{1} group by ([{2,number,#},{3,number,#}),1m)";
        String sql = MessageFormat.format(originSqlModel, vehicleStorageGroup, vehicleCode, startTime, endTime);
        ArrayList<ArrayList<String>> dbQueryResult = ((IoTDBUtils) tsdbUtils).query2IoTDB(sql);

        JSONObject dataJson = new JSONObject();
        JSONArray dataValueArray = new JSONArray();
        for (ArrayList<String> rowInfo : dbQueryResult) {
            JSONObject timeAndValue = new JSONObject();
            timeAndValue.put("time", rowInfo.get(0));
            timeAndValue.put("value", Double.valueOf(rowInfo.get(1)));
            dataValueArray.add(timeAndValue);
        }

        dataJson.put(vehicleCode, dataValueArray);
        return dataJson;
    }
}
