package cc.crrc.manage.service.eva;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.RedisUtils;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.SysDictMapping;
import cc.crrc.manage.mapper.eva.HealthResultMapping;
import cc.crrc.manage.mapper.eva.HealthRuleMapping;
import cc.crrc.manage.mapper.mtc.MtcAutoFaultRecordMapping;
import cc.crrc.manage.mapper.mtr.MtrSubsystemDictMapping;
import cc.crrc.manage.mapper.mtr.MtrVehicleMapping;
import cc.crrc.manage.pojo.SysDictDTO;
import cc.crrc.manage.pojo.SysDictVO;
import cc.crrc.manage.pojo.eva.EvaHealthResultPO;
import cc.crrc.manage.pojo.eva.EvaHealthRuleDTO;
import cc.crrc.manage.pojo.eva.EvaHealthRulePO;
import cc.crrc.manage.pojo.eva.EvaSysHealtVO;
import cc.crrc.manage.pojo.mtr.MtrSubsystemDictPO;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 健康评分服务层
 *
 * <AUTHOR> GuoYang
 * 2020/02/17
 **/
@Service
public class HealthyScopeService {
    private final Logger logger = LoggerFactory.getLogger(HealthyScopeService.class);
    //用于健康评分判断分值区间时使用
    private final static String itemNameStr = "严重;中等;轻微;事件;轻微和警告扣分上限";

    @Autowired
    private HealthRuleMapping healthRuleMapping;
    @Autowired
    private HealthResultMapping healthResultMapping;
    @Autowired
    private MtcAutoFaultRecordMapping mtcAutoFaultRecordMapping;
    @Autowired
    private MtrVehicleMapping mtrVehicleMapping;
    @Autowired
    private MtrSubsystemDictMapping mtrSubsystemDictMapping;
    @Autowired
    private SysDictMapping sysDictMapping;

    /**
     * 查询车辆健康评分概览
     * 若指定线路id，则查询该线路id下所有车辆的评分数据；
     * 否则，查询全部车辆的评分数据。
     *
     * @param metroLineId 车辆线路id
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR> GuoYang
     * 2020-02-19
     **/
    public JSONObject getScopeListByLineId(String metroLineId,String startTime, String endTime) {
        JSONObject result = new JSONObject();
        try {
            List<MtrVehiclePO> vehiclePOList = mtrVehicleMapping.listVehicle(metroLineId);
            //获取投运级别分数配置
            EvaHealthRuleDTO evaHealthRuleDTO = new EvaHealthRuleDTO();
            evaHealthRuleDTO.setCategory("g");
//            evaHealthRuleDTO.setVehicleTypeId("129");//贵阳2号线车型id：129
            evaHealthRuleDTO.setLineId(metroLineId);
            List<EvaHealthRulePO> healthRuleList = healthRuleMapping.listHealthRule(evaHealthRuleDTO);
            //四种投运级别和对应健康状态分数范围(高投运级==健康,中投运级==良好,低投运级==可用,不可投运==病态)
            double high = 0, medium = 0,low = 0,cannot = 0;
            for (int i = 0; healthRuleList != null && i < healthRuleList.size(); i++) {
                String itemName = healthRuleList.get(i).getItemName();
                String itemValue = healthRuleList.get(i).getItemValue();
                if("高投运级".equals(itemName)){
                    high = Double.valueOf(itemValue);
                }else  if("中投运级".equals(itemName)){
                    medium = Double.valueOf(itemValue);
                }else if("低投运级".equals(itemName)){
                    low = Double.valueOf(itemValue);
                }else if("不可投运".equals(itemName)){
                    cannot = Double.valueOf(itemValue);
                }
            }
            double[] commisRule = {high,medium,low,cannot};
            int highByLine = 0,mediumByLine = 0,lowByLine = 0,cannotByLine = 0,vahivleCount = 1;
            double highPercent = 0.0,mediumPercent = 0.0,lowPercent = 0.0,cannotPercent = 0.0;
            if(vehiclePOList != null && vehiclePOList.size()>0){
                vahivleCount = vehiclePOList.size();
            }
            String redisName = "Health_Score_Data";
            String redisResult = JSONObject.toJSONString(RedisUtils.get(redisName));
            JSONObject redisJsonObject = JSONObject.parseObject(redisResult);
            // 实时数据
            JSONObject vehiclesJson = JSONObject.parseObject(redisJsonObject.getString("v"));
            // 按照子系统进行数据合并
            Map<String, Object> vehicleMap = JSONObject.parseObject(vehiclesJson.toJSONString(), new TypeReference<Map<String, Object>>() {
            });
            // 取出所有的key值进行遍
            Set<String> vehicleCodeSet = vehicleMap.keySet();
            JSONArray vehicleList = new JSONArray();

            //2021-06-09 lixin 添加  查询该线路下有的子系统都是那些
            SysDictDTO sysDictDTO = new SysDictDTO();
            sysDictDTO.setLineId(metroLineId);
            sysDictDTO.setTypeCode("ass_car_system");
            List<SysDictVO>sysDictVOS  = sysDictMapping.listDictUniversal(sysDictDTO);
            Map<String, Long> sysInfoMap = new HashMap<>();
            for(SysDictVO sysDictVO:sysDictVOS){
                sysInfoMap.put(sysDictVO.getLabel(),sysDictVO.getSortNumber());
            }
            //用作判断键值是否属于系统
            Set<String> sysInfoKeySet = sysInfoMap.keySet();

            for (MtrVehiclePO vehiclePO : vehiclePOList) {
                String vehicleCode = vehiclePO.getVehicleCode();
                JSONObject vehicleResult = new JSONObject();
                //车辆编码缩写
                vehicleResult.put("vehicleCodeAbbr",vehiclePO.getVehicleCodeAbbr());
                vehicleResult.put("vehicleCode", vehicleCode);
                //判断车辆离线状态
                String online = String.valueOf(RedisUtils.get(vehicleCode + "_online"));
                vehicleResult.put("onlineStatus",online == "true"?true:false);
                vehicleResult.put("onlineStatusSort",online == "true"?"在线":"不在线");
                if(!vehicleCodeSet.contains(vehicleCode)){
                    continue;
                }
                //redis中对应车的数据
                JSONObject infoJson = (JSONObject) vehiclesJson.get(vehicleCode);
                //List遍历出redis中的key
                List<String> infoKeyList = new ArrayList<>();
                Iterator keys = infoJson.keySet().iterator();
                while (keys.hasNext()){
                    String key = String.valueOf(keys.next());
                    infoKeyList.add(key);
                }
                //排序
                Collections.sort(infoKeyList);
                //返回的List
                List<EvaSysHealtVO> sysList = new ArrayList<>();
                //各子系统的健康分数和以及子系统数量
                double sumCcore  = 0;
                int num = 0;
                //车辆指导意见
                String trainInstruction = "";
                for (String infoKey : infoKeyList) {
                    // key中包含系统的单独存储json
                    if (sysInfoKeySet.contains(infoKey)) {
                        JSONObject infoKeyObj = infoJson.getJSONObject(infoKey);
                        EvaSysHealtVO sysObj = new EvaSysHealtVO();
                        sysObj.setScore(infoKeyObj.getString("score"));
                        sysObj.setSysCountFault(infoKeyObj.getString("sysCountFault"));
                        sysObj.setSysFault(infoKeyObj.getString("sysFault"));
                        sysObj.setSysName(infoKey);
                        sysObj.setVehicleCode(vehicleCode);
                        sysObj.setLineId(metroLineId);
                        //处理子系统指导意见
                        sysObj.setInstruction(getSysInstruction(sysObj,commisRule));
                        sumCcore += Double.valueOf(sysObj.getScore());
                        trainInstruction += sysObj.getSysName() + "当前健康评分为" + sysObj.getScore() + "分;";
                        num++;
                        sysList.add(sysObj);
                    } else {
                        // key中非包含系统的存入另外一个json
                        vehicleResult.put(infoKey, infoJson.get(infoKey));
                    }
                    if("metroScore".equals(infoKey)){
                        double metroScore = infoJson.getDouble(infoKey);
                        if(metroScore > high && metroScore<= 100){
                            highByLine++;
                            vehicleResult.put("operationLevel","高投运级");
                        }else if(metroScore > medium){
                            mediumByLine++;
                            vehicleResult.put("operationLevel","中投运级");
                        }else if(metroScore > low){
                            lowByLine++;
                            vehicleResult.put("operationLevel","低投运级");
                        }else if(metroScore >= cannot){
                            cannotByLine++;
                            vehicleResult.put("operationLevel","不可投运");
                        }else{
                            vehicleResult.put("operationLevel","车辆异常");
                        }
                    }
                    vehicleResult.put("sys", sysList);
                }
                if(num != 0 && (sumCcore/num) >= commisRule[0]){
                    trainInstruction += "当前车辆整体状态处于健康状态;";
                }else if(num != 0 && (sumCcore/num) >= commisRule[1]){
                    trainInstruction += "当前车辆整体状态处于良好状态;";
                }else if(num != 0 && (sumCcore/num) >= commisRule[2]){
                    trainInstruction += "当前车辆整体状态处于可用状态;";
                }else if(num != 0 && (sumCcore/num) >= commisRule[3]){
                    trainInstruction += "当前车辆整体状态处于不可用状态;";
                }else{
                    trainInstruction = "当前车辆处于离线状态或无健康评分;";
                }
                vehicleResult.put("instruction",trainInstruction.substring(0,trainInstruction.length()-1));
                vehicleList.add(vehicleResult);
            }
            vehicleList.sort(Comparator.comparing(obj -> ((JSONObject) obj).getString("onlineStatusSort").length()));
            //线路综合评分趋势图
            List<EvaHealthResultPO> historyHealtByLine = healthResultMapping.getScopeHistoryByLine(startTime,endTime,metroLineId);
            JSONObject lineResult = new JSONObject();
            //健康状态比例
            highPercent = new BigDecimal(highByLine*100.0/vahivleCount).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            mediumPercent = new BigDecimal(mediumByLine*100.0/vahivleCount).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            lowPercent = new BigDecimal(lowByLine*100.0/vahivleCount).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            cannotPercent = new BigDecimal(cannotByLine*100.0/vahivleCount).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            lineResult.put("highByLine",highByLine);
            lineResult.put("mediumByLine",mediumByLine);
            lineResult.put("lowByLine",lowByLine);
            lineResult.put("cannotByLine",cannotByLine);
            lineResult.put("highPercent",highPercent);
            lineResult.put("mediumPercent",mediumPercent);
            lineResult.put("lowPercent",lowPercent);
            lineResult.put("cannotPercent",cannotPercent);
            lineResult.put("historyHealtByLine",historyHealtByLine);
            lineResult.put("time", redisJsonObject.getString("time"));
            lineResult.put("lineScore", redisJsonObject.getString("lineScore"));
            //封装结果集
            result.put("vehicleList", vehicleList);
            result.put("lineResult",lineResult);
            return result;
        } catch (DataAccessException e) {
            logger.error("Method[getScopeListByLineId] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }


    /**
     * 查询车辆最新的评分记录
     *
     * @param vehicleCode 车辆编码
     * @return cc.crrc.manage.pojo.eva.EvaHealthResultPO
     * <AUTHOR> GuoYang
     * 2020-03-02
     **/
    public EvaHealthResultPO getLatestScopeByVehicleCode(String vehicleCode) {
        try {
            return healthResultMapping.getLatestScopeByVehicleCode(vehicleCode);
        } catch (DataAccessException e) {
            logger.error("Method[getLatestScopeByVehicleCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 查询车辆子系统历史评分记录
     *
     * @param vehicleCode 车辆编码
     * @param sysNameArray     系统名称数组
     * @param startTime
     * @param endTime
     * @return java.util.List<cc.crrc.manage.pojo.eva.EvaHealthResultPO>
     * <AUTHOR> GuoYang
     * 2020-03-02
     * 2021-04-19 修改 苏五——》贵阳2
     **/
    public JSONObject getSystemScopeByVehicleCodeAndSysName(String vehicleCode, List<String> sysNameArray, String startTime, String endTime) {
        JSONObject result = new JSONObject();
        try {
            List<EvaHealthResultPO> historyResultByVehicle = healthResultMapping.getScopeHistoryByVehicleCode(vehicleCode,startTime,endTime);
            List<EvaHealthResultPO> historyResultBySys = new ArrayList<>();
            List<Date> timeList = new ArrayList<>();
            //取得时间轴
            if(historyResultByVehicle != null){
                timeList = historyResultByVehicle.stream().map(EvaHealthResultPO::getCreateTime).collect(Collectors.toList());
            }
            JSONArray historyResult = new JSONArray();
            if(sysNameArray != null && sysNameArray.size() != 0){
                for (int i = 0; i < sysNameArray.size(); i++) {
                    String sysName = sysNameArray.get(i);
                    historyResultBySys = healthResultMapping.getSystemScopeByVehicleCodeAndSysName(vehicleCode, sysName,startTime,endTime);
                    historyResult.add(historyResultBySys);
                    if(timeList.size()<historyResultBySys.size()){
                        timeList = historyResultBySys.stream().map(EvaHealthResultPO::getCreateTime).collect(Collectors.toList());
                    }
                }
            }
            historyResult.add(historyResultByVehicle);
            result.put("historyResult",historyResult);
            result.put("timeList",timeList);
            return result;
        } catch (DataAccessException e) {
            logger.error("Method[getSystemScopeByVehicleCodeAndSysName] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }


    /**
     * 查询车辆所有子系统列表
     *
     * @param vehicleCode 车辆编码
     * @return java.util.List<java.lang.String>
     * <AUTHOR> GuoYang
     * 2020-03-02
     **/
    public JSONObject getAllSystemByVehicleCode(String vehicleCode) {
        try {
            JSONObject result = new JSONObject();
            MtrVehiclePO vehiclePO = mtrVehicleMapping.getVehicleByCode(vehicleCode);
            String vehicleId = vehiclePO.getId();
            List<MtrSubsystemDictPO> mtrSubsystemDictPOList = mtrSubsystemDictMapping.listSubsystemDictByVehicleId(vehicleId);
            result.put("mtrSubsystemDictPOList",mtrSubsystemDictPOList);
            return result;
//            return mtrSubsystemDictPOList.stream().map(MtrSubsystemDictPO::getLabel).collect(Collectors.toList());
        } catch (DataAccessException e) {
            logger.error("Method[getAllSystemByVehicleCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 查询健康评分规则并分组
     *
     * @param evaHealthRuleDTO 车型id、类别
     * @return java.util.List<cc.crrc.manage.pojo.eva.EvaHealthRulePO>
     * <AUTHOR> GuoYang
     * 2020-02-21
     * 2021-04-19 从苏州五号线迁移本地
     **/
    public List<EvaHealthRulePO> listHealthRule(EvaHealthRuleDTO evaHealthRuleDTO) {
        try {
            List<EvaHealthRulePO> evaHealthRulePOList = healthRuleMapping.listHealthRule(evaHealthRuleDTO);
            return evaHealthRulePOList;
        } catch (DataAccessException e) {
            logger.error("Method[listHealthRule] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 将待插入的规则类，插入到目标列表相应的分类中。
     *
     * @param targetList      目标列表
     * @param evaHealthRulePO 待插入的规则类
     * @param categoryName    分类名称
     * <AUTHOR> GuoYang
     * 2020-02-21
     **/
    private void setRule2List(List<JSONObject> targetList, EvaHealthRulePO evaHealthRulePO, String categoryName) {
        for (JSONObject target : targetList) {
            if (categoryName.equals(target.get("categoryName"))) {
                JSONArray ruleList = target.getJSONArray("ruleList");
                ruleList.add(evaHealthRulePO);
                return;
            }
        }
        JSONObject newCategoryMap = new JSONObject();
        newCategoryMap.put("categoryName", categoryName);
        List<EvaHealthRulePO> newRuleList = new ArrayList<>();
        newRuleList.add(evaHealthRulePO);
        newCategoryMap.put("ruleList", newRuleList);
        targetList.add(newCategoryMap);
    }

    /**
     * 添加健康评分规则信息
     *
     * @param evaHealthRulePO 健康评分规则信息
     * <AUTHOR> GuoYang
     * 2020-02-21
     **/
    @Transactional(rollbackFor = Exception.class)
    public void addHealthRule(EvaHealthRulePO evaHealthRulePO) {
        try {
            // 添加创建人信息
            String currentId = UserUtils.getUserId();
            evaHealthRulePO.setCreateBy(currentId);
            evaHealthRulePO.setModifyBy(currentId);
            // 保存健康评分规则至数据库
            int successCount = healthRuleMapping.addHealthRule(evaHealthRulePO);
            if (successCount != 1) {
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
            }
        } catch (DataAccessException e) {
            logger.error("Method[addHealthRule] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * @Description  健康评分项点值修改新接口
     * @Param evaHealthRule：json串
     * @Return JSONObject
     * <AUTHOR> zhijian
     * @Date 2020/9/15 15:19
     */
    public JSONObject updateHealthRule(String evaHealthRule,String category) {
        JSONObject result = new JSONObject();
        int rows = 0;
        try{
            if(evaHealthRule == null ){
                throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,"没有需要修改的参数,请重新配置");
            }
            JSONArray healthRuleArray = JSONArray.parseArray(evaHealthRule);
            List<EvaHealthRulePO> list = new ArrayList<>();
            if(healthRuleArray == null || healthRuleArray.size() == 0){
                throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,"没有需要修改的参数,请重新配置");
            }
            if("a".equals(category) || "b".equals(category)){
                //配置权重值为小数的项点
                BigDecimal valueSum = new BigDecimal("0");
                for (int i = 0; i < healthRuleArray.size(); i++) {
                    JSONObject obj = healthRuleArray.getJSONObject(i);
                    EvaHealthRulePO po = new EvaHealthRulePO();
                    po.setId(obj.getString("id"));
                    po.setItemName(obj.getString("itemName"));
                    String itemValue = obj.getString("itemValue");
                    //小数的正则表达式regex = 0\.\d+
                    if(!itemValue.matches("0\\.\\d+")){
                        throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,"权值只能是介于0~1之间的小数,请重新配置");
                    }
                    po.setItemValue(itemValue);
                    BigDecimal bigDecimal = new BigDecimal(itemValue);
                    valueSum = valueSum.add(bigDecimal);
                    list.add(po);
                }
                if(valueSum.doubleValue() != 1.0){
                    throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,"所有权值之和不等于1,请重新配置");
                }
            }else if("c".equals(category) || "d".equals(category) || "e".equals(category) || "f".equals(category)){
                //配置权重值为分数的项点
                JSONObject obj = healthRuleArray.getJSONObject(0);
                EvaHealthRulePO po = new EvaHealthRulePO();
                po.setId(obj.getString("id"));
                String itemName = obj.getString("itemName");
                po.setItemName(itemName);
                String itemValue = obj.getString("itemValue");
                //正整数的正则表达式regex = (0|[1-9]\+\d*)

                if(!itemValue.matches("(0|[1-9]\\d*)") ){
                    throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,"权值只能是0或正整数,请重新配置");
                }
                if(itemNameStr.contains(itemName) && (Integer.valueOf(itemValue)<0 || Integer.valueOf(itemValue)>100)){
                    throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,itemName + "权值只能是介于0~100之间的正整数,请重新配置");
                }
                po.setItemValue(itemValue);
                list.add(po);
            }else if("g".equals(category)){
                //配置健康状态和投运等级分数界限的项点
                int[] itemValues = new int[4];
                for (int i = 0; i < healthRuleArray.size(); i++) {
                    JSONObject obj = healthRuleArray.getJSONObject(i);
                    EvaHealthRulePO po = new EvaHealthRulePO();
                    po.setId(obj.getString("id"));
                    String itemValue = obj.getString("itemValue");
                    if(!itemValue.matches("(0|[1-9]\\d*)") || Integer.valueOf(itemValue)<0 || Integer.valueOf(itemValue)>100){
                        throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,"权值只能是介于0~100之间的正整数,请重新配置");
                    }
                    po.setItemValue(itemValue);
                    itemValues[i] = Integer.valueOf(itemValue);
                    list.add(po);
                }
                if(itemValues[0]<=itemValues[1] || itemValues[1]<=itemValues[2] || itemValues[2] <= itemValues[3]){
                    throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,"低等级的投运级别分数不能高于高等级的投运级别分数,请重新配置");
                }
            }
            if(!CollectionUtils.isEmpty(list)){
                for (EvaHealthRulePO rulePO : list) {
                    rows += healthRuleMapping.updateHealthRule(rulePO);
                }
            }
            result.put("result","成功保存了"+rows+"条权值");
        }catch (DataAccessException e){
            logger.error("Method[deleteHealthRuleById] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
        return result;
    }

    /**
     * 根据id删除健康评分规则
     *
     * @param id 健康评分规则id
     * <AUTHOR> GuoYang
     * 2020-02-21
     **/
    @Transactional(rollbackFor = Exception.class)
    public void deleteHealthRuleById(Long id) {
        try {
            // 删除健康评分规则信息
            Integer successCount = healthRuleMapping.deleteHealthRuleById(id);
            if (successCount != 1) {
                throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
            }
        } catch (DataAccessException e) {
            logger.error("Method[deleteHealthRuleById] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }
    /**
     * @Description  处理子系统的指导意见
     * @Return
     * <AUTHOR> zhijian
     * @Date 2021/3/17 11:19
     */
    private String getSysInstruction(EvaSysHealtVO sysObj,double[] commisRule) {
        Double score = Double.valueOf(sysObj.getScore());
        Integer sysCountFault = Integer.valueOf(sysObj.getSysCountFault());
        String sysName = sysObj.getSysName();
        String scoreStr = "";
        String sysFaultStr = "设备近期故障率属于可控范围内。";
        if(score >= commisRule[0]){
            scoreStr = sysName + "设备状态健康，请继续保持;";
        }else if(score >= commisRule[1]){
            scoreStr = sysName + "设备状态良好，请保持运维;";
        }else if(score >= commisRule[2]){
            scoreStr = sysName + "设备状态可用，请注意监督;";
        }else {
            scoreStr = sysName + "设备状态病态，请及时检修;";
        }
        if(sysCountFault >= 5){
            sysFaultStr = "系统近期故障发生次数较多，建议加强管理和设备的运维工作。";
        }
        return scoreStr + sysFaultStr;
    }

}
