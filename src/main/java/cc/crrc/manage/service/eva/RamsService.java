package cc.crrc.manage.service.eva;

import cc.crrc.manage.common.utils.StringUtils;
import cc.crrc.manage.mapper.eva.RamsMapping;
import cc.crrc.manage.mapper.mtr.MtrVehicleTypeMapping;
import cc.crrc.manage.pojo.component.OperatingParameterDTO;
import cc.crrc.manage.pojo.component.RamsParameterDTO;
import cc.crrc.manage.pojo.eva.RamsLineVehicleTypeVO;
import cc.crrc.manage.pojo.eva.RamsLineVo;
import cc.crrc.manage.pojo.eva.RamsfKindVO;
import cc.crrc.manage.pojo.line.LineDTO;
import cc.crrc.manage.pojo.mtr.MtrVehicleTypeVO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
/**
 * <AUTHOR> mingkuan
 * @Date 2020/7/8 10:20
 * @Version 1.0
 **/
@Service
public class RamsService {
     private static final Logger logger = LoggerFactory.getLogger(RamsService.class);

    @Autowired
    private RamsMapping ramsMapping;
    @Autowired
    private MtrVehicleTypeMapping vehicleTypeMapping;

    /**
     * @Description rams 线路
     * @Return java.util.List<cc.crrc.manage.pojo.eva.RamsLineVo>
     * <AUTHOR> wei
     * @Date
     * @Param []
    **/
    public List<RamsLineVo> getRamsLineList() {
        DecimalFormat df = new DecimalFormat("#");
        DecimalFormat df2 = new DecimalFormat("#.00");
        List<RamsLineVo> ramsLineVos = new ArrayList<>();
        //获取线路列表
        List<LineDTO> lineDTOList = ramsMapping.getLineDTOList();
        for (LineDTO lineDTO : lineDTOList) {
            RamsLineVo ramsLineVo = new RamsLineVo();

            //封装LineName
            ramsLineVo.setLineName(lineDTO.getName());
            //根据LineId统计获取运行里程、运行时间并封装
            RamsLineVo drivingDistanceAndTime = ramsMapping.getDrivingDistanceAndTime(lineDTO.getId());
            if(drivingDistanceAndTime == null){
                ramsLineVo.setTotalDistance("0");
                ramsLineVo.setTotalDrivingTime("0");
            }else {
                if(StringUtils.isEmpty(drivingDistanceAndTime.getTotalDistance())){
                    ramsLineVo.setTotalDistance("0");
                }
                if(StringUtils.isEmpty(drivingDistanceAndTime.getTotalDistance())){
                    ramsLineVo.setTotalDrivingTime("0");
                }else {
                    ramsLineVo.setTotalDistance(drivingDistanceAndTime.getTotalDistance());
                    ramsLineVo.setTotalDrivingTime(drivingDistanceAndTime.getTotalDrivingTime());
                }
            }

            //封装车型名称及数量
            List<RamsLineVehicleTypeVO> ramsLineVehicleTypeVOS = ramsMapping.getRamsVehicleTypeVOs(lineDTO.getId());
            ramsLineVo.setRamsLineVehicleTypeVOS(ramsLineVehicleTypeVOS);
            //计算当前线路所有车型车辆数量
            int vehicleNumberOneLine = ramsLineVehicleTypeVOS.stream().mapToInt(RamsLineVehicleTypeVO::getSameVehicleTypeNumber).sum();
            if(vehicleNumberOneLine == 0){
                break;
            }
            //封装rams参数列表
            List<RamsfKindVO> ramsfKindList = new ArrayList<>();
            RamsfKindVO ramsfKindVOF = new RamsfKindVO();
            RamsfKindVO ramsfKindVOR = new RamsfKindVO();
            RamsfKindVO ramsfKindVOA = new RamsfKindVO();
            RamsfKindVO ramsfKindVOM = new RamsfKindVO();
            RamsfKindVO ramsfKindVOS = new RamsfKindVO();
            List<RamsParameterDTO> ramsParameterDTOSF = new ArrayList<>();
            List<RamsParameterDTO> ramsParameterDTOSR = new ArrayList<>();
            List<RamsParameterDTO> ramsParameterDTOSA = new ArrayList<>();
            List<RamsParameterDTO> ramsParameterDTOSM = new ArrayList<>();
            List<RamsParameterDTO> ramsParameterDTOSS = new ArrayList<>();

            List<RamsParameterDTO> items = ramsMapping.getItems(lineDTO.getId());
            items.forEach(ramsParameterDTO -> {
                switch (ramsParameterDTO.getItemCategory()) {
                    case "服务故障":
                        ramsParameterDTO.setItemValue(df.format(Double.valueOf(ramsParameterDTO.getItemValue())));
                        ramsParameterDTOSF.add(ramsParameterDTO);
                        break;
                    case "可靠性R":
                        ramsParameterDTO.setItemValue(df.format(Double.valueOf(ramsParameterDTO.getItemValue())));
                        ramsParameterDTOSR.add(ramsParameterDTO);
                        break;
                    case "可用性A":
                        ramsParameterDTO.setItemValue(df2.format(Double.valueOf(ramsParameterDTO.getItemValue()))+"%");
                        ramsParameterDTOSA.add(ramsParameterDTO);
                        break;
                    case "维修性M":
                        ramsParameterDTO.setItemValue(df.format(Double.valueOf(ramsParameterDTO.getItemValue())));
                        ramsParameterDTOSM.add(ramsParameterDTO);
                        break;
                    case "安全性S":
                        ramsParameterDTO.setItemValue(df.format(Double.valueOf(ramsParameterDTO.getItemValue())));
                        ramsParameterDTOSS.add(ramsParameterDTO);
                        break;
                }
            });

            ramsfKindVOF.setItemCategory("服务故障");
            ramsfKindVOF.setRamsParameterDTOList(ramsParameterDTOSF);
            ramsfKindVOR.setItemCategory("可靠性R");
            ramsfKindVOR.setRamsParameterDTOList(ramsParameterDTOSR);
            ramsfKindVOA.setItemCategory("可用性A");
            ramsfKindVOA.setRamsParameterDTOList(ramsParameterDTOSA);
            ramsfKindVOM.setItemCategory("维修性M");
            ramsfKindVOM.setRamsParameterDTOList(ramsParameterDTOSM);
            ramsfKindVOS.setItemCategory("安全性S");
            ramsfKindVOS.setRamsParameterDTOList(ramsParameterDTOSS);
            ramsfKindList.add(ramsfKindVOF);
            ramsfKindList.add(ramsfKindVOR);
            ramsfKindList.add(ramsfKindVOA);
            ramsfKindList.add(ramsfKindVOM);
            ramsfKindList.add(ramsfKindVOS);
            ramsLineVo.setRamsfKindList(ramsfKindList);
            ramsLineVos.add(ramsLineVo);
        }
        return ramsLineVos;
    }

    public Object getVehicleTypeRams() {
        //取得 车型列表
        List<MtrVehicleTypeVO> vehicleTypeList = vehicleTypeMapping.listVehicleType();
        JSONArray result = new JSONArray();

        for (MtrVehicleTypeVO mtrVehicleTypeVO : vehicleTypeList) {
            if (ramsMapping.count(mtrVehicleTypeVO.getId()) > 0) {

                JSONObject vehicleTypeJson = new JSONObject();

                vehicleTypeJson.put("manufacturerName", mtrVehicleTypeVO.getManufacturerName());
                vehicleTypeJson.put("commType", mtrVehicleTypeVO.getCommType());

                List<OperatingParameterDTO> operatingParameterDTOList = ramsMapping.getParamList(mtrVehicleTypeVO.getId());
                double totalDistance = 0;
                double totalDrivingTime = 0;

                for (OperatingParameterDTO operatingParameterDTO : operatingParameterDTOList) {
                    if ("总运行里程".equals(operatingParameterDTO.getItemNameCn())) {
                        totalDistance += Double.parseDouble(operatingParameterDTO.getItemValue());
                    } else if ("总运行时间".equals(operatingParameterDTO.getItemNameCn())) {
                        totalDrivingTime += Double.parseDouble(operatingParameterDTO.getItemValue());
                    }
                }
                //运行里程  运行里程:'11km'
                vehicleTypeJson.put("totalDistance", totalDistance + " km");
                //运行时间  运行时间:'123h',
                vehicleTypeJson.put("totalDrivingTime", totalDrivingTime + " h");
                //车型:[{name:'A车',列:'2'},{name:'B车',列:'2'}]

                vehicleTypeJson.put("vehicleTypeName", mtrVehicleTypeVO.getName());
                vehicleTypeJson.put("count", ramsMapping.count(mtrVehicleTypeVO.getId()));

                JSONObject jsonObjectF = new JSONObject();
                JSONObject jsonObjectR = new JSONObject();
                JSONObject jsonObjectA = new JSONObject();
                JSONObject jsonObjectM = new JSONObject();
                JSONObject jsonObjectS = new JSONObject();

                JSONArray jsonArrayF = new JSONArray();
                JSONArray jsonArrayR = new JSONArray();
                JSONArray jsonArrayA = new JSONArray();
                JSONArray jsonArrayM = new JSONArray();
                JSONArray jsonArrayS = new JSONArray();

                List<RamsParameterDTO> listRamsParameter = ramsMapping.getRamsParamList(null, mtrVehicleTypeVO.getId());

                for (RamsParameterDTO ramsParameterDTO : listRamsParameter) {
                    if ("晚点故障".equals(ramsParameterDTO.getItemName())
                            || "一般故障".equals(ramsParameterDTO.getItemName())
                            || "故障总数".equals(ramsParameterDTO.getItemName())
                            || "危险发生次数".equals(ramsParameterDTO.getItemName())) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("itemName", ramsParameterDTO.getItemName());
                        jsonObject.put("itemValue", ramsParameterDTO.getItemValue());
                        jsonObject.put("unit", ramsParameterDTO.getUnit());
                        jsonArrayF.add(jsonObject);
                    }

                    if ("平均故障间隔里程".equals(ramsParameterDTO.getItemName())
                            || "平均服务故障间隔里程".equals(ramsParameterDTO.getItemName())
                            || "平均故障间隔时间".equals(ramsParameterDTO.getItemName())
                            || "平均服务故障间隔时间".equals(ramsParameterDTO.getItemName())) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("itemName", ramsParameterDTO.getItemName());
                        jsonObject.put("itemValue", ramsParameterDTO.getItemValue());
                        jsonObject.put("unit", ramsParameterDTO.getUnit());
                        jsonArrayR.add(jsonObject);
                    }

                    if ("固有可用度".equals(ramsParameterDTO.getItemName()) || "运行可用度".equals(ramsParameterDTO.getItemName())) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("itemName", ramsParameterDTO.getItemName());
                        jsonObject.put("itemValue", ramsParameterDTO.getItemValue());
//			        jsonObject.put("unit", ramsParameterDTO.getUnit());
                        jsonArrayA.add(jsonObject);
                    }

                    if ("平均修复时间".equals(ramsParameterDTO.getItemName())) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("itemName", ramsParameterDTO.getItemName());
                        jsonObject.put("itemValue", ramsParameterDTO.getItemValue());
                        jsonObject.put("unit", ramsParameterDTO.getUnit());
                        jsonArrayM.add(jsonObject);
                    }

                    if ("平均危险事件间隔时间".equals(ramsParameterDTO.getItemName())) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("itemName", ramsParameterDTO.getItemName());
                        jsonObject.put("itemValue", ramsParameterDTO.getItemValue());
                        jsonObject.put("unit", ramsParameterDTO.getUnit());
                        jsonArrayS.add(jsonObject);
                    }
                }

                jsonObjectF.put("itemCategory", "服务故障");
                jsonObjectF.put("list", jsonArrayF);

                jsonObjectR.put("itemCategory", "可靠性R");
                jsonObjectR.put("list", jsonArrayR);

                jsonObjectA.put("itemCategory", "可靠性A");
                jsonObjectA.put("list", jsonArrayA);

                jsonObjectM.put("itemCategory", "维修性M");
                jsonObjectM.put("list", jsonArrayM);

                jsonObjectS.put("itemCategory", "安全性S");
                jsonObjectS.put("list", jsonArrayS);

                JSONArray listValueArray = new JSONArray();
                listValueArray.add(jsonObjectF);
                listValueArray.add(jsonObjectR);
                listValueArray.add(jsonObjectA);
                listValueArray.add(jsonObjectM);
                listValueArray.add(jsonObjectS);

                vehicleTypeJson.put("list", listValueArray);

                result.add(vehicleTypeJson);
            }
        }
        return result;
    }

    public Object selectRams(String nameCn,String productNumber,int pageNumber,int pageSize) {
        //分页
        PageHelper.startPage(pageNumber, pageSize*3);
        //通过部件型号名称或者部件型号编码查询部件RAMS
        List<Object> list = ramsMapping.selectRams(nameCn, productNumber);
        PageInfo PageInfo = new PageInfo<>(list);
        PageInfo.setTotal(PageInfo.getTotal()/3);
        PageInfo.setPageSize(pageSize);
        return PageInfo;

    }
}
