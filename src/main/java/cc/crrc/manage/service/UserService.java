package cc.crrc.manage.service;


import cc.crrc.manage.common.utils.CodecUtils;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UUIDUtils;
import cc.crrc.manage.mapper.UserMapping;
import cc.crrc.manage.pojo.SysDictVO;
import cc.crrc.manage.pojo.User;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class UserService {

    @Autowired
    private UserMapping userMapper;

    public Object register(User user) {
  /*      String key = KEY_PREFIX + user.getPhone();
        //1.从redis中取出验证码
        String codeCache = this.stringRedisTemplate.opsForValue().get(key);
        //2.检查验证码是否正确
        if(!codeCache.equals(code)){
            //不正确，返回
            return false;
        }*/
//        user.setId(UUID.randomUUID().toString());
//        user.setId(UUIDUtils.generateUuid());//String 型改成自增序列
        //雪花算法添加主键
        user.setId(PrimaryKeyGenerator.generatorId());
        user.setCreateBy("0");
        user.setDelFlag("0");
//        user.setTenantId(UUIDUtils.generateUuid());
//        user.setOrganizationId(UUIDUtils.generateUuid());
        user.setCreateDate(new Date());
        user.setIsSuperAdmin("0");
        //3.密码加密
        String encodePassword = CodecUtils.passwordBcryptEncode(user.getPassword().trim());
        user.setPassword(encodePassword);
        //4.写入数据库
        boolean result = this.userMapper.insertUser(user) == 1;
        if (result) {
            return "success";
        } else {
            throw new RuntimeException("新增失败");
        }

        //5.如果注册成功，则删掉redis中的code
     /*   if (result){
            try{
                this.stringRedisTemplate.delete(KEY_PREFIX + user.getPhone());
            }catch (Exception e){
                logger.error("删除缓存验证码失败，code:{}",code,e);
            }
        }*/
    }


    public User queryUser(String username, String password) {
        // 查询
        User record = new User();
        record.setUsername(username);
        User user = this.userMapper.getUserInfoByName(record);
        // 校验用户名
        if (user == null) {
            return null;
        }
        // 校验密码
        boolean result = CodecUtils.passwordConfirm(password, user.getPassword());
        if (!result) {
            return null;
        }
        // 用户名密码都正确
        return user;
    }

    /**
     * 根据用户id查询用户信息
     *
     * @param userId
     * @return
     */
    public User selectUserInfoById(String userId) {
        User user = userMapper.selectUserInfoById(userId);
        return user;
    }


    public PageInfo<User>  getAll(User user) {
        //分页
        int currentPage = user.getPageNumber();
        int pageSize = user.getPageSize();
        PageHelper.startPage(currentPage, pageSize);
        List<User> result = userMapper.getAll(user);
        //查询所有用户的角色名称和角色编码以及对应的用户id zhangzhijian 2020-6-22
        List<User> roleNameCodeList = userMapper.findAllRoleNameCode();
        //遍历用户信息result 在遍历所有的角色信息，然后将用户所有角色名称和编码拼接，以“,”间隔
        for (int i = 0; result != null && i < result.size(); i++) {
            String id = result.get(i).getId();
            String roleName = "";
            String roleCode = "";
            for (int j = 0; roleNameCodeList != null && j < roleNameCodeList.size(); j++) {
                String userId = roleNameCodeList.get(j).getId();
                String rName = roleNameCodeList.get(j).getRoleName();
                String rCode = roleNameCodeList.get(j).getRoleCode();
                if(id != null && id.equals(userId)){
                    roleName = roleName + rName + ",";
                    roleCode = roleCode + rCode + ",";
                }
            }
            //去掉字符串最后一位的, 然后将角色编码和名称重新赋值给result zhangzhijian 2020-6-16
            if(roleName.length()>0){
                result.get(i).setRoleName(roleName.substring(0,roleName.length()-1));
            }
            if(roleCode.length()>0){
                result.get(i).setRoleCode(roleCode.substring(0,roleCode.length()-1));
            }
        }
        return new PageInfo<>(result);
    }

    public User getUserByUsername(String username) {
        User user = new User();
        user.setUsername(username);
        User result = userMapper.getUserInfoByName(user);
        return result;
    }
}