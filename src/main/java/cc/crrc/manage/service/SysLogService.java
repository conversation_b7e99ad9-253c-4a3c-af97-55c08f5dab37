package cc.crrc.manage.service;

import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.mapper.SysLogMapping;
import cc.crrc.manage.pojo.SysLogDTO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import eu.bitwalker.useragentutils.Browser;
import eu.bitwalker.useragentutils.UserAgent;
import net.minidev.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
@Service
public class SysLogService {
	@Autowired
	private SysLogMapping sysLogMapping;
	
	public Object insertLog(SysLogDTO sysLogDTO) {
		sysLogDTO.setId(PrimaryKeyGenerator.generatorId());
		int result = sysLogMapping.insertLog(sysLogDTO);
		return result;
	}

	public SysLogDTO getMenuByPath(String path) {
		SysLogDTO result = sysLogMapping.getMenuByPath(path);
		return result;
	}
	
	public PageInfo<SysLogDTO> getLogs(SysLogDTO log) {
		PageHelper.startPage(log.getPageNumber(), log.getPageSize());
		List<SysLogDTO> logs = sysLogMapping.getLogs(log);
		PageInfo<SysLogDTO> pageInfo = new PageInfo<SysLogDTO>(logs);
		return pageInfo;
	}

	public Object getOptCount(SysLogDTO log) {
		// TODO Auto-generated method stub
		Map<String,Integer> otpCount = sysLogMapping.getOptCount(log);
		return otpCount;
	}

	public Object getMenuCount(String startTime,String endTime) {
		// TODO Auto-generated method stub
		List<SysLogDTO> menuCount = sysLogMapping.getMenuCount(startTime,endTime);
		return menuCount;
	}
	
	public Object getAccessCount() {
		// TODO Auto-generated method stub
		String startTime = null;
		Map<String,Object> accessCount = sysLogMapping.getAccessCount(startTime);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd 00:00:00");  
		startTime = sdf.format(new Date());
		Map<String,Object> todayAccessCount =  sysLogMapping.getAccessCount(startTime);
		JSONObject count = new JSONObject();
		count.put("count", accessCount.get("count"));
		count.put("todayCount", todayAccessCount.get("count"));
		return count;
	}

	public Object getAgent(String startTime, String endTime) {
		// TODO Auto-generated method stub
		List<SysLogDTO> agents = sysLogMapping.getAgent(startTime, endTime);
		int chromeNum = 0,edgeNum = 0,firefoxNum = 0,ieNum = 0,safariNum = 0,operaNum = 0;
		for(SysLogDTO agent : agents) {
			UserAgent userAgent = UserAgent.parseUserAgentString(agent.getUserAgent());
			Browser browser = userAgent.getBrowser();
//			OperatingSystem os = userAgent.getOperatingSystem();
			if(browser.getName().toLowerCase().indexOf("Chrome".toLowerCase()) != -1) {
				chromeNum = chromeNum + agent.getCount();
			}
			if(browser.getName().toLowerCase().indexOf("Edge".toLowerCase()) != -1) {
				edgeNum = edgeNum + agent.getCount();
			}
			if(browser.getName().toLowerCase().indexOf("Firefox".toLowerCase()) != -1) {
				firefoxNum = firefoxNum + agent.getCount();
			}
			if(browser.getName().toLowerCase().indexOf("Internet".toLowerCase()) != -1) {
				ieNum = ieNum + agent.getCount();
			}
			if(browser.getName().toLowerCase().indexOf("Safari".toLowerCase()) != -1) {
				safariNum = safariNum + agent.getCount();
			}
			if(browser.getName().toLowerCase().indexOf("Opera".toLowerCase()) != -1) {
				operaNum = operaNum + agent.getCount();
			}
		}
		JSONObject agentJson = new JSONObject();
		agentJson.put("Chrome", chromeNum);
		agentJson.put("Edge", edgeNum);
		agentJson.put("Firefox", firefoxNum);
		agentJson.put("IE", ieNum);
		agentJson.put("Safari", safariNum);
		agentJson.put("Opera", operaNum);
		System.out.println(agentJson.toString());
		
		return agentJson;
	}

	public PageInfo<SysLogDTO> getVisitors(SysLogDTO log) {
		// TODO Auto-generated method stub
		PageHelper.startPage(log.getPageNumber(), log.getPageSize());
		List<SysLogDTO> logs = sysLogMapping.getVisitors(log);
		//↓↓↓↓↓解析参数，调用SysLogService.saveLog(log, request);
//		HttpServletRequest request =
//                ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
//		JSONObject json = new JSONObject();
//		SysLogDTO saveLogDTO = new SysLogDTO();
//		json.put("开始时间", "测试解析参数值"+log.getStartTime());
//		json.put("结束时间", "测试解析参数值"+log.getEndTime());
//		saveLogDTO.setOpt("查询访客列表");
//		saveLogDTO.setParams(json.toString());
//		this.saveLog(log, request);
		//↑↑↑↑↑解析参数结束
		List<SysLogDTO> logsOut = new ArrayList<SysLogDTO>();
		if(logs!=null) {
			for(SysLogDTO visitor : logs) {
				UserAgent userAgent = UserAgent.parseUserAgentString(visitor.getUserAgent());
				visitor.setBrowser(userAgent.getBrowser().getName());
				visitor.setOperatingSystem(userAgent.getOperatingSystem().getName());
				logsOut.add(visitor);
			}
		}
		PageInfo<SysLogDTO> pageInfo = new PageInfo<SysLogDTO>(logs);
		pageInfo.setList(logsOut);
		return pageInfo;
	}
}
