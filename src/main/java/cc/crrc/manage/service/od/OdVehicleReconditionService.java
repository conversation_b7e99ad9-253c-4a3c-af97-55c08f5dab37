package cc.crrc.manage.service.od;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.mapper.od.OdVehicleReconditionMapping;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import cc.crrc.manage.pojo.od.OdReconditionDecisionPO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * @ClassName OdVehicleReconditionDecisionService
 * @Description service层
 * <AUTHOR>
 * @Date 2020/8/10 16:48
 * @Version 1.0
 **/
@Service
public class OdVehicleReconditionService {

    private final static Logger LOGGER = LoggerFactory.getLogger(OdVehicleReconditionService.class);
    //延后操作当前时间增加15分钟
    private final static long SUM_TIME = 15 * 60 * 1000L;

    @Autowired
    private OdVehicleReconditionMapping mapping;

    /**
     * @Description 根据条件查询车辆检修决策列表
     * @Param 查询条件
     * @Return 分页数据
     * <AUTHOR> mingkuan
     * @Date 2020/8/11 14:48
     */
    public PageInfo<OdReconditionDecisionPO> findReconditionDecisions(OdReconditionDecisionPO reconditionDecisionPO) {
        try {
            // 分页
            int currentPage = reconditionDecisionPO.getPageNumber();
            int pageSize = reconditionDecisionPO.getPageSize();
            PageHelper.startPage(currentPage, pageSize);
            // 获取数据
            List<OdReconditionDecisionPO> decisionList = mapping.findReconditionDecisions(reconditionDecisionPO);
            return new PageInfo<OdReconditionDecisionPO>(decisionList);
        } catch (DataAccessException e) {
            LOGGER.error("Method[findVehicleRecondition] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @Description  对决策进行操作,更新提交状态
     * @Param decisionId:id  status:状态值
     * @Return JSONObject
     * <AUTHOR> mingkuan
     * @Date 2020/8/11 17:07
     */
    public JSONObject actOnDecisions(OdReconditionDecisionPO reconditionDecisionPO) {
        JSONObject result = new JSONObject();
        try {
            String status = reconditionDecisionPO.getStatus();
            Date triggerTime = reconditionDecisionPO.getTriggerTime();
            if("1".equals(status)) {
                //操作时间向后延迟+SUM_TIME
                triggerTime.setTime((new Date()).getTime() + SUM_TIME);
            }
            reconditionDecisionPO.setTriggerTime(triggerTime);
            int row = mapping.actOnDecisions(reconditionDecisionPO);
            result.put("result","更新了"+row+"条数据");
            return result;
        } catch (DataAccessException e) {
            LOGGER.error("Method[actOnDecisions] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }
    
    /**
     * @Description 根据车辆编码查询决策列表数据 
     * @Param 车辆编码
     * @Return  车辆检修决策
     * <AUTHOR> zhijian
     * @Date 2020/8/12 11:04
     */
    public JSONArray findDecisionByVehicleCode(List<MtrVehiclePO> vehicleCodes) {
        JSONArray result = new JSONArray();
        List<OdReconditionDecisionPO> decisionList = mapping.findDecisionByVehicleCode(vehicleCodes,new Date());
        if(!CollectionUtils.isEmpty(decisionList)){
            for (OdReconditionDecisionPO decisionPO : decisionList) {
                JSONObject obj = (JSONObject)JSONObject.toJSON(decisionPO);
                result.add(obj);
            }
        }
        return result;
    }
}
