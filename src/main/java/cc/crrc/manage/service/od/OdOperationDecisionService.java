package cc.crrc.manage.service.od;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.od.OdOperationDecisionMapping;
import cc.crrc.manage.pojo.od.OdOperationDecisionPO;
import cc.crrc.manage.pojo.od.OdReconditionDecisionPO;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * @FileName OdOperationDecisionService
 * @Description 运维决策service
 * <AUTHOR> yuxi
 * @Date 2020/5/7 15:00
 **/
@Service
public class OdOperationDecisionService {
    private final static Logger LOGGER = LoggerFactory.getLogger(OdOperationDecisionService.class);
    @Autowired
    private OdOperationDecisionMapping operationDecisionMapping;
    @Autowired
    private OdVehicleReconditionService vehicleReconditionService;

    /**
     * @Description 分页查询决策列表
     * @Return java.util.List<cc.crrc.manage.pojo.od.OdOperationDecisionPO>
     * <AUTHOR> yuxi
     * @Date 17:01 2020/5/7
     * @Update  zhangzhijian 2020/8/14修改
     * @Param [operationDecision]
     **/
    public PageInfo<OdOperationDecisionPO> listOperationDecision(OdOperationDecisionPO operationDecision) {
        try {
            // 分页
            int currentPage = operationDecision.getPageNumber();
            int pageSize = operationDecision.getPageSize();
            PageHelper.startPage(currentPage, pageSize);
            List<OdOperationDecisionPO> operationDecisionList = operationDecisionMapping.listOperationDecision(operationDecision);
            return new PageInfo<>(operationDecisionList);
        } catch (DataAccessException e) {
            LOGGER.error("Method[listOperationDecision] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }
    
    /**
     * @Description 修改决策状态同时维护详细决策表 状态标识 0:待处理  1:延后  2:完成 3:忽略
     * @Param operationDecision:决策数据
     * @Return  JSONObject
     * <AUTHOR> zhijian
     * @Date 2020/8/14 10:51
     */
    @Transactional(rollbackFor = Exception.class)
    public JSONObject UpdateReconditionStatus(OdOperationDecisionPO operationDecision) {
        JSONObject result = new JSONObject();
        try{
            String decisionType = operationDecision.getDecisionType();
            String status = operationDecision.getStatus();
            Date pushTime = operationDecision.getPushTime();
            if("车辆检修决策".equals(decisionType)){
                OdReconditionDecisionPO rDPO = new OdReconditionDecisionPO();
                rDPO.setId(operationDecision.getDecisionId());
                rDPO.setStatus(status);
                rDPO.setTriggerTime(pushTime);
                JSONObject row = vehicleReconditionService.actOnDecisions(rDPO);
            }
            operationDecision.setPushTime(pushTime);
            operationDecision.setOperateTime(new Date());
            operationDecision.setOperateBy(UserUtils.getUserId());
            int row = operationDecisionMapping.UpdateReconditionStatus(operationDecision);
            result.put("result","更新了"+row+"条数据");
        }catch (DataAccessException e){
            LOGGER.error("Method[UpdateReconditionStatus] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
        return result;
    }

}
