package cc.crrc.manage.service.od;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.StringUtils;
import cc.crrc.manage.common.utils.UUIDUtils;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.od.SchedulingPlanDecisionMapping;
import cc.crrc.manage.pojo.od.SchedulingPlanDecisionPO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * @FileName SchedulingPlanDecisionService
 * @Description 调度计划决策逻辑层
 * <AUTHOR> zhijian
 * @Date 2020/7/29 17:19
 **/
@Service
public class SchedulingPlanDecisionService {

    private final static Logger LOGGER = LoggerFactory.getLogger(SchedulingPlanDecisionService.class);
    @Autowired
    private SchedulingPlanDecisionMapping schedulingPlanDecisionMapping;

    /**
     * @Description 查询调度计划决策列表数据
     * @Param pageNumber:分页页码 pageSize：分页大小
     * @Return 调度计划决策数据
     * <AUTHOR> zhijian
     * @Date 2020/7/30 13:17
     */
    public PageInfo findSchedulingPlanPOList(Integer pageNumber,Integer pageSize,String pushStatus) {
        try{
            PageHelper.startPage(pageNumber, pageSize);
            List<SchedulingPlanDecisionPO> schedulingPlanPOList = schedulingPlanDecisionMapping.findSchedulingPlanPOList(pushStatus);
            return new PageInfo<>(schedulingPlanPOList);
        }catch (DataAccessException e){
            LOGGER.error("Method[findSchedulingPlanPOList] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @Description 保存调度计划决策
     * @Param schedulingPlanDecisionPO：调度计划决策对象
     * @Return  JSONObject
     * <AUTHOR> zhijian
     * @Date 2020/7/30 13:18
     */
    public JSONObject saveSchedulingPlanPO(SchedulingPlanDecisionPO po) {
        Float rideRate = po.getRideRate() / 100.0f;
        if (rideRate < 0.0f || rideRate > 2.0f){
            throw new RestApiException(ExceptionInfoEnum.DATA_ADD_LENGTH_BEYOND_LIMIT,"乘车率范围在0~200%之间");
        }
        JSONObject result = new JSONObject();
        JSONObject resultObj = formulaComputing(po);
        int intervalTime = (int)resultObj.get("intervalTime");
        int commissioningCount = (int)resultObj.get("commissioningCount");
        //获取和验证时间段参数(前端格式5:00-7:00)
        String startTime;
        String endTime;
        try {
            startTime = po.getStartTime();
            endTime = po.getEndTime();
            float startNumber = Float.parseFloat(startTime.split(":")[0]) * 100 + Float.parseFloat(startTime.split(":")[1]);
            float endNumber = Float.parseFloat(endTime.split(":")[0]) * 100 + Float.parseFloat(endTime.split(":")[1]);
            if(endNumber <= startNumber){
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION,"结束时间需要大于起始时间,请选择重新选择");
            }
        }catch (DataAccessException e){
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION,"时间段起始时间不能为空");
        }
        try{
            //生成调度计划编码，用于区分同时间段数据
            int countByTime = schedulingPlanDecisionMapping.findCountByStartAndEnd(startTime,endTime);
            String schedulingCode = getSchedulingCode(countByTime,startTime,endTime);
            //实体类赋值
            po.setId(UUIDUtils.generateUuid());
            po.setSchedulingCode(schedulingCode);
            po.setCommissioningCount(commissioningCount);
            po.setIntervalTime(intervalTime);
            po.setCraeteBy(UserUtils.getUserId());
            po.setModifyBy(UserUtils.getUserId());
            po.setCreateTime(new Date());
            po.setModifyTime(po.getCreateTime());
            po.setValid(1);
            po.setPushStatus("0");
            int row = schedulingPlanDecisionMapping.saveSchedulingPlanPO(po);
            result.put("result","保存了"+row+"条数据");
        }catch (DataAccessException e){
            LOGGER.error("Method[saveSchedulingPlanPO] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
        return result;
    }

    /**
     * @Description 生成 getSchedulingCode 调度计划决策编码
     * @Param countByTime 该时间段内决策数量 startTime、endTime:时间段的起止时间
     * @Return String
     * <AUTHOR> zhijian
     * @Date 2020/7/29 10:25
     */
    private synchronized String getSchedulingCode(int countByTime,String startTime,String endTime){
        String schedulingCode = "DD"+StringUtils.stringJoint(startTime,":")+
                StringUtils.stringJoint(endTime,":")+
                String.format("%03d",++countByTime);
        //校验schedulingCode是否是唯一的
        int countByCode = schedulingPlanDecisionMapping.findCountBySchedulingCode(schedulingCode);
        if(countByCode > 0){
            return getSchedulingCode(countByTime,startTime,endTime);
        }
        return schedulingCode;
    }

    /**
     * @Description 通过参数计算发车间隔时间和投运车辆数量
     * @Param schedulingPlanDecisionPO：调度计划决策实体类获取配置参数
     * @Return JSONObject
     * <AUTHOR> zhijian
     * @Date 2020/7/30 15:45
     */
    public JSONObject formulaComputing(SchedulingPlanDecisionPO schedulingPlanDecisionPO){
        Float rideRate = schedulingPlanDecisionPO.getRideRate() / 100.0f;
        if (rideRate < 0.0f || rideRate > 2.0f){
            throw new RestApiException(ExceptionInfoEnum.DATA_ADD_LENGTH_BEYOND_LIMIT,"乘车率范围在0~200%之间");
        }
        JSONObject result = new JSONObject();
        try{
            /* 计算公式:((2L×60)/V+T1+T2)/T间 = Nt   Nt∙E≥Z
             * 定义计算公式的参数:
             * lineMileage:线路里程(L km)  vehicleSpeed:平均车速(V km/h) startSwitchingTime:始发站调头时间(T1 min)
             * endSwitchingTime:终点站调头时间(T2 min)  intervalTime:发车间隔(T间 min) commissioningCount:投运车辆(Nt 辆)
             * rideRate:乘车率(E 最大100%)  historyRideCount:设定时间段平均车辆数Z
             */
            float lineMileage = schedulingPlanDecisionPO.getLineMileage();
            long vehicleSpeed = schedulingPlanDecisionPO.getVehicleSpeed();
            long startSwitchingTime = schedulingPlanDecisionPO.getStartSwitchingTime();
            long endSwitchingTime = schedulingPlanDecisionPO.getEndSwitchingTime();
            float historyRideCount = Float.valueOf(schedulingPlanDecisionPO.getHistoryRideCount());
            /*
             * Nt:求天花板  T间:求地板
             */
            //验证时间段内所需车辆数
            if(historyRideCount == 0){
                throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION,"时间段平均车辆数参数异常，请重新配置");
            }
            int commissioningCount = (int)Math.ceil(historyRideCount/rideRate);
            int intervalTime = (int)Math.floor(((2.0*lineMileage*60/vehicleSpeed)+startSwitchingTime+endSwitchingTime)*rideRate/historyRideCount);
            result.put("commissioningCount",commissioningCount);
            result.put("intervalTime",intervalTime);
            return result;
        }catch (DataAccessException e){
            LOGGER.error("Method[formulaComputing] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION);
        }
    }

    /**
     * @Description 通过系统推荐平均车速(vehicleSpeed) 乘车率(rideRate) 设定时间段平均车辆数Z(historyRideCount)
     * @Return JSONObject
     * <AUTHOR> zhijian
     * @Date 2020/7/31 9:51
     */
    public JSONObject systemRecommended(String calculateParam) {
        JSONObject result = new JSONObject();
        int vehicleSpeed = 0;
        float rideRate = 0.0f;
        String historyRideCount = "";
        try{
            //按需求传递值
            if("V".equals(calculateParam)){
                vehicleSpeed = 65;
                result.put("result",vehicleSpeed);
            }else if("E".equals(calculateParam)){
                rideRate = 90f;
                result.put("result",rideRate);
            }else if("Z".equals(calculateParam)){
                historyRideCount = "6";
                result.put("result",historyRideCount);
            }
        }catch (DataAccessException e){
            LOGGER.error("Method[systemRecommended] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_OPERATION_EXCEPTION);
        }
        return result;
    }

    /**
     * @Description 删除多个行车调度计划决策数据
     * @Param 多个id串
     * @Return JSONObject
     * <AUTHOR> zhijian
     * @Date 2020/8/6 11:34
     */
    public JSONObject deleteSchedulingPlanPO(String ids) {
        JSONObject result = new JSONObject();
        try{
            if(StringUtils.isNotEmpty(ids)){
                String[] idArray = ids.split(",");
                int rows = schedulingPlanDecisionMapping.deleteSchedulingPlanPO(idArray);
                result.put("result","删除了"+rows+"条数据");
                return result;
            }
            return result;
        }catch (DataAccessException e){
            LOGGER.error("Method[deleteSchedulingPlanPO] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_OPERATION_EXCEPTION);
        }
    }

    /**
     * @Description 编辑行车调度计划决策和推送状态修改
     * @Param 决策实体类
     * @Return JSONObject
     * <AUTHOR> zhijian
     * @Date 2020/8/6 13:21
     */
    public JSONObject updeteSchedulingPlanPO(SchedulingPlanDecisionPO schedulingPlanDecisionPO) {
        Float rideRate = schedulingPlanDecisionPO.getRideRate() / 100.0f;
        if (rideRate < 0.0f || rideRate > 2.0f){
            throw new RestApiException(ExceptionInfoEnum.DATA_ADD_LENGTH_BEYOND_LIMIT,"乘车率范围在0~200%之间");
        }
        JSONObject result = new JSONObject();
        JSONObject resultObj = formulaComputing(schedulingPlanDecisionPO);
        int intervalTime = (int)resultObj.get("intervalTime");
        int commissioningCount = (int)resultObj.get("commissioningCount");
        //验证时间段参数
        String startTime;
        String endTime;
        try {
            startTime = schedulingPlanDecisionPO.getStartTime();
            endTime = schedulingPlanDecisionPO.getEndTime();
            float startNumber = Float.valueOf(startTime.split(":")[0])+Float.valueOf(startTime.split(":")[1]);
            float endNumber = Float.valueOf(endTime.split(":")[0])+Float.valueOf(endTime.split(":")[1]);
            if(endNumber <= startNumber){
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION,"结束时间需要大于起始时间,请选择重新选择");
            }
        }catch (DataAccessException e){
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION,"时间段起始时间不能为空");
        }
        try{
            //查询原数据 如果startTime和endTime都没有修改则不重新生成决策编码
            String id = schedulingPlanDecisionPO.getId();
            SchedulingPlanDecisionPO oldSchedulingPlan = schedulingPlanDecisionMapping.findSchedulingPlanById(id);
            String oldStartTime = oldSchedulingPlan.getStartTime();
            String oldEndTime = oldSchedulingPlan.getEndTime();
            if(!startTime.equals(oldStartTime) || !endTime.equals(oldEndTime)){
                //重新生成调度计划编码，用于区分同时间段数据
                int countByTime = schedulingPlanDecisionMapping.findCountByStartAndEnd(startTime,endTime);
                String schedulingCode = getSchedulingCode(countByTime,startTime,endTime);
                schedulingPlanDecisionPO.setSchedulingCode(schedulingCode);
            }
            //实体类赋值
            schedulingPlanDecisionPO.setCommissioningCount(commissioningCount);
            schedulingPlanDecisionPO.setIntervalTime(intervalTime);
            schedulingPlanDecisionPO.setModifyBy(UserUtils.getUserId());
            schedulingPlanDecisionPO.setModifyTime(new Date());
            int row = schedulingPlanDecisionMapping.updeteSchedulingPlanPO(schedulingPlanDecisionPO);
            result.put("result","更新了"+row+"条数据");
        }catch (DataAccessException e){
            LOGGER.error("Method[updeteSchedulingPlanPO] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_OPERATION_EXCEPTION);
        }
        return result;
    }
    
    /**
     * @Description websocket推送推送状态为1的行车调度计划决策数据
     * @Param  lineId 线路id
     * @Return JSONArray
     * <AUTHOR> zhijian
     * @Date 2020/8/12 14:05
     */
    public JSONArray findDecisionByLineId(Long lineId) {
        JSONArray result = new JSONArray();
        List<SchedulingPlanDecisionPO> list = schedulingPlanDecisionMapping.findDecisionByLineId(lineId);
        if(!CollectionUtils.isEmpty(list)){
            for (SchedulingPlanDecisionPO decisionPO : list) {
                JSONObject obj = (JSONObject)JSONObject.toJSON(decisionPO);
                result.add(obj);
            }
        }
        return result;
    }
}
