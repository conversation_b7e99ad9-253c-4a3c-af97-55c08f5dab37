package cc.crrc.manage.service;


import cc.crrc.manage.cache.RefreshCache;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.StringUtils;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.mapper.SysDictMapping;
import cc.crrc.manage.mapper.SysDictTypeMapping;
import cc.crrc.manage.mapper.mtr.MtrVehicleMapping;
import cc.crrc.manage.pojo.SysDictDTO;
import cc.crrc.manage.pojo.SysDictVO;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @FileName SysDictService
 * <AUTHOR> yuxi
 * @Date 2019/6/14 13:34
 * @Version 1.0
 **/
@Service
public class SysDictService {
    private final Logger logger = LoggerFactory.getLogger(SysDictService.class);
    private final String SUB_SYSTEM_DICT_TYPE_NAME = "ass_car_system";
    @Autowired
    private SysDictMapping dictMapper;
    @Autowired
    private SysDictTypeMapping sysDictTypeMapping;


    /**
     * @return java.util.List<cc.crrc.manage.pojo.SysDictVO>
     * @Description 分页取得数据字典列表
     * <AUTHOR> yuxi
     * @Date 15:20 2019/6/14
     * @Param [dictVO]
     **/
    public PageInfo<SysDictVO> listDict(SysDictVO dictVO) {
        try {
            //分页
            int currentPage = dictVO.getPageNumber();
            int pageSize = dictVO.getPageSize();
            PageHelper.startPage(currentPage, pageSize);
            //获取数据
            List<SysDictVO> resultList = dictMapper.listDict(dictVO);
            return new PageInfo<>(resultList);
        } catch (DataAccessException e) {
            logger.error("Method[listDict] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @return java.util.List<cc.crrc.manage.pojo.SysDictVO>
     * @Description 据类型id筛选数据字典
     * <AUTHOR> yuxi
     * @Date 13:36 2019/10/17
     * @Param [typeId]
     **/
    public List<SysDictVO> listByTypeId(String typeId) {
        try {
            return dictMapper.listByTypeId(typeId);
        } catch (DataAccessException e) {
            logger.error("Method[listByTypeId] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * @return java.util.List<cc.crrc.manage.pojo.SysDictVO>
     * @Description 据类型筛名称选数据字典
     * <AUTHOR> yuxi
     * @Date 13:36 2019/10/17
     * @Param [type]
     **/
    public List<SysDictVO> listByType(String typeCode) {
        try {
            return dictMapper.listByType(typeCode);
        } catch (DataAccessException e) {
            logger.error("Method[listByType] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    /**
     * 查询子系统数据字典
     *
     * @return java.util.List<cc.crrc.manage.pojo.SysDictVO>
     * <AUTHOR> GuoYang
     * 2020/3/26
     **/
    public List<SysDictVO> listSubSystemDict() {
        return listByType(SUB_SYSTEM_DICT_TYPE_NAME);
    }

    /**
     * @return int
     * @Description 新增数据
     * <AUTHOR> yuxi
     * @Date 15:21 2019/6/14
     * @Param [dictVO]
     **/
    public int addDict(SysDictDTO dictDTO) {
        try {
            int count = dictMapper.getCountByValue_typeId(null, dictDTO.getValue(), dictDTO.getTypeId());
            if (count > 0) {
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "数据字典已存在！");
            }
            String userId = UserUtils.getUserId();
            Date date = new Date();
            Timestamp t = new Timestamp(date.getTime());
            dictDTO.setDelFlag("0");
            dictDTO.setCreateTime(t);
            dictDTO.setCreateBy(userId);
            dictDTO.setModifyTime(t);
            dictDTO.setModifyBy(userId);
            //雪花算法添加主键
            dictDTO.setId(PrimaryKeyGenerator.generatorId());
            return dictMapper.addDict(dictDTO);
        } catch (DataAccessException e) {
            logger.error("Method[addDict] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    /**
     * @return int
     * @Description 更新数据
     * <AUTHOR> yuxi
     * @Date 15:21 2019/6/14
     * @Param [dictVO]
     **/

    public int updateDict(SysDictDTO dictDTO) {
        try {
            int count = dictMapper.getCountByValue_typeId(dictDTO.getId(), dictDTO.getValue(), dictDTO.getTypeId());
            if (count > 0) {
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "数据字典已存在！");
            }
            Date date = new Date();
            Timestamp t = new Timestamp(date.getTime());
            dictDTO.setDelFlag("0");
            dictDTO.setModifyTime(t);
            dictDTO.setModifyBy(UserUtils.getUserId());
            return dictMapper.updateDict(dictDTO);
        } catch (DataAccessException e) {
            logger.error("Method[updateDict] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    /**
     * @return void
     * @Description 删除数据
     * <AUTHOR> yuxi
     * @Date 15:44 2019/6/14
     * @Param [dictVO]
     **/
    public int deleteDict(String id) {
        try {
            String userId = UserUtils.getUserId();
            Date date = new Date();
            Timestamp t = new Timestamp(date.getTime());
            SysDictDTO dictDTO = new SysDictDTO();
            dictDTO.setId(id);
            dictDTO.setDelFlag("1");
            dictDTO.setModifyTime(t);
            dictDTO.setModifyBy(userId);
            return dictMapper.updateDict(dictDTO);
        } catch (DataAccessException e) {
            logger.error("Method[deleteDict] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    public Object listDictUniversal(SysDictDTO sysDictDTO) {
        //第一步 现根据TypeCode查询筛选范围
        String level = sysDictTypeMapping.getTypeLevel(sysDictDTO.getTypeCode());
        if(StringUtils.isEmpty(level)){
            throw new RestApiException(ExceptionInfoEnum.DATA_NOT_FOUND_EXCEPTION);
        }
        //第二步 根据确定的 范围进行条件查询 先声明一个参数实体类
        if(StringUtils.isEmpty(sysDictDTO.getTypeCode())){
            throw new RestApiException(ExceptionInfoEnum.DATA_NOT_FOUND_EXCEPTION.getErrorCode(), "缺少查询条件！");
        }
        SysDictDTO param = new SysDictDTO();
        if(level.equals("1")){
            param.setTypeCode(sysDictDTO.getTypeCode());
        }else if (level.equals("2")) {
            param.setTypeCode(sysDictDTO.getTypeCode());
            String lineId = sysDictDTO.getLineId();
            if(StringUtils.isEmpty(lineId)){
                throw new RestApiException(ExceptionInfoEnum.DATA_NOT_FOUND_EXCEPTION.getErrorCode(),"缺少线路信息！");
            }
            param.setLineId(lineId);
        }else if(level.equals("3")){
            param.setTypeCode(sysDictDTO.getTypeCode());
//            String lineId = sysDictDTO.getLineId();
//            if(StringUtils.isEmpty(lineId)){
//                throw new RestApiException(ExceptionInfoEnum.DATA_NOT_FOUND_EXCEPTION.getErrorCode(),"缺少线路信息！");
//            }
//            param.setLineId(lineId);
            String vehicleTypeId = sysDictDTO.getVehicleTypeId();
            if(StringUtils.isEmpty(vehicleTypeId)){
                throw new RestApiException(ExceptionInfoEnum.DATA_NOT_FOUND_EXCEPTION.getErrorCode(),"缺少车型信息！");
            }
            param.setVehicleTypeId(vehicleTypeId);
        }else{
            throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION);
        }

        List<SysDictVO> sysDictVOS =  dictMapper.listDictUniversal(param);

        return sysDictVOS;
    }
}
