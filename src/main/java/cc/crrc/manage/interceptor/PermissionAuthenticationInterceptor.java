package cc.crrc.manage.interceptor;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.JsonParseException;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.Constants;
import cc.crrc.manage.common.utils.JsonPathUtils;
import cc.crrc.manage.common.utils.RedisUtils;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.security.PathGrantedAuthority;
import cc.crrc.manage.security.UserDetail;
import cc.crrc.manage.service.EntitlementService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @FileName PermissionAuthenticationInterceptor
 * <AUTHOR> shuangquan
 * @Date 2019/9/26 16:41
 **/
@Component
@Profile("prod")
public class PermissionAuthenticationInterceptor implements AuthenticationInterceptor {
    private static final Logger logger = LoggerFactory.getLogger(PermissionAuthenticationInterceptor.class);
    @Autowired
    private EntitlementService entitlementService;

    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {

        /*if ("1".equals(user.getIsSuperAdmin())) {
            return true;
        }*/
        String requestUri = request.getRequestURI();
        String method = request.getMethod().toUpperCase();
        String contextPath = request.getContextPath();
        if (!StringUtils.isEmpty(contextPath)) {
            requestUri = requestUri.replaceFirst(contextPath, "");
        }
        if (!checkPermission(requestUri, method)) {
            throw new RestApiException(ExceptionInfoEnum.REQUEST_NOT_ACCESS);
        }
        return true;
    }

    private boolean checkPermission(String url, String method) {
        if (StringUtils.isEmpty(url) || StringUtils.isEmpty(method)) {
            return false;
        }
        UserDetail user = UserUtils.getUser();
        String json =
                (String) RedisUtils.get(Constants.REDIS_USER_AUTHRITY_KEY_PREFIX.concat(String.valueOf(UserUtils.getUserId())));
        if (StringUtils.isEmpty(json)) {
            return false;
        }
        List<PathGrantedAuthority> authorityList = null;
        try {
            authorityList = JsonPathUtils.parseList(json, "$", PathGrantedAuthority.class);
        } catch (IOException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION);
        } catch (JsonParseException e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_PARSE_EXCEPTION);
        }
        return authorityList.parallelStream().anyMatch(authority -> url.equals(authority.getPath()) && method.equals(authority.getMethod()));
      /*  try {
            UserInfo userInfo = entitlementService.getUserInfo();
            List<RoleInfo> elements = userInfo.getElements();
            List<RoleInfo> menus = userInfo.getMenus();
            List<RoleInfo> permissions = new ArrayList<>(elements);
            permissions.addAll(menus);
            return permissions.parallelStream().anyMatch(role -> url.equals(role.getUri()) && method.equals(role
            .getMethod()));
        } catch (Exception e) {
            logger.error("Get user info error {}", e.getMessage());
            return false;
        }*/
    }
}
