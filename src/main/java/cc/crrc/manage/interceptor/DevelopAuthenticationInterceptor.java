package cc.crrc.manage.interceptor;

import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @FileName DevelopAuthenticationInterceptor
 * <AUTHOR>
 * @Date 2019/9/26 16:43
 **/
@Component
@Profile("dev")
public class DevelopAuthenticationInterceptor implements AuthenticationInterceptor {

    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        return true;
    }
}
