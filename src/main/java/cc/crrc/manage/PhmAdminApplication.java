package cc.crrc.manage;

import cc.crrc.manage.cache.EnableCacheRefresh;
import com.github.pagehelper.autoconfigure.PageHelperAutoConfiguration;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(scanBasePackages = {"cc.crrc"}, exclude = {PageHelperAutoConfiguration.class, RabbitAutoConfiguration.class})
@MapperScan({"cc.crrc.manage.mapper","cc.crrc.manage.monitoringConfig.dao","cc.crrc.manage.websocket.dao"})
@EnableConfigurationProperties
@EnableTransactionManagement
@EnableCacheRefresh
@EnableScheduling
public class PhmAdminApplication {
    public static void main(String[] args) {
        SpringApplication.run(PhmAdminApplication.class, args);
    }
}
