package cc.crrc.manage.cache;

import cc.crrc.manage.common.utils.Constants;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

public class CacheSubscribeConfiguration {
    @Bean
    public Executor subscribeExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setThreadNamePrefix("cache subscribe-");
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(8);
        executor.setQueueCapacity(100);
        executor.initialize();
        return executor;
    }

    @Bean
    public CacheSubscribe cacheSubscribe(ApplicationContext context){
        CacheSubscribe cacheSubscribe = new CacheSubscribe(context);
        cacheSubscribe.afterProperties();
        return cacheSubscribe;
    }

    @Bean
    public MessageListenerAdapter listenerAdapter(CacheSubscribe cacheSubscribe) {
        MessageListenerAdapter adapter = new MessageListenerAdapter(cacheSubscribe, "onMessage");
        return adapter;
    }

    @Bean
    public RedisMessageListenerContainer container(RedisConnectionFactory connectionFactory,Executor subscribeExecutor,
                                                   MessageListenerAdapter listenerAdapter) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.setTaskExecutor(subscribeExecutor);
        container.addMessageListener(listenerAdapter, new PatternTopic(Constants.CACHE_REFRESH_CHANNEL));
        return container;
    }

    @Bean
    public CacheAspect cacheAspect(){
        return new CacheAspect();
    }

}
