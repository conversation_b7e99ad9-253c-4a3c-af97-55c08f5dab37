package cc.crrc.manage.cache;

import cc.crrc.manage.common.utils.JsonUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.util.Assert;

import java.util.List;


public class CacheSubscribe {

    private ApplicationContext context;

    public CacheSubscribe(ApplicationContext context) {
        this.context = context;
    }

    public void onMessage(String message) {
        List<String> caches = JsonUtils.parseList(message, String.class);
        for (String cache : caches) {
            CacheEvent ce = new CacheEvent(this,cache);
            context.publishEvent(ce);
        }
    }

    public void afterProperties() {
        Assert.notNull(context, "ApplicationContext is null");
    }
}
