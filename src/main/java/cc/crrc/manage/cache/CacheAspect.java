package cc.crrc.manage.cache;

import cc.crrc.manage.service.CacheService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.Arrays;

@Aspect
public class CacheAspect {

    @Autowired
    private CacheService service;

    @Around("@annotation(refreshCache)")
    public Object around(ProceedingJoinPoint joinPoint, RefreshCache refreshCache) throws Throwable {
        try {
            Object[] args = joinPoint.getArgs();
            Object result = joinPoint.proceed(args);
            if (TransactionSynchronizationManager.isSynchronizationActive()) {
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        TransactionSynchronization.super.afterCommit();
                        service.refreshCache(Arrays.asList(refreshCache.values()));
                    }
                });
            } else {
                service.refreshCache(Arrays.asList(refreshCache.values()));
            }
            return result;
        } catch (Throwable t) {
            throw t;
        }
    }
}
