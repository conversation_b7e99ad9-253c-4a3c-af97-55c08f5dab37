package cc.crrc.manage.cache.dict;

import cc.crrc.manage.cache.AbstractCache;
import cc.crrc.manage.common.utils.StringUtils;
import cc.crrc.manage.pojo.draw.DrawLineConfigVO;
import cc.crrc.manage.service.draw.DrawLineConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class DrawInfoCache extends AbstractCache<String, List<DrawLineConfigVO>> {

    @Autowired
    private DrawLineConfigService service;


    public DrawInfoCache() {
        super("DRAW_INFO");
    }


    @Override
    public void load() {
        List<DrawLineConfigVO> list = service.getDrawInfoList(null, null);
        Map<String, List<DrawLineConfigVO>> map = list.stream().collect(Collectors.groupingBy(item ->
                StringUtils.isEmpty(item.getLineId()) ? "" : String.join("_", item.getLineId(), item.getType())));
        putAll(map);
    }
}
