package cc.crrc.manage.cache.dict;

import cc.crrc.manage.cache.AbstractCache;
import cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import cc.crrc.manage.service.ekb.EkbFaultTypeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.stream.Collectors;

@Component
public class EkbFaultTypeCache extends AbstractCache<String, List<EkbFaultTypeDTO>> {
    private static final Logger logger = LoggerFactory.getLogger(EkbFaultTypeCache.class);

    private final Map<String, EkbFaultTypeDTO> kvMap = new HashMap<>();

    private final ReadWriteLock lock = new ReentrantReadWriteLock(true);
    private final Lock rLcok = lock.readLock();
    private final Lock wLock = lock.writeLock();


    @Autowired
    private EkbFaultTypeService service;

    protected EkbFaultTypeCache() {
        super("EKB_FAULT_TYPE");
    }

    @Override
    public void load() {
        List<EkbFaultTypeDTO> list = service.getEkbFaultTypeAll();
        Map<String, List<EkbFaultTypeDTO>> map = list.stream().collect(Collectors.groupingBy(
                item -> String.join("_", item.getLineId(), String.valueOf(item.getFaultCategory()))));
        //重新设置kvMap
        refreshMap(list);
        putAll(map);
    }

    private void refreshMap(List<EkbFaultTypeDTO> list) {
        wLock.lock();
        try {
            kvMap.clear();
            list.stream().forEach(item -> kvMap.put(item.getFaultTypeKey(), item));
        } catch (Exception e) {
            logger.error("refreshMap in EkbFaultTypeCache error = {} e = {}", e.getMessage(), e);
        } finally {
            wLock.unlock();
        }

    }

    public EkbFaultTypeDTO getValue(String key) {
        rLcok.lock();
        try {
            return kvMap.get(key);
        } catch (Exception e) {
            logger.error("getValue in EkbFaultTypeCache error = {} e = {}", e.getMessage(), e);
        } finally {
            rLcok.unlock();
        }
        return null;
    }
}
