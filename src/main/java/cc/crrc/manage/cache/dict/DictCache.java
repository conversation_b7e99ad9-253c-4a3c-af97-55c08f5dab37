package cc.crrc.manage.cache.dict;

import cc.crrc.manage.cache.AbstractCache;
import cc.crrc.manage.mapper.SysDictMapping;
import cc.crrc.manage.pojo.SysDictVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class DictCache extends AbstractCache<String,List<SysDictVO>> {

    public static final String TRAIN_ORGANIZE = "car_organize";
    public static final String TRAIN_MONITOR_CONFIG_MAPPING = "train_monitor_config_mapping";
    public static final String ASS_CAR_SYSTEM = "ass_car_system";
    public static final String FAULT_LEVEL = "fault_level";
    public static final String SOFTWARE_MANUAL_FLAG = "software_manual_flag";

    @Autowired
    private SysDictMapping mapping;

    public DictCache(){
        super("DICT");
    }

    public void load() {
        List<SysDictVO> list = mapping.listByType(null);
        Map map = list.stream().collect(Collectors.groupingBy(item -> item.getTypeCode() +
                (StringUtils.isEmpty(item.getLineId()) ? "" : StringUtils.join("_", item.getLineId()))
                + (StringUtils.isEmpty(item.getVehicleTypeId()) ? "" : StringUtils.join("_", item.getVehicleTypeId()))));
        putAll(map);
    }
}
