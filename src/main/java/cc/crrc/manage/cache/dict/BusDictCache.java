package cc.crrc.manage.cache.dict;

import cc.crrc.manage.cache.AbstractCache;
import cc.crrc.manage.mapper.BusDictMapping;
import cc.crrc.manage.pojo.BusDictVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class BusDictCache extends AbstractCache<String, List<BusDictVO>> {

    @Autowired
    private BusDictMapping mapping;

    public BusDictCache() {
        super("BUS_DICT");
    }

    public void load() {
        List<BusDictVO> list = mapping.listByType(null);
        Map map = list.stream().collect(Collectors.groupingBy(item -> item.getTypeCode() +
                (StringUtils.isEmpty(item.getLineId()) ? "" : StringUtils.join("_", item.getLineId()))));
        putAll(map);
    }

}
