package cc.crrc.manage.cache.dict;

import cc.crrc.manage.cache.AbstractCache;
import cc.crrc.manage.cache.CacheUtils;
import cc.crrc.manage.mapper.line.LineMapping;
import cc.crrc.manage.pojo.line.StationDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import java.util.IntSummaryStatistics;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class StationCache extends AbstractCache<String,List<StationDTO>> {

    @Autowired
    private LineMapping lineMapping;
    public static volatile Integer MAX_STATION_CODE;
    public static volatile Integer MIN_STATION_CODE;

    public StationCache() {
        super("STATION");
    }

    @Override
    public void load() {
        List<StationDTO> list = lineMapping.getStation(new StationDTO());
        Map<String, List<StationDTO>> map = list.stream().collect(Collectors.groupingBy(item ->
                String.join("_",item.getDirection(),StringUtils.isEmpty(item.getMetroLineId()) ? "" : item.getMetroLineId())));
        IntSummaryStatistics summaryStatistics = map.get("up_" + CacheUtils.METRO_LINE_ID).stream()
                .filter(i -> CacheUtils.METRO_LINE_ID.equals(i.getMetroLineId()))
                .map(StationDTO::getStationCode)
                .collect(Collectors.summarizingInt(Integer::intValue));
        MAX_STATION_CODE = summaryStatistics.getMax();
        MIN_STATION_CODE = summaryStatistics.getMin();
        putAll(map);
    }
}
