package cc.crrc.manage.cache.dict;

import cc.crrc.manage.cache.AbstractCache;
import cc.crrc.manage.common.utils.StringUtils;
import cc.crrc.manage.mapper.mtr.MtrVehicleMapping;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

@Component
public class VehicleCache extends AbstractCache<String, List<MtrVehiclePO>> {
    private static final Logger logger = LoggerFactory.getLogger(VehicleCache.class);
    @Autowired
    private MtrVehicleMapping vehicleRepo;

    public static Map<String, MtrVehiclePO> CODE_VEHICLE_MAP = new ConcurrentHashMap<>();
    private final ReadWriteLock lock = new ReentrantReadWriteLock(true);
    private final Lock rLcok = lock.readLock();
    private final Lock wLock = lock.writeLock();


    public VehicleCache() {
        super("VEHICLE");
    }

    @Override
    public void load() {
        List<MtrVehiclePO> list = vehicleRepo.listVehicle(null);
        Map<String, List<MtrVehiclePO>> map = list.stream().collect(Collectors.groupingBy(
                item -> StringUtils.isEmpty(item.getMetroLineId()) ? "" : item.getMetroLineId()));
        //重新设置 CODE_VEHICLE_MAP
        refreshMap(list);
        putAll(map);

    }

    private void refreshMap(List<MtrVehiclePO> list) {
        wLock.lock();
        try {
            CODE_VEHICLE_MAP = list.stream().collect(toMap(MtrVehiclePO::getVehicleCode,i -> i));
        }catch (Exception e){
            logger.error("refreshMap in VehicleCache error = {} e = {}", e.getMessage(), e);
        }finally {
            wLock.unlock();
        }
    }

    public MtrVehiclePO getValue(String key){
        rLcok.lock();
        try {
            return CODE_VEHICLE_MAP.get(key);
        }catch (Exception e){
            logger.error("getValue in VehicleCache error = {} e = {}", e.getMessage(), e);
        }finally {
            rLcok.unlock();
        }
        return null;
    }
}
