package cc.crrc.manage.cache.dict;

import cc.crrc.manage.cache.AbstractCache;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;

@Component
public class TestCahe extends AbstractCache {
    @Override
    public void load() {
        put("name","123");
        put("age","100");
    }

    public TestCahe(){
        super(new ConcurrentHashMap<>(),"test");
    }
}
