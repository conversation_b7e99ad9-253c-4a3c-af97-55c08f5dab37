package cc.crrc.manage.cache;


import org.springframework.context.ApplicationListener;
import org.springframework.util.Assert;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public abstract class AbstractCache<K, V> implements ApplicationListener<CacheEvent> {
    private static final Map identificationMap = new ConcurrentHashMap();
    private Map<K, V> cache;
    private String identification;

    protected AbstractCache(Map<K, V> cache, String identification) {
        init(cache, identification);
        afterProperties();
    }

    protected AbstractCache(String cacheKey) {
        this(new ConcurrentHashMap<>(), cacheKey);
    }

    /**
     * @return void
     * <AUTHOR>
     * @date 2022/5/20 8:52
     * @comment
     */
    public void create() {
        load();
    }

    public V get(K key) {
        return cache.get(key);
    }

    public Map<K, V> get() {
        return cache;
    }

    public void put(K key, V value) {
        cache.put(key, value);
    }

    public void putAll(Map<K, V> map) {
        cache.putAll(map);
    }

    /**
     * @return void
     * <AUTHOR>
     * @date 2022/5/20 8:53
     * @comment refresh cache data
     */
    private void reload() {
        remove();
        load();
    }

    protected void remove() {
        cache.clear();
    }

    private void init(Map<K, V> cache, String identification) {
        if (identificationMap.containsKey(identification)) {
            throw new IllegalArgumentException("Cache identification[" + identification + "] is reduplicate");
        }
        this.cache = cache;
        this.identification = identification;
        identificationMap.put(identification, identification);
    }

    /**
     * @param event
     * @return void
     * <AUTHOR>
     * @date 2022/5/20 8:51
     * @comment receive cache refresh event
     */
    public void onApplicationEvent(CacheEvent event) {
        if (support(event)) {
            reload();
        }
    }

    private boolean support(CacheEvent event) {
        return identification.equals(event.getMessage());
    }

    /**
     * @return void
     * <AUTHOR>
     * @date 2022/5/20 8:51
     * @comment Load cache data by sub class
     */
    public abstract void load();


    /**
     * @return void
     * <AUTHOR>
     * @date 2022/5/20 8:50
     * @comment Check variable
     */
    public void afterProperties() {
        Assert.notNull(cache, "Cache map is null");
        Assert.notNull(identification, "Cache identification is null");
    }

}
