package cc.crrc.manage.cache;

import cc.crrc.manage.common.utils.SpringBeanUtils;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Order(0)
public class CacheRunner implements CommandLineRunner {
    @Override
    public void run(String... args) throws Exception {
        Map<String, AbstractCache> map = SpringBeanUtils.getBeans(AbstractCache.class);
        if (map == null || map.isEmpty()){
            return;
        }
        map.forEach((k, v) -> v.create());
    }
}
