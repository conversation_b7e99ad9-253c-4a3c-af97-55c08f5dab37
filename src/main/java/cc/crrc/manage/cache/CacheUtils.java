package cc.crrc.manage.cache;

import cc.crrc.manage.common.utils.SpringBeanUtils;

import java.util.Map;

public final class CacheUtils {

    public static final String METRO_LINE_ID = "24";
    public static final String VEHICLE_TYPE_ID = "129";

    private CacheUtils(){

    }

    public static <K, V> V getValue(Class<? extends AbstractCache> beanClass, K key){
        AbstractCache<K, V> instance =  SpringBeanUtils.getBean(beanClass);
        return instance.get(key);
    }

    public static <K, V> Map<K,V> getCache(Class<? extends AbstractCache> beanClass){
        AbstractCache<K,V> instance =  SpringBeanUtils.getBean(beanClass);
        return instance.get();
    }

}
