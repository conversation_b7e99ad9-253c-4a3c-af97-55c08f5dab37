package cc.crrc.manage.cache;

import cc.crrc.manage.cache.CacheUtils;
import cc.crrc.manage.cache.dict.DictCache;
import cc.crrc.manage.cache.dict.TestCahe;
import cc.crrc.manage.service.CacheService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
public class CacheController {

    private CacheService service;

    @PostMapping("/cache/refresh")
    public Object refreshCache(@RequestBody List<String> caches) {
        service.refreshCache(caches);
        return null;
    }

    @GetMapping("/cache/value/{key}")
    public Object getCache(@PathVariable String key) {
        System.out.println(CacheUtils.getValue(TestCahe.class, key).toString());
        System.out.println(CacheUtils.getValue(DictCache.class, key).toString());
        return null;
    }
}
