package cc.crrc.manage.monitoringConfig.util;


public class MenuTree extends TreeNodeVO<MenuTree> {

    String name;
    String url;
    String sort;
    String menuCode;
    Boolean showStatus;
    String componentsUrl;
    Boolean defaultActive;

    public MenuTree() {
    }

    public MenuTree(int id,  int parentId) {
        this.id = id;
        this.parentId = parentId;
    }

    public MenuTree(int id,  MenuTree parent) {
        this.id = id;
        this.parentId = parent.getId();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Boolean getShowStatus() {
        return showStatus;
    }

    public void setShowStatus(Boolean showStatus) {
        this.showStatus = showStatus;
    }

    public String getMenuCode() {
        return menuCode;
    }

    public void setMenuCode(String menuCode) {
        this.menuCode = menuCode;
    }

    public String getComponentsUrl() {
        return componentsUrl;
    }

    public void setComponentsUrl(String componentsUrl) {
        this.componentsUrl = componentsUrl;
    }

    public Boolean getDefaultActive() {
        return defaultActive;
    }

    public void setDefaultActive(Boolean defaultActive) {
        this.defaultActive = defaultActive;
    }
}
