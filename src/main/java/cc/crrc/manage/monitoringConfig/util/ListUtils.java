package cc.crrc.manage.monitoringConfig.util;

import java.util.List;

/**
 * @FileName ListUtils
 * @Description 集合工具类
 * <AUTHOR>
 * @Date 2020/7/17 14:02
 **/
public class ListUtils {
    /**
     * @Description  集合不为null和非空校验
     * @Param list 集合
     * @Return  boolean
     * <AUTHOR>
     * @Date 2020/7/17 14:10
     */
    public static <T> boolean isNotEmpty(List<T> list){
        return (list != null && list.size()>0);
    }

}
