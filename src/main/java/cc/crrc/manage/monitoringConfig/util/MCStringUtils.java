package cc.crrc.manage.monitoringConfig.util;

/**
 * @FileName StringUtils
 * @Description 字符串工具类
 * <AUTHOR>
 * @Date 2020/7/20 17:39
 **/
public class MCStringUtils {
    public static boolean isEmpty(String source){
        return source == null || source.isEmpty();
    }

    public static boolean isTrimEmpty(String source){
        return source == null || source.trim().isEmpty();
    }

    public static boolean isNotEmpty(String source){
        return !isEmpty(source);
    }
}
