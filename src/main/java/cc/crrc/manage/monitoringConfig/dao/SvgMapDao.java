package cc.crrc.manage.monitoringConfig.dao;

import cc.crrc.manage.monitoringConfig.entity.SvgMapEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
@Mapper
public interface SvgMapDao {

    int addSvgMap(SvgMapEntity svgMapEntity);

    SvgMapEntity getSvgMapInfoByLineId(@Param("lineId") String lineId);

    int editSvgMapByLineId(SvgMapEntity svgMapEntity);

    int deleteSvgMapByLineId(@Param("lineId") String lineId);
}
