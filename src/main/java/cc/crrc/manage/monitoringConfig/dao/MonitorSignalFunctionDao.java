package cc.crrc.manage.monitoringConfig.dao;

import cc.crrc.manage.monitoringConfig.entity.MonitorSignalFunctionPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;

/**
 * @Author: guo wei
 * 2020-08-08
 */
@Repository
@Mapper
public interface MonitorSignalFunctionDao {
    ArrayList<MonitorSignalFunctionPO> list(@Param("id") String functionId);
    //查询该车辆下可配置模块使用的脚本
    ArrayList<String> listUsedFunctionKey(@Param("traCode") String traCode);
}
