package cc.crrc.manage.monitoringConfig.dao;

import cc.crrc.manage.monitoringConfig.entity.MonitorTableItemEntity;
import cc.crrc.manage.monitoringConfig.entity.TriggerPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
@Mapper
public interface MonitorTableItemDao {
    int addItem(MonitorTableItemEntity itemEntity);

    int deleteItem(@Param("itemId") String itemId);

    int editItem(MonitorTableItemEntity itemEntity);

    List<MonitorTableItemEntity> list(@Param("menuId") String menuId, @Param("type") String type, @Param("slotBoardId") String slotBoardId);

    MonitorTableItemEntity getItemInfoById(@Param("itemId") String itemId);

    List<MonitorTableItemEntity> listForPub(@Param("traCode") String traCode);

    List<String> structureList(@Param("traCode") String traCode);

    //查重使用
    List<MonitorTableItemEntity> selectItemForDuplicateCheck(MonitorTableItemEntity itemEntity);

    List<MonitorTableItemEntity> selectItemByRelationKey(@Param("menuId") String menuId, @Param("relationKey") String relationKey);

    List<TriggerPO> listLabelAndLogo(String itemId);

    //svg菜单 覆盖功能使用
    MonitorTableItemEntity selectItemForSvg(@Param("menuId") String menuId);


    ArrayList<String> getItemIdListForCopy(@Param("menuIdsToDel") List<String> menuIdsToDel);

    //批量保存复制的item
    void addItems(@Param("allItem") List<MonitorTableItemEntity> allItem);

    ArrayList<MonitorTableItemEntity> listForCopy(@Param("menuId") String menuId);

    void updateSlotBoardId(@Param("newSlotBoradId") String newSlotBoradId, @Param("oldSlotBoradId") String oldSlotBoradId, @Param("targetTraCode") String targetTraCode);
}
