package cc.crrc.manage.monitoringConfig.dao;

import cc.crrc.manage.monitoringConfig.entity.MonitorSlotEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
@Mapper
public interface MonitorSlotDao {
    int addSlots(@Param("slotList") List<MonitorSlotEntity> slotList);

    void deleteSlotByItemId(@Param("itemId") String itemId);

    void editSlotType(@Param("slotId") String slotId, @Param("slotType") Integer slotType);

//    List<MonitorSlotEntity> getSlotList(@Param("itemId") String itemId);

//    List<MonitorTableItemEntity> getAllBasicItem(@Param("menuId")String menuId);

//    List<MonitorTableFormatEntity> getFormat(@Param("itemId") String itemId);

    List<MonitorSlotEntity> getSlotByFormatId(@Param("formatId") String formatId);

    List<MonitorSlotEntity> listBoard(@Param("menuId") String menuId);
    List<MonitorSlotEntity> getSlotList(@Param("itemId") String itemId);


    ArrayList<String> getSlotIdListForCopy(@Param("formatIdsToDel") List<String> formatIdsToDel);
}
