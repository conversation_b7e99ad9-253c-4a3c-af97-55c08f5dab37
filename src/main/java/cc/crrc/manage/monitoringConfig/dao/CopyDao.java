package cc.crrc.manage.monitoringConfig.dao;

import cc.crrc.manage.monitoringConfig.entity.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
@Mapper
public interface CopyDao {

    void updateParentId(@Param("newMenuId") String newMenuId, @Param("oldMenuId") String oldMenuId, @Param("targetTraCode") String targetTraCode);

    int delMenu(@Param("menuIds") List<String> menuIds);

    void delItem(@Param("itemIdsToDel") ArrayList<String> itemIdsToDel);

    void delFormat(@Param("formatIdsToDel") ArrayList<String> formatIdsToDel);

    void delSlot(@Param("slotIdsToDel") ArrayList<String> slotIdsToDel);

    void delTrigger(@Param("triggerIdsToDel") ArrayList<String> triggerIdsToDel);

    WebsocketVO getWebsocketVO(@Param("menuId") String menuId);
}
