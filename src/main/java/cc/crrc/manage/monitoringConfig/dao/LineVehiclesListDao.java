package cc.crrc.manage.monitoringConfig.dao;

import cc.crrc.manage.pojo.line.LineDTO;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 线路与车辆列表查询
 * <AUTHOR> yong<PERSON>
 * @version 2020年7月17日9:16:35
 */
@Repository
@Mapper
public interface LineVehiclesListDao {

    List<LineDTO> getLine();

    ArrayList<Map<String, String>> queryStationByLineId(@Param("metroLineId") String metroLineId);

    ArrayList<Map<String, String>> getCity(@Param("tenantId") String tenantId);

    List<MtrVehiclePO> getTrainsForMonitor(@Param("metroLineId") String metroLineId);
}
