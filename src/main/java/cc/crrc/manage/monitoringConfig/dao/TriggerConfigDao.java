package cc.crrc.manage.monitoringConfig.dao;

import cc.crrc.manage.monitoringConfig.entity.TriggerPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * @FileName TriggerConfigDao
 * @Description trigger配置dao层接口
 * <AUTHOR>
 * @Date 2020/7/17 11:02
 **/
@Repository
@Mapper
public interface TriggerConfigDao {
    /**根据卡槽id查询trigger列表数据*/
    List<TriggerPO> findTriggerBySlotId(@Param("slotId") String slotId);

    /**插入trigger数据*/
    Integer insertTriggers(@Param("insertTriggerPOList") List<TriggerPO> insertTriggerPOList);

    /**更新trigger数据*/
    Integer updateTriggers(@Param("triggerPO") TriggerPO triggerPO);

    /**根据triggerId删除对应的数据*/
    Integer deleteTrigger(@Param("id") String id, @Param("modifyBy") String modifyBy);

    /**根据项点id查询所有关联的triggerId*/
    List<String> findTriggerListByItemId(@Param("itemId") String itemId);

    /**根据trigger Id逻辑删除*/
    Integer deleteTriggersByItemId(@Param("triggerIdList") List<String> triggerIdList);

    /**根据slotId查询trigger数量*/
    Integer findCountById(@Param("slotId") String slotId);

    /**根据slotId删除trigger*/
    Integer deleteTriggersBySlotId(@Param("slotId") String slotId);

    ArrayList<String> getTriggerIdListForCopy(@Param("slotIdsToDel") List<String> slotIdsToDel);
}
