package cc.crrc.manage.monitoringConfig.dao;

import cc.crrc.manage.monitoringConfig.entity.MonitorMenuEntity;
import cc.crrc.manage.monitoringConfig.entity.MonitorMenuEntityPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description：菜单数据访问层
 * @FileName: MonitorMenuDao
 * @Author: liu xinchen
 * @Date: 2020/7/16
 */
@Repository
@Mapper
public interface MonitorMenuDao {

    List<MonitorMenuEntityPO> getMonitorMenuList(MonitorMenuEntityPO monitorMenuEntityPO);

    int updateMonitorMenu(MonitorMenuEntity monitorMenuEntity);

    int insertMonitorMenu(MonitorMenuEntity monitorMenuEntity);

    List<MonitorMenuEntity> findMonitorMenuByNameOrCode(MonitorMenuEntity monitorMenuEntity);

    List<MonitorMenuEntityPO> findMonitorMenuByParentName(MonitorMenuEntityPO monitorMenuEntityPO);

    List<MonitorMenuEntityPO> getMenuList(MonitorMenuEntityPO monitorMenuEntityPO);

    List<MonitorMenuEntity> findMonitorMenuByTraCode(String traCode);

    MonitorMenuEntity getMenuById(@Param("id") String menuId);

    ArrayList<String> getMonitorMenuIdListForCopy(@Param("targetTraCodes") List<String> targetTraCodes);

    MonitorMenuEntity getMenuBySlotId(@Param("slotId") String slotId);
}
