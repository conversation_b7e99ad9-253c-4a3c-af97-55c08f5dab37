package cc.crrc.manage.monitoringConfig.dao;

import cc.crrc.manage.monitoringConfig.entity.MonitorConfigWtdSignalPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @FileName WtdSignalDao
 * @Description 信号dao层
 * <AUTHOR>
 * @Date 2020/7/18 14:36
 **/
@Repository
@Mapper
public interface MonitorConfigSignalDao {

    /**
     * 根据项点名称或者输入变量名模糊查询
     */
    List<MonitorConfigWtdSignalPO> findLikeWtdSignalByInputName(@Param("inputName") String inputName, @Param("protocolId") String protocolId);

    /*根据车号查协议号*/
    String selectProtocolIdByVehicleCode(@Param("vehicleCode") String vehicleCode);

    String selectProtocolIdByVehicleType(@Param("vehicleType") String vehicleType);
}
