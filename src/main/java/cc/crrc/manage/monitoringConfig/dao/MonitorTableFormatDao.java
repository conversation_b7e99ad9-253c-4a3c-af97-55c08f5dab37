package cc.crrc.manage.monitoringConfig.dao;

import cc.crrc.manage.monitoringConfig.entity.MonitorTableFormatEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
@Mapper
public interface MonitorTableFormatDao {
    int addFormats(@Param("formatList") List<MonitorTableFormatEntity> formatList);

    void deleteFormatByItemId(@Param("itemId") String itemId);

    List<MonitorTableFormatEntity> selectFormatByItemId(@Param("itemId") String itemId);

    List<MonitorTableFormatEntity> getFormatByItemId(@Param("itemId") String itemId);

    ArrayList<String> getFormatIdListForCopy(@Param("itemIdsToDel") List<String> itemIdsToDel);
}
