package cc.crrc.manage.monitoringConfig.dao;

import cc.crrc.manage.monitoringConfig.entity.SysDictEntity;
import cc.crrc.manage.pojo.SysDictDTO;
import cc.crrc.manage.pojo.SysDictVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
@Mapper
public interface DictForMonitorDao {

//    List<DictEntity> getDictByType(@Param("type") String type);
    List<SysDictEntity> getDictByType(@Param("type") String type);

    List<SysDictEntity> getRelationKeyByMenuId(@Param("menuId") String menuId,
                                               @Param("lineId") String lineId,
                                               @Param("vehicleTypeId") String vehicleTypeId);

    List<SysDictVO> listDictUniversal(SysDictDTO sysDictDTO);

    Map<String, String> getVehicleTypeAndLineIdById(@Param("vehicleId")String vehicleId);

    String getTypeLevel(@Param("typeCode") String typeCode);
}
