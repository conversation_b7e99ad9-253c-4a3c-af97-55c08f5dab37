package cc.crrc.manage.monitoringConfig.dao;

import cc.crrc.manage.monitoringConfig.entity.MonitorPageInfoDTO;
import cc.crrc.manage.monitoringConfig.entity.MonitorPageInfoPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Mapper
public interface MonitorPageInfoDao {

    int addMonitorPageInfo(MonitorPageInfoDTO monitorPageInfoDTO);

    MonitorPageInfoPO getHtmlByMenuCode(@Param("menuCode") String menuCode);

    MonitorPageInfoPO getCssByLineId(@Param("lineId") String lineId);

    int editMonitorPageInfo(MonitorPageInfoDTO monitorPageInfoDTO);

    int deleteMonitorPageInfo(@Param("id") String id);

    List<MonitorPageInfoDTO> monitorPageInfoList(MonitorPageInfoDTO count);
}
