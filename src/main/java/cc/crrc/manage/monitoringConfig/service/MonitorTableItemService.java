package cc.crrc.manage.monitoringConfig.service;

import cc.crrc.manage.cache.AbstractCache;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.UUIDUtils;
import cc.crrc.manage.monitoringConfig.dao.*;
import cc.crrc.manage.monitoringConfig.entity.*;
import cc.crrc.manage.pojo.SysDictVO;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @FileName monitorTableItemService
 * <AUTHOR> xin
 * @Date 2020/7/16 17:44
 * @Version 1.0
 **/
@Service
public class MonitorTableItemService {
    @Autowired
    private MonitorTableItemDao monitorTableItemDao;
    @Autowired
    private MonitorTableFormatDao monitorTableFormatDao;
    @Autowired
    private MonitorSlotDao monitorSlotDao;
    @Autowired
    private TriggerConfigDao triggerConfigDao;
    @Autowired
    private MonitorMenuDao monitorMenuDao;
    @Autowired
    private TriggerConfigService triggerConfigService;
    @Autowired
    private AbstractCache<String,List<SysDictVO>> dictCache;


    public Object structureList(String traCode) {
        List<String> structureList;
        try {
//            structureList = monitorTableItemDao.structureList(traCode);
            //修改为查询数据字典接口
            List<String> carSystem = dictCache.get("car_organize_24_129").stream().map(SysDictVO::getValue).collect(Collectors.toList());
            return carSystem;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object addItem(MonitorTableItemEntity itemEntity) {
        String errorMessage = "保存项点异常!";
        try {
            //判断relationKey是否重复使用
            String menuId = itemEntity.getMenuId();
            String relationKey = itemEntity.getRelationKey();
            if (!StringUtils.isEmpty(relationKey)) {
                List<MonitorTableItemEntity> items = monitorTableItemDao.selectItemByRelationKey(menuId, relationKey);
                if (items.size() > 0) {
                    errorMessage = "新增项点因关联键值重复而失败!";
                    throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
                }
            }
            //第一步首先保存项点item
            /*ItemId*/
            String ItemId = UUIDUtils.generateUuid();
            itemEntity.setId(ItemId);
            MonitorTableItemEntity itemExist = new MonitorTableItemEntity();
            if (StringUtils.isEmpty(itemEntity.getMenuId())) {
                errorMessage = "新增项点因条件缺失而失败！";
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
            }
            itemExist.setMenuId(itemEntity.getMenuId());
            itemExist.setName(itemEntity.getName());
            List<MonitorTableItemEntity> exist = monitorTableItemDao.selectItemForDuplicateCheck(itemExist);

            //判断菜单是不是menuRiomType类型 该类型不做项点名称重复校验
            MonitorMenuEntity monitorMenuEntity =monitorMenuDao.getMenuById(menuId);
            String menuType = monitorMenuEntity.getMenuType();
            if(!"menuRiomType".equals(menuType)){
                if(exist.size()>0){
                    errorMessage = "该菜单下项点名称重复，请重新输入！";
                    throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
                }
            }
            //第二步 获取到车厢编组list 保存format
            long column = itemEntity.getColumn();
            long row = itemEntity.getRow();
            List<String> formatIteration = itemEntity.getFormatStruList();
            List<MonitorTableFormatEntity> formatList = new ArrayList<>();
            for(int j =0;j<formatIteration.size();j++){
                String vehicleLocation = formatIteration.get(j);
                //新建一个format实体类并保存上相应的值 然后放到formatlist里进行批量保存
                MonitorTableFormatEntity monitorTableFormatEntity = new MonitorTableFormatEntity();
                /*formatId*/
                String formatId = UUIDUtils.generateUuid();
                monitorTableFormatEntity.setId(formatId);
                monitorTableFormatEntity.setItemId(ItemId);
                monitorTableFormatEntity.setVehicleLocation(vehicleLocation);
                monitorTableFormatEntity.setNColumn(column);
                monitorTableFormatEntity.setNRow(row);
                monitorTableFormatEntity.setDelFlag(false);
                monitorTableFormatEntity.setSort(String.valueOf(j));
                //第三步 获取每个编组的卡槽数 保存slot
                int slotNumber = itemEntity.getSlotNumber();
                List<MonitorSlotEntity> slotList = new ArrayList<>();
                for(int i = 0;i<slotNumber;i++){
                    //新建一个slot实体类并保存上相应的值 然后放到slotlist里进行批量保存
                    MonitorSlotEntity monitorSlotEntity = new MonitorSlotEntity();
                    monitorSlotEntity.setId(UUIDUtils.generateUuid());
                    monitorSlotEntity.setTableFormatId(formatId);
                    monitorSlotEntity.setLocation(vehicleLocation);
                    monitorSlotEntity.setSlotType(0);
                    monitorSlotEntity.setDelFlag(false);
                    monitorSlotEntity.setSort(i);
                    slotList.add(monitorSlotEntity);
                }
                //保存1 首先进行slotList批量保存
                //todo 保存slot
                int slotAddStatus = monitorSlotDao.addSlots(slotList);
                if(!(slotAddStatus >0)){
                    errorMessage = "没有数据插入,slot保存失败！";
                    throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
                }
                formatList.add(monitorTableFormatEntity);

            }
            //保存2 循环将每个format的slotList保存完在进行formatList批量保存
            //todo 保存format
            int formatAddStatus = monitorTableFormatDao.addFormats(formatList);
            if(!(formatAddStatus >0)){
                errorMessage = "没有数据插入,format保存失败！";
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
            }
            //保存3 将formatList保存完之后证明该项点所有需要保存的东西都保存好了 在进行项点保存
            //todo 保存item
            itemEntity.setDelFlag(false);
            int itemAddStatis = monitorTableItemDao.addItem(itemEntity);
            if(!(itemAddStatis >0)){
                errorMessage = "没有数据插入,item保存失败!！";
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
            }
            return itemEntity;
        }catch (Exception e){
            e.printStackTrace();
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(),errorMessage);
        }
    }


    public Object addItemForSvg(String menuId,String json) {
        MonitorTableItemEntity itemEntity = new MonitorTableItemEntity();
        try {
            //前奏  先把原菜单下的svg项点相关删除
            MonitorTableItemEntity item = null;
            if(StringUtils.isNotEmpty(menuId)){
                 item = monitorTableItemDao.selectItemForSvg(menuId);
            }

            if(null!=item){
                this.batchDeleteItem(item.getId());
            }
            //第一步 首先保存项点item 需要的数据
            /*ItemId*/
            String ItemId = UUIDUtils.generateUuid();
            itemEntity.setId(ItemId);
            itemEntity.setName("svg");
            itemEntity.setMenuId(menuId);
            itemEntity.setType("basic");
            itemEntity.setSort("1");
            //第二步 项点（名称）查重校验
            MonitorTableItemEntity itemExist = new MonitorTableItemEntity();
            if(StringUtils.isEmpty(itemEntity.getMenuId())){
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "新增项点因条件缺失而失败！");
            }
            itemExist.setMenuId(itemEntity.getMenuId());
            itemExist.setName(itemEntity.getName());
            List<MonitorTableItemEntity> exist = monitorTableItemDao.selectItemForDuplicateCheck(itemExist);
            if(exist.size()>0){
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "该菜单下项点名称重复，请重新输入！");
            }
            //第三步 获取svg json 并处理保存format和slot
            JSONArray svgJson = JSONArray.parseArray(json);
            //formatList 用作批量插入
            List<MonitorTableFormatEntity> formatList = new ArrayList<>();
            int i = 1;//自定义计数器 svgJson循环使用
            for(Object format :svgJson){
                //将format转成JSONObject 方便后续操作
                JSONObject formatJson = JSONObject.parseObject(format.toString());
                String vehicleLocation =formatJson.getString("name");
                //新建一个format实体类并保存上相应的值 然后放到formatlist里进行批量保存
                MonitorTableFormatEntity monitorTableFormatEntity = new MonitorTableFormatEntity();
                /*formatId*/
                String formatId = UUIDUtils.generateUuid();
                monitorTableFormatEntity.setId(formatId);
                monitorTableFormatEntity.setItemId(ItemId);
                monitorTableFormatEntity.setVehicleLocation(vehicleLocation);
                monitorTableFormatEntity.setDelFlag(false);
                monitorTableFormatEntity.setSort(String.valueOf(i));
                i++;
                JSONArray slots = formatJson.getJSONArray("slots");
                //slotList 用作批量插入
                List<MonitorSlotEntity> slotList = new ArrayList<>();
                //triggerPOList 用作批量插入
                List<TriggerPO> triggerPOList = new ArrayList<>();
                int j = 1;//自定义计数器 slots循环使用
                for(Object slot:slots){
                    //新建一个slot实体类并保存上相应的值 然后放到slotlist里进行批量保存
                    MonitorSlotEntity monitorSlotEntity = new MonitorSlotEntity();
                    String slotId = UUIDUtils.generateUuid();
                    monitorSlotEntity.setId(slotId);
                    monitorSlotEntity.setTableFormatId(formatId);
                    monitorSlotEntity.setLocation(vehicleLocation);
                    monitorSlotEntity.setSlotType(0);
                    monitorSlotEntity.setDelFlag(false);
                    monitorSlotEntity.setSort(j);
                    j++;

                    //新建一个slot实体类并保存上相应的值 然后放到slotlist里进行批量保存
                    TriggerPO triggerPO = new TriggerPO();
                    JSONObject trigger = JSONObject.parseObject(slot.toString());
                    String label = trigger.getString("name");
                    triggerPO.setId(UUIDUtils.generateUuid());
                    triggerPO.setSlotId(slotId);
                    triggerPO.setLabel(label);
                    triggerPO.setCreateTime(new Date());
                    triggerPO.setModifyTime(triggerPO.getCreateTime());
                    triggerPO.setDelFlag(false);
                    //两个批量保存的list
                    triggerPOList.add(triggerPO);
                    slotList.add(monitorSlotEntity);
                }
                //保存1 trigger保存
                int triggerStatus = triggerConfigDao.insertTriggers(triggerPOList);
                if(!(triggerStatus >0)){
                    throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "trigger保存异常！");
                }
                //保存2 进行slotList批量保存
                int slotAddStatus = monitorSlotDao.addSlots(slotList);
                if(!(slotAddStatus >0)){
                    throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "没有数据插入,slot保存失败！");
                }
                formatList.add(monitorTableFormatEntity);
            }
            //保存3 循环将每个format的slotList保存完在进行formatList批量保存
            //todo 保存format
            int formatAddStatus = monitorTableFormatDao.addFormats(formatList);
            if(!(formatAddStatus >0)){
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "没有数据插入,format保存失败！");
            }
            //保存4 将formatList保存完之后证明该项点所有需要保存的东西都保存好了 在进行项点保存
            //todo 保存item
            itemEntity.setDelFlag(false);
            int itemAddStatis = monitorTableItemDao.addItem(itemEntity);
            if(!(itemAddStatis >0)){
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "没有数据插入,item保存失败！");

            }
            return itemEntity;
        }catch (Exception e){
            e.printStackTrace();
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    //删除项点 同事批量删除卡槽和format
    public Object batchDeleteItem(String itemId) {
        try{
            //第一步 查出项点关联的所有槽点slotid 删除槽点相关联的trigger 并且删除槽点
            monitorSlotDao.deleteSlotByItemId(itemId);
            //todo 删除trigger方法 暂未更新 更新时及时补充
            /*删除trigger方法*/
            triggerConfigService.deleteTriggersByItemId(itemId);
            //第二步 删除项点itemId关联的所有format
            monitorTableFormatDao.deleteFormatByItemId(itemId);
            //第三步 删除itemId对应的项点记录
            monitorTableItemDao.deleteItem(itemId);
            return "删除成功";
        }catch (Exception e){
            e.printStackTrace();
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    //单纯根据itemId删除记录方法
    public Object deleteItem(String itemId) {
        try{
            return monitorTableItemDao.deleteItem(itemId);
        }catch (Exception e){
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }

    //修改项点信息 其中format，slot相关联的 编组行列 和编组个数不可修改 也不去进行修改
    //此修改方法 只修改项点的基础信息
    public Object edit(MonitorTableItemEntity itemEntity) {
        MonitorTableItemEntity itemExist = new MonitorTableItemEntity();
        itemExist.setMenuId(itemEntity.getMenuId());
        itemExist.setName(itemEntity.getName());
        itemExist.setId(itemEntity.getId());
        List<MonitorTableItemEntity> exist = monitorTableItemDao.selectItemForDuplicateCheck(itemExist);
        if(exist.size()>0){
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
        try{
            return monitorTableItemDao.editItem(itemEntity);

        }catch (Exception e){
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
    }
    }

    //查询项点列表 是需要根据条件查询 车厢项点 还是基础项点
    public Object list(String menuId ,String type, String slotBoardId) {
        String typeName = "";
        if(type.equals("basic")){
            typeName="基础";
        }else {
            typeName="车厢";
        }
        List<MonitorTableItemEntity>list;
        try{
            list = monitorTableItemDao.list(menuId,type,slotBoardId);
            return list;
        }catch (Exception e){
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object getItemInfoById(String itemId) {
        MonitorTableItemEntity monitorTableItemEntity;
        try{
            monitorTableItemEntity = monitorTableItemDao.getItemInfoById(itemId);
            List<MonitorTableFormatEntity> monitorTableFormatEntity = monitorTableFormatDao.selectFormatByItemId(itemId);

            if(monitorTableFormatEntity.size()>0){
                long column = monitorTableFormatEntity.get(0).getNColumn();
                long row = monitorTableFormatEntity.get(0).getNRow();
                monitorTableItemEntity.setColumn(column);
                monitorTableItemEntity.setRow(row);
                monitorTableItemEntity.setSlotNumber((int) (column*row));
            }else {
                throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
            }

            return monitorTableItemEntity;
        }catch (Exception e){
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object listForPub(String traCode) {
        List<MonitorTableItemEntity>listForPub;
        try{
            listForPub = monitorTableItemDao.listForPub(traCode);
            return listForPub;
        }catch (Exception e){
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object copyPubItem(String startItem, String targetItemId) {
        try{
            //首先删除目标项点下所有数据
            List<String> targetItemIds = Collections.singletonList(targetItemId);
            triggerConfigDao.deleteTriggersByItemId(targetItemIds);

            monitorTableFormatDao.deleteFormatByItemId(targetItemId);

            monitorSlotDao.deleteSlotByItemId(targetItemId);


            //复制项点下内容 format slot trigger
            //第一层 查询项点下所有format
            List<MonitorTableFormatEntity>formatEntities =  monitorTableFormatDao.selectFormatByItemId(startItem);
            for(MonitorTableFormatEntity formatEntity:formatEntities){
                String newFormatId = UUIDUtils.generateUuid();
                String oldFormatId = formatEntity.getId();
                //第二层查询format下所有slot
                List<MonitorSlotEntity> slotEntities =monitorSlotDao.getSlotByFormatId(oldFormatId);//查询条件 oldFormatId
                for(MonitorSlotEntity slotEntity:slotEntities){
                    String newSlotId = UUIDUtils.generateUuid();
                    String oldSlotId = slotEntity.getId();
                    //第三层查询slot下所有trigger
                    List<TriggerPO>triggerPOS = triggerConfigDao.findTriggerBySlotId(oldSlotId);//查询条件 oldSlotId
                    if(triggerPOS.size()>0){
                        triggerPOS.forEach(o->{
                            o.setId(UUIDUtils.generateUuid());
                            o.setSlotId(newSlotId);
                            //两级翻转！
                            //保存第一层 保存修改后的triggerList
                            //批量保存triggerList
                        });
                        triggerConfigDao.insertTriggers(triggerPOS);
                    }
                    slotEntity.setId(newSlotId);
                    slotEntity.setTableFormatId(newFormatId);
                }
                //保存第二层 保存修改后的slotList
                //批量保存slotList
                monitorSlotDao.addSlots(slotEntities);
                formatEntity.setId(newFormatId);
                formatEntity.setItemId(targetItemId);
            }
            //保存第三层 保存修改后的formatList
            //批量保存formatList
            monitorTableFormatDao.addFormats(formatEntities);
            return "复制成功";
        }catch (Exception e){
            e.printStackTrace();
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    public Object listBoard(String menuId) {
        try{
            Iterator<MonitorSlotEntity> iterator = monitorSlotDao.listBoard(menuId).iterator();
            List<MonitorSlotEntity>list = new ArrayList<>();
            while (iterator.hasNext()){
                MonitorSlotEntity slotEntity = iterator.next();
                String slotId = slotEntity.getId();
                List<TriggerPO> triggerPO = triggerConfigDao.findTriggerBySlotId(slotId);
                if(triggerPO.size()>0){
                    slotEntity.setBoardName(triggerPO.get(0).getLabel()+"("+slotEntity.getLocation()+")");
                    list.add(slotEntity);
                }else{
                    iterator.remove();
                }
            }
            return list;
        }catch (Exception e){
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object listLabelAndLogo(String itemId) {
        try {
            List<TriggerPO> list = monitorTableItemDao.listLabelAndLogo(itemId);
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }
}
