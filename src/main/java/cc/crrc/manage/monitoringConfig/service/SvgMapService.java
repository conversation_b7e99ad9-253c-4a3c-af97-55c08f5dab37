package cc.crrc.manage.monitoringConfig.service;


import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.UUIDUtils;
import cc.crrc.manage.monitoringConfig.dao.SvgMapDao;
import cc.crrc.manage.monitoringConfig.entity.SvgMapEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @FileName SvgCodeService
 * <AUTHOR> xin
 * @Date 2020/8/13 10:40
 * @Version 1.0
 **/
@Service
public class SvgMapService {
    @Autowired
    private SvgMapDao svgMapDao;

    private final static Logger LOGGER = LoggerFactory.getLogger(TriggerConfigService.class);

    public Object addSvgMap(SvgMapEntity svgMapEntity) {
        try{
            SvgMapEntity svgMap = svgMapDao.getSvgMapInfoByLineId(svgMapEntity.getLineId());
            if (null != svgMap){
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(),"保存失败！该线路下存在");
            }
            svgMapEntity.setId(UUIDUtils.generateUuid());
            return svgMapDao.addSvgMap(svgMapEntity);
        }catch(Exception e){
            LOGGER.error(TriggerConfigService.class+"保存svgCode、svgFunction数据失败");
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    public Object getSvgMapInfoByLineId(String lineId) {
        try{
            SvgMapEntity svgMapEntity = svgMapDao.getSvgMapInfoByLineId(lineId);
            return svgMapEntity;
        }catch(Exception e){
            LOGGER.error(TriggerConfigService.class+"查询svgMap信息失败");
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object editSvgMapByLineId(SvgMapEntity svgMapEntity) {
        try{
            return svgMapDao.editSvgMapByLineId(svgMapEntity);
        }catch(Exception e){
            LOGGER.error(TriggerConfigService.class+"修改svgMap信息失败");
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    public Object deleteSvgMapByLineId(String lineId) {
        try{
            return  svgMapDao.deleteSvgMapByLineId(lineId);

        }catch(Exception e){
            LOGGER.error(TriggerConfigService.class+"删除svgMap信息失败");
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
        }
    }
}
