package cc.crrc.manage.monitoringConfig.service;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.monitoringConfig.dao.*;
import cc.crrc.manage.monitoringConfig.entity.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

/**
 * @FileName MonitorSlotService
 * <AUTHOR> xin
 * @Date 2020/7/17 14:53
 * @Version 1.0
 **/
@Service
public class MonitorSlotService {
    @Autowired
    private MonitorSlotDao monitorSlotDao;
    @Autowired
    private TriggerConfigDao triggerConfigDao;
    @Autowired
    private MonitorTableItemDao monitorTableItemDao;
    @Autowired
    private MonitorTableFormatDao monitorTableFormatDao;
    @Autowired
    private MonitorMenuDao monitorMenuDao;

    private final static Logger LOGGER = LoggerFactory.getLogger(TriggerConfigService.class);

    public Object editSlotType(MonitorSlotEntity slotEntity) {
        String slotId = slotEntity.getId();
        Integer slotType = slotEntity.getSlotType();
        //判断不同的菜单类型处理逻辑可能有相应的变化
        MonitorMenuEntity menuEntity = monitorMenuDao.getMenuBySlotId(slotId);
        String menuType = menuEntity.getMenuType();
        //todo 先理逻辑后续开发
        //增加判断 因为多处调用此方法 此处业务逻辑为
        //因为slotType(卡槽类型会随着trigger创建时变动 但是要求一个卡槽内配置的trigger都是同一个类型)
        //所以需要增加校验 如果slotType传值等于0 那说明是trigger完全删除时调用 直接修改即可
        //如果 slotType传值不等于0 那说明slot“第一次”绑定trigger 所以先查slot下有没有trigger进行一次校验
        /*此处业务逻辑有点问题*/
//        if(slotType!=0&&!"menuSvgType".equals(menuType)){
//            List<TriggerPO>list = triggerConfigDao.findTriggerBySlotId(slotId);
//            for(TriggerPO triggerPO:list){
//                if(slotType != triggerPO.getSlotType()){
//                    throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
//                }
//            }
//        }
        try {
            monitorSlotDao.editSlotType(slotId,slotType);
            return "修改成功";
        }catch (Exception e){
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    public Object getSlotStatus(String itemId) {
        try {
            ArrayList<Object> objects = new ArrayList<>();
            // 获取指定项点(itemId)下的所有 Format
            List<MonitorTableFormatEntity> formatList = monitorTableFormatDao.selectFormatByItemId(itemId);
            // 遍历formatList
            for (MonitorTableFormatEntity format : formatList){
                // 获取 formatId
                String formatId = format.getId();
                // 获取指定format下的卡槽列表
                List<MonitorSlotEntity> slotList = monitorSlotDao.getSlotByFormatId(formatId);
                objects.add(slotList);
            }
            return objects;
        } catch (Exception e) {
            LOGGER.error(MonitorSlotService.class + "getSlotStatus:获取数据异常");
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object getAllSlotStatus(String menuId,String type,String slotBoardId) {
        MonitorMenuEntity menuInfo =  monitorMenuDao.getMenuById(menuId);
        String menuType ="";
        if(null!=menuInfo){
             menuType = menuInfo.getMenuType();
        }

        //  判断slotList里的vehicleLocation和formatList里的vehicleLocation是否一致，一致的话就把这个slot放进这个foamat里。

        try {
            JSONObject value  = new JSONObject();
            //第一步 声明总返回结果 res 数据结构的第一层
            JSONArray res = new JSONArray();
            // 查到指定菜单(menuId)下所有的项点
            List<MonitorTableItemEntity> itemList = monitorTableItemDao.list(menuId,type,slotBoardId);

            for (MonitorTableItemEntity item : itemList){
                //第二步 声明项点的json数组  数据结构的第二层
                JSONObject itemObj = new JSONObject();
                //第三步 声明format的json数组 需求：需要format的row，column 以及 format包含的slot 数据结构的第三层
                JSONArray formatObj = new JSONArray();
                String itemId = item.getId();
                String itemName = item.getName();
                String signalFunctionId = item.getSignalFunctionId();
                String itemType = item.getType();
                // 获取指定项点(itemId)下的所有 Format
                List<MonitorTableFormatEntity> formatList = monitorTableFormatDao.selectFormatByItemId(itemId);
                for (MonitorTableFormatEntity format : formatList){
                    //第四步 查询slot数组 并结合format属性向上返回 向上返回
                    JSONObject jsonObject = new JSONObject();
                    // 获取 formatId
                    String formatId = format.getId();
                    // 通过formatId 获取 format，获取行列
                    long nRow = format.getNRow();
                    jsonObject.put("row",nRow);
                    long nColumn = format.getNColumn();
                    jsonObject.put("column",nColumn);
                    // 获取指定format下的卡槽列表
                    List<MonitorSlotEntity> slotList = monitorSlotDao.getSlotByFormatId(formatId);
                    //如果为板卡类型 那么所查询卡槽状态时需要多返回一些数据 所以做额外处理
                    if("menuRiomType".equals(menuType)){
                        for(MonitorSlotEntity slot:slotList){
                            List<TriggerPO> triggerPO = triggerConfigDao.findTriggerBySlotId(slot.getId());
                            if(triggerPO.size()>0){
                                TriggerPO trigger = triggerPO.get(0);//正常情况下 板卡类型 卡槽只配一个trigger  只取一条数据
                                String triggerLabel =  trigger.getLabel();
                                String extProperties ="";
                                JSONArray extProp = JSONArray.parseArray(trigger.getExtProperties());
                                if(extProp != null && extProp.size()>0){
                                    int size = extProp.size();
                                    for (int i = 0; i < size; i++) {
                                        JSONObject o = (JSONObject)extProp.get(i);
                                        Collection<Object> values = o.values();
                                        Iterator<Object> iterator = values.iterator();
                                        while (iterator.hasNext()){
                                            extProperties += ","+iterator.next();
                                        }
                                    }
                                    if(extProperties!=null){
                                        extProperties = extProperties.replaceFirst(",","");
                                    }
                                }
                                slot.setExtProperties(extProperties);
                                slot.setTriggerLabel(triggerLabel);
                            }
                        }
                    }
                    //如果item有脚本 且item类型属于board 将卡槽置为以配置
                    if("board".equals(itemType)&& StringUtils.isNotEmpty(signalFunctionId)){
                        slotList.forEach(o ->{
                            //此处修改卡槽类型是因为 仪表盘存在不配置trigger只配置脚本方法的情况
                            //当配置脚本的时候 不能再slot层做维护所以添加这个逻辑
                            o.setSlotType(-1);
                        });
                    }
                    jsonObject.put("slots",slotList);
                    formatObj.add(jsonObject);
                }
                itemObj.put("itemId",itemId);
                itemObj.put("itemName",itemName);
                itemObj.put("formatList",formatObj);
                res.add(itemObj);
            }
            value.put("value",res);
            return value;

        } catch (Exception e) {
            LOGGER.error(MonitorSlotService.class + "getAllSlotStatusForBasicItem:获取数据异常");
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }

    public Object getSvgSlotStatus(String itemId) {
        try {
            List<MonitorTableFormatEntity> formatList = monitorTableFormatDao.selectFormatByItemId(itemId);
            JSONArray formatArrayList = new JSONArray();
            for (MonitorTableFormatEntity format : formatList){
                //封装单个format内容
                JSONObject formatJson = new JSONObject();
                // 获取 formatId
                String formatId = format.getId();

                // 获取指定format下的卡槽列表
                List<MonitorSlotEntity> slotList = monitorSlotDao.getSlotByFormatId(formatId);
                JSONArray slots = new JSONArray();
                //如果为板卡类型 那么所查询卡槽状态时需要多返回一些数据 所以做额外处理
                for(MonitorSlotEntity slot:slotList){
                    List<TriggerPO> triggerPO = triggerConfigDao.findTriggerBySlotId(slot.getId());
                    if(triggerPO.size()>0){
                        TriggerPO trigger = triggerPO.get(0);//正常情况下 svg类型 卡槽只配一个trigger  只取一条数据
                        String triggerLabel =  trigger.getLabel();
                        slot.setTriggerLabel(triggerLabel);
                    }
                    slots.add(slot);
                }
                formatJson.put("id",formatId);
                formatJson.put("vehicleLocation",format.getVehicleLocation());
                formatJson.put("triggerLabel",format.getVehicleLocation());
                formatJson.put("slots",slots);
                formatArrayList.add(formatJson);
            }
            return formatArrayList;
        } catch (Exception e) {
            LOGGER.error(MonitorSlotService.class + "getAllSlotStatusForBasicItem:获取数据异常");
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }
}
