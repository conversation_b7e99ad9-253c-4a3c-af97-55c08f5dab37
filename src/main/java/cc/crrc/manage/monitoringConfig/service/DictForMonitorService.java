package cc.crrc.manage.monitoringConfig.service;

import cc.crrc.manage.cache.CacheUtils;
import cc.crrc.manage.cache.dict.DictCache;
import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.StringUtils;
import cc.crrc.manage.monitoringConfig.dao.DictForMonitorDao;
import cc.crrc.manage.monitoringConfig.entity.SysDictEntity;
import cc.crrc.manage.pojo.SysDictDTO;
import cc.crrc.manage.pojo.SysDictVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @FileName DictService
 * <AUTHOR> xin
 * @Date 2020/7/23 11:05
 * @Version 1.0
 **/
@Service
public class DictForMonitorService {

    public final static String DEFAULT_LINE_ID = "24";
    private final static String VEHICLE_TYPE_ID = "129";
    @Autowired
    private DictForMonitorDao dictDao;

    public Object getDictByType(String type) {
        try {
            List<SysDictEntity> list = dictDao.getDictByType(type);
            return list;
        }catch (Exception e){
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "数据查询错误！");
        }
    }

    public Object getRelationKeyByMenuId(String menuId,String lineId,String vehicleTypeId) {
        try {
            List<SysDictEntity> list = dictDao.getRelationKeyByMenuId(menuId,lineId,vehicleTypeId);
            return list;
        }catch (Exception e){
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "数据查询错误！");
        }
    }

    public Object listDictUniversal(SysDictDTO sysDictDTO) {
        //第一步 现根据TypeCode查询筛选范围
        String level = dictDao.getTypeLevel(sysDictDTO.getTypeCode());
        if(StringUtils.isEmpty(level)){
            throw new RestApiException(ExceptionInfoEnum.DATA_NOT_FOUND_EXCEPTION);
        }
        //第二步 根据确定的 范围进行条件查询 先声明一个参数实体类
        if(StringUtils.isEmpty(sysDictDTO.getTypeCode())){
            throw new RestApiException(ExceptionInfoEnum.DATA_NOT_FOUND_EXCEPTION.getErrorCode(), "缺少查询条件！");
        }
        SysDictDTO param = new SysDictDTO();
        if(level.equals("1")){
            param.setTypeCode(sysDictDTO.getTypeCode());
        }else if (level.equals("2")) {
            param.setTypeCode(sysDictDTO.getTypeCode());
            String lineId = sysDictDTO.getLineId();
            if(StringUtils.isEmpty(lineId)){
                throw new RestApiException(ExceptionInfoEnum.DATA_NOT_FOUND_EXCEPTION.getErrorCode(),"缺少线路信息！");
            }
            param.setLineId(lineId);
        }else if(level.equals("3")){
            param.setTypeCode(sysDictDTO.getTypeCode());
            String vehicleTypeId = sysDictDTO.getVehicleTypeId();
            if(StringUtils.isEmpty(vehicleTypeId)){
                throw new RestApiException(ExceptionInfoEnum.DATA_NOT_FOUND_EXCEPTION.getErrorCode(),"缺少车型信息！");
            }
            param.setVehicleTypeId(vehicleTypeId);
        }else{
            throw new RestApiException(ExceptionInfoEnum.DATA_USED_EXCEPTION);
        }
        List<SysDictVO> sysDictVOS = CacheUtils.getValue(DictCache.class, sysDictDTO.getTypeCode() + "_" + DEFAULT_LINE_ID + "_" + VEHICLE_TYPE_ID);
        return sysDictVOS;
    }
}
