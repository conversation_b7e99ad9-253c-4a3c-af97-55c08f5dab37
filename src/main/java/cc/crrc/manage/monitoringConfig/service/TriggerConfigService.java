package cc.crrc.manage.monitoringConfig.service;


import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.UUIDUtils;
import cc.crrc.manage.monitoringConfig.dao.TriggerConfigDao;
import cc.crrc.manage.monitoringConfig.entity.MonitorSlotEntity;
import cc.crrc.manage.monitoringConfig.entity.TriggerPO;
import cc.crrc.manage.monitoringConfig.util.ListUtils;
import cc.crrc.manage.monitoringConfig.util.MCStringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @FileName TriggerConfigService
 * @Description trigger配置逻辑层
 * <AUTHOR>
 * @Date 2020/7/16 19:55
 **/
@Service
public class TriggerConfigService {
    //修改标识
    private final static String UPDATEFLAG = "UPDATEFLAG";
    //新增标识
    private final static String INSERTFLAG = "INSERTFLAG";
    private final static Logger LOGGER = LoggerFactory.getLogger(TriggerConfigService.class);

    @Autowired
    private TriggerConfigDao triggerConfigDao;
    @Autowired
    private MonitorConfigSignalService monitorConfigSignalService;
    @Autowired
    private MonitorSlotService monitorSlotService;

    /**
     * @Description 根据卡槽id查询卡槽中trigger数据
     * @Param slotId 卡槽id
     * <AUTHOR> zhijian
     * @Date 2020/7/17 13:30
     */
    public Object findTriggerBySlotId(String slotId) {
        try{
            return  triggerConfigDao.findTriggerBySlotId(slotId);
        }catch(Exception e){
            LOGGER.error(TriggerConfigService.class+"findTriggerBySlotId:卡槽数据获取失败");
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION,"卡槽数据获取失败");
        }
    }

    /**
     * @Description 保存triggerPO数据到数据库(新增和修改共用此接口)
     * @Param triggerPOList trigger集合
     * <AUTHOR> zhijian
     * @Date 2020/7/17 13:32
     */
    @Transactional(readOnly = false)
    public Object saveTrigger(List<TriggerPO> triggerPOList) {
        Integer updateRows = 0;
        Integer insertRows = 0;
        Integer slotType = 0;
        String slotId = "";
        try{
            if(triggerPOList != null && triggerPOList.size()>0) {
                //trigger数据校验
                paramCheckout(triggerPOList);
                slotType = triggerPOList.get(0).getSlotType();
                slotId = triggerPOList.get(0).getSlotId();
                List<TriggerPO> insertTriggerPOList  = new ArrayList<>();
                //解析trigger集合,取出要插入的数据，更新要修改的数据
                for (TriggerPO triggerPO : triggerPOList) {
                    //获取修改或新增标识
                    String UpdeteOrInsertFlag = triggerPO.getUpdeteOrInsertFlag();
                    //判断数据是修改还是新增
                    if(TriggerConfigService.UPDATEFLAG.equals(UpdeteOrInsertFlag)){
                        //修改数据需要更新修改人编码和修改时间
                        triggerPO.setModifyTime(new Date());
                        triggerConfigDao.updateTriggers(triggerPO);
                        updateRows++;
                    }else if(TriggerConfigService.INSERTFLAG.equals(UpdeteOrInsertFlag)){
                        //新增数据需要更新id 创建人、修改人编码和时间
                        triggerPO.setId(UUIDUtils.generateUuid());
                        triggerPO.setCreateTime(new Date());
                        triggerPO.setModifyTime(triggerPO.getCreateTime());
                        triggerPO.setDelFlag(false);
                        insertTriggerPOList.add(triggerPO);
                        insertRows++;
                    }
                }
                //插入新增数据
                if(insertTriggerPOList.size()>0){
                    insertRows = triggerConfigDao.insertTriggers(insertTriggerPOList);
                }
                //当插入数据后修改卡槽类型
                if(insertRows != 0){
                    MonitorSlotEntity slotEntity = new MonitorSlotEntity();
                    slotEntity.setId(slotId);
                    slotEntity.setSlotType(slotType);
                    monitorSlotService.editSlotType(slotEntity);
                }
            }
            return "该卡槽新增"+insertRows+"条数据,修改"+updateRows+"条数据";
        }catch (Exception e){
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION,"trigger配置数据更新失败");
        }
    }

    /**
     * @Description trigger字段校验
     * @Param triggerDTOList
     * @Return JSONObject
     * <AUTHOR> zhijian
     * @Date 2020/7/17 17:13
     */
    private void paramCheckout(List<TriggerPO> triggerPOList) {
        //1、校验所有的卡槽类型是否一致
        Integer slotType = triggerPOList.get(0).getSlotType();
        boolean allElemSlotType = triggerPOList.stream().allMatch(o -> slotType != null && slotType.equals(o.getSlotType()));
        if(!allElemSlotType){
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "卡槽类型不一致,请修改！");
        }
        //2、校验信号扩展属性是否为json数组 暂时先不加
        /*try{
            for (TriggerPO triggerPO : triggerPOList) {
                if(triggerPO.getSlotType().equals(1)){
                    JSONArray jsonArray = JSONArray.parseArray(triggerPO.getExtProperties());
                }
            }
        }catch (Exception e){
            throw new RtException("信号扩展属性格式不正确,请修改!");
        }*/
        //3、校验优先级是否都是正整数 regex = 0|[1-9]\d*
        boolean allElemPositiveInteger = triggerPOList.stream().allMatch(o -> ((o.getSlotType().equals(0)) || (o.getSlotType().equals(2)) ||
                (o.getSlotType().equals(1) && String.valueOf(o.getSort()).matches("0|[1-9]\\d*"))));
        if(!allElemPositiveInteger){
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "优先级格式不正确,请修改!");
        }
    }

    /**
     * @Description 根据id删除数据(逻辑删除) 维护卡槽类型(当没有trigger数据时置零)
     * @Param id triggerPO数据id
     * @Return object
     * <AUTHOR> zhijian
     * @Date 2020/7/18 13:17
     */
    @Transactional(readOnly = false)
    public Object deleteTrigger(String id,String slotId) {
        try{
            Integer rows = triggerConfigDao.deleteTrigger(id,"0");
            Integer countTriggerOfSlot = triggerConfigDao.findCountById(slotId);
            if(countTriggerOfSlot == null && countTriggerOfSlot == 0){
                //当卡槽下的trigger数据全部删除后，修改卡槽slot表格的slotType类型为0
                MonitorSlotEntity slotEntity = new MonitorSlotEntity();
                slotEntity.setId(slotId);
                slotEntity.setSlotType(0);
                monitorSlotService.editSlotType(slotEntity);
            }
            return "成功删除了"+rows+"条数据";
        }catch (Exception e){
            LOGGER.error(TriggerConfigService.class+"deleteTrigger:数据删除失败");
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION.getErrorCode(), "trigger配置数据删除失败");
        }
    }

    /**
     * @Description 根据项点名称或者输入变量名模糊查询
     * @Param inputName 项点名称或输入变量名
     * @Return JSONObject
     * <AUTHOR> zhijian
     * @Date 2020/7/18 13:52
     */
    public Object findLikeWtdSignalByInputName(String vehicleCode , String inputName ) {
        try{
            return monitorConfigSignalService.findLikeWtdSignalByInputName(vehicleCode, inputName);
        }catch(Exception e){
            LOGGER.error(TriggerConfigService.class+"findLikeWtdSignalByInputName:信号变量名称查询失败");
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION,"信号变量名称查询失败");
        }
    }

    /**
     * @Description 根据项点id逻辑删除项点里的trigger数据
     * @Param itemId 项点id
     * @Return JSONObject
     * <AUTHOR> zhijian
     * @Date 2020/7/20 17:28
     */
    public Object deleteTriggersByItemId(String itemId) {
        try{
            List<String> triggerIdList = new ArrayList<>();
            Integer rows = 0;
            if(MCStringUtils.isTrimEmpty(itemId)){
                triggerIdList = triggerConfigDao.findTriggerListByItemId(itemId);
            }
            if(ListUtils.isNotEmpty(triggerIdList)){
                rows = triggerConfigDao.deleteTriggersByItemId(triggerIdList);
            }
            return "删除了该项点下的"+rows+"条trigger数据";
        }catch(Exception e){
            LOGGER.error(TriggerConfigService.class+"deleteTriggersByItemId:删除trigger数据失败");
           throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION,"该项点关联的trigger数据删除失败");
        }
    }

    /**
     * @Description 批量赋值卡槽数据
     * @Param slotIdStart:源卡槽id targetSlotIds:目标卡槽ids
     * @Return
     * <AUTHOR> xin
     * @Date 2020/8/4 9:38
     */
    public Object copyTrigger(String slotIdStart, List<String> targetSlotIds) {
        try{
            if(!(targetSlotIds.size() >0)){
                throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,"目标卡槽为空复制失败");
            }
            //第一步 查询出起始点下所有的trigger
            List<TriggerPO>triggerPOS = triggerConfigDao.findTriggerBySlotId(slotIdStart);
            if(!(triggerPOS.size() >0)){
                throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,"目标卡槽的trigger值为空，请选择有trigger值的卡槽进行复制");
            }
            //第二步 将查询出来的triggerPOS 依次存进目标targetSlotIds里
            for(String slotId:targetSlotIds){
                //将查询出来的triggerPOS进行一次处理 更换点原来的slotId 换成slotIdNew
                triggerPOS.forEach(o ->{o.setSlotId(slotId);
                            o.setId(UUIDUtils.generateUuid());
                            o.setCreateTime(new Date());
                            o.setModifyTime(new Date());
                            o.setDelFlag(false);
                });
                //第三步 现将该卡槽原有的数据删除掉 在进行新增
                triggerConfigDao.deleteTriggersBySlotId(slotId);
                //插入新增数据
                int insertRows = 0;
                Integer slotType = triggerPOS.get(0).getSlotType();
                if(triggerPOS.size()>0){
                     insertRows =  triggerConfigDao.insertTriggers(triggerPOS);
                }
                //当插入数据后修改卡槽类型
                if(insertRows != 0 ){
                    MonitorSlotEntity slotEntity = new MonitorSlotEntity();
                    slotEntity.setId(slotId);
                    slotEntity.setSlotType(slotType);
                    monitorSlotService.editSlotType(slotEntity);
                }
            }
            return "复制成功！";
        }catch(Exception e){
            LOGGER.error(TriggerConfigService.class+"copyTrigger:删复制trigger数据失败");
            throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION,"复制失败");
        }
    }
}
