package cc.crrc.manage.monitoringConfig.service;


import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.JSONObjectUtils;
import cc.crrc.manage.common.utils.UUIDUtils;
import cc.crrc.manage.monitoringConfig.dao.*;
import cc.crrc.manage.monitoringConfig.entity.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.TypeUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * @Author: guo wei
 * 2020-07-20
 */
@Service
public class CopyService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CopyService.class);
    private static ScriptEngine se = new ScriptEngineManager().getEngineByName("javascript");

    @Autowired
    private MonitorMenuDao monitorMenuDao;
    @Autowired
    private MonitorTableItemDao monitorTableItemDao;
    @Autowired
    private MonitorTableFormatDao monitorTableFormatDao;
    @Autowired
    private MonitorSlotDao monitorSlotDao;
    @Autowired
    private TriggerConfigDao triggerConfigDao;
    @Autowired
    private CopyDao copyDao;
    @Autowired
    private MonitorSignalFunctionDao monitorSignalFunctionDao;

    private HashMap<Long, String> signalNameAndIdCache = new HashMap<>();
    private HashMap<String, Class> resultTypeMap = new HashMap<String, Class>() {{
        put("String", String.class);
        put("Float", Float.class);
        put("Boolean", Boolean.class);
        put("Integer", Integer.class);
    }};


    @Transactional(rollbackFor = Exception.class)
    public Object copyVehicle(String originTraCode, List<String> targetTraCodes) {
        try {
            ArrayList<String> menuIdsToDel = new ArrayList<>();
            ArrayList<String> itemIdsToDel = new ArrayList<>();
            ArrayList<String> formatIdsToDel = new ArrayList<>();
            ArrayList<String> slotIdsToDel = new ArrayList<>();
            ArrayList<String> triggerIdsToDel = new ArrayList<>();
            //第一步先查询出目标车辆所有的菜单id
            menuIdsToDel = monitorMenuDao.getMonitorMenuIdListForCopy(targetTraCodes);
            if (menuIdsToDel != null && menuIdsToDel.size() > 0) {
                itemIdsToDel = monitorTableItemDao.getItemIdListForCopy(menuIdsToDel);
                if (itemIdsToDel != null && itemIdsToDel.size() > 0) {
                    formatIdsToDel = monitorTableFormatDao.getFormatIdListForCopy(itemIdsToDel);
                    if (formatIdsToDel != null && formatIdsToDel.size() > 0) {
                        slotIdsToDel = monitorSlotDao.getSlotIdListForCopy(formatIdsToDel);
                        if (slotIdsToDel != null && slotIdsToDel.size() > 0) {
                            triggerIdsToDel = triggerConfigDao.getTriggerIdListForCopy(slotIdsToDel);
                        }
                    }
                }
            }

            if (menuIdsToDel.size() > 0) {
                //逻辑删除信menu表
                copyDao.delMenu(menuIdsToDel);
            }
            if (itemIdsToDel.size() > 0) {
                //逻辑删除item表
                copyDao.delItem(itemIdsToDel);
            }
            if (formatIdsToDel.size() > 0) {
                //逻辑删除format表
                copyDao.delFormat(formatIdsToDel);
            }
            if (slotIdsToDel.size() > 0) {
                //逻辑删除slot表
                copyDao.delSlot(slotIdsToDel);
            }
            if (triggerIdsToDel.size() > 1) {
                //逻辑删除trigger表
                copyDao.delTrigger(triggerIdsToDel);
            }


            for (String targetTraCode : targetTraCodes) {
                List<MonitorMenuEntity> menusByOriginTraCode = monitorMenuDao.findMonitorMenuByTraCode(originTraCode);
                if (!(menusByOriginTraCode != null && menusByOriginTraCode.size() > 0)) {
                    continue;
                }
                //批量保存的List
                List<MonitorTableItemEntity> monitorTableItemEntitiesForInsert = new ArrayList<>();
                List<MonitorTableFormatEntity> formatsForInsert = new ArrayList<>();

                //为后续更新slotBoardId使用
                HashMap<String, String> slotIdForUpdate = new HashMap<String, String>() ;


                for (MonitorMenuEntity vehicleMenuRelationPO : menusByOriginTraCode) {
                    String newMenuId = UUIDUtils.generateUuid();
                    String oldMenuId = vehicleMenuRelationPO.getId();

                    vehicleMenuRelationPO.setId(newMenuId);
                    vehicleMenuRelationPO.setTraCode(targetTraCode);

                    monitorMenuDao.insertMonitorMenu(vehicleMenuRelationPO);
                    //更新子菜单的parentId
                    copyDao.updateParentId(newMenuId, oldMenuId, targetTraCode);

                    if (oldMenuId == null) {
                        continue;
                    }
                    List<MonitorTableItemEntity> monitorTableItemEntities = monitorTableItemDao.listForCopy(oldMenuId);
                    if (!(monitorTableItemEntities != null && monitorTableItemEntities.size() > 0)) {
                        continue;
                    }


                    for (MonitorTableItemEntity monitorTableItemEntity : monitorTableItemEntities) {
                        //每个项点的trigger同时复制
                        List<TriggerPO> triggerPOSForInsert = new ArrayList<>();
                        //每个项点的slot同时复制
                        List<MonitorSlotEntity> slotEntitiesForInsert = new ArrayList<>();
                        String newItemId = UUIDUtils.generateUuid();
                        String oldItemId = monitorTableItemEntity.getId();

                        monitorTableItemEntity.setId(newItemId);
                        monitorTableItemEntity.setMenuId(newMenuId);


                        List<MonitorTableFormatEntity> formats = monitorTableFormatDao.selectFormatByItemId(oldItemId);
                        if (!(formats != null && formats.size() > 0)) {
                            continue;
                        }
                        for (MonitorTableFormatEntity formatEntity : formats) {
                            String newFormatId = UUIDUtils.generateUuid();
                            String oldFormatId = formatEntity.getId();

                            formatEntity.setId(newFormatId);
                            formatEntity.setItemId(newItemId);
                            //第二层查询format下所有slot
                            List<MonitorSlotEntity> slotEntities = monitorSlotDao.getSlotByFormatId(oldFormatId);//查询条件 oldFormatId
                            if (!(slotEntities != null && slotEntities.size() > 0)) {
                                continue;
                            }
                            for (MonitorSlotEntity slotEntity : slotEntities) {
                                String newSlotId = UUIDUtils.generateUuid();
                                String oldSlotId = slotEntity.getId();
                                if("menuRiomType".equals(vehicleMenuRelationPO.getMenuType())&& "carWithRIOMType".equals(monitorTableItemEntity.getCarType())){
                                    slotIdForUpdate.put(oldSlotId,newSlotId);
                                }
                                slotEntity.setId(newSlotId);
                                slotEntity.setTableFormatId(newFormatId);
                                //第三层查询slot下所有trigger
                                List<TriggerPO> triggerPOS = triggerConfigDao.findTriggerBySlotId(oldSlotId);//查询条件 oldSlotId
                                if (!(triggerPOS != null && triggerPOS.size() > 0)) {
                                    slotEntitiesForInsert.add(slotEntity);//如果没有trigger 先把卡槽放进存储list再跳出此层循环
                                    continue;
                                }
                                slotEntitiesForInsert.add(slotEntity);
                                triggerPOS.forEach(o -> {
                                    o.setId(UUIDUtils.generateUuid());
                                    o.setSlotId(newSlotId);
                                    triggerPOSForInsert.add(o);
                                });
                            }
                            formatsForInsert.add(formatEntity);
                        }
                        if (triggerPOSForInsert.size() > 0) {
                            //保存第一层 保存修改后的triggerList
                            //todo 批量保存triggerList
                            triggerConfigDao.insertTriggers(triggerPOSForInsert);
                        }
                        if (slotEntitiesForInsert.size() > 0) {
                            //保存第二层 保存修改后的slotList
                            //todo 批量保存slotList
                            monitorSlotDao.addSlots(slotEntitiesForInsert);
                        }
                        monitorTableItemEntitiesForInsert.add(monitorTableItemEntity);
                    }
                }


                if (formatsForInsert.size() > 0) {
                    //保存第三层 保存修改后的formatList
                    //todo 批量保存formatList
                    monitorTableFormatDao.addFormats(formatsForInsert);
                }
                if (monitorTableItemEntitiesForInsert.size() > 0) {
                    //保存第四层 保存修改后的item
                    monitorTableItemDao.addItems(monitorTableItemEntitiesForInsert);
                }
                if (slotIdForUpdate.size() > 0) {
                    //更新 新旧所属板卡id slotBoardId
                    for(HashMap.Entry<String,String> entry:slotIdForUpdate.entrySet()){
                        String newSlotBoradId = entry.getValue();
                        String oldSlotBoradId = entry.getKey();
                        monitorTableItemDao.updateSlotBoardId(newSlotBoradId,oldSlotBoradId,targetTraCode);


                    }
                }
            }
            return "复制成功！";
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION,"车辆数据复制失败！");
        }
    }


    public JSONObject getAll(JSONObject result, String traCode, HashMap<String, JSONObject> redisKeyHashMap,
                             long date, ThreadPoolExecutor threadPoolExecutor) {
        List<MonitorMenuEntity> monitorMenuByTraCode = monitorMenuDao.findMonitorMenuByTraCode(traCode);
        ArrayList<String> parentMenuId = new ArrayList<>();
        monitorMenuByTraCode.forEach(o -> parentMenuId.add(o.getParentId()));
        //筛选menu下有真实数据的menu（如：筛选掉状态总览菜单，因为数据显示都在子菜单上，此方法不适合父菜单有展示数据、子菜单也有数据）
        List<MonitorMenuEntity> childrenMenus = monitorMenuByTraCode.parallelStream().filter(o -> !parentMenuId.contains(o.getId()))
                .collect(Collectors.toList());
        CountDownLatch countDownLatch = new CountDownLatch(childrenMenus.size());
        for (int i = 0; i < childrenMenus.size(); i++) {
            int x = i;
            Runnable runnable
                    = () -> {
                long l = System.currentTimeMillis();
                MonitorMenuEntity monitorMenuEntity = childrenMenus.get(x);
                JSONObject items = new JSONObject();
                items.put("date", date);
                String menuType = monitorMenuEntity.getMenuType();
                //查询该菜单下的所有项点
                WebsocketVO websocketVO = copyDao.getWebsocketVO(monitorMenuEntity.getId());
                List<WebsocketItemEntity> itemList = websocketVO.getWebsocketItemEntity();
                JSONObject boardItems = new JSONObject();
                JSONArray jsonArray = new JSONArray();
                JSONArray basicItems = new JSONArray();

                if(null!=itemList){
                    //筛选出dashboard数据类型项点
                    List<WebsocketItemEntity> boardItemList = itemList.stream().filter(o -> "board".equals(o.getType())).collect(Collectors.toList());
                    boardItems = dashBoardItem("board", items, redisKeyHashMap, boardItemList, date, traCode);
                    //筛选出车厢数据类型项点
                    List<WebsocketItemEntity> carItemList = itemList.stream().filter(o -> "car".equals(o.getType())).collect(Collectors.toList());
                    JSONArray carItems = wrapperItem("car", redisKeyHashMap, carItemList, date, traCode);
                    jsonArray = carContainItem(carItems, traCode);
                    //筛选出基本数据类型项点
                    List<WebsocketItemEntity> basicItemList = itemList.stream().filter(o -> "basic".equals(o.getType())).collect(Collectors.toList());
                    basicItems = wrapperItem("basic", redisKeyHashMap, basicItemList, date, traCode);
                }

                switch (menuType) {
                    case "menuStaticComponentType":
                        items.put("url", monitorMenuEntity.getComponentsUrl());
                        break;
                    case "dashBoard":
                        items = boardItems;
                        break;
                    case "menuSvgType":
                    case "menuRiomType":
                    case "menuBaseType":
                        items.put("carItemList", jsonArray);
                        items.put("basicItemList", basicItems);
                        break;
                    case "menuUrlType":
                        items.put("url", monitorMenuEntity.getUrl());
                        break;
                    default:
                        break;
                }

                result.put(monitorMenuEntity.getMenuCode(), items);
                long l1 = System.currentTimeMillis();
                System.out.println(l1 - l + "-----    " + monitorMenuEntity.getMenuCode());
                countDownLatch.countDown();
                System.out.println(Thread.currentThread().getName());
            };
            threadPoolExecutor.execute(runnable);
        }
        try {
            countDownLatch.await();
            System.out.println("end");
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * @Description 封装dashboard类型item
     * @Return com.alibaba.fastjson.JSONObject
     * <AUTHOR> wei
     * @Date 10:11 2020/8/6
     * @Param [itemType, result, objResult, boardItemList]
     **/
    private JSONObject dashBoardItem(String itemType, JSONObject result, HashMap<String, JSONObject> objResult, List<WebsocketItemEntity> boardItemList, long date, String traCode) {
//        dahsboard类型一个项点只有一个卡槽
        for (WebsocketItemEntity monitorTableItemEntity : boardItemList) {
            MonitorSignalFunctionPO function = monitorTableItemEntity.getMonitorSignalFunctionPO();
            List<WebsocketFormatEntity> formatList = monitorTableItemEntity.getWebsocketFormatEntity();
            for (WebsocketFormatEntity o : formatList) {
                List<WebsocketSlotEntity> slots = o.getWebsocketSlotEntity();
                for (WebsocketSlotEntity slot : slots) {
                    if (function != null && StringUtils.isNotBlank(function.getId())) {
                        Object signalFunctionResult = null;
                        String functionStr = function.getFunctionStr();
                        String functionName = function.getFunctionName();
                        String redisKey = function.getRedisKey();
                        ArrayList<Object> valueList = new ArrayList<>();
                        valueList.add(0, objResult.get(redisKey));
                        valueList.add(1, traCode);
                        valueList.add(2, slot.getWebsocketTriggerEntity());
                        Object[] triggerValues = valueList.toArray();
                        try {
                            signalFunctionResult = signalFunction(functionStr, functionName, triggerValues);
                        } catch (Exception e) {
                            LOGGER.error("--error{}", e);
                            LOGGER.error("--function[]{}的取值或计算有问题,functionStr{}", function.getId(), functionStr);
                        }
                        if (signalFunctionResult == null) {
                            LOGGER.error("--function[]{}的值为空", function.getId());
                        }
                        result.put(monitorTableItemEntity.getRelationKey(), JSONObject.parseObject(String.valueOf(signalFunctionResult)));
                    } else {

                        JSONObject slotValue = slotConverter(itemType, objResult, slot,
                                monitorTableItemEntity.getMonitorSignalFunctionPO(), date, traCode);
                        result.put(monitorTableItemEntity.getRelationKey(), slotValue);
                    }
                }
            }
        }
        return result;
    }

    /**
     * @Description 将基本项点数据格式转化为车厢项点数据格式。 基本项点数据格式是以每个项点划分为多个车厢（横切），
     * 而厢项点数据格式是一个车厢下显示多个项点（纵分）。
     * @Return com.alibaba.fastjson.JSONArray
     * <AUTHOR> wei
     * @Date 10:14 2020/8/6
     * @Param [carItems, traCode]
     **/
    private JSONArray carContainItem(JSONArray carItems, String traCode) {
        //得到车厢名称list    如；["A1","B1","B2","A2"]
        List<String> carNames = monitorTableItemDao.structureList(traCode);
        ArrayList<JSONObject> carList = new ArrayList<>();
        JSONObject car;
        ArrayList<JSONArray> carValueList = new ArrayList<>();
        JSONArray carValue;
        for (String carName : carNames) {
            car = new JSONObject();
            car.put("carName", carName);
            carList.add(car);
            carValue = new JSONArray();
            carValueList.add(carValue);
        }
        for (Object carItem : carItems) {
            JSONObject o = (JSONObject) carItem;
            Object carType = o.get("carType");
            Object relationKey = o.get("relationKey");
            Object itemName = o.get("itemName");
            Object itemId = o.get("itemId");
            Object slotId = o.get("slotId");
            JSONArray jsonArray1 = JSONArray.parseArray(o.get("itemValue").toString());
            for (int i = 0; i < jsonArray1.size(); i++) {
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put("relationKey", relationKey);
                jsonObject1.put("carType", carType);
                jsonObject1.put("itemName", itemName);
                jsonObject1.put("itemId", itemId);
                jsonObject1.put("slotId", slotId);
                jsonObject1.put("itemValue", jsonArray1.get(i));
                carValueList.get(i).add(jsonObject1);
            }
        }
        for (int i = 0; i < carNames.size(); i++) {
            carList.get(i).put("value", carValueList.get(i));
        }
        return JSONArray.parseArray(carList.toString());
    }

    /**
     * @Description 封装item的内容
     * @Return com.alibaba.fastjson.JSONArray
     * <AUTHOR> wei
     * @Date 10:25 2020/8/6
     * @Param [itemType, objResult, itemList]
     **/
    private JSONArray wrapperItem(String itemType, HashMap<String, JSONObject> objResult, List<WebsocketItemEntity> itemList, long date, String traCode) {
        JSONArray items = new JSONArray();
        for (WebsocketItemEntity monitorTableItemEntity : itemList) {
            JSONObject item = new JSONObject();
            MonitorSignalFunctionPO function = monitorTableItemEntity.getMonitorSignalFunctionPO();
            if (function != null && StringUtils.isNotBlank(function.getId())) {
                Object signalFunctionResult = null;
                String functionStr = function.getFunctionStr();
                String functionName = function.getFunctionName();
                String redisKey = function.getRedisKey();
                ArrayList<Object> valueList = new ArrayList<>();
                valueList.add(0, objResult.get(redisKey));
                valueList.add(1, traCode);
                valueList.add(2, monitorTableItemEntity);
                Object[] triggerValues = valueList.toArray();
                try {
                    signalFunctionResult = signalFunction(functionStr, functionName, triggerValues);
                } catch (Exception e) {
                    LOGGER.error("--error{}", e);
                    LOGGER.error("--function[]{}的取值或计算有问题,functionStr{}", function.getId(), functionStr);
                }
                if (signalFunctionResult == null) {
                    LOGGER.error("--function[]{}的值为空", function.getId());
                }
                item = JSONObject.parseObject(String.valueOf(signalFunctionResult));
            } else {

                JSONArray formats = new JSONArray();
                List<WebsocketFormatEntity> formatList = monitorTableItemEntity.getWebsocketFormatEntity();
                for (WebsocketFormatEntity o : formatList) {
                    JSONObject format = new JSONObject();
                    if (StringUtils.isNotBlank(monitorTableItemEntity.getSlotBoardId())) {
                        item.put("row", o.getNRow());
                        item.put("col", o.getNColumn());
                    } else {
                        format.put("row", o.getNRow());
                        format.put("col", o.getNColumn());
                    }
                    JSONArray formatValue = new JSONArray();
                    List<WebsocketSlotEntity> slots = o.getWebsocketSlotEntity();
                    for (WebsocketSlotEntity slot : slots) {
                        JSONObject slotValue = slotConverter(itemType, objResult, slot,
                                function, date, traCode);
                        formatValue.add(slotValue);
                    }
                    format.put("vehicleLocation", o.getVehicleLocation());
                    format.put("value", formatValue);
                    formats.add(format);
                }
                item.put("itemId", monitorTableItemEntity.getId());
                item.put("itemName", monitorTableItemEntity.getName());
                item.put("relationKey", monitorTableItemEntity.getRelationKey());
                item.put("itemValue", formats);
                item.put("carType", monitorTableItemEntity.getCarType());
                item.put("slotId", monitorTableItemEntity.getSlotBoardId());
            }
            items.add(item);
        }
        return items;
    }

    /**
     * @Description 封装slot
     * @Return com.alibaba.fastjson.JSONObject
     * <AUTHOR> wei
     * @Date 10:26 2020/8/6
     * @Param [itemType, all, slotType, slotId, functionId]
     **/
    private JSONObject slotConverter(String itemType, HashMap<String, JSONObject> all, WebsocketSlotEntity slot,
                                     MonitorSignalFunctionPO function, long date, String traCode) {
        JSONObject slotResult = new JSONObject();
        String slotId = slot.getId();
        //针对绑卡类型菜单：项点类型为car类型的时候需要返回slotId，用于和下方基本类型数据联动
        if ("car".equals(itemType)) {
            slotResult.put("slotId", slotId);
        }
        Object value = null;
        //按优先级查询slotId下的所有trigger可能
        List<WebsocketTriggerEntity> triggers = slot.getWebsocketTriggerEntity();
        String dataDisplayPoint = "";
        String url = null;
        String label = "--";
        String nameEn = null;
        String triggerValue;
        Object triggerValueResult = null;
        String extProperties = "";
//        Object signalFunctionResult = null;
        String resultType;
//        if (function != null && StringUtils.isNotBlank(function.getId())) {
//            String functionStr = function.getFunctionStr();
//            String functionName = function.getFunctionName();
//            String redisKey = function.getRedisKey();
//            ArrayList<Object> valueList = new ArrayList<>();
//            valueList.add(0, all.get(redisKey));
//            valueList.add(1, traCode);
//            valueList.add(2, triggers);
////            for (WebsocketTriggerEntity o : triggers) {
////                url = o.getSvgUrl();
////                if(StringUtils.isNotEmpty(url)){
////                    valueList.add(url);
////                }
////            }
//
//            Object[] triggerValues = valueList.toArray();
//            try {
//                signalFunctionResult = signalFunction(functionStr, functionName, triggerValues);
//            } catch (Exception e) {
//                LOGGER.error("--error{}", e);
//                LOGGER.error("--function[]{}的取值或计算有问题,functionStr{},args{}", function.getId(),functionStr,triggers.toString());
//            }
//            for (WebsocketTriggerEntity o : triggers) {
//                url = o.getSvgUrl();
//                if(StringUtils.isNotEmpty(url)){
//                    valueList.add(url);
//                }
//            }
//            slotResult = JSONObject.parseObject(String.valueOf(signalFunctionResult));
//        } else {
//        if(!(function != null && StringUtils.isNotBlank(function.getId()))){
        for (WebsocketTriggerEntity trigger : triggers) {
            dataDisplayPoint = trigger.getDataDisplayPoint();
            url = trigger.getSvgUrl();
            label = trigger.getLabel();
            triggerValue = trigger.getTriggerValue();
            extProperties = trigger.getExtProperties();
            nameEn = trigger.getSignalNameEn();
            resultType = trigger.getResultType();
            if (nameEn == null) {
                continue;
            }
            switch (resultType) {
                case "Boolean":
//                        value = JSON.parseObject(all.get("trainCode_shadow").get("signals").toString()).get(nameEn);

                    value = JSONObjectUtils.getJsonBooleanValue((JSONObject) all.get("trainCode_shadow").get("signals"), nameEn, "value", null);
                    if (StringUtils.isNotBlank(triggerValue)) {
                        try {
                            triggerValueResult = TypeUtils.castToJavaBean(triggerValue, Boolean.class);
                        } catch (Exception e) {
                            LOGGER.debug("triggerValueResult[]{} signalId[]{}", trigger.getId(), trigger.getSignalId());
                        }
                    }
                    break;
                case "String":
//                        value = JSON.parseObject(all.get("trainCode_shadow").get("signals").toString()).get(nameEn);

                    value = JSONObjectUtils.getJsonStringValue((JSONObject) all.get("trainCode_shadow").get("signals"), nameEn, "value", null);
                    if (StringUtils.isNotBlank(triggerValue)) {
                        triggerValueResult = String.valueOf(triggerValue);
                    }
                    break;
                case "Float":
//                        value = JSON.parseObject(all.get("trainCode_shadow").get("signals").toString()).get(nameEn);

                    value = JSONObjectUtils.getJsonFloatValue((JSONObject) all.get("trainCode_shadow").get("signals"), nameEn, "value", null);
                    if (StringUtils.isNotBlank(triggerValue)) {
                        triggerValueResult = Float.valueOf(triggerValue);
                    }
                    break;
                case "Integer":
                    value = JSONObjectUtils.getJsonIntegerValue((JSONObject) all.get("trainCode_shadow").get("signals"), nameEn, "value", null);
                    if (StringUtils.isNotBlank(triggerValue)) {
                        triggerValueResult = Integer.valueOf(triggerValue);
                    }
                    break;
            }
            if (triggerValueResult != null && value != null && (triggerValueResult == value)) {
                break;
            }
        }

        switch (dataDisplayPoint == null ? "" : dataDisplayPoint) {
            case "1":
                slotResult.put("slotValue", String.valueOf(value));
                slotResult.put("label", label);
                break;
            case "2":
                slotResult.put("slotValue", value + JSONObjectUtils.getJsonStringValue((JSONObject) all.get("trainCode_shadow").get("signals"), nameEn, "unit", ""));
                break;
            case "3":
                slotResult.put("url", url);
                slotResult.put("label", label);
                break;
            case "4":
                slotResult.put("label", label);
                slotResult.put("url", url);
                break;
            case "5":
                JSONArray jsonArray = JSONArray.parseArray(extProperties);
                String resolvedExtProperties = "";
                if (jsonArray != null && jsonArray.size() > 0) {
                    int size = jsonArray.size();
                    for (int i = 0; i < size; i++) {
                        JSONObject o = (JSONObject) jsonArray.get(i);
                        Collection<Object> values = o.values();
                        Iterator<Object> iterator = values.iterator();
                        while (iterator.hasNext()) {
                            resolvedExtProperties += "," + iterator.next();
                        }
                    }
                }
                if (StringUtils.isNotBlank(resolvedExtProperties)) {
                    resolvedExtProperties = resolvedExtProperties.replaceFirst(",", "");
                }
                slotResult.put("extProperties", resolvedExtProperties);
                slotResult.put("label", label);
                slotResult.put("slotValue", value);
                break;
            case "6":
                slotResult.put("slotValue", String.valueOf(value));
                slotResult.put("date", date);
                break;
            //针对svg类型 主从信号展示使用
            case "7":
                slotResult.put("label", label);
                slotResult.put("slotValue", String.valueOf(value));
                if (extProperties == null || extProperties.isEmpty()) {
                    slotResult.put("master", 0);
                } else {
                    JSONArray o = JSONArray.parseArray(extProperties);
                    JSONObject jsonSvgMaster = o.getJSONObject(0);
                    String masterSignal = "";
                    if (jsonSvgMaster != null) {
                        masterSignal = jsonSvgMaster.getString("master");
                        if (!masterSignal.isEmpty()) {
                            slotResult.put("master", JSONObjectUtils.getJsonIntegerValue((JSONObject) all.get("trainCode_shadow").get("signals"), masterSignal, "value"));
                        } else {
                            slotResult.put("master", 0);
                        }
                    }
                }

                break;
            case "0":
            default:
                slotResult.put("label", label);
                slotResult.put("slotValue", label);
                break;
        }
//        }
        return slotResult;
    }

    /**
     * @Description 信号js函数处理
     * @Return java.lang.Float
     * <AUTHOR> wei
     * @Date 13:30 2020/8/6
     * @Param [triggers]
     **/
    static Object signalFunction(String functionStr, String functionName, Object[] triggerValues) {
        try {
            se.eval(functionStr);
            if (se instanceof Invocable) {
                Invocable in = (Invocable) se;
                return in.invokeFunction(functionName, triggerValues);
            }
        } catch (ScriptException | NoSuchMethodException e) {
            e.printStackTrace();
        }
        return null;
    }


}
