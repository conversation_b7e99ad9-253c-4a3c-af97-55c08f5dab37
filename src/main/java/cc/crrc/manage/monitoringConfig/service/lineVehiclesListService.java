package cc.crrc.manage.monitoringConfig.service;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.monitoringConfig.dao.LineVehiclesListDao;
import cc.crrc.manage.monitoringConfig.dao.MonitorMenuDao;
import cc.crrc.manage.pojo.line.LineDTO;
import cc.crrc.manage.pojo.mtr.MtrVehiclePO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 线路与车辆列表查询
 * <AUTHOR> yong<PERSON>
 * @version 2020年7月17日9:20:17
 */
@Service
public class lineVehiclesListService {

    @Autowired
    private LineVehiclesListDao lvld;

    @Autowired
    private MonitorMenuDao mmd;

    private static String superTenantId = "1";
    
    public Object getLine(){
        List<LineDTO> traLineList;
        try{
            traLineList = lvld.getLine();
        }catch(Exception e){
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "数据查询错误！");
        }
        return traLineList;
    }


    public Object getTrains(String lineId){
        List<MtrVehiclePO> traTrainLlist;
        try{
            traTrainLlist = lvld.getTrainsForMonitor(lineId);
        }catch(Exception e){
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "数据查询错误！");
        }
        return traTrainLlist;
    }


    public Object queryStationByLineId(String lineId) {
        try{
            ArrayList<Map<String, String>> stationList = lvld.queryStationByLineId(lineId);
            return stationList;
        }catch(Exception e){
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "数据查询错误！");
        }
    }

    public Object getCity(String tenantId) {
        if (superTenantId.equals(tenantId)) {
            tenantId = "";
        }
        ArrayList<Map<String, String>> citylist;
        try{
            citylist = lvld.getCity(tenantId);
            return citylist;
        }catch(Exception e){
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "数据查询错误！");
        }
    }
}
