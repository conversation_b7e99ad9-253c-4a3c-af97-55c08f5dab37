package cc.crrc.manage.monitoringConfig.service;


import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.UUIDUtils;
import cc.crrc.manage.monitoringConfig.dao.MonitorMenuDao;
import cc.crrc.manage.monitoringConfig.dao.MonitorTableItemDao;
import cc.crrc.manage.monitoringConfig.entity.MonitorMenuEntity;
import cc.crrc.manage.monitoringConfig.entity.MonitorMenuEntityPO;
import cc.crrc.manage.monitoringConfig.util.MenuTree;
import cc.crrc.manage.monitoringConfig.util.TreeUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * @Description：菜单业务层
 * @FileName: MonitorMenuService
 * @Author: liu xinchen
 * @Date: 2020/7/16
 */
@Service
public class MonitorMenuService {

    private final static Logger LOGGER = LoggerFactory.getLogger(MonitorMenuService.class);

    @Autowired
    private MonitorMenuDao monitorMenuDao;
    @Autowired
    private MonitorTableItemDao monitorTableItemDao;

    /**
     * @Description 查询菜单信息/通过id查询菜单信息
     * @Author: liu xinchen
     * @Param: [monitorMenuEntityPO]
     * @return: java.util.List<com.dhc.rad.modules.monitoringConfig.entity.MonitorMenuEntityPO>
     * @Date: 2020/7/20
     */
    public List<MonitorMenuEntityPO> getMonitorMenuList(MonitorMenuEntityPO monitorMenuEntityPO) {
        try{
            List<MonitorMenuEntityPO> list = monitorMenuDao.getMonitorMenuList(monitorMenuEntityPO);
            return list;
        }catch (Exception e){
            e.printStackTrace();
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }

    }

    /**
     * @Description 查询父子结构菜单信息
     * @Author: liu xinchen
     * @Param: [monitorMenuEntityPO]
     * @return: java.util.List<com.dhc.rad.modules.monitoringConfig.util.MenuTree>
     * @Date: 2020/7/24
     */
    public List<MenuTree> getMenuList(MonitorMenuEntityPO monitorMenuEntityPO) {
        List<MonitorMenuEntityPO> menuList = new ArrayList();
        for (MonitorMenuEntityPO menu : monitorMenuDao.getMenuList(monitorMenuEntityPO)) {
            // 首页面不显示仪表盘类型的菜单 过滤仪表盘(dashBoard)类型数据
            if (!menu.getMenuType().equals("dashBoard")) {
                menuList.add(menu);
            }
        }
        return getMenuTree(menuList, "-1");
    }

    /**
     * @Description 查询所有父级菜单名称
     * @Author: liu xinchen
     * @Param: [monitorMenuEntityPO]
     * @return: java.util.List<com.dhc.rad.modules.monitoringConfig.entity.MonitorMenuEntityPO>
     * @Date: 2020/7/24
     */
    public List<MonitorMenuEntityPO> findMonitorMenuByName(MonitorMenuEntityPO monitorMenuEntityPO) {
        //父级菜单id标识码
        monitorMenuEntityPO.setParentId("-1");
        return monitorMenuDao.findMonitorMenuByParentName(monitorMenuEntityPO);
    }

    /**
     * @Description 逻辑删除菜单信息
     * @Author: liu xinchen
     * @Param: [monitorMenuEntityPO]
     * @return: com.dhc.rad.common.web.Result
     * @Date: 2020/7/17
     */
    @Transactional(readOnly = false)
    public Object deleteMonitorMenu(MonitorMenuEntityPO monitorMenuEntityPO) {
        String errorMessage = "数据删除错误!";
        try {
            if (StringUtils.isNotBlank(monitorMenuEntityPO.getId())) {
                monitorMenuEntityPO.setModifyBy("");
                monitorMenuEntityPO.setDelFlag(true);
                monitorMenuEntityPO.setModifyTime(new Timestamp(new Date().getTime()));
                // 查询菜单下是否存在项点
                if (monitorTableItemDao.list(monitorMenuEntityPO.getId(), null, null).isEmpty()) {
                    // 当前删除菜单默认激活状态为false才可以进行删除否则不能进行删除操作
                    if (monitorMenuEntityPO.getDefaultActive() == false) {
                        if (monitorMenuDao.updateMonitorMenu(monitorMenuEntityPO) != 1) {
                            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
                        }
                    } else {
                        // 通过车辆编码查询当前车辆下所有有效菜单信息
                        List<MonitorMenuEntity> monitorMenuByTraCode = monitorMenuDao.findMonitorMenuByTraCode(monitorMenuEntityPO.getTraCode());
                        // 该车辆下菜单只有一个时 不论是否为默认激活都可以删除该菜单
                        if (monitorMenuByTraCode.size() == 1) {
                            // 删除菜单
                            if (monitorMenuDao.updateMonitorMenu(monitorMenuEntityPO) != 1) {
                                throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
                            }
                        } else {
                            errorMessage = "该菜单已选择默认激活不允许删除";
                            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION, "该菜单已选择默认激活不允许删除");
                        }
                    }
                } else {
                    errorMessage = "该菜单下存在绑定项点";
                    throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
                }
            } else {
                errorMessage = "菜单id为空";
                throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION);
            }
        } catch (Exception e) {
            LOGGER.error(MonitorMenuService.class + "deleteMonitorMenu:删除菜单信息失败");
            throw new RestApiException(ExceptionInfoEnum.DATA_DELETE_EXCEPTION.getErrorCode(),errorMessage);
        }
        return null;
    }

    /**
     * @Description 新增Or修改菜单信息
     * @Author: liu xinchen
     * @Param: [monitorMenuEntity]
     * @return: com.dhc.rad.common.web.Result
     * @Date: 2020/7/27
     */
    @Transactional(readOnly = false)
    public Object insertOrUpdateMonitorMenu(MonitorMenuEntityPO monitorMenuEntity) {
        MonitorMenuEntityPO mon = new MonitorMenuEntityPO();
        MonitorMenuEntityPO monitorMenuEntityPO = new MonitorMenuEntityPO();
        try {
            // 判断菜单id是否为空 为空则进行新增
            if (monitorMenuEntity.getId() != null) {
                // 初始化 修改人 修改时间
                monitorMenuEntity.setModifyBy("");
                monitorMenuEntity.setModifyTime(new Timestamp(new Date().getTime()));
                // 编辑校验 菜单名称 菜单编码
                // 通过传入的菜单id查询出对应的菜单信息
                List<MonitorMenuEntityPO> monitorMenuList = monitorMenuDao.getMonitorMenuList(monitorMenuEntity);
                if (!monitorMenuList.isEmpty()) {
                    // 遍历菜单信息集合
                    for (MonitorMenuEntityPO monitorMenuById : monitorMenuList) {
                        // 校验菜单名称 如果查出的菜单名称与当前传入的菜单名称不相等
                        // 则进行筛选同线路内与其他菜单是否存在相同的菜单名称
                        if (!monitorMenuById.getName().equals(monitorMenuEntity.getName())) {
                            // 查询是否存在相同的菜单名称
                            List<MonitorMenuEntity> monitorMenuByNameOrCodeOrSort = monitorMenuDao.findMonitorMenuByNameOrCode(monitorMenuEntity);
                            for (MonitorMenuEntity monitorMenu : monitorMenuByNameOrCodeOrSort) {
                                // 对比是否是同一线路下
                                if (monitorMenu.getTraCode().equals(monitorMenuEntity.getTraCode())) {
                                    // 对比菜单名称是否重复
                                    if (monitorMenu.getName().equals(monitorMenuEntity.getName())) {
                                        // 判断菜单信息是否有效
                                        if (monitorMenu.getDelFlag() == false) {
                                            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "菜单名称已存在！");
                                        }
                                    }
                                }
                            }
                        }
                        if (!monitorMenuById.getMenuCode().equals(monitorMenuEntity.getMenuCode())) {
                            List<MonitorMenuEntity> monitorMenuByNameOrCodeOrSort = monitorMenuDao.findMonitorMenuByNameOrCode(monitorMenuEntity);
                            for (MonitorMenuEntity monitorMenu : monitorMenuByNameOrCodeOrSort) {
                                if (monitorMenu.getTraCode().equals(monitorMenuEntity.getTraCode())) {
                                    if (monitorMenu.getMenuCode().equals(monitorMenuEntity.getMenuCode())) {
                                        if (monitorMenu.getDelFlag() == false) {
                                            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "菜单名称已存在！");
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                // 默认激活判断 一个线路下只有一个为默认激活(true) 其余的均为不激活状态(false)
                monitorMenuEntityPO.setTraCode(monitorMenuEntity.getTraCode());
                monitorMenuEntityPO.setId(monitorMenuEntity.getId());
                // 通过当前要修改的菜单id 所属车辆， 查出该条数据在数据库里的信息
                List<MonitorMenuEntityPO> monitorMenu = monitorMenuDao.getMonitorMenuList(monitorMenuEntityPO);
                for (MonitorMenuEntityPO monitor : monitorMenu) {
                    // 当前要修改的菜单默认激活状态 与 数据库里的激活状态不一致 则进行对比修改
                    if (monitorMenuEntity.getDefaultActive() != monitor.getDefaultActive()) {
                        // 如数据库里该菜单默认激活状态为激活 则不能修改这个菜单的默认激活状态
                        if (monitor.getDefaultActive() == true) {
                            throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION.getErrorCode(), "当前菜单不可修改默认激活状态！");
                        }
                        // 查询当前要修改的菜单所属车辆下的所有菜单信息
                        mon.setTraCode(monitorMenuEntity.getTraCode());
                        List<MonitorMenuEntityPO> moni = monitorMenuDao.getMonitorMenuList(mon);
                        for (MonitorMenuEntityPO mo : moni) {
                            // 当前传入修改的菜单默认激活状态如果为true 其余该车厢下所有菜单默认激活状态都改为false
                            if (!mo.getId().equals(monitorMenuEntity.getId())) {
                                mo.setDefaultActive(false);
                                monitorMenuDao.updateMonitorMenu(mo);
                            }
                        }
                    }
                }
                // 修改菜单信息
                if (monitorMenuDao.updateMonitorMenu(monitorMenuEntity) != 1) {
                    throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION.getErrorCode(), "数据更新异常！");
                }
            } else {
                // 新增校验 菜单名称 菜单编码
                List<MonitorMenuEntity> monitorMenuByNameOrCodeOrSort = monitorMenuDao.findMonitorMenuByNameOrCode(monitorMenuEntity);
                for (MonitorMenuEntity monitor : monitorMenuByNameOrCodeOrSort) {
                    if (monitor.getTraCode().equals(monitorMenuEntity.getTraCode())) {
                        if (monitor.getMenuCode().equals(monitorMenuEntity.getMenuCode())) {
                            if (monitor.getDelFlag() == false) {
                                throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION.getErrorCode(), "菜单编码已存在！");
                            }
                        }
                    }
                    if (monitor.getTraCode().equals(monitorMenuEntity.getTraCode())) {
                        if (monitor.getName().equals(monitorMenuEntity.getName())) {
                            if (monitor.getDelFlag() == false) {
                                throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION.getErrorCode(), "菜单名称已存在！");
                            }
                        }
                    }
                }
                // 初始化 创建人 创建时间 菜单id
                monitorMenuEntity.setCreateBy("");
                monitorMenuEntity.setDelFlag(false);
                monitorMenuEntity.setId(UUIDUtils.generateUuid());
                monitorMenuEntity.setCreateTime(new Timestamp(new Date().getTime()));
                monitorMenuEntityPO.setTraCode(monitorMenuEntity.getTraCode());
                for (MonitorMenuEntityPO monitorMenu : monitorMenuDao.getMonitorMenuList(monitorMenuEntityPO)) {
                    // 判断 当前传入的默认激活是否为空,如果为空则为其赋初始值false(不激活)
                    if (monitorMenuEntity.getDefaultActive() != null) {
                        // 查询该线路下数据库里默认激活状态为空的菜单  并赋初始值false
                        if (monitorMenu.getDefaultActive() == null) {
                            monitorMenu.setDefaultActive(false);
                            monitorMenuDao.updateMonitorMenu(monitorMenu);
                        }
                        // 一个线路下只有一个菜单为默认激活 当前新增的菜单默认激活为true的话 其余该线路下菜单则设置为不激活false
                        if (monitorMenuEntity.getDefaultActive() == true) {
                            monitorMenu.setDefaultActive(false);
                            monitorMenuDao.updateMonitorMenu(monitorMenu);
                        }
                    } else {
                        monitorMenuEntity.setDefaultActive(false);
                    }
                }
                // 新增菜单信息
                if (monitorMenuDao.insertMonitorMenu(monitorMenuEntity) != 1) {
                    throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(), "菜单插入错误！");
                }
            }
        } catch (Exception e) {
            LOGGER.error(MonitorMenuService.class + "insertOrUpdateMonitorMenu:新增Or修改菜单信息失败");
            throw new RestApiException(ExceptionInfoEnum.DATA_ACCESS_EXCEPTION.getErrorCode(), "新增Or修改菜单信息失败！");
        }
        return null;
    }

    /**
     * @Description 获取树形结构的json串
     * @Author: liu xinchen
     * @Param: [menus, root]
     * @return: java.util.List<com.dhc.rad.modules.monitoringConfig.util.MenuTree>
     * @Date: 2020/7/20
     */
    List<MenuTree> getMenuTree(List<MonitorMenuEntityPO> menus, String root) {
        List<MenuTree> trees = new ArrayList<>();
        MenuTree node = null;
        for (MonitorMenuEntityPO menu : menus) {
            node = new MenuTree();
            node.setUrl(menu.getUrl());
            node.setMenuCode(menu.getMenuCode());
            node.setShowStatus(menu.getShowStatus());
            node.setComponentsUrl(menu.getComponentsUrl());
            node.setDefaultActive(menu.getDefaultActive());
            BeanUtils.copyProperties(menu, node);
            trees.add(node);
        }
        List<MenuTree> bulid = TreeUtil.bulid(trees, root, null);
        return bulid;
    }
}
