package cc.crrc.manage.monitoringConfig.service;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.common.utils.UserUtils;
import cc.crrc.manage.controller.excel.ExcelController;
import cc.crrc.manage.monitoringConfig.dao.MonitorPageInfoDao;
import cc.crrc.manage.monitoringConfig.entity.MonitorPageInfoDTO;
import cc.crrc.manage.monitoringConfig.entity.MonitorPageInfoPO;
import org.apache.commons.lang3.StringUtils;
import org.osgi.service.resolver.ResolutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MonitorPageInfoService {
    private final Logger logger = LoggerFactory.getLogger(MonitorPageInfoService.class);

    @Autowired
    private MonitorPageInfoDao monitorPageInfoDao;


    public Object addHtmlByMenuCode(MonitorPageInfoDTO monitorPageInfoDTO) {
        try {
            String type = monitorPageInfoDTO.getType();
            String menuCode = monitorPageInfoDTO.getMenuCode();
            String lineId = monitorPageInfoDTO.getLineId();
            //html样式跟线路id、菜单code挂钩
            if ("html".equals(type) && StringUtils.isNotEmpty(menuCode) && StringUtils.isNotEmpty(lineId)) {
                //查重校验 一条线路下同一个menuCode只有一条记录
                MonitorPageInfoDTO pageInfoDTO = new MonitorPageInfoDTO();
                pageInfoDTO.setMenuCode(monitorPageInfoDTO.getMenuCode());
                pageInfoDTO.setLineId(monitorPageInfoDTO.getLineId());
                pageInfoDTO.setType(type);
                List<MonitorPageInfoDTO> list = monitorPageInfoDao.monitorPageInfoList(pageInfoDTO);
                if(list.size()>0){
                    logger.error("Method[addHtmlByMenuCode] Error:{}", "同线路下，该菜单已经存在配置信息，请勿重复配置！");
                    throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(),"同线路下，该菜单已经存在配置信息，请勿重复配置！");
                }
                //查重通过之后 在进行新增
                monitorPageInfoDTO.setId(PrimaryKeyGenerator.generatorId());
                monitorPageInfoDTO.setCreateBy(UserUtils.getUserId());
                monitorPageInfoDTO.setModifyBy(UserUtils.getUserId());
                int count = monitorPageInfoDao.addMonitorPageInfo(monitorPageInfoDTO);
                return count;
            } else {
                logger.error("Method[addHtmlByMenuCode] Error:{}", "html基础信息配置，新增类型不匹配！");
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
            }
        } catch (Exception e) {
            logger.error("Method[addHtmlByMenuCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }

    }

    public Object editHtmlByMenuCodeAndLineId(MonitorPageInfoDTO monitorPageInfoDTO) {
        try {
            String menuCode = monitorPageInfoDTO.getMenuCode();
            String lineId = monitorPageInfoDTO.getLineId();
            //html样式跟线路id、菜单code挂钩
            if (StringUtils.isNotEmpty(menuCode) && StringUtils.isNotEmpty(lineId)) {
                monitorPageInfoDTO.setModifyBy(UserUtils.getUserId());
                int count = monitorPageInfoDao.editMonitorPageInfo(monitorPageInfoDTO);
                return count;
            } else {
                logger.error("Method[editHtmlByMenuCodeAndLineId] Error:{}", "html基础信息配置，无修改匹配项！");
                throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
            }
        } catch (Exception e) {
            logger.error("Method[editHtmlByMenuCodeAndLineId] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_UPDATE_EXCEPTION);
        }
    }

    public Object getHtmlByMenuCode(String menuCode) {
        try {
            MonitorPageInfoPO monitorPageInfoPO = monitorPageInfoDao.getHtmlByMenuCode(menuCode);
            return monitorPageInfoPO;
        } catch (Exception e) {
            logger.error("Method[getHtmlByMenuCode] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }


    public Object addCssByLineId(MonitorPageInfoDTO monitorPageInfoDTO) {
        try {
            String type = monitorPageInfoDTO.getType();
            String lineId = monitorPageInfoDTO.getLineId();
            //css样式跟线路id、挂钩
            if ("css".equals(type) && StringUtils.isNotEmpty(lineId)) {
                //查重校验 一条线路下同一个menuCode只有一条记录
                MonitorPageInfoDTO pageInfoDTO = new MonitorPageInfoDTO();
                pageInfoDTO.setLineId(monitorPageInfoDTO.getLineId());
                pageInfoDTO.setType(type);
                List<MonitorPageInfoDTO> list = monitorPageInfoDao.monitorPageInfoList(pageInfoDTO);
                if(list.size()>0){
                    logger.error("Method[addHtmlByMenuCode] Error:{}", "同线路下，该菜单已经存在配置信息，请勿重复配置！");
                    throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION.getErrorCode(),"同线路下，该菜单已经存在配置信息，请勿重复配置！");
                }
                //查重通过之后 在进行新增
                monitorPageInfoDTO.setId(PrimaryKeyGenerator.generatorId());
                monitorPageInfoDTO.setCreateBy(UserUtils.getUserId());
                monitorPageInfoDTO.setModifyBy(UserUtils.getUserId());
                int count = monitorPageInfoDao.addMonitorPageInfo(monitorPageInfoDTO);
                return count;
            } else {
                logger.error("Method[addCssByLineId] Error:{}", "css基础信息配置，新增类型不匹配！");
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
            }
        } catch (Exception e) {
            logger.error("Method[addCssByLineId] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }

    }

    public Object editCssById(MonitorPageInfoDTO monitorPageInfoDTO) {
        try {
            String id = monitorPageInfoDTO.getId();
            //css样式跟线路id、挂钩
            if (StringUtils.isNotEmpty(id)) {
                monitorPageInfoDTO.setModifyBy(UserUtils.getUserId());
                int count = monitorPageInfoDao.editMonitorPageInfo(monitorPageInfoDTO);
                return count;
            } else {
                logger.error("Method[editCssById] Error:{}", "css基础信息配置，无修改匹配项！");
                throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
            }
        } catch (Exception e) {
            logger.error("Method[editCssById] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_INSERT_EXCEPTION);
        }
    }

    public Object getCssByLineId(String lineId) {
        try {
            MonitorPageInfoPO monitorPageInfoPO = monitorPageInfoDao.getCssByLineId(lineId);
            return monitorPageInfoPO;
        } catch (Exception e) {
            logger.error("Method[getCssByLineId] Error:{}", e.getMessage());
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION);
        }
    }
}
