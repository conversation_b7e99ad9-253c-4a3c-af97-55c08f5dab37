package cc.crrc.manage.monitoringConfig.service;

import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.monitoringConfig.dao.MonitorSignalFunctionDao;
import cc.crrc.manage.monitoringConfig.entity.MonitorSignalFunctionPO;
import cc.crrc.manage.monitoringConfig.util.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;


/**
 * @Author: guo wei
 * 2020-08-08
 */
@Service
public class MonitorSignalFunctionService {
    @Autowired
    private MonitorSignalFunctionDao monitorSignalFunctionDao;
    public Object list(){
        try {
            return monitorSignalFunctionDao.list(null);
        } catch (Exception e) {
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION,"信号脚本列表查询异常");
        }
    }

    public MonitorSignalFunctionPO getFunctionById(String functionId){
        try {
            ArrayList<MonitorSignalFunctionPO> list = monitorSignalFunctionDao.list(functionId);
            if(list != null && list.size()>0){
                return list.get(0);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
