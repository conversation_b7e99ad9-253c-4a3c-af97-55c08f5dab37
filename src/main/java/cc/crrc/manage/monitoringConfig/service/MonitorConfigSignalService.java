package cc.crrc.manage.monitoringConfig.service;


import cc.crrc.manage.common.exception.ExceptionInfoEnum;
import cc.crrc.manage.common.exception.RestApiException;
import cc.crrc.manage.monitoringConfig.dao.MonitorConfigSignalDao;
import cc.crrc.manage.monitoringConfig.entity.MonitorConfigWtdSignalPO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @FileName WtdSignalService
 * @Description 信号逻辑业务层
 * <AUTHOR>
 * @Date 2020/7/18 14:35
 **/
@Service
public class MonitorConfigSignalService {

    @Autowired
    private MonitorConfigSignalDao monitorConfigSignalDao;
    private final static Logger LOGGER = LoggerFactory.getLogger(MonitorConfigSignalService.class);
    /**
     * @Description  模糊查询信号变量名
     * @Param inputName 项点名称或者输入的中文名称
     * @Return List<WtdSignalPO> 信号集合
     * <AUTHOR> zhijian
     * @Date 2020/7/18 14:47
     */
    public List<MonitorConfigWtdSignalPO> findLikeWtdSignalByInputName(String vehicleCode, String inputName) {
        List<MonitorConfigWtdSignalPO> wtdSignalPOList = new ArrayList<>();
        try{
            String protocolId = monitorConfigSignalDao.selectProtocolIdByVehicleCode(vehicleCode);
            if(null == protocolId){
                return null;
            }
            wtdSignalPOList = monitorConfigSignalDao.findLikeWtdSignalByInputName(inputName, protocolId);
        }catch(Exception e){
            LOGGER.error("findLikeWtdSignalByInputName:信号变量名称查询失败");
            throw new RestApiException(ExceptionInfoEnum.DATA_SELECT_EXCEPTION.getErrorCode(), "数据查询错误！");
        }
        return wtdSignalPOList;
    }

}
