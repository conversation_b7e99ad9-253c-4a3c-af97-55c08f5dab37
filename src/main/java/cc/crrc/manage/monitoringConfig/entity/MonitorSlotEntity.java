package cc.crrc.manage.monitoringConfig.entity;


import java.util.Date;

public class MonitorSlotEntity {

  private String id;
  private String tableFormatId;
  private Integer sort;
  private String location;
  private Date createTime;
  private String createBy;
  private Date modifyTime;
  private String modifyBy;
  private Boolean delFlag;
  private Integer slotType;//卡槽类型 0未配置 1信号 2文本



  //额外返回所需字段
  private String boardName;//板卡名称

  private String triggerLabel;//trigger的状态名称

  private String extProperties;//trigger扩展属性



  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }


  public String getTableFormatId() {
    return tableFormatId;
  }

  public void setTableFormatId(String tableFormatId) {
    this.tableFormatId = tableFormatId;
  }

  public Integer getSort() {
    return sort;
  }

  public void setSort(Integer sort) {
    this.sort = sort;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public void setModifyTime(Date modifyTime) {
    this.modifyTime = modifyTime;
  }

  public String getLocation() {
    return location;
  }

  public void setLocation(String location) {
    this.location = location;
  }

  public String getCreateBy() {
    return createBy;
  }

  public void setCreateBy(String createBy) {
    this.createBy = createBy;
  }

  public String getModifyBy() {
    return modifyBy;
  }

  public void setModifyBy(String modifyBy) {
    this.modifyBy = modifyBy;
  }

  public Boolean getDelFlag() {
    return delFlag;
  }

  public void setDelFlag(Boolean delFlag) {
    this.delFlag = delFlag;
  }

  public Date getCreateTime() {
    return createTime;
  }

  public Date getModifyTime() {
    return modifyTime;
  }

  public Integer getSlotType() {
    return slotType;
  }

  public void setSlotType(Integer slotType) {
    this.slotType = slotType;
  }

  public String getBoardName() {
    return boardName;
  }

  public void setBoardName(String boardName) {
    this.boardName = boardName;
  }

  public String getTriggerLabel() {
    return triggerLabel;
  }

  public void setTriggerLabel(String triggerLabel) {
    this.triggerLabel = triggerLabel;
  }

  public String getExtProperties() {
    return extProperties;
  }

  public void setExtProperties(String extProperties) {
    this.extProperties = extProperties;
  }
}
