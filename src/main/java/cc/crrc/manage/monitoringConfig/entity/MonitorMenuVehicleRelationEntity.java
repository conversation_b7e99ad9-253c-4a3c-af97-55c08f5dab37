package cc.crrc.manage.monitoringConfig.entity;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * @FileName: MonitorMenuVehicleRelation
 * @Author: liu xinchen
 * @Date: 2020/7/28
 */
public class MonitorMenuVehicleRelationEntity implements Serializable {

    private String traCode;
    private String menuId;
    private Boolean enableState;
    private Boolean delFlag;
    private Timestamp createTime;
    private Timestamp modifyTime;
    private String createBy;
    private String modifyBy;

    public String getTraCode() {
        return traCode;
    }

    public void setTraCode(String traCode) {
        this.traCode = traCode;
    }

    public String getMenuId() {
        return menuId;
    }

    public void setMenuId(String menuId) {
        this.menuId = menuId;
    }

    public Boolean getEnableState() {
        return enableState;
    }

    public void setEnableState(Boolean enableState) {
        this.enableState = enableState;
    }

    public Boolean getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Boolean delFlag) {
        this.delFlag = delFlag;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Timestamp modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }
}
