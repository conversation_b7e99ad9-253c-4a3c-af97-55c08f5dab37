package cc.crrc.manage.monitoringConfig.entity;


import java.util.Date;

public class SvgMapEntity {
/**
 * @FileName SvgCodeEntity
 * <AUTHOR> xin
 * @Date 2020/8/13 10:41
 * @Version 1.0
 **/
private String id;
    private String menuId;
    private String vehicleType;
    private String key;
    private String signalFunctionId;
    private String svgCode;
    private String svgFunction;
    private String lineId;
    private Date createTime;
    private String createBy;
    private Date modifyTime;
    private String modifyBy;
    private Boolean delFlag;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }


    public String getMenuId() {
        return menuId;
    }

    public void setMenuId(String menuId) {
        this.menuId = menuId;
    }

    public String getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleType = vehicleType;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }


    public String getSignalFunctionId() {
        return signalFunctionId;
    }

    public void setSignalFunctionId(String signalFunctionId) {
        this.signalFunctionId = signalFunctionId;
    }


    public String getSvgCode() {
        return svgCode;
    }

    public void setSvgCode(String svgCode) {
        this.svgCode = svgCode;
    }


    public String getSvgFunction() {
        return svgFunction;
    }

    public void setSvgFunction(String svgFunction) {
        this.svgFunction = svgFunction;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public String getModifyBy() {
        return modifyBy;
    }

    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    public Boolean getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Boolean delFlag) {
        this.delFlag = delFlag;
    }
}
