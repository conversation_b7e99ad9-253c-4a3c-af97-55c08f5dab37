package cc.crrc.manage.monitoringConfig.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * @FileName BasePO
 * @Description 实体类共有属性
 * @<PERSON> <PERSON>
 * @Date 2020/7/16 19:59
 **/
public class ConfigBasePO implements Serializable {

    private static final long serialVersionUID = -2462517220759198440L;
    //创建用户编号
    private String createBy;
    //修改用户编号
    private String modifyBy;
    //创建用户编号
    private Date createTime;
    //修改用户编号
    private Date modifyTime;
    //逻辑删除标识
    private Boolean delFlag;

    public String getCreateBy() { return createBy; }

    public void setCreateBy(String createBy) { this.createBy = createBy; }

    public String getModifyBy() { return modifyBy; }

    public void setModifyBy(String modifyBy) { this.modifyBy = modifyBy; }

    public Date getCreateTime() { return createTime; }

    public void setCreateTime(Date createTime) { this.createTime = createTime; }

    public Date getModifyTime() { return modifyTime; }

    public void setModifyTime(Date modifyTime) { this.modifyTime = modifyTime; }

    public Boolean getDelFlag() { return delFlag; }

    public void setDelFlag(Boolean delFlag) { this.delFlag = delFlag; }
}
