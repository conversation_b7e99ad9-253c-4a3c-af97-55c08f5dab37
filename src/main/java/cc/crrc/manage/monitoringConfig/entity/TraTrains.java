package cc.crrc.manage.monitoringConfig.entity;
/**
 * 车辆列表
 * <AUTHOR> yong<PERSON>
 * @version 2020年7月17日9:13:53
 */
public class TraTrains {
    private int pkId;
    private String tenantId;
    private String lineId;
    private String assetId;
    private String traCode;
    private String traName;
    private String createBy;
    private String createDate;
    private String updateBy;
    private String updateDate;
    private String traComments;
    private int flagStatus;
    private int pId;
    private int vehicleStorageGroup;
    private boolean trainsDeploy;


    public int getPkId() {
        return pkId;
    }

    public void setPkId(int pkId) {
        this.pkId = pkId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getAssetId() {
        return assetId;
    }

    public void setAssetId(String assetId) {
        this.assetId = assetId;
    }

    public String getTraCode() {
        return traCode;
    }

    public void setTraCode(String traCode) {
        this.traCode = traCode;
    }

    public String getTraName() {
        return traName;
    }

    public void setTraName(String traName) {
        this.traName = traName;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    public String getTraComments() {
        return traComments;
    }

    public void setTraComments(String traComments) {
        this.traComments = traComments;
    }

    public int getFlagStatus() {
        return flagStatus;
    }

    public void setFlagStatus(int flagStatus) {
        this.flagStatus = flagStatus;
    }

    public int getpId() {
        return pId;
    }

    public void setpId(int pId) {
        this.pId = pId;
    }

    public int getVehicleStorageGroup() {
        return vehicleStorageGroup;
    }

    public void setVehicleStorageGroup(int vehicleStorageGroup) {
        this.vehicleStorageGroup = vehicleStorageGroup;
    }

    public boolean getTrainsDeploy() {
        return trainsDeploy;
    }

    public void setTrainsDeploy(boolean trainsDeploy) {
        this.trainsDeploy = trainsDeploy;
    }


}
