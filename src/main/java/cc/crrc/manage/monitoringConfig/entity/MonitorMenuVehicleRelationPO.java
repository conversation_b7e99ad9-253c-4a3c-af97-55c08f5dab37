package cc.crrc.manage.monitoringConfig.entity;


import java.sql.Timestamp;
import java.util.Date;

/**
 * @FileName: MonitorMenuVehicleRelationPO
 * @Author: liu xinchen
 * @Date: 2020/7/28
 */
public class MonitorMenuVehicleRelationPO extends MonitorMenuVehicleRelationEntity {

    private String traCode;
    private String menuId;
    private Boolean enableState;
    private Boolean delFlag;
    private Timestamp createTime;
    private Timestamp modifyTime;
    private String createBy;
    private String modifyBy;

    @Override
    public String getTraCode() {
        return traCode;
    }

    @Override
    public void setTraCode(String traCode) {
        this.traCode = traCode;
    }

    @Override
    public String getMenuId() {
        return menuId;
    }

    @Override
    public void setMenuId(String menuId) {
        this.menuId = menuId;
    }

    @Override
    public Boolean getEnableState() {
        return enableState;
    }

    @Override
    public void setEnableState(Boolean enableState) {
        this.enableState = enableState;
    }

    @Override
    public String getCreateBy() {
        return createBy;
    }

    @Override
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    @Override
    public String getModifyBy() {
        return modifyBy;
    }

    @Override
    public void setModifyBy(String modifyBy) {
        this.modifyBy = modifyBy;
    }

    @Override
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    @Override
    public Timestamp getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Timestamp modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Boolean getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Boolean delFlag) {
        this.delFlag = delFlag;
    }
}
