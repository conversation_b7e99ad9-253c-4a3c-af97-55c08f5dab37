package cc.crrc.manage.monitoringConfig.entity;

import java.util.List;

/**
 * @Author: guo wei
 * 2020-08-11
 */
public class WebsocketSlotEntity extends MonitorSlotEntity{
    private List<WebsocketTriggerEntity> websocketTriggerEntity;

    public List<WebsocketTriggerEntity> getWebsocketTriggerEntity() {
        return websocketTriggerEntity;
    }

    public void setWebsocketTriggerEntity(List<WebsocketTriggerEntity> websocketTriggerEntity) {
        this.websocketTriggerEntity = websocketTriggerEntity;
    }
}
