package cc.crrc.manage.monitoringConfig.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * @FileName WtdSignalPO
 * @Description 信号实体类
 * <AUTHOR>
 * @Date 2020/7/18 13:56
 **/
public class MonitorConfigWtdSignalPO implements Serializable {

    private static final long serialVersionUID = 3350994847046240045L;
    //信号id
    private String id;
    //关联的协议id
    private Integer protocolId;
    //信号中文名称
    private String nameCn;
    //信号英文名称
    private String nameEn;
    //字节偏移
    private String byteOffset;
    //位偏移
    private String bitOffset;
    //数据类型
    private String dataType;
    //信号单位
    private String unit;
    //修改时间
    private Date modifyTime;
    //信号所属系统
    private String subSystem;
    //信号所属子车厢
    private String carLocation;
    //解析函数
    private String parseFunction;
    //信号最大值
    private Float maxValue;
    //信号最小值
    private Float minValue;
    //故障触发值
    private String triggerValue;
    //故障ID
    private String triggerFaultId;
    //数据包帧编号
    private Integer frameType;
    //监控标识符
    private Integer monitorFlag;
    //旁路触发值
    private Integer bypassTriggerValue;
    //信号描述
    private String description;
    //备注
    private String remark;
    //信号解析结果类型
    private String resultType;
    //redis中的存储状态1 存储 0 未存储
    private String redisFlag;

    private Integer packageOrder;

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public Integer getProtocolId() { return protocolId; }

    public void setProtocolId(Integer protocolId) { this.protocolId = protocolId; }

    public String getNameCn() { return nameCn; }

    public void setNameCn(String nameCn) { this.nameCn = nameCn; }

    public String getNameEn() { return nameEn; }

    public void setNameEn(String nameEn) { this.nameEn = nameEn; }

    public String getByteOffset() { return byteOffset; }

    public void setByteOffset(String byteOffset) { this.byteOffset = byteOffset; }

    public String getBitOffset() { return bitOffset; }

    public void setBitOffset(String bitOffset) { this.bitOffset = bitOffset; }

    public String getDataType() { return dataType; }

    public void setDataType(String dataType) { this.dataType = dataType; }

    public String getUnit() { return unit; }

    public void setUnit(String unit) { this.unit = unit; }

    public Date getModifyTime() { return modifyTime; }

    public void setModifyTime(Date modifyTime) { this.modifyTime = modifyTime; }

    public String getSubSystem() { return subSystem; }

    public void setSubSystem(String subSystem) { this.subSystem = subSystem; }

    public String getCarLocation() { return carLocation; }

    public void setCarLocation(String carLocation) { this.carLocation = carLocation; }

    public String getParseFunction() { return parseFunction; }

    public void setParseFunction(String parseFunction) { this.parseFunction = parseFunction; }

    public Float getMaxValue() { return maxValue; }

    public void setMaxValue(Float maxValue) { this.maxValue = maxValue; }

    public Float getMinValue() { return minValue; }

    public void setMinValue(Float minValue) { this.minValue = minValue; }

    public String getTriggerValue() { return triggerValue; }

    public void setTriggerValue(String triggerValue) { this.triggerValue = triggerValue; }

    public String getTriggerFaultId() { return triggerFaultId; }

    public void setTriggerFaultId(String triggerFaultId) { this.triggerFaultId = triggerFaultId; }

    public Integer getMonitorFlag() { return monitorFlag; }

    public void setMonitorFlag(Integer monitorFlag) { this.monitorFlag = monitorFlag; }

    public Integer getBypassTriggerValue() { return bypassTriggerValue; }

    public void setBypassTriggerValue(Integer bypassTriggerValue) { this.bypassTriggerValue = bypassTriggerValue; }

    public String getDescription() { return description; }

    public void setDescription(String description) { this.description = description; }

    public String getRemark() { return remark; }

    public void setRemark(String remark) { this.remark = remark; }

    public String getResultType() { return resultType; }

    public void setResultType(String resultType) { this.resultType = resultType; }

    public Integer getFrameType() {
        return frameType;
    }

    public void setFrameType(Integer frameType) {
        this.frameType = frameType;
    }

    public String getRedisFlag() {
        return redisFlag;
    }

    public void setRedisFlag(String redisFlag) {
        this.redisFlag = redisFlag;
    }

    public Integer getPackageOrder() {
        return packageOrder;
    }

    public void setPackageOrder(Integer packageOrder) {
        this.packageOrder = packageOrder;
    }
}
