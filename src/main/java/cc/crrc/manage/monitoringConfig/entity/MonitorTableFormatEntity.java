package cc.crrc.manage.monitoringConfig.entity;


import java.util.Date;

public class MonitorTableFormatEntity {

  private String id;
  private String itemId;
  private long NRow;
  private long NColumn;
  private String vehicleLocation;
  private Date createTime;
  private String createBy;
  private Date modifyTime;
  private String modifyBy;
  private Boolean delFlag;
  private String sort;


  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }


  public String getItemId() {
    return itemId;
  }

  public void setItemId(String itemId) {
    this.itemId = itemId;
  }


  public long getNRow() {
    return NRow;
  }

  public void setNRow(long NRow) {
    this.NRow = NRow;
  }

  public long getNColumn() {
    return NColumn;
  }

  public void setNColumn(long NColumn) {
    this.NColumn = NColumn;
  }

  public String getVehicleLocation() {
    return vehicleLocation;
  }

  public void setVehicleLocation(String vehicleLocation) {
    this.vehicleLocation = vehicleLocation;
  }


  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public String getCreateBy() {
    return createBy;
  }

  public void setCreateBy(String createBy) {
    this.createBy = createBy;
  }


  public Date getModifyTime() {
    return modifyTime;
  }

  public void setModifyTime(Date modifyTime) {
    this.modifyTime = modifyTime;
  }

  public String getModifyBy() {
    return modifyBy;
  }

  public void setModifyBy(String modifyBy) {
    this.modifyBy = modifyBy;
  }

  public Boolean getDelFlag() {
    return delFlag;
  }

  public void setDelFlag(Boolean delFlag) {
    this.delFlag = delFlag;
  }

  public String getSort() {
    return sort;
  }

  public void setSort(String sort) {
    this.sort = sort;
  }
}
