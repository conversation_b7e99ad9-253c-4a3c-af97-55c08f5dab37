package cc.crrc.manage.monitoringConfig.controller;

import cc.crrc.manage.monitoringConfig.entity.MonitorMenuEntityPO;
import cc.crrc.manage.monitoringConfig.service.MonitorMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description：菜单控制层
 * @FileName: MonitorMenuController
 * @Author: liu xinchen
 * @Date: 2020/7/16
 * @Version 1.0
 */
@RestController
@RequestMapping(value = "/ac/monitoringConfig/monitorMenu")
public class MonitorMenuController {

    @Autowired
    private MonitorMenuService monitorMenuService;

    /**
     * @Description 菜单查询
     * @Author: liu xinchen
     * @Param: [monitorMenuEntityPO]
     * @return: java.lang.Object
     * @Date: 2020/7/20
     */
    @PostMapping(value = "/list")
    public Object getMonitorMenuList(@RequestBody MonitorMenuEntityPO monitorMenuEntityPO) {
        return monitorMenuService.getMonitorMenuList(monitorMenuEntityPO);
    }

    /**
     * @Description 查询父子结构菜单信息
     * @Author: liu xinchen
     * @Param: [monitorMenuEntityPO]
     * @return: java.lang.Object
     * @Date: 2020/7/24
     */
    @PostMapping(value = "/getMenuList")
    public Object getMenuList(@RequestBody MonitorMenuEntityPO monitorMenuEntityPO) {
        return monitorMenuService.getMenuList(monitorMenuEntityPO);
    }

    /**
     * @Description 查询所有父级菜单名称(用于下拉框)
     * @Author: liu xinchen
     * @Param: [monitorMenuEntityPO]
     * @return: java.lang.Object
     * @Date: 2020/7/23
     */
    @PostMapping(value = "/findMonitorMenuByName")
    public Object findMonitorMenuByName(@RequestBody MonitorMenuEntityPO monitorMenuEntityPO) {
        return monitorMenuService.findMonitorMenuByName(monitorMenuEntityPO);
    }

    /**
     * @Description 新增Or修改菜单信息
     * @Author: liu xinchen
     * @Param: [monitorMenuEntityPO]
     * @return: java.lang.Object
     * @Date: 2020/7/17
     */
    @PostMapping(value = "/insertOrUpdateMonitorMenu")
    public Object insertMonitorMenu(@RequestBody MonitorMenuEntityPO monitorMenuEntityPO) {
        return monitorMenuService.insertOrUpdateMonitorMenu(monitorMenuEntityPO);
    }

    /**
     * @Description 删除菜单
     * @Author: liu xinchen
     * @Param: [monitorMenuEntityPO]
     * @return: java.lang.Object
     * @Date: 2020/7/17
     */
    @PostMapping(value = "/deleteMonitorMenu")
    public Object deleteMonitorMenu(@RequestBody MonitorMenuEntityPO monitorMenuEntityPO) {
        return monitorMenuService.deleteMonitorMenu(monitorMenuEntityPO);
    }
}