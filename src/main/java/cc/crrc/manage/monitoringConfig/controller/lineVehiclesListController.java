package cc.crrc.manage.monitoringConfig.controller;

import cc.crrc.manage.common.annotation.LogParam;
import cc.crrc.manage.monitoringConfig.service.CopyService;
import cc.crrc.manage.monitoringConfig.service.lineVehiclesListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 线路与车辆列表查询
 * <AUTHOR> yongda
 * @version 2020年7月17日9:17:43
 */
@RestController
@RequestMapping(value = "/ac/monitoringConfig/lineVehiclesList")
public class lineVehiclesListController {
    @Autowired
    private lineVehiclesListService lvls;

    /**
     * @Description 线路列表
     * @Author: jiang yongda
     * @Date: 2020/7/17
     */
    @GetMapping(value = "/getLine")
    @ResponseBody
    public Object getLine(){
        return lvls.getLine();
    };

    /**
     * @Description 车辆列表
     * <AUTHOR> yongda
     * @Date 2020/7/17
     */
    @GetMapping(value = "/getTrains")
    @ResponseBody
    public Object getTrains(@LogParam(description = "线路id") @RequestParam String metroLineId){
        return lvls.getTrains(metroLineId);
    }

    @Autowired
    private CopyService copyService;
    /**
     * @Description 车辆复制
     * @Return com.dhc.rad.common.web.Result
     * <AUTHOR> wei
     * @Date 8:42 2020/8/5
     * @Param [copyVehicleVO]
    **/
    @PostMapping(value = "/copy/vehicle")
    public Object copyVehicle(@RequestParam String originTraCode, @RequestParam List<String> targetTraCodes){
        return copyService.copyVehicle(originTraCode,targetTraCodes);
    }
    /**
     * 根据线路ID查询该线路下的车站信息
     * <AUTHOR>
     * @Date 2020-08-24 将phm原有接口复制到可配置模块方便统一管理
     * @param lineId
     * @param request
     * @param response
     * @return 返回有序车站信息 站台编码为staCode,站台名称为staName
     * @Description 为可配置监控 提供根据线路id查询车站信息
     */
    @GetMapping(value = "/findStation")
    @ResponseBody
    public Object findStation(String lineId, HttpServletRequest request,
                                                      HttpServletResponse response) {
        return lvls.queryStationByLineId(lineId);
    }

    /**
     * @Description 根据租户查询其 所含有线路的城市列表
     * @Author: lixin
     * @Date: 2020/8/24
     */
    @GetMapping(value = "/getCity")
    @ResponseBody
    public Object getCity(String tenantId){
        return lvls.getCity(tenantId);
    };




}
