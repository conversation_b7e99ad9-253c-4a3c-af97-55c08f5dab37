package cc.crrc.manage.monitoringConfig.controller;


import cc.crrc.manage.monitoringConfig.entity.MonitorTableItemEntity;
import cc.crrc.manage.monitoringConfig.entity.SvgCodeVO;
import cc.crrc.manage.monitoringConfig.service.MonitorTableItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;


/**
 * @FileName monitorTableItemController
 * <AUTHOR> xin
 * @Date 2020/7/16 17:44
 * @Version 1.0
 **/

@RestController
@RequestMapping(value = "/ac/monitoringConfig/item")
public class MonitorTableItemController{
    @Autowired
    private MonitorTableItemService monitorTableItemService;



    /*
     * @return java.lang.Object
     * @Description 查询车辆构型的车厢list
     * <AUTHOR> xin
     * @Date 13:14 2020/7/21
     * @Param [vehicleId]
     **/
    @PostMapping(value = "/structureList")
    public Object structureList(String traCode) {
        return monitorTableItemService.structureList(traCode);
    }

    @PostMapping(value = "/addItem")
    @Transactional(rollbackFor=Exception.class)
    public Object addItem(MonitorTableItemEntity itemEntity) {

        return monitorTableItemService.addItem(itemEntity);
    }
    /*
     * @return java.lang.Object
     * @Description 流程中新增项点(SVG菜单类型)  需要在后端同时维护item，format，slot三张表
     *              因为流程设计中简化操作步骤 所以所需结构性内容由后端进行补充
     * <AUTHOR> xin
     * @Date 13:10 2020/7/21
     * @Param [itemEntity]
     **/
    @PostMapping(value = "/addItemForSvg")
    @Transactional(rollbackFor=Exception.class)
    public Object addItemForSvg(@RequestBody SvgCodeVO svgCodeVO) {

        return monitorTableItemService.addItemForSvg(svgCodeVO.getMenuId(),svgCodeVO.getJson());
    }

    /*
     * @return java.lang.Object
     * @Description 批量删除项点 以及项点关联的format slot 和trigger
     * <AUTHOR> xin
     * @Date 13:11 2020/7/21
     * @Param [itemId]
     **/
    @PostMapping(value = "/batchDelete")
    @Transactional(rollbackFor=Exception.class)
    public Object batchDeleteItem(String itemId) {
        return monitorTableItemService.batchDeleteItem(itemId);
    }


    /*
     * @return java.lang.Object
     * @Description
     * <AUTHOR> xin
     * @Date 13:12 2020/7/21
     * @Param [itemEntity]
     **/
    @PostMapping(value = "/edit")
    @Transactional(rollbackFor=Exception.class)
    public Object edit(MonitorTableItemEntity itemEntity) {
        return monitorTableItemService.edit(itemEntity);
    }


    /*
     * @return java.lang.Object
     * @Description 查询项点列表 是需要根据条件查询 车厢项点 还是基础项点
     * <AUTHOR> xin
     * @Date 13:14 2020/7/21
     * @Param [menuId, type]
     **/
    @GetMapping(value = "/list")
    public Object list(String menuId,String type,String slotBoardId) {
        return monitorTableItemService.list(menuId,type,slotBoardId);
    }

    /*
     * @return java.lang.Object
     * @Description 查询共有项点列表 需要去重（根据项点中文名称去重）
     * <AUTHOR> xin
     * @Date 13:14 2020/7/21
     * @Param [vehicleId]
     **/
    @PostMapping(value = "/listForPub")
    public Object listForPub(String traCode) {
        return monitorTableItemService.listForPub(traCode);
    }


    /*
     * @return java.lang.Object
     * @Description 查询单个项点信息 根据itemId
     * <AUTHOR> xin
     * @Date 13:14 2020/7/21
     * @Param [itemId]
     **/
    @PostMapping(value = "/getItemInfoById")
    public Object getItemInfoById(String itemId) {
        return monitorTableItemService.getItemInfoById(itemId);
    }

    /*
     * @return java.lang.Object
     * @Description 复制已存在的共有项点到 新的菜单下
     * <AUTHOR> xin
     * @Date 15:43 2020/7/21
     * @Param [itemId]
     **/
    @PostMapping(value = "/copyPubItem")
    @Transactional(rollbackFor=Exception.class)
    public Object copyPubItem(String startItem,String targetItemId) {
        return monitorTableItemService.copyPubItem(startItem,targetItemId);
    }

    /*
     * @return java.lang.Object
     * @Description //TODO 
     * <AUTHOR> xin
     * @Date 11:41 2020/7/27
     * @Param [traCode]
     **/
    @PostMapping(value = "/listBoard")
    public Object listBoard(String menuId) {
        return monitorTableItemService.listBoard(menuId);
    }

    @GetMapping(value = "/labelAndLogo")
    public Object listLabelAndLogo(String itemId) {
        return monitorTableItemService.listLabelAndLogo(itemId);
    }

}
