package cc.crrc.manage.monitoringConfig.controller;

import cc.crrc.manage.monitoringConfig.entity.MonitorPageInfoDTO;
import cc.crrc.manage.monitoringConfig.entity.MonitorPageInfoPO;
import cc.crrc.manage.monitoringConfig.entity.SvgMapEntity;
import cc.crrc.manage.monitoringConfig.service.MonitorPageInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "/ac/monitoringConfig/monitorPageInfo")
public class MonitorPageInfoController {

    @Autowired
    private MonitorPageInfoService monitorPageInfoService;
    private MonitorPageInfoDTO monitorPageInfoDTO;


    /*
     * @return java.lang.Object
     * @Description 根据菜单code新增配置所需的html
     * <AUTHOR> xin
     * @Date 13:10 2022/2/16
     * @Param [MonitorPageInfoPO]
     **/
    @PostMapping(value = "/addHtmlByMenuCode")
    public Object addHtmlByMenuCode(@RequestBody MonitorPageInfoDTO monitorPageInfoPO) {
        return monitorPageInfoService.addHtmlByMenuCode(monitorPageInfoPO);
    }


    /*
     * @return java.lang.Object
     * @Description 根据菜单code修改配置所需的html
     * <AUTHOR> xin
     * @Date 13:10 2022/2/16
     * @Param [MonitorPageInfoDTO]
     **/
    @PostMapping(value = "/editHtmlByMenuCodeAndLineId")
    public Object editHtmlByMenuCode(@RequestBody MonitorPageInfoDTO monitorPageInfoDTO) {
        return monitorPageInfoService.editHtmlByMenuCodeAndLineId(monitorPageInfoDTO);
    }
    /*
     * @return java.lang.Object
     * @Description 根根据菜单code删除配置所需的html
     * <AUTHOR> xin
     * @Date 13:10 2022/2/16
     * @Param [itemEntity]
     **/
    @GetMapping(value = "/getHtmlByMenuCode")
    public Object deleteHtmlByMenuCode(@RequestParam String menuCode) {
        return monitorPageInfoService.getHtmlByMenuCode(menuCode);
    }



    /*
     * @return java.lang.Object
     * @Description 根据线路id新增配置所需的css
     * <AUTHOR> xin
     * @Date 13:10 2022/2/16
     * @Param [MonitorPageInfoPO]
     **/
    @PostMapping(value = "/addCssByLineId")
    public Object addCssByLineId(@RequestBody MonitorPageInfoDTO monitorPageInfoPO) {
        return monitorPageInfoService.addCssByLineId(monitorPageInfoPO);
    }

    /*
     * @return java.lang.Object
     * @Description 根据线路id修改配置所需的css
     * <AUTHOR> xin
     * @Date 13:10 2022/2/16
     * @Param [MonitorPageInfoDTO]
     **/
    @PostMapping(value = "/editCssById")
    public Object editCssByLineId(@RequestBody MonitorPageInfoDTO monitorPageInfoDTO) {
        return monitorPageInfoService.editCssById(monitorPageInfoDTO);
    }
    /*
     * @return java.lang.Object
     * @Description 根据线路id删除配置所需的css
     * <AUTHOR> xin
     * @Date 13:10 2022/2/16
     * @Param [itemEntity]
     **/
    @GetMapping(value = "/getCssByLineId")
    public Object getCssByLineId(@RequestParam String lineId) {
        return monitorPageInfoService.getCssByLineId(lineId);
    }





}
