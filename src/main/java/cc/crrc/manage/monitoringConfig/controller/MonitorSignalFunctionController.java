package cc.crrc.manage.monitoringConfig.controller;

import cc.crrc.manage.monitoringConfig.service.MonitorSignalFunctionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 信号function
 * @Author: guo wei
 * 2020-08-08
 */
@RestController
@RequestMapping(value = "/ac/monitoringConfig/function")
public class MonitorSignalFunctionController {
    @Autowired
    private MonitorSignalFunctionService monitorSignalFunctionService;
    @GetMapping(value = "/list")
    public Object list(){
        return monitorSignalFunctionService.list();
    }
}
