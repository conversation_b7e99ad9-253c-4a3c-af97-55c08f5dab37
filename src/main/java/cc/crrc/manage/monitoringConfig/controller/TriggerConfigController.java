package cc.crrc.manage.monitoringConfig.controller;

import cc.crrc.manage.monitoringConfig.entity.TriggerPO;
import cc.crrc.manage.monitoringConfig.service.TriggerConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @FileName TriggerConfigController
 * @Description trigger配置类
 * <AUTHOR>
 * @Date 2020/7/16 19:28
 **/
@RestController
@RequestMapping(value = "/ac/monitoringConfig/trigger/")
public class TriggerConfigController  {

    @Autowired
    private TriggerConfigService triggerConfigService;

    /**
     * @Description  根据卡槽id查询卡槽中trigger数据
     * @Param slotId 卡槽id
     * <AUTHOR>
     * @Date 2020/7/17 9:30
     */
    @GetMapping(value = "findTriggerBySlotId")
    public Object findTriggerBySlotId(@RequestParam String slotId){
        return triggerConfigService.findTriggerBySlotId(slotId);
    }

    /**
     * @Description 保存triggerPO数据到数据库(新增和修改公用此接口)
     * @Param triggerPOList trigger集合
     * <AUTHOR> zhijian
     * @Date 2020/7/17 13:26
     */
    @PostMapping(value = "saveTrigger")
    public Object saveTrigger(@RequestBody List<TriggerPO> triggerPOList) {
        return triggerConfigService.saveTrigger(triggerPOList);
    }

    /**
     * @Description 根据id删除对应的数据
     * @Param id trigger数据id
     * <AUTHOR> zhijian
     * @Date 2020/7/18 13:25
     */
    @GetMapping(value = "deleteTrigger")
    public Object deleteTrigger(@RequestParam String id, @RequestParam String slotId){
        return triggerConfigService.deleteTrigger(id,slotId);
    }

    /**
     * @Description 根据项点名称或输入字符串模糊查询信号变量名
     * @Param inputName 项点名称或输入字符串
     * <AUTHOR> zhijian
     * @Date 2020/7/18 17:06
     */
    @GetMapping(value = "findLikeWtdSignalByInputName")
    public Object findLikeWtdSignalByInputName(@RequestParam String vehicleCode, String inputName){
        return triggerConfigService.findLikeWtdSignalByInputName(vehicleCode, inputName);
    }

    /**
     * @Description 根据项点名称或输入字符串模糊查询信号变量名
     * @Param inputName 项点名称或输入字符串
     * <AUTHOR>
     * @Date 2020/7/28 11:18
     */
    @PostMapping(value = "copyTrigger")
    public Object copyTrigger(@RequestParam String slotIdStart, @RequestParam List<String>targetSlotIds){
        return triggerConfigService.copyTrigger(slotIdStart,targetSlotIds);
    }

}
