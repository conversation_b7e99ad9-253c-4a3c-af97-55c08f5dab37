package cc.crrc.manage.monitoringConfig.controller;

import cc.crrc.manage.monitoringConfig.entity.MonitorSlotEntity;
import cc.crrc.manage.monitoringConfig.service.MonitorSlotService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @FileName MonitorSlotController
 * <AUTHOR> xin
 * @Date 2020/7/17 14:50
 * @Version 1.0
 **/
@RestController
@RequestMapping(value = "/ac/monitoringConfig/slot")
public class MonitorSlotController{

    @Autowired
    private MonitorSlotService monitorSlotService;

    /*
     * @return java.lang.Object
     * @Description  配置trigger时 需要对配置trigger的slot进行类型判定
     * 也就是对slot表数据进行一次slot_tpye字段的维护
     * <AUTHOR> xin
     * @Date 11:15 2020/7/21
     * @Param [slotEntity]
     **/
    @PostMapping(value = "/editSlotType")
    @Transactional(rollbackFor=Exception.class)
    public Object edit(MonitorSlotEntity slotEntity) {
        return monitorSlotService.editSlotType(slotEntity);
    }

    /*
     * @ClassName slotStatus
     * @Description 查询卡槽列表 并展示卡槽配置状态
     * <AUTHOR> mingkuan
     * @Date 2020/7/16 15:47
     **/
    @PostMapping(value = "/slotStatus")
    public Object slotStatus(@RequestParam String itemId){
        return monitorSlotService.getSlotStatus(itemId);
    }

    /*
     * @ClassName getAllSlotStatus
     * @Description 查询指定菜单下的所有项点的卡槽配置状态
     * <AUTHOR> mingkuan
     * @Date 2020/7/23 15:32
     **/
    @PostMapping(value = "/getAllSlotStatus")
    public Object slotStatusForBasicItem(@RequestParam String menuId, String type, String slotBoardId){
        return monitorSlotService.getAllSlotStatus(menuId,type,slotBoardId);
    }

    /*
     * @ClassName getSvgSlotStatus
     * @Description 查询svg菜单下的svg项点的卡槽配置状态
     * <AUTHOR>
     * @Date 2020/8/12 9:00
     **/
    @PostMapping(value = "/getSvgSlotStatus")
    public Object getSvgSlotStatus(@RequestParam String itemId){
        return monitorSlotService.getSvgSlotStatus(itemId);
    }
}
