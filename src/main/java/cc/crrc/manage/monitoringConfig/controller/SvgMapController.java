package cc.crrc.manage.monitoringConfig.controller;


import cc.crrc.manage.common.utils.PrimaryKeyGenerator;
import cc.crrc.manage.monitoringConfig.entity.SvgMapEntity;
import cc.crrc.manage.monitoringConfig.service.SvgMapService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * @FileName SvgCodeController
 * <AUTHOR> xin
 * @Date 2020/8/13 10:39
 * @Version 1.0
 **/
@RestController
@RequestMapping(value = "/ac/monitoringConfig/svg")
public class SvgMapController {
    @Autowired
    private SvgMapService svgMapService;


    /*
     * @return java.lang.Object
     * @Description svgMap新增
     * <AUTHOR> xin
     * @Date 13:10 2020/8/13
     * @Param [itemEntity]
     **/
    @PostMapping(value = "/addSvgMap")
    public Object addSvgMap(@RequestBody SvgMapEntity svgMapEntity) {
        return svgMapService.addSvgMap(svgMapEntity);
    }



    /*
     * @return java.lang.Object
     * @Description 根据线路id获取svgMap信息
     * <AUTHOR> xin
     * @Date 13:10 2020/8/13
     * @Param [itemEntity]
     **/
    @GetMapping(value = "/getSvgMapInfoByLineId")
    public Object getSvgMapInfoByLineId(@RequestParam String lineId) {
        return svgMapService.getSvgMapInfoByLineId(lineId);
    }
    /*
     * @return java.lang.Object
     * @Description 根据线路id获取svgMap信息
     * <AUTHOR> xin
     * @Date 13:10 2020/8/13
     * @Param [itemEntity]
     **/
    @PostMapping(value = "/editSvgMapByLineId")
    public Object editSvgMapByLineId(@RequestBody SvgMapEntity svgMapEntity) {
        return svgMapService.editSvgMapByLineId(svgMapEntity);
    }
    /*
     * @return java.lang.Object
     * @Description 根据线路id获取svgMap信息
     * <AUTHOR> xin
     * @Date 13:10 2020/8/13
     * @Param [itemEntity]
     **/
    @RequestMapping(value = "/deleteSvgMapByLineId")
    public Object deleteSvgMapByLineId(@RequestParam String lineId) {
        return svgMapService.deleteSvgMapByLineId(lineId);
    }
    /**
     * @return List
     * @Description 工具方法 获取该服务器的生成的主键
     * <AUTHOR>
     * @Date 10:04 2021/11/08
     * @Param [number]
     **/

    @ApiOperation(value = "工具方法-获取该服务器的生成的主键")
    @GetMapping("/utils/getPrimaryKey")
    public Object getStaListForDrawLine(@RequestParam("count") int count) {
        List<String> keyList = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            String primaryKey = PrimaryKeyGenerator.generatorId();
            keyList.add(primaryKey);
        }
        return keyList;
    }
}
