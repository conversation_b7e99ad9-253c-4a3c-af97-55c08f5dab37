package cc.crrc.manage.monitoringConfig.controller;

import cc.crrc.manage.monitoringConfig.service.DictForMonitorService;
import cc.crrc.manage.pojo.SysDictDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @FileName DictController
 * <AUTHOR> xin
 * @Date 2020/7/23 11:03
 * @Version 1.0
 **/
@RestController
@RequestMapping(value = "/ac/monitoringConfig/dict")
public class DictForMonitorController {

    @Autowired
    private DictForMonitorService dictService;


    @ApiOperation(value = "查询字典通用接口")
    @PostMapping("/getDictByType")
    public Object listDictUniversal(@RequestBody SysDictDTO sysDictDTO) {
        return dictService.listDictUniversal(sysDictDTO);
    }

    @GetMapping(value = "/getRelationKeyByMenuId")
    public Object getRelationKeyByMenuId(String menuId,String lineId,String vehicleTypeId) {
        return dictService.getRelationKeyByMenuId(menuId,lineId,vehicleTypeId);
    }

}
