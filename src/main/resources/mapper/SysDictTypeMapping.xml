<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.SysDictTypeMapping">
    <sql id="sysDictTypeAlias">
        id,
        type,
        description,
        type_level AS typeLevel
    </sql>
    <!--取得所有字典类型-->
    <select id="listByType" resultType="cc.crrc.manage.pojo.SysDictTypePO">
        SELECT
            <include refid="sysDictTypeAlias"></include>
        FROM
            sys_dict_type
        WHERE
            del_flag = '0'
            <if test="type != null and type != ''">
                AND type LIKE '%'||#{type}||'%'
            </if>
        ORDER BY
            type
    </select>
    <select id="getTypeLevel" resultType="java.lang.String">
        select
            type_level AS typeLevel
        from
            sys_dict_type
        WHERE
        del_flag = '0'
        AND
        type =#{typeCode}
    </select>
    <!--新增-->
    <insert id="addDictType">
        INSERT INTO sys_dict_type (
            id,
            type,
            description,
            type_level,
            create_time,
            create_by,
            modify_time,
            modify_by,
            del_flag
        )
        VALUES (
            #{id},
            #{type},
            #{description},
            #{typeLevel},
            CURRENT_TIMESTAMP,
            #{createBy},
            CURRENT_TIMESTAMP,
            #{modifyBy},
            '0'
        )
    </insert>
    <!--更新-->
    <update id="updateDictType">
        UPDATE sys_dict_type
        SET
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="description != null and description != ''">
                description = #{description},
            </if>
            <if test="typeLevel != null and typeLevel != ''">
                type_level = #{typeLevel},
            </if>
            <if test="modifyBy != null and modifyBy != ''">
                modify_by = #{modifyBy},
            </if>
            <if test="delFlag != null and delFlag != ''">
                del_flag = #{delFlag},
            </if>
            modify_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>
</mapper>