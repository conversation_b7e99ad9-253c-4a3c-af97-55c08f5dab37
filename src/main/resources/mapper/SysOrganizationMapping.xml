<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.SysOrganizationMapping">
    <sql id="sysOrganizationAlias">
        id,
        parent_id AS parentId,
        node_type AS nodeType,
        name,
        name_en AS nameEn,
        code,
        type,
        location_id AS locationId,
        remark,
        modify_time AS modifyTime
    </sql>
    <!--查询所有组织-->
    <select id="listOrganizations" resultType="cc.crrc.manage.pojo.SysOrganizationPO">
        SELECT
            <include refid="sysOrganizationAlias"></include>
        FROM
            sys_organization
        WHERE
            del_flag = '0'
        ORDER BY
            code ASC
    </select>
    <!--根据父级id取得父级名称-->
    <select id="getParentNameByParentId" resultType="java.lang.String">
        SELECT
            name
        FROM
            sys_organization
        WHERE
            id = #{parentId} AND
            del_flag = '0'
    </select>
    <!--根据id查询组织-->
    <select id="getOrganizationById" resultType="cc.crrc.manage.pojo.SysOrganizationPO">
        SELECT
            <include refid="sysOrganizationAlias"></include>
        FROM
            sys_organization
        WHERE
            id = #{id} AND
            del_flag = '0'
    </select>
    <!--根据name查询组织-->
    <select id="getOrganizationByName" resultType="cc.crrc.manage.pojo.SysOrganizationPO">
        SELECT
            <include refid="sysOrganizationAlias"></include>
        FROM
            sys_organization
        WHERE
            name = #{name} AND
            del_flag = '0'
    </select>
    <!--根据id查询组织下的子部门数量-->
    <select id="countSubOrganization" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM
            sys_organization
        WHERE
            parent_id = #{id} AND
            del_flag = '0'
    </select>
    <!--根据组织名称查询组织数量-->
    <select id="getOrganizationCountByName" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM
            sys_organization
        WHERE
            name = #{name} AND
            del_flag = '0'
    </select>
    <!--根据组织编码查询组织数量-->
    <select id="getOrganizationCountByCode" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM
            sys_organization
        WHERE
            code = #{code} AND
            del_flag = '0'
    </select>
    <insert id="addOrganizationUser">
        INSERT INTO sys_organization_user
            (user_id, organization_id)
        VALUES(#{userId}, #{organizationId});
    </insert>
    <!--新增组织-->
    <insert id="addOrganization">
        INSERT INTO sys_organization (
            id,
            parent_id,
            node_type,
            name,
            name_en,
            code,
            type,
            location_id,
            remark,
            create_time,
            create_by,
            modify_time,
            modify_by,
            del_flag
        )
        VALUES (
            #{id},
            #{parentId},
            #{nodeType},
            #{name},
            #{nameEn},
            #{code},
            #{type},
            #{locationId},
            #{remark},
            #{createTime},
            #{createBy},
            #{modifyTime},
            #{modifyBy},
            #{delFlag}
        )
    </insert>
    <!--更新组织-->
    <update id="updateOrganization">
        UPDATE sys_organization
        <trim prefix="set" suffixOverrides=",">
            <if test="parentId != null and parentId != ''">
                parent_id = #{parentId},
            </if>
            <if test="nodeType != null and nodeType != ''">
                node_type = #{nodeType},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="nameEn != null and nameEn != ''">
                name_en = #{nameEn},
            </if>
            <if test="code != null and code != '' ">
                code = #{code},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="locationId != null and locationId != ''">
                location_id = #{locationId},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>
            <if test="modifyBy != null and modifyBy != ''">
                modify_by = #{modifyBy},
            </if>
            <if test="delFlag != null and delFlag != ''">
                del_flag = #{delFlag},
            </if>
        </trim>
        WHERE
            id = #{id}
    </update>
    
    
    
    
    <!--查找当前用户的组织结构树-->
    <select id="listOrganizationsUser" resultType="cc.crrc.manage.pojo.SysOrganizationPO">
    SELECT 
	  <include refid="sysOrganizationAlias"></include>
	FROM
		sys_organization 
	WHERE
		ID IN (
			(WITH RECURSIVE T  AS (
			SELECT ID,
				parent_id 
			FROM
				sys_organization 
			WHERE
				ID IN ( SELECT organ_user.organization_id FROM sys_organization_user organ_user WHERE organ_user.user_id =#{userId} ) 
				AND del_flag = '0' UNION ALL
			SELECT
				T1.ID,
				T1.parent_id 
			FROM
				sys_organization T1
				JOIN T ON T1.parent_id = T.ID 
				AND T1.del_flag = '0' 
			) SELECT ID 
		FROM
		T)	
		UNION ALL
			(WITH RECURSIVE S AS (
			SELECT ID,
				parent_id 
			FROM
				sys_organization 
			WHERE
				ID IN ( SELECT organ_user.organization_id FROM sys_organization_user organ_user WHERE organ_user.user_id = #{userId} ) 
				AND del_flag = '0' UNION ALL
			SELECT
				T1.ID,
				T1.parent_id 
			FROM
				sys_organization T1
				JOIN S ON T1.ID = S.parent_id
				AND T1.del_flag = '0' 
			) 
			SELECT ID 
		FROM
		S
	)	
	)
         
    </select>
    
    <select id="listSelectBox"  resultType="java.lang.String">
     SELECT
	    ID 
	FROM
		sys_organization  
	WHERE
		ID IN (
			WITH RECURSIVE T AS (
			SELECT ID
				,
				parent_id 
			FROM
				sys_organization 
			WHERE
				ID IN ( SELECT organ_user.organization_id FROM sys_organization_user organ_user WHERE organ_user.user_id = #{userId} ) 
				AND del_flag = '0' UNION ALL
			SELECT
				T1.ID,
				T1.parent_id 
			FROM
				sys_organization T1
				JOIN T ON T1.parent_id = T.ID 
				AND T1.del_flag = '0' 
			) SELECT ID 
		FROM
		T	
		) 
        
    
    </select>

    <select id="listOrganizationsUserTraversalDown" resultType="cc.crrc.manage.pojo.SysOrganizationPO">
        SELECT
        <include refid="sysOrganizationAlias"></include>
        FROM
        sys_organization
        WHERE
        ID IN (
        WITH RECURSIVE T  AS (
        SELECT ID,
        parent_id
        FROM
        sys_organization
        WHERE
        ID IN ( SELECT organ_user.organization_id FROM sys_organization_user organ_user WHERE organ_user.user_id =#{userId} )
        AND del_flag = '0' UNION ALL
        SELECT
        T1.ID,
        T1.parent_id
        FROM
        sys_organization T1
        JOIN T ON T1.parent_id = T.ID
        AND T1.del_flag = '0'
        ) SELECT ID
        FROM  T
        )

    </select>

    <select id="listOrganizationsByUserAndNotId" resultType="cc.crrc.manage.pojo.SysOrganizationPO">
        SELECT
        <include refid="sysOrganizationAlias"></include>
        FROM
        sys_organization
        WHERE
        ID IN (
        (WITH RECURSIVE T  AS (
        SELECT ID,
        parent_id
        FROM
        sys_organization
        WHERE
        ID IN ( SELECT organ_user.organization_id FROM sys_organization_user organ_user WHERE organ_user.user_id =#{userId} )
        AND del_flag = '0' UNION ALL
        SELECT
        T1.ID,
        T1.parent_id
        FROM
        sys_organization T1
        JOIN T ON T1.parent_id = T.ID
        AND T1.del_flag = '0'  AND T1.id != #{id}
        ) SELECT ID
        FROM
        T)
        UNION ALL
        (WITH RECURSIVE S AS (
        SELECT ID,
        parent_id
        FROM
        sys_organization
        WHERE
        ID IN ( SELECT organ_user.organization_id FROM sys_organization_user organ_user WHERE organ_user.user_id = #{userId} )
        AND del_flag = '0' UNION ALL
        SELECT
        T1.ID,
        T1.parent_id
        FROM
        sys_organization T1
        JOIN S ON T1.ID = S.parent_id
        AND T1.del_flag = '0' AND T1.id != #{id}
        )
        SELECT ID
        FROM
        S
        )
        )
    </select>


</mapper>