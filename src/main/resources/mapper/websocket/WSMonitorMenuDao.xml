<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.websocket.dao.WSMonitorMenuDao">
    <!--初始化表字段-->
    <sql id="MonitorMenuColumnAlias">
        id,
        menu_code as menuCode,
        parent_id as parentId,
        name,
        sort,
        board_status as boardStatus,
        menu_type as menuType,
        url,
        create_time as createTime,
        create_by as createBy,
        modify_time as modifyTime,
        modify_by as modifyBy,
        show_status as showStatus,
        tra_code as traCode,
        components_url as componentsUrl,
        default_active as defaultActive,
        del_flag as delFlag
   </sql>


    <select id="findMonitorMenuByTraCode"
            resultType="cc.crrc.manage.websocket.entity.MonitorMenuEntity">
        select
        <include refid="MonitorMenuColumnAlias"/>
        from monitor_menu
        where
        del_flag = false
        and tra_code = #{traCode}
        and del_flag = false
        ORDER BY parent_id DESC
    </select>

</mapper>