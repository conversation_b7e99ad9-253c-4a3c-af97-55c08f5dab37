<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.websocket.dao.SocketMonitorMenuDao">
    <!--初始化表字段-->
    <sql id="MonitorMenuColumnAlias">
        id,
        menu_code as menuCode,
        parent_id as parentId,
        name,
        sort,
        board_status as boardStatus,
        menu_type as menuType,
        url,
        create_time as createTime,
        create_by as createBy,
        modify_time as modifyTime,
        modify_by as modifyBy,
        show_status as showStatus,
        tra_code as traCode,
        components_url as componentsUrl,
        default_active as defaultActive,
        del_flag as delFlag
   </sql>
    <!--查询有效菜单信息-->
    <select id="getMonitorMenuList" resultType="cc.crrc.manage.websocket.entity.MonitorMenuEntityPO">
        select
        <include refid="MonitorMenuColumnAlias"/>
        from monitor_menu
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="traCode != null and traCode != ''">
                and tra_code = #{traCode}
            </if>
            and del_flag = false
            order by cast(sort as int)
        </trim>
    </select>

    <!--查询父子结构形 菜单信息-->
    <select id="getMenuList" resultType="cc.crrc.manage.websocket.entity.MonitorMenuEntityPO">
        select
        <include refid="MonitorMenuColumnAlias"/>
        from monitor_menu
        <trim prefix="where" prefixOverrides="and|or">
            <if test="traCode != null">
                and tra_code = #{traCode}
            </if>
            and del_flag = false
            order by cast(sort as int)
        </trim>
    </select>
    <select id="listLabelAndLogo" resultType="cc.crrc.manage.websocket.entity.TriggerPO">
    SELECT DISTINCT
    regexp_replace( mt.label, '[0-9]', '', 'g' ) as label,
    mt.svg_url  AS svgUrl,
    mt.image_type AS imageType
    FROM
	monitor_trigger mt
	JOIN monitor_slot ms ON mt.slot_id = ms.ID
	AND ms.del_flag = FALSE JOIN monitor_table_format mf ON mf.ID = ms.table_format_id
	AND mf.del_flag = false
    WHERE
	mt.del_flag = false
	AND mf.item_id = #{itemId}
    </select>

</mapper>