<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.websocket.dao.SocketSvgMapDao">
    <sql id="svgMapAlias">
        t.id,
		t.menu_id as menuId,
		t.vehicle_type as vehicleType,
		t.key,
		t.signal_function_id as signalFunctionId,
		t.svg_code as svgCode,
		t.svg_function as svgFunction,
		t.line_id as lineId,
		t.create_time AS createTime,
		t.create_by AS createBy,
		t.modify_time as modifyTime,
		t.modify_by as modifyBy,
		t.del_flag as delFlag
    </sql>
    <sql id="MonitorPageInfoAlias">
        t.id,
		t.menu_code as menuCode,
		t.vehicle_type as vehicleType,
		t.html_content as htmlContent,
		t.css_content as cssContent,
		t.line_id as lineId,
		t.create_time AS createTime,
		t.create_by AS createBy,
		t.modify_time as modifyTime,
		t.modify_by as modifyBy,
		t.del_flag as delFlag,
        t.type
    </sql>
    <select id="getSvgMapInfoByLineId" resultType="cc.crrc.manage.websocket.entity.SvgMapEntity">
        select
        <include refid="svgMapAlias"></include>
        FROM svg_map t
        <where>
            1=1
            <if test="lineId != null and lineId != ''">
                AND t.line_id = #{lineId}
            </if>
            and
            t.del_flag = false
        </where>
    </select>


    <select id="getCssByLineId" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorPageInfoPO">
        select
        <include refid="MonitorPageInfoAlias"></include>
        FROM monitor_page_info t
        <where>
            1=1
            <if test="lineId != null and lineId != ''">
                AND t.line_id = #{lineId}
            </if>
            and
            t.del_flag = false
            and
            t.type = 'css'
        </where>
    </select>
    <select id="getHtmlByMenuCode" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorPageInfoPO">
        select
        <include refid="MonitorPageInfoAlias"></include>
        FROM monitor_page_info t
        <where>
            1=1
            <if test="menuCode != null and menuCode != ''">
                AND t.menu_code = #{menuCode}
            </if>
            and
            t.del_flag = false
            and
            t.type = 'html'
        </where>
    </select>
</mapper>