<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.websocket.dao.SignalTrendWarningDao">
    <select id="getSignalBasicInfo" resultType="cc.crrc.manage.websocket.entity.SignalTrendWarningEntity">
        SELECT stw.signal_name_en AS signalNameEn,
               stw.model_name     AS modelName,
               stw.model_rmse     AS modelRmse,
               cos.location       AS carLocation,
               cos.name_cn        AS nameCn,
               cos.unit           AS unit,
               cos.max_value      AS maxValue,
               cos.min_value      AS minValue,
               cos.id             AS id
        FROM (SELECT row_number()
                     OVER (PARTITION BY signal_name_en ORDER BY create_time DESC) AS KeyId, *
              FROM signal_trend_warning) stw
                 LEFT JOIN comm_original_signal cos
                           ON cos.name_en = stw.signal_name_en AND cos.protocol_id = stw.protocol_id
        WHERE stw.KeyId = 1
          AND stw.protocol_id = #{protocolId}
          AND stw.subsystem = #{subsystem}
    </select>

</mapper>