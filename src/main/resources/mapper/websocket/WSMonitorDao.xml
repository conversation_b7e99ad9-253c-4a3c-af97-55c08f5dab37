<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.websocket.dao.WSMonitorDao">
    <resultMap id="WebsocketVO" type="cc.crrc.manage.websocket.entity.WebsocketVO">
        <result property="id" column="menuId"></result>
        <result property="menuCode" column="menuCode"></result>
        <result property="menuType" column="menuType"></result>
        <collection property="websocketItemEntity"
                    ofType="cc.crrc.manage.websocket.entity.WebsocketItemEntity">
            <result property="id" column="itemId"></result>
            <result property="type" column="type"></result>
            <result property="relationKey" column="relationKey"></result>
            <result property="monitorSignalFunctionPO.id" column="signalFunctionId"></result>
            <result property="name" column="name"></result>
            <result property="carType" column="carType"></result>
            <result property="slotBoardId" column="slotBoardId"></result>
            <result property="monitorSignalFunctionPO.redisKey" column="functionStr"></result>
            <result property="monitorSignalFunctionPO.functionName" column="functionName"></result>
            <result property="monitorSignalFunctionPO.redisKey" column="redisKey"></result>
            <result property="monitorSignalFunctionPO.functionStr" column="functionStr"></result>

            <collection property="websocketFormatEntity"
                        ofType="cc.crrc.manage.websocket.entity.WebsocketFormatEntity">
                <result property="id" column="formatId"></result>
                <result property="vehicleLocation" column="vehicleLocation"></result>
                <result property="NRow" column="NRow"></result>
                <result property="NColumn" column="NColumn"></result>
                <collection property="websocketSlotEntity"
                            ofType="cc.crrc.manage.websocket.entity.WebsocketSlotEntity">
                    <result property="id" column="slotId"></result>
                    <result property="slotType" column="slotType"></result>
                    <collection property="websocketTriggerEntity"
                                ofType="cc.crrc.manage.websocket.entity.WebsocketTriggerEntity">
                        <result property="resultType" column="resultType"></result>
                        <result property="id" column="triggerId"></result>
                        <result property="signalId" column="signalId"></result>
                        <result property="signalNameEn" column="nameEn"></result>
                        <result property="dataDisplayPoint" column="dataDisplayPoint"></result>
                        <result property="svgUrl" column="svgUrl"></result>
                        <result property="imageType" column="imageType"></result>
                        <result property="imagePath" column="imagePath"></result>
                        <result property="label" column="label"></result>
                        <result property="triggerValue" column="triggerValue"></result>
                        <result property="extProperties" column="extProperties"></result>
                        <result property="sort" column="sort"></result>
                    </collection>
                </collection>
            </collection>
        </collection>
    </resultMap>


    <select id="getWebsocketVO" resultMap="WebsocketVO">
    SELECT
	mm.id menuId,
	mm.menu_type menuType,
	mm.menu_code menuCode,
	mi.type as type,
	mi.id itemId,
	mi.relation_key relationKey,
	mi.signal_function_id signalFunctionId,
	mi.name AS NAME,
	mi.car_type carType,
	mi.slot_board_id slotBoardId,
	msf.function_str functionStr,
	msf.function_name functionName,
	msf.redis_key redisKey,
	mf.id formatId,
	mf.vehicle_location vehicleLocation,
	mf.n_row NRow,
	mf.n_column NColumn,
	ms.id slotId,
	ms.slot_type slotType,
	mt.id triggerId,
	mt.signal_id signalId,
	mt.data_display_point dataDisplayPoint,
	mt.svg_url svgUrl,
    mt.image_type imageType,
    mt.image_path imagePath,
	mt.label AS label,
	mt.trigger_value triggerValue,
	mt.ext_properties extProperties,
	mt.sort sort,
	ns.name_en nameEn,
	ns.result_type resultType
    FROM
	monitor_menu mm
	LEFT JOIN monitor_table_item mi ON mi.menu_id = mm.id AND mi.del_flag = FALSE
	LEFT JOIN monitor_signal_function MSF ON MSF.id = MI.signal_function_id AND MSF.del_flag = FALSE
	LEFT JOIN monitor_table_format mf ON mf.item_id = mi.id AND mf.del_flag = FALSE
	LEFT JOIN monitor_slot ms ON ms.table_format_id = mf.id AND ms.del_flag = FALSE
	LEFT JOIN monitor_trigger mt ON mt.slot_id = ms.id AND mt.del_flag = FALSE
	LEFT JOIN comm_original_signal ns ON ns.name_en = mt.signal_name_en
    WHERE
	mm.del_flag = FALSE
    <if test="trainCode != null and trainCode != ''">
        AND mm.tra_code = #{trainCode}
    </if>
	order by
    length(mi.sort),
	mi.sort,
	length(mf.sort),
	mf.sort,
	ms.sort,
	mt.sort
	asc
    </select>

</mapper>