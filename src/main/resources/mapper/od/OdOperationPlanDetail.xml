<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.od.OdOperationPlanDetailMapping">
    <select id="getOperationPlanDetailList" resultType="cc.crrc.manage.pojo.od.OdOperationPlanDetailPO">
        SELECT
            t.id,
            t.operation_decision_id AS operationDecisionId,
            t.serial_number AS serialNumber,
            t.start_time AS startTime,
            t.end_time AS endTime,
            t.service_count AS serviceCount,
            t.time_interval AS timeInterval,
            t.service_vehicle_code AS serviceVehicleCode
        FROM
            od_operation_plan_detail t
        WHERE
            t.operation_decision_id = #{id}
        ORDER BY t.serial_number
    </select>
</mapper>