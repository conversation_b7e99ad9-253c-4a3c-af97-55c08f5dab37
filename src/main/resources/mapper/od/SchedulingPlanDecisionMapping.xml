<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.od.SchedulingPlanDecisionMapping">
    <sql id="schedulingPlanDecisionAlias">
        oopd.id,
        oopd.scheduling_code AS schedulingCode,
        oopd.start_time AS startTime,
        oopd.end_time AS endTime,
        oopd.commissioning_count AS commissioningCount,
        oopd.interval_time AS intervalTime,
        oopd.line_id AS lineId,
        oopd.vehicle_speed AS vehicleSpeed,
        oopd.start_switching_time AS startSwitchingTime,
        oopd.end_switching_time AS endSwitchingTime,
        oopd.ride_rate AS rideRate,
        oopd.history_ride_count AS historyRideCount,
        oopd.remark,
        oopd.valid,
        oopd.craete_by AS craeteBy,
        oopd.create_time AS createTime,
        oopd.modify_by AS modifyBy,
        oopd.modify_time AS modifyTime,
        oopd.push_status AS pushStatus
    </sql>

    <!--1、查询调度计划决策列表-->
    <select id="findSchedulingPlanPOList" resultType="cc.crrc.manage.pojo.od.SchedulingPlanDecisionPO">
        SELECT
            <include refid="schedulingPlanDecisionAlias"/>,
            ml.name AS lineName,
            ml.mileage AS lineMileage,
            concat('行车调度计划决策') AS decisionType,
            concat(oopd.start_time,'-',oopd.end_time) AS startEndTime,
            concat('列车投运数量: ',oopd.commissioning_count,'辆 ; ','发车间隔时间: ',oopd.interval_time,'分钟') AS recommendDecision
        FROM
            od_operation_plan_detail oopd
        LEFT JOIN mtr_line ml ON ml.id = oopd.line_id
        WHERE
            oopd.valid = 1
            <if test="pushStatus != null and pushStatus != ''">
                AND oopd.push_status = #{pushStatus}
            </if>
        ORDER BY
            oopd.start_time,oopd.scheduling_code
    </select>

    <!--2、保存调度计划决策信息-->
    <insert id="saveSchedulingPlanPO">
        INSERT INTO
            od_operation_plan_detail
            (
            id,scheduling_code,start_time,end_time,commissioning_count,
            interval_time,line_id,vehicle_speed,start_switching_time,end_switching_time,
            ride_rate,history_ride_count,remark,valid,craete_by,
            create_time,modify_by,modify_time
            )VALUES
            (
            #{id},#{schedulingCode},#{startTime},#{endTime},#{commissioningCount},
            #{intervalTime},#{lineId},#{vehicleSpeed},#{startSwitchingTime},#{endSwitchingTime},
            #{rideRate},#{historyRideCount},#{remark},#{valid},#{craeteBy},
            #{createTime},#{modifyBy},#{modifyTime}
            )
    </insert>

    <!--3、查询该时间段调度计划数量-->
    <select id="findCountByStartAndEnd" resultType="java.lang.Integer">
        SELECT
            COUNT(scheduling_code)
        FROM
            od_operation_plan_detail
        WHERE
            start_time = #{startTime}
        AND
            end_time = #{endTime}
    </select>

    <!--4、查询该调度计划决策编码数量-->
    <select id="findCountBySchedulingCode" resultType="java.lang.Integer">
        SELECT
            COUNT(scheduling_code)
        FROM
            od_operation_plan_detail
        WHERE
            scheduling_code = #{schedulingCode}
    </select>


    <!--5、删除多个行车调度计划决策数据-->
    <update id="deleteSchedulingPlanPO">
        UPDATE
           od_operation_plan_detail
        SET
            valid = 0
        WHERE
            valid = 1
        AND
            id IN
            <foreach collection="ids" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
    </update>

    <!--6、编辑行车调度计划决策和推送状态修改-->
    <update id="updeteSchedulingPlanPO">
        UPDATE
            od_operation_plan_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="startTime != null and startTime != ''">
                start_time = #{startTime},
            </if>
            <if test="endTime != null and endTime != ''">
                end_time = #{endTime},
            </if>
            <if test="commissioningCount != null">
                commissioning_count = #{commissioningCount},
            </if>
            <if test="intervalTime != null">
                interval_time = #{intervalTime},
            </if>
            <if test="lineId != null">
                line_id = #{lineId},
            </if>
            <if test="vehicleSpeed != null">
                vehicle_speed = #{vehicleSpeed},
            </if>
            <if test="startSwitchingTime != null">
                start_switching_time = #{startSwitchingTime},
            </if>
            <if test="endSwitchingTime != null">
                end_switching_time = #{endSwitchingTime},
            </if>
            <if test="rideRate != null">
                ride_rate = #{rideRate},
            </if>
            <if test="historyRideCount != null">
                history_ride_count = #{historyRideCount},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="modifyBy != null">
                modify_by = #{modifyBy},
            </if>
            <if test="pushStatus != null and pushStatus != ''">
                push_status = #{pushStatus},
            </if>
                modify_time = #{modifyTime},
        </trim>
        WHERE
            id = #{id}
    </update>

    <!--7、根据id查询编辑数据的原始数据-->
    <select id="findSchedulingPlanById" resultType="cc.crrc.manage.pojo.od.SchedulingPlanDecisionPO">
        SELECT
            <include refid="schedulingPlanDecisionAlias"></include>
        FROM
            od_operation_plan_detail oopd
        WHERE
            oopd.id = #{id}
    </select>

    <!--8、线路监控推送数据列表查询 推送状态为1-->
    <select id="findDecisionByLineId" resultType="cc.crrc.manage.pojo.od.SchedulingPlanDecisionPO">
        SELECT
            <include refid="schedulingPlanDecisionAlias"></include>,
            ml.name AS lineName,
            concat('列车投运数量: ',oopd.commissioning_count,'辆 ; ','发车间隔时间: ',oopd.interval_time,'分钟') AS recommendDecision
        FROM
            od_operation_plan_detail oopd
        LEFT JOIN mtr_line ml ON ml.id = oopd.line_id
        WHERE
            oopd.valid = 1
        AND
            oopd.push_status = '1'
        AND
            oopd.line_id = #{lineId}
        ORDER BY
        oopd.start_time,oopd.create_time DESC
    </select>
</mapper>