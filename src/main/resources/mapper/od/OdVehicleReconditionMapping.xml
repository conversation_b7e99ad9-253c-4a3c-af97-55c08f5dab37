<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.od.OdVehicleReconditionMapping">
        <sql id="reconditionDecisionAlias">
            ovrd.id,
            ovrd.substance,
            ovrd.scheduling_code AS schedulingCode,
            ovrd.recommend_decision AS recommendDecision,
            ovrd.status,
            ovrd.trigger_time AS triggerTime,
            ovrd.create_time AS createTime,
            ovrd.remark,
            ovrd.submit_status AS submitStatus,
            ovrd.subsystem,
            ovrd.location
        </sql>
    <!-- 查询车辆运维决策List -->
    <select id="findReconditionDecisions" resultType="cc.crrc.manage.pojo.od.OdReconditionDecisionPO">
        SELECT
            <include refid="reconditionDecisionAlias"/>
        FROM
            od_vehicle_recondition_decision ovrd
        WHERE

            1=1
        ORDER BY
            ovrd.status,ovrd.create_time DESC,ovrd.scheduling_code DESC
    </select>

    <select id="findDecisionByVehicleCode" resultType="cc.crrc.manage.pojo.od.OdReconditionDecisionPO">
        SELECT
            <include refid="reconditionDecisionAlias"/>
        FROM
            od_vehicle_recondition_decision ovrd
        WHERE
            ovrd.status = '0'
        AND
            ovrd.trigger_time &lt;= #{triggerTime}
        <if test="vehicleCodes != null">
        AND  ovrd.substance IN
            <foreach collection="vehicleCodes" separator="," close=")" open="(" item="item">
                #{item.vehicleCode}
            </foreach>
        </if>
        ORDER BY
            ovrd.create_time DESC,ovrd.trigger_time DESC
    </select>

    <!-- 对决策进行操作,更新提交状态和触发时间 -->
    <update id="actOnDecisions">
        UPDATE
            od_vehicle_recondition_decision
        SET
            <if test="triggerTime != null">
                trigger_time = #{triggerTime},
            </if>
            status = #{status}
        WHERE
            id = #{id}
    </update>

</mapper>