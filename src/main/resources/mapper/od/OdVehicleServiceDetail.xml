<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.od.OdVehicleServiceDetailMapping">
    <select id="getOdVehicleServiceDetailList" resultType="cc.crrc.manage.pojo.od.OdVehicleServiceDetailPO">
        SELECT
            t.id,
            t.operation_decision_id operationDecisionId,
            t.vehicle_id vehicleId,
            t.whether_service whetherService,
            t.priority priority,
            t.health_degree healthDegree,
            t.decision_basis decisionBasis,
            t2.vehicle_code vehicleCode
        FROM od_vehicle_service_detail t
        LEFT JOIN mtr_vehicle t2 ON t.vehicle_id = t2.id
        WHERE
            t.operation_decision_id = #{id}
    </select>
</mapper>