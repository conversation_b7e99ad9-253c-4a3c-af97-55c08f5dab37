<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.od.OdOperationDecisionMapping">
    <sql id="operationDescriptionAlias">
        ood.id,
        ood.scheduling_code AS schedulingCode,
        ood.decision_type AS decisionType,
        ood.recommend_decision AS recommendDecision,
        ood.push_time AS pushTime,
        ood.status,
        ood.decision_substance AS decisionSubstance,
        ood.subsystem,
        ood.location,
        ood.operate_time AS operateTime,
        ood.operate_by AS operateBy,
        ood.decision_id AS decisionId
    </sql>

    <!--查询运维决策列表 zhangzhijian 2020-08-17修改-->
    <select id="listOperationDecision" resultType="cc.crrc.manage.pojo.od.OdOperationDecisionPO">
        SELECT
            <include refid="operationDescriptionAlias"></include>
        FROM
            od_operation_decision ood
        <where>
            <if test="schedulingCode != null and schedulingCode != ''">
                ood.scheduling_code LIKE '%'||#{schedulingCode}||'%'
            </if>
            <if test="decisionSubstance != null and decisionSubstance != ''">
                AND ood.decision_substance LIKE '%'||#{decisionSubstance}||'%'
            </if>
            <if test="recommendDecision != null and recommendDecision != ''">
                AND ood.recommend_decision LIKE '%'||#{recommendDecision}||'%'
            </if>
            <if test="decisionType != null and decisionType != ''">
                AND ood.decision_type = #{decisionType}
            </if>
            <if test="subsystem != null and subsystem != ''">
                AND ood.subsystem = #{subsystem}
            </if>
            <if test="startPushTime != null">
                AND ood.push_time &gt;= #{startPushTime}
            </if>
            <if test="endPushTime != null">
                AND ood.push_time &lt;= #{endPushTime}
            </if>
            <if test="status != null and status != ''">
                AND ood.status = #{status}
            </if>
        </where>
        ORDER BY
            ood.status,ood.push_time,ood.operate_time DESC
    </select>

    <!--修改决策推送时间和状态 zhangzhijian 2020-08-16-->
    <update id="UpdateReconditionStatus">
        UPDATE
            od_operation_decision
        SET
        <if test="pushTime != null">
            push_time = #{pushTime},
        </if>
            operate_time = #{operateTime},
            operate_by = #{operateBy},
            status = #{status}
        WHERE
            id = #{id}
    </update>

</mapper>