<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.stru.StruVehicleComponentRecordMapping">

    <sql id="StruVehicleComponentRecordColumnAlias">
        t.id,
        t.vehicle_id as vehicleId,
        t.structure_code as structureCode,
        t.component_position as componentPosition,
        t.previous_component_code as previousComponentCode,
        t.current_component_code as currentComponentCode,
        t.executer,
        t.execute_time as executeTime,
        t.create_by as createBy,
        t.create_time AS createTime
    </sql>

    <select id="getVehicleComponentRecordForExcel"
            resultType="cc.crrc.manage.pojo.stru.StruVehicleComponentRecordExcelVO">
        SELECT
        mvt.name AS vehicleTypeName,
        mv.vehicle_code AS vehicleCode,
        t.structure_code AS structureCode,
        t.name_cn AS nameCn
        FROM stru_vehicle_structure_component t
        LEFT JOIN mtr_vehicle_type_relation mvtr ON  mvtr.vehicle_id = #{vehicleId}
        LEFT JOIN mtr_vehicle_type mvt ON mvt.id = mvtr.vehicle_type_id
        LEFT JOIN mtr_vehicle mv ON mv.id = #{vehicleId}
        WHERE
        t.del_flag = 0
        AND t.vehicle_id = #{vehicleId}
        AND mv.del_flag = 0
        AND mvtr.valid = 1
        AND mvt.del_flag = 0
        ORDER BY t.structure_code
    </select>

    <select id="getVehicleComponentRecordList"
            resultType="cc.crrc.manage.pojo.stru.StruVehicleComponentRecordVO">
        SELECT
        svcr.structure_code AS structureCode,
        svsc.name_cn AS nameCn,
        svcr.component_position AS componentPosition,
        svcr.previous_component_code AS previousComponentCode,
        svcr.current_component_code AS currentComponentCode,
        svcr.executer AS executer,
        svcr.execute_time AS executeTime
        FROM stru_vehicle_component_record svcr
        LEFT JOIN stru_vehicle_structure_component svsc
        ON (svcr.structure_code = svsc.structure_code and svcr.vehicle_id = svsc.vehicle_id)
        WHERE svsc.vehicle_id = #{vehicleId}
        AND svsc.structure_code LIKE #{structureCode}||'%'
        AND svsc.del_flag = 0
        ORDER BY svcr.execute_time DESC,svcr.structure_code
    </select>

    <insert id="batchAddRecord">
        INSERT INTO stru_vehicle_component_record(
        id,
        vehicle_id,
        structure_code,
        component_position,
        previous_component_code,
        current_component_code,
        executer,
        execute_time,
        create_by,
        create_time)
        <foreach collection="voList" item="vo" index="index" separator="union all">
            <if test="vo.currentComponentCode != null and vo.currentComponentCode != ''">
            select
            #{vo.id},
            mv.id,
            #{vo.structureCode},
            #{vo.componentPosition},
            #{vo.previousComponentCode},
            #{vo.currentComponentCode},
            #{vo.executer},
            #{vo.executeTime} :: TimeStamp,
            #{createBy},
            CURRENT_TIMESTAMP
            FROM mtr_vehicle mv
            where mv.vehicle_code = #{vo.vehicleCode}
            and mv.del_flag = 0
            </if>
        </foreach>
    </insert>

</mapper>