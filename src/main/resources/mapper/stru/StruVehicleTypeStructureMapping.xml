<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.stru.StruVehicleTypeStructureMapping">
    <sql id="StruVehicleTypeStructureColumnAlias">
        t.id,
        t.vehicle_type_id as vehicleTypeId,
        t.parent_structure_code as parentStructureCode,
        t.structure_code as structureCode,
        t.name_cn as nameCn,
        t.name_en as nameEn,
        t.short_name_en as shortNameEn,
        t.structure_position as structurePosition,
        t.structure_type as structureType,
        t.component_type_id as componentTypeId,
        t.sort_number as sortNumber,
        t.remark,
        t.fbx_file_id as fbxFileId,
        t.threed_code as threedCode
    </sql>

    <insert id="addStruVehicleTypeStructure">
        INSERT INTO stru_vehicle_type_structure
        (
            id,
            vehicle_type_id,
            parent_structure_code,
            structure_code,
            name_cn,
            name_en,
            short_name_en,
            structure_position,
            structure_type,
            component_type_id,
            sort_number,
            del_flag,
            create_by,
            create_time,
            modify_by,
            modify_time,
            remark
        )
        VALUES(
            #{id},
            #{vehicleTypeId},
            #{parentStructureCode},
            #{structureCode},
            #{nameCn},
            #{nameEn},
            #{shortNameEn},
            #{structurePosition},
            #{structureType},
            #{componentTypeId},
            #{sortNumber},
            0,
            #{createBy},
            CURRENT_TIMESTAMP,
            #{modifyBy},
            CURRENT_TIMESTAMP,
            #{remark}
        )
    </insert>

    <select id="getStruVehicleTypeStructureById" resultType="cc.crrc.manage.pojo.stru.StruVehicleTypeStructurePO">
        SELECT
            <include refid="StruVehicleTypeStructureColumnAlias"/>,
            sct.name_cn as componentTypeName
        FROM
            stru_vehicle_type_structure t
        LEFT JOIN stru_component_type sct ON sct.id = t.component_type_id
        WHERE
            t.id = #{id} AND t.del_flag = '0'
    </select>

    <select id="getStructureByStructureCode" resultType="cc.crrc.manage.pojo.stru.StruVehicleTypeStructurePO">
        SELECT
            <include refid="StruVehicleTypeStructureColumnAlias"/>
        FROM
            stru_vehicle_type_structure t
        WHERE
            t.structure_code = #{structureCode}
            AND t.vehicle_type_id = #{vehicleTypeId}
            AND t.del_flag = '0'
    </select>

    <!--根据id查询子节点-->
    <select id="getSubStructureById" resultType="cc.crrc.manage.pojo.stru.StruVehicleTypeStructurePO">
        SELECT
            <include refid="StruVehicleTypeStructureColumnAlias"/>
        FROM
            stru_vehicle_type_structure t
        WHERE
            t.del_flag = '0' AND
            t.parent_structure_code = (
                SELECT structure_code FROM stru_vehicle_type_structure WHERE id = #{id} AND del_flag = '0'
            )
    </select>

    <select id="getVehicleTypeRootStructureCode" resultType="java.util.HashMap">
        SELECT
            t.structure_code,
            sf.url as fbxUrl,
            t.threed_code as "threedCode"
        FROM
            stru_vehicle_type_structure t
            LEFT JOIN sys_file sf ON sf.ID = T.fbx_file_id
        WHERE
            t.del_flag = '0'
            AND t.parent_structure_code = 'root'
            AND t.vehicle_type_id = #{vehicleTypeId}

    </select>

    <update id="deleteStruVehicleTypeStructure">
        UPDATE stru_vehicle_type_structure
        SET del_flag = 1,
            modify_by = #{modifyBy},
            modify_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <update id="updateStruVehicleTypeStructure">
         UPDATE stru_vehicle_type_structure
        <set>
            <if test="vehicleTypeId != null and vehicleTypeId != ''">
                vehicle_type_id = #{vehicleTypeId},
            </if>
            <if test="parentStructureCode != null and parentStructureCode != ''">
                parent_structure_code = #{parentStructureCode},
            </if>
            <if test="structureCode != null and structureCode != ''">
                structure_code = #{structureCode},
            </if>
            <if test="nameCn != null and nameCn != ''">
                name_cn = #{nameCn},
            </if>
            <if test="nameEn != null and nameEn != ''">
                name_en = #{nameEn},
            </if>
            <if test="shortNameEn != null and shortNameEn != ''">
                short_name_en = #{shortNameEn},
            </if>
            <if test="structureType != null and structureType != ''">
                structure_type = #{structureType},
            </if>
            <if test="componentTypeId != null and componentTypeId !=''">
                component_type_id = #{componentTypeId},
            </if>
            <if test="sortNumber != null">
                sort_number = #{sortNumber},
            </if>
            <if test="modifyBy != null and modifyBy !=''">
                modify_by = #{modifyBy},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            modify_time = CURRENT_TIMESTAMP
        </set>
        WHERE
            id = #{id} AND del_flag = '0'
    </update>

    <select id="selectStructureTreeList" resultType="cc.crrc.manage.pojo.stru.StruVehicleTypeStructureTreeVO">
        SELECT
        <include refid="StruVehicleTypeStructureColumnAlias"/>,
        mvt.name AS vehicleTypeName,
        sy.url AS fbxUrl,
        CONCAT(t.vehicle_type_id, '-', t.id) AS uniqueFlag
        FROM
        stru_vehicle_type_structure t
        LEFT JOIN
        mtr_vehicle_type mvt ON mvt.id = t.vehicle_type_id
        LEFT JOIN
        sys_file sy on sy.id = t.fbx_file_id
        WHERE
        t.del_flag = 0
        <if test="vehicleTypeId != null and vehicleTypeId !=''">
            AND t.vehicle_type_id = #{vehicleTypeId}
        </if>
    </select>

    <!--查询符合条件的节点唯一标识，，前端用作展开构型树-->
    <select id="listUniqueFlag" resultType="java.lang.String">
        SELECT
            CONCAT(#{vehicleTypeId}::VARCHAR, '-', id)
        FROM stru_vehicle_type_structure
        WHERE structure_code in (
            SELECT
                t.parent_structure_code
            FROM
            stru_vehicle_type_structure t
            WHERE
            t.del_flag = 0
            AND t.parent_structure_code NOT IN ('root','-1')
                    AND t.vehicle_type_id = #{vehicleTypeId}
                <if test="nameCn != null and nameCn != ''">
                    AND t.name_cn ~ #{nameCn}
                </if>
        )
            AND vehicle_type_id = #{vehicleTypeId}
    </select>

    <!--更新子节点的parentStructureCode-->
    <update id="updateChildrenStructureCode">
        UPDATE stru_vehicle_type_structure
        SET parent_structure_code = #{structureCodeNew}
        WHERE
            vehicle_type_id = #{vehicleTypeId} AND
            parent_structure_code = #{structureCodeOrigin} AND
            del_flag = '0'
    </update>

    <!--根据id取得父节点-->
    <select id="getParentNodeById" resultType="cc.crrc.manage.pojo.stru.StruVehicleTypeStructurePO">
        SELECT
        <include refid="StruVehicleTypeStructureColumnAlias"/>
        FROM stru_vehicle_type_structure t
        WHERE
        t.structure_code = (
            SELECT parent_structure_code FROM stru_vehicle_type_structure WHERE id = #{id}
        )
        AND t.del_flag = '0'
    </select>

    <select id="getLocationByVehicleTypeId" resultType="cc.crrc.manage.pojo.stru.StruVehicleTypeStructurePO">
        SELECT
        t.id,
        t.vehicle_type_id as vehicleTypeId,
        t.structure_code as structureCode,
        t.name_cn as nameCn,
        split_part(t.structure_code,'/',2) AS nameEn
        /*2020-3-24 修改 因为不确定nameEn的格式 但是structure_code不会变所以修改成截取structure_code的第二位 -by lixin*/
--         SUBSTR(t.name_en, 1, LENGTH(t.name_en)-4) as nameEn
        FROM stru_vehicle_type_structure t
        WHERE
        t.vehicle_type_id =  #{vehicleTypeId}
        AND t.structure_type = '车厢'
        AND t.del_flag = '0'
    </select>

    <select id="getAllSystemByVehicleTypeId" resultType = "java.lang.String">
        SELECT
            DISTINCT trim(t.name_cn) AS nameCn
        FROM stru_vehicle_type_structure t
        WHERE
            t.vehicle_type_id =  #{vehicleTypeId}
          AND t.structure_type = '系统'
          AND t.del_flag = '0'
    </select>

    <select id="getVehicleTypeStructureCode" resultType="cc.crrc.manage.pojo.mtr.StruVehicleStructureComponentPO">
        SELECT
        #{vehicleId}::VARCHAR AS vehicleId,
        t1.parent_structure_code AS parentStructureCode,
        t1.structure_code as structureCode,
        t1.name_en AS nameEn,
        t1.name_cn AS nameCn,
        t1.short_name_en AS shortNameEn,
        t1.structure_position AS structurePosition,
        t1.structure_type AS structureType,
        t1.component_type_id AS componentTypeId,
        t1.sort_number AS sortNumber,
        #{currentId}::VARCHAR AS createBy,
        #{currentId}::VARCHAR AS modifyBy,
        t1.remark
        from
        stru_vehicle_type_structure t1
        where
        t1.vehicle_type_id=#{vehicleTypeId}
        and
        t1.del_flag = 0
    </select>

    <select id="getDiffStructureListByStructureCode" resultType="java.lang.String">
        SELECT
        t.structure_code
        FROM
        stru_vehicle_type_structure t
        LEFT JOIN
        mtr_vehicle_type_relation r
        ON
        r.vehicle_type_id = t.vehicle_type_id
        <where>
            <if test="vehicleId != null and vehicleId !=''">
                AND r.vehicle_id = #{vehicleId}
            </if>
            <if test="structureCode != null and structureCode != ''">
                AND t.structure_code LIKE #{structureCode}||'%'
            </if>
        </where>
        EXCEPT
        SELECT
        c.structure_code
        FROM
        stru_vehicle_structure_component c
        <where>
            c.del_flag = 0
            <if test="vehicleId != null and vehicleId != ''">
                AND c.vehicle_id = #{vehicleId}
            </if>
            <if test="structureCode != null and structureCode != ''">
                AND c.structure_code LIKE #{structureCode}||'%'
            </if>
        </where>
    </select>

    <select id="getStructureTreeByStructureCode" resultType="cc.crrc.manage.pojo.stru.StruVehicleTypeStructureTreeVO">
        SELECT
        <include refid="StruVehicleTypeStructureColumnAlias"/>,
        CONCAT(t.vehicle_type_id, '-', t.id) AS uniqueFlag
        FROM
        stru_vehicle_type_structure t
        LEFT JOIN
        mtr_vehicle_type_relation mvtr
        ON
        mvtr.vehicle_type_id = t.vehicle_type_id
        WHERE
        t.structure_code = #{structureCode}
        AND mvtr.vehicle_id = #{vehicleId}
    </select>

    <select id="getAllowStructureTree" resultType="cc.crrc.manage.pojo.stru.StruVehicleTypeStructureTreeVO">
        SELECT
        <include refid="StruVehicleTypeStructureColumnAlias"/>,
        CONCAT(t.vehicle_type_id, '-', t.id) AS uniqueFlag
        FROM
        stru_vehicle_type_structure t
        LEFT JOIN
        mtr_vehicle_type_relation mvtr
        ON
        mvtr.vehicle_type_id = t.vehicle_type_id
        WHERE
        t.structure_code IN
        <foreach collection="structureCodeList" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        AND mvtr.vehicle_id = #{vehicleId}
    </select>

    <select id="getStruVehicleStructureById" resultType="cc.crrc.manage.pojo.mtr.StruVehicleStructureComponentPO">
        SELECT
            #{vehicleId}::VARCHAR AS vehicleId,
            t1.parent_structure_code AS parentStructureCode,
            t1.structure_code as structureCode,
            t1.name_en AS nameEn,
            t1.name_cn AS nameCn,
            t1.short_name_en AS shortNameEn,
            t1.structure_position AS structurePosition,
            t1.structure_type AS structureType,
            t1.component_type_id AS componentTypeId,
            t1.sort_number AS sortNumber,
            #{currentId}::VARCHAR AS createBy,
            #{currentId}::VARCHAR AS modifyBy,
            t1.remark
        from
            stru_vehicle_type_structure t1
        where
            t1.id=#{id}
          and
            t1.del_flag = 0
    </select>

    <insert id="addStruVehicleTypeStructureList">
        insert into stru_vehicle_type_structure
        (
        id,
        vehicle_type_id,
        parent_structure_code,
        structure_code,
        name_cn,
        name_en,
        short_name_en,
        structure_position,
        structure_type,
        component_type_id,
        sort_number,
        remark
        )
        values
        <foreach collection="list" item="item" index= "index" separator =",">
            (
            #{item.id},
            #{item.vehicleTypeId},
            #{item.parentStructureCode},
            #{item.structureCode},
            #{item.nameCn},
            #{item.nameEn},
            #{item.shortNameEn},
            #{item.structurePosition},
            #{item.structureType},
            #{item.componentTypeId},
            #{item.sortNumber},
            #{item.remark}
            )
        </foreach>
    </insert>

    <select id="getStruVehicleTypeStructureForExcel"
            resultType="cc.crrc.manage.pojo.excel.StruVehicleTypeStructureForExcelPO">
        SELECT
        <include refid="StruVehicleTypeStructureColumnAlias"/>
        FROM stru_vehicle_type_structure t
        WHERE
        t.vehicle_type_id =#{vehicleTypeId}
        <if test="structureCode != null and structureCode != ''">
            AND t.structure_code LIKE #{structureCode}||'%'
        </if>
        AND t.del_flag = '0'
        ORDER BY t.structure_code
    </select>
    <select id="getLocationByVehicleCode" resultType="java.lang.String">
        SELECT
        split_part(t1.structure_code,'/',2) AS location
        /*2020-4-28 修改 因为不确定nameEn的格式 但是structure_code不会变所以修改成截取structure_code的第二位 -by lixin*/
        FROM stru_vehicle_structure_component t1
        LEFT JOIN mtr_vehicle t2
        ON t1.vehicle_id = t2.id
        WHERE
        t2.vehicle_code =  #{vehicleCode}
        AND t1.structure_type = '车厢'
        AND t1.del_flag = '0'
		ORDER BY t1.sort_number
    </select>
    <select id="getParentNodeByStructureCode" resultType="cc.crrc.manage.pojo.excel.StruVehicleTypeStructureForExcelPO">
        SELECT
        <include refid="StruVehicleTypeStructureColumnAlias"/>
        FROM stru_vehicle_type_structure t
        WHERE
        t.vehicle_type_id =#{vehicleTypeId}
        AND t.structure_code = #{structureCode}
        AND t.del_flag = '0'
    </select>

    <update id="saveVehicleTypeStructureFileRelation">
        UPDATE stru_vehicle_type_structure
        SET fbx_file_id = #{fbxFileId}
        WHERE id = #{id}
        and del_flag = 0
        and fbx_file_id is null
    </update>

    <update id="deleteVehicleTypeStructureFileRelation">
        UPDATE stru_vehicle_type_structure
        SET fbx_file_id = null
        WHERE id = #{id}
        and del_flag = 0
        and fbx_file_id is not null
    </update>

    <update id="updateVehicleTypeStructureFileRelation">
        UPDATE stru_vehicle_type_structure
        SET fbx_file_id = #{fbxFileId}
        WHERE id = #{id}
        and del_flag = 0
        and fbx_file_id is not null
    </update>
</mapper>