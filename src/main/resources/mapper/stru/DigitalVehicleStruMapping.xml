<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.stru.DigitalVehicleStruMapping">
    <sql id="DigitalVehicleStruColumnAlias">
        t
        .
        id
        ,
        t.vehicle_type_id as vehicleTypeId,
        t.parent_structure_code as parentStructureCode,
        t.structure_code as structureCode,
        t.name_cn as nameCn,
        t.name_en as nameEn,
        t.short_name_en as shortNameEn,
        t.structure_position as structurePosition,
        t.structure_type as structureType,
        t.component_type_id as componentTypeId,
        t.sort_number as sortNumber,
        t.remark,
        t.fbx_file_id as fbxFileId
    </sql>
    <select id="mechanismAlarmRuleListByStruCode" resultType="cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRuleVO">
        SELECT
        rule.id AS id,
        rule.vehicle_type_id AS vehicleTypeId,
        mvt.name AS vehicleTypeName,
        rule.fault_type_key AS faultTypeKey,
        fault.location AS location,
        fault.name_cn ||'('||fault.name_en||')' AS faultName,
        fault.subsystem AS subsystem,
        sd.label AS subSystemName,
        rule.name AS name,
        rule.enable AS enable,
        rule.description AS description,
        file.group||'/'||file.file_location AS imageUrl,
        rule.line_id AS lineId
        FROM
        mtc_mechanism_alarm_rule rule
        LEFT JOIN sys_file file ON rule.image_file = file.id
        LEFT JOIN ekb_fault_type fault ON rule.fault_type_key = fault.fault_type_key
        LEFT JOIN mtr_vehicle_type mvt ON mvt.id = rule.vehicle_type_id
        LEFT JOIN mtr_vehicle mv on mv.metro_line_id = mvt.line_id
        LEFT JOIN sys_dict sd ON sd.type_code = 'ass_car_system' and sd.value = fault.subsystem and sd.del_flag = '0'
        WHERE
        rule.line_id = sd.line_id
        AND fault.del_flag = 0
        AND mvt.del_flag = 0
        AND file.del_flag = 0
        AND rule.vehicle_type_id = sd.vehicle_type_id
        <if test="vehicleCode != null and vehicleCode !=''">
            AND mv.vehicle_code = #{vehicleCode}
        </if>
        <if test="structureCode != null and structureCode != ''">
            AND fault.vehicle_structure_code LIKE '%'||#{structureCode}||'%'
        </if>

        ORDER BY
        rule.create_time desc
    </select>


    <select id="listEkbFaultType" resultType="cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO">
        SELECT
        t.id,
        t.line_id as lineId,
        t.vehicle_type_id as vehicleTypeId,
        t.fault_type_key as faultTypeKey,
        t.vehicle_structure_code as vehicleStructureCode,
        t.name_cn AS nameCn,
        t.name_cn ||'('||t.name_en||')' AS nameCnE,
        t.name_en as nameEn,
        t.fault_code as faultCode,
        t.fault_level as faultLevel,
        t.subsystem,
        t.description,
        t.location,
        t.frontline_disposal_recommendations as frontlineDisposalRecommendations,
        t.overhaul_suggestions as overhaulSuggestions,
        t.fault_reason as faultReason,
        t.model_code AS modelCode,
        t.enable,
        mvt.name as vehicleType,
        sd.label as assCarSystem,
        svts.name_cn AS vehicleStructureName
        FROM ekb_fault_type t
        LEFT JOIN mtr_vehicle_type mvt ON t.vehicle_type_id = mvt.id
        LEFT JOIN sys_dict sd ON sd.line_id = t.line_id and sd.vehicle_type_id= t.vehicle_type_id and sd.type_code
        ='ass_car_system' and t.subsystem = sd.value
        LEFT JOIN stru_vehicle_type_structure svts ON t.vehicle_structure_code = svts.structure_code AND
        svts.vehicle_type_id = t.vehicle_type_id
        LEFT JOIN mtr_vehicle mv on mv.metro_line_id = mvt.line_id
        WHERE
        t.del_flag = '0' AND
        mvt.del_flag = '0'
        <if test="structureCode != null and structureCode != ''">
            AND t.vehicle_structure_code LIKE '%'||#{structureCode}||'%'
        </if>
        <if test="vehicleCode != null and vehicleCode !=''">
            AND mv.vehicle_code = #{vehicleCode}
        </if>
        AND sd.del_flag!='1'
        ORDER BY
        convert_to( sd.label, 'gbk' ),
        CASE t.fault_level WHEN '3' THEN 1 WHEN '2' THEN 2 WHEN '1' THEN 3 WHEN '0' THEN 4 ELSE 5
        END ASC,
        convert_to( t.name_cn, 'gbk' )

    </select>
    <select id="struCodeInfoByThreedCode" resultType="java.lang.String">
        SELECT t1.structure_code
        FROM stru_vehicle_type_structure t1
                 LEFT JOIN mtr_vehicle_type mvt ON mvt.id = t1.vehicle_type_id
                 LEFT JOIN mtr_vehicle mv on mv.metro_line_id = mvt.line_id
        WHERE t1.threed_code = #{threedCode}
          AND mv.vehicle_code = #{vehicleCode}
    </select>
    <select id="softWareInfoListForStru" resultType="cc.crrc.manage.pojo.mtr.MtrSoftWareVO">
        SELECT
        dad.id,
        dad.name,
        dad.version,
        dad.description,
        dad.create_by AS createBy,
        dad.create_time AS createTime,
        dad.modify_by AS modifyBy,
        dad.modify_time AS modifyTime,
        msm.structure_code AS vehicleStructureCode,
        dad.vehicle_code AS vehicleCode,
        dad.effective_time AS effectiveTime,
        dad.end_time AS endTime,
        dad.signal_name_en AS signalNameEn,
        dad.operator,
        dad.line_id AS lineId,
        ml.name AS lineName,
        mvtr.vehicle_type_id AS vehicleTypeId,
        mvt.name AS vehicleTypeName,
        son.id AS sonId,
        son.name AS sonName,
        son.version AS sonVersion,
        son.description AS sonDescription,
        son.create_by AS sonCreateBy,
        son.create_time AS sonCreateTime,
        son.modify_by AS sonModifyBy,
        son.modify_time AS sonModifyTime,
        (CASE WHEN son.end_time is not null THEN msm.structure_code ELSE null END) AS sonVehicleStructureCode,
        son.vehicle_code AS sonVehicleCode,
        son.effective_time AS sonEffectiveTime,
        son.end_time AS sonEndTime,
        son.signal_name_en AS sonSignalNameEn,
        son.operator AS sonOperator,
        son.line_id AS sonLineId
        FROM mtr_software dad
        LEFT JOIN mtr_software son ON dad.signal_name_en = son.signal_name_en AND dad.vehicle_code = son.vehicle_code
        AND son.end_time is not null
        LEFT JOIN mtr_software_mapping msm ON dad.signal_name_en = msm.signal_name_en
        LEFT JOIN mtr_vehicle mv ON mv.vehicle_code = dad.vehicle_code
        LEFT JOIN mtr_vehicle_type_relation mvtr ON mv.id = mvtr.vehicle_id
        LEFT JOIN mtr_vehicle_type mvt ON mvtr.vehicle_type_id = mvt.id AND msm.vehicle_type_id = mvt.id
        LEFT JOIN mtr_line ml ON ml.id = mv.metro_line_id
        WHERE
        mv.del_flag = 0
        AND mvt.del_flag = 0
        AND ml.del_flag = 0
        AND msm.del_flag = 0
        AND dad.end_time is null
        <if test="vehicleCode != null and vehicleCode != ''">
            AND dad.vehicle_code = #{vehicleCode}
        </if>
        <if test="structureCode != null and structureCode != ''">
            AND msm.structure_code LIKE '%'||#{structureCode}||'%'
        </if>
        ORDER BY dad.vehicle_code ASC, dad.name ASC, dad.signal_name_en ASC, dad.effective_time DESC, son.end_time DESC
    </select>
    <select id="relayContactorLifeListByStruCode" resultType="cc.crrc.manage.pojo.monitor.RelayContactorPO">
        SELECT
        CONCAT (round( rcl.operation_cnt::numeric/rc.electrical_life*100)) AS usePercent,
        rcl.id AS id,
        rcl.relay_contactor_id AS relayContactorId,
        rcl.operation_cnt AS operationCnt,
        rcl.replace_times AS replaceTimes,
        rcl.vehicle_code AS vehicleCode,
        rcl.assembly_time AS assemblyTime,
        rcl.assembly_location AS assemblyLocation,
        rc.name_cn AS nameCn,
        rc.type AS type,
        sd.label AS typeCn,
        sd.line_id AS lineId,
        rc.create_by AS createBy,
        rc.create_time AS createTime,
        rc.modify_by AS modifyBy,
        rc.modify_time AS modifyTime,
        rc.electrical_life AS electricalLife,
        rc.operation_avg AS operationAvg,
        rc.structure_code AS structureCode,
        rc.unit
        FROM
        relay_contactor_life rcl
        LEFT JOIN
        relay_contactor rc ON rcl.relay_contactor_id = rc.id
        LEFT JOIN mtr_vehicle mr ON mr.vehicle_code = rcl.vehicle_code
        LEFT JOIN sys_dict sd ON sd.value = rc.type and mr.metro_line_id =sd.line_id
        WHERE
        rcl.valid = 1
        AND rc.component_kind = 0
        AND rc.del_flag = '0'
        AND sd.del_flag = '0'
        <if test="vehicleCode != null and vehicleCode != ''">
            AND rcl.vehicle_code = #{vehicleCode}
        </if>
        <if test="structureCode != null and structureCode != ''">
            AND rc.structure_code like'%'||#{structureCode}||'%'
        </if>

        ORDER BY
        ( rcl.operation_cnt :: NUMERIC / rc.electrical_life * 100 ) DESC,
        rcl.operation_cnt DESC,
        COALESCE ( rcl.replace_times, 0 ) DESC,
        convert_to( rc.name_cn, 'gbk' )

    </select>
    <select id="mechanismAlarmRuleListByVehicleCode"
            resultType="cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRuleVO">
        SELECT
        rule.id AS id,
        svts.threed_code AS threedCode,
        rule.vehicle_type_id AS vehicleTypeId,
        mvt.name AS vehicleTypeName,
        rule.fault_type_key AS faultTypeKey,
        fault.location AS location,
        fault.name_cn ||'('||fault.name_en||')' AS faultName,
        fault.subsystem AS subsystem,
        sd.label AS subSystemName,
        rule.name AS name,
        rule.enable AS enable,
        rule.description AS description,
        file.group||'/'||file.file_location AS imageUrl,
        rule.line_id AS lineId
        FROM
        mtc_mechanism_alarm_rule rule
        LEFT JOIN sys_file file ON rule.image_file = file.id
        LEFT JOIN ekb_fault_type fault ON rule.fault_type_key = fault.fault_type_key
        LEFT JOIN stru_vehicle_type_structure svts on svts.structure_code = fault.vehicle_structure_code
        LEFT JOIN mtr_vehicle_type mvt ON mvt.id = rule.vehicle_type_id
        LEFT JOIN mtr_vehicle mv on mv.metro_line_id = mvt.line_id
        LEFT JOIN sys_dict sd ON sd.type_code = 'ass_car_system' and sd.value = fault.subsystem and sd.del_flag = '0'
        WHERE
        rule.line_id = sd.line_id
        AND fault.del_flag = 0
        AND mvt.del_flag = 0
        AND file.del_flag = 0
        AND rule.vehicle_type_id = sd.vehicle_type_id
        <if test="vehicleCode != null and vehicleCode !=''">
            AND mv.vehicle_code = #{vehicleCode}
        </if>
        ORDER BY
        svts.threed_code,
        rule.create_time desc
    </select>
    <select id="componentListByStruCode" resultType="cc.crrc.manage.pojo.monitor.RelayContactorPO">
        SELECT
        CONCAT (round( rcl.operation_cnt::numeric/rc.electrical_life*100)) AS usePercent,
        rcl.id AS id,
        rcl.relay_contactor_id AS relayContactorId,
        rcl.operation_cnt AS operationCnt,
        rcl.replace_times AS replaceTimes,
        rcl.vehicle_code AS vehicleCode,
        rcl.assembly_time AS assemblyTime,
        rcl.assembly_location AS assemblyLocation,
        rc.name_cn AS nameCn,
        rc.type AS type,
        sd.label AS typeCn,
        sd.line_id AS lineId,
        rc.create_by AS createBy,
        rc.create_time AS createTime,
        rc.modify_by AS modifyBy,
        rc.modify_time AS modifyTime,
        rc.electrical_life AS electricalLife,
        rc.operation_avg AS operationAvg,
        rc.structure_code AS structureCode,
        rc.unit,
        rc.structure_code AS structureCode,
        rc.component_kind AS componentKind,
        rc.name_en AS nameEn,
        rcl.product_number AS productNumber,
        rcl.manufacturer_id AS manufacturerId,
        rcl.installer,
        mm.name AS manufacturerName
        FROM
        relay_contactor_life rcl
        LEFT JOIN
        relay_contactor rc ON rcl.relay_contactor_id = rc.id
        LEFT JOIN mtr_vehicle mr ON mr.vehicle_code = rcl.vehicle_code
        LEFT JOIN sys_dict sd ON sd.value = rc.type and mr.metro_line_id =sd.line_id
        LEFT JOIN mtr_manufacturer mm ON mm.id = rcl.manufacturer_id
        WHERE
        rcl.valid = 1
        AND rc.del_flag = '0'
        AND sd.del_flag = '0'
        <if test="vehicleCode != null and vehicleCode != ''">
            AND rcl.vehicle_code = #{vehicleCode}
        </if>
        <if test="structureCode != null and structureCode != ''">
            AND rc.structure_code like'%'||#{structureCode}||'%'
        </if>
        ORDER BY
        ( rcl.operation_cnt :: NUMERIC / rc.electrical_life * 100 ) DESC,
        rcl.operation_cnt DESC,
        COALESCE ( rcl.replace_times, 0 ) DESC,
        convert_to( rc.name_cn, 'gbk' )
    </select>
    <select id="listMtcAutoFaultRecordForStru" resultType="cc.crrc.manage.pojo.mtc.MtcAutoFaultRecordVO">
        SELECT
        b.startTime,
        b.endTime,
        b.faultNameCn,
        b.faultCode,
        b.faultLevel,
        b.faultTypeKey,
        b.SOURCE,
        b.realCounter AS realCounter
        FROM
        (
        SELECT
        A.startTime AS startTime,
        A.endTime AS endTime,
        A.faultNameCn,
        A.faultCode,
        A.faultTypeKey,
        A.faultLevel,
        A.SOURCE,
        "count" ( 1 ) OVER ( PARTITION BY A.faultTypeKey ) AS realCounter,
        "row_number" ( ) OVER ( PARTITION BY A.faultTypeKey ORDER BY startTime desc ) AS rownum
        FROM
        (
        SELECT
        t1.start_time AS startTime,
        t1.end_time AS endTime,
        t2.name_cn AS faultNameCn,
        t2.fault_code AS faultCode,
        t2.fault_type_key AS faultTypeKey,
        t2.fault_level AS faultLevel,
        case t1.fault_source when '0' then '自动上报' when '1' then '机理故障' end source
        FROM
        mtc_alarm_warning t1,
        ekb_fault_type t2
        WHERE
        t1.fault_type_key = t2.fault_type_key
        AND t2.vehicle_structure_code LIKE '%'||#{structureCode}||'%'
        AND t1.vehicle_id = #{vehicleId}
        <if test="startTime != null">
            AND t1.start_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="startTimeEnd != null">
            AND t1.start_time <![CDATA[<=]]> #{startTimeEnd}
        </if>
        AND t2.del_flag = 0
        ) A
        ) b
        WHERE
        b.rownum = 1
        ORDER BY
        b.startTime DESC
    </select>
    <select id="listMtcAutoFaultRecordForStruByFaulitTypeKey" resultType="cc.crrc.manage.pojo.mtc.MtcAutoFaultRecordVO">
        SELECT
        t1.start_time AS startTime,
        t1.end_time AS endTime,
        t2.name_cn AS faultNameCn,
        t2.fault_code AS faultCode,
        t2.fault_type_key AS faultTypeKey,
        t2.fault_level AS faultLevel,
        case t1.fault_source when '0' then '自动上报' when '1' then '机理故障' end source
        FROM
        mtc_alarm_warning t1,
        ekb_fault_type t2
        WHERE
        t1.fault_type_key = t2.fault_type_key
        AND t2.vehicle_structure_code LIKE '%'||#{structureCode}||'%'
        AND t1.vehicle_id = #{vehicleId}
        AND t2.fault_type_key = #{faultTypeKey}
        <if test="startTime != null">
            AND t1.start_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="startTimeEnd != null">
            AND t1.start_time <![CDATA[<=]]> #{startTimeEnd}
        </if>
        AND t2.del_flag = 0
        ORDER BY t1.start_time DESC
    </select>
    <select id="mtrSoftWareListByStruCodeLite" resultType="cc.crrc.manage.pojo.mtr.MtrSoftWareVO">
        SELECT ms.id,
               ms.name,
               ms.version,
               ms.description,
               ms.create_by         AS createBy,
               ms.create_time       AS createTime,
               ms.modify_by         AS modifyBy,
               ms.modify_time       AS modifyTime,
               msm.structure_code   AS vehicleStructureCode,
               ms.vehicle_code      AS vehicleCode,
               ms.effective_time    AS effectiveTime,
               ms.end_time          AS endTime,
               ms.signal_name_en    AS signalNameEn,
               ms.operator,
               ms.line_id           AS lineId,
               ml.name              AS lineName,
               mvtr.vehicle_type_id AS vehicleTypeId,
               mvt.name             AS vehicleTypeName
        FROM mtr_software ms
                 LEFT JOIN mtr_software_mapping msm ON ms.signal_name_en = msm.signal_name_en
                 LEFT JOIN mtr_vehicle mv ON mv.vehicle_code = ms.vehicle_code
                 LEFT JOIN mtr_vehicle_type_relation mvtr ON mv.id = mvtr.vehicle_id
                 LEFT JOIN mtr_vehicle_type mvt ON mvtr.vehicle_type_id = mvt.id AND msm.vehicle_type_id = mvt.id
                 LEFT JOIN mtr_line ml ON ml.id = mv.metro_line_id
        WHERE mv.del_flag = 0
          AND mvt.del_flag = 0
          AND ml.del_flag = 0
          AND msm.del_flag = 0
          AND ms.end_time is null
          AND ms.vehicle_code = #{vehicleCode}
          AND msm.structure_code LIKE '%' || #{structureCode} || '%'
        ORDER BY ms.vehicle_code ASC, ms.name ASC, ms.signal_name_en ASC, ms.effective_time DESC
    </select>
    <select id="mtrSoftWareListByStruCodeAndSignalName" resultType="cc.crrc.manage.pojo.mtr.MtrSoftWareVO">
        SELECT ms.id,
               ms.name,
               ms.version,
               ms.description,
               ms.create_by         AS createBy,
               ms.create_time       AS createTime,
               ms.modify_by         AS modifyBy,
               ms.modify_time       AS modifyTime,
               msm.structure_code   AS vehicleStructureCode,
               ms.vehicle_code      AS vehicleCode,
               ms.effective_time    AS effectiveTime,
               ms.end_time          AS endTime,
               ms.signal_name_en    AS signalNameEn,
               ms.operator,
               ms.line_id           AS lineId,
               ml.name              AS lineName,
               mvtr.vehicle_type_id AS vehicleTypeId,
               mvt.name             AS vehicleTypeName
        FROM mtr_software ms
                 LEFT JOIN mtr_software_mapping msm ON ms.signal_name_en = msm.signal_name_en
                 LEFT JOIN mtr_vehicle mv ON mv.vehicle_code = ms.vehicle_code
                 LEFT JOIN mtr_vehicle_type_relation mvtr ON mv.id = mvtr.vehicle_id
                 LEFT JOIN mtr_vehicle_type mvt ON mvtr.vehicle_type_id = mvt.id AND msm.vehicle_type_id = mvt.id
                 LEFT JOIN mtr_line ml ON ml.id = mv.metro_line_id
        WHERE mv.del_flag = 0
          AND mvt.del_flag = 0
          AND ml.del_flag = 0
          AND msm.del_flag = 0
          AND ms.end_time is null
          AND ms.vehicle_code = #{vehicleCode}
          AND msm.structure_code LIKE '%' || #{structureCode} || '%'
          AND ms.signal_name_en = #{signalNameEn}
        ORDER BY ms.vehicle_code ASC, ms.name ASC, ms.signal_name_en ASC, ms.effective_time DESC
    </select>
    <select id="autoFaultGroupListByVehicleCodeAndThreedCode" resultType="cc.crrc.manage.pojo.mtc.MtcAutoFaultRecordVO">
        SELECT
        t1.start_time AS startTime,
        t1.end_time AS endTime,
        t3.threed_code AS threedCode,
        t2.name_cn AS faultNameCn,
        t2.fault_code AS faultCode,
        t2.fault_type_key AS faultTypeKey,
        t2.fault_level AS faultLevel,
        t1.vehicle_structure_code AS vehicleStructureCode,
        t9.label AS faultLevelName,
        t2.description,
        case t1.fault_source when '0' then '自动上报' when '1' then '机理故障' end source
        FROM
        mtc_alarm_warning t1
        LEFT JOIN ekb_fault_type t2 on t1.fault_type_key = t2.fault_type_key
        LEFT JOIN stru_vehicle_type_structure t3 on t3.structure_code  = t2.vehicle_structure_code
        AND t3.vehicle_type_id = t1.vehicle_type_id
        LEFT JOIN mtr_vehicle t4 on t4.id = t1.vehicle_id
        LEFT JOIN sys_dict t9 ON cast(t9.VALUE as int) = t2.fault_level and  t9.line_id = t1.line_id
                                     and t9.vehicle_type_id = t1.vehicle_type_id and t9.type_code ='fault_level'
        WHERE
        1=1
        AND t4.vehicle_code =  #{vehicleCode}
        AND t3.threed_code = #{threedCode}
        AND t2.del_flag = 0
        ORDER BY t1.start_time DESC
    </select>
    <select id="autoFaultGroupCountNumByVehicleCode" resultType="java.util.HashMap">
        SELECT
            count(*),
            CASE WHEN t3.threed_code IS NULL THEN '其他' ELSE t3.threed_code END "threedCode"
        FROM
            mtc_alarm_warning t1
                LEFT JOIN ekb_fault_type t2 on t1.fault_type_key = t2.fault_type_key
                LEFT JOIN stru_vehicle_type_structure t3 on t3.structure_code  = t2.vehicle_structure_code
                AND t3.vehicle_type_id = t1.vehicle_type_id
                LEFT JOIN mtr_vehicle t4 on t4.id = t1.vehicle_id
        WHERE
            1=1
          AND t4.vehicle_code = #{vehicleCode}
          AND t2.del_flag = 0
        GROUP BY t3.threed_code
    </select>
    <select id="componentGroupListByVehicleCode" resultType="java.util.HashMap">
        SELECT
            count(*),
            CASE WHEN svts.threed_code IS NULL THEN '其他' ELSE svts.threed_code END "threedCode"
        FROM
            relay_contactor_life rcl
                LEFT JOIN
            relay_contactor rc ON rcl.relay_contactor_id = rc.id
                LEFT JOIN mtr_vehicle mr ON mr.vehicle_code = rcl.vehicle_code
                LEFT JOIN mtr_vehicle_type_relation mvtr on mvtr.vehicle_id = mr.id
                LEFT JOIN stru_vehicle_type_structure svts ON svts.structure_code = rc.structure_code
                and svts.vehicle_type_id = mvtr.vehicle_type_id
        WHERE
            1 = 1
          AND rc.del_flag = '0'
          AND rcl.valid = 1
          AND rcl.vehicle_code = #{vehicleCode}
        GROUP BY svts.threed_code

    </select>
    <select id="componentListGroupListByVehicleCodeAndThreedCode"
            resultType="cc.crrc.manage.pojo.monitor.RelayContactorPO">
        SELECT
        CONCAT (round( rcl.operation_cnt::numeric/rc.electrical_life*100)) AS usePercent,
        rcl.id AS id,
        rcl.relay_contactor_id AS relayContactorId,
        rcl.operation_cnt AS operationCnt,
        rcl.replace_times AS replaceTimes,
        rcl.vehicle_code AS vehicleCode,
        rcl.assembly_time AS assemblyTime,
        rcl.assembly_location AS assemblyLocation,
        rc.name_cn AS nameCn,
        rc.type AS type,
        sd.label AS typeCn,
        sd.line_id AS lineId,
        rc.create_by AS createBy,
        rc.create_time AS createTime,
        rc.modify_by AS modifyBy,
        rc.modify_time AS modifyTime,
        rc.electrical_life AS electricalLife,
        rc.operation_avg AS operationAvg,
        rc.structure_code AS structureCode,
        rc.unit,
        rc.structure_code AS structureCode,
        rc.component_kind AS componentKind,
        rc.name_en AS nameEn,
        rcl.product_number AS productNumber,
        rcl.manufacturer_id AS manufacturerId,
        rcl.installer,
        mm.name AS manufacturerName
        FROM
        relay_contactor_life rcl
        LEFT JOIN
        relay_contactor rc ON rcl.relay_contactor_id = rc.id
        LEFT JOIN mtr_vehicle mr ON mr.vehicle_code = rcl.vehicle_code
        LEFT JOIN mtr_vehicle_type_relation mvtr on mvtr.vehicle_id = mr.id
        LEFT JOIN sys_dict sd ON sd.value = rc.type and mr.metro_line_id =sd.line_id
        LEFT JOIN mtr_manufacturer mm ON mm.id = rcl.manufacturer_id
        LEFT JOIN stru_vehicle_type_structure svts ON svts.structure_code = rc.structure_code
        and svts.vehicle_type_id = mvtr.vehicle_type_id
        WHERE
        1=1
        AND rc.del_flag = '0'
        AND sd.del_flag = '0'
        <if test="vehicleCode != null and vehicleCode != ''">
            AND rcl.vehicle_code = #{vehicleCode}
        </if>
        <if test="threedCode != null and threedCode != ''">
            AND svts.threed_code LIKE '%'||#{threedCode}||'%'
        </if>
        ORDER BY
        ( rcl.operation_cnt :: NUMERIC / rc.electrical_life * 100 ) DESC,
        rcl.operation_cnt DESC,
        COALESCE ( rcl.replace_times, 0 ) DESC,
        convert_to( rc.name_cn, 'gbk' )
    </select>
    <select id="relayContactorLifeGroupListByVehicleCode" resultType="java.util.HashMap">
        SELECT
            count(*),
            CASE WHEN svts.threed_code IS NULL THEN '其他' ELSE svts.threed_code END "threedCode"
        FROM
            relay_contactor_life rcl
                LEFT JOIN
            relay_contactor rc ON rcl.relay_contactor_id = rc.id
                LEFT JOIN mtr_vehicle mr ON mr.vehicle_code = rcl.vehicle_code
                LEFT JOIN mtr_vehicle_type_relation mvtr on mvtr.vehicle_id = mr.id
                LEFT JOIN stru_vehicle_type_structure svts ON svts.structure_code = rc.structure_code
                and svts.vehicle_type_id = mvtr.vehicle_type_id
        WHERE
            1 = 1
          AND rc.component_kind = 0
          AND rc.del_flag = '0'
          AND rcl.valid = 1
          AND rcl.vehicle_code = #{vehicleCode}
        GROUP BY svts.threed_code
    </select>
    <select id="relayContactorLifeGroupListByVehicleCodeAndThreedCode"
            resultType="cc.crrc.manage.pojo.monitor.RelayContactorPO">
        SELECT
        CONCAT (round( rcl.operation_cnt::numeric/rc.electrical_life*100)) AS usePercent,
        rcl.id AS id,
        rcl.relay_contactor_id AS relayContactorId,
        rcl.operation_cnt AS operationCnt,
        rcl.replace_times AS replaceTimes,
        rcl.vehicle_code AS vehicleCode,
        rcl.assembly_time AS assemblyTime,
        rcl.assembly_location AS assemblyLocation,
        rc.name_cn AS nameCn,
        rc.type AS type,
        sd.label AS typeCn,
        sd.line_id AS lineId,
        rc.create_by AS createBy,
        rc.create_time AS createTime,
        rc.modify_by AS modifyBy,
        rc.modify_time AS modifyTime,
        rc.electrical_life AS electricalLife,
        rc.operation_avg AS operationAvg,
        rc.structure_code AS structureCode,
        rc.unit,
        rc.structure_code AS structureCode,
        rc.component_kind AS componentKind,
        rc.name_en AS nameEn,
        rcl.product_number AS productNumber,
        rcl.manufacturer_id AS manufacturerId,
        rcl.installer,
        mm.name AS manufacturerName
        FROM
        relay_contactor_life rcl
        LEFT JOIN
        relay_contactor rc ON rcl.relay_contactor_id = rc.id
        LEFT JOIN mtr_vehicle mr ON mr.vehicle_code = rcl.vehicle_code
        LEFT JOIN mtr_vehicle_type_relation mvtr on mvtr.vehicle_id = mr.id
        LEFT JOIN sys_dict sd ON sd.value = rc.type and mr.metro_line_id =sd.line_id
        LEFT JOIN mtr_manufacturer mm ON mm.id = rcl.manufacturer_id
        LEFT JOIN stru_vehicle_type_structure svts ON svts.structure_code = rc.structure_code
        and svts.vehicle_type_id = mvtr.vehicle_type_id
        WHERE
        1=1
        AND rc.component_kind = 0
        AND rcl.valid = 1
        AND rc.del_flag = '0'
        AND sd.del_flag = '0'
        <if test="vehicleCode != null and vehicleCode != ''">
            AND rcl.vehicle_code = #{vehicleCode}
        </if>
        <if test="threedCode != null and threedCode != ''">
            AND svts.threed_code LIKE '%'||#{threedCode}||'%'
        </if>
        ORDER BY
        ( rcl.operation_cnt :: NUMERIC / rc.electrical_life * 100 ) DESC,
        rcl.operation_cnt DESC,
        COALESCE ( rcl.replace_times, 0 ) DESC,
        convert_to( rc.name_cn, 'gbk' )
    </select>
    <select id="mechanismAlarmRuleGroupCount" resultType="java.util.HashMap">
        SELECT
        count(*),
        svts.threed_code AS "threedCode"
        FROM
        mtc_mechanism_alarm_rule rule
        LEFT JOIN ekb_fault_type fault ON rule.fault_type_key = fault.fault_type_key
        LEFT JOIN stru_vehicle_type_structure svts on svts.structure_code = fault.vehicle_structure_code
        LEFT JOIN mtr_vehicle_type_relation mvtr ON mvtr.vehicle_type_id = svts.vehicle_type_id
        LEFT JOIN mtr_vehicle mv on mv.id = mvtr.vehicle_id
        WHERE
        1=1
        AND fault.del_flag = 0
        <if test="vehicleCode != null and vehicleCode !=''">
            AND mv.vehicle_code = #{vehicleCode}
        </if>
        GROUP BY
        svts.threed_code
    </select>
    <select id="mechanismAlarmRuleListByVehicleCodeAndThreedCode"
            resultType="cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRuleVO">
        SELECT
        rule.id AS id,
        svts.threed_code AS threedCode,
        rule.vehicle_type_id AS vehicleTypeId,
--         mvt.name AS vehicleTypeName,
        rule.fault_type_key AS faultTypeKey,
        fault.location AS location,
        fault.name_cn ||'('||fault.name_en||')' AS faultName,
        fault.subsystem AS subsystem,
        sd.label AS subSystemName,
        rule.name AS name,
        rule.enable AS enable,
        rule.description AS description,
        file.group||'/'||file.file_location AS imageUrl,
        rule.line_id AS lineId
        FROM
        mtc_mechanism_alarm_rule rule
        LEFT JOIN sys_file file ON rule.image_file = file.id
        LEFT JOIN ekb_fault_type fault ON rule.fault_type_key = fault.fault_type_key
        LEFT JOIN stru_vehicle_type_structure svts on svts.structure_code = fault.vehicle_structure_code
        LEFT JOIN mtr_vehicle_type_relation mvtr ON mvtr.vehicle_type_id = svts.vehicle_type_id
        LEFT JOIN mtr_vehicle mv on mv.id = mvtr.vehicle_id
--         LEFT JOIN mtr_vehicle_type mvt ON mvt.id = rule.vehicle_type_id
--         LEFT JOIN mtr_vehicle mv on mv.metro_line_id = mvt.line_id
        LEFT JOIN sys_dict sd ON sd.type_code = 'ass_car_system' and sd.value = fault.subsystem and sd.del_flag = '0'
        WHERE
        rule.line_id = sd.line_id
        AND fault.del_flag = 0
--         AND mvt.del_flag = 0
        AND file.del_flag = 0
        AND rule.vehicle_type_id = sd.vehicle_type_id
        <if test="vehicleCode != null and vehicleCode !=''">
            AND mv.vehicle_code = #{vehicleCode}
        </if>
        <if test="threedCode != null and threedCode !=''">
            AND  svts.threed_code  LIKE '%'||#{threedCode}||'%'
        </if>
        ORDER BY
        svts.threed_code,
        rule.create_time desc
    </select>


</mapper>