<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.UserMapping">

    <select id="getUserInfoByName" resultType="cc.crrc.manage.pojo.User">
        select id,username,password,name,tenant_id as tenantId, organization_id as organizationId,image,del_flag as
        delFlag,
        is_super_admin as isSuperAdmin from sys_user where username = #{username} and del_flag ='0'
    </select>
    <select id="selectUserInfoById" resultType="cc.crrc.manage.pojo.User">
        select id,username,is_super_admin as isSuperAdmin,image from sys_user where id = #{userId} and del_flag ='0'
    </select>

    <select id="getAll" resultType="cc.crrc.manage.pojo.User">
        SELECT
        t1.ID,
        t1.update_date AS updateDate,
        t1.username,
        t1.image,
        t1.PASSWORD AS PASSWORD,
        t1.NAME AS NAME,
        t1.birthday AS birthday,
        t1.address AS address,
        t1.mobile_phone AS mobilePhone,
        t1.tel_phone AS telPhone,
        t1.email AS email,
        t1.sex AS sex,
        t1.TYPE AS TYPE,
        t1.remarks AS remarks,
        t1.tenant_id AS tenantId,
        t1.organization_id AS organizationId,
        t1.is_super_admin AS isSuperAdmin,
        t1.role_id AS roleId,
        t1.create_date AS createDate,

        array_to_string(ARRAY(SELECT distinct UNNEST (ARRAY_AGG(t5.role_name))),',') roleName,
        array_to_string(ARRAY(SELECT distinct UNNEST (ARRAY_AGG(t5.role_code))),',') roleCode,
        array_to_string(ARRAY(SELECT distinct UNNEST (ARRAY_AGG(t3.NAME))),',') organizationNames,
        array_to_string(ARRAY(SELECT distinct UNNEST (ARRAY_AGG(t3.ID))),',') organizationIds
        FROM
        sys_user t1
        LEFT JOIN sys_role_user t2 ON t2.user_id = t1.ID
        LEFT JOIN sys_role t5 ON t5.ID = t2.role_id
        LEFT JOIN sys_organization_user t4 ON t4.user_id = t1.ID
        LEFT JOIN sys_organization t3 ON t3.ID = t4.organization_id
        WHERE
        $DATASCOPE$ AND
        t1.del_flag = '0' AND
        t1.id!='0'
        <if test = "name != null and name !='' ">
            AND t1.name Like '%\'||#{name}||'%'
        </if>
        GROUP BY
        t1.ID,
        t1.update_date
        ORDER BY
        t1.update_date DESC
    </select>

    <!--根据用户保存的角色id字符串查询用户所有角色对应的名称和编码-->
    <select id="findAllRoleNameCode" resultType="cc.crrc.manage.pojo.User">
        SELECT
        t1.uname as username,
        t1.uid AS id,
        sr.role_Code AS roleCode,
        sr.role_name AS roleName
        FROM
        (SELECT su.id AS uid,su.username AS uname,regexp_split_to_table(su.user_role_ids,';') rid FROM
        sys_user su WHERE del_flag = '0')t1
        LEFT JOIN sys_role sr
        ON t1.rid = sr.id
    </select>

    <insert id="insertUser">
        INSERT INTO sys_user (
        id,
        username,
        name,
        password,
        mobile_phone,
        create_by,
        create_date,
        del_flag,
        tenant_id,
        organization_id,
        is_super_admin,
        image,
        role_id
        )
        VALUES
        (
        #{id},#{username},#{name},#{password},#{mobilePhone},#{createBy},#{createDate},#{delFlag},#{tenantId},#{organizationId},#{isSuperAdmin},#{image},#{roleId})
    </insert>
</mapper>
