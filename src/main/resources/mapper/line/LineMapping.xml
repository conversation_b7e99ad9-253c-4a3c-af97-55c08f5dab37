<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.line.LineMapping">

    <select id="getLines" resultType="cc.crrc.manage.pojo.line.LineDTO">
        SELECT DISTINCT
        line.id,
        line.name,
        line.location_id as locationId,
        location.name_cn as locationName,
        line.mileage,
        line.longitude,
        line.latitude,
        line.create_by as createBy,
        line.create_time as createTime,
        line.modify_by as modifyBy,
        line.modify_time as modifyTime,
        line.remark,
        line.color,
        line.direction_status as directionStatus
        FROM mtr_line line
        LEFT JOIN sys_location location ON line.location_id=location.id
        <if test='userId != null and userId != "0"'>
            LEFT JOIN mtr_org_metro_line lo ON line.id=lo.metro_line_id
            LEFT JOIN sys_organization o ON lo.organization_id=o.id
            /*LEFT JOIN sys_organization_user ou ON o.id=ou.organization_id*/
        </if>
        WHERE
            line.del_flag = 0
            <if test="lineDTO.locationId != null and lineDTO.locationId !=''">
                AND line.location_id = #{lineDTO.locationId}
            </if>
            <if test="lineDTO.locationName != null and lineDTO.locationName != ''">
                AND location.name_cn = #{lineDTO.locationName}
            </if>
            <if test="lineDTO.name != null and lineDTO.name != ''">
                AND line.name like '%'||#{lineDTO.name}||'%'
            </if>
            <!--<if test="userId != null and userId != 0">
                AND ou.user_id = #{userId}
            </if>-->
            <!-- 只筛选当前用户的部门及其子部门下关联的线路数据 -->
            <if test='userId != null and userId != "0" and list != null and list.size() != 0'>
                AND o.id IN
                <foreach collection="list" separator="," item="item" open="(" close=")">
                    #{item.id}
                </foreach>
            </if>
        ORDER BY line.create_time asc
    </select>
    <update id="updateLine">
        UPDATE mtr_line set
        <if test="name != null and name != ''">
            name = #{name},
        </if>
        <if test="locationId != null and locationId !=''">
            location_id = #{locationId},
        </if>
        <if test="mileage != null">
            mileage = #{mileage},
        </if>
        <if test="longitude != null">
            longitude = #{longitude},
        </if>
        <if test="latitude != null">
            latitude = #{latitude},
        </if>
        <if test="remark != null and remark != ''">
            remark = #{remark},
        </if>
        <if test="color != null and color != ''">
            color = #{color},
        </if>
        modify_by = #{modifyBy},
        modify_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <insert id="addLine">
        INSERT INTO mtr_line
            (
            id,
            name,
            location_id,
            mileage,
            longitude,
            latitude,
            create_by,
            create_time,
            modify_by,
            modify_time,
            remark,
            del_flag,
            color,
            direction_status)
        VALUES
            (
            #{id},
            #{name},
            #{locationId},
            #{mileage},
            #{longitude},
            #{latitude},
            #{createBy},
            CURRENT_TIMESTAMP,
            #{modifyBy},
            CURRENT_TIMESTAMP,
            #{remark},
             0,
            #{color},
            #{directionStatus})
    </insert>
    <!--删除线路-->
    <delete id="deleteLine">
        UPDATE mtr_line
        SET del_flag = 1,
            modify_time = CURRENT_TIMESTAMP,
            modify_by = #{userId}
        WHERE id = #{id}
    </delete>

    <!-- 地域 -->
    <select id="getLocations" resultType="cc.crrc.manage.pojo.line.LineDTO">
        SELECT
            id as locationId,
            name_cn as locationName,
            longitude,
            latitude
        FROM
            sys_location
    </select>
    <!-- 线路与组织部门关联表 -->
    <insert id="addOrgMetroLine">
        INSERT INTO mtr_org_metro_line
            (metro_line_id,
            organization_id)
        VALUES
            (#{metroLineId},
            #{organizationId})
    </insert>
    <!--删除部门线路关系-->
    <delete id="deleteOrganizationLine">
        DELETE FROM mtr_org_metro_line WHERE metro_line_id = #{metroLineId}
    </delete>
    <!--通过线路id筛选部门-->
    <select id="getOrganizationsByLineId" resultType="java.util.HashMap">
        SELECT
            array_to_string( ARRAY ( SELECT DISTINCT UNNEST ( ARRAY_AGG ( org.id ) ) ), ',' ) "organizationsId",
            array_to_string( ARRAY ( SELECT DISTINCT UNNEST ( ARRAY_AGG ( org.name ) ) ), ',' ) "organizationsName"
        FROM sys_organization org
        LEFT JOIN mtr_org_metro_line relation ON org.id = relation.organization_id
        LEFT JOIN mtr_line line ON relation.metro_line_id = line.id
        WHERE line.id = #{lineId}
        GROUP BY line.id
    </select>
    <!--通过id查询线路-->
    <select id="getLineById" resultType="cc.crrc.manage.pojo.line.LineDTO">
        SELECT
            line.id,
            line.name,
            line.location_id as locationId,
            location.name_cn as locationName,
            line.mileage,
            line.longitude,
            line.latitude,
            line.color,
            line.direction_status as directionStatus
        FROM mtr_line line
        LEFT JOIN sys_location location ON line.location_id=location.id
        WHERE line.id = #{id}
    </select>
    <!--站点-->
    <select id="getStations" resultType="cc.crrc.manage.pojo.line.StationDTO">
        SELECT 
            id,
            station_code as stationCode,
            name,
            sort_number as sortNumber,
            departure_distance as departureDistance,
            status,
            metro_line_id as metroLineId,
            create_by as createBy,
            create_time as createTime,
            modify_by as modifyBy,
            modify_time as modifyTime,
            remark,
            type,
            direction,
            sta_id as staId
        FROM 
            mtr_station
        WHERE
            metro_line_id = #{metroLineId}
        <if test="direction != null and direction != ''">
            AND direction = #{direction}
        </if>
        <if test="type != null and type != ''">
            AND type = #{type}
        </if>
        <if test="id != null and id != ''">
            AND id != #{id}
        </if>
        <if test="stationCode != null">
            AND station_code = #{stationCode}
        </if>
        AND del_flag = 0
        ORDER BY direction desc, sort_number ASC
    </select>

    <select id="getStation" resultType="cc.crrc.manage.pojo.line.StationDTO">
        SELECT 
            id,
            station_code as stationCode,
            name,
            sort_number as sortNumber,
            departure_distance as departureDistance,
            status,
            metro_line_id as metroLineId,
            create_by as createBy,
            create_time as createTime,
            modify_by as modifyBy,
            modify_time as modifyTime,
            remark,
            type,
            direction,
            sta_id as staId
        FROM 
            mtr_station
        WHERE
            status = 1
            AND del_flag = 0
            <if test="metroLineId != null and metroLineId != ''">
                AND  metro_line_id=#{metroLineId}
            </if>
        ORDER BY sort_number ASC
    </select>

    <select id="getCurrentStation" resultType="cc.crrc.manage.pojo.line.StationDTO">
        SELECT
            id,
            station_code as stationCode,
            name,
            sort_number as sortNumber,
            departure_distance as departureDistance,
            status,
            metro_line_id as metroLineId,
            create_by as createBy,
            create_time as createTime,
            modify_by as modifyBy,
            modify_time as modifyTime,
            remark,
            type,
            direction,
            sta_id as staId
        FROM
            mtr_station
        WHERE
            metro_line_id=#{metroLineId}
            and
            station_code=#{stationCode}
        AND
            status = 1
        AND del_flag = 0
        ORDER BY sort_number ASC
    </select>
    
    <select id="getStationByVehicleId" resultType="cc.crrc.manage.pojo.line.StationDTO">
        SELECT 
            s.id,
            s.station_code as stationCode,
            s.name,
            s.sort_number as sortNumber,
            s.departure_distance as departureDistance,
            s.status,
            s.metro_line_id as metroLineId,
            s.create_by as createBy,
            s.create_time as createTime,
            s.modify_by as modifyBy,
            s.modify_time as modifyTime,
            s.remark
        FROM 
            mtr_station s
		JOIN
			mtr_vehicle v
		ON s.metro_line_id = v.metro_line_id
        WHERE
            v.vehicle_code = #{vehicleId}
        AND 
            s.status = 1
        AND s.del_flag = 0
        ORDER BY sort_number ASC
    </select>
    <select id="getCurrentRunDistance" resultType="java.lang.Float">
        select accumlation from dw_realtime_train_status_by_day
        where signal_name_en = 'DDU_RunDistance'
         and train_id = #{trainId}
         order by to_number(time,'99999999') desc limit 1
    </select>

    <insert id="insertStations">
        INSERT INTO mtr_station
        (
            id,
            station_code,
            name,
            sort_number,
            departure_distance,
            metro_line_id,
            create_by,
            create_time,
            remark,
            type,
            direction,
            sta_id,
            del_flag
        )VALUES
        (
            #{id},
            #{stationCode},
            #{name},
            #{sortNumber},
            #{departureDistance},
            #{metroLineId},
            #{createBy},
            CURRENT_TIMESTAMP,
            #{remark},
            #{type},
            #{direction},
            #{staId},
            0
        )
    </insert>

    <update id="updateStations">
        UPDATE mtr_station SET
            station_code = #{stationCode},
            name = #{name},
            sort_number = #{sortNumber},
            departure_distance = #{departureDistance},
            status = #{status},
            metro_line_id = #{metroLineId},
            <if test="modifyTime != null and modifyTime == 'true'">
                modify_by = #{modifyBy},
                modify_time = CURRENT_TIMESTAMP,
            </if>
            type = #{type},
            direction = #{direction},
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            sta_id = #{staId}
        WHERE
            id = #{id}
    </update>
    <update id="deleteStation">
        UPDATE mtr_station SET
        del_flag = 1
        WHERE id=#{id}
    </update>
    <select id="getStartStation"  resultType="cc.crrc.manage.pojo.line.StationDTO">
        SELECT
            id,
            station_code as stationCode,
            name,
            sort_number as sortNumber,
            departure_distance as departureDistance,
            status,
            metro_line_id as metroLineId,
            create_by as createBy,
            create_time as createTime,
            modify_by as modifyBy,
            modify_time as modifyTime,
            remark,
            type,
            direction,
            sta_id as staId
        FROM
            mtr_station
        WHERE
            metro_line_id=#{metroLineId}
          and direction = #{direction}
          and "type"= '1'
          and del_flag = 0
        <choose>
            <when test="direction != null and direction== 'up'">
                ORDER BY sort_number DESC
            </when>
            <otherwise>
                order by sort_number ASC
            </otherwise>
        </choose>
        LIMIT 1
    </select>
    <select id="getStationsForDrawLine" resultType="cc.crrc.manage.pojo.line.StationDTO">
        SELECT
        id,
        station_code as stationCode,
        name ||'('||direction||')' AS name,
        sort_number as sortNumber,
        departure_distance as departureDistance,
        status,
        metro_line_id as metroLineId,
        create_by as createBy,
        create_time as createTime,
        modify_by as modifyBy,
        modify_time as modifyTime,
        remark,
        type,
        direction,
        sta_id as staId
        FROM
        mtr_station
        WHERE
        metro_line_id = #{metroLineId}
        and del_flag = 0
        ORDER BY direction desc, sort_number ASC
    </select>
</mapper>