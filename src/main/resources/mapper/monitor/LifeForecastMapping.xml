<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.monitor.LifeForecastMapping">
    <update id="delRelayContactorInfo">
        UPDATE relay_contactor
        SET del_flag    = 1,
            modify_by   = #{userId},
            modify_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <select id="findRelayContactorBys" resultType="cc.crrc.manage.pojo.monitor.RelayContactorPO">
        SELECT
        CONCAT (round( rcl.operation_cnt::numeric/rc.electrical_life*100)) AS usePercent,
        rcl.id AS id,
        rcl.relay_contactor_id AS relayContactorId,
        rcl.operation_cnt AS operationCnt,
        rcl.replace_times AS replaceTimes,
        rcl.vehicle_code AS vehicleCode,
        rcl.assembly_time AS assemblyTime,
        rcl.assembly_location AS assemblyLocation,
        rc.name_cn AS nameCn,
        rc.type AS type,
        sd.label AS typeCn,
        sd.line_id AS lineId,
        rc.create_by AS createBy,
        rc.create_time AS createTime,
        rc.modify_by AS modifyBy,
        rc.modify_time AS modifyTime,
        rc.electrical_life AS electricalLife,
        rc.operation_avg AS operationAvg,
        rc.structure_code AS structureCode,
        rc.unit,
        rc.component_kind AS componentKind,
        rc.name_en AS nameEn,
        rc.product_number AS productNumber,
        rc.manufacturer_id AS manufacturerId,
        mm.name AS manufacturerName
        FROM
        relay_contactor_life rcl
        LEFT JOIN
        relay_contactor rc
        ON
        rcl.relay_contactor_id = rc.id
        LEFT JOIN mtr_vehicle mr ON
        mr.vehicle_code = rcl.vehicle_code
        LEFT JOIN
        sys_dict sd
        ON
        sd.value = rc.type and mr.metro_line_id =sd.line_id
        LEFT JOIN mtr_manufacturer mm ON mm.id = rc.manufacturer_id
        WHERE
        rcl.valid = 1
        AND rc.component_kind = 0
        AND sd.del_flag = '0'
        AND rc.del_flag = 0
        <if test="lineId != null and lineId != ''">
            and mr.metro_line_id = #{lineId}
        </if>
        <if test="vehicleCode != null and vehicleCode != ''">
            AND rcl.vehicle_code = #{vehicleCode}
        </if>
        <if test="componentType != null and componentType != ''">
            AND rc.type = #{componentType}
        </if>
        <choose>
            <when test="lifeWarning == null">
                AND CONCAT ( round( rcl.operation_cnt ::numeric / rc.electrical_life * 100 ) ) ::numeric >= 0
            </when>
            <when test="lifeWarning != null and lifeWarning == 1">
                AND CONCAT ( round( rcl.operation_cnt ::numeric / rc.electrical_life * 100 ) ) ::numeric >=95
            </when>
            <otherwise>
                AND CONCAT ( round( rcl.operation_cnt ::numeric / rc.electrical_life * 100 ) ) ::numeric &lt;= 94
            </otherwise>
        </choose>
        <if test="componentKind != null and componentKind != ''">
            AND rc.component_kind = #{componentKind}
        </if>
        <if test="structureCode != null and structureCode != ''">
            AND rc.structure_code = #{structureCode}
        </if>


        ORDER BY
        ( rcl.operation_cnt :: NUMERIC / rc.electrical_life * 100 ) DESC,
        rcl.operation_cnt DESC,
        COALESCE ( rcl.replace_times, 0 ) DESC,
        convert_to( rc.name_cn, 'gbk' )
    </select>

    <select id="findCountBys" resultType="int">
        SELECT
        COUNT(1)
        FROM
        relay_contactor_life rcl
        LEFT JOIN
        relay_contactor rc
        ON
        rcl.relay_contactor_id = rc.id
        WHERE
        rcl.valid = 1
        <if test="vehicleCode != null and vehicleCode != ''">
            AND rcl.vehicle_code = #{vehicleCode}
        </if>
        <if test="componentType != null and componentType != ''">
            AND rc.type = #{componentType}
        </if>
        <choose>
            <when test="lifeWarning != null and lifeWarning == 0">
                AND CONCAT ( round( rcl.operation_cnt ::numeric / rc.electrical_life * 100 ) ) ::numeric >= 0
            </when>
            <when test="lifeWarning != null and lifeWarning == 1">
                AND CONCAT ( round( rcl.operation_cnt ::numeric / rc.electrical_life * 100 ) ) ::numeric >=95
            </when>
            <otherwise>
                AND CONCAT ( round( rcl.operation_cnt ::numeric / rc.electrical_life * 100 ) ) ::numeric &lt;= 94
            </otherwise>
        </choose>
    </select>
    <select id="listRelayContactor" resultType="cc.crrc.manage.pojo.monitor.RelayContactorPO">
        SELECT
        rc.id,
        rc.comm_original_signal_id AS commOriginalSignalId,
        rc.name_cn AS nameCn,
        rc.type AS type,
        rc.create_by AS createBy,
        rc.create_time AS createTime,
        rc.modify_by AS modifyBy,
        rc.modify_time AS modifyTime,
        rc.electrical_life AS electricalLife,
        rc.operation_avg AS operationAvg,
        rc.mtr_vehicle_type_id AS mtrVehicleTypeId,
        rc.component_kind AS componentKind,
        rc.name_en AS nameEn,
        rc.product_number AS productNumber,
        rc.manufacturer_id AS manufacturerId,
        rc.structure_code AS structureCode
        FROM
        relay_contactor rc
        WHERE
        1=1
        <if test="vehicleTypeId != null and vehicleTypeId != ''">
            AND rc.mtr_vehicle_type_id = #{vehicleTypeId}
        </if>
        ORDER BY
        rc.comm_original_signal_id

    </select>

    <insert id="insertRelayContactorLife">
        INSERT INTO relay_contactor_life(
        relay_contactor_id,
        operation_cnt,
        remaining_life,
        vehicle_code,
        valid,
        assembly_time,
        product_number,
        manufacturer_id
        )
        VALUES
        <foreach collection="relayContactorLifePOS" separator="," item="item">
            (#{item.relayContactorId},#{item.operationCnt},#{item.remainingLife},#{item.vehicleCode},#{item.valid},CURRENT_TIMESTAMP,#{item.productNumber},#{item.manufacturerId})
        </foreach>
    </insert>

    <select id="getById" resultType="cc.crrc.manage.pojo.monitor.RelayContactorPO">
        SELECT rcl.id                AS id,
               rcl.operation_cnt     AS operationCnt,
               rcl.remaining_life    AS remainingLife,
               rcl.vehicle_code      AS vehicleCode,
               rcl.assembly_time     AS assemblyTime,
               rcl.assembly_location AS assemblyLocation,
               rcl.usepercent        AS usePercent,
               rcl.installer         AS installer,
               rcl.replace_times     AS replaceTimes,
               rc.name_cn            AS nameCn,
               rc.type               AS type,
               rc.create_by          AS createBy,
               rc.create_time        AS createTime,
               rc.modify_by          AS modifyBy,
               rc.modify_time        AS modifyTime,
               rc.electrical_life    AS electricLife,
               rc.operation_avg      AS operationAvg,
               rc.structure_code     AS structureCode,
               rc.component_kind     AS componentKind,
               rc.name_en            AS nameEn,
               rc.product_number     AS productNumber,
               rc.manufacturer_id    AS manufacturerId
        FROM relay_contactor_life rcl
                 LEFT JOIN
             relay_contactor rc
             ON
                 rcl.relay_contactor_id = rc.id
        WHERE rcl.id = #{id}
    </select>

    <insert id="addRelayHistory">
        INSERT INTO relay_contactor_history(id,
                                            relay_contactor_life_id,
                                            operation_cnt,
                                            assembly_time,
                                            installer,
                                            end_time,
                                            product_number,
                                            manufacturer_id)
        VALUES (#{snowId},
                #{id},
                #{operationCnt},
                #{assemblyTime},
                #{installer},
                CURRENT_TIMESTAMP,
                #{productNumber},
                #{manufacturerId})
    </insert>

    <insert id="insertRelayContactorInfo" parameterType="cc.crrc.manage.pojo.monitor.RelayContactorPO">
        INSERT INTO relay_contactor (id,
                                     comm_original_signal_id,
                                     type,
                                     create_by,
                                     create_time,
                                     modify_by,
                                     modify_time,
                                     remark,
                                     electrical_life,
                                     operation_avg,
                                     mtr_vehicle_type_id,
                                     name_cn,
                                     structure_code,
                                     calculation_type,
                                     trigger_value,
                                     calculation_method,
                                     line_id,
                                     unit,
                                     component_kind,
                                     name_en,
                                     product_number,
                                     manufacturer_id)
        VALUES (#{id},
                #{commOriginalSignalId},
                #{type},
                #{createBy},
                CURRENT_TIMESTAMP,
                #{modifyBy},
                CURRENT_TIMESTAMP,
                #{remark},
                #{electricalLife},
                #{operationAvg},
                #{mtrVehicleTypeId},
                #{nameCn},
                #{structureCode},
                #{calculationType},
                #{triggerValue},
                #{calculationMethod},
                #{lineId},
                #{unit},
                #{componentKind},
                #{nameEn},
                #{productNumber},
                #{manufacturerId})
    </insert>

    <update id="addReplaceTimes">
        UPDATE relay_contactor_life
        SET installer     = #{userName},
            replace_times = #{replaceTimes}
        WHERE id = #{id}
    </update>

    <update id="updRelayContactorLife">
        UPDATE relay_contactor_life
        SET operation_cnt = 0,
            assembly_time = CURRENT_TIMESTAMP,
            usepercent    = 0,
            installer     = #{userName},
            replace_times = replace_times + 1
        WHERE id = #{id}
    </update>

    <update id="updateRelayContactorInfo" parameterType="cc.crrc.manage.pojo.monitor.RelayContactorPO">
        UPDATE relay_contactor
        SET
        modify_by = #{modifyBy},
        modify_time = CURRENT_TIMESTAMP
        <if test="commOriginalSignalId != null and commOriginalSignalId != ''">
            ,comm_original_signal_id = #{commOriginalSignalId}
        </if>
        <if test="type != null">
            ,type = #{type}
        </if>
        <if test="remark != null">
            ,remark = #{remark}
        </if>
<!--        <if test="electricalLife != null">-->
<!--            ,electrical_life = #{electricalLife}-->
<!--        </if>-->
<!--        <if test="triggerValue != null">-->
<!--            ,trigger_value = #{triggerValue}-->
<!--        </if>-->
        ,electrical_life = #{electricalLife}
        ,trigger_value = #{triggerValue}
        <if test="operationAvg != null">
            ,operation_avg = #{operationAvg}
        </if>
        <if test="mtrVehicleTypeId != null and mtrVehicleTypeId != ''">
            ,mtr_vehicle_type_id = #{mtrVehicleTypeId}
        </if>
        <if test="nameCn != null and nameCn != ''">
            ,name_cn = #{nameCn}
        </if>
        <if test="structureCode != null and structureCode != ''">
            ,structure_code = #{structureCode}
        </if>
        <if test="calculationType != null ">
            ,calculation_type = #{calculationType}
        </if>
        <if test="calculationMethod != null ">
            ,calculation_method = #{calculationMethod}
        </if>
        <if test="lineId != null and lineId != ''">
            ,line_id = #{lineId}
        </if>
        <if test="unit != null ">
            ,unit = #{unit}
        </if>
        <if test="componentKind != null and componentKind != ''">
            ,component_kind = #{componentKind}
        </if>
        <if test="nameEn != null and nameEn != ''">
            ,name_en = #{nameEn}
        </if>
        <if test="productNumber != null and productNumber != ''">
            ,product_number = #{productNumber}
        </if>
        <if test="manufacturerId != null and manufacturerId != ''">
            ,manufacturer_id = #{manufacturerId}
        </if>
        WHERE id = #{id}
    </update>

    <select id="getExcelData" resultType="cc.crrc.manage.pojo.monitor.RelayContactorPO">
        SELECT operationCnt, assemblyTime, installer, nameCn,unit
        FROM (SELECT rch.operation_cnt AS operationCnt,
                     rch.assembly_time AS assemblyTime,
                     rch.installer     AS installer,
                     rc.name_cn        AS nameCn,
                     rc.unit
              FROM relay_contactor_life rcl
                       LEFT JOIN
                   relay_contactor rc
                   ON
                       rcl.relay_contactor_id = rc.id
                       JOIN
                   relay_contactor_history rch
                   ON
                       rcl.id ::TEXT = rch.relay_contactor_life_id ::TEXT
              WHERE
                  rcl.id = #{id}
              UNION ALL
              SELECT
                  rcl.operation_cnt AS operationCnt,
                  rcl.assembly_time AS assemblyTime,
                  rcl.installer AS installer,
                  rc.name_cn AS nameCn,
                  rc.unit
              FROM
                  relay_contactor_life rcl
                  LEFT JOIN
                  relay_contactor rc
              ON
                  rcl.relay_contactor_id = rc.id
              WHERE
                  rcl.id = #{id}) history
        ORDER BY assemblyTime DESC
    </select>

    <select id="getLifeForecastForExcel"
            resultType="cc.crrc.manage.pojo.excel.LifeForecastForExcelPO">
        SELECT
        CONCAT (round( rcl.operation_cnt::numeric/rc.electrical_life*100)) AS usePercent,
        rcl.id AS id,
        rcl.operation_cnt AS operationCnt,
        rcl.remaining_life AS remainingLife,
        rcl.vehicle_code AS vehicleCode,
        rcl.assembly_time AS assemblyTime,
        rcl.assembly_location AS assemblyLocation,
        rcl.replace_times AS replaceTimes,
        rc.name_cn AS nameCn,
        rc.type AS type,
        sd.label AS typeCn,
        sd.line_name AS lineName,
        rc.create_by AS createBy,
        rc.create_time AS createTime,
        rc.modify_by AS modifyBy,
        rc.modify_time AS modifyTime,
        rc.electrical_life AS electricLife,
        rc.operation_avg AS operationAvg
        FROM
        relay_contactor_life rcl
        LEFT JOIN
        relay_contactor rc
        ON
        rcl.relay_contactor_id = rc.id
        LEFT JOIN mtr_vehicle mr ON
        mr.vehicle_code = rcl.vehicle_code
        LEFT JOIN
        sys_dict sd
        ON
        sd.value = rc.type and mr.metro_line_id =sd.line_id
        WHERE
        rcl.valid = 1
        AND sd.del_flag = '0'
        AND rcl.operation_cnt  IS NOT NULL
        AND rc.electrical_life IS NOT NULL
        <if test="lineId != null and lineId !='' ">
            and sd.line_id = #{lineId}
        </if>
        <if test="vehicleCode != null and vehicleCode !='' ">
            and rcl.vehicle_code = #{vehicleCode}
        </if>
        <if test="componentType != null and componentType != ''">
            AND rc.type = #{componentType}
        </if>
        <choose>
            <when test="lifeWarning == null">
                AND CONCAT ( round( rcl.operation_cnt ::numeric / rc.electrical_life * 100 ) ) ::numeric >= 0
            </when>
            <when test="lifeWarning != null and lifeWarning == 1">
                AND CONCAT ( round( rcl.operation_cnt ::numeric / rc.electrical_life * 100 ) ) ::numeric >=95
            </when>
            <otherwise>
                AND CONCAT ( round( rcl.operation_cnt ::numeric / rc.electrical_life * 100 ) ) ::numeric &lt;= 94
            </otherwise>
        </choose>
        ORDER BY
        ( rcl.operation_cnt :: NUMERIC / rc.electrical_life * 100 ) DESC,
        rcl.operation_cnt DESC,
        COALESCE ( rcl.replace_times, 0 ) DESC,
        convert_to( rc.name_cn, 'gbk' )
    </select>
    <select id="relayContactorLifesHistory" resultType="cc.crrc.manage.pojo.monitor.RelayContactorPO">
        SELECT operationCnt, assemblyTime, installer, nameCn,unit,componentKind,nameEn,productNumber,manufacturerId,manufacturerName
        FROM (SELECT rch.operation_cnt AS operationCnt,
                     rch.assembly_time AS assemblyTime,
                     rch.installer     AS installer,
                     rc.name_cn        AS nameCn,
                     rc.unit,
                     rc.component_kind AS componentKind,
                     rc.name_en AS nameEn,
                     rc.product_number AS productNumber,
                     rc.manufacturer_id AS manufacturerId,
                     mm.name AS manufacturerName
              FROM relay_contactor_life rcl
                       LEFT JOIN
                   relay_contactor rc
                   ON
                       rcl.relay_contactor_id = rc.id
                       JOIN
                   relay_contactor_history rch
                   ON
                       rcl.id ::TEXT = rch.relay_contactor_life_id ::TEXT
                   LEFT JOIN mtr_manufacturer mm ON mm.id = rc.manufacturer_id
              WHERE
                  rcl.id = #{id}
              UNION ALL
              SELECT
                  rcl.operation_cnt AS operationCnt,
                  rcl.assembly_time AS assemblyTime,
                  rcl.installer AS installer,
                  rc.name_cn AS nameCn,
                  rc.unit,
                  rc.component_kind AS componentKind,
                  rc.name_en AS nameEn,
                  rc.product_number AS productNumber,
                  rc.manufacturer_id AS manufacturerId,
                  mm.name AS manufacturerName
              FROM
                  relay_contactor_life rcl
                  LEFT JOIN
                  relay_contactor rc
              ON
                  rcl.relay_contactor_id = rc.id
                  LEFT JOIN mtr_manufacturer mm ON mm.id = rc.manufacturer_id
              WHERE
                  rcl.id = #{id}) history
        ORDER BY assemblyTime DESC
    </select>

    <select id="findRelayContactorConfigList" resultType="cc.crrc.manage.pojo.monitor.RelayContactorPO"
            parameterType="cc.crrc.manage.pojo.monitor.LifeForecastConfigDTO">
        SELECT
        rc.id,
        rc.comm_original_signal_id AS commOriginalSignalId,
        cos.name_en AS commOriginalSignalNameEn,
        rc.mtr_vehicle_type_id AS mtrVehicleTypeId,
        mvt.name AS vehicleTypeNameCn,
        rc.calculation_method AS calculationMethod,
        rc.name_cn AS nameCn,
        rc.type AS type,
        sd1.label AS typeCn,
        rc.line_id AS lineId,
        ml.name AS lineName,
        rc.create_by AS createBy,
        rc.create_time AS createTime,
        rc.modify_by AS modifyBy,
        rc.modify_time AS modifyTime,
        rc.electrical_life AS electricalLife,
        rc.calculation_type AS calculationType,
        sd2.label AS calculationTypeCn,
        sd3.label AS calculationMethodCn,
        rc.trigger_value AS triggerValue,
        rc.unit AS unit,
        rc.structure_code AS structureCode,
        rc.component_kind AS componentKind,
        rc.name_en AS nameEn,
        rc.product_number AS productNumber,
        rc.manufacturer_id AS manufacturerId,
        mm.name AS manufacturerName
        FROM
        relay_contactor rc
        LEFT JOIN mtr_vehicle_type mvt ON mvt.id = rc.mtr_vehicle_type_id
        LEFT JOIN mtr_line ml ON ml.id = rc.line_id
        LEFT JOIN comm_original_signal cos ON cos.id = rc.comm_original_signal_id
        LEFT JOIN sys_dict sd1 ON sd1.value = rc.type AND rc.line_id = sd1.line_id AND sd1.type_code = 'device_type'
        LEFT JOIN sys_dict sd2 ON sd2.value = rc.calculation_type AND rc.line_id = sd2.line_id AND sd2.type_code =
        'calculation_type'
        LEFT JOIN sys_dict sd3 ON sd3.value = rc.calculation_method AND rc.line_id = sd3.line_id AND sd3.type_code =
        'calculation_method'
        LEFT JOIN mtr_manufacturer mm ON mm.id = rc.manufacturer_id
        WHERE
        mvt.del_flag = '0'
        AND rc.del_flag = '0'
        AND ml.del_flag = '0'

        <if test="lineId != null and lineId != ''">
            and rc.line_id = #{lineId}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId != ''">
            and rc.mtr_vehicle_type_id = #{vehicleTypeId}
        </if>
        <if test="type != null and type != ''">
            and rc.type = #{type}
        </if>
        <if test="structureCode != null and structureCode != ''">
            and rc.structure_code LIKE '%'||#{structureCode}||'%'
        </if>
        <if test="calculationType != null and calculationType != ''">
            and rc.calculation_type = #{calculationType}
        </if>
        <if test="nameCn != null and nameCn != ''">
            and rc.name_cn LIKE '%'||#{nameCn}||'%'
        </if>
        <if test="nameEn != null and nameEn != ''">
            and rc.name_en LIKE '%'||#{nameEn}||'%'
        </if>
        <if test="productNumber != null and productNumber != ''">
            and rc.product_number LIKE '%'||#{productNumber}||'%'
        </if>
        <if test="manufacturerName != null and manufacturerName != ''">
            and mm.name LIKE '%'||#{manufacturerName}||'%'
        </if>
        <if test="componentKind != null and componentKind != ''">
            AND rc.component_kind = #{componentKind}
        </if>



        ORDER BY
        rc.modify_time DESC, rc.type ASC, convert_to( rc.name_cn, 'gbk' )
    </select>

    <select id="repeatCount" resultType="java.lang.Integer"
            parameterType="cc.crrc.manage.pojo.monitor.RelayContactorPO">
        SELECT count(1)
        FROM relay_contactor
        WHERE del_flag = 0
        <if test="mtrVehicleTypeId != null and mtrVehicleTypeId != ''">
            and mtr_vehicle_type_id = #{mtrVehicleTypeId}
        </if>
        <if test="nameCn != null and nameCn != ''">
            and name_cn = #{nameCn}
        </if>
        <if test="commOriginalSignalId != null and commOriginalSignalId != ''">
            and comm_original_signal_id = #{commOriginalSignalId}
        </if>
    </select>
    <select id="relayContactorExistList" resultType="java.util.HashMap">
        SELECT
        t1.vehicle_code AS "vehicleCode",
        t1.relay_contactor_id AS "relayContactorId"
        FROM
        relay_contactor_life t1
        LEFT JOIN mtr_vehicle t2 ON t1.vehicle_code = t2.vehicle_code
        WHERE 1=1
        <if test="lineId != null and lineId != ''">
            and t2.metro_line_id = #{lineId}
        </if>
        ORDER BY
        t1.vehicle_code,
        t1.relay_contactor_id
    </select>

    <update id="updateRelayContactorLife">
        UPDATE relay_contactor_life
        SET manufacturer_id = #{manufacturerId},
            installer     = #{installer},
            product_number = #{productNumber}
        WHERE id = #{id}
    </update>

</mapper>