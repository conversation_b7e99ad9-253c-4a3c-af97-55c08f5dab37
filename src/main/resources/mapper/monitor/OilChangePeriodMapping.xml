<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.monitor.OilChangePeriodMapping">

    <insert id="addOilChangeInfo">
        INSERT INTO maintenance_period (
            id,
            unique_id,
            line_id,
            vehicle_type_id,
            vehicle_code,
            location,
            component_type,
            name_cn,
            period,
            mode,
            operator,
            operate_time,
            warning_time,
            change_time,
            work_type,
            remark,
            create_by,
            create_time,
            modify_by,
            modify_time,
            initial_mileage,
            warning_mileage
        ) VALUES (
            #{id},
            #{uniqueId},
            #{lineId},
            #{vehicleTypeId},
            #{vehicleCode},
            #{location},
            #{componentType},
            #{nameCn},
            #{period},
            #{mode},
            #{operator},
            #{operateTime},
            #{warningTime},
            #{changeTime},
            #{workType},
            #{remark},
            #{createBy},
            CURRENT_TIMESTAMP,
            #{modifyBy},
            CURRENT_TIMESTAMP,
            #{initialMileage},
            #{warningMileage}
        )
    </insert>

    <insert id="batchAddRecord">
        INSERT INTO maintenance_period(
        id,
        unique_id,
        line_id,
        vehicle_type_id,
        vehicle_code,
        location,
        component_type,
        name_cn,
        period,
        mode,
        operator,
        operate_time,
        warning_time,
        change_time,
        work_type,
        remark,
        create_by,
        create_time,
        modify_by,
        modify_time,
        initial_mileage,
        warning_mileage
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.uniqueId},
            #{item.lineId},
            #{item.vehicleTypeId},
            #{item.vehicleCode},
            #{item.location},
            #{item.componentType},
            #{item.nameCn},
            #{item.period},
            #{item.mode},
            #{item.operator},
            #{item.operateTime},
            #{item.warningTime},
            #{item.changeTime},
            #{item.workType},
            #{item.remark},
            #{item.createBy},
            CURRENT_TIMESTAMP,
            #{item.modifyBy},
            CURRENT_TIMESTAMP,
            #{item.initialMileage},
            #{item.warningMileage}
            )
        </foreach>
    </insert>

    <update id="oilChangeHistory" parameterType="cc.crrc.manage.pojo.monitor.WorkPeriodPO">
        UPDATE maintenance_period
        SET change_time = CURRENT_TIMESTAMP,
            modify_by = #{modifyBy},
            modify_time = CURRENT_TIMESTAMP,
            running_mileage = #{runningMileage}
        WHERE id = #{id}
    </update>

    <update id="updateCurrentInfo" parameterType="cc.crrc.manage.pojo.monitor.WorkPeriodPO">
        UPDATE maintenance_period
        <set>
            <if test="nameCn != null and nameCn != ''">
                name_cn = #{nameCn},
            </if>
            <if test="componentType != null and componentType != ''">
                component_type = #{componentType},
            </if>
            <if test="vehicleCode != null and vehicleCode != ''">
                vehicle_code = #{vehicleCode},
            </if>
            <if test="location != null and location != ''">
                location = #{location},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="operateTime != null">
                operate_time = #{operateTime},
            </if>
            <if test="warningTime != null">
                warning_time = #{warningTime},
            </if>
            <if test="period != null and period != ''">
                period = #{period},
            </if>

            <if test="initialMileage != null and initialMileage != ''">
                initial_mileage = #{initialMileage},
            </if>
            <if test="runningMileage != null and runningMileage != ''">
                running_mileage = #{runningMileage},
            </if>
            <if test="warningMileage != null and warningMileage != ''">
                warning_mileage = #{warningMileage},
            </if>
            modify_time = now(),
            modify_by = #{modifyBy}
        </set>
        WHERE id = #{id}
    </update>

    <update id="deleteOilChangeInfo">
        UPDATE maintenance_period
        SET del_flag = TRUE,
            modify_by = #{userId},
            modify_time = CURRENT_TIMESTAMP
        WHERE unique_id = #{id}
    </update>

    <select id="checkComponentUnique" resultType="java.lang.Integer"
            parameterType="cc.crrc.manage.pojo.monitor.WorkPeriodPO">
        SELECT count(1) FROM maintenance_period
        WHERE change_time IS NULL
        AND del_flag = FALSE
        AND work_type = 'oilChange'
        <if test="nameCn != null and nameCn != ''">
            AND name_cn = #{nameCn}
        </if>
        <if test="componentType != null and componentType != ''">
            AND component_type = #{componentType}
        </if>
        <if test="vehicleCode != null and vehicleCode != ''">
            AND vehicle_code = #{vehicleCode}
        </if>
        <if test="location != null and location != ''">
            AND location = #{location}
        </if>
    </select>

    <select id="getInfoById" resultType="cc.crrc.manage.pojo.monitor.WorkPeriodPO"
            parameterType="java.lang.String">
        SELECT
        mp.id,
        mp.unique_id AS uniqueId,
        mp.line_id AS lineId,
        mp.vehicle_type_id AS vehicleTypeId,
        mp.vehicle_code AS vehicleCode,
        mp.location,
        mp.component_type AS componentType,
        mp.name_cn AS nameCn,
        mp.period,
        mp.mode,
        mp.operator,
        mp.operate_time AS operateTime,
        mp.warning_time AS warningTime,
        mp.change_time AS changeTime,
        mp.work_type AS workType,
        mp.remark,
        mp.create_by AS createBy,
        mp.create_time AS createTime,
        mp.modify_by AS modifyBy,
        mp.modify_time AS modifyTime,
        mp.initial_mileage AS initialMileage,
        mp.running_mileage AS runningMileage,
        mp.warning_mileage AS warningMileage
        FROM maintenance_period mp
        WHERE mp.id = #{id}
    </select>

    <select id="listOilChangeInfo" resultType="cc.crrc.manage.pojo.monitor.WorkPeriodVO"
            parameterType="cc.crrc.manage.pojo.monitor.WorkPeriodVO">
        SELECT
        A.*,
        case when A."mileagePercent" > A.percent THEN A."mileagePercent" ELSE A.percent END AS sort
        from
        (SELECT
        DISTINCT
        mp.id,
        mp.unique_id AS "uniqueId",
        mp.line_id AS "lineId",
        mp.vehicle_type_id AS "vehicleTypeId",
        mp.vehicle_code AS "vehicleCode",
        mp.location,
        mp.component_type AS "componentType",
        sd.label AS "componentTypeCn",
        mp.name_cn AS "nameCn",
        mp.period,
        mp.mode,
        mp.operator,
        mp.operate_time AS "operateTime",
        mp.warning_time AS "warningTime",
        mp.change_time AS "changeTime",
        mp.work_type AS "workType",
        mp.remark,
        mp.create_by AS "createBy",
        mp.create_time AS "createTime",
        mp.modify_by AS "modifyBy",
        mp.modify_time AS "modifyTime",
        mp.initial_mileage AS "initialMileage",
        mp.warning_mileage AS "warningMileage",
        case when  (MAX ( CASE dw.signal_name_en WHEN 'DDU_CudRunDistance' THEN dw.accumlation END )) is null then 0
        else (MAX ( CASE dw.signal_name_en WHEN 'DDU_CudRunDistance' THEN dw.accumlation END )) END - mp.initial_mileage AS "runningMileage",
        case when (round(
        (case when  (MAX ( CASE dw.signal_name_en WHEN 'DDU_CudRunDistance' THEN dw.accumlation END )) is null then 0
        else (MAX ( CASE dw.signal_name_en WHEN 'DDU_CudRunDistance' THEN dw.accumlation END )) END - mp.initial_mileage)/
        (case when mp.warning_mileage = 0 then 1 else mp.warning_mileage end)*100
        )) >=100 then 100
        else round(
        (case when  (MAX ( CASE dw.signal_name_en WHEN 'DDU_CudRunDistance' THEN dw.accumlation END )) is null then 0
        else (MAX ( CASE dw.signal_name_en WHEN 'DDU_CudRunDistance' THEN dw.accumlation END )) END - mp.initial_mileage)/
        (case when mp.warning_mileage = 0 then 1 else mp.warning_mileage end)*100
        ) end
        AS "mileagePercent",
        case when (round(
        date_part( 'day', CURRENT_DATE :: TIMESTAMP - mp.operate_time :: TIMESTAMP ) /
        date_part( 'day', mp.warning_time :: TIMESTAMP - mp.operate_time :: TIMESTAMP )*100
        )) >=100 then 100
        ELSE round(
        date_part( 'day', CURRENT_DATE :: TIMESTAMP - mp.operate_time :: TIMESTAMP ) /
        date_part( 'day', mp.warning_time :: TIMESTAMP - mp.operate_time :: TIMESTAMP )*100
        ) end AS percent
        FROM
        maintenance_period mp
        LEFT JOIN sys_dict sd ON sd.value = mp.component_type AND sd.type_code = 'oil_change_component'
        LEFT JOIN dw_realtime_train_status_by_day dw ON dw.train_id = mp.vehicle_code
        WHERE
        mp.work_type = 'oilChange'
        AND mp.del_flag = FALSE
        AND mp.change_time IS NULL
        <if test="lineId != null and lineId != ''">
            AND mp.line_id = #{lineId}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId != ''">
            AND mp.vehicle_type_id = #{vehicleTypeId}
        </if>
        <if test="vehicleCode != null and vehicleCode != ''">
            AND mp.vehicle_code = #{vehicleCode}
        </if>
        <if test="componentType != null and componentType != ''">
            AND mp.component_type = #{componentType}
        </if>
        <if test="location != null and location != ''">
            AND mp.location = #{location}
        </if>
        <if test="nameCn != null and nameCn != ''">
            AND mp.name_cn LIKE '%'||#{nameCn}||'%'
        </if>
        GROUP BY
        mp.id,sd.label
        ) A
        ORDER BY sort DESC,"mileagePercent" DESC,percent DESC ,"warningTime" ASC
    </select>

    <select id="getExcelData" resultType="cc.crrc.manage.pojo.monitor.OilChangeHistoryForExcelPO"
            parameterType="java.lang.String">
        SELECT
        mp.vehicle_code AS vehicleCode,
        mp.location,
        sd.label AS typeCn,
        mp.name_cn AS nameCn,
        mp.operator,
        mp.operate_time AS changeTime,
        mp.warning_time AS warningTime,
        mp.warning_mileage AS warningMileage,
        CASE mp.running_mileage WHEN 0 THEN
        (CASE WHEN (MAX ( CASE dw.signal_name_en WHEN 'DDU_CudRunDistance' THEN dw.accumlation END )) is null then 0
        ELSE (MAX ( CASE dw.signal_name_en WHEN 'DDU_CudRunDistance' THEN dw.accumlation END )) END - mp.initial_mileage)
        ELSE mp.running_mileage END AS runningMileage
        FROM maintenance_period mp
        LEFT JOIN sys_dict sd ON sd.value = mp.component_type AND sd.type_code = 'oil_change_component'
        LEFT JOIN dw_realtime_train_status_by_day dw ON dw.train_id = mp.vehicle_code
        WHERE mp.unique_id = #{id}
        GROUP BY mp.vehicle_code, mp."location", sd.label, mp.name_cn,
        mp."operator", mp.operate_time, mp.warning_time, mp.warning_mileage, mp.running_mileage, mp.initial_mileage
        ORDER BY mp.operate_time DESC
    </select>

    <select id="getOilChangeForExcel" resultType="cc.crrc.manage.pojo.excel.OilChangeForExcelPO">
        SELECT
        mp.id,
        mp.vehicle_code AS vehicleCode,
        mp.location,
        sd.label AS typeCn,
        mp.name_cn AS nameCn,
        mp.operate_time AS changeTime,
        mp.warning_time AS warningTime,
        mp.warning_mileage AS warningMileage,
        case when  (MAX ( CASE dw.signal_name_en WHEN 'DDU_CudRunDistance' THEN dw.accumlation END )) is null then 0
        else (MAX ( CASE dw.signal_name_en WHEN 'DDU_CudRunDistance' THEN dw.accumlation END )) END - mp.initial_mileage AS runningMileage,
        round(
        (case when  (MAX ( CASE dw.signal_name_en WHEN 'DDU_CudRunDistance' THEN dw.accumlation END )) is null then 0
        else (MAX ( CASE dw.signal_name_en WHEN 'DDU_CudRunDistance' THEN dw.accumlation END )) END - mp.initial_mileage)/
        (case when mp.warning_mileage = 0 then 1 else mp.warning_mileage end)*100
        ) AS mileagePercent,
        round(
        date_part( 'day', CURRENT_DATE :: TIMESTAMP - mp.operate_time :: TIMESTAMP ) /
        date_part( 'day', mp.warning_time :: TIMESTAMP - mp.operate_time :: TIMESTAMP )*100
        ) AS percent
        FROM
        maintenance_period mp
        LEFT JOIN sys_dict sd ON sd.value = mp.component_type AND sd.type_code = 'oil_change_component'
        LEFT JOIN dw_realtime_train_status_by_day dw ON dw.train_id = mp.vehicle_code
        WHERE
        mp.work_type = 'oilChange'
        AND mp.del_flag = FALSE
        AND mp.change_time IS NULL
        <if test="lineId != null and lineId != ''">
            AND mp.line_id = #{lineId}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId != ''">
            AND mp.vehicle_type_id = #{vehicleTypeId}
        </if>
        <if test="vehicleCode != null and vehicleCode != ''">
            AND mp.vehicle_code = #{vehicleCode}
        </if>
        <if test="type != null and type != ''">
            AND mp.component_type = #{type}
        </if>
        <if test="location != null and location != ''">
            AND mp.location = #{location}
        </if>
        <if test="countNum != null and countNum != '' and countNum == 1">
            limit 1
        </if>
        GROUP BY mp."id", sd.label
        ORDER BY
        mp.warning_time ASC
    </select>

    <select id="listOilChangeHistory" resultType="cc.crrc.manage.pojo.monitor.WorkPeriodVO">
        SELECT
        mp.id,
        mp.unique_id AS uniqueId,
        mp.line_id AS lineId,
        mp.vehicle_type_id AS vehicleTypeId,
        mp.vehicle_code AS vehicleCode,
        mp.location,
        mp.component_type AS componentType,
        sd.label AS componentTypeCn,
        mp.name_cn AS nameCn,
        mp.period,
        mp.mode,
        mp.operator,
        mp.operate_time AS operateTime,
        mp.warning_time AS warningTime,
        mp.change_time AS changeTime,
        mp.work_type AS workType,
        mp.remark,
        mp.create_by AS createBy,
        mp.create_time AS createTime,
        mp.modify_by AS modifyBy,
        mp.modify_time AS modifyTime,
        mp.initial_mileage AS initialMileage,
        mp.warning_mileage AS warningMileage,
        CASE mp.running_mileage WHEN 0 THEN
        (CASE WHEN (MAX ( CASE dw.signal_name_en WHEN 'DDU_CudRunDistance' THEN dw.accumlation END )) is null then 0
        ELSE (MAX ( CASE dw.signal_name_en WHEN 'DDU_CudRunDistance' THEN dw.accumlation END )) END - mp.initial_mileage)
        ELSE mp.running_mileage END AS runningMileage
        FROM
        maintenance_period mp
        LEFT JOIN sys_dict sd ON sd.value = mp.component_type AND sd.type_code = 'oil_change_component'
        LEFT JOIN dw_realtime_train_status_by_day dw ON dw.train_id = mp.vehicle_code
        WHERE
        mp.work_type = 'oilChange'
        AND mp.unique_id = #{id}
        GROUP BY
        mp.id,sd.label
        ORDER BY
        mp.change_time DESC
    </select>

    <select id="getLineIdByVehicleTypeId" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT
        line_id
        FROM mtr_vehicle_type
        WHERE
        del_flag = 0
        AND id = #{vehicleTypeId}
    </select>
    <select id="getMileageByTargetTime" resultType="java.lang.Long">
        SELECT
        MAX ( CASE WHEN dw.accumlation IS NULL THEN 0 ELSE dw.accumlation END ) AS accumlation
        FROM
        dw_realtime_train_status_by_day dw
        WHERE
        1 = 1
        AND dw.train_id = #{vehicleCode}
        AND to_number( dw.time, '9999999999' ) <![CDATA[<=]]> to_number( #{targetTime} :: TEXT, '9999999999' )
        AND dw.signal_name_en = 'DDU_CudRunDistance'
        GROUP BY
        time
        ORDER BY
        time DESC
        LIMIT 1
    </select>

</mapper>