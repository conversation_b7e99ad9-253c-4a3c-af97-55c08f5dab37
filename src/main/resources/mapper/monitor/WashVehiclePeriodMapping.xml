<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.monitor.WashVehiclePeriodMapping">

    <insert id="addWashVehicleInfo">
        INSERT INTO maintenance_period (
            id,
            unique_id,
            line_id,
            vehicle_type_id,
            vehicle_code,
            location,
            component_type,
            name_cn,
            period,
            mode,
            operator,
            operate_time,
            warning_time,
            change_time,
            work_type,
            remark,
            create_by,
            create_time
        ) VALUES (
            #{id},
            #{uniqueId},
            #{lineId},
            #{vehicleTypeId},
            #{vehicleCode},
            #{location},
            #{componentType},
            #{nameCn},
            #{period},
            #{mode},
            #{operator},
            #{operateTime},
            #{warningTime},
            #{changeTime},
            #{workType},
            #{remark},
            #{createBy},
            CURRENT_TIMESTAMP
        )
    </insert>

    <insert id="batchAddRecord">
        INSERT INTO maintenance_period(
        id,
        unique_id,
        line_id,
        vehicle_type_id,
        vehicle_code,
        location,
        component_type,
        name_cn,
        period,
        mode,
        operator,
        operate_time,
        warning_time,
        change_time,
        work_type,
        remark,
        create_by,
        create_time,
        modify_by,
        modify_time)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.uniqueId},
            #{item.lineId},
            #{item.vehicleTypeId},
            #{item.vehicleCode},
            #{item.location},
            #{item.componentType},
            #{item.nameCn},
            #{item.period},
            #{item.mode},
            #{item.operator},
            #{item.operateTime},
            #{item.warningTime},
            #{item.changeTime},
            #{item.workType},
            #{item.remark},
            #{item.createBy},
            CURRENT_TIMESTAMP,
            #{item.modifyBy},
            CURRENT_TIMESTAMP
            )
        </foreach>
    </insert>

    <update id="washVehicleHistory" parameterType="cc.crrc.manage.pojo.monitor.WorkPeriodPO">
        UPDATE maintenance_period
        SET change_time = CURRENT_TIMESTAMP,
            modify_by = #{modifyBy},
            modify_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <select id="checkVehicleUnique" resultType="java.lang.Integer"
            parameterType="cc.crrc.manage.pojo.monitor.WorkPeriodPO">
        SELECT count(1) FROM maintenance_period
        WHERE change_time IS NULL
        AND del_flag = FALSE
        AND work_type = 'washVehicle'
        <if test="vehicleCode != null and vehicleCode != ''">
            AND vehicle_code = #{vehicleCode}
        </if>
    </select>

    <select id="getInfoById" resultType="cc.crrc.manage.pojo.monitor.WorkPeriodPO"
            parameterType="java.lang.String">
        SELECT
        mp.id,
        mp.unique_id AS uniqueId,
        mp.line_id AS lineId,
        mp.vehicle_type_id AS vehicleTypeId,
        mp.vehicle_code AS vehicleCode,
        mp.mode,
        mp.period,
        mp.operator,
        mp.operate_time AS operateTime,
        mp.warning_time AS warningTime,
        mp.change_time AS changeTime,
        mp.work_type AS workType,
        mp.remark,
        mp.create_by AS createBy,
        mp.create_time AS createTime,
        mp.modify_by AS modifyBy,
        mp.modify_time AS modifyTime
        FROM maintenance_period mp
        WHERE mp.id = #{id}
    </select>

    <select id="listWashVehicleInfo" resultType="cc.crrc.manage.pojo.monitor.WorkPeriodVO"
            parameterType="cc.crrc.manage.pojo.monitor.WorkPeriodVO">
        SELECT
        mp.id,
        mp.unique_id AS uniqueId,
        mp.line_id AS lineId,
        mp.vehicle_type_id AS vehicleTypeId,
        mp.vehicle_code AS vehicleCode,
        mp.mode,
        mp.period,
        sd.label AS modeCn,
        mp.operator,
        mp.operate_time AS operateTime,
        mp.warning_time AS warningTime,
        mp.change_time AS changeTime,
        mp.work_type AS workType,
        mp.remark,
        mp.create_by AS createBy,
        mp.create_time AS createTime,
        mp.modify_by AS modifyBy,
        mp.modify_time AS modifyTime
        FROM
        maintenance_period mp
        LEFT JOIN sys_dict sd ON sd.value = mp.mode AND sd.type_code = 'wash_vehicle_mode'
        WHERE
        mp.work_type = 'washVehicle'
        AND mp.del_flag = FALSE
        AND mp.change_time IS NULL
        <if test="lineId != null and lineId != ''">
            AND mp.line_id = #{lineId}
        </if>
        <if test="vehicleCode != null and vehicleCode != ''">
            AND mp.vehicle_code = #{vehicleCode}
        </if>
        <if test="mode != null and mode != ''">
            AND mp.mode = #{mode}
        </if>
        ORDER BY
        mp.warning_time ASC
    </select>

    <select id="getExcelData" resultType="cc.crrc.manage.pojo.monitor.WashVehicleHistoryForExcelPO"
            parameterType="java.lang.String">
        SELECT
        mp.vehicle_code AS vehicleCode,
        sd.label AS modeCn,
        mp.operator,
        mp.operate_time AS changeTime,
        mp.warning_time AS warningTime
        FROM maintenance_period mp
        LEFT JOIN sys_dict sd ON sd.value = mp.mode AND sd.type_code = 'wash_vehicle_mode'
        WHERE mp.unique_id = #{id}
        ORDER BY mp.operate_time DESC
    </select>

    <select id="getWashVehicleForExcel" resultType="cc.crrc.manage.pojo.excel.WashVehicleForExcelPO">
        SELECT
        mp.id,
        mp.vehicle_code AS vehicleCode,
        sd.label AS modeCn,
        mp.operate_time AS changeTime,
        mp.warning_time AS warningTime
        FROM
        maintenance_period mp
        LEFT JOIN sys_dict sd ON sd.value = mp.mode AND sd.type_code = 'wash_vehicle_mode'
        WHERE
        mp.work_type = 'washVehicle'
        AND mp.del_flag = FALSE
        AND mp.change_time IS NULL
        <if test="lineId != null and lineId != ''">
            AND mp.line_id = #{lineId}
        </if>
        <if test="vehicleCode != null and vehicleCode != ''">
            AND mp.vehicle_code = #{vehicleCode}
        </if>
        <if test="mode != null and mode != ''">
            AND mp.mode = #{mode}
        </if>
        <if test="countNum != null and countNum != '' and countNum == 1">
            limit 1
        </if>
        ORDER BY
        mp.warning_time ASC
    </select>

    <update id="updateCurrentInfo" parameterType="cc.crrc.manage.pojo.monitor.WorkPeriodPO">
        UPDATE maintenance_period
        <set>
            <if test="mode != null and componentType != ''">
                mode = #{mode},
            </if>
            <if test="vehicleCode != null and vehicleCode != ''">
                vehicle_code = #{vehicleCode},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="operateTime != null">
                operate_time = #{operateTime},
            </if>
            <if test="warningTime != null">
                warning_time = #{warningTime},
            </if>
            <if test="period != null and period != ''">
                period = #{period},
            </if>
            modify_time = now(),
            modify_by = #{modifyBy}
        </set>
        WHERE id = #{id}
    </update>

    <update id="deleteWashVehicleInfo">
        UPDATE maintenance_period
        SET del_flag = TRUE,
            modify_by = #{userId},
            modify_time = CURRENT_TIMESTAMP
        WHERE unique_id = #{id}
    </update>

    <select id="listWashVehicleHistory" resultType="cc.crrc.manage.pojo.monitor.WorkPeriodVO">
        SELECT
        mp.id,
        mp.unique_id AS uniqueId,
        mp.line_id AS lineId,
        mp.vehicle_type_id AS vehicleTypeId,
        mp.vehicle_code AS vehicleCode,
        mp.location,
        mp.component_type AS componentType,
        mp.name_cn AS nameCn,
        mp.period,
        mp.mode,
        sd.label AS modeCn,
        mp.operator,
        mp.operate_time AS operateTime,
        mp.warning_time AS warningTime,
        mp.change_time AS changeTime,
        mp.work_type AS workType,
        mp.remark,
        mp.create_by AS createBy,
        mp.create_time AS createTime,
        mp.modify_by AS modifyBy,
        mp.modify_time AS modifyTime
        FROM
        maintenance_period mp
        LEFT JOIN sys_dict sd ON sd.value = mp.mode AND sd.type_code = 'wash_vehicle_mode'
        WHERE
        mp.work_type = 'washVehicle'
        AND mp.unique_id = #{id}
        ORDER BY
        mp.change_time DESC
    </select>

    <select id="getLineIdByVehicleCode" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT
        metro_line_id
        FROM mtr_vehicle
        WHERE
        del_flag = 0
        AND vehicle_code = #{vehicleCode}
    </select>
</mapper>