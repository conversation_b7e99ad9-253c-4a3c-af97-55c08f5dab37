<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.draw.DrawLineConfigMapping">
    <sql id="drawLineColumnAlias">
        t.id,
        t.segment_id AS segmentId,
        t.current_sta AS currentSta,
        t.coordinate,
        t.json,
        t.path,
        t.direction,
        t.line_id AS lineId,
        t.type,
        t.sort,
        t.create_by AS createBy,
        t.create_time AS createTime,
        t.modify_by AS modifyBy,
        t.modify_time AS modifyTime,
        t.del_flag AS delFlag
    </sql>


    <!--新增数据-->
    <insert id="addDrawLine">
        INSERT INTO draw_line_config (
            id,
            segment_id,
            current_sta,
            coordinate,
            json,
            path,
            direction,
            line_id,
            type,
            sort,
            create_by,
            create_time,
            modify_by,
            modify_time,
            del_flag
        )
        VALUES (
                   #{id},
                   #{segmentId},
                   #{currentSta},
                   #{coordinate},
                   #{json},
                   #{path},
                   #{direction},
                   #{lineId},
                   #{type},
                   #{sort},
                   #{createBy},
                   CURRENT_TIMESTAMP,
                   #{modifyBy},
                   CURRENT_TIMESTAMP,
                   #{delFlag}
               )
    </insert>

    <!--更新数据-->
    <update id="updateDrawLine">
        UPDATE draw_line_config
        <trim prefix="set" suffixOverrides=",">
            <if test="segmentId != null and segmentId != ''">
                segment_id = #{segmentId},
            </if>
            <if test="currentSta != null and currentSta != ''">
                current_sta = #{currentSta},
            </if>
            <if test="coordinate != null and coordinate != ''">
                coordinate = #{coordinate},
            </if>
            <if test="json != null">
                json = #{json},
            </if>
            <if test="path != null">
                path = #{path},
            </if>
            <if test="direction != null and direction != ''">
                direction = #{direction},
            </if>
            <if test="lineId != null and lineId != ''">
                line_id = #{lineId},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="sort != null and sort != ''">
                sort = #{sort},
            </if>
            <if test="modifyBy != null and modifyBy != ''">
                modify_by = #{modifyBy},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>
            <if test="delFlag != null and delFlag != ''">
                del_flag = #{delFlag},
            </if>
        </trim>
        WHERE
        id = #{id}
    </update>

    <select id="getDrawLineInfoById" resultType="cc.crrc.manage.pojo.draw.DrawLineConfigVO">
        SELECT
        <include refid="drawLineColumnAlias"/>,
        t1.name AS lineName,
        sd.label AS typeName,
        ms.name AS currentStaName
        FROM draw_line_config t
        LEFT JOIN mtr_line t1 ON t1.id = t.line_id
        LEFT JOIN sys_dict sd ON t.type = sd.value and sd.type_code='draw_line_type'
        LEFT JOIN mtr_station ms ON t.current_sta = ms.sta_id and ms.metro_line_id =  t.line_id
        WHERE
        t.del_flag = '0'
        and t.id = #{id}
    </select>

    <select id="getDrawLineListForConfig" resultType="cc.crrc.manage.pojo.draw.DrawLineConfigVO">
        SELECT
        <include refid="drawLineColumnAlias"/>,
        t1.name AS lineName,
        sd.label AS typeName,
        mts.name AS currentStaName
        FROM
        draw_line_config t
        LEFT JOIN mtr_line t1 ON t1.id = t.line_id
        LEFT JOIN sys_dict sd ON t.type = sd.value and sd.type_code='draw_line_type'
        LEFT JOIN mtr_station mts ON t.current_sta = mts.sta_id and t.line_id = mts.metro_line_id
        WHERE
        t.del_flag = '0'
        AND mts.del_flag = '0'
        <if test="direction != null and direction != ''">
            AND t.direction = #{direction}
        </if>
        <if test="lineId != null and lineId != ''">
            AND t.line_id = #{lineId}
        </if>
        <if test="type != null and type != ''">
            AND t.type = #{type}
        </if>
        ORDER BY
        t.sort ASC
    </select>
    <select id="getDrawLineInfo" resultType="cc.crrc.manage.pojo.draw.DrawLineConfigVO">
        SELECT
        <include refid="drawLineColumnAlias"/>,
        t1.name AS lineName,
        sd.label AS typeName,
        ms.name AS currentStaName
        FROM
        draw_line_config t
        LEFT JOIN mtr_line t1 ON t1.id = t.line_id
        LEFT JOIN sys_dict sd ON t.type = sd.value and sd.type_code='draw_line_type'
        LEFT JOIN mtr_station ms ON t.current_sta = ms.sta_id
        WHERE
        t.del_flag = '0'
        <if test="segmentId != null and segmentId != ''">
            AND t.segment_id = #{segmentId}
        </if>
        <if test="currentSta != null and currentSta != ''">
            AND t.current_sta = #{currentSta}
        </if>
        <if test="coordinate != null and coordinate != ''">
            AND t.coordinate  LIKE '%'||#{coordinate}||'%'
        </if>
        <if test="json != null and json != ''">
            AND t.json  LIKE '%'||#{json}||'%'
        </if>
        <if test="path != null and path != ''">
            AND t.path  LIKE '%'||#{path}||'%'
        </if>
        <if test="direction != null and direction != ''">
            AND t.direction = #{direction}
        </if>
        <if test="lineId != null and lineId != ''">
            AND t.line_id = #{lineId}
        </if>
        <if test="type != null and type != ''">
            AND t.type = #{type}
        </if>
        <if test="sort != null and sort != ''">
            AND t.sort = #{sort}
        </if>
        ORDER BY
        t.sort ASC
    </select>
    <select id="getDrawLineInfoList" resultType="cc.crrc.manage.pojo.draw.DrawLineConfigVO">
        SELECT
        <include refid="drawLineColumnAlias"/>,
        t1.name AS lineName,
        sd.label AS typeName,
        ms.name AS currentStaName
        FROM
        draw_line_config t
        LEFT JOIN mtr_line t1 ON t1.id = t.line_id
        LEFT JOIN sys_dict sd ON t.type = sd.value and sd.type_code='draw_line_type'
        LEFT JOIN mtr_station ms ON t.current_sta = ms.sta_id
        WHERE
        t.del_flag = '0'
        <if test="lineId != null and lineId != ''">
            AND t.line_id = #{lineId}
        </if>
        <if test="type != null and type != ''">
            AND t.type = #{type}
        </if>
        ORDER BY
        t.sort ASC
    </select>

    <insert id="batchInsert">
        insert into ekb_fault_type_reason
        (
        id,
        segment_id,
        current_sta,
        coordinate,
        json,
        path,
        direction,
        line_id,
        type,
        sort,
        create_by,
        create_time,
        modify_by,
        modify_time,
        del_flag
        )
        values
        <foreach collection="list" item="item" index= "index" separator =",">
            #{id},
            #{segmentId},
            #{currentSta},
            #{coordinate},
            #{json},
            #{path},
            #{direction},
            #{lineId},
            #{type},
            #{sort},
            #{createBy},
            CURRENT_TIMESTAMP,
            #{modifyBy},
            CURRENT_TIMESTAMP,
            #{delFlag}
        </foreach>
    </insert>


</mapper>