<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.BusDictTypeMapping">

    <resultMap type="cc.crrc.manage.pojo.BusDictType" id="BusDictTypeResult">
        <result property="id"    column="id"    />
        <result property="description"    column="description"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="lastModifyTime"    column="last_modify_time"    />
        <result property="lastModifyBy"    column="last_modify_by"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="typeLevel"    column="type_level"    />
    </resultMap>

    <sql id="selectBusDictTypeVo">
        select id, description, code, name, type_level from bus_dict_type
    </sql>

    <select id="selectBusDictTypeList" resultType="cc.crrc.manage.pojo.BusDictTypeVO">
        <include refid="selectBusDictTypeVo"/>
        <where>
            del_flag = '0'
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}::text, '%')</if>
        </where>
    </select>


    <insert id="insertBusDictType" parameterType="cc.crrc.manage.pojo.BusDictType">
        insert into bus_dict_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="description != null">description,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="lastModifyTime != null">last_modify_time,</if>
            <if test="lastModifyBy != null">last_modify_by,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="name != null">name,</if>
            <if test="typeLevel != null and typeLevel != ''">type_level,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="description != null">#{description},</if>
            <if test="createTime != null ">#{createTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="lastModifyTime != null">#{lastModifyTime},</if>
            <if test="lastModifyBy != null">#{lastModifyBy},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="name != null">#{name},</if>
            <if test="typeLevel != null and typeLevel != ''">#{typeLevel},</if>
        </trim>
    </insert>

    <update id="updateBusDictType" parameterType="cc.crrc.manage.pojo.BusDictType">
        update bus_dict_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="description != null">description = #{description},</if>
            <if test="createTime != null ">create_time = #{createTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="lastModifyBy != null">last_modify_by = #{lastModifyBy},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="name != null">name = #{name},</if>
            <if test="typeLevel != null and typeLevel != ''">type_level = #{typeLevel},</if>
            last_modify_time = CURRENT_TIMESTAMP
        </trim>
        where id = #{id}
    </update>

    <update id="deleteBusDictTypeByIds" parameterType="String">
        update bus_dict_type
        set
        del_flag = '1',
        last_modify_by = #{lastModifyBy},
        last_modify_time = #{lastModifyTime}
        where id = #{id}
    </update>

    <select id="listCodeByIds" resultType="string">
        select code from bus_dict_type
        where id = #{id}
    </select>

</mapper>