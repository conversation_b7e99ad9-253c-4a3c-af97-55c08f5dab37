<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.SysAreaMapping">

    <insert id="saveSysArea">
        insert into sys_location(
        id,
        parent_id,
        parent_ids,
        node_type,
        name_cn,
        name_en,
        code,
        sort_number,
        longitude,
        latitude,
        del_flag,
        create_by,
        create_time,
        modify_by,
        modify_time
        )
        values(
        #{id},#{parentId},#{parentIds},
        #{nodeType},#{nameCn},#{nameEn},#{code},#{sortNumber},#{longitude},#{latitude},
        #{delFlag},#{createBy},#{createTime},#{modifyBy},#{modifyTime}
        )
    </insert>

    <select id="getCountByNameCn" resultType="java.lang.Integer">
        select count(1) from sys_location where name_cn = #{nameCn} and del_flag = false
    </select>

    <update id="deleteSysArea">
        UPDATE sys_location
        <trim prefix="set" suffixOverrides=",">
            <if test="modifyBy != null and modifyBy != ''">
                modify_by = #{modifyBy},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>
            <if test="delFlag != null and delFlag != ''">
                del_flag = #{delFlag},
            </if>
        </trim>
        WHERE
        id = #{id}
    </update>

    <update id="updateSysArea">
        UPDATE sys_location
        <trim prefix="set" suffixOverrides=",">
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            <if test="parentIds != null and parentIds != ''">
                parent_ids = #{parentIds},
            </if>
            <if test="nodeType != null and nodeType != ''">
                node_type = #{nodeType},
            </if>
            <if test="nameCn != null and nameCn != ''">
                name_cn = #{nameCn},
            </if>
            <if test="nameEn != null and nameEn != ''">
                name_en = #{nameEn},
            </if>
            <if test="code != null and code != ''">
                code = #{code},
            </if>
            <if test="sortNumber != null">
                sort_number = #{sortNumber},
            </if>
            <if test="longitude != null and longitude != ''">
                longitude = #{longitude},
            </if>
            <if test="latitude != null and latitude != ''">
                latitude = #{latitude},
            </if>
            <if test="modifyBy != null and modifyBy !=''">
                modify_by = #{modifyBy},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>
            <if test="delFlag != null and delFlag != ''">
                del_flag = #{delFlag},
            </if>
        </trim>
        WHERE
        id = #{id}
    </update>
    <!-- 分页取得区域列表 -->
    <select id="getSysAreaList" resultType="cc.crrc.manage.pojo.SysLocationVO">
        select
        id,
        parent_id as parentId,
        parent_ids as parentIds,
        node_type as nodeType,
        name_cn as nameCn,
        name_en as nameEn,
        code,
        sort_number as sortNumber,
        longitude,
        latitude,
        del_flag as delFlag,
        create_by as createBy,
        create_time as createTime,
        modify_by as modifyBy,
        modify_time as modifyTime
        from
        sys_location
        where
        del_flag = false
        <if test="nameCn != null and nameCn != ''">
            and name_cn like '%'||#{nameCn}||'%'
        </if>
        order by
        sort_number
    </select>
<!--    &lt;!&ndash; 省级列表 &ndash;&gt;-->
<!--    <select id="getProvinceList" resultType="cc.crrc.manage.pojo.SysAreaVO">-->
<!--        SELECT-->
<!--            "areaId",-->
<!--            "areaCode",-->
<!--            "areaName",-->
<!--            "level",-->
<!--            "cityCode",-->
<!--            "center",-->
<!--            "parentId"-->
<!--        FROM-->
<!--            sys_area-->
<!--        WHERE-->
<!--            LEVEL = 1-->
<!--        ORDER BY-->
<!--            "areaCode"-->
<!--    </select>-->
    <!-- 查询市级列表 (带模糊查询) -->
    <select id="getCityListByAreaName" resultType="cc.crrc.manage.pojo.SysAreaVO">
        SELECT
            "areaId",
            "areaCode",
            "areaName",
            "level",
            "cityCode",
            "center",
            "parentId"
        FROM
            sys_area
        WHERE
            LEVEL = 2
            AND "areaName" like '%'||#{areaName}||'%'
        ORDER BY
            "areaCode"
    </select>
    <!--查询当前用户的区域列表-->
    <select id="getLocationList" resultType="cc.crrc.manage.pojo.SysLocationVO">
        SELECT location_."id",
               location_.parent_id   AS parentId,
               location_.parent_ids  AS parentIds,
               location_.node_type   AS nodeType,
               location_.name_cn     AS nameCn,
               location_.name_en     AS nameEn,
               location_.code,
               location_.sort_number AS sortNumber,
               location_.longitude,
               location_.latitude
        FROM sys_location location_,
             (
                 SELECT DISTINCT line.location_id
                 FROM mtr_line line
                 WHERE line."id" IN (
                     SELECT DISTINCT oml.metro_line_id
                     FROM mtr_org_metro_line oml
                     WHERE oml.organization_id IN
                           (
                WITH RECURSIVE T  AS (
                SELECT ID,
                parent_id
                FROM
                sys_organization
                WHERE
                ID IN ( SELECT organ_user.organization_id FROM sys_organization_user organ_user WHERE organ_user.user_id =#{userId} )
                AND del_flag = '0' UNION ALL
                SELECT
                T1.ID,
                T1.parent_id
                FROM
                sys_organization T1
                JOIN T ON T1.parent_id = T.ID
                AND T1.del_flag = '0'
                ) SELECT ID
                FROM  T
                )
        )

             ) lo
        WHERE location_."id" = lo.location_id and
              location_.del_flag = false
    </select>
    <!--查询当前区域下的线路列表-->
    <select id="getLineList" resultType="cc.crrc.manage.pojo.line.LineDTO">
        SELECT
            line.id,
            line.name,
            line.location_id as locationId,
            line.mileage,
            line.longitude,
            line.latitude,
            line.create_by as createBy,
            line.create_time as createTime,
            line.modify_by as modifyBy,
            line.modify_time as modifyTime,
            line.remark,
            line.color
        FROM mtr_line line
        WHERE
        line.del_flag = 0
        <if test="locationId != null">
            AND line.location_id = #{locationId}
        </if>
    </select>
    <!--查询当前线路下的车型列表-->
    <select id="getVehicleTypeList" resultType="cc.crrc.manage.pojo.mtr.MtrVehicleTypeVO">
        SELECT
            mvt.id,
            mvt.name AS name,
            mvt.TYPE AS type,
            mvt.marshalling_number AS marshallingNumber,
            mvt.manufacturer_id AS manufacturerId,
            mvt.comm_type AS commType,
            mvt.vehicle_type_code AS vehicleTypeCode,
            mvt.line_id AS lineId
        FROM mtr_vehicle_type mvt
        WHERE mvt.del_flag = 0
        <if test="lineId != null and lineId !=''">
            AND mvt.line_id = #{lineId}
        </if>
    </select>
    <!--查询车辆列表-->
    <select id="getVehicleList" resultType="cc.crrc.manage.pojo.mtr.MtrVehicleVO">
        SELECT
            mv.id AS id,
            mv.name_cn AS nameCn,
            mv.name_en AS nameEn,
            mv.vehicle_code AS vehicleCode,
            mv.metro_line_id AS metroLineId,
            mv.production_date AS productionDate,
            mv.delivery_date AS deliveryDate,
            mv.guarantee_period AS guaranteePeriod
        FROM mtr_vehicle mv
        WHERE mv.del_flag = 0
        <if test="lineId != null and lineId !=''">
            AND mv.metro_line_id = #{lineId}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId !=''">
            AND mv.ID IN (
                SELECT DISTINCT relation.vehicle_id
                FROM mtr_vehicle_type_relation relation
                WHERE relation.valid = 1 AND
                      relation.vehicle_type_id = #{vehicleTypeId} )
        </if>
    ORDER BY mv.vehicle_code
    </select>
</mapper>