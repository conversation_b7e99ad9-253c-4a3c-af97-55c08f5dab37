<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.SysMenuMapping">

    <insert id="saveSysMenu">
        INSERT INTO sys_menu (id,
                              code,
                              title,
                              parent_id,
                              href,
                              icon,
                              type,
                              enabled,
                              path,
                              create_by,
                              create_date,
                              update_by,
                              update_date,
                              tenant_id,
                              attr1,
                              sort,
                              redirecting,
                              before_enter,
                              props,
                              regex)
        VALUES (#{id}, #{code}, #{title}, #{parentId}, #{href}, #{icon}, #{type}, #{enabled}, #{path}, #{createBy},
                CURRENT_TIMESTAMP, #{updateBy}, CURRENT_TIMESTAMP, #{tenantId}, #{attr1}, #{sort}, #{redirecting},
                #{beforeEnter}, #{props}, #{regex})
    </insert>

    <update id="deleteSysMenuById">
        update
        sys_menu
        <trim prefix="set" suffixOverrides=",">
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            update_date = CURRENT_TIMESTAMP,
            <if test="delFlag != null and delFlag != ''">
                del_flag = #{delFlag}
            </if>
        </trim>
        <where>
            id = #{id}
        </where>

    </update>

    <delete id="deleteSysMenu">
        DELETE FROM sys_menu
        <where>
            id = #{id}
        </where>
    </delete>

    <update id="updateSysMenu">
        update
        sys_menu
        <trim prefix="set" suffixOverrides=",">

            <if test="code != null and code != ''">
                code = #{code},
            </if>
            <if test="title != null and title != ''">
                title = #{title},
            </if>
            <if test="parentId != null and parentId != ''">
                parent_id = #{parentId},
            </if>
            <if test="href != null and href != ''">
                href = #{href},
            </if>
            <if test="icon != null and icon != ''">
                icon = #{icon},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="path != null and path != ''">
                path = #{path},
            </if>
            <if test="enabled != null">
                enabled = #{enabled},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>

            update_date = CURRENT_TIMESTAMP,

            <if test="tenantId != null and tenantId != ''">
                tenant_id = #{tenantId},
            </if>
            <if test="delFlag != null and delFlag != ''">
                del_flag = #{delFlag},
            </if>
            <if test="attr1 != null and attr1 != ''">
                attr1 = #{attr1},
            </if>
            <if test="sort != null and sort != ''">
                sort = #{sort},
            </if>
            <if test="redirecting != null">
                redirecting = #{redirecting},
            </if>
            <if test="beforeEnter != null and beforeEnter != ''">
                before_enter = #{beforeEnter},
            </if>
            <if test="props != null and props != ''">
                props = #{props},
            </if>
            <if test="regex != null and regex != ''">
                regex = #{regex}
            </if>
        </trim>
        <where>
            id = #{id}
        </where>

    </update>

    <select id="selectSysMenuById" resultType="cc.crrc.manage.pojo.SysMenuVO">
        select
        id,
        code,
        title,
        parent_id as parentId,
        href,
        icon,
        type,
        path,
        enabled,
        create_by as createBy,
        create_date as createDate,
        update_by as updateBy,
        update_date as updateDate,
        tenant_id as tenantId,
        del_flag as delFlag,
        attr1,
        sort,
        redirecting,
        before_enter as beforeEnter,
        props,
        regex
        from sys_menu
        <where>
            id = #{id} and del_flag = '0';
        </where>
    </select>

    <select id="selectSysMenuTreeList" resultType="cc.crrc.manage.pojo.SysMenuVO">
        select id,
               code,
               title,
               parent_id    as parentId,
               href,
               icon,
               type,
               path,
               enabled,
               create_by    as createBy,
               create_date  as createDate,
               update_by    as updateBy,
               update_date  as updateDate,
               tenant_id    as tenantId,
               del_flag     as delFlag,
               attr1,
               sort,
               redirecting,
               before_enter as beforeEnter,
               props,
               regex
        from sys_menu
        WHERE del_flag = '0'
        ORDER BY sort
    </select>

    <select id="selectSysMenuTreeListByRoleId" resultType="cc.crrc.manage.pojo.SysMenuVO">
        SELECT
        sm.id,
        sm.code,
        sm.title,
        sm.parent_id as parentId,
        sm.href,
        sm.icon,
        sm.type,
        sm.path,
        sm.enabled,
        sm.create_by as createBy,
        sm.create_date as createDate,
        sm.update_by as updateBy,
        sm.update_date as updateDate,
        sm.tenant_id as tenantId,
        sm.del_flag as delFlag,
        sm.attr1,
        sm.sort,
        sm.redirecting,
        sm.before_enter as beforeEnter,
        sm.props,
        sm.regex
        FROM
        sys_menu sm
        LEFT JOIN sys_role_menu srm ON sm.ID = srm.menu_id
        WHERE
        srm.role_id= #{roleId}
        and
        del_flag = '0'
        ORDER BY sort
    </select>

    <!--重复校验-->
    <select id="selectMenu" resultType="cc.crrc.manage.pojo.SysMenuVO">
        SELECT
        id,
        code,
        title,
        parent_id as parentId,
        href,
        icon,
        type,
        path,
        enabled,
        create_by as createBy,
        create_date as createDate,
        update_by as updateBy,
        update_date as updateDate,
        tenant_id as tenantId,
        del_flag as delFlag,
        attr1,
        sort,
        redirecting,
        before_enter as beforeEnter,
        props,
        regex
        from sys_menu
        <where>
            <if test="code != null and code != ''">
                code = #{code}
            </if>
            and del_flag = '0'
        </where>
    </select>


    <select id="selectSysMenuTreeListByUsername" resultType="cc.crrc.manage.pojo.SysMenuVO">

        SELECT sm.ID,
               sm.code,
               sm.title,
               sm.parent_id    AS parentId,
               sm.href,
               sm.icon,
               sm.TYPE,
               sm.PATH,
               sm.enabled,
               sm.create_by    AS createBy,
               sm.create_date  AS createDate,
               sm.update_by    AS updateBy,
               sm.update_date  AS updateDate,
               sm.tenant_id    AS tenantId,
               sm.del_flag     AS delFlag,
               sm.attr1,
               sm.sort,
               sm.redirecting,
               sm.before_enter AS beforeEnter,
               sm.props,
               sm.regex
        FROM sys_menu sm
        WHERE
            sm.del_flag = '0'
            <if test='userId != "0"'>
               AND sm.ID IN (
                SELECT DISTINCT srm.menu_id
                FROM sys_user su,sys_role_user sru,sys_role_menu srm
                WHERE su.ID = sru.user_id
                AND sru.role_id = srm.role_id
                AND su.id = #{userId}
                )
            </if>

    </select>


    <select id="selectSysMenuTreeListByHomePage" resultType="cc.crrc.manage.pojo.SysMenuVO">

        SELECT
        sm.ID,
        sm.code,
        sm.title,
        sm.parent_id AS parentId,
        sm.href,
        sm.icon,
        sm.TYPE,
        sm.PATH,
        sm.enabled,
        sm.create_by AS createBy,
        sm.create_date AS createDate,
        sm.update_by AS updateBy,
        sm.update_date AS updateDate,
        sm.tenant_id AS tenantId,
        sm.del_flag AS delFlag,
        sm.attr1,
        sm.sort,
        sm.redirecting,
        sm.before_enter AS beforeEnter,
        sm.props,
        sm.regex
        FROM
        sys_menu sm
        WHERE
        sm.ID IN
        <foreach collection="MenuIds" item="MenuId" index="index" open="(" separator="," close=")">
            #{MenuId}
        </foreach>


    </select>
    <select id="getParentMenuForAccess" resultType="cc.crrc.manage.pojo.SysMenuVO">
        WITH RECURSIVE r AS (
            SELECT *
            FROM sys_menu
            WHERE id = #{id}
            union ALL
            SELECT t.*
            FROM sys_menu t,
                 r
            WHERE t.id = r.parent_id
        )
        SELECT *
        FROM r
        where code!= 'base'
        ORDER BY parent_id
            LIMIT 1
    </select>


</mapper>