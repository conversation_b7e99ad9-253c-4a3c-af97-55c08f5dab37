<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.UserManageMapping">
    <!--通过username取得用户-->
    <select id="getUserByUsername" resultType="cc.crrc.manage.pojo.User">
        SELECT id,username,password,name,birthday,address,mobile_phone AS mobilePhone,tel_phone AS telPhone,email,sex,type,remarks,tenant_id as tenantId, organization_id as organizationId,is_super_admin as isSuperAdmin,role_id AS roleId,image
        FROM sys_user
        WHERE username = #{username} and del_flag = '0'
    </select>
    <!--通过id取得用户-->
    <select id="getUserById" resultType="cc.crrc.manage.pojo.User">
        SELECT id,username,password,name,birthday,address,mobile_phone AS mobilePhone,tel_phone AS telPhone ,email,sex,type,remarks,tenant_id as tenantId, organization_id as organizationId,is_super_admin as isSuperAdmin,role_id roleId,image
        FROM sys_user
        WHERE id = #{id} and del_flag = '0'
    </select>
    <!--通过username取得用户数量（用作更新用户时校验用户名重复）-->
    <select id="checkUsernameExist" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            sys_user
        WHERE
            (name = #{name} or
            username = #{username}) AND
            id != #{id} AND
            del_flag = '0'
    </select>
    <!--分页取得全部用户-->
    <select id="listUser" resultType="cc.crrc.manage.pojo.User">
        SELECT id,username,password,name,birthday,address,mobile_phone AS mobilePhone,tel_phone AS telPhone,email,sex,type,remarks,tenant_id as tenantId, organization_id as organizationId,is_super_admin as isSuperAdmin,role_id roleId,image
        FROM sys_user
        WHERE del_flag = '0'
        <if test="username != null and username != ''">
            AND username LIKE '%'||#{username}||'%'
        </if>
        <if test="name != null and name != ''">
            AND name LIKE '%'||#{name}||'%'
        </if>
        ORDER BY
        update_date DESC
    </select>
    <!--根据部门id取得用户数量-->
    <select id="checkOrganizationExistUser" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            sys_user
        WHERE
            organization_id = #{organizationId} AND
            del_flag = '0'
    </select>
    <!--新增用户-->
    <insert id="addNewUser">
        INSERT INTO
        sys_user
        (id,username,name,password,birthday,address,mobile_phone,
        tel_phone,email,sex,type,remarks,create_by,create_date,
        update_by,update_date,del_flag,tenant_id,organization_id,
        is_super_admin,role_id,image,user_role_ids)
        VALUES
        (#{id},#{username},#{name},#{password},#{birthday},#{address},
        #{mobilePhone},#{telPhone},#{email},#{sex},#{type},#{remarks},
        #{createBy},#{createDate},#{updateBy},#{updateDate},#{delFlag},
        #{tenantId},#{organizationId},#{isSuperAdmin},#{roleId},#{image},#{userRoleIds})
    </insert>
    <!--更新用户-->
    <update id="updateUser">
        UPDATE
        sys_user
        <trim prefix="set" suffixOverrides=",">
            <if test="username != null and username != ''">
                username = #{username},
            </if>
            <if test="password != null and password != ''">
                password = #{password},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="birthday != null and birthday != ''">
                birthday = #{birthday},
            </if>
            <if test="address != null and address != ''">
                address = #{address},
            </if>
            <if test="mobilePhone != null and mobilePhone != ''">
                mobile_phone = #{mobilePhone},
            </if>
            <if test="telPhone != null and telPhone != ''">
                tel_phone = #{telPhone},
            </if>

            email = #{email},

            <if test="sex != null and sex != ''">
                sex = #{sex},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>

            remarks = #{remarks},

            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateDate != null">
                update_date = #{updateDate},
            </if>
            <if test="delFlag != null and delFlag != ''">
                del_flag = #{delFlag},
            </if>
            <if test="tenantId != null and tenantId != ''">
                tenant_id = #{tenantId},
            </if>
            <if test="organizationId != null and organizationId != ''">
                organization_id = #{organizationId},
            </if>
            <if test="isSuperAdmin != null and isSuperAdmin != ''">
                is_super_admin = #{isSuperAdmin},
            </if>
            <if test="roleId != null and roleId != ''">
                role_id = #{roleId},
            </if>
            <if test="image != null and image != ''">
                image = #{image},
            </if>
            <if test="userRoleIds != null and userRoleIds != ''">
                user_role_ids = #{userRoleIds}
            </if>
        </trim>
        WHERE
        id = #{id}
    </update>
    <!--用户中心 -修改用户信息-->
    <update id="updateUserForPersonalCenter">
        UPDATE
        sys_user
        <trim prefix="set" suffixOverrides=",">
            <if test="password != null and password != ''">
                password = #{password},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="birthday != null and birthday != ''">
                birthday = #{birthday},
            </if>
            <if test="address != null and address != ''">
                address = #{address},
            </if>
            <if test="mobilePhone != null">
                mobile_phone = #{mobilePhone},
            </if>
            <if test="telPhone != null">
                tel_phone = #{telPhone},
            </if>
            <if test="email != null">
                email = #{email},
            </if>
            <if test="sex != null and sex != ''">
                sex = #{sex},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="remarks != null and remarks != ''">
                remarks = #{remarks},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="updateDate != null">
                update_date = #{updateDate},
            </if>
            <if test="organizationId != null and organizationId != ''">
                organization_id = #{organizationId},
            </if>
            <if test="image != null and image != ''">
                image = #{image}
            </if>
            <if test="userRoleIds != null and userRoleIds != ''">
                user_role_ids = #{userRoleIds}
            </if>
        </trim>
        WHERE
        id = #{id}
    </update>

    <select id="getRoleIdsById" resultType="cc.crrc.manage.pojo.User">
      SELECT
	t1.ID,
	t1.update_date AS updateDate,
	t1.username,
	t1.image,
	t1.PASSWORD AS PASSWORD,
	t1.NAME AS NAME,
	t1.birthday AS birthday,
	t1.address AS address,
	t1.mobile_phone AS mobilePhone,
	t1.tel_phone AS telPhone,
	t1.email AS email,
	t1.sex AS sex,
	t1.TYPE AS TYPE,
	t1.remarks AS remarks,
	t1.tenant_id AS tenantId,
	t1.organization_id AS organizationId,
	t1.is_super_admin AS isSuperAdmin,
	t1.role_id AS roleId,
	t1.create_date AS createDate,
	array_to_string(ARRAY(SELECT distinct UNNEST (ARRAY_AGG(t5.role_name))),',') roleName,
	array_to_string(ARRAY(SELECT distinct UNNEST (ARRAY_AGG(t5.role_code))),',') roleCode,
	array_to_string(ARRAY(SELECT distinct UNNEST (ARRAY_AGG(t3.NAME))),',') organizationNames,
	array_to_string(ARRAY(SELECT distinct UNNEST (ARRAY_AGG(t3.ID))),',') organizationIds
FROM
	sys_user t1
	LEFT JOIN sys_role_user t2 ON t2.user_id = t1.ID
	LEFT JOIN sys_role t5 ON t5.ID = t2.role_id
	LEFT JOIN sys_organization_user t4 ON t4.user_id = t1.ID
	LEFT JOIN sys_organization t3 ON t3.ID = t4.organization_id
WHERE
	t1.del_flag = '0'
	and t1.id = #{id}
GROUP BY
	t1.ID,
	t1.update_date
ORDER BY
	t1.update_date DESC
    </select>
</mapper>