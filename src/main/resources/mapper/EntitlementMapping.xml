<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.EntitlementMapping">

    <select id="getAllMenu" resultType="cc.crrc.manage.pojo.Menu">
        SELECT
            id,
            code,
            title,
            sort,
            parent_id AS parentId,
            href,
            icon,
            "type",
            "path",
            "enabled",
            create_by AS createBy,
            create_date AS createDate,
            update_by AS updateBy,
            update_date AS updateDate,
            tenant_id AS tenantId,
            del_flag AS delFlag,
            attr1 as component
        FROM
            sys_menu
        where del_flag='0'
    </select>

    <select id="getMenuByUserId" resultType="cc.crrc.manage.pojo.Menu">
-- 2019/11/06 根据角色用户关系多对多需求修改的sql
           SELECT
             DISTINCT
	       M.ID,
	       M.code,
	       M.title,
	       M.parent_id AS parentId,
	       M.href,
	       <PERSON>.icon,
	       <PERSON><PERSON>,
	       M.sort,
	       M.PATH,
	       M.enabled,
	       M.create_by AS createBy,
	       M.create_date AS createDate,
	       M.update_by AS updateBy,
	       M.update_date AS updateDate,
	       M.tenant_id AS tenantId,
	       M.del_flag AS delFlag,
	       M.attr1 AS component
           FROM
	         sys_menu M
           WHERE
	         M.ID IN
	       (SELECT DISTINCT menu_id FROM sys_role_menu WHERE role_id IN
	       (SELECT role_id FROM sys_role_user WHERE user_id = #{userId} ))
	       AND M.del_flag = '0'
           ORDER BY
	         M.ID
    </select>


    <select id="getAllElement" resultType="cc.crrc.manage.pojo.Element">
        SELECT DISTINCT
            se.element_code AS elementCode,
            se.element_type AS elementType,
            se.element_name AS elementName,
            se.element_uri AS elementUri,
            se.menu_id AS menuId,
            se.parent_id AS parentId,
            se.path,
            se. METHOD,
            se. tenant_id,
            M .title AS menuName
        FROM
            sys_element se
        INNER JOIN sys_menu M ON se.menu_id = M . ID
        WHERE
            se.del_flag = '0'
    </select>
    <select id="getAllElementByUserId" resultType="cc.crrc.manage.pojo.Element">
--         2019/11/06 根据角色用户关系多对多需求修改的sql
        SELECT DISTINCT
	      se.ID AS ID,
	      se.element_code AS elementCode,
	      se.element_type AS elementType,
	      se.element_name AS elementName,
	      se.element_uri AS elementUri,
	      se.METHOD AS METHOD,
	      M.title AS menuName
        FROM
	      sys_element se
	    LEFT JOIN sys_menu M ON se.menu_id = M.ID
        WHERE
	      se.ID IN ( SELECT DISTINCT element_id FROM sys_element_role WHERE role_id IN ( SELECT role_id FROM sys_role_user WHERE user_id = #{userId} ) )
	    AND se.del_flag = '0'
	    AND m.del_flag = '0'
        ORDER BY
	    se.ID
    </select>
    <!--根据默认首页菜单id查询菜单信息 zhangzhijian 2020-6-22 -->
    <select id="findMenuByHomeMenuId" resultType="cc.crrc.manage.pojo.Menu">
        SELECT DISTINCT
	       sm.id,
	       sm.code,
	       sm.title,
	       sm.parent_id AS parentId,
	       sm.href,
	       sm.icon,
	       sm.TYPE,
	       sm.sort,
	       sm.PATH,
	       sm.enabled,
	       sm.create_by AS createBy,
	       sm.create_date AS createDate,
	       sm.update_by AS updateBy,
	       sm.update_date AS updateDate,
	       sm.tenant_id AS tenantId,
	       sm.del_flag AS delFlag,
	       sm.attr1 AS component
        FROM
	        sys_menu sm
        WHERE
         	del_flag = '0'
        AND
	        sm.id = #{homeMenuId}

    </select>
    <!--根据登录用户id查询用户角色信息 zhangzhijian 2020-6-22 -->
    <select id="findRoleIdByUserId" resultType="cc.crrc.manage.pojo.SysRoleVO">
		SELECT
			sr.id,
			sr.role_code AS roleCode,
			sr.role_name AS roleName,
			sr.parent_id AS parentId,
			sr.role_path AS rolePath,
			sr.role_type AS roleType,
			sr.remarks,
			sr.create_by AS createBy,
			sr.create_date AS createDate,
			sr.update_by AS updateBy,
			sr.update_date AS updateDate,
			sr.tenant_id AS tenatId,
			sr.del_flag AS delFlag,
			sr.data_permission AS dataPermission,
			sr.selected_menu_ids AS selectedMenuIds,
			sr.home_menu_id AS homeMenuId
        FROM
        	sys_role sr
        WHERE
        	sr.id = (SELECT split_part(su.user_role_ids,';',1) FROM sys_user su WHERE del_flag = '0' AND su.id = #{userId})
	    ORDER BY
	        sr.data_permission DESC
	</select>

</mapper>
