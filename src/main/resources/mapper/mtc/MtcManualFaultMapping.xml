<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtc.MtcManualFaultMapping">

	<select id="getManualFaultList" resultType="cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO">
		SELECT
		mmaw.id AS id,
		mmaw.vehicle_id AS vehicleId,
		mmaw.start_time AS startTime,
		mmaw.end_time AS endTime,
		mmaw.fault_name_cn AS faultNameCn,
		mmaw.fault_level AS faultLevel,
		mv.vehicle_code AS vehicleCode,
		mmaw.fault_source AS faultSource
		FROM mtc_manual_alarm_warning mmaw
		LEFT JOIN mtr_vehicle mv ON mv.id = mmaw.vehicle_id
		WHERE
		mmaw.status = '0'
		<if test="faultSource != null and faultSource != ''">
			AND mmaw.fault_source = #{faultSource}
		</if>
		<if test="vehicleCode != null and vehicleCode != ''">
			AND mv.vehicle_code = #{vehicleCode}
		</if>
		<if test="faultNameCn != null and faultNameCn != ''">
			AND mmaw.fault_name_cn like '%' || #{faultNameCn} || '%'
		</if>
		<if test="vehicleTypeId != null and vehicleTypeId != ''">
			AND mmaw.vehicle_type_id = #{vehicleTypeId}
		</if>
		<if test="lineId != null and lineId != ''">
			AND mmaw.line_id = #{lineId}
		</if>
	</select>



</mapper>