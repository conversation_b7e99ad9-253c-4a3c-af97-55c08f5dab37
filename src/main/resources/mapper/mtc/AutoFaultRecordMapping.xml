<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtc.AutoFaultRecordMapping">

	<insert id="saveFault">
		INSERT INTO mtc_alarm_warning
		(
			vehicle_id,
			fault_type_key,
			component_id,
			start_time,
			status,
			confirm,
			speed,
			mileage,
			current_station_id,
			next_station_id,
			level,
			active_cabin,
			mode,
			suppressed_by,
			suppressed_status
		)values(
			#{vehicleId},
			#{faultTypeKey},
			#{componentId},
			#{startTime},
			#{status},
			#{confirm},
			#{speed},
			#{mileage},
			#{currentStationId},
			#{nextStationId},
			#{level},
			#{activeCabin},
			#{mode},
			#{suppressedBy},
			#{suppressedStatus}
		)
	</insert>
	
	<update id="changeFault">
		UPDATE mtc_alarm_warning SET
			status = #{status},
			confirm = #{confirm},
			end_time = #{endTime}
		WHERE
			vehicle_id = #{vehicleId}
			AND fault_type_key = #{faultTypeKey}
	</update>
	
	<select id="getSnowSlide" resultType="cc.crrc.manage.pojo.ekb.EkbFaultSnowSlidePO">
		SELECT 
			id,
			vehicle_type_id AS vehicleTypeId,
			parent_fault_type_key AS parentFaultTypeKey,
			fault_type_key AS faultTypeKey,
			correlation_time AS correlationTime
		FROM
			fault_snow_slide
		WHERE
			(parent_fault_type_key = #{faultTypeKey}
		OR
			fault_type_key = #{faultTypeKey})
		AND
			enable = true
	</select>
	
	<update id="updateSnowSlide">
		UPDATE 	mtc_alarm_warning SET
			suppressed_by = #{autoFaultRecordDTO.faultTypeKey},
			suppressed_status = true
		FROM(
		SELECT
			id, 
			vehicle_id, 
			fault_type_key, 
			start_time
		FROM
		mtc_alarm_warning
		WHERE
			vehicle_id = #{autoFaultRecordDTO.vehicleId}
		AND
			end_time IS NULL
		And
			suppressed_status = false
		AND
			<foreach collection="keys" open="(" close=")" separator="or" item="item">
				(fault_type_key = #{item.faultTypeKey}
			AND
				abs(tstzrange_subdiff(#{autoFaultRecordDTO.startTime}, start_time)) &lt;= #{item.correlationTime} / 1000)
        	</foreach>
        ) tmp
		WHERE
		mtc_alarm_warning.id = tmp.id
	</update>
	
	<select id="getParentFaultTypeKey" resultType="cc.crrc.manage.pojo.ekb.EkbFaultSnowSlidePO">
		SELECT
			id, 
			vehicle_id AS vehicleId, 
			fault_type_key AS faultTypeKey, 
			start_time AS startTime
		FROM
		mtc_alarm_warning
		WHERE
			vehicle_id = #{autoFaultRecordDTO.vehicleId}
		AND
			end_time IS NULL
		AND
			<foreach collection="keys" open="(" close=")" separator="or" item="item">
				(fault_type_key = #{item.parentFaultTypeKey}
			AND
				abs(tstzrange_subdiff(#{autoFaultRecordDTO.startTime}, start_time)) &lt;= #{item.correlationTime} / 1000)
        	</foreach>
		ORDER BY start_time ASC
		LIMIT 1
	</select>
	
	<select id="getChildFault" resultType="cc.crrc.manage.pojo.mtc.AutoFaultRecordDTO">
		SELECT
			id, 
			vehicle_id AS vehicleId, 
			fault_type_key AS faultTypeKey, 
			start_time AS startTime
		FROM
			mtc_alarm_warning
		WHERE
			vehicle_id = #{vehicleId}
		AND
			end_time IS NULL
		AND
			suppressed_by = #{faultTypeKey}
	</select>
	
	<select id="getParentFault" resultType="cc.crrc.manage.pojo.ekb.EkbFaultSnowSlidePO">
		SELECT 
			id,
			vehicle_type_id AS vehicleTypeId,
			parent_fault_type_key AS parentFaultTypeKey,
			fault_type_key AS faultTypeKey,
			correlation_time AS correlationTime
		FROM
			fault_snow_slide
		WHERE
			fault_type_key = #{childFault.faultTypeKey}
		AND
			parent_fault_type_key != #{autoFaultRecordDTO.faultTypeKey}
		AND
			enable = true
	</select>
	
	<update id="rmSnowSlide">
		UPDATE mtc_alarm_warning set
			suppressed_by = null, 
			suppressed_status = false
		WHERE
			id = #{id}
	</update>
	
	<update id="updateSlideList">
		UPDATE 	mtc_alarm_warning SET
			suppressed_by = tmp.fault_type_key,
			suppressed_status = true
		FROM(
		SELECT
			id, 
			vehicle_id, 
			fault_type_key, 
			start_time
		FROM
		mtc_alarm_warning
		WHERE
			vehicle_id = #{childFault.vehicleId}
		AND
			end_time IS NULL
		AND
			suppressed_status = false
		AND
			<foreach collection="keys" open="(" close=")" separator="or" item="item">
				(fault_type_key = #{item.parentFaultTypeKey}
			AND
				abs(tstzrange_subdiff(#{childFault.startTime}, start_time)) &lt;= #{item.correlationTime} / 1000)
        	</foreach>
		ORDER BY start_time ASC
		LIMIT 1
		) tmp
		WHERE
		mtc_alarm_warning.id = #{childFault.id}
	</update>
</mapper>