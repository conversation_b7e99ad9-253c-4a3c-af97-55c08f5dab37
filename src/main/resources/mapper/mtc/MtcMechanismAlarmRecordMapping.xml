<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtc.MtcMechanismAlarmRecordMapping">
    <sql id="MtcMechanismAlarmRecordAlias">
        t1.id,
        t1.vehicle_id AS vehicleId,
        t1.alarm_rule_id AS alarmRuleId,
        t1.alarm_snapshot AS alarmSnapshot,
        t1.start_time AS startTime,
        t1.end_time AS endTime,
        t1.status,
        t1.modify_by AS modifyBy,
        t1.modify_time AS modifyTime
    </sql>

    <select id="listMtcMechanismAlarmRecord" resultType="cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRecordVO">
        SELECT
        <include refid="MtcMechanismAlarmRecordAlias"/>,
        replace(cast(t1.end_time-t1.start_time as VARCHAR),'days','天') AS duration,
        t2.location,
        t2.subsystem,
        t8.label,
        t2.name_cn AS faultNameCn,
        t2.name_en AS faultNameEn,
        t2.fault_code AS faultCode,
        t2.fault_level AS faultLevel,
        t9.label AS  faultLevelName,
        t3.name_cn AS vehicleNameCn,
        t3.name_en AS vehicleNameEn,
        t3.vehicle_code AS vehicleCode,
        t4.name AS lineName,
        t4.id AS lineId,
        t6.id AS vehicleTypeId
        FROM
        mtc_mechanism_alarm_record t1
        LEFT JOIN mtc_mechanism_alarm_rule t7
        ON t1.alarm_rule_id = t7.id
        LEFT JOIN ekb_fault_type t2
        ON t7.fault_type_key = t2.fault_type_key
        LEFT JOIN mtr_vehicle t3
        ON t1.vehicle_id =t3.id
        LEFT JOIN mtr_line t4
        ON t3.metro_line_id = t4.id
        LEFT JOIN mtr_vehicle_type t6
        ON t6.id = t7.vehicle_type_id
        LEFT JOIN sys_dict t8
        ON t8.type_id = '28' and t8.value = t2.subsystem
        LEFT JOIN sys_dict t9 ON t9.type_id = '59' AND cast(t9.VALUE as int) = t2.fault_level
        WHERE
        t3.del_flag = 0
        <if test="lineId != null and lineId != ''">
            AND  t4.id = #{lineId}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId != ''">
            AND t6.id = #{vehicleTypeId}
        </if>
        <if test="vehicleCode != null and vehicleCode != ''">
            AND t3.vehicle_code = #{vehicleCode}
        </if>
        <if test="subsystem != null and subsystem != ''">
            AND t2.subsystem = #{subsystem}
        </if>
        <if test="startTime != null">
            AND t1.start_time &gt; #{startTime}
        </if>
        <if test="startTimeEnd != null">
            AND t1.start_time &lt; #{startTimeEnd}
        </if>
        <if test="location != null and location != ''">
            AND t2.location = #{location}
        </if>
        <if test="faultCode != null and faultCode != ''">
            AND t2.fault_code LIKE '%'||#{faultCode}||'%'
        </if>
        <if test="faultNameCn != null and faultNameCn != ''">
            and t2.name_cn LIKE '%'||#{faultNameCn}||'%'
        </if>
        <if test="faultLevel != null">
            AND t2.fault_level = #{faultLevel}
        </if>
        <if test="endStatus==1 ">
            AND t1.end_time is not null
        </if>
        <if test="endStatus==0 ">
            AND t1.end_time is null
        </if>
        ORDER BY
        t1.start_time DESC,t1.id DESC
    </select>
    <select id="countMtcMechanismAlarmRecordByLineId" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM
        mtc_mechanism_alarm_record t1
        LEFT JOIN mtr_vehicle t3
        ON t1.vehicle_id =t3.id
        LEFT JOIN mtr_line t4
        ON t3.metro_line_id = t4.id
        WHERE
        t1.end_time IS NULL AND
        t3.del_flag = 0
        <if test="lineId != null and lineId != ''">
            AND  t4.id = #{lineId}
        </if>
    </select>
    <select id="findMechanismAlarmFaultListByLineId" resultType="cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRecordVO">
        SELECT
        <include refid="MtcMechanismAlarmRecordAlias"/>,
        t2.location,
        t2.subsystem,
        t2.name_cn AS faultNameCn,
        t2.name_en AS faultNameEn,
        t2.fault_code AS faultCode,
        t2.fault_level AS faultLevel,
        t3.name_cn AS vehicleNameCn,
        t3.name_en AS vehicleNameEn,
        t3.vehicle_code AS vehicleCode,
        t4.name AS lineName,
        t4.id AS lineId,
        t6.id AS vehicleTypeId
        FROM
        mtc_mechanism_alarm_record t1
        LEFT JOIN mtc_mechanism_alarm_rule t7
        ON t1.alarm_rule_id = t7.id
        LEFT JOIN ekb_fault_type t2
        ON t7.fault_type_key = t2.fault_type_key
        LEFT JOIN mtr_vehicle t3
        ON t1.vehicle_id =t3.id
        LEFT JOIN mtr_line t4
        ON t3.metro_line_id = t4.id
        LEFT JOIN mtr_vehicle_type t6
        ON t6.id = t7.vehicle_type_id
        WHERE
        t3.del_flag = 0
        <if test="lineId != null and lineId != ''">
            AND  t4.id = #{lineId}
        </if>
        ORDER BY
        t1.start_time DESC,t1.end_time DESC
    </select>
    <select id="findMechanismAlarmFaultListByVehicleCode" resultType="cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRecordVO">
        SELECT
        <include refid="MtcMechanismAlarmRecordAlias"/>,
        t2.location,
        t2.subsystem,
        t9.label,
        t2.name_cn AS faultNameCn,
        t2.name_en AS faultNameEn,
        t2.fault_code AS faultCode,
        t2.fault_level AS faultLevel,
        t8.label AS  faultLevelName,
        t3.name_cn AS vehicleNameCn,
        t3.name_en AS vehicleNameEn,
        t3.vehicle_code AS vehicleCode,
        t4.name AS lineName,
        t4.id AS lineId,
        t6.id AS vehicleTypeId,
        t7.name AS mechanismAlarmRuleName,
        t7.content AS mechanismAlarmRuleContent
        FROM
        mtc_mechanism_alarm_record t1
        LEFT JOIN mtc_mechanism_alarm_rule t7
        ON t1.alarm_rule_id = t7.id
        LEFT JOIN ekb_fault_type t2
        ON t7.fault_type_key = t2.fault_type_key
        LEFT JOIN mtr_vehicle t3
        ON t1.vehicle_id =t3.id
        LEFT JOIN mtr_line t4
        ON t3.metro_line_id = t4.id
        LEFT JOIN mtr_vehicle_type t6
        ON t6.id = t7.vehicle_type_id
        LEFT JOIN sys_dict t8 ON t8.type_id = '59' AND cast(t8.VALUE as int) = t2.fault_level
        LEFT JOIN sys_dict t9
        ON t9.type_id = '28' and t9.value = t2.subsystem
        WHERE
        t3.del_flag = 0 AND
        t1.end_time IS NULL
        <if test="vehicleCode != null and vehicleCode != ''">
            AND t3.vehicle_code = #{vehicleCode}
        </if>
        ORDER BY
        t1.start_time DESC,t1.end_time DESC
    </select>
    <!--车辆机理故障的最高等级-->
    <select id="getMechanismAlarmFaultLevel" resultType="java.lang.String">
        SELECT MAX(faultType.fault_level) faultLevel
        FROM mtc_mechanism_alarm_record record_
        LEFT JOIN mtc_mechanism_alarm_rule rule_ ON record_.alarm_rule_id = rule_.id
        LEFT JOIN ekb_fault_type faultType ON rule_.fault_type_key = faultType.fault_type_key
        LEFT JOIN mtr_vehicle vehicle_ ON vehicle_.id = record_.vehicle_id
        WHERE
            vehicle_.vehicle_code = #{vehicleCode} AND
            vehicle_.del_flag = 0 AND
            record_.end_time IS NULL
    </select>

    <update id="updateCloseStatue">
        UPDATE mtc_alarm_warning SET
            close_statue = #{closeStatue}
        WHERE
            id = #{id}
    </update>
</mapper>