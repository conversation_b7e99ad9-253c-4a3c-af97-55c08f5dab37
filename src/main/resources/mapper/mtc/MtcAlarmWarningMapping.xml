<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtc.MtcAlarmWarningMapping">
    <sql id="MtcAlarmWarningRecordAlias">
        t1.id,
        t1.vehicle_id AS vehicleId,
        t1.fault_type_key AS faultTypeKey,
        t1.component_id AS componentId,
        t1.alarm_rule_id AS alarmRuleId,
        t1.alarm_snapshot AS alarmSnapshot,
        t1.start_time AS startTime,
        t1.end_time AS endTime,
        t1.suppressed_by AS suppressedBy,
        t1.status,
        t1.running_status AS runningStatus,
        t1.suppressed_status AS suppressedStatus,
        t1.line_id AS lineId,
        t1.vehicle_type_id AS vehicleTypeId,
        t1.vehicle_structure_code AS vehicleStructureCode,
        t1.subsystem,
        t1.location AS location,
        t1.fault_name_cn AS faultNameCn,
        t1.fault_code AS faultCode,
        t1.fault_level AS faultLevel,
        t1.fault_source AS faultSource,
        t1.params AS params
    </sql>

    <select id="getAlarmWarning" resultType="cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO">
        SELECT
        t1.id,
        t1.fault_type_key AS faultTypeKey,
        t1.alarm_snapshot AS alarmSnapshot,
        t1.start_time AS startTime,
        t1.end_time AS endTime,
        t1.status,
        t1.params,
        t1.running_status AS runningStatus,
        t1.vehicle_structure_code AS vehicleStructureCode,
        t1.subsystem,
        t1.location AS location,
        t1.fault_name_cn ||'('||t1.fault_name_en||')' AS faultNameCn,
        t1.fault_code AS faultCode,
        t1.fault_level AS faultLevel,
        t1.fault_source AS faultSource,
        t3.vehicle_code AS vehicleCode,
        t4.label
        FROM mtc_alarm_warning t1
        LEFT JOIN ekb_fault_type t2 ON t1.fault_type_key = t2.fault_type_key
        LEFT JOIN mtr_vehicle t3 ON t1.vehicle_id = t3.id
        LEFT JOIN sys_dict t4 ON t2.subsystem = t4.value and t4.line_id = t1.line_id and t4.vehicle_type_id = t1.vehicle_type_id and t4.type_code ='ass_car_system'
        WHERE
        t2.del_flag = 0
        <if test="subsystem != null and subsystem != ''">
            AND t1.subsystem = #{subsystem}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId!=''">
            AND t1.vehicle_type_id = #{vehicleTypeId}
        </if>
        <if test="lineId != null and lineId!=''">
            AND t1.line_id = #{lineId}
        </if>
        <if test="faultSource != null and faultSource != ''">
            AND t1.fault_source = #{faultSource}
        </if>
        <if test="faultCode != null and faultCode != ''">
            AND t1.fault_code LIKE '%'||#{faultCode}||'%'
        </if>
        <if test="faultNameCn != null and faultNameCn != ''">
            AND t1.fault_name_cn LIKE '%'||#{faultNameCn}||'%'
        </if>
        <if test="vehicleCode != null and vehicleCode != ''">
            AND t3.vehicle_code = #{vehicleCode}
        </if>
        <if test="faultLevel != null">
            AND t1.fault_level = #{faultLevel}
        </if>
        <if test="location != null and location != ''">
            AND t1.location = #{location}
        </if>
        <if test="runningStatus != null">
            AND t1.running_status = #{runningStatus}
        </if>
        <if test="startTime != null">
            AND t1.start_time >= #{startTime}
        </if>
        <if test="startTimeEnd != null">
            AND t1.start_time <![CDATA[<=]]> #{startTimeEnd}
        </if>
        ORDER BY
        t1.start_time DESC,
        t1.end_time DESC
    </select>

    <insert id="insertAlarmWarning">
        INSERT INTO mtc_alarm_warning
        (
             id,
             line_id,
             vehicle_id,
             vehicle_type_id,
             vehicle_structure_code,
             fault_type_key,
             subsystem,
             "location",
             fault_name_cn,
             fault_name_en,
             fault_code,
             fault_level,
             alarm_rule_id,
             alarm_snapshot,
             params,
             start_time,
             status,
             running_status,
             fault_source,
             create_time,
             modify_time,
             close_statue
         )VALUES (
            #{id},
            #{lineId},
            #{vehicleId},
            #{vehicleTypeId},
            #{vehicleStructureCode},
            #{faultType},
            #{subsystem},
            #{location},
            #{faultNameCn},
            #{faultNameEn},
            #{faultCode},
            #{faultLevel},
            #{alarmRuleId},
            #{snapshotStr},
            #{paramStr},
            #{date},
            #{status},
            #{runningStatus},
            #{faultSource},
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP,
            FALSE
        )
    </insert>
    <update id="updateAlarmWaring">
        UPDATE mtc_alarm_warning
        SET
        <if test="date != null">
            end_time = #{date},
        </if>
            modify_time = CURRENT_TIMESTAMP,
            end_status = 1
        WHERE
            vehicle_id = #{vehicleId}
          AND fault_type_key = #{faultType}
          AND fault_source = #{faultSource}
          AND end_status = 0
    </update>
    <select id="getChildAlarmWarning" resultType="cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO">
        SELECT id,
               vehicle_id     AS vehicleId,
               fault_type_key AS faultTypeKey,
               start_time     AS startTime
        FROM mtc_alarm_warning
        WHERE vehicle_id = #{vehicleId}
          AND end_time IS NULL
          AND suppressed_by = #{faultTypeKey}
    </select>
    <update id="removeSnowSlide">
        UPDATE mtc_alarm_warning
        SET suppressed_by     = null,
            suppressed_status = false
        WHERE id = #{id}
    </update>
    <update id="updateSlideList">
        UPDATE mtc_alarm_warning SET
        suppressed_by = tmp.fault_type_key,
        suppressed_status = true
        FROM(
        SELECT
        id,
        vehicle_id,
        fault_type_key,
        start_time
        FROM mtc_alarm_warning
        WHERE
        vehicle_id = #{childFault.vehicleId} AND
        end_time IS NULL AND
        suppressed_status = false AND
        <foreach collection="keys" open="(" close=")" separator="or" item="item">
            (fault_type_key = #{item.parentFaultTypeKey} AND
            abs(tstzrange_subdiff(#{childFault.startTime}, start_time)) &lt;= #{item.correlationTime} / 1000)
        </foreach>
        ORDER BY start_time
        LIMIT 1
        ) tmp
        WHERE mtc_alarm_warning.id = #{childFault.id}
    </update>
    <!--故障等级统计-->
    <select id="countAlarmWarningByFaultLevel" resultType="java.util.HashMap">
        SELECT
            COUNT(*) AS count,
            CONCAT(maw.fault_source,maw.fault_level) AS faultkey
        FROM mtc_alarm_warning maw
        JOIN ekb_fault_type eft ON maw.fault_type_key = eft.fault_type_key
        WHERE
            maw.end_status = 0
            AND maw.fault_level > 0
            <if test="lineId != null and lineId != ''">
                AND maw.line_id = #{lineId}
            </if>
        GROUP BY
            maw.fault_level,
            maw.fault_source
    </select>
    <!--查询车辆最高的告警等级或预警等级-->
    <select id="getHighestLevel" resultType="java.lang.Integer">
        SELECT
        CASE WHEN MAX(alarm_.fault_level) IS NULL THEN 0
        ELSE MAX(alarm_.fault_level) END
        FROM
        "mtc_alarm_warning" alarm_
        LEFT JOIN "ekb_fault_type" fault_ ON alarm_.fault_type_key = fault_.fault_type_key
        LEFT JOIN mtr_vehicle vehicle_ ON alarm_.vehicle_id = vehicle_.id
        LEFT JOIN mtr_line line_ ON vehicle_.metro_line_id = line_.id
        <where>
            alarm_.end_time IS NULL
            and
            alarm_.running_status = true
            <if test="lineId != null and lineId !=''">
                AND line_.id= #{lineId}
            </if>
            <if test="vehicleCode != null and vehicleCode != ''">
                AND vehicle_.vehicle_code = #{vehicleCode}
            </if>
            <if test="faultSource != null and faultSource != ''">
                AND alarm_.fault_source = #{faultSource}
            </if>
        </where>
    </select>

    <!-- 2020年8月18日 房明宽 -->
    <select id="getRealtimeAlarmInformationList" resultType="cc.crrc.manage.pojo.mtc.MtcAutoFaultRecordVO">
        SELECT
        t1.id,
        t1.vehicle_id AS vehicleId,
        t1.fault_type_key AS faultTypeKey,
        t1.component_id AS componentId,
        t1.start_time AS startTime,
        t1.end_time AS endTime,
        t1.status,
        t1.modify_by AS modifyBy,
        t1.modify_time AS modifyTime,
        t1.suppressed_by AS suppressedBy,
        t1.running_status AS runningStatus,
        t1.suppressed_status AS suppressedStatus,
        CASE
        ( SELECT COUNT ( maw.fault_type_key ) FROM mtc_alarm_warning maw WHERE maw.suppressed_by = t1.fault_type_key AND
        maw.suppressed_status = TRUE )
        WHEN 0 THEN
        0 ELSE 1
        END AS ifSuppressed,
        t1.fault_source AS source,
        t2.LOCATION,
        t2.description,
        t2.subsystem,
        t2.model_code AS modelCode,
        t7.label,
        t2.name_cn AS faultNameCn,
        t2.name_en AS faultNameEn,
        t2.fault_code AS faultCode,
        t1.fault_level AS faultLevel,
        t2.frontline_disposal_recommendations AS fdr,
        t2.overhaul_suggestions AS overhaulSuggestion,
        t2.fault_mode AS faultMode,
        t2.fault_reason AS faultReason,
        t3.name_cn AS vehicleNameCn,
        t3.name_en AS vehicleNameEn,
        t3.vehicle_code AS vehicleCode,
        t4.name AS lineName,
        t4.id AS lineId,
        t6.id AS vehicleTypeId,
        t9.name_cn AS componentName,
        REPLACE ( CAST ( t1.end_time - t1.start_time AS VARCHAR ), 'days', '天' ) AS duration
        FROM
        mtc_alarm_warning t1
        LEFT JOIN ekb_fault_type t2 ON t1.fault_type_key = t2.fault_type_key
        LEFT JOIN mtr_vehicle t3 ON t1.vehicle_id = t3.id
        LEFT JOIN mtr_line t4 ON t3.metro_line_id = t4.id
        LEFT JOIN mtr_vehicle_type_relation t5 ON t5.vehicle_id = t1.vehicle_id
        LEFT JOIN mtr_vehicle_type t6 ON t6.id = t5.vehicle_type_id
        LEFT JOIN sys_dict t7 ON t7.value = t2.subsystem AND t1.vehicle_type_id = t7.vehicle_type_id AND t1.line_id
        =t7.line_id AND t7.type_code = 'ass_car_system'
        LEFT JOIN stru_component t8 ON t1.component_id = t8.id
        LEFT JOIN stru_component_type t9 ON t8.component_type_id = t9.id
        WHERE
        t3.del_flag = 0
        and t2.del_flag = 0
        <if test="vehicleId != null and vehicleId != ''">
            and t1.vehicle_id = #{vehicleId}
        </if>
        <if test="vehicleId != null and vehicleId != ''">
            and t2.model_code = #{modelCode}
        </if>
    </select>


    <select id="getAlarmLevel" resultType="cc.crrc.manage.pojo.excel.AlarmLevelForExcelPO">
        SELECT mv.vehicle_code AS                                   vehicleCode,
               eft.subsystem,
               sd.label        AS                                   subsystemName,
               COUNT(*)        AS                                   countAll,
               SUM(CASE WHEN maw.fault_level = 0 THEN 1 ELSE 0 END) count1,
               SUM(CASE WHEN maw.fault_level = 1 THEN 1 ELSE 0 END) count2,
               SUM(CASE WHEN maw.fault_level = 2 THEN 1 ELSE 0 END) count3,
               SUM(CASE WHEN maw.fault_level = 3 THEN 1 ELSE 0 END) count4
        FROM mtc_alarm_warning maw
                 LEFT JOIN mtr_vehicle mv ON mv.id = maw.vehicle_id
                 LEFT JOIN ekb_fault_type eft ON maw.fault_type_key = eft.fault_type_key
                 LEFT JOIN sys_dict sd ON eft.subsystem = sd.code AND eft.vehicle_type_id = sd.vehicle_type_id AND
                                          eft.line_id = sd.line_id
        WHERE sd.type_code = 'ass_car_system'
          AND sd.del_flag = '0'
          AND maw.vehicle_id = #{vehicleId}
          AND maw.start_time >= #{startTime}
          AND maw.start_time <![CDATA[<=]]> #{endTime}
        GROUP BY eft.subsystem, subsystemName, mv.vehicle_code
    </select>

    <select id="getMtcAlarmRecordById" resultType="cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO">
        SELECT
        <include refid="MtcAlarmWarningRecordAlias"/>,
        t2.frontline_disposal_recommendations fdr,
        t2.overhaul_suggestions overhaulSuggestion,
        t2.fault_reason faultReason
        FROM mtc_alarm_warning t1
        LEFT JOIN ekb_fault_type t2 ON t1.fault_type_key = t2.fault_type_key
        WHERE
        t2.del_flag = 0
        AND t1.id = #{id}

    </select>


    <select id="getMtcAlarmWarningForMonitor" resultType="cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO">
        SELECT
        t1.ID,
        t1.vehicle_id AS vehicleId,
        t1.start_time AS startTime,
        t1.end_time AS endTime,
        t1.fault_name_cn AS faultNameCn,
        t1.fault_level AS faultLevel,
        t1.fault_source AS faultSource,
        t3.vehicle_code AS vehicleCode
        FROM mtc_alarm_warning t1
        LEFT JOIN mtr_vehicle t3 ON t1.vehicle_id = t3.id
        LEFT JOIN ekb_fault_type t4 ON t1.fault_type_key = t4.fault_type_key
        WHERE
        1=1
        /*线路监控只查endTime为空的，故障查询查所有*/
        <if test="endTime == null">
            AND t1.end_status = 0
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId!=''">
            AND t1.vehicle_type_id = #{vehicleTypeId}
        </if>
        <if test="lineId != null and lineId!=''">
            AND t1.line_id = #{lineId}
        </if>
        <if test="faultSource != null and faultSource != ''">
            AND t1.fault_source = #{faultSource}
        </if>
        <if test="vehicleCode != null and vehicleCode != ''">
            AND t3.vehicle_code = #{vehicleCode}
        </if>
        <if test="runningStatus != null">
            AND t1.running_status = #{runningStatus}
        </if>
        <if test="startTime != null">
            AND t1.start_time >= #{startTime}
        </if>
        <if test="startTimeEnd != null">
            AND #{startTimeEnd} >= t1.start_time
        </if>
        <if test="onlineVehicles != null and onlineVehicles.size()>0">
            AND t3.vehicle_code IN
            <foreach collection="onlineVehicles" item="vehicleCode" index="index" open="(" separator="," close=")">
                #{vehicleCode}
            </foreach>
        </if>
        ORDER BY
        CASE t1.fault_level WHEN '3' THEN 1 WHEN '2' THEN 2 WHEN '1' THEN 3 WHEN '0' THEN 4 ELSE 5 END ASC,
        t1.start_time desc,
        convert_to(t1.fault_name_cn, 'gbk')
        LIMIT 100
    </select>

    <select id="getLineFaultStatistics" resultType="cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO">
        SELECT
            maw.id AS id,
            maw.vehicle_id AS vehicleId,
            maw.start_time AS startTime,
            maw.end_time AS endTime,
            maw.fault_name_cn AS faultNameCn,
            maw.fault_level AS faultLevel,
            maw.fault_source AS faultSource,
            maw.running_status AS runningStatus,
            mv.vehicle_code AS vehicleCode
        FROM mtr_vehicle mv
        LEFT JOIN mtc_alarm_warning maw ON mv.id = maw.vehicle_id
        WHERE maw.end_status = 0
            AND maw.line_id = #{lineId}
    </select>

    <select id="getMoreMtcAlarmWarningForMonitor" resultType="cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO">
        SELECT
        t1.id,
        t1.vehicle_id AS vehicleId,
        t1.start_time AS startTime,
        t1.end_time AS endTime,
        t1.fault_name_cn AS faultNameCn,
        t1.fault_level AS faultLevel,
        t1.fault_source AS faultSource,
        t3.vehicle_code AS vehicleCode
        FROM mtc_alarm_warning t1
        LEFT JOIN mtr_vehicle t3 ON t1.vehicle_id = t3.id
        WHERE
        t1.end_status = 0
        <if test="lineId != null and lineId!=''">
            AND t1.line_id = #{lineId}
        </if>
        <if test="faultSource != null and faultSource != ''">
            AND t1.fault_source = #{faultSource}
        </if>
        <if test="vehicleCode != null and vehicleCode != ''">
            AND t3.vehicle_code = #{vehicleCode}
        </if>
        <if test="startTime != null">
            AND t1.start_time >= #{startTime}
        </if>
        ORDER BY
        t1.start_time DESC,
        t1.end_time DESC
    </select>

    <select id="getWarningCount" resultType="java.util.HashMap">
        SELECT mv.id AS "vehicleId",
            COUNT ( T.* ) FILTER ( WHERE T.fault_source = '1' )  AS warning
        FROM
            mtr_vehicle mv  JOIN
            mtc_alarm_warning T on mv.id = T.vehicle_id
        WHERE
            1 = 1
          AND T.end_time IS NULL
          AND T.line_id = #{lineId}
       /*   AND T.running_status = true*/
        GROUP BY
            mv.id
        ORDER BY
            mv.id
    </select>
    <select id="getAlarmCount" resultType="java.util.HashMap">
        SELECT T.vehicle_id AS "vehicleId",
               COUNT ( T.* ) FILTER ( WHERE T.fault_source = '0' ) AS "current"
        FROM
            mtc_alarm_warning T
                LEFT JOIN ekb_fault_type t1 ON T.fault_type_key = t1.fault_type_key
        WHERE
            1 = 1
          AND T.end_time IS NULL
          AND T.running_status = true
          AND T.line_id = #{lineId}
        GROUP BY
            T.vehicle_id
        ORDER BY
            T.vehicle_id
    </select>
    <select id="getAlarmOrWarningCountDef" resultType="java.util.HashMap">
        SELECT mv.id AS "vehicleId",
               0 AS current,
			   0 AS warning
        FROM mtr_vehicle mv
        WHERE
            1 = 1
          AND mv.del_flag = 0
          AND mv.metro_line_id = #{lineId}
        ORDER BY
            mv.vehicle_code
    </select>

    <select id="listMtcAlarmWarningRuleTest" resultType="cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO">
        SELECT
        t1.id,
        t1.vehicle_id AS vehicleId,
        t1.fault_type_key AS faultTypeKey,
        t1.component_id AS componentId,
        t1.alarm_rule_id AS alarmRuleId,
        t1.alarm_snapshot AS alarmSnapshot,
        t1.start_time AS startTime,
        t1.end_time AS endTime,
        t1.suppressed_by AS suppressedBy,
        t1.status,
        t1.running_status AS runningStatus,
        t1.suppressed_status AS suppressedStatus,
        t1.line_id AS lineId,
        t1.vehicle_type_id AS vehicleTypeId,
        t1.vehicle_structure_code AS vehicleStructureCode,
        t1.subsystem,
        t1.location AS location,
        t1.fault_name_cn ||'('||t1.fault_name_en||')' AS faultNameCn,
        t1.fault_code AS faultCode,
        t1.fault_level AS faultLevel,
        t1.fault_source AS faultSource,
        t2.fault_mode AS faultMode,
        t3.vehicle_code AS vehicleCode,
        t4.label
        FROM mtc_alarm_warning_rule_test t1
        LEFT JOIN ekb_fault_type t2 ON t1.fault_type_key = t2.fault_type_key
        LEFT JOIN mtr_vehicle t3 ON t1.vehicle_id = t3.id
        LEFT JOIN sys_dict t4 ON t2.subsystem = t4.value and t4.line_id = t1.line_id and t4.vehicle_type_id =
        t1.vehicle_type_id and t4.type_code ='ass_car_system'
        WHERE
        t2.del_flag = 0
        /*线路监控只查endTime为空的，故障查询查所有*/
        <if test="endTime == null">
            AND t1.end_time IS NULL
        </if>
        <if test="subsystem != null and subsystem != ''">
            AND t1.subsystem = #{subsystem}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId!=''">
            AND t1.vehicle_type_id = #{vehicleTypeId}
        </if>
        <if test="lineId != null and lineId!=''">
            AND t1.line_id = #{lineId}
        </if>
        <if test="faultSource != null and faultSource != ''">
            AND t1.fault_source = #{faultSource}
        </if>
        <if test="faultCode != null and faultCode != ''">
            AND t1.fault_code LIKE '%\'||#{faultCode}||'%'
        </if>
        <if test="faultNameCn != null and faultNameCn != ''">
            AND t1.fault_name_cn LIKE '%\'||#{faultNameCn}||'%'
        </if>
        <if test="vehicleCode != null and vehicleCode != ''">
            AND t3.vehicle_code = #{vehicleCode}
        </if>
        <if test="faultMode != null and faultMode != ''">
            AND t2.fault_mode = #{faultMode}
        </if>
        <if test="faultLevel != null">
            AND t1.fault_level = #{faultLevel}
        </if>
        <if test="location != null and location != ''">
            AND t1.location = #{location}
        </if>
        <if test="runningStatus != null">
            AND t1.running_status = #{runningStatus}
        </if>
        <if test="suppressedStatus != null">
            AND t1.suppressed_status = #{suppressedStatus}
        </if>
        <if test="startTime != null">
            AND t1.start_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="startTimeEnd != null">
            AND t1.start_time <![CDATA[<=]]> #{startTimeEnd}
        </if>
        <if test="onlineVehicles != null and onlineVehicles.size()>0">
            AND t3.vehicle_code IN
            <foreach collection="onlineVehicles" item="vehicleCode" index="index" open="(" separator="," close=")">
                #{vehicleCode}
            </foreach>
        </if>
        ORDER BY
        t1.start_time DESC,
        t1.end_time DESC,
        t1.fault_source ,
        convert_to(fault_name_cn, 'gbk')
    </select>

    <insert id="insertTestAlarmWarning">
        INSERT INTO mtc_alarm_warning_rule_test
        (
            id,
            line_id,
            vehicle_id,
            vehicle_type_id,
            vehicle_structure_code,
            fault_type_key,
            subsystem,
            "location",
            fault_name_cn,
            fault_name_en,
            fault_code,
            fault_level,
            alarm_rule_id,
            alarm_snapshot,
            params,
            start_time,
            status,
            running_status,
            fault_source,
            create_time,
            modify_time
        )VALUES (
            #{id},
            #{lineId},
            #{vehicleId},
            #{vehicleTypeId},
            #{vehicleStructureCode},
            #{faultType},
            #{subsystem},
            #{location},
            #{faultNameCn},
            #{faultNameEn},
            #{faultCode},
            #{faultLevel},
            #{alarmRuleId},
            #{snapshotStr},
            #{paramStr},
            #{date},
            #{status},
            #{runningStatus},
            #{faultSource},
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        )
    </insert>

    <update id="updateTestAlarmWaring">
        UPDATE mtc_alarm_warning_rule_test
        SET
        <if test="status != null and status != ''">
            status = #{status},
        </if>
        <if test="date != null">
            end_time = #{date},
        </if>
            modify_time = CURRENT_TIMESTAMP,
            end_status = 1
        WHERE
            vehicle_id = #{vehicleId}
        AND fault_type_key = #{faultType}
        AND fault_source = #{faultSource}
        AND end_status = 0
    </update>

    <select id="getTestMtcAlarmWarningById" resultType="cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO">
        SELECT
            t1.id ,
            t1.vehicle_id AS vehicleId,
            t1.fault_type_key AS faultTypeKey,
            t1.component_id AS componentId,
            t1.alarm_rule_id AS alarmRuleId,
            t1.start_time AS startTime,
            t1.end_time AS endTime,
            t1.suppressed_by AS suppressedBy,
            t1.status,
            t1.running_status AS runningStatus,
            t1.suppressed_status AS suppressedStatus,
            t1.line_id AS lineId,
            t1.vehicle_type_id AS vehicleTypeId,
            t1.vehicle_structure_code AS vehicleStructureCode,
            t1.subsystem,
            t1.location AS location,
            t1.fault_name_cn AS faultNameCn,
            t1.fault_code AS faultCode,
            t1.fault_level AS faultLevel,
            t1.fault_source AS faultSource,
            t2.fault_mode AS faultMode,
            t3.vehicle_code AS vehicleCode,
            t4.label,
            t2.frontline_disposal_recommendations fdr,
            t2.overhaul_suggestions overhaulSuggestion
        FROM mtc_alarm_warning_rule_test t1
        LEFT JOIN ekb_fault_type t2 ON t1.fault_type_key = t2.fault_type_key
        LEFT JOIN mtr_vehicle t3 ON t1.vehicle_id = t3.id
        LEFT JOIN sys_dict t4 ON t2.subsystem = t4.value and t4.line_id = t1.line_id and t4.vehicle_type_id =
        t1.vehicle_type_id and t4.type_code ='ass_car_system'
        WHERE
        t2.del_flag = 0
        AND t1.id = #{id}
    </select>

    <!--车辆监控 查询车辆故障和预警数据-->
    <select id="getTrainFaultList" resultType="cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO">
        SELECT
            maw.id AS id,
            maw.vehicle_id AS vehicleId,
            maw.start_time AS startTime,
            maw.end_time AS endTime,
            maw.fault_name_cn AS faultNameCn,
            maw.fault_level AS faultLevel,
            maw.fault_source AS faultSource,
            maw.running_status AS runningStatus,
            mv.vehicle_code AS vehicleCode
        FROM mtr_vehicle mv
                 LEFT JOIN mtc_alarm_warning maw ON mv.id = maw.vehicle_id
        WHERE maw.end_status = 0
          AND mv.vehicle_code = #{vehicleCode}
    </select>
    <!--车辆监控 查询车辆故障和预警数据-->
    <select id="getTrainFaultListV2" resultType="cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO">
        SELECT maw.id             AS id,
               maw.vehicle_id     AS vehicleId,
               maw.start_time     AS startTime,
               maw.end_time       AS endTime,
               maw.fault_name_cn  AS faultNameCn,
               maw.fault_level    AS faultLevel,
               maw.fault_source   AS faultSource,
               maw.running_status AS runningStatus,
               mv.vehicle_code    AS vehicleCode
        FROM mtr_vehicle mv
                 LEFT JOIN mtc_alarm_warning maw ON mv.id = maw.vehicle_id
        WHERE maw.end_status = 0
          AND mv.vehicle_code = #{vehicleCode}
          AND maw.fault_source = #{faultSource}
        order by maw.id desc
    </select>
    <select id="getTrainMonitorFaults" resultType="cc.crrc.manage.pojo.mtc.AlarmWarningInfo">
        SELECT maw.id             AS id,
               maw.vehicle_id     AS vehicleId,
               maw.start_time     AS startTime,
               maw.end_time       AS endTime,
               maw.fault_name_cn  AS faultNameCn,
               maw.fault_level    AS faultLevel,
               maw.fault_source   AS faultSource,
               maw.running_status AS runningStatus,
               mv.vehicle_code    AS vehicleCode
        FROM mtc_alarm_warning maw
        inner JOIN mtr_vehicle mv ON maw.vehicle_id = mv.id
        and mv.del_flag = 0
        WHERE maw.end_status = 0
          AND mv.vehicle_code = #{vehicleCode}
          AND maw.fault_source = #{faultSource}
        order by maw.id desc
    </select>

    <!--检修管理-故障工单-新增-故障列表-->
    <select id="getAutoFaultList" resultType="cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO">
        SELECT
            maw.id AS id,
            maw.vehicle_id AS vehicleId,
            maw.start_time AS startTime,
            maw.end_time AS endTime,
            maw.fault_name_cn AS faultNameCn,
            maw.fault_level AS faultLevel,
            mv.vehicle_code AS vehicleCode,
            maw.fault_source AS faultSource
        FROM mtc_alarm_warning maw
        LEFT JOIN mtr_vehicle mv ON mv.id = maw.vehicle_id
        WHERE
            maw.status = '0'
            AND maw.fault_source = '0'
            <if test="vehicleCode != null and vehicleCode != ''">
                AND mv.vehicle_code = #{vehicleCode}
            </if>
            <if test="faultNameCn != null and faultNameCn != ''">
                AND maw.fault_name_cn like '%' || #{faultNameCn} || '%'
            </if>
            <if test="vehicleTypeId != null and vehicleTypeId != ''">
                AND maw.vehicle_type_id = #{vehicleTypeId}
            </if>
            <if test="lineId != null and lineId != ''">
                AND maw.line_id = #{lineId}
            </if>
    </select>

    <insert id="addManualAlarmFault">
        INSERT INTO mtc_manual_alarm_warning
        (
            id,
            line_id,
            vehicle_id,
            vehicle_type_id,
            vehicle_structure_code,
            fault_type_key,
            subsystem,
            "location",
            fault_name_cn,
            fault_name_en,
            fault_code,
            fault_level,
            start_time,
            status,
            running_status,
            fault_source,
            create_time,
            modify_time,
            end_status,
            end_time
        ) VALUES (
            #{id},
            #{lineId},
            #{vehicleId},
            #{vehicleTypeId},
            #{vehicleStructureCode},
            #{faultTypeKey},
            #{subsystem},
            #{location},
            #{faultNameCn},
            #{faultNameEn},
            #{faultCode},
            #{faultLevel},
            #{startTime},
            '0',
            true,
            #{faultSource},
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP,
            #{endStatus},
            #{endTime}
        )
    </insert>

    <select id="getManualAlarmWarning" resultType="cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO">
        SELECT
        t1.id,
        t1.line_id AS lineId,
        t1.vehicle_id AS vehicleId,
        t1.vehicle_type_id AS vehicleTypeId,
        t1.fault_type_key AS faultTypeKey,
        t1.vehicle_structure_code AS vehicleStructureCode,
        t1.start_time AS startTime,
        t1.end_time AS endTime,
        t1.running_status AS runningStatus,
        t1.location AS location,
        t1.fault_name_cn AS faultNameCn,
        t1.fault_name_en AS faultNameEn,
        t1.fault_code AS faultCode,
        t1.fault_level AS faultLevel,
        t1.fault_source AS faultSource,
        t1.subsystem,
        t1.status,
        t3.vehicle_code AS vehicleCode,
        t4.label
        FROM mtc_manual_alarm_warning t1
        LEFT JOIN ekb_fault_type t2 ON t1.fault_type_key = t2.fault_type_key
        LEFT JOIN mtr_vehicle t3 ON t1.vehicle_id = t3.id
        LEFT JOIN sys_dict t4 ON t2.subsystem = t4.value and t4.line_id = t1.line_id and t4.vehicle_type_id = t1.vehicle_type_id and t4.type_code ='ass_car_system'
        WHERE
        t2.del_flag = 0
        <if test="subsystem != null and subsystem != ''">
            AND t1.subsystem = #{subsystem}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId!=''">
            AND t1.vehicle_type_id = #{vehicleTypeId}
        </if>
        <if test="lineId != null and lineId!=''">
            AND t1.line_id = #{lineId}
        </if>
        <if test="faultCode != null and faultCode != ''">
            AND t1.fault_code LIKE '%'||#{faultCode}||'%'
        </if>
        <if test="faultNameCn != null and faultNameCn != ''">
            AND t1.fault_name_cn LIKE '%'||#{faultNameCn}||'%'
        </if>
        <if test="vehicleCode != null and vehicleCode != ''">
            AND t3.vehicle_code = #{vehicleCode}
        </if>
        <if test="faultLevel != null">
            AND t1.fault_level = #{faultLevel}
        </if>
        <if test="location != null and location != ''">
            AND t1.location = #{location}
        </if>
        <if test="runningStatus != null">
            AND t1.running_status = #{runningStatus}
        </if>
        <if test="startTime != null">
            AND t1.start_time >= #{startTime}
        </if>
        <if test="startTimeEnd != null">
            AND t1.start_time <![CDATA[<=]]> #{startTimeEnd}
        </if>
        ORDER BY
        t1.start_time DESC,
        t1.end_time DESC
    </select>

    <select id="exportManualFaultExcel" resultType="cc.crrc.manage.pojo.excel.MtcAlarmWarningForExcelPO">
        SELECT
            t1.fault_name_cn  AS faultNameCn,
            t1.fault_name_en  AS faultNameEn,
            t1.fault_code AS faultCode,
            t1.fault_level AS faultLevel,
            t3.vehicle_code AS vehicleCode,
            CASE WHEN t1.running_status = true THEN '正线' ELSE '非正线' END AS runningStatus,
            t1.subsystem AS subsystem,
            t1.location AS location,
            t1.start_time AS startTime,
            t1.end_time AS endTime,
            t1.fault_source AS faultSource
        FROM mtc_manual_alarm_warning t1
        LEFT JOIN ekb_fault_type t2 ON t1.fault_type_key = t2.fault_type_key
        LEFT JOIN mtr_vehicle t3 ON t1.vehicle_id = t3.id
        WHERE
        t2.del_flag = 0
        <if test="subsystem != null and subsystem != ''">
            AND t1.subsystem = #{subsystem}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId!=''">
            AND t1.vehicle_type_id = #{vehicleTypeId}
        </if>
        <if test="lineId != null and lineId!=''">
            AND t1.line_id = #{lineId}
        </if>
        <if test="faultCode != null and faultCode != ''">
            AND t1.fault_code LIKE '%'||#{faultCode}||'%'
        </if>
        <if test="faultNameCn != null and faultNameCn != ''">
            AND t1.fault_name_cn LIKE '%'||#{faultNameCn}||'%'
        </if>
        <if test="vehicleCode != null and vehicleCode != ''">
            AND t3.vehicle_code = #{vehicleCode}
        </if>
        <if test="faultLevel != null">
            AND t1.fault_level = #{faultLevel}
        </if>
        <if test="location != null and location != ''">
            AND t1.location = #{location}
        </if>
        <if test="runningStatus != null">
            AND t1.running_status = #{runningStatus}
        </if>
        <if test="startTime != null">
            AND t1.start_time >= #{startTime}
        </if>
        <if test="startTimeEnd != null">
            AND t1.start_time <![CDATA[<=]]> #{startTimeEnd}
        </if>
        ORDER BY
        t1.start_time DESC,
        t1.end_time DESC
        limit 10000
    </select>

    <select id="exportAutoFaultExcel" resultType="cc.crrc.manage.pojo.excel.MtcAlarmWarningForExcelPO">
        SELECT
        t1.fault_name_cn  AS faultNameCn,
        t1.fault_name_en  AS faultNameEn,
        t1.fault_code AS faultCode,
        t1.fault_level AS faultLevel,
        t3.vehicle_code AS vehicleCode,
        CASE WHEN t1.running_status = true THEN '正线' ELSE '非正线' END AS runningStatus,
        t1.subsystem AS subsystem,
        t1.location AS location,
        t1.start_time AS startTime,
        t1.end_time AS endTime,
        t1.fault_source AS faultSource
        FROM mtc_alarm_warning t1
        LEFT JOIN ekb_fault_type t2 ON t1.fault_type_key = t2.fault_type_key
        LEFT JOIN mtr_vehicle t3 ON t1.vehicle_id = t3.id
        WHERE
        t2.del_flag = 0
        <if test="subsystem != null and subsystem != ''">
            AND t1.subsystem = #{subsystem}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId!=''">
            AND t1.vehicle_type_id = #{vehicleTypeId}
        </if>
        <if test="lineId != null and lineId!=''">
            AND t1.line_id = #{lineId}
        </if>
        <if test="faultCode != null and faultCode != ''">
            AND t1.fault_code LIKE '%'||#{faultCode}||'%'
        </if>
        <if test="faultNameCn != null and faultNameCn != ''">
            AND t1.fault_name_cn LIKE '%'||#{faultNameCn}||'%'
        </if>
        <if test="vehicleCode != null and vehicleCode != ''">
            AND t3.vehicle_code = #{vehicleCode}
        </if>
        <if test="faultLevel != null">
            AND t1.fault_level = #{faultLevel}
        </if>
        <if test="location != null and location != ''">
            AND t1.location = #{location}
        </if>
        <if test="runningStatus != null">
            AND t1.running_status = #{runningStatus}
        </if>
        <if test="startTime != null">
            AND t1.start_time >= #{startTime}
        </if>
        <if test="startTimeEnd != null">
            AND t1.start_time <![CDATA[<=]]> #{startTimeEnd}
        </if>
        ORDER BY
        t1.start_time DESC,
        t1.end_time DESC
        limit 10000
    </select>

    <select id="getManualMtcAlarmRecordById" resultType="cc.crrc.manage.pojo.mtc.MtcAlarmWarningPO">
        SELECT
            t1.id,
            t1.vehicle_id AS vehicleId,
            t1.fault_type_key AS faultTypeKey,
            t1.start_time AS startTime,
            t1.end_time AS endTime,
            t1.status,
            t1.running_status AS runningStatus,
            t1.line_id AS lineId,
            t1.vehicle_type_id AS vehicleTypeId,
            t1.vehicle_structure_code AS vehicleStructureCode,
            t1.subsystem,
            t1.location AS location,
            t1.fault_name_cn AS faultNameCn,
            t1.fault_code AS faultCode,
            t1.fault_level AS faultLevel,
            t1.fault_source AS faultSource,
            t2.frontline_disposal_recommendations fdr,
            t2.overhaul_suggestions overhaulSuggestion,
            t2.fault_reason faultReason
        FROM mtc_manual_alarm_warning t1
        LEFT JOIN ekb_fault_type t2 ON t1.fault_type_key = t2.fault_type_key
        WHERE
        t2.del_flag = 0
        AND t1.id = #{id}
    </select>

    <update id="updateManualAlarmFault">
        UPDATE mtc_manual_alarm_warning
        <trim prefix="set" suffixOverrides=",">
            <if test="lineId != null and lineId != ''">
                line_id = #{lineId},
            </if>
            <if test="vehicleId != null and vehicleId != ''">
                vehicle_id = #{vehicleId},
            </if>
            <if test="vehicleTypeId != null and vehicleTypeId != ''">
                vehicle_type_id = #{vehicleTypeId},
            </if>
            <if test="faultTypeKey != null and faultTypeKey != ''">
                fault_type_key = #{faultTypeKey},
            </if>
            <if test="subsystem != null and subsystem !=''">
                subsystem = #{subsystem},
            </if>
            <if test="location != null and location !='' ">
                location = #{location},
            </if>
            <if test="faultLevel != null and faultLevel !=null">
                fault_level = #{faultLevel},
            </if>
            <if test="faultNameCn != null and faultNameCn != ''">
                fault_name_cn = #{faultNameCn},
            </if>
            <if test="faultNameCn != null and faultNameCn != ''">
                fault_name_en = #{faultNameEn},
            </if>
            <if test="faultCode != null and faultCode !=''">
                fault_code = #{faultCode},
            </if>
            <if test="startTime != null ">
                start_time = #{startTime},
            </if>
            <if test="endStatus != null and endStatus !=''">
                end_status = #{endStatus},
            </if>
            end_time = #{endTime},
            modify_time = CURRENT_TIMESTAMP
        </trim>
        WHERE
        id = #{id}
    </update>

    <delete id="deleteManualAlarmFault">
        DELETE FROM mtc_manual_alarm_warning
        <where>
            id IN
            <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </delete>


    <update id="updateAlarmFaultStatus">
        UPDATE ${tableName} SET
            status = '1',
            modify_time = CURRENT_TIMESTAMP
        WHERE
            id = #{id}
        AND status = '0'
    </update>
</mapper>