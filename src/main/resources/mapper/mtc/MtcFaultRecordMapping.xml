<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cc.crrc.manage.mapper.mtc.MtcFaultRecordMapping">


    <sql id="MtcAFaultRecordAlias">
        t1.id,
        t1.sys_responsible_by AS sysResponsibleBy,
        t1.found_by AS foundBy,
        t1.treatment,
        t1.fault_info AS faultInfo,
        t1.treatment_by AS treatmentBy,
        t1.treatment_company AS treatmentCompany ,
        t1.treatment_time AS treatmentTime,
        t1.comit_by AS comitBy,
        t1.input_by AS inputBy ,
        t1.material_usage AS materialUsage,
        t1.material_ascription AS materialAscription ,
        t1.fault_location AS faultLocation,
        t1.line_id AS lineId,
        t1.vehicle_id AS vehicleId,
        t1.fault_id AS faultId,
        t1.start_time AS startTime,
        t1.confirm,
        t1.fault_source AS faultSource,
        t1.fault_name_cn AS faultNameCn
    </sql>
    <insert id="saveFaultRecord">
        insert into mtc_fault_record
        (
            id,
            sys_responsible_by,
            found_by,
            treatment,
            fault_info,
            treatment_by,
            treatment_company,
            treatment_time,
            comit_by,
            input_by,
            material_usage,
            material_ascription,
            fault_location,
            fault_id,
            fault_source,
            line_id,
            vehicle_id,
            start_time,
            modify_time,
            modify_by,
            confirm,
            fault_name_cn
        ) values (
            #{id},
            #{sysResponsibleBy},
            #{foundBy},
            #{treatment},
            #{faultInfo},
            #{treatmentBy},
            #{treatmentCompany},
            #{treatmentTime},
            #{comitBy},
            #{inputBy},
            #{materialUsage},
            #{materialAscription},
            #{faultLocation},
            #{faultId},
            #{faultSource},
            #{lineId},
            #{vehicleId},
            #{startTime},
            CURRENT_TIMESTAMP,
            #{modifyBy},
            #{confirm},
            #{faultNameCn}
        )
    </insert>

    <update id="updFaultRecord">
        UPDATE mtc_fault_record
        SET
            sys_responsible_by = #{sysResponsibleBy},
            found_by = #{foundBy},
            treatment = #{treatment},
            fault_info = #{faultInfo},
            treatment_by = #{treatmentBy},
            treatment_company = #{treatmentCompany},
            treatment_time = #{treatmentTime},
            comit_by = #{comitBy},
            input_by = #{inputBy},
            material_usage = #{materialUsage},
            material_ascription = #{materialAscription},
            fault_location = #{faultLocation},
            modify_time = CURRENT_TIMESTAMP,
            modify_by = #{modifyBy},
            fault_name_cn = #{faultNameCn},
            line_id = #{lineId},
            vehicle_id = #{vehicleId},
            fault_id = #{faultId},
            start_time = #{startTime},
            fault_source = #{faultSource}
        WHERE
            id = #{id}
    </update>

    <update id="updFaultRecordConfirm">
        UPDATE mtc_fault_record SET
            confirm = #{confirm},
            confirm_time = CURRENT_TIMESTAMP,
            modify_time = CURRENT_TIMESTAMP
        WHERE
           id = #{id}
    </update>

    <delete id="delFaultRecord">
        DELETE FROM mtc_fault_record WHERE id = #{id}
    </delete>

    <select id="getFaultRecord" resultType="cc.crrc.manage.pojo.mtc.MtcFaultRecordVO">
        SELECT
            <include refid="MtcAFaultRecordAlias"></include>,
            t2.vehicle_code AS vehicleCode
        FROM mtc_fault_record t1
        LEFT JOIN mtr_vehicle t2 ON t2.id = t1.vehicle_id
        WHERE
            t2.del_flag = 0
        <if test="lineId != null and lineId != ''">
            AND t1.line_id = #{lineId}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId != ''">
            AND t2.vehicle_type_id = #{vehicleTypeId}
        </if>
        <if test="vehicleCode != null and vehicleCode != ''">
            AND t2.vehicle_code = #{vehicleCode}
        </if>
        <if test="queryTimeStart != null">
            AND t1.start_time &gt; #{queryTimeStart}
        </if>
        <if test="queryTimeEnd != null">
            AND t1.start_time &lt; #{queryTimeEnd}
        </if>
        <if test="faultNameCn != null and faultNameCn != ''">
            AND t1.fault_name_cn LIKE '%'||#{faultNameCn}||'%'
        </if>
        <if test="faultSource != null and faultSource != ''">
            AND t1.fault_source = #{faultSource}
        </if>
        ORDER BY
        t1.confirm ASC,
        t1.start_time DESC
    </select>

    <select id="findFaultWorkOrderList" resultType="cc.crrc.manage.pojo.excel.MtcFaultRecordExcelPO">
        SELECT
            t1.fault_name_cn AS faultNameCn,
            t1.start_time AS startTime,
            t2.vehicle_code AS vehicleCode,
            t1.sys_responsible_by AS sysResponsibleBy,
            t3.location AS location,
            t3.subsystem AS subsystem,
            CASE WHEN t3.fault_level= 0 THEN '事件' WHEN t3.fault_level = 1 THEN '轻微' WHEN t3.fault_level = 2 THEN '一般' WHEN t3.fault_level = 3 THEN '严重' END AS faultLevel,
            t1.confirm AS confirm,
            t1.sys_responsible_by AS sysResponsibleBy,
            t1.treatment_by AS treatmentBy,
            t1.treatment_company AS treatmentCompany ,
            t1.treatment_time AS treatmentTime,
            t1.fault_source AS faultSource
        FROM mtc_fault_record t1
        LEFT JOIN mtc_alarm_warning t3 ON t3.id = t1.fault_id
        LEFT JOIN mtr_vehicle t2 ON t2.id = t1.vehicle_id
        WHERE
            t2.del_flag = 0
        <if test="lineId != null and lineId != ''">
            AND t1.line_id = #{lineId}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId != ''">
            AND t2.vehicle_type_id = #{vehicleTypeId}
        </if>
        <if test="vehicleCode != null and vehicleCode != ''">
            AND t2.vehicle_code = #{vehicleCode}
        </if>
        <if test="queryTimeStart != null">
            AND t1.start_time &gt; #{queryTimeStart}
        </if>
        <if test="queryTimeEnd != null">
            AND t1.start_time &lt; #{queryTimeEnd}
        </if>
        <if test="faultNameCn != null and faultNameCn != ''">
            AND t1.fault_name_cn LIKE '%'||#{faultNameCn}||'%'
        </if>
        <if test="faultSource != null and faultSource != ''">
            AND t1.fault_source = #{faultSource}
        </if>
    </select>

</mapper>