<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtc.MtcAutoFaultRecordMapping">
    <resultMap id="supressionFaultMap" type="java.util.HashMap">
        <result column="name" property="name"/>
        <result column="value" property="value"/>
        <collection property="children" select="getSubSupressionFault" column="{key=fault_type_key}">
            <result column="name" property="name"/>
            <result column="value" property="value"/>
        </collection>
    </resultMap>
    <sql id="MtcAutoFaultRecordAlias">
        t1.id,
        t1.vehicle_id AS vehicleId,
        t1.fault_type_key AS faultTypeKey,
        t1.component_id AS componentId,
        t1.start_time AS startTime,
        t1.end_time AS endTime,
        t1.status,
        t1.modify_by AS modifyBy,
        t1.modify_time AS modifyTime,
        t1.suppressed_by AS suppressedBy,
        t1.running_status AS runningStatus,
        t1.suppressed_status AS suppressedStatus
    </sql>
    <update id="confirmMtcAutoFaultRecordById">
        UPDATE
        mtc_alarm_warning
        SET
        status = '1',
        modify_by = #{userId},
        modify_time = CURRENT_TIMESTAMP
        WHERE
        id = #{id}
    </update>
    <select id="listMtcAutoFaultRecord" resultType="cc.crrc.manage.pojo.mtc.MtcAutoFaultRecordVO">
        SELECT
        <include refid="MtcAutoFaultRecordAlias"/>,
        t2.location,
        t2.subsystem,
        t7.label,
        t2.name_cn AS faultNameCn,
        t2.name_en AS faultNameEn,
        t2.model_code AS modelCode,
        t2.fault_code AS faultCode,
        t1.fault_level AS faultLevel,
        t9.label AS faultLevelName,
        t3.name_cn AS vehicleNameCn,
        t3.name_en AS vehicleNameEn,
        t3.vehicle_code AS vehicleCode,
        t4.name AS lineName,
        t4.id AS lineId,
        t6.id AS vehicleTypeId,
        t10.structure_position AS structurePosition,
        replace(cast(t1.end_time-t1.start_time as VARCHAR),'days','天') AS duration
        FROM
        mtc_alarm_warning t1
        LEFT JOIN ekb_fault_type t2
        ON t1.fault_type_key = t2.fault_type_key
        LEFT JOIN mtr_vehicle t3
        ON t1.vehicle_id =t3.id
        LEFT JOIN mtr_line t4
        ON t3.metro_line_id = t4.id
        LEFT JOIN mtr_vehicle_type_relation t5
        ON t5.vehicle_id = t1.vehicle_id
        LEFT JOIN mtr_vehicle_type t6
        ON t6.id = t5.vehicle_type_id
        LEFT JOIN sys_dict t7
        ON  t7.value = t2.subsystem  and t7.line_id = t1.line_id and t7.vehicle_type_id = t1.vehicle_type_id and t7.type_code ='ass_car_system'
        LEFT JOIN sys_dict t9 ON cast(t9.VALUE as int) = t2.fault_level and  t9.line_id = t1.line_id and t9.vehicle_type_id = t1.vehicle_type_id and t9.type_code ='fault_level'
        LEFT JOIN stru_vehicle_structure_component t10 ON t2.vehicle_structure_code = t10.structure_code and
        t1.vehicle_id = t10.vehicle_id
        WHERE
        t3.del_flag = 0
        <if test="lineId != null and lineId != ''">
            AND t4.id = #{lineId}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId != ''">
            AND t6.id = #{vehicleTypeId}
        </if>
        <if test="vehicleCode != null and vehicleCode != ''">
            AND t3.vehicle_code = #{vehicleCode}
        </if>
        <if test="subsystem != null and subsystem != ''">
            AND t2.subsystem = #{subsystem}
        </if>
        <if test="startTime != null">
            AND t1.start_time &gt; #{startTime}
        </if>
        <if test="startTimeEnd != null">
            AND t1.start_time &lt; #{startTimeEnd}
        </if>
        <if test="location != null and location != ''">
            AND t2.location = #{location}
        </if>
        <if test="faultCode != null and faultCode != ''">
            AND t2.fault_code LIKE '%'||#{faultCode}||'%'
        </if>
        <if test="faultNameCn != null and faultNameCn != ''">
            and t2.name_cn LIKE '%'||#{faultNameCn}||'%'
        </if>
        <if test="faultLevel != null">
            AND t1.fault_level = #{faultLevel}
        </if>
        <if test="faultLevelList != null and faultLevelList.size()>0">
            AND t2.fault_level in
            <foreach collection="faultLevelList" separator="," item="item" open="(" close=")">
                #{item}
            </foreach>
        </if>
        /*2020-03-31 新加字段*/
        <if test="runningStatus != null">
            AND t1.running_status = #{runningStatus}
        </if>
        <if test="suppressedStatus != null">
            AND t1.suppressed_status = #{suppressedStatus}
        </if>
        <if test="confirm != null">
            AND t1.confirm = #{confirm}
        </if>
        <if test="status != null and status != ''">
            AND t1.status = #{status}
        </if>
        <if test="componentId != null">
            AND t1.component_id = #{componentId}
        </if>
        <if test="endStatus==1 ">
            AND t1.end_time is not null
        </if>
        <if test="endStatus==0 ">
            AND t1.end_time is null
        </if>
        ORDER BY
        case t1.suppressed_status
        when true then 2
        when false then 1
        when null then 3 end ASC,
        t1.confirm ASC,
        t1.fault_level DESC,
        case t1.running_status
        when true then 1
        when false then 2
        when null then 3 end ASC,
        t1.start_time DESC
    </select>
    <select id="listCurrentAutoFaultRecordByVehicleCode" resultType="cc.crrc.manage.pojo.mtc.MtcAutoFaultRecordVO">
        SELECT mafr.id AS id,
        mafr.fault_type_key AS faultTypeKey,
        mafr.start_time AS startTime,
        eft.name_cn AS faultNameCn,
        eft.fault_code AS faultCode,
        eft.description,
        eft.fault_level AS faultLevel,
        t9.label AS faultLevelName,
        eft.subsystem,
        eft.location,
        svsc.structure_position AS structurePosition,
        t7.label,
        case mafr.fault_source when '0' then '自动上报' when '1' then '机理故障' end source
        FROM mtc_alarm_warning mafr
        LEFT JOIN ekb_fault_type eft ON mafr.fault_type_key = eft.fault_type_key
        LEFT JOIN stru_vehicle_structure_component svsc
        ON eft.vehicle_structure_code = svsc.structure_code and mafr.vehicle_id = svsc.vehicle_id
        LEFT JOIN mtr_vehicle mv ON mv.id = mafr.vehicle_id
        LEFT JOIN sys_dict t7
        ON t7.value = eft.subsystem and  mafr.line_id = t7.line_id and mafr.vehicle_type_id = t7.vehicle_type_id and t7.type_code ='ass_car_system'
        LEFT JOIN sys_dict t9 ON  cast(t9.VALUE as int) = eft.fault_level and mafr.line_id = t9.line_id and mafr.vehicle_type_id = t9.vehicle_type_id and t7.type_code ='fault_level'
        WHERE mv.vehicle_code = #{vehicleCode}
        AND mv.del_flag = 0
        ORDER BY mafr.status,
        eft.fault_level DESC,
        case mafr.running_status
        when true then 1
        when false then 2
        when null then 3 end,
        mafr.start_time DESC
    </select>

    <select id="getSupressionFault" resultMap="supressionFaultMap">
        select maw.fault_type_key,eft.fault_code as value,eft.name_cn as name
        from mtc_alarm_warning maw,ekb_fault_type eft
        where maw.fault_type_key = eft.fault_type_key and maw.id=#{id}
    </select>

    <select id="getSubSupressionFault" resultMap="supressionFaultMap">
        select maw.fault_type_key,eft.fault_code as value,eft.name_cn as name
        from mtc_alarm_warning maw,ekb_fault_type eft
        where maw.fault_type_key = eft.fault_type_key and maw.suppressed_by=#{key} and suppressed_status=true
    </select>

    <select id="getMtcAutoFaultRecordById" resultType="cc.crrc.manage.pojo.mtc.MtcAutoFaultRecordVO">
        SELECT
        <include refid="MtcAutoFaultRecordAlias"/>,
        case
        (SELECT count(maw.fault_type_key) FROM mtc_alarm_warning maw WHERE maw.suppressed_by=t1.fault_type_key AND
        maw.suppressed_status = true) when 0 then 0 else 1 end ifSuppressed,
        t2.LOCATION,
        t2.subsystem,
        t2.model_code modelCode,
        t7.label,
        t2.name_cn ||'('||t2.name_en||')' AS faultNameCn,
        t2.name_en AS faultNameEn,
        t2.fault_code AS faultCode,
        t1.fault_level AS faultLevel,
        t2.frontline_disposal_recommendations fdr,
        t2.overhaul_suggestions overhaulSuggestion,
        t2.fault_mode faultMode,
        t2.fault_reason faultReason,
        t3.name_cn AS vehicleNameCn,
        t3.name_en AS vehicleNameEn,
        t3.vehicle_code AS vehicleCode,
        t4.NAME AS lineName,
        t4.ID AS lineId,
        t6.ID AS vehicleTypeId,
        t9.name_cn AS componentName,
        t1.fault_source AS source,
        REPLACE ( CAST ( t1.end_time - t1.start_time AS VARCHAR ), 'days', '天' ) AS duration
        FROM
        mtc_alarm_warning t1
        LEFT JOIN ekb_fault_type t2 ON t1.fault_type_key = t2.fault_type_key
        LEFT JOIN mtr_vehicle t3 ON t1.vehicle_id = t3.ID
        LEFT JOIN mtr_line t4 ON t3.metro_line_id = t4.ID
        LEFT JOIN mtr_vehicle_type_relation t5 ON t5.vehicle_id = t1.vehicle_id
        LEFT JOIN mtr_vehicle_type t6 ON t6.ID = t5.vehicle_type_id
        LEFT JOIN sys_dict t7 ON  t7.VALUE= t2.subsystem and t7.line_id = t1.line_id and t7.vehicle_type_id = t1.vehicle_type_id and t7.type_code ='ass_car_system'
        LEFT JOIN stru_component t8 ON t1.component_id = t8.id
        LEFT JOIN stru_component_type t9 ON t8.component_type_id = t9.id
        WHERE
        t1.id=#{id}
        AND t3.del_flag = 0
    </select>
    <select id="getCommOriginalSignalByFaultTypeKey" resultType="cc.crrc.manage.pojo.comm.signal.SignalPO">
        select
        t4.id,
        t4.protocol_id AS protocolId,
        t4.name_cn AS nameCn,
        t4.name_en AS nameEn
        from ekb_fault_type t1
        LEFT JOIN ekb_fault_type_signal_group t2 ON t1.fault_type_key = t2.fault_type_key
        LEFT JOIN comm_original_signal_group t3 ON t2.signal_group_id = t3.group_id
        LEFT JOIN comm_original_signal t4 ON t3.original_signal_name_en = t4.name_en
        LEFT JOIN comm_protocol t5 ON t5.id = t4.protocol_id
        WHERE t2.signal_group_id is not null
        and t5.vehicle_type_id = t1.vehicle_type_id
        and t5.del_flag = 0
        <if test="faultTypeKey != null and faultTypeKey != ''">
            and t1.fault_type_key = #{faultTypeKey}
        </if>
    </select>
    <select id="getVehicleFaultLevel" resultType="java.lang.String">
        SELECT MAX(mafr.fault_level) faultLevel
        FROM
        mtc_alarm_warning mafr
        LEFT JOIN ekb_fault_type eft ON mafr.fault_type_key = eft.fault_type_key
        LEFT JOIN mtr_vehicle mv ON mv.id = mafr.vehicle_id
        WHERE
        mv.vehicle_code = #{vehicleCode}
        AND mv.del_flag = 0
        AND mafr.end_time IS NULL
        AND mafr.status = 0
    </select>
    <select id="listCurrentAutoFaultRecordByLineId" resultType="cc.crrc.manage.pojo.mtc.MtcAutoFaultRecordVO">
        SELECT mafr.id AS id,
        mafr.fault_type_key AS faultTypeKey,
        mafr.start_time AS startTime,
        eft.name_cn AS faultNameCn,
        eft.fault_code AS faultCode,
        eft.description,
        mafr.fault_level AS faultLevel,
        eft.subsystem,
        eft.location,
        t7.label,
        case mafr.fault_source when '0' then '自动上报' when '1' then '机理故障' end source
        FROM mtc_alarm_warning mafr
        LEFT JOIN ekb_fault_type eft ON mafr.fault_type_key = eft.fault_type_key
        LEFT JOIN mtr_vehicle mv ON mv.id = mafr.vehicle_id
        LEFT JOIN sys_dict t7 ON t7.line_id = mafr.line_id and t7.vehicle_type_id = mafr.vehicle_type_id and t7.type_code ='ass_car_system'
            and  t7.value = eft.subsystem
        WHERE mv.metro_line_id = #{lineId}
        AND mv.del_flag = 0
        ORDER BY mafr.status,
        mafr.fault_level DESC,
        case mafr.running_status
        when true then 1
        when false then 2
        when null then 3 end,
        mafr.start_time DESC
    </select>
    <select id="findCurrentAutoFaultRecordByVehicleCode" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM
        mtc_alarm_warning mafr
        LEFT JOIN ekb_fault_type eft ON mafr.fault_type_key = eft.fault_type_key
        LEFT JOIN mtr_vehicle mv ON mv.id = mafr.vehicle_id
        WHERE
        mv.vehicle_code = #{vehicleCode}
        AND mv.del_flag = 0
        AND mafr.status = '已处理'
    </select>
    <select id="getMaxTroubleLevelList" resultType="java.util.HashMap">
        SELECT
        CAST(max(mafr.fault_level) AS varchar) AS max_level,
        mv.vehicle_code AS vehicle_code
        FROM mtc_alarm_warning mafr
        LEFT JOIN ekb_fault_type eft ON mafr.fault_type_key = eft.fault_type_key
        LEFT JOIN mtr_vehicle mv ON mafr.vehicle_id = mv.id
        WHERE mafr.end_time IS NULL
        GROUP BY mv.vehicle_code
    </select>
    <update id="changeStatusFromFracas">
        UPDATE mtc_alarm_warning
        SET
        status = #{status},
        end_time = #{endTime}
        where
        id=#{id}
    </update>
    <select id="countNotConfirmNumberFroTrain" resultType="java.lang.Integer">
        SELECT COUNT
        (CASE WHEN t1.status = '0' THEN t1.status END)
        FROM mtc_alarm_warning t1
        LEFT JOIN mtr_vehicle t2 ON t1.vehicle_id = t2.ID
        WHERE t2.vehicle_code = #{vehicleCode}
        AND t1.suppressed_status = false

    </select>
    <select id="countNotConfirmNumberForLine" resultType="java.lang.Integer">
        SELECT COUNT
        (CASE WHEN t1.status = '0' THEN t1.status END) AS confirmNumber
        FROM mtc_alarm_warning t1
        LEFT JOIN mtr_vehicle t2 ON t1.vehicle_id = t2.
        ID
        LEFT JOIN mtr_line t3 ON t2.metro_line_id = t3.ID
        WHERE t3.ID = #{lineId}
        AND t1.suppressed_status = false

    </select>
    <select id="countNotConfirmNumber" resultType="java.lang.Integer">
        SELECT COUNT
        (CASE WHEN t1.status = '0' THEN t1.status END) AS confirmNumber
        FROM mtc_alarm_warning t1
        WHERE t1.suppressed_status = false
    </select>
    <select id="countFaultNumber" resultType="java.lang.Integer">
        SELECT
        COUNT(1)
        FROM
        mtc_alarm_warning t1
    </select>
    <select id="countFaultNumberForTrain" resultType="java.lang.Integer">
        SELECT
        COUNT(1)
        FROM
        mtc_alarm_warning t1
        LEFT JOIN mtr_vehicle t2 ON t1.vehicle_id = t2.ID
        WHERE
        t2.vehicle_code = #{vehicleCode}
    </select>
    <select id="getUniqueFlag" resultType="java.lang.String">
        SELECT
        CONCAT(#{lineId}, '-', #{vehicleId}, '-', t.id) AS uniqueFlag
        FROM
        stru_vehicle_structure_component t
        WHERE
        t.del_flag = 0
        AND t.structure_code = #{structureCode}
        AND t.vehicle_id = #{vehicleId}
    </select>
    <select id="getParentStructureCode" resultType="java.lang.String">
        SELECT
        t.parent_structure_code
        FROM
        stru_vehicle_structure_component t
        WHERE
        t.del_flag = 0
        AND t.structure_position = #{structurePosition}
        AND t.vehicle_id = #{vehicleId}
    </select>
    <!--查询实时告警和状态预警数据list-->
    <select id="getRealTimeAlarmOrEarlyWarningList" resultType="cc.crrc.manage.pojo.mtc.MtcAlarmWarningVO">
        SELECT fault_."id",
        fault_."fault_type_key" faultTypeKey,
        fault_."faultSource",
        fault_.startTime,
        faultType_."name_cn" faultNameCn,
        faultType_."fault_level" faultLevel,
        faultType_.fault_mode faultMode
        FROM (
        SELECT record_."id",
        record_."fault_type_key",
        record_."start_time" startTime,
        '0' "faultSource"
        FROM mtc_alarm_warning record_
        LEFT JOIN mtr_vehicle vehicle_ ON record_.vehicle_id = vehicle_."id"
        LEFT JOIN mtr_line line_ ON vehicle_.metro_line_id = line_."id"
        WHERE line_."id" = #{lineId}
        <if test="trainCode != '' and trainCode != null">
            AND vehicle_.vehicle_code = #{trainCode}
        </if>
        UNION ALL
        SELECT record_."id",
        rule_."fault_type_key",
        record_."start_time" startTime,
        '1' "faultSource"
        FROM mtc_mechanism_alarm_record record_
        LEFT JOIN mtc_mechanism_alarm_rule rule_ ON record_."alarm_rule_id" = rule_."id"
        LEFT JOIN mtr_vehicle vehicle_ ON record_.vehicle_id = vehicle_."id"
        LEFT JOIN mtr_line line_ ON vehicle_.metro_line_id = line_."id"
        WHERE line_."id" = #{lineId}
        <if test="trainCode != '' and trainCode != null">
            AND vehicle_.vehicle_code = #{trainCode}
        </if>
        ) fault_
        LEFT JOIN ekb_fault_type faultType_ ON fault_.fault_type_key = faultType_.fault_type_key
        WHERE faultType_.fault_mode = #{faultMode}
    </select>
    <select id="listCurrentMechanismFaultRecordByLineId" resultType="cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRecordVO">
        SELECT * FROM ( (
        SELECT
        t1.id,
        t1.vehicle_id AS vehicleId,
        t1.alarm_rule_id AS alarmRuleId,
        t1.alarm_snapshot AS alarmSnapshot,
        t1.start_time AS startTime,
        t1.end_time AS endTime,
        t1.status,
        t1.modify_by AS modifyBy,
        t1.modify_time AS modifyTime,
        t2.location,
        t2.subsystem,
        t2.name_cn AS faultNameCn,
        t2.name_en AS faultNameEn,
        t2.fault_code AS faultCode,
        t2.fault_level AS faultLevel,
        t3.name_cn AS vehicleNameCn,
        t3.name_en AS vehicleNameEn,
        t3.vehicle_code AS vehicleCode,
        t4.name AS lineName,
        t4.id AS lineId,
        t6.id AS vehicleTypeId,
        t8.label,
        t7.puw_statue AS puwStatue,
        t7.test_status AS testStatus,
        t1.close_statue AS closeStatue,
        '机理故障' AS faultSource
        FROM
        mtc_alarm_warning t1
        LEFT JOIN mtc_mechanism_alarm_rule t7
        ON t1.alarm_rule_id = t7.id
        LEFT JOIN ekb_fault_type t2
        ON t1.fault_type_key = t2.fault_type_key
        LEFT JOIN mtr_vehicle t3
        ON t1.vehicle_id =t3.id
        LEFT JOIN mtr_line t4
        ON t3.metro_line_id = t4.id
        LEFT JOIN mtr_vehicle_type t6
        ON t6.id = t7.vehicle_type_id
        LEFT JOIN sys_dict t8 ON t8.type_code = 'ass_car_system' and t8.value = t2.subsystem AND t8.line_id =
        t2.line_id AND t8.vehicle_type_id = t8.vehicle_type_id
        where
        t3.vehicle_code IN
        <foreach collection="onlineVehicles" item="vehicleCode" index="index" open="(" separator="," close=")">
            #{vehicleCode}
        </foreach>
        AND t1.fault_source = '1'
        AND t1.close_statue = false
        AND t1.end_time is null
        AND t3.del_flag = 0
        AND t7.test_status = false)
        union all
       ( SELECT
        t1.id,
        t1.vehicle_id AS vehicleId,
        t1.alarm_rule_id AS alarmRuleId,
        t1.alarm_snapshot AS alarmSnapshot,
        t1.start_time AS startTime,
        t1.end_time AS endTime,
        t1.status,
        t1.modify_by AS modifyBy,
        t1.modify_time AS modifyTime,
        t2.location,
        t2.subsystem,
        t2.name_cn AS faultNameCn,
        t2.name_en AS faultNameEn,
        t2.fault_code AS faultCode,
        t2.fault_level AS faultLevel,
        t3.name_cn AS vehicleNameCn,
        t3.name_en AS vehicleNameEn,
        t3.vehicle_code AS vehicleCode,
        t4.name AS lineName,
        t4.id AS lineId,
        t6.id AS vehicleTypeId,
        t8.label,
        t7.puw_statue AS puwStatue,
        t7.test_status AS testStatus,
        t1.close_statue AS closeStatue,
        '自动故障' AS faultSource
        FROM
        mtc_alarm_warning t1
        LEFT JOIN mtc_mechanism_alarm_rule t7
        ON t1.alarm_rule_id = t7.id
        LEFT JOIN ekb_fault_type t2
        ON t1.fault_type_key = t2.fault_type_key
        LEFT JOIN mtr_vehicle t3
        ON t1.vehicle_id =t3.id
        LEFT JOIN mtr_line t4
        ON t3.metro_line_id = t4.id
        LEFT JOIN mtr_vehicle_type t6
        ON t6.id = t7.vehicle_type_id
        LEFT JOIN sys_dict t8 ON t8.type_code = 'ass_car_system' and t8.value = t2.subsystem AND t8.line_id =
        t2.line_id AND t8.vehicle_type_id = t8.vehicle_type_id
        where
        t3.vehicle_code IN
        <foreach collection="onlineVehicles" item="vehicleCode" index="index" open="(" separator="," close=")">
            #{vehicleCode}
        </foreach>
        AND t1.fault_source = '0'
        AND t1.close_statue = false
        AND t2.fault_level = 3
        AND t1.end_time is null
        AND t3.del_flag = 0
        )) combined_result
        ORDER BY
        case faultLevel
        when'1' then 1
        when'2' then 2
        when'3' then 3
        when'0' then 4
        end,
        startTime DESC,
        vehicleCode ASC
    </select>
</mapper>