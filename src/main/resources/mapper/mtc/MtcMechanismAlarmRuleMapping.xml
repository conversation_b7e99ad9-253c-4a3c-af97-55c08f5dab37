<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtc.MtcMechanismAlarmRuleMapping">
    <sql id="MtcMechanismAlarmRuleAlias">
        rule
        .
        id
        AS id,
        rule.vehicle_type_id AS vehicleTypeId,
        rule.fault_type_key AS faultTypeId,
        rule.name AS name,
        rule.content AS conntent,
        rule.enable AS enable,
        rule.image_file AS imageFile,
        rule.description AS description,
        rule.modify_by AS modifyBy,
        rule.modify_time AS modifyTime,
        rule.create_by AS createBy,
        rule.create_time AS createTime,
        rule.signal_cycle AS signalCycle,
        rule.test_status AS testStatus,
        rule.encryption_status AS encryptionStatus
    </sql>

    <!--  列表显示机理规则，信息不包含json字符串  -->
    <select id="list" resultType="cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRuleVO">
        SELECT
        rule.id AS id,
        rule.vehicle_type_id AS vehicleTypeId,
        mvt.name AS vehicleTypeName,
        rule.fault_type_key AS faultTypeKey,
        fault.location AS location,
        fault.name_cn ||'('||fault.name_en||')' AS faultName,
        fault.subsystem AS subsystem,
        sd.label AS subSystemName,
        rule.name AS name,
        rule.enable AS enable,
        rule.description AS description,
        file.group||'/'||file.file_location AS imageUrl,
        rule.signal_cycle AS signalCycle,
        rule.test_status AS testStatus,
        rule.line_id AS lineId,
        rule.content AS content,
        rule.encryption_status AS encryptionStatus,
        rule.puw_statue AS puwStatue
        FROM mtc_mechanism_alarm_rule rule
        LEFT JOIN sys_file file ON rule.image_file = file.id
        LEFT JOIN ekb_fault_type fault ON rule.fault_type_key = fault.fault_type_key
        JOIN mtr_vehicle_type mvt ON mvt.id = rule.vehicle_type_id
        LEFT JOIN sys_dict sd ON sd.type_code = 'ass_car_system' and sd.value = fault.subsystem and sd.del_flag = '0'
        WHERE
        rule.line_id = sd.line_id
        AND rule.vehicle_type_id = sd.vehicle_type_id
        <if test="id != null and id !=''">
            AND rule.id = #{id}
        </if>
        <if test="lineId != null and lineId !=''">
            AND rule.line_id = #{lineId}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId !=''">
            AND rule.vehicle_type_id = #{vehicleTypeId}
        </if>
        <if test="subsystem != null and subsystem != ''">
            AND fault.subsystem = #{subsystem}
        </if>
        <if test="location != null and location != ''">
            AND fault.location = #{location}
        </if>
        <if test="faultTypeKey != null and faultTypeKey != ''">
            AND rule.fault_type_key = #{faultTypeKey}
        </if>
        <if test="faultNameCn != null and faultNameCn != ''">
            AND fault.name_cn LIKE '%'||#{faultNameCn}||'%'
        </if>
        <if test="name != null and name != ''">
            AND rule.name LIKE '%'||#{name}||'%'
        </if>
        <if test="enable != null">
            AND rule.enable = #{enable}
        </if>
        <if test="description != null and description != ''">
            AND rule.description LIKE '%'||#{description}||'%'
        </if>
        <if test="signalCycle != null and signalCycle !=''">
            AND rule.signal_cycle = #{signalCycle}
        </if>
        <if test="testStatus != null">
            AND rule.test_status = #{testStatus}
        </if>
        <if test="encryptionStatus != null">
            AND rule.encryption_status = #{encryptionStatus}
        </if>
        ORDER BY
        rule.create_time, rule.id DESC
    </select>

    <select id="getJson" resultType="java.util.Map">
        SELECT content, test_status AS "testStatus", encryption_status AS "encryptionStatus"
        FROM mtc_mechanism_alarm_rule
        WHERE id = #{id}
    </select>

    <insert id="add">
        INSERT INTO mtc_mechanism_alarm_rule
        (id, vehicle_type_id, fault_type_key, name, description, create_by, create_time, modify_by, modify_time,
         line_id, signal_cycle, encryption_status)
        VALUES (#{id}, #{vehicleTypeId}, #{faultTypeKey}, #{name}, #{description}, #{createBy}, now(), #{modifyBy},
                now(), #{lineId}, #{signalCycle}, #{encryptionStatus})
    </insert>

    <update id="update">
        UPDATE mtc_mechanism_alarm_rule
        SET
        <if test="vehicleTypeId != null and vehicleTypeId!=''">
            vehicle_type_id = #{vehicleTypeId},
        </if>
        <if test="faultTypeKey != null and faultTypeKey != ''">
            fault_type_key = #{faultTypeKey},
        </if>
        <if test="name != null and name != ''">
            name = #{name},
        </if>
        <if test="enable != null">
            enable = #{enable},
        </if>
        <if test="imageFile != null">
            image_file = #{imageFile},
        </if>
        <if test="description != null">
            description = #{description},
        </if>
        <if test="content != null and content != ''">
            content = #{content},
        </if>
        <if test="signalCycle != null and signalCycle != ''">
            signal_cycle = #{signalCycle},
        </if>
        <if test="testStatus != null">
            test_status = #{testStatus},
        </if>
        <if test="encryptionStatus != null">
            encryption_status = #{encryptionStatus},
        </if>
        <if test="puwStatue != null and puwStatue!=''">
            puw_statue = #{puwStatue},
        </if>
        modify_by = #{modifyBy},
        modify_time = now()
        WHERE id = #{id}
    </update>

    <update id="updateTestStatus">
        UPDATE mtc_mechanism_alarm_rule
        SET
        <if test="testStatus != null">
            test_status = #{testStatus},
        </if>
        modify_by = #{modifyBy},
        modify_time = now()
        WHERE id = #{id}
    </update>

    <delete id="delete">
        DELETE
        FROM mtc_mechanism_alarm_rule
        WHERE id = #{id}
    </delete>

    <!--2020-03-06 lixin 查询数字量信号-->
    <!--2021-01-12 Jin GuoYang 添加子系统查询和中文名的模糊查询-->
    <select id="booleanSignal" resultType="cc.crrc.manage.pojo.comm.signal.CommSignalVO">
        SELECT
        t1.id,
        t1.name_cn ||'('||t1.name_en||')' AS nameCn,
        t1.name_en AS nameEn,
        t1.data_type AS dataType
        FROM
        comm_original_signal t1
        LEFT JOIN
        comm_protocol t2
        ON
        t1.protocol_id = t2.id
        WHERE
        t1.data_type = 'BOOLEAN1'
        and
        t2.vehicle_type_id = #{vehicleTypeId}
        <if test="location != null and location != ''">
            and t1.location = #{location}
        </if>
        <if test="subsystem != null and subsystem != ''">
            and t1.subsystem = #{subsystem}
        </if>
        <if test="nameCn != null and nameCn != ''">
            and t1.name_cn like '%'||#{nameCn}||'%'
        </if>
        LIMIT 100
    </select>

    <!--2021-01-12 Jin GuoYang 添加子系统查询和中文名的模糊查询-->
    <select id="analogSignal" resultType="cc.crrc.manage.pojo.comm.signal.CommSignalVO">
        SELECT
        t1.id,
        t1.name_cn ||'('||t1.name_en||')' AS nameCn,
        t1.name_en AS nameEn,
        t1.data_type AS dataType
        FROM
        comm_original_signal t1
        LEFT JOIN
        comm_protocol t2
        ON
        t1.protocol_id = t2.id
        WHERE
        t1.data_type != 'BOOLEAN1'
        and
        t2.vehicle_type_id = #{vehicleTypeId}
        <if test="location != null and location != ''">
            and t1.location = #{location}
        </if>
        <if test="subsystem != null and subsystem != ''">
            and t1.subsystem = #{subsystem}
        </if>
        <if test="nameCn != null and nameCn != ''">
            and t1.name_cn like '%'||#{nameCn}||'%'
        </if>
        ORDER BY
        t1.name_cn
    </select>

    <select id="selectById" resultType="cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRulePO">
        SELECT id,
               vehicle_type_id   AS vehicleTypeId,
               fault_type_key    AS faultTypeKey,
               enable,
               content,
               signal_cycle      AS signalCycle,
               test_status       AS testStatus,
               encryption_status AS encryptionStatus
        FROM mtc_mechanism_alarm_rule
        WHERE id = #{id}
    </select>
    <select id="mechanismAlarmRuleListByStruCodeAndVehicleType"
            resultType="cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRuleVO">
        SELECT
        rule.id AS id,
        rule.vehicle_type_id AS vehicleTypeId,
        mvt.name AS vehicleTypeName,
        rule.fault_type_key AS faultTypeKey,
        fault.location AS location,
        fault.name_cn ||'('||fault.name_en||')' AS faultName,
        fault.subsystem AS subsystem,
        sd.label AS subSystemName,
        rule.name AS name,
        rule.enable AS enable,
        rule.description AS description,
        file.group||'/'||file.file_location AS imageUrl,
        rule.line_id AS lineId,
        rule.test_status AS testStatus
        FROM
        mtc_mechanism_alarm_rule rule
        LEFT JOIN sys_file file ON rule.image_file = file.id
        LEFT JOIN ekb_fault_type fault ON rule.fault_type_key = fault.fault_type_key
        LEFT JOIN mtr_vehicle_type mvt ON mvt.id = rule.vehicle_type_id
        LEFT JOIN sys_dict sd ON sd.type_code = 'ass_car_system' and sd.value = fault.subsystem and sd.del_flag = '0' and sd.vehicle_type_id = mvt.id
        WHERE
        rule.line_id = sd.line_id
        AND fault.del_flag = 0
        AND mvt.del_flag = 0
        AND file.del_flag = 0
        AND rule.vehicle_type_id = sd.vehicle_type_id
        <if test="vehicleTypeId != null and vehicleTypeId !=''">
            AND mvt.id = #{vehicleTypeId}
        </if>
        <if test="structureCode != null and structureCode != ''">
            AND fault.vehicle_structure_code LIKE '%'||#{structureCode}||'%'
        </if>
        AND rule.encryption_status = false
        ORDER BY
        rule.create_time desc
    </select>
    <select id="ruleListForCopy" resultType="cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRuleVO">
        SELECT
        rule.id AS id,
        rule.name AS name,
        -- rule.enable AS enable,
        -- rule.content AS content,
        -- file.group||'/'||file.file_location AS imageUrl,
        rule.test_status AS testStatus,
        rule.encryption_status AS encryptionStatus,
        rule.line_id AS lineId
        FROM
        mtc_mechanism_alarm_rule rule
        -- LEFT JOIN sys_file file ON rule.image_file = file.id
        WHERE
        1=1
        -- AND file.del_flag = 0
        <if test="lineId != null and lineId !=''">
            AND rule.line_id = #{lineId}
        </if>
        <if test="name != null and name != ''">
            AND rule.name LIKE '%'||#{name}||'%'
        </if>
        <if test="encryptionStatus != null and encryptionStatus == false">
            AND rule.encryption_status = #{encryptionStatus}
        </if>
        ORDER BY
        rule.create_time desc
    </select>
    <select id="booleanSignal64ms" resultType="cc.crrc.manage.pojo.comm.signal.CommSignalVO">
        SELECT
        t1.id,
        t1.name_cn ||'('||t1.name_en||')' AS nameCn,
        t1.name_en AS nameEn,
        t1.data_type AS dataType
        FROM
        comm_original_signal_64ms t1
        WHERE
        t1.data_type = 'BOOLEAN1'
        <if test="location != null and location != ''">
            and t1.location = #{location}
        </if>
        <if test="subsystem != null and subsystem != ''">
            and t1.subsystem = #{subsystem}
        </if>
        <if test="nameCn != null and nameCn != ''">
            and t1.name_cn like '%'||#{nameCn}||'%'
        </if>
        ORDER BY
        t1.name_cn
    </select>
    <select id="analogSignal64ms" resultType="cc.crrc.manage.pojo.comm.signal.CommSignalVO">
        SELECT
        t1.id,
        t1.name_cn ||'('||t1.name_en||')' AS nameCn,
        t1.name_en AS nameEn,
        t1.data_type AS dataType
        FROM
        comm_original_signal_64ms t1
        WHERE
        t1.data_type != 'BOOLEAN1'
        <if test="location != null and location != ''">
            and t1.location = #{location}
        </if>
        <if test="subsystem != null and subsystem != ''">
            and t1.subsystem = #{subsystem}
        </if>
        <if test="nameCn != null and nameCn != ''">
            and t1.name_cn like '%'||#{nameCn}||'%'
        </if>
        ORDER BY
        t1.name_cn
    </select>
    <select id="ruleListForCopyEncryption" resultType="cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRuleVO">
        SELECT
        rule.id AS id,
        rule.name AS name,
        rule.test_status AS testStatus,
        rule.encryption_status AS encryptionStatus,
        rule.line_id AS lineId
        FROM
        mtc_mechanism_alarm_rule rule
        WHERE
        1=1
        <choose>
            <when test="idList != null and idList.size > 0">
                AND id in
                <foreach collection="idList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        ORDER BY
        rule.create_time desc
    </select>
    <select id="ruleListForEncryption" resultType="cc.crrc.manage.pojo.mtc.MtcMechanismAlarmRuleVO">
        SELECT
        rule.id AS id,
        rule.vehicle_type_id AS vehicleTypeId,
        mvt.name AS vehicleTypeName,
        rule.fault_type_key AS faultTypeKey,
        fault.location AS location,
        fault.name_cn ||'('||fault.name_en||')' AS faultName,
        fault.subsystem AS subsystem,
        sd.label AS subSystemName,
        rule.name AS name,
        rule.enable AS enable,
        rule.description AS description,
        file.group||'/'||file.file_location AS imageUrl,
        rule.signal_cycle AS signalCycle,
        rule.test_status AS testStatus,
        rule.line_id AS lineId,
        rule.encryption_status AS encryptionStatus,
        rule.puw_statue AS puwStatue
        FROM
        mtc_mechanism_alarm_rule rule
        LEFT JOIN sys_file file
        ON rule.image_file = file.id
        LEFT JOIN ekb_fault_type fault
        ON rule.fault_type_key = fault.fault_type_key
        JOIN mtr_vehicle_type mvt ON mvt.id = rule.vehicle_type_id
        LEFT JOIN sys_dict sd ON sd.type_code = 'ass_car_system' and sd.value = fault.subsystem and sd.del_flag = '0'
        WHERE
        rule.line_id = sd.line_id
        AND rule.vehicle_type_id = sd.vehicle_type_id
        AND rule.encryption_status = true
        <choose>
            <when test="idList != null and idList.size > 0">
                AND rule.id in
                <foreach collection="idList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
        </choose>
        ORDER BY
        rule.create_time desc
    </select>

</mapper>