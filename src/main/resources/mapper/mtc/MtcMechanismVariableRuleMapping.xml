<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtc.MtcMechanismVariableRuleMapping">
    <sql id="MtcMechanismVariableRuleAlias">
        rule.id AS id,
        rule.vehicle_type_id AS vehicleTypeId,
        rule.name AS name,
        rule.content AS content,
        rule.enable AS enable,
        rule.image_file AS imageFile,
        rule.description AS description,
        rule.modify_by AS modifyBy,
        rule.modify_time AS modifyTime,
        rule.create_by AS createBy,
        rule.create_time AS createTime,
        rule.location AS location,
        rule.subsystem AS subsystem
    </sql>

<!--  列表显示全局变量，信息不包含json字符串  -->
    <select id="list" resultType="cc.crrc.manage.pojo.mtc.MtcMechanismVariableRuleVO">
        SELECT
        rule.id AS id,
        rule.vehicle_type_id AS vehicleTypeId,
        rule.line_id AS lineId,
        mvt.name AS vehicleTypeName,
        rule.location AS location,
        rule.subsystem AS subsystem,
        sd.label AS subSystemName,
        rule.name AS name,
        rule.enable AS enable,
        rule.description AS description,
        file.url AS imageUrl
        FROM
        mtc_mechanism_variable_rule    rule
        LEFT JOIN sys_file file ON rule.image_file = file.id
        JOIN mtr_vehicle_type mvt ON mvt.id = rule.vehicle_type_id
        LEFT JOIN sys_dict sd ON sd.type_id = '28' and sd.value = rule.subsystem and sd.del_flag = '0'
        WHERE
        1=1
        <if test="id != null and id !=''">
            AND rule.id = #{id}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId !=''">
            AND rule.vehicle_type_id = #{vehicleTypeId}
        </if>
        <if test="lineId != null and lineId !=''">
            AND rule.line_id = #{lineId}
        </if>
        <if test="subsystem != null and subsystem != ''">
            AND rule.subsystem = #{subsystem}
        </if>
        <if test="location != null and location != ''">
            AND rule.location = #{location}
        </if>
        <if test="name != null and name != ''">
            AND rule.name LIKE '%'||#{name}||'%'
        </if>
        <if test="enable != null">
            AND rule.enable = #{enable}
        </if>
        <if test="description != null and description != ''">
            AND rule.description LIKE '%'||#{description}||'%'
        </if>
    </select>

    <select id="getJson" resultType="java.util.Map">
        SELECT
            content
        FROM
            mtc_mechanism_variable_rule
        WHERE id = #{id}
    </select>

    <insert id="add" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO mtc_mechanism_variable_rule
            (id,vehicle_type_id, name, description, create_by, create_time, modify_by, modify_time, location, subsystem,line_id)
        VALUES
            (#{id},#{vehicleTypeId}, #{name}, #{description}, #{createBy}, now(), #{modifyBy}, now(), #{location}, #{subsystem},#{lineId})
    </insert>

    <update id="update">
        UPDATE mtc_mechanism_variable_rule
        SET
            <if test="vehicleTypeId != null and vehicleTypeId !=''">
                vehicle_type_id = #{vehicleTypeId},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="enable != null">
                enable = #{enable},
            </if>
            <if test="imageFile != null and imageFile !=''">
                image_file = #{imageFile},
            </if>
            <if test="description != null and description != ''">
                description = #{description},
            </if>
            <if test="content != null and content != ''">
                content = #{content},
            </if>
            <if test="location != null and location != ''">
                location = #{location},
            </if>
            <if test="subsystem != null and subsystem != ''">
                subsystem = #{subsystem},
            </if>
            <if test="lineId != null and lineId !=''">
                line_id = #{lineId},
            </if>
            modify_by = #{modifyBy},
            modify_time = now()
        WHERE id = #{id}
    </update>

    <delete id="delete">
        DELETE FROM mtc_mechanism_variable_rule
        WHERE id = #{id}
    </delete>

    <select id="selectById"   resultType="cc.crrc.manage.pojo.mtc.MtcMechanismVariableRulePO">
        SELECT
            id,
            vehicle_type_id AS vehicleTypeId,
            content
        FROM
            mtc_mechanism_variable_rule
        WHERE id = #{id}
    </select>

    <select id="variables" resultType="cc.crrc.manage.pojo.mtc.MtcMechanismVariableRuleVO">
        SELECT
        rule.id AS id,
        rule.name AS name,
        rule.description AS description
        FROM
        mtc_mechanism_variable_rule rule
        WHERE
        rule.vehicle_type_id = #{vehicleTypeId}
        <if test="location != null and location != ''">
            AND rule.location = #{location}
        </if>
        <if test="subsystem != null and subsystem != ''">
            and rule.subsystem = #{subsystem}
        </if>
        <if test="nameCn != null and nameCn != ''">
            and rule.name like '%'||#{nameCn}||'%'
        </if>
        ORDER BY
        rule.name
    </select>

</mapper>