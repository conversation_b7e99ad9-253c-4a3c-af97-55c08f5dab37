<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.SysDictMapping">
    <sql id="sysDictColumnAlias">
        t.id,
        t.sort_number AS sortNumber,
        t.code,
        t.label,
        t.value,
        t.type_id AS typeId,
        t.description,
        t.line_id AS lineId,
        t.vehicle_type_id AS vehicleTypeId,
        t.line_name AS lineName,
        t.vehicle_type_name AS vehicleTypeName,
        t.type_code AS typeCode
    </sql>
    <!--分页取得数据字典列表-->
    <select id="listDict" resultType="cc.crrc.manage.pojo.SysDictVO">
        SELECT
            <include refid="sysDictColumnAlias"></include>
        FROM
            sys_dict t
        WHERE
            t.del_flag = '0'
            <if test="typeId != null and typeId!=''">
                AND t.type_id = #{typeId}
            </if>
            <if test="label != null and label != ''">
                AND t.label LIKE '%\'||#{label}||'%'
            </if>
            <if test="description != null and description != ''">
                AND t.description LIKE '%\'||#{description}||'%'
            </if>
            <if test="typeCode != null and typeCode != ''">
                AND t.type_code = #{typeCode}
            </if>
            <if test="lineId != null and lineId != ''">
                AND t.line_id = #{lineId}
            </if>
            <if test="vehicleTypeId != null and vehicleTypeId != ''">
                AND t.vehicle_type_id = #{vehicleTypeId}
            </if>
        ORDER BY
            t.type_code, t.sort_number, t.modify_time ASC
    </select>
    <!--根据类型id筛选字典数据-->
    <select id="listByTypeId" resultType="cc.crrc.manage.pojo.SysDictVO">
        SELECT
            <include refid="sysDictColumnAlias"></include>
        FROM
            sys_dict t
        WHERE
            t.del_flag = '0'
            <if test="typeId != null and typeId !=''">
                AND t.type_id = #{typeId}
            </if>
        order by
        t.sort_number

    </select>
    <!--根据类型名称筛选字典数据-->
    <select id="listByType" resultType="cc.crrc.manage.pojo.SysDictVO">
        SELECT
            <include refid="sysDictColumnAlias"></include>
        FROM
            sys_dict t
        WHERE
            t.del_flag = '0'
            <if test="typeCode != null">
                AND t.type_code = #{typeCode}
            </if>
        ORDER BY
            t.sort_number,t.modify_time DESC
    </select>
    <!--根据键值、标签、类型筛选个数-->
    <select id="getCountByValue_typeId" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM
            sys_dict
        WHERE
            value = #{value} AND
            type_id = #{typeId}
            <if test="id != null and id !=''">
                AND id != #{id}
            </if>
    </select>
    <select id="getLabel" resultType="java.lang.String">
        select label from sys_dict where type_id = #{typeId} and value = #{value}
    </select>
    <!--新增数据-->
    <insert id="addDict">
        INSERT INTO sys_dict (
            id,
            sort_number,
            code,
            label,
            value,
            type_id,
            description,
            modify_time,
            modify_by,
            create_time,
            create_by,
            del_flag,
            line_id,
            vehicle_type_id,
            line_name,
            vehicle_type_name,
            type_code
        )
        VALUES (
            #{id},
            #{sortNumber},
            #{code},
            #{label},
            #{value},
            #{typeId},
            #{description},
            #{modifyTime},
            #{modifyBy},
            #{createTime},
            #{createBy},
            #{delFlag},
            #{lineId},
            #{vehicleTypeId},
            #{lineName},
            #{vehicleTypeName},
            #{typeCode}
        )
    </insert>
    <!--更新数据-->
    <update id="updateDict">
        UPDATE sys_dict
        <trim prefix="set" suffixOverrides=",">
            <if test="sortNumber != null">
                sort_number = #{sortNumber},
            </if>
            <if test="code != null and code != ''">
                code = #{code},
            </if>
            <if test="label != null and label != ''">
                label = #{label},
            </if>
            <if test="value != null and value != ''">
                value = #{value},
            </if>
            <if test="typeId != null and typeId !=''">
                type_id = #{typeId},
            </if>
            <if test="description != null and description != ''">
                description = #{description},
            </if>
            <if test="modifyBy != null and modifyBy != ''">
                modify_by = #{modifyBy},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>
            <if test="delFlag != null and delFlag != ''">
                del_flag = #{delFlag},
            </if>

            <if test="lineId != null and lineId != ''">
                line_id = #{lineId},
            </if>
            <if test="vehicleTypeId != null and vehicleTypeId != ''">
                vehicle_type_id = #{vehicleTypeId},
            </if>
            <if test="lineName != null">
                line_name = #{lineName},
            </if>
            <if test="vehicleTypeName != null">
                vehicle_type_name = #{vehicleTypeName},
            </if>
            <if test="typeCode != null">
                type_code = #{typeCode},
            </if>
        </trim>
        WHERE
            id = #{id}
    </update>
    <select id="listDictUniversal" resultType="cc.crrc.manage.pojo.SysDictVO">
        SELECT
        DISTINCT
        t.id,
        t.sort_number AS sortNumber,
        t.code,
        t.label,
        t.value,
        t.type_id AS typeId,
        t.description,
        t.line_id AS lineId,
        t.vehicle_type_id AS vehicleTypeId,
        t.line_name AS lineName,
        t.vehicle_type_name AS vehicleTypeName,
        t.type_code AS typeCode
        FROM
        sys_dict t
        WHERE
        t.del_flag = '0'
        <if test="typeId != null and typeId !=''">
            AND t.type_id = #{typeId}
        </if>
        <if test="typeCode != null and typeCode != ''">
            AND t.type_code = #{typeCode}
        </if>
        <if test="lineId != null and lineId != ''">
            AND t.line_id = #{lineId}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId != ''">
            AND t.vehicle_type_id = #{vehicleTypeId}
        </if>
        ORDER BY
        t.sort_number ASC
    </select>
</mapper>