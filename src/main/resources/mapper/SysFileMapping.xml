<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.SysFileMapping">

    <sql id="SysFileColumnAlias">
        t.id,
        t.name,
        t.url,
        t.type,
        t.format,
        t.size,
        t.hash_code as hashCode,
        t.del_flag as delFlag,
        t.create_by as createBy,
        t.create_time as createTime,
        t.modify_by as modifyBy,
        t.modify_time as modifyTime,
        t.remark,
        t.group,
        t.file_location as fileLocation
    </sql>

    <insert id="addSysFile">
        INSERT INTO
        sys_file
        (
        id,
        name,
        url,
        type,
        format,
        size,
        hash_code,
        del_flag,
        create_by,
        create_time,
        modify_by,
        modify_time,
        remark,
        "group",
        file_location
        )
        VALUES
        (
        #{id},
        #{name},
        #{url},
        #{type},
        #{format},
        #{size},
        #{hashCode},
        #{delFlag},
        #{createBy},
        now(),
        #{modifyBy},
        now(),
        #{remark},
        #{group},
        #{fileLocation}
        )
    </insert>

    <update id="deleteSysFile">
        UPDATE sys_file
        SET
        del_flag = 1
        WHERE
        id = #{id}
    </update>

    <select id="getSysFileListByFaultTypeKey" resultType="cc.crrc.manage.pojo.SysFilePO">
        SELECT
        <include refid="SysFileColumnAlias"></include>
        FROM
        sys_file t,
        ekb_fault_type_file t1
        WHERE
         t.id = t1.file_id AND
         t1.fault_type_key = #{faultTypeKey}
    </select>

</mapper>