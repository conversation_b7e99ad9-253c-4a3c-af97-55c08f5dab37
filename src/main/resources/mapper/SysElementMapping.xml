<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.SysElementMapping">

    <!--重复校验-->
    <select id="selectElement" resultType="cc.crrc.manage.pojo.Element">
        SELECT element_code from sys_element
        <where>
            <if test="elementCode != null and elementCode != ''">
                element_code = #{elementCode}
            </if>
            and  del_flag = '0'
        </where>
    </select>

    <!--新增 资源-->
    <insert id="addSysElement">
          insert into sys_element
          (id,
          element_code,
          element_type,
          element_name,
          element_uri,
          menu_id,
          parent_id,
          path,
          method,
          remarks,
          create_by,
          create_date,
          update_by,
          update_date,
          tenant_id,
          del_flag
          )
          values
          (#{id},#{elementCode},#{elementType},#{elementName},#{elementUri},#{menuId},#{parentId},#{path},#{method},#{remarks},#{createBy},now(),#{updateBy},#{updateDate},#{tenantId},#{delFlag})
     </insert>

    <update id="deleteSysElementById">
        update
        sys_element
        <trim prefix="set" suffixOverrides=",">
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            update_date = now(),
            <if test="delFlag != null and delFlag != ''">
                del_flag = #{delFlag}
            </if>
        </trim>
        <where>
            id = #{id}
        </where>
    </update>


    <update id="updateSysElementById">
        update
        sys_element
        <trim prefix="set" suffixOverrides=",">

            <if test="elementCode != null and elementCode != ''">
                element_code = #{elementCode},
            </if>
            <if test="elementName != null and elementName != ''">
                element_name = #{elementName},
            </if>
            <if test="elementUri != null and elementUri != ''">
                element_uri = #{elementUri},
            </if>
            <if test="method != null and method != ''">
                method = #{method},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="remarks != null and remarks != ''">
                remarks = #{remarks},
            </if>
            update_date = now()
        </trim>
        <where>
            id = #{id}
        </where>

    </update>

    <select id="selectSysElement" resultType="cc.crrc.manage.pojo.Element">
        select
        id,
        element_code as elementCode,
        element_type as elementType,
        element_name as elementName,
        element_uri as elementUri,
        menu_id as menuId,
        parent_id as parentId,
        path,
        method,
        remarks,
        create_by as createBy,
        create_date as createDate,
        update_by as updateBy,
        update_date as updateDate,
        tenant_id as tenantId,
        del_flag as delFlag
        from sys_element
        <where>
            1=1
            <if test="elementCode != null and elementCode != ''">
                and element_code = #{elementCode}
            </if>
            <if test="elementName != null and elementName != ''">
                and element_name like '%'||#{elementName}||'%'
            </if>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="method != null and method != ''">
                and method = #{method}
            </if>
            <if test="menuId != null and menuId != ''">
                and menu_id = #{menuId}
            </if>
            and  del_flag = '0'
        </where>
    </select>


    <insert id="addElementByRoleId">
          insert into sys_element_role
          (id,
          element_id,
          role_id,
          create_by,
          create_date
          )
          values
          (#{id},#{elementId},#{roleId},#{createBy},now())
     </insert>

    <delete id="removeElementByRoleId">
        delete from
        sys_element_role
        <where>
            element_id = #{elementId} and
            role_id = #{roleId}
        </where>
    </delete>


    <select id="getAllElementByRoleId" resultType="cc.crrc.manage.pojo.Element">
        SELECT DISTINCT
            ser.id AS elementRoleId,
            se.method,
            se.id AS id,
            se.element_code AS elementCode,
            se.element_type AS elementType,
            se.element_name AS elementName,
            se.element_uri AS elementUri,
            m.title AS menuName
            FROM sys_element_role ser
			LEFT JOIN sys_element se ON ser.element_id = se.id
			INNER JOIN sys_menu m ON se.menu_id = m.id
			<where>
                role_id = #{roleId}
            </where>



    </select>
    <!--角色资源重复校验-->
    <select id="selectElementRole" resultType="cc.crrc.manage.pojo.Element">
        SELECT
        id AS elementId
        FROM sys_element_role ser
        <where>
        1=1
            <if test="elementId != null and elementId != ''">
                and element_id = #{elementId}
            </if>
            <if test="roleId != null and roleId != ''">
                and role_id = #{roleId}
            </if>
        </where>
    </select>
    
    
    
 
  <select id="selectSysElementByUser" resultType="cc.crrc.manage.pojo.Element">
        select 
        se.id as id,
        se.element_code as elementCode,
        se.element_type as elementType,
        se.element_name as elementName,
        se.element_uri as elementUri,
        se.menu_id as menuId,
        se.parent_id as parentId,
        se.path as path,
        se.method as method,
        se.remarks as remarks,
        se.create_by as createBy,
        se.create_date as createDate,
        se.update_by as updateBy,
        se.update_date as updateDate,
        se.tenant_id as tenantId,
        se.del_flag as delFlag
        from   sys_element se,sys_element_role ser
        <where>
              se.id=ser.element_id 
              and  ser.role_id in 
              <foreach collection="roleIdList" index="index" item="sysRole" open="(" separator="," close=")">
                #{sysRole.roleId}
              </foreach>
              and  se.menu_id = #{menuId} 
            and   se.del_flag = '0'
        </where>
         group by
          se.id
         
    </select>
    
    
    
    
     <select id="selectSyeElementByRoleId" resultType="java.lang.String">
        select
        se.id as id
        from   sys_element se,sys_element_role ser
        <where>
              se.id=ser.element_id 
              and  ser.role_id=#{roleId}
            and  se.del_flag = '0'
        </where>
    </select>
    
    
    
       <delete id="removeEleByRoleId">
        delete from
        sys_element_role
        <where>
            role_id = #{roleId}
        </where>
    </delete>
    
    
 
 
 
 
 
 
 
    
    
   
    
    
    

</mapper>