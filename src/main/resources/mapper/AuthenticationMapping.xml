<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.AuthenticationMapping">
    <select id="loadUserByUsername" resultType="cc.crrc.manage.security.UserDetail">
        select u.id,u.username,u.password,u.name, u.is_super_admin as
        isSuperAdmin from
        sys_user u where u.username = #{username} and u.del_flag='0'
    </select>

    <select id="loadUserAuthority" resultType="cc.crrc.manage.security.PathGrantedAuthority">
        SELECT
        se.id,
        se.element_uri path,
        se.method
        FROM
        sys_element_role ser,
        sys_element se
        WHERE
        se.del_flag='0'
        AND se.ID = ser.element_id
        AND ser.role_id = #{roleId}
    </select>

    <select id="loadSelfOrganization" resultType="Long">
        SELECT
        organ.ID
        FROM
        sys_organization organ,
        sys_organization_user organ_user
        WHERE
        organ_user.user_id = #{userId}
        AND organ.ID = organ_user.organization_id
        AND organ.del_flag = '0'
    </select>

    <select id="loadOrganization" resultType="Long">
        WITH RECURSIVE T ( ID, NAME, parent_id ) AS (
        SELECT ID
        ,
        NAME,
        parent_id
        FROM
        sys_organization
        WHERE
        ID IN (
        SELECT
        organ.ID
        FROM
        sys_organization organ,
        sys_organization_user organ_user
        WHERE
        organ_user.user_id = #{userId}
        AND organ.ID = organ_user.organization_id
        AND organ.del_flag = '0'
        )
        AND del_flag = '0' UNION ALL
        SELECT
        T1.ID,
        T1.NAME,
        T1.parent_id
        FROM
        sys_organization T1
        JOIN T ON T1.parent_id = T.ID
        AND T1.del_flag = '0'
        )
        SELECT ID
        FROM
        T
    </select>
</mapper>