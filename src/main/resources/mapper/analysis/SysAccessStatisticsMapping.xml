<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.analysis.SysAccessStatisticsMapping">

    <insert id="addAccessStatistics">
        insert into sys_access_statistics
        (id,
         menu_name,
         menu_code,
         parent_menu_code,
         user_id,
         date,
         name,
         host,
         username,
         user_agent)
        values (#{id},
                #{menuName},
                #{menuCode},
                #{parentMenuCode},
                #{userId},
                CURRENT_TIMESTAMP,
                #{name},
                #{host},
                #{username},
                #{userAgent})
    </insert>

    <select id="getVisitors" resultType="cc.crrc.manage.pojo.analysis.SysAccessStatisticsVO">
        SELECT
        user_agent as userAgent,
        host,
        name,
        date
        FROM
        sys_access_statistics
        WHERE
        1=1
        <if test="userId != null and userId != ''">
            and menu_code in (select t5.code from  sys_role_menu t3,sys_role_user t4,sys_menu t5
            where t5.id = t3.menu_id and t4.role_id = t3.role_id and t4.user_id = #{userId})
        </if>
        <if test="startTime != null and startTime != ''">
            and date &gt; #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and date &lt;= #{endTime}
        </if>
        ORDER BY date DESC
    </select>
    <select id="getAgent" resultType="cc.crrc.manage.pojo.analysis.SysAccessStatisticsVO">
        SELECT
        user_agent as userAgent,
        count(1) as count
        FROM
        sys_access_statistics
        WHERE
        1=1
        <if test="userId != null and userId != ''">
            and menu_code in (select t5.code from  sys_role_menu t3,sys_role_user t4,sys_menu t5
            where t5.id = t3.menu_id and t4.role_id = t3.role_id and t4.user_id = #{userId})
        </if>
        <if test="startTime != null and startTime != ''">
            and date &gt; #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and date &lt;= #{endTime}
        </if>
        GROUP BY
        user_agent
    </select>
    <select id="getCountByDate" resultType="java.util.Map">
        SELECT to_char(date, 'YYYY-MM-DD') as "dateTime" ,count(*)  FROM sys_access_statistics
        WHERE
        1=1
        <if test="userId != null and userId != ''">
            and menu_code in (select t5.code from sys_role_menu t3,sys_role_user t4,sys_menu t5
            where t5.id = t3.menu_id and t4.role_id = t3.role_id and t4.user_id = #{userId})
        </if>
        <if test="startTime != null and startTime != ''">
            and date &gt; #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and date &lt;= #{endTime}
        </if>
        GROUP BY "dateTime"
        ORDER BY "dateTime"

    </select>
    <select id="getParentMenuCount" resultType="java.util.Map">
        SELECT
        sort,
        title AS "menuName",
        "parentMenuCode" AS "menuCode",
        SUM ( num )
        from
        (
        SELECT
        t2.title,
        t2.sort,
        t1.date,
        t2.code as "parentMenuCode",
        CASE WHEN t1.menu_code IS NULL THEN 0 ELSE 1 END num
        FROM
        sys_access_statistics t1
        LEFT JOIN sys_menu t2 on t1.parent_menu_code =t2.code
        WHERE
        t2.enabled = false
        and
        t2.parent_id ='13'
        <if test="userId != null and userId != ''">
            and t1.menu_code in (select t5.code from  sys_role_menu t3,sys_role_user t4,sys_menu t5
            where t5.id = t3.menu_id and t4.role_id = t3.role_id and t4.user_id = #{userId})
        </if>
        ) temp1
        where 1=1
        <if test="startTime != null and startTime != ''">
            and date &gt; #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and date &lt;= #{endTime}
        </if>
        OR DATE IS NULL
        GROUP BY
        sort,
        "menuCode",
        "menuName"
        ORDER BY
        sort
    </select>
    <select id="getMenuCountByDate" resultType="java.util.Map">
        SELECT
        code AS "menuCode",
        title AS "menuName",
        SUM ( num ) ,
        sort
        FROM
        (
        SELECT
        t2.code,
        t2.title,
        t2.sort,
        CASE WHEN t1.menu_code IS NULL THEN 0 ELSE 1 END num,
        t1.date
        FROM
        sys_access_statistics  t1
        LEFT JOIN sys_menu t2 ON t2.code = t1.menu_code
        WHERE
        t2.enabled = false
        and
        t1.parent_menu_code = ( SELECT code FROM sys_menu WHERE code = #{parentMenuCode} )
        <if test="userId != null and userId != ''">
            and t1.menu_code in (select t5.code from  sys_role_menu t3,sys_role_user t4,sys_menu t5
            where t5.id = t3.menu_id and t4.role_id = t3.role_id and t4.user_id = #{userId})
        </if>
        ) temp1
        WHERE
            1=1
        <if test="startTime != null and startTime != ''">
            and date &gt; #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and date &lt;= #{endTime}
        </if>
        OR DATE IS NULL
        GROUP BY
        "menuCode",
        "menuName",
        sort
        ORDER BY
        sum desc,
        sort
    </select>
</mapper>