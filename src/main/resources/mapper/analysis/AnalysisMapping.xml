<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.analysis.AnalysisMapping">
	<!--车辆各项能耗统计-->
    <select id="getEnergy" resultType="cc.crrc.manage.pojo.analysis.AnalysisParamPO">
       SELECT signal_name_en AS signalNameEn,SUM(increament) AS increamentSum
       FROM dw_realtime_train_status_by_day
       WHERE train_id = #{vehicleCode}
       AND time:: BIGINT BETWEEN #{startTime} AND #{endTime}
       GROUP BY signal_name_en
   </select>

    <select id="getEnergyForExcel" resultType="cc.crrc.manage.pojo.excel.EnergyForExcelPO">
        SELECT type,value
        FROM (
        SELECT
        '列车运行里程(km)' AS type,
        case when SUM  (increament)  is null then 0 ELSE SUM  (increament)  end AS value
        FROM dw_realtime_train_status_by_day
        WHERE train_id = #{vehicleCode}
        AND signal_name_en = 'ECR_udRunDistance'
        AND time:: BIGINT BETWEEN #{startTime}
        AND #{endTime}
        UNION ALL
        SELECT
        '列车总能耗(kw‧h)' AS type,
        case when SUM  (increament)  is null then 0 ELSE SUM  (increament)  end AS value
        FROM dw_realtime_train_status_by_day
        WHERE train_id = #{vehicleCode}
        AND signal_name_en = 'ECR_udHistTTL'
        AND time:: BIGINT BETWEEN #{startTime}
        AND #{endTime}
        UNION ALL
        SELECT
        '牵引能耗(kw‧h)' AS type,
        case when SUM  (increament)  is null then 0 ELSE SUM  (increament)  end AS value
        FROM dw_realtime_train_status_by_day
        WHERE train_id = #{vehicleCode}
        AND signal_name_en = 'ECR_udHistMotor'
        AND time:: BIGINT BETWEEN #{startTime}
        AND #{endTime}
        UNION ALL
        SELECT
        '辅助能耗(kw‧h)' AS type,
        case when SUM  (increament)  is null then 0 ELSE SUM  (increament)  end AS value
        FROM dw_realtime_train_status_by_day
        WHERE train_id = #{vehicleCode}
        AND signal_name_en = 'ECR_udHistAUX'
        AND time:: BIGINT BETWEEN #{startTime}
        AND #{endTime}
        UNION ALL
        SELECT
        '再生能耗(kw‧h)' AS type,
        case when SUM  (increament)  is null then 0 ELSE SUM  (increament)  end AS value
        FROM dw_realtime_train_status_by_day
        WHERE
        train_id = #{vehicleCode}
        AND signal_name_en = 'ECR_udHistFDB'
        AND time:: BIGINT BETWEEN #{startTime}
        AND #{endTime}
        UNION ALL
        SELECT
            '充电机能耗(kw‧h)' AS type,
            case when SUM  (increament)  is null then 0 ELSE SUM  (increament)  end AS value
        FROM dw_realtime_train_status_by_day
        WHERE
            train_id = #{vehicleCode}
          AND signal_name_en = 'ECR_udHistBCH'
          AND time:: BIGINT BETWEEN #{startTime}
            AND #{endTime}
        ) t
        ORDER BY type
    </select>

    <select id="listVehicleorLine" resultType="cc.crrc.manage.pojo.mtr.MtrVehiclePO">
        select
        li.name as lineName,
        mv.vehicle_code as vehicleCode
        from mtr_vehicle mv left
        join mtr_line li on mv.metro_line_id = li.id
        where li.id = #{name}
        and mv.del_flag = 0
    </select>
    <select id="listVehicleor" resultType="cc.crrc.manage.pojo.mtr.MtrVehiclePO">
        select
        li.name as lineName,
        mv.vehicle_code as vehicleCode
        from mtr_vehicle mv left
        join mtr_line li on mv.metro_line_id = li.id
        where mv.id = #{vehicleId}
    </select>



	<!--车辆总能耗趋势-->
	<select id="totalEnergyTrend" resultType="java.util.HashMap">
       SELECT time,
              accumlation
       FROM dw_realtime_train_status_by_day
       WHERE train_id = #{vehicleCode}
         AND signal_name_en = #{ECR_UD_HIST_TTL}
         AND time:: BIGINT BETWEEN #{startTime}
           AND #{endTime}
       ORDER BY time
   </select>

    <select id="totalEnergyTrendForExcel" resultType="cc.crrc.manage.pojo.excel.TotalEnergyTrendForExcelPO">
        SELECT time,
        accumlation
        FROM dw_realtime_train_status_by_day
        WHERE train_id = #{vehicleCode}
        AND signal_name_en = 'ECR_udHistTTL'
        AND time:: BIGINT BETWEEN #{startTime}
        AND #{endTime}
        ORDER BY time
    </select>

	<!--车辆各能耗趋势-->
	<select id="traConTrend" resultType="cc.crrc.manage.pojo.analysis.AnalysisParamPO">
        SELECT sd.label,dw.time,dw.accumlation
        FROM dw_realtime_train_status_by_day dw
        JOIN sys_dict sd ON sd.code = dw.signal_name_en
        WHERE
            train_id = #{vehicleCode}
        AND time:: BIGINT BETWEEN #{startTime} AND #{endTime}
        AND sd.sort_number &lt; 6
        GROUP BY sd.label,time,accumlation,sd.sort_number
        ORDER BY sd.sort_number
   </select>

    <select id="traConTrendForExcel" resultType="cc.crrc.manage.pojo.excel.AnalysisParamForExcelPO">
        SELECT
        t1.TIME AS TIME,
        MAX ( CASE t1.signal_name_en WHEN 'ECR_udHistAUX' THEN t1.accumlation END ) AS "cudDisegy",
        MAX ( CASE t1.signal_name_en WHEN 'ECR_udHistMotor' THEN t1.accumlation END ) AS "cudProegy",
        MAX ( CASE t1.signal_name_en WHEN 'ECR_udHistFDB' THEN t1.accumlation END ) AS "cudRegegy",
        MAX ( CASE t1.signal_name_en WHEN 'ECR_udHistBCH' THEN t1.accumlation END ) AS "bchegy"
        FROM
        dw_realtime_train_status_by_day t1
        WHERE
        train_id = #{vehicleCode}
        AND t1.TIME :: BIGINT BETWEEN #{startTime} AND #{endTime}
        GROUP BY time
    </select>

	<!--线路各列车数据量统计-->
	<select id="collectionReportCount" resultType="cc.crrc.manage.pojo.analysis.CollectionReportByDayDTO">
		SELECT
		train_name AS vehicleCode,
		SUM(data_count) AS dataCount
		FROM collection_report_by_day
		where
		line_id = #{lineId}
		<if test="vehicleCode != null and vehicleCode != ''">
			AND  train_name=#{vehicleCode}
		</if>
		AND collection_day:: BIGINT BETWEEN #{startTime} AND #{endTime}
		GROUP BY train_name
		ORDER BY train_name
	</select>

	<!--线路各车辆运行总时间-->
	<select id="totalRunTime" resultType="java.util.HashMap">
        SELECT
            status.train_id AS trainId,
            SUM ( status.increament ) AS value
        FROM
            dw_realtime_train_status_by_day status
        LEFT JOIN mtr_vehicle vehicle ON status.train_id = vehicle.vehicle_code
        WHERE
            status.signal_name_en = #{TMS_UD_TOTAL_RUN_TIME}
          AND vehicle.metro_line_id = #{lineId}
        GROUP BY status.train_id
        ORDER BY status.train_id
    </select>

	<!--线路各车辆运行总里程-->
	<select id="totalRunDistance" resultType="java.util.HashMap">
        SELECT
            status.train_id AS trainId,
            SUM ( status.increament ) AS value
        FROM
            dw_realtime_train_status_by_day status
        LEFT JOIN mtr_vehicle vehicle ON status.train_id = vehicle.vehicle_code
        WHERE
            status.signal_name_en = #{ECR_UD_RUN_DISTANCE}
          AND vehicle.metro_line_id = #{lineId}
        GROUP BY status.train_id
        ORDER BY status.train_id
    </select>

	<!--线路各车辆的各个能耗统计-->
	<select id="totalEngyByVehicle" resultType="java.util.HashMap">
        SELECT
            train_id,
            SUM ( CASE status.signal_name_en WHEN 'ECR_udHistTTL' THEN status.increament ELSE 0 END ) "totalEngy",
            SUM ( CASE status.signal_name_en WHEN 'ECR_udHistMotor' THEN status.increament ELSE 0 END ) "proEngy",
            SUM ( CASE status.signal_name_en WHEN 'ECR_udHistAUX' THEN status.increament ELSE 0 END ) "aceEngy",
            SUM ( CASE status.signal_name_en WHEN 'ECR_udHistFDB' THEN status.increament ELSE 0 END ) "regEngy",
            SUM ( CASE status.signal_name_en WHEN 'ECR_udHistBCH' THEN status.increament ELSE 0 END ) "bchEngy"
        FROM
            dw_realtime_train_status_by_day status
                LEFT JOIN mtr_vehicle vehicle ON status.train_id = vehicle.vehicle_code
        WHERE
            vehicle.metro_line_id = #{lineId}
        GROUP BY train_id
        ORDER BY train_id
    </select>

	<!--线路的里程趋势-->
	<select id="lineMileageTrend" resultType="java.util.HashMap">
        SELECT
            status.time,
            SUM ( status.accumlation ) AS value
        FROM
            dw_realtime_train_status_by_day status
        LEFT JOIN mtr_vehicle vehicle ON status.train_id = vehicle.vehicle_code
        WHERE
            status.signal_name_en = #{ECR_UD_RUN_DISTANCE}
          AND vehicle.metro_line_id = #{lineId}

        GROUP BY status.time
        ORDER BY status.time
    </select>

	<!--车辆运行统计-根据线路获取所有信息-->
	<select id="getTotolEnergy" resultType="cc.crrc.manage.pojo.analysis.AnalysisParamPO">
        SELECT dw.signal_name_en AS signalNameEn,
        case when SUM(increament) is null then 0 ELSE SUM(increament) end AS increamentSum
        FROM  dw_realtime_train_status_by_day dw
        LEFT JOIN mtr_vehicle mv ON dw.train_id = mv.vehicle_code
        WHERE
            mv.metro_line_id = #{lineId}
        AND dw.time:: BIGINT BETWEEN #{startTime}
        AND #{endTime}
        GROUP BY dw.signal_name_en;
   </select>

    <select id="lineAllRunExcel" resultType="java.util.HashMap">
        SELECT type,value
        FROM (
        SELECT
        '总里程(km)' AS type,
        case when SUM  (increament)  is null then 0 ELSE SUM  (increament)  end AS value
        FROM dw_realtime_train_status_by_day status
        LEFT JOIN mtr_vehicle vehicle ON status.train_id = vehicle.vehicle_code
        WHERE
        vehicle.metro_line_id = #{lineId}
        AND status.signal_name_en = 'ECR_udRunDistance'
        AND status.time:: BIGINT BETWEEN #{startTime}
        AND #{endTime}
        UNION ALL
        SELECT
        '总能耗(kw‧h)' AS type,
        case when SUM  (increament)  is null then 0 ELSE SUM  (increament)  end
        AS value
        FROM dw_realtime_train_status_by_day status
        LEFT JOIN mtr_vehicle vehicle ON status.train_id = vehicle.vehicle_code
        WHERE
        vehicle.metro_line_id = #{lineId}
        AND signal_name_en = 'ECR_udHistTTL'
        AND  status.time:: BIGINT BETWEEN #{startTime}
        AND #{endTime}
        UNION ALL
        SELECT
        '牵引能耗(kw‧h)' AS type,
        case when SUM  (increament)  is null then 0 ELSE SUM  (increament)  end
        AS value
        FROM dw_realtime_train_status_by_day status
        LEFT JOIN mtr_vehicle vehicle ON status.train_id = vehicle.vehicle_code
        WHERE
        vehicle.metro_line_id = #{lineId}
        AND signal_name_en = 'ECR_udHistMotor'
        AND  status.time:: BIGINT BETWEEN #{startTime}
        AND #{endTime}
        UNION ALL
        SELECT
        '辅助能耗(kw‧h)' AS type,
        case when SUM  (increament)  is null then 0 ELSE SUM  (increament)  end
        AS value
        FROM dw_realtime_train_status_by_day status
        LEFT JOIN mtr_vehicle vehicle ON status.train_id = vehicle.vehicle_code
        WHERE
        vehicle.metro_line_id = #{lineId}
        AND signal_name_en = 'ECR_udHistAUX'
        AND  status.time:: BIGINT BETWEEN #{startTime}
        AND #{endTime}
        UNION ALL
        SELECT
        '再生能耗(kw‧h)' AS type,
        case when SUM  (increament)  is null then 0 ELSE SUM  (increament)  end
        AS value
        FROM dw_realtime_train_status_by_day status
        LEFT JOIN mtr_vehicle vehicle ON status.train_id = vehicle.vehicle_code
        WHERE
        vehicle.metro_line_id = #{lineId}
        AND status.signal_name_en = 'ECR_udHistFDB'
        AND time:: BIGINT BETWEEN #{startTime}
        AND #{endTime}
        UNION ALL
        SELECT
            '充电机能耗(kw‧h)' AS type,
            case when SUM  (increament)  is null then 0 ELSE SUM  (increament)  end
                             AS value
        FROM dw_realtime_train_status_by_day status
                 LEFT JOIN mtr_vehicle vehicle ON status.train_id = vehicle.vehicle_code
        WHERE
            vehicle.metro_line_id = #{lineId}
          AND status.signal_name_en = 'ECR_udHistBCH'
          AND time:: BIGINT BETWEEN #{startTime}
            AND #{endTime}
        ) t
        ORDER BY type
    </select>

	<!--车辆里程趋势-->
	<select id="mileageTrend" resultType="java.util.HashMap">
        SELECT time,accumlation
        FROM dw_realtime_train_status_by_day
        WHERE train_id = #{vehicleCode}
        AND signal_name_en = #{ECR_UD_RUN_DISTANCE}
        AND time:: BIGINT BETWEEN #{startTime}
        AND #{endTime}
        ORDER BY time
    </select>

    <select id="mileageTrendForExcel" resultType="cc.crrc.manage.pojo.excel.MileageTrendForExcelPO">
        SELECT time,
        accumlation
        FROM dw_realtime_train_status_by_day
        WHERE train_id = #{vehicleCode}
        AND signal_name_en = 'ECR_udRunDistance'
        AND time:: BIGINT BETWEEN #{startTime}
        AND #{endTime}
        ORDER BY time
    </select>



    <select id="getLineAll" resultType="java.util.HashMap">
     SELECT
            status.train_id AS trainId,
            SUM ( CASE status.signal_name_en WHEN 'ECR_udHistTTL' THEN status.increament ELSE 0 END ) "totalEngy",
            SUM ( CASE status.signal_name_en WHEN 'ECR_udHistMotor' THEN status.increament ELSE 0 END ) "proEngy",
            SUM ( CASE status.signal_name_en WHEN 'ECR_udHistAUX' THEN status.increament ELSE 0 END ) "aceEngy",
            SUM ( CASE status.signal_name_en WHEN 'ECR_udHistFDB' THEN status.increament ELSE 0 END ) "regEngy",
			SUM ( CASE status.signal_name_en WHEN 'ECR_udHistBCH' THEN status.increament ELSE 0 END ) "bchEngy",
			SUM ( CASE status.signal_name_en WHEN 'TMS_udTotalRunTime' THEN status.increament ELSE 0 END ) "totalTime",
			SUM ( CASE status.signal_name_en WHEN 'ECR_udRunDistance' THEN status.increament ELSE 0 END ) "totalDistance"
        FROM
            dw_realtime_train_status_by_day status
         LEFT JOIN mtr_vehicle vehicle ON status.train_id = vehicle.vehicle_code
        WHERE
            vehicle.metro_line_id = #{lineId}
		AND status.time:: BIGINT BETWEEN #{startTime} AND #{endTime}
        GROUP BY train_id
        ORDER BY train_id
    </select>

    <select id="LineAllForExcel" resultType="cc.crrc.manage.pojo.excel.LineAllExcelPO">
        SELECT
        status.train_id AS trainId,
        SUM ( CASE status.signal_name_en WHEN 'ECR_udHistTTL' THEN status.increament ELSE 0 END ) "totalEngy",
        SUM ( CASE status.signal_name_en WHEN 'ECR_udHistMotor' THEN status.increament ELSE 0 END ) "proEngy",
        SUM ( CASE status.signal_name_en WHEN 'ECR_udHistAUX' THEN status.increament ELSE 0 END ) "aceEngy",
        SUM ( CASE status.signal_name_en WHEN 'ECR_udHistFDB' THEN status.increament ELSE 0 END ) "regEngy",
        SUM ( CASE status.signal_name_en WHEN 'ECR_udHistBCH' THEN status.increament ELSE 0 END ) "bchEngy",
        SUM ( CASE status.signal_name_en WHEN 'TMS_udTotalRunTime' THEN status.increament ELSE 0 END ) "totalTime",
        SUM ( CASE status.signal_name_en WHEN 'ECR_udRunDistance' THEN status.increament ELSE 0 END ) "totalDistance"
        FROM
        dw_realtime_train_status_by_day status
        LEFT JOIN mtr_vehicle vehicle ON status.train_id = vehicle.vehicle_code
        WHERE
        vehicle.metro_line_id = #{lineId}
        AND status.time:: BIGINT BETWEEN #{startTime}
        AND #{endTime}
        GROUP BY train_id
        ORDER BY train_id
    </select>


    <select id="lineDistanceForExcel" resultType="cc.crrc.manage.pojo.excel.LineDistanceForExcel">
        SELECT
        status.train_id AS trainId,
        SUM ( CASE status.signal_name_en WHEN 'ECR_udHistTTL' THEN status.increament ELSE 0 END ) "totalEngy",
        SUM ( CASE status.signal_name_en WHEN 'ECR_udHistMotor' THEN status.increament ELSE 0 END ) "proEngy",
        SUM ( CASE status.signal_name_en WHEN 'ECR_udHistAUX' THEN status.increament ELSE 0 END ) "aceEngy",
        SUM ( CASE status.signal_name_en WHEN 'ECR_udHistFDB' THEN status.increament ELSE 0 END ) "regEngy",
        SUM ( CASE status.signal_name_en WHEN 'ECR_udHistBCH' THEN status.increament ELSE 0 END ) "bchEngy",
        SUM ( CASE status.signal_name_en WHEN 'TMS_udTotalRunTime' THEN status.increament ELSE 0 END ) "totalTime",
        SUM ( CASE status.signal_name_en WHEN 'ECR_udRunDistance' THEN status.increament ELSE 0 END ) "totalDistance"
        FROM
        dw_realtime_train_status_by_day status
        LEFT JOIN mtr_vehicle vehicle ON status.train_id = vehicle.vehicle_code
        WHERE
        vehicle.metro_line_id = #{lineId}
        AND status.time:: BIGINT BETWEEN #{startTime}
        AND #{endTime}
        GROUP BY train_id
        ORDER BY train_id
    </select>


	<!--车辆运行统计-根据线路获取所有信息(饼图)-->
	<select id="getTotalEnergyPic" resultType="java.util.HashMap">
       SELECT dw.signal_name_en AS name,
              case when SUM  (increament)  is null then 0 ELSE SUM  (increament) end AS value
         FROM dw_realtime_train_status_by_day dw
             LEFT JOIN mtr_vehicle mv ON dw.train_id = mv.vehicle_code
            WHERE mv.metro_line_id = #{lineId}
                AND dw.signal_name_en = 'ECR_udHistTTL'
                AND time:: BIGINT BETWEEN #{startTime}
                AND #{endTime}
       GROUP BY dw.signal_name_en

   </select>

    <select id="totalEnergyPic" resultType="cc.crrc.manage.pojo.excel.EnergyPicForExcelPO">
        SELECT name ,value
        FROM (
        SELECT
        '再生能耗' AS name ,
        case when SUM  (increament)  is null then 0 ELSE SUM  (increament)  end
        AS value
        FROM dw_realtime_train_status_by_day status
        LEFT JOIN mtr_vehicle vehicle ON status.train_id = vehicle.vehicle_code
        WHERE
        vehicle.metro_line_id = #{lineId}
        AND status.signal_name_en = 'ECR_udHistFDB'
        AND time:: BIGINT BETWEEN #{startTime}
        AND #{endTime}
        UNION ALL
        SELECT
        '辅助能耗' AS name ,
        case when SUM  (increament)  is null then 0 ELSE SUM  (increament)  end
        AS value
        FROM dw_realtime_train_status_by_day status
        LEFT JOIN mtr_vehicle vehicle ON status.train_id = vehicle.vehicle_code
        WHERE
        vehicle.metro_line_id = #{lineId}
        AND signal_name_en = 'ECR_udHistAUX'
        AND  status.time:: BIGINT BETWEEN #{startTime}
        AND #{endTime}
        UNION ALL
        SELECT
        '牵引能耗' AS name ,
        case when SUM  (increament)  is null then 0 ELSE SUM  (increament)  end
        AS value
        FROM dw_realtime_train_status_by_day status
        LEFT JOIN mtr_vehicle vehicle ON status.train_id = vehicle.vehicle_code
        WHERE
        vehicle.metro_line_id = #{lineId}
        AND signal_name_en = 'ECR_udHistMotor'
        AND  status.time:: BIGINT BETWEEN #{startTime}
        AND #{endTime}
        UNION ALL
        SELECT
            '充电机能耗' AS name ,
            case when SUM  (increament)  is null then 0 ELSE SUM  (increament)  end
                       AS value
        FROM dw_realtime_train_status_by_day status
                 LEFT JOIN mtr_vehicle vehicle ON status.train_id = vehicle.vehicle_code
        WHERE
            vehicle.metro_line_id = #{lineId}
          AND signal_name_en = 'ECR_udHistBCH'
          AND  status.time:: BIGINT BETWEEN #{startTime}
            AND #{endTime}
        ) t
    </select>


	<!--车辆故障统计——根据车厢查询-->
	<select id="getFaultByLocation" resultType="java.util.HashMap">
        SELECT
            eft.LOCATION,
            COUNT ( * ) AS COUNT
        FROM
            mtc_alarm_warning maw,
            ekb_fault_type eft
        WHERE
            maw.fault_type_key = eft.fault_type_key
          AND maw.vehicle_id = #{vehicleId}
          AND maw.start_time >= #{startTime}
          AND maw.start_time <![CDATA[<=]]> #{endTime}
        GROUP BY
            eft.LOCATION
        ORDER BY
            eft.LOCATION
    </select>

    <select id="FaultByLocation" resultType="cc.crrc.manage.pojo.excel.FaultByLocationForExcelPO">
        SELECT
        eft.LOCATION,
        COUNT ( * ) AS COUNT
        FROM
        mtc_alarm_warning maw,
        ekb_fault_type eft
        WHERE
        maw.fault_type_key = eft.fault_type_key
        AND maw.vehicle_id = #{vehicleId}
        AND maw.start_time >= #{startTime}
        AND maw.start_time <![CDATA[<=]]> #{endTime}
        GROUP BY
        eft.LOCATION
        ORDER BY
        eft.LOCATION
    </select>

	<!--车辆故障统计——根据子系统查询-->
	<select id="getFaultBySubsystem" resultType="java.util.HashMap">
    SELECT
    eft.subsystem,
    sd.label AS subName,
    COUNT ( * ) AS COUNT
    FROM
    mtc_alarm_warning maw
    LEFT JOIN ekb_fault_type eft ON maw.fault_type_key = eft.fault_type_key
    LEFT JOIN sys_dict sd ON eft.subsystem = sd.code AND eft.vehicle_type_id = sd.vehicle_type_id AND eft.line_id = sd.line_id
    WHERE
    sd.type_code = 'ass_car_system'
    AND sd.del_flag = '0'
    AND maw.vehicle_id = #{vehicleId}
    AND maw.start_time >= #{startTime}
    AND maw.start_time <![CDATA[<=]]> #{endTime}
    GROUP BY
    eft.subsystem,subName
    </select>

	<!--车辆故障统计——根据故障等级查询-->
	<select id="getFaultByLevel" resultType="java.util.HashMap">
    SELECT
    eft.fault_level AS faultLevel,
    COUNT ( * ) AS COUNT
    FROM
    mtc_alarm_warning maw
    LEFT JOIN ekb_fault_type eft ON maw.fault_type_key = eft.fault_type_key
    WHERE
    maw.vehicle_id = #{vehicleId}
    AND maw.start_time >= #{startTime}
    AND maw.start_time <![CDATA[<=]]> #{endTime}
    GROUP BY
    eft.fault_level
    ORDER BY 	eft.fault_level ASC
    </select>

	<!--车辆故障统计——根据日期查询-->
	<select id="getFaultByDate" resultType="java.util.HashMap">
    SELECT
    to_char(maw.start_time,'YYYY/MM/DD')AS time,
    COUNT ( * ) AS COUNT
    FROM
    mtc_alarm_warning maw
    LEFT JOIN ekb_fault_type eft ON maw.fault_type_key = eft.fault_type_key
    WHERE
    maw.vehicle_id = #{vehicleId}
    AND maw.start_time >= #{startTime}
    AND maw.start_time <![CDATA[<=]]> #{endTime}
    GROUP BY
    time
    ORDER BY
    time ASC
</select>


    <select id="FaultByDate" resultType="cc.crrc.manage.pojo.excel.FaultByDateForExcelPO">
        SELECT
        to_char(maw.start_time,'YYYY/MM/DD')AS time,
        COUNT ( * ) AS COUNT
        FROM
        mtc_alarm_warning maw
        LEFT JOIN ekb_fault_type eft ON maw.fault_type_key = eft.fault_type_key
        WHERE
        maw.vehicle_id = #{vehicleId}
        AND maw.start_time >= #{startTime}
        AND maw.start_time <![CDATA[<=]]> #{endTime}
        GROUP BY
        time
        ORDER BY
        time ASC
    </select>

	<!--车辆故障统计——根据子系统和故障等级查询-->
	<select id="getSystemFaultByLevel"  resultType="java.util.HashMap">
	SELECT
    eft.subsystem,
    sd.label AS subName,
	eft.fault_level as faultLevel,
    COUNT ( * ) AS COUNT
    FROM
    mtc_alarm_warning maw
    LEFT JOIN ekb_fault_type eft ON maw.fault_type_key = eft.fault_type_key
    LEFT JOIN sys_dict sd ON eft.subsystem = sd.code AND eft.vehicle_type_id = sd.vehicle_type_id AND eft.line_id = sd.line_id
    WHERE
    sd.type_code = 'ass_car_system'
    AND sd.del_flag = '0'
    AND eft.del_flag = 0
    AND maw.vehicle_id = #{vehicleId}
    AND maw.start_time >= #{startTime}
    AND maw.start_time <![CDATA[<=]]> #{endTime}
    GROUP BY
    eft.subsystem,subName,faultLevel
	ORDER BY eft.subsystem  ASC
    </select>

	<select id="collectionReportVehicleCount" resultType="java.util.HashMap">
        SELECT
        collection_day AS collectionDay,
        data_count AS dataCount
        FROM collection_report_by_day
        where
        train_name=#{vehicleCode}
        AND collection_day:: BIGINT BETWEEN #{startTime} AND #{endTime}
        order BY collectionDay ASC
    </select>

    <select id="totleMileageTrendExcel" resultType="java.util.HashMap">
	   SELECT
		    LEFT(TIME,6) AS time,
		    case when SUM  (increament)  is null then 0 ELSE SUM  (increament)  end AS accumlation
		FROM
			dw_realtime_train_status_by_day
		WHERE
			train_id = #{vehicleCode}
		AND signal_name_en = 'ECR_udRunDistance'
		AND time:: BIGINT BETWEEN #{startTime}
        AND #{endTime}
		GROUP BY LEFT(TIME,6)
		ORDER BY
			TIME ASC
   </select>
    <select id="totleEnergyTrendExcel" resultType="java.util.HashMap">
	   SELECT
		    LEFT(TIME,6) AS time,
		    case when SUM  (increament)  is null then 0 ELSE SUM  (increament)  end AS accumlation
		FROM
			dw_realtime_train_status_by_day
		WHERE
			train_id = #{vehicleCode}
		AND signal_name_en = 'ECR_udHistTTL'
		AND time:: BIGINT BETWEEN #{startTime}
        AND #{endTime}
		GROUP BY LEFT(TIME,6)
		ORDER BY
			TIME ASC
   </select>

    <select id="getTotalDistance" resultType="integer">
        SELECT
            accumlation
        FROM
            dw_realtime_train_status_by_day
        WHERE
            train_id =  #{vehicleCode}
          AND signal_name_en =  #{paramName}
        ORDER BY
            time desc
            LIMIT 1
    </select>
    <select id="getTotalDistanceAndTotleEnergy" resultType="java.util.HashMap">
        SELECT
            drts.train_id AS "vehicleCode",
            MAX ( CASE drts.signal_name_en WHEN 'ECR_udHistTTL' THEN drts.accumlation END ) AS "energy",
            MAX ( CASE drts.signal_name_en WHEN 'ECR_udRunDistance' THEN drts.accumlation END ) AS "distances"
        FROM
            dw_realtime_train_status_by_day drts
        JOIN mtr_vehicle mv ON drts.train_id = mv.vehicle_code
        WHERE
            1 = 1
            <if test="lineId != null and lineId != ''">
                AND mv.metro_line_id = #{lineId}
            </if>
            <if test="vehicleCode != null and vehicleCode != ''">
                AND mv.vehicle_code = #{vehicleCode}
            </if>
        GROUP BY
            train_id
        ORDER BY
            train_id
    </select>

    <select id="getDistancesAndEnergyByV" resultType="java.util.HashMap">
        SELECT
            mv.vehicle_code AS vehicleCode,
            MAX (drts.increament) AS value
        FROM
            dw_realtime_train_status_by_day drts
        JOIN mtr_vehicle mv ON drts.train_id = mv.id
        WHERE
            drts.signal_name_en = #{signalNameEn}
        GROUP BY mv.vehicle_code
    </select>


    <select id="getVehicleHistoryData" resultType="java.util.HashMap">
        SELECT
            signal_name_en AS signalName,
            accumlation AS value
        FROM dw_realtime_train_status_by_day
        WHERE
            train_id = #{traCode}
        AND time = (SELECT MAX(time) FROM dw_realtime_train_status_by_day where #{now} > time AND train_id = #{traCode});
    </select>


</mapper>