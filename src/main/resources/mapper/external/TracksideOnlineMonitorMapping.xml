<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.external.TracksideOnlineMonitorMapping">

    <insert id="batchInsertCheckoutData">
        INSERT INTO check_out_data (id, train_pass_info_id, point_type_code, part_code, device_type_code,
        data_value, alarm_id, alarm_level, status, part_type)
        VALUES
        <foreach collection="dataList" item="item" separator=",">
            (#{item.id},
            #{item.trainPassInfoId},
            #{item.pointTypeCode},
            #{item.partCode},
            #{item.deviceTypeCode},
            #{item.dataValue},
            #{item.alarmId},
            #{item.alarmLevel},
            #{item.status},
            #{item.partType})
        </foreach>
    </insert>

    <insert id="insertTrainLogInfo" parameterType="cc.crrc.manage.pojo.external.TrainLogInfo">
        INSERT INTO train_pass_info (id, vehicle_code, trace_time, trace_file, station_code, direction)
        VALUES (#{id}, #{vehicleCode}, #{traceTime}, #{traceFile}, #{stationCode}, #{direction})
    </insert>

    <select id="listTrainLogInfo" resultType="cc.crrc.manage.pojo.external.TrainLogInfo">
        SELECT DISTINCT
        t1.id,
        t1.vehicle_code AS vehicleCode,
        t1.trace_time AS traceTime,
        t1.trace_file AS traceFile,
        t1.station_code AS stationCode,
        t1.direction
        FROM train_pass_info t1
        INNER JOIN check_out_data t2 ON t2.train_pass_info_id = t1.id
        WHERE
        t2.alarm_id IS NULL
        <if test="vehicleCode != null and vehicleCode != ''">
            AND t1.vehicle_code = #{vehicleCode}
        </if>
        <if test="stationCode != null">
            AND t1.station_code = #{stationCode}
        </if>
        <if test="traceTimeStart != null">
            AND t1.trace_time >= #{traceTimeStart}
        </if>
        <if test="traceTimeEnd != null">
            AND t1.trace_time <![CDATA[<=]]> #{traceTimeEnd}
        </if>
        ORDER BY t1.trace_time DESC
    </select>

    <select id="listCheckoutDataWheel" resultType="cc.crrc.manage.pojo.external.CheckoutData">
        SELECT t1.id,
        t1.point_type_code AS pointTypeCode,
        t1.part_code AS partCode,
        t1.device_type_code AS deviceTypeCode,
        t1.data_value AS dataValue
        FROM check_out_data t1
        LEFT JOIN monitor_name_code_mapping t2 ON t1.point_type_code = t2.code AND t2.type = 'point_type_code_wheel'
        LEFT JOIN monitor_name_code_mapping t3 ON t1.part_code = t3.code AND t3.type = 'part_code_wheel'
        WHERE t1.train_pass_info_id = #{id}
        AND t1.device_type_code = 302
        AND t1.point_type_code NOT IN (6,9,10,11)
        ORDER BY t3.sort, t2.sort
    </select>

    <select id="listCheckoutDataAxle" resultType="cc.crrc.manage.pojo.external.CheckoutData">
        SELECT t1.id,
        t1.point_type_code AS pointTypeCode,
        t1.part_code AS partCode,
        t1.device_type_code AS deviceTypeCode,
        t1.data_value AS dataValue
        FROM check_out_data t1
        LEFT JOIN monitor_name_code_mapping t2 ON t1.point_type_code = t2.code AND t2.type = 'point_type_code_wheel'
        LEFT JOIN monitor_name_code_mapping t3 ON t1.part_code = t3.code AND t3.type = 'part_code_axle'
        LEFT JOIN monitor_name_code_mapping t4 ON t1.part_code = t4.code AND t4.type = 'part_code_bogie'
        LEFT JOIN monitor_name_code_mapping t5 ON t1.part_code = t5.code AND t5.type = 'part_code_carriage'
        WHERE t1.train_pass_info_id = #{id}
        AND t1.device_type_code = 302
        AND t1.point_type_code not in (1,2,3,7,8,61)
        ORDER BY t2.sort
    </select>

    <select id="wheelCheckPointDict" resultType="cc.crrc.manage.pojo.SysDictDTO">
        SELECT code,
        display_name AS label
        FROM monitor_name_code_mapping
        WHERE type = 'point_type_code_wheel'
        ORDER BY code
    </select>

    <select id="panCheckPointDict" resultType="cc.crrc.manage.pojo.SysDictDTO">
        SELECT code,
        display_name AS label
        FROM monitor_name_code_mapping
        WHERE type = 'point_type_code_pan'
        ORDER BY code
    </select>

    <select id="listCheckoutDataPan" resultType="cc.crrc.manage.pojo.external.CheckoutData">
        SELECT t1.id,
        t1.point_type_code AS pointTypeCode,
        t1.part_code AS partCode,
        t1.device_type_code AS deviceTypeCode,
        t1.data_value AS dataValue
        FROM check_out_data t1
        LEFT JOIN monitor_name_code_mapping t2 ON t1.point_type_code = t2.code AND t2.type = 'point_type_code_pan'
        LEFT JOIN monitor_name_code_mapping t3 ON t1.part_code = t3.code AND t3.type = 'part_code_pan'
        WHERE t1.train_pass_info_id = #{id}
        AND t1.device_type_code = 101
        ORDER BY t3.sort, t2.sort
    </select>

    <select id="wheelPartDict" resultType="cc.crrc.manage.pojo.SysDictDTO">
        SELECT code,
        display_name AS label
        FROM monitor_name_code_mapping
        WHERE type IN ('part_code_wheel', 'part_code_axle', 'part_code_bogie', 'part_code_carriage')
        ORDER BY type, sort
    </select>

    <select id="listAlarmInfo" resultType="cc.crrc.manage.pojo.external.CheckoutAlarmDataVO"
            parameterType="cc.crrc.manage.pojo.external.CheckoutAlarmDataDTO">
        SELECT
        t1.id,
        t2.vehicle_code AS vehicleCode,
        t2.trace_time AS alarmTime,
        t2.trace_file AS picture,
        t2.station_code AS stationCode,
        t1.status,
        t3.label AS statusCn,
        t1.point_type_code AS pointTypeCode,
        t1.part_code AS partCode,
        t1.data_value AS dataValue,
        t4.display_name AS pointTypeCn,
        t5.display_name AS partCn,
        t1.alarm_level AS alarmLevel
        FROM check_out_data t1
        LEFT JOIN train_pass_info t2 ON t1.train_pass_info_id = t2.id
        LEFT JOIN sys_dict t3 ON t3.code = t1.status::varchar AND t3.type_code = 'alarm_status'
        LEFT JOIN monitor_name_code_mapping t4 ON t4.code = t1.point_type_code
        LEFT JOIN monitor_name_code_mapping t5 ON t5.code = t1.part_code AND t5.type = t1.part_type
        WHERE
        t1.alarm_id IS NOT NULL
        AND t3.del_flag = '0'
        AND t1.device_type_code = #{monirorProject}
        <if test="monirorProject != null and monirorProject == 302">
            AND t4.type = 'point_type_code_wheel'
        </if>
        <if test="monirorProject != null and monirorProject == 101">
            AND t4.type = 'point_type_code_pan'
        </if>
        <if test="vehicleCode != null and vehicleCode != ''">
            AND t2.vehicle_code = #{vehicleCode}
        </if>
        <if test="stationCode != null">
            AND t2.station_code = #{stationCode}
        </if>
        <if test="status != null">
            AND t1.status = #{status}
        </if>
        <if test="pointTypeCode != null">
            AND t1.point_type_code = #{pointTypeCode}
        </if>
        <if test="partCode != null">
            AND t5.display_name = #{partCode}
        </if>
        <if test="traceTimeStart != null">
            AND t2.trace_time >= #{traceTimeStart}
        </if>
        <if test="traceTimeEnd != null">
            AND t2.trace_time <![CDATA[<=]]> #{traceTimeEnd}
        </if>
        ORDER BY t2.trace_time DESC
    </select>

    <select id="panPartDict" resultType="cc.crrc.manage.pojo.SysDictDTO">
        SELECT code,
        display_name AS label
        FROM monitor_name_code_mapping
        WHERE type = 'part_code_pan'
        ORDER BY code
    </select>

    <select id="getComponentsByLocation" resultType="java.lang.String">
        SELECT code FROM
        monitor_name_code_mapping
        WHERE substring(display_name FROM 1 FOR 3) = #{location}
        AND type = 'part_code_wheel' order by sort
    </select>

    <select id="getComponents" resultType="java.lang.String">
        SELECT code FROM
        monitor_name_code_mapping
        WHERE type = 'part_code_wheel' order by sort
    </select>

    <select id="getPantograph" resultType="java.lang.String">
        SELECT code FROM
        monitor_name_code_mapping
        WHERE type = 'part_code_pan'
    </select>

    <select id="getComponentsByLocationSecond" resultType="java.lang.String">
        SELECT code FROM
        monitor_name_code_mapping
        WHERE display_name  = #{location}
        AND type = 'part_code_wheel' order by sort
    </select>

    <select id="locationDict" resultType="cc.crrc.manage.pojo.SysDictDTO">
        SELECT code,
        display_name AS label
        FROM monitor_name_code_mapping
        WHERE type = 'part_code_carriage'
        ORDER BY code 
    </select>

    <select id="getPantographSecond" resultType="java.lang.String">
        SELECT code FROM
        monitor_name_code_mapping
        WHERE type = 'part_code_pan' and display_name  = #{partCn}
    </select>
</mapper>