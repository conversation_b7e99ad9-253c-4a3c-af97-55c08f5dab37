<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.ekb.EkbFaultReasonMapping">

    <sql id="EkbFaultReasonColumnAlias">
        t.id,
        t.vehicle_type_id as vehicleTypeId,
        t.reason_key as reasonKey,
        t.reason_code as reasonCode,
        t.content,
        t.category,
        t.create_by as createBy,
        t.create_time as createTime,
        t.modify_by as modifyBy,
        t.modify_time as modifyTime
    </sql>

    <insert id="addEkbFaultReason">
        INSERT INTO
        ekb_fault_reason (
            id,
            vehicle_type_id,
            reason_key,
            reason_code,
            content,
            category,
            create_by,
            create_time,
            modify_time,
            modify_by
        )
        VALUES (
            #{id},
            #{vehicleTypeId},
            #{reasonKey},
            #{reasonCode},
            #{content},
            #{category},
            #{createBy},
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP,
            #{modifyBy}
        )
    </insert>

    <delete id="deleteEkbFaultReason">
        DELETE FROM ekb_fault_reason WHERE id = #{id}
    </delete>

    <update id="updateEkbFaultReason">
        UPDATE ekb_fault_reason
        <trim prefix="set" suffixOverrides=",">
            <if test="vehicleTypeId != null and vehicleTypeId != ''">
                vehicle_type_id = #{vehicleTypeId},
            </if>
            <if test="reasonCode != null and reasonCode != ''">
                reason_code = #{reasonCode},
            </if>
            <if test="content != null and content != ''">
                content = #{content},
            </if>
            <if test="category != null and category != ''">
                category = #{category},
            </if>
            <if test="modifyBy != null and modifyBy != ''">
                modify_by = #{modifyBy},
            </if>
            modify_time = CURRENT_TIMESTAMP
        </trim>
        WHERE
        id = #{id}
    </update>

    <select id="listEkbFaultReason" resultType="cc.crrc.manage.pojo.ekb.EkbFaultReasonDTO">
        SELECT
            DISTINCT
            <include refid="EkbFaultReasonColumnAlias"></include>,
            mvt.name as vehicleType
        FROM
            ekb_fault_reason t
        LEFT JOIN
            mtr_vehicle_type mvt
        ON
            t.vehicle_type_id = mvt.id
--         2020/4/17 guowei fracas 添加故障名称筛选
        left join ekb_fault_type_reason eftr on eftr.fault_reason_key = t.reason_key
        left join ekb_fault_type eft on eft.fault_type_key = eftr.fault_type_key

        <where>
            <if test="vehicleTypeId != null and vehicleTypeId !=''">
                AND t.vehicle_type_id = #{vehicleTypeId}
            </if>
            <if test="reasonKey != null and reasonKey != ''">
                AND t.reason_key LIKE '%'||#{reasonKey}||'%'
            </if>
            <if test="reasonCode != null and reasonCode != ''">
                AND t.reason_code LIKE '%'||#{reasonCode}||'%'
            </if>
            <if test="content != null and content != ''">
                AND t.content LIKE '%'||#{content}||'%'
            </if>
            <if test="category != null and category != ''">
                AND t.category LIKE '%'||#{category}||'%'
            </if>
            <!--  2020/4/17 guowei fracas 添加故障名称筛选-->
            <if test="nameCn != null and nameCn != ''">
                AND eft.name_cn  LIKE   '%'||#{nameCn}||'%'
            </if>
        </where>
        ORDER BY
            t.create_time DESC
    </select>


    <select id="getEkbFaultReasonById" resultType="cc.crrc.manage.pojo.ekb.EkbFaultReasonDTO">
        SELECT
            <include refid="EkbFaultReasonColumnAlias"></include>
        FROM
            ekb_fault_reason t
        WHERE
            t.id = #{id}
    </select>

    <!--更新原因措施关系的real_counter-->
    <update id="updateTypeReasonRealCounter">
        UPDATE
            ekb_fault_type_reason
        SET
            real_counter = #{realCounter}
        WHERE
            fault_type_key = #{faultTypeKey} AND
            fault_reason_key = #{faultReasonKey}
    </update>
    <!--按照故障原因编码、故障原因类别、故障原因内容同时模糊查询-->
    <select id="listEkbFaultReasonVaguely" resultType="cc.crrc.manage.pojo.ekb.EkbFaultReasonDTO">
        SELECT
            DISTINCT
            <include refid="EkbFaultReasonColumnAlias"></include>,
            mvt.name as vehicleType
        FROM
            ekb_fault_reason t
        LEFT JOIN
            mtr_vehicle_type mvt
        ON
            t.vehicle_type_id = mvt.id
        <where>
        	<if test="id != null and id !=''">
	        t.reason_key NOT IN(
				SELECT eftr.fault_reason_key FROM ekb_fault_type_reason eftr
				JOIN ekb_fault_type eft ON eft.fault_type_key = eftr.fault_type_key
				WHERE eft.id = #{id} AND eftr.fault_reason_key IS NOT NULL)
        	</if>
            <if test="keyword != null and keyword != ''">
                AND (t.content LIKE '%'||#{keyword}||'%' OR
                t.reason_code LIKE '%'||#{keyword}||'%' OR
                t.category LIKE '%'||#{keyword}||'%')
            </if>
        </where>
        ORDER BY t.create_time DESC, t.modify_time DESC
    </select>

    <!--2019/12/25 提供新加披业主展示用 此方法为暂时使用-->
    <select id="getEkbFaultReason" resultType="cc.crrc.manage.pojo.ekb.EkbFaultReasonDTO">
        SELECT
        <include refid="EkbFaultReasonColumnAlias"></include>
        FROM
        ekb_fault_reason t
        LEFT JOIN ekb_fault_type_reason t2 ON t.reason_key = t2.fault_reason_key
        where
        t2.fault_type_key = #{faultTypeKey}
        LIMIT 1
    </select>
    <select id="getFaultReasonByContent" resultType="cc.crrc.manage.pojo.ekb.EkbFaultReasonDTO">
        SELECT
        <include refid="EkbFaultReasonColumnAlias"></include>
        FROM
        ekb_fault_reason t
        WHERE
        t.content = #{content}
    </select>

    <insert id="ddFaultReasonList">
        insert into ekb_fault_reason
        (
        id,
        vehicle_type_id,
        reason_key,
        reason_code,
        content,
        category
        )
        values
        <foreach collection="list" item="item" index= "index" separator =",">
            (
            #{item.id}
            #{item.vehicleTypeId},
            #{item.reasonKey},
            #{item.reasonCode},
            #{item.content},
            #{item.category}
            )
        </foreach>
    </insert>

    <select id="getEkbFaultReasonForExcel"
            resultType="cc.crrc.manage.pojo.excel.EkbFaultReasonForExcelPO">
        SELECT
        <include refid="EkbFaultReasonColumnAlias"/>
        FROM ekb_fault_reason t
        order by t.id
        desc
        <if test="countNum != null and countNum != '' and countNum == 1">
            limit 1
        </if>
    </select>

    <select id="getFaultTypeReasonKeyList" resultType="java.lang.String">
        select reason_key from ekb_fault_reason
    </select>

    <select id="getFaultTypeReasonKeyListByfaultTypeKey" resultType="java.lang.String">
         select fault_reason_key from ekb_fault_type_reason  where fault_type_key = #{faultTypeKey}
    </select>

    <select id="getReasonKeyAndReasonCodeList" resultType="cc.crrc.manage.pojo.excel.EkbFaultReasonForExcelPO">
         select
         reason_key as reasonKey,
         reason_code as reasonCode
         from ekb_fault_reason
    </select>
</mapper>