<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.ekb.EkbFaultTypeFileMapping">
    <sql id="SysFileAlias">
        t.id,
        t.name,
        t.url,
        t.type,
        t.format,
        t.size,
        t.hash_code as hashCode,
        t.del_flag as delFlag,
        t.create_by as createBy,
        t.create_time as createTime,
        t.modify_by as modifyBy,
        t.modify_time as modifyTime,
        t.remark,
        t.group,
        t.file_location as fileLocation
    </sql>
    <insert id="saveFaultTypeFile">
        INSERT INTO
        ekb_fault_type_file
        (
        fault_type_key,
        file_id,
        start_date
        )
        VALUES
        (
        #{faultTypeKey},
        #{fileId},
        CURRENT_TIMESTAMP
        )

    </insert>
    <delete id="deleteFaultTypeFile">
        UPDATE
        ekb_fault_type_file
        SET
        del_flag = 1,
        end_date = CURRENT_TIMESTAMP
        WHERE
        fault_type_key = #{faultTypeKey}
        AND
        file_id = #{fileId}
    </delete>

    <select id="getEkbFaultTypeFileList" resultType="cc.crrc.manage.pojo.SysFilePO">
        SELECT
        <include refid="SysFileAlias"></include>
        FROM sys_file t,ekb_fault_type_file t2,ekb_fault_type t3
        WHERE
        t.id = t2.file_id AND
        t2.fault_type_key = t3.fault_type_key AND
        t3.fault_type_key = #{faultTypeKey} AND
        t2.del_flag = 0
    </select>

</mapper>