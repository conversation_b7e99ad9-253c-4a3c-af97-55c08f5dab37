<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.ekb.EkbFaultTypeReasonMapping">

    <insert id="addFaultTypeReasonRelation">
        INSERT INTO ekb_fault_type_reason
        (
        fault_reason_key,
        fault_type_key,
        init_counter,
        real_counter
        )
        values
        (
        #{faultReasonKey},
        #{faultTypeKey},
        #{initCounter},
        #{realCounter}
        )
    </insert>
    <insert id="batchInsert">
        insert into ekb_fault_type_reason
        (
        fault_type_key,
        fault_reason_key,
        init_counter,
        real_counter
        )
        values
        <foreach collection="list" item="item" index= "index" separator =",">
            (
            #{item.faultTypeKey},
            #{item.faultReasonKey},
            0,
            0
            )
        </foreach>
    </insert>

    <!--取得故障类型原因关系-->
    <select id="listFaultTypeReason" resultType="cc.crrc.manage.pojo.ekb.EkbFaultTypeReasonVO">
        SELECT
            fault_reason_key AS faultReasonKey,
            fault_type_key AS faultTypeKey,
            init_counter AS initCounter,
            real_counter AS realCounter
        FROM
            ekb_fault_type_reason
        <where>
            <if test="faultTypeKey != null and faultTypeKey != ''">
                AND fault_type_key = #{faultTypeKey}
            </if>
            <if test="faultReasonKey != null and faultReasonKey != ''">
                AND fault_reason_key = #{faultReasonKey}
            </if>
        </where>
    </select>

    <delete id="deleteEkbFaultTypeReason">
        DELETE FROM ekb_fault_type_reason
        WHERE
        fault_type_key = #{faultTypeKey}
    </delete>

    <delete id="deleteEkbFaultTypeReasonByFaultReasonKey">
         DELETE FROM ekb_fault_type_reason
         WHERE
         fault_reason_key = #{faultReasonKey}
    </delete>

	<delete id="deleteEkbFaultTypeReasonRelation">
		DELETE FROM ekb_fault_type_reason
        WHERE
        	fault_type_key = #{faultTypeKey}
        AND
        	fault_reason_key = #{faultReasonKey}
	</delete>
	<delete id="deleteEkbFaultReasonMeasureRelation">
		DELETE FROM ekb_reason_measure
        WHERE
        	fault_type_key = #{faultTypeKey}
        AND
        	fault_reason_key = #{faultReasonKey}
        AND
        	fault_measure_key = #{faultMeasureKey}
	</delete>
</mapper>