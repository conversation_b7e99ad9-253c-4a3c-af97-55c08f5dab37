<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.ekb.EkbFaultSnowSlideMapping">
    <sql id="ekbFaultSnowSlideColumnAlias">
        fss.id AS id,
        fss.vehicle_type_id AS vehicleTypeId,
        fss.parent_fault_type_key AS parentFaultTypeKey,
        fss.fault_type_key AS faultTypeKey,
        fss.correlation_time AS correlationTime,
        fss.enable AS enable,
        fss.create_by AS createBy,
        fss.create_time AS createTime,
        fss.modify_by AS modifyBy,
        fss.modify_time AS modifyTime,
        fss.remark AS remark
    </sql>

    <select id="findFaultSnowSlideByKey" resultType="cc.crrc.manage.pojo.ekb.EkbFaultSnowSlidePO">
        SELECT
        <include refid="ekbFaultSnowSlideColumnAlias"/>,
            eft.name_cn AS nameCn,
            eftp.name_cn AS parentNameCn
        FROM
            fault_snow_slide fss
        LEFT JOIN
            ekb_fault_type eft
        ON
            fss.fault_type_key = eft.fault_type_key
        LEFT JOIN
            ekb_fault_type eftp
        ON
            fss.parent_fault_type_key = eftp.fault_type_key
        WHERE
            fss.parent_fault_type_key = #{faultTypeKey}
        ORDER BY fss.create_by DESC
    </select>

    <select id="getEnableStatusByFaultTypeKey" resultType="java.lang.Boolean">
        SELECT
            bool_and(fss.enable)
        FROM
            fault_snow_slide fss
        WHERE
            fss.fault_type_key = #{faultTypeKey}
    </select>

    <delete id="deleteFaultSnowSlideByKey">
        DELETE FROM
            fault_snow_slide
        WHERE
            parent_fault_type_key = #{faultTypeKey}
    </delete>

    <insert id="saveFaultSnowSlideRelation">
        INSERT INTO
            fault_snow_slide(
            id,
            vehicle_type_id,
            parent_fault_type_key,
            fault_type_key,
            correlation_time,
            enable,
            create_by,
            create_time,
            modify_by,
            modify_time,
            remark
            )VALUES
        <foreach collection="listSlidePO" index="index" item="item" separator=",">
            (
            #{item.id},
            #{item.vehicleTypeId},
            #{item.parentFaultTypeKey},
            #{item.faultTypeKey},
            #{item.correlationTime},
            false,
            #{item.createBy},
            NOW(),
            #{item.modifyBy},
            NOW(),
            #{item.remark}
            )
        </foreach>
    </insert>

    <update id="changeEnableStatus">
        UPDATE fault_snow_slide
        SET enable      = #{enable},
            modify_by   = #{modifyBy},
            modify_time = CURRENT_TIMESTAMP
        WHERE vehicle_type_id = #{vehicleTypeId}
          AND (
                fault_type_key = #{faultTypeKey}
                OR
                parent_fault_type_key = #{faultTypeKey}
            )
    </update>

    <select id="getFaultSnowSlideExceptKey" resultType="cc.crrc.manage.common.vo.TreeNodeVO">
        SELECT
            fss.parent_fault_type_key AS parentId,
            fss.fault_type_key AS id
        FROM
            fault_snow_slide fss
        WHERE
            fss.vehicle_type_id = #{vehicleTypeId}
            AND fss.parent_fault_type_key != #{faultTypeKey}
        ORDER BY fss.create_by DESC
    </select>

    <!--暂时不用改sql
    <select id="findFaultSnowSlideRelationBys" resultType="cc.crrc.manage.pojo.ekb.EkbFaultSnowSlidePO">
        SELECT
            <include refid="ekbFaultSnowSlideColumnAlias"></include>,
            eft.name_cn AS nameCn,
            eft.fault_code AS faultCode,
            eftp.name_cn AS parentNameCn,
            eftp.fault_code AS parentFaultCode
        FROM
            fault_snow_slide fss
        LEFT JOIN
            ekb_fault_type eft
        ON
            fss.fault_type_key = eft.fault_type_key
        LEFT JOIN
            ekb_fault_type eftp
        ON
            fss.parent_fault_type_key = eftp.fault_type_key
        <where>
                <if test="vehicleTypeId != null and vehicleTypeId != 0">
                    AND fss.vehicle_type_id = #{vehicleTypeId}
                </if>
                <if test="faultCode != null and faultCode !=''">
                    AND (eft.fault_code LIKE '%'||#{faultCode}||'%' OR eftp.fault_code = '%'||#{faultCode}||'%')
                </if>
                <if test="nameCn != null and nameCn !=''">
                    AND (eft.name_cn = LIKE '%'||#{nameCn}||'%' OR eftp.name_cn = LIKE '%'||#{nameCn}||'%')
                </if>
        </where>
    </select>
    -->
    <select id="findFirstParentFaultTypeKey" resultType="java.lang.String">
        SELECT DISTINCT
        parent_fault_type_key AS parentFaultTypeKey
        FROM
            fault_snow_slide
        WHERE
            parent_fault_type_key not IN (SELECT fault_type_key FROM fault_snow_slide)
            <if test="vehicleTypeId != null and vehicleTypeId != ''">
                AND vehicle_type_id = #{vehicleTypeId}
            </if>
    </select>

    <!--<select id="findFaultSnowSlideBy" resultType="cc.crrc.manage.pojo.ekb.EkbFaultSnowSlidePO">
        SELECT
        <include refid="ekbFaultSnowSlideColumnAlias"></include>,
        eft.name_cn AS nameCn,
        eft.fault_code AS faultCode,
        eftp.name_cn AS parentNameCn,
        eftp.fault_code AS parentFaultCode
        FROM
        fault_snow_slide fss
        LEFT JOIN
        ekb_fault_type eft
        ON
        fss.fault_type_key = eft.fault_type_key
        LEFT JOIN
        ekb_fault_type eftp
        ON
        fss.parent_fault_type_key = eftp.fault_type_key
        <where>
            <if test="vehicleTypeId != null">
                fss.vehicle_type_id = #{vehicleTypeId}
            </if>
            <if test="faultTypeKey != null and faultTypeKey !=''">
                AND fss.parent_fault_type_key = #{faultTypeKey}
            </if>
        </where>
    </select>-->

    <select id="findListEkbFaultSnowSlide" resultType="cc.crrc.manage.pojo.ekb.EkbFaultSnowSlidePO">
        SELECT
        <include refid="ekbFaultSnowSlideColumnAlias"/>,
            eft.name_cn AS nameCn,
            eft.fault_code AS faultCode,
            eftp.name_cn AS parentNameCn,
            eftp.fault_code AS parentFaultCode,
            mvt.name AS vehicleTypeName
        FROM
            fault_snow_slide fss
        LEFT JOIN
            ekb_fault_type eft
        ON
            fss.fault_type_key = eft.fault_type_key
        LEFT JOIN
            ekb_fault_type eftp
        ON
            fss.parent_fault_type_key = eftp.fault_type_key
        LEFT JOIN
            mtr_vehicle_type mvt
        ON
            fss.vehicle_type_id = mvt.id
        <where>
            <if test="vehicleTypeId != null and vehicleTypeId != ''">
                fss.vehicle_type_id = #{vehicleTypeId}
            </if>
        </where>
    </select>
    <select id="getSnowSlide" resultType="cc.crrc.manage.pojo.ekb.EkbFaultSnowSlidePO">
        SELECT
            id,
            vehicle_type_id AS vehicleTypeId,
            parent_fault_type_key AS parentFaultTypeKey,
            fault_type_key AS faultTypeKey,
            correlation_time AS correlationTime
        FROM
            fault_snow_slide
        WHERE
            (parent_fault_type_key = #{faultTypeKey} OR fault_type_key = #{faultTypeKey})
        <if test="enable != null">
            AND enable = #{enable}
        </if>
    </select>
    <update id="updateSnowSlide">
        UPDATE 	mtc_alarm_warning
        SET suppressed_by = #{alarmWarning.faultTypeKey},
            suppressed_status = true
        FROM(
            SELECT
                id,
                vehicle_id,
                fault_type_key,
                start_time
            FROM mtc_alarm_warning
            WHERE
                vehicle_id = #{alarmWarning.vehicleId} AND
                end_time IS NULL AND
                suppressed_status = false AND
                <foreach collection="keys" open="(" close=")" separator="or" item="item">
                    (fault_type_key = #{item.faultTypeKey}
                    AND
                    abs(tstzrange_subdiff(#{alarmWarning.startTime}, start_time)) &lt;= #{item.correlationTime} / 1000)
                </foreach>
        ) tmp
        WHERE mtc_alarm_warning.id = tmp.id
    </update>
    <select id="getParentFaultTypeKey" resultType="cc.crrc.manage.pojo.ekb.EkbFaultSnowSlidePO">
        SELECT
            id,
            vehicle_id AS vehicleId,
            fault_type_key AS faultTypeKey,
            start_time AS startTime
        FROM mtc_alarm_warning
        WHERE
            vehicle_id = #{alarmWarning.vehicleId} AND
            end_time IS NULL AND
            <foreach collection="keys" open="(" close=")" separator="or" item="item">
                (fault_type_key = #{item.parentFaultTypeKey}
                AND
                abs(tstzrange_subdiff(#{alarmWarning.startTime}, start_time)) &lt;= #{item.correlationTime} / 1000)
            </foreach>
        ORDER BY start_time
        LIMIT 1
    </select>
    <select id="getParentFault" resultType="cc.crrc.manage.pojo.ekb.EkbFaultSnowSlidePO">
        SELECT
            id,
            vehicle_type_id AS vehicleTypeId,
            parent_fault_type_key AS parentFaultTypeKey,
            fault_type_key AS faultTypeKey,
            correlation_time AS correlationTime
        FROM
            fault_snow_slide
        WHERE
            fault_type_key = #{childFault.faultTypeKey} AND
            parent_fault_type_key != #{alarmWarning.faultTypeKey} AND
            enable = true
    </select>

    <delete id="deleteSnowSlideByKeys">
        DELETE FROM
        fault_snow_slide
        WHERE
        fault_type_key IN
        <foreach collection="faultTypeKeyList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        OR
        parent_fault_type_key IN
        <foreach collection="faultTypeKeyList" index="index" item="parent" open="(" separator="," close=")">
            #{parent}
        </foreach>
    </delete>

    <!--根据父节点faultTypeKey查询子节点faultTypeKey-->
    <select id="findFaultTypeKeyByParentKey" resultType="java.lang.String">
        SELECT DISTINCT
        fault_type_key AS faultTypeKey
        FROM
        fault_snow_slide
        WHERE
        parent_fault_type_key IN
        <foreach collection="faultTypeKeyList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>