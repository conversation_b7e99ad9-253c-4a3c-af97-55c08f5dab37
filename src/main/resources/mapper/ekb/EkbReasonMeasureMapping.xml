<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.ekb.EkbReasonMeasureMapping">
    <!--取得故障原因和措施关系-->
    <select id="listReasonMeasure" resultType="cc.crrc.manage.pojo.ekb.EkbReasonMeasurePO">
        SELECT
            fault_type_key AS faultTypeKey,
            fault_reason_key AS faultReasonKey,
            init_counter AS initCounter,
            real_counter AS realCounter,
            fault_measure_key AS faultMeasureKey
        FROM
            ekb_reason_measure
        <where>
            <if test="faultTypeKey != null and faultTypeKey != ''">
                AND fault_type_key = #{faultTypeKey}
            </if>
            <if test="faultReasonKey != null and faultReasonKey != ''">
                AND fault_reason_key = #{faultReasonKey}
            </if>
            <if test="faultMeasureKey != null and faultMeasureKey != ''">
                AND fault_measure_key = #{faultMeasureKey}
            </if>
        </where>
    </select>
    <!--更新原因措施关系的real_counter-->
    <update id="updateReasonMeasureRealCounter">
        UPDATE
            ekb_reason_measure
        SET
            real_counter = #{realCounter}
        WHERE
            fault_type_key = #{faultTypeKey} AND
            fault_reason_key = #{faultReasonKey} AND
            fault_measure_key = #{faultMeasureKey}
    </update>

    <insert id="batchInsertReasonMeasure">
        insert into ekb_reason_measure
        (
        fault_type_key,
        fault_reason_key,
        fault_measure_key,
        init_counter,
        real_counter
        )
        values
        <foreach collection="list" item="item" index= "index" separator =",">
            (
            #{item.faultTypeKey},
            #{item.faultReasonKey},
            #{item.faultMeasureKey},
            0,
            0
            )
        </foreach>

    </insert>
    <!--删除-->
    <delete id="deleteReasonMeasure">
        DELETE FROM ekb_reason_measure
        <where>
            <if test="faultTypeKey != null and faultTypeKey != ''">
                AND fault_type_key = #{faultTypeKey}
            </if>
            <if test="faultReasonKey != null and faultReasonKey != ''">
                AND fault_reason_key = #{faultReasonKey}
            </if>
            <if test="faultMeasureKey != null and faultMeasureKey != ''">
                AND fault_measure_key = #{faultMeasureKey}
            </if>
        </where>
    </delete>
    <delete id="deleteReasonMeasureByMeasureKeys">
        DELETE FROM ekb_reason_measure
        <where>
            fault_measure_key IN
            <foreach collection="measureList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </delete>
</mapper>