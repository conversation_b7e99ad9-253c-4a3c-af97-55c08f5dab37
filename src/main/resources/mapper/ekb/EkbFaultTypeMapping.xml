<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.ekb.EkbFaultTypeMapping">

    <sql id="EkbFaultTypeColumnAlias">
        t.id,
        t.line_id as lineId,
        t.vehicle_type_id as vehicleTypeId,
        t.fault_type_key as faultTypeKey,
        t.vehicle_structure_code as vehicleStructureCode,
        t.name_cn as nameCn,
        t.name_en as nameEn,
        t.fault_code as faultCode,
        t.fault_level as faultLevel,
        t.subsystem,
        t.description,
        t.location,
        t.frontline_disposal_recommendations as frontlineDisposalRecommendations,
        t.overhaul_suggestions as overhaulSuggestions,
        t.fault_reason as faultReason,
        t.model_code AS modelCode,
        t.enable,
        t.fault_category AS faultCategory
    </sql>

    <!--新增故障类型-->
    <insert id="addEkbFaultType">
        INSERT INTO
        ekb_fault_type
        (
            id,
            line_id,
            vehicle_type_id,
            fault_type_key,
            vehicle_structure_code,
            name_cn,
            name_en,
            fault_code,
            fault_level,
            subsystem,
            del_flag,
            create_time,
            create_by,
            modify_time,
            modify_by,
            description,
            location,
            frontline_disposal_recommendations,
            overhaul_suggestions,
            fault_reason,
            fault_category
        )
        VALUES
        (
            #{id},
            #{lineId},
            #{vehicleTypeId},
            #{faultTypeKey},
            #{vehicleStructureCode},
            #{nameCn},
            #{nameEn},
            #{faultCode},
            #{faultLevel},
            #{subsystem},
            0,
            CURRENT_TIMESTAMP,
            #{createBy},
            CURRENT_TIMESTAMP,
            #{modifyBy},
            #{description},
            #{location},
            #{frontlineDisposalRecommendations},
            #{overhaulSuggestions},
            #{faultReason},
            #{faultCategory}
        )
    </insert>
    <!--删除故障类型 （逻辑删除）-->
    <update id="deleteEkbFaultType">
        UPDATE ekb_fault_type SET del_flag = 1 WHERE id = #{id}
    </update>

    <!--更新故障类型-->
    <update id="updateEkbFaultType">
        UPDATE ekb_fault_type
        <trim prefix="set" suffixOverrides=",">
            <if test="vehicleTypeId != null and vehicleTypeId != ''">
                vehicle_type_id = #{vehicleTypeId},
            </if>
            <if test="vehicleStructureCode != null and vehicleStructureCode != ''">
                vehicle_structure_code = #{vehicleStructureCode},
            </if>
            <if test="nameCn != null and nameCn != ''">
                name_cn = #{nameCn},
            </if>
            <if test="nameEn != null and nameEn != ''">
                name_en = #{nameEn},
            </if>
            <if test="faultCode != null and faultCode !=''">
                fault_code = #{faultCode},
            </if>
            <if test="faultLevel != null and faultLevel !=null">
                fault_level = #{faultLevel},
            </if>
            <if test="subsystem != null and subsystem != ''">
                subsystem = #{subsystem},
            </if>
            <if test="modifyBy != null and modifyBy !=''">
                modify_by = #{modifyBy},
            </if>
            <if test="description != null ">
                description = #{description},
            </if>
            <if test="frontlineDisposalRecommendations != null ">
                frontline_disposal_recommendations = #{frontlineDisposalRecommendations},
            </if>
            <if test="overhaulSuggestions != null">
                overhaul_suggestions = #{overhaulSuggestions},
            </if>
            <if test="faultReason != null">
                fault_reason = #{faultReason},
            </if>
            <if test="location != null and location !=''">
                location = #{location},
            </if>
            modify_time = CURRENT_TIMESTAMP
        </trim>
        WHERE
            id = #{id} AND del_flag = '0'
    </update>

    <!--查询故障类型列表-->
    <select id="listEkbFaultType" resultType = "cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO">
        SELECT
        t.id,
        t.line_id as lineId,
        t.vehicle_type_id as vehicleTypeId,
        t.fault_type_key as faultTypeKey,
        t.vehicle_structure_code as vehicleStructureCode,
        t.name_cn AS nameCn,
        t.name_cn ||'('||t.name_en||')' AS nameCnE,
        t.name_en as nameEn,
        t.fault_code as faultCode,
        t.fault_level as faultLevel,
        t.subsystem,
        t.description,
        t.location,
        t.fault_category AS faultCategory,
        t.frontline_disposal_recommendations as frontlineDisposalRecommendations,
        t.overhaul_suggestions as overhaulSuggestions,
        t.fault_reason as faultReason,
        t.model_code AS modelCode,
        t.enable,
        mvt.name as vehicleType,
        sd.label as assCarSystem,
        svts.name_cn AS vehicleStructureName
        FROM ekb_fault_type t
        LEFT JOIN mtr_vehicle_type mvt ON t.vehicle_type_id = mvt.id
        LEFT JOIN sys_dict sd ON    sd.line_id = t.line_id and sd.vehicle_type_id= t.vehicle_type_id and sd.type_code ='ass_car_system' and t.subsystem = sd.value
        LEFT JOIN stru_vehicle_type_structure svts ON t.vehicle_structure_code = svts.structure_code AND svts.vehicle_type_id = t.vehicle_type_id
        WHERE
            t.del_flag = '0' AND
            mvt.del_flag = '0'
            <if test="id != null and id !=''">
                AND t.id = #{id}
            </if>
            <if test="lineId!=null and lineId!=''">
                and t.line_id = #{lineId}
            </if>
            <if test="vehicleTypeId != null and vehicleTypeId != ''">
                AND t.vehicle_type_id = #{vehicleTypeId}
            </if>
            <if test="faultTypeKey != null and faultTypeKey != ''">
                AND t.fault_type_key LIKE '%'||#{faultTypeKey}||'%'
            </if>
            <if test="vehicleStructureCode != null and vehicleStructureCode != ''">
                AND t.vehicle_structure_code LIKE '%'||#{vehicleStructureCode}||'%'
            </if>
            <if test="nameCn != null and nameCn != ''">
                AND t.name_cn LIKE '%'||#{nameCn}||'%'
            </if>
            <if test="nameEn != null and nameEn != ''">
                AND t.name_en LIKE '%'||#{nameEn}||'%'
            </if>
            <if test="faultCode != null and faultCode != ''">
                AND t.fault_code LIKE '%'||#{faultCode}||'%'
            </if>
            <if test="faultLevel != null">
                AND t.fault_level = #{faultLevel}
            </if>
            <if test="subsystem != null and subsystem != ''">
                AND t.subsystem = #{subsystem}
            </if>
            <if test="description != null and description != ''">
                AND t.description LIKE '%'||#{description}||'%'
            </if>
            <if test="location != null and location != ''">
                AND t.location = #{location}
            </if>
            <if test="faultReason != null and faultReason != ''">
                AND t.fault_reason LIKE '%'||#{faultReason}||'%'
            </if>
            <if test="overhaulSuggestions != null and overhaulSuggestions != ''">
                AND t.overhaul_suggestions LIKE '%'||#{overhaulSuggestions}||'%'
            </if>
            <if test="frontlineDisposalRecommendations != null and frontlineDisposalRecommendations != ''">
                AND t.frontline_disposal_recommendations LIKE '%'||#{frontlineDisposalRecommendations}||'%'
            </if>
            <if test="faultCategory != null">
                AND t.fault_category = #{faultCategory}
            </if>
            AND sd.del_flag = '0'
        ORDER BY
        convert_to( sd.label, 'gbk' ),
        CASE t.fault_level WHEN '3' THEN 1 WHEN '2' THEN 2 WHEN '1' THEN 3 WHEN '0' THEN 4 ELSE 5
        END ASC,
        convert_to( t.name_cn, 'gbk' )

    </select>

    <select id="getNumbeOfDisabledNodes" resultType="java.lang.Boolean">
        select enable
        FROM fault_snow_slide
        WHERE fault_type_key = #{faultTypeKey}
    </select>

    <select id="getEkbFaultTypeById" resultType="cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO">
        SELECT
        <include refid="EkbFaultTypeColumnAlias"></include>
        FROM
        ekb_fault_type t
        WHERE
        t.id = #{id}
    </select>
    <select id="getEkbFaultTypeByCode" resultType="cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO">
        SELECT
        <include refid="EkbFaultTypeColumnAlias"></include>
        FROM
        ekb_fault_type t
        WHERE
        t.fault_code = #{faultCode}
    </select>
    <!--根据子系统和构型编码筛选故障-->
    <select id="getFaultTypeByStructureCodeAndSubsystem" resultType="cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO">
        SELECT
            <include refid="EkbFaultTypeColumnAlias"></include>
        FROM
            ekb_fault_type t
        WHERE
            t.del_flag = '0'
            <if test="structureCode != null and structureCode != ''">
                AND t.vehicle_structure_code = #{structureCode}
            </if>
            <if test="subsystem != null and subsystem != ''">
                AND t.subsystem = #{subsystem}
            </if>
    </select>

    <select id="getEkbFaultTypeForVehicleTypeList" resultType="cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO">
        SELECT
        <include refid="EkbFaultTypeColumnAlias"></include>
        FROM
        ekb_fault_type t
        WHERE
        t.vehicle_structure_code in
        (
        SELECT
        structure_code
        FROM
        stru_vehicle_type_structure t2
        WHERE
        t2.structure_code  LIKE '%'||#{structureCode}||'%'
        )
        <if test="vehicleTypeId != null and vehicleTypeId != ''">
            AND t.vehicle_type_id = #{vehicleTypeId}
        </if>
        AND t.del_flag = '0'
    </select>
    <select id="getEkbFaultTypeByFaultTypeKey" resultType="cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO">
        SELECT
        <include refid="EkbFaultTypeColumnAlias"></include>
        FROM
        ekb_fault_type t
        WHERE
        t.fault_type_key= #{faultTypeKey}
    </select>
    <select id="faultList" resultType="java.util.HashMap">
        SELECT
        t.fault_type_key AS faultTypeKey,
        t.name_cn ||'('||t.name_en||')'AS nameCN,
        t.name_en AS nameEN,
        t.fault_code AS faultCode
        FROM
        ekb_fault_type t
        WHERE
        t.del_flag = '0'
        <if test="location != null and location != ''">
            AND t.location = #{location}
        </if>
        <if test="subsystem != null and subsystem != ''">
            AND t.subsystem = #{subsystem}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId != ''">
            AND t.vehicle_type_id = #{vehicleTypeId}
        </if>
    </select>
    <select id="faultFuzzyList" resultType="java.util.HashMap">
        SELECT
        t.fault_type_key AS faultTypeKey,
        t.name_cn AS nameCN,
        t.name_en AS nameEN,
        t.fault_code AS faultCode
        FROM
        ekb_fault_type t
        WHERE
        t.del_flag = '0'
        <if test="location != null and location != ''">
            AND t.location = #{location}
        </if>
        <if test="subsystem != null and subsystem != ''">
            AND t.subsystem = #{subsystem}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId != '' ">
            AND t.vehicle_type_id = #{vehicleTypeId}
        </if>
        <if test="nameCn != null and nameCn != ''">
            AND t.name_cn LIKE '%'||#{nameCn}||'%'
        </if>
    </select>
    <select id="getFaultTypeByNameCn" resultType="cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO">
        SELECT
            <include refid="EkbFaultTypeColumnAlias"></include>
        FROM
            ekb_fault_type t
        WHERE
            t.name_cn = #{nameCn}
            and
            t.del_flag = '0'
    </select>
    <select id="getFaultTypeByFaultTypeKey" resultType="cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO">
        SELECT
        <include refid="EkbFaultTypeColumnAlias"></include>
        FROM
        ekb_fault_type t
        WHERE
        t.fault_type_key = #{faultTypeKey}
        and
        t.del_flag = 0
    </select>
     <select id="listEkbFaultTypeForSnowSlide" resultType="cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO">
        SELECT
            <include refid="EkbFaultTypeColumnAlias"></include>
        FROM
            ekb_fault_type t
        WHERE
            del_flag = 0
        <if test="nameCn != null and nameCn != ''">
            AND t.name_cn LIKE '%'||#{nameCn}||'%'
        </if>
            AND t.fault_type_key not in
        <foreach item="item" index="index" collection="faultTypeKey" open="("  close=")" separator=",">
            #{item}
        </foreach>
            AND t.subsystem in ( SELECT DISTINCT subsystem FROM ekb_fault_type WHERE fault_type_key in
        <foreach item="item1" index="index" collection="faultTypeKey" open="("  close=")" separator=",">
             #{item1}
        </foreach> )

     </select>

    <select id="getFaultTypeListByFaultTypeKey" resultType="cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO">
        SELECT
            <include refid="EkbFaultTypeColumnAlias"/>
        FROM
            ekb_fault_type t
        WHERE
            t.fault_type_key IN
            <foreach collection="faultTypeKeyList" open="(" close=")" separator="," item="faultTypeKey">
                #{faultTypeKey}
            </foreach>
    </select>

    <insert id="addFaultTypeList">
        insert into ekb_fault_type
        (
        id,
        line_id,
        vehicle_type_id,
        fault_type_key,
        vehicle_structure_code,
        name_cn,
        name_en,
        fault_code,
        fault_level,
        subsystem,
        del_flag,
        description,
        location,
        frontline_disposal_recommendations,
        overhaul_suggestions,
        fault_reason
        )
        values
        <foreach collection="list" item="item" index= "index" separator =",">
            (
            #{item.id},
            #{item.lineId},
            #{item.vehicleTypeId},
            #{item.faultTypeKey},
            #{item.vehicleStructureCode},
            #{item.nameCn},
            #{item.nameEn},
            #{item.faultCode},
            #{item.faultLevel},
            #{item.subsystem},
            0,
            #{item.description},
            #{item.location},
            #{item.frontlineDisposalRecommendations},
            #{item.overhaulSuggestions},
            #{item.faultReason}
            )
        </foreach>
    </insert>
    <insert id="addEkbFaultTypeForExcel">
        insert into ekb_fault_type
        (
        id,
        line_id,
        vehicle_type_id,
        fault_type_key,
        vehicle_structure_code,
        name_cn,
        name_en,
        fault_code,
        fault_level,
        subsystem,
        del_flag,
        create_time,
        modify_time,
        description,
        location,
        enable,
        frontline_disposal_recommendations,
        overhaul_suggestions,
        fault_reason,
        fault_category
        )
        values
        (
        #{id},
        #{lineId},
        #{vehicleTypeId},
        #{faultTypeKey},
        #{vehicleStructureCode},
        #{nameCn},
        #{nameEn},
        #{faultCode},
        #{faultLevel},
        #{subsystem},
        0,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP,
        #{description},
        #{location},
        #{enable},
        #{frontlineDisposalRecommendations},
        #{overhaulSuggestions},
        #{faultReason},
        #{faultCategory}
        )
    </insert>

    <select id="getEkbFaultCodeForExcel" resultType="cc.crrc.manage.pojo.excel.EkbFaultTypeForExportPO">
        SELECT
            t.id,
            t.line_id as lineId,
            t.vehicle_type_id as vehicleTypeId,
            t.fault_type_key as faultTypeKey,
            t.vehicle_structure_code as vehicleStructureCode,
            t.name_cn as nameCn,
            t.name_en as nameEn,
            t.fault_code as faultCode,
            t.fault_level as faultLevel,
            t.subsystem,
            t.description,
            t.location,
            t.frontline_disposal_recommendations as frontlineDisposalRecommendations,
            t.overhaul_suggestions as overhaulSuggestions,
            t.fault_reason as faultReason,
            t.model_code AS modelCode,
            t.enable,
            CASE WHEN t.fault_category = 1 THEN '自动故障' WHEN t.fault_category = 2 THEN '人工故障' END AS faultCategory,
            t.enable AS enable
        FROM ekb_fault_type t
        WHERE
        t.del_flag = '0'
        <if test="lineId != null and lineId !='' ">
            and t.line_id = #{lineId}
        </if>
        <if test="faultCategory != null">
            and t.fault_category = #{faultCategory}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId != ''">
            AND t.vehicle_type_id = #{vehicleTypeId}
        </if>
        LIMIT 3000
    </select>

    <select id="getFaultTypeList" resultType="java.lang.String">
        select distinct fault_type_key from ekb_fault_type where del_flag = '0'
    </select>

    <select id="getFaultTypeAndFaultCodeList" resultType="cc.crrc.manage.pojo.excel.EkbFaultTypeForExcelPO">
        select distinct
        fault_type_key as faultTypeKey,
        fault_code as faultCode
        from ekb_fault_type where del_flag = '0'
    </select>

    <!--根据fault_type_key修改enable状态-->
    <update id="updateEkbFaultTypeByKey">
        UPDATE ekb_fault_type
        SET
        <if test="location != null and location != ''">
            location = #{location},
        </if>
        <if test="enable != null">
            enable = #{enable},
        </if>
        modify_time = CURRENT_TIMESTAMP
        WHERE
        del_flag = 0
        AND fault_type_key = #{faultTypeKey}
    </update>

    <select id="getEkbFaultTypeAll" resultType="cc.crrc.manage.pojo.ekb.EkbFaultTypeDTO">
        SELECT
            <include refid="EkbFaultTypeColumnAlias"/>,
            svsc.component_id AS componentId
        FROM
            ekb_fault_type t
        LEFT JOIN stru_vehicle_structure_component svsc ON  t.vehicle_structure_code = svsc.structure_code
        WHERE
            t.del_flag = '0'
    </select>
    <select id="getEkbFaultCodeByExcel" resultType="cc.crrc.manage.pojo.excel.EkbFaultTypeForExcelPO">
        SELECT
        t.id,
        CASE WHEN t.line_id = '24' THEN '绍兴2号线一期' END AS lineId,
        t.vehicle_type_id as vehicleTypeId,
        t.fault_type_key as faultTypeKey,
        t.vehicle_structure_code as vehicleStructureCode,
        t.name_cn as nameCn,
        t.name_en as nameEn,
        t.fault_code as faultCode,
        t.fault_level as faultLevel,
        t.subsystem,
        t.description,
        t.location,
        t.frontline_disposal_recommendations as frontlineDisposalRecommendations,
        t.overhaul_suggestions as overhaulSuggestions,
        t.fault_reason as faultReason,
        t.model_code AS modelCode,
        t.enable,
        CASE WHEN t.fault_category = 1 THEN '自动故障' WHEN t.fault_category = 2 THEN '人工故障' END AS faultCategory,
        t.enable AS enable,
        mvt.name as vehicleType
        FROM ekb_fault_type t
        LEFT JOIN mtr_vehicle_type mvt ON t.vehicle_type_id = mvt.id
        WHERE
        t.del_flag = '0'
        <if test="lineId != null and lineId !='' ">
            and t.line_id = #{lineId}
        </if>
        <if test="faultCategory != null">
            and t.fault_category = #{faultCategory}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId != ''">
            AND t.vehicle_type_id = #{vehicleTypeId}
        </if>
        <choose>
            <when test="countNum !=null and countNum!= ''">
                LIMIT 1
            </when>
            <otherwise>
                LIMIT 3000
            </otherwise>
        </choose>
    </select>
</mapper>