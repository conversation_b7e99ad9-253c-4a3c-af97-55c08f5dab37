<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.ekb.EkbFaultMeasureMapping">
    <sql id="ekbFaultMeasureAlias">
        t1.id,
        t1.vehicle_type_id AS vehicleTypeId,
        t1.measure_key AS measureKey,
        t1.measure_code AS measureCode,
        t1.content,
        t1.category
    </sql>
    <!--按照措施编码、措施类别、措施内容同时模糊查询-->
    <select id="listFaultMeasureVaguely" resultType="cc.crrc.manage.pojo.ekb.EkbFaultMeasureVO">
        SELECT
            <include refid="ekbFaultMeasureAlias"></include>,
            mvt.name AS vehicleType
        FROM
            ekb_fault_measure t1
        LEFT JOIN
            mtr_vehicle_type mvt
        ON
            t1.vehicle_type_id = mvt.id
        <where>
        	<if test="id != null and id !=''">
		        t1.measure_key NOT IN(
					SELECT erm.fault_measure_key FROM ekb_reason_measure erm
					JOIN ekb_fault_reason efr ON efr.reason_key = erm.fault_reason_key
					WHERE efr.id = #{id} AND erm.fault_measure_key IS NOT NULL)
        	</if>
            <if test="keyword != null and keyword != ''">
                AND (t1.content LIKE '%'||#{keyword}||'%' OR
                t1.measure_code LIKE '%'||#{keyword}||'%' OR
                t1.category LIKE '%'||#{keyword}||'%')
            </if>
        </where>
        ORDER BY
            t1.modify_time DESC
    </select>
    <!--查询列表-->
    <select id="listFaultMeasure" resultType="cc.crrc.manage.pojo.ekb.EkbFaultMeasureVO">
        SELECT
            <include refid="ekbFaultMeasureAlias"></include>,
            mvt.name AS vehicleType
        FROM
            ekb_fault_measure t1
        LEFT JOIN
            mtr_vehicle_type mvt
        ON
            t1.vehicle_type_id = mvt.id
        <where>
            <if test="vehicleTypeId != null and vehicleTypeId != ''">
                AND t1.vehicle_type_id = #{vehicleTypeId}
            </if>
            <if test="measureCode != null and measureCode != ''">
                AND t1.measure_code LIKE '%'||#{measureCode}||'%'
            </if>
            <if test="content != null and content != ''">
                AND t1.content LIKE '%'||#{content}||'%'
            </if>
            <if test="category != null and category != ''">
                AND t1.category = #{category}
            </if>
        </where>
        ORDER BY
            t1.modify_time DESC
    </select>
    <!--新增-->
    <insert id="insertFaultMeasure">
        INSERT INTO ekb_fault_measure (
            id,
            vehicle_type_id,
            measure_key,
            measure_code,
            content,
            category,
            create_by,
            create_time,
            modify_by,
            modify_time
        )
        VALUES (
            #{id},
            #{vehicleTypeId},
            #{measureKey},
            #{measureCode},
            #{content},
            #{category},
            #{createBy},
            CURRENT_TIMESTAMP,
            #{modifyBy},
            CURRENT_TIMESTAMP
        )
    </insert>
    <!--更新-->
    <update id="updateFaultMeasure">
        UPDATE ekb_fault_measure
        <trim prefix="set" suffixOverrides=",">
            <if test="vehicleTypeId != null and vehicleTypeId != ''">
                vehicle_type_id = #{vehicleTypeId},
            </if>
            <if test="measureCode != null and measureCode != ''">
                measure_code = #{measureCode},
            </if>
            <if test="content != null and content != ''">
                content = #{content},
            </if>
            <if test="category != null and category != ''">
                category = #{category},
            </if>
            <if test="modifyBy != null and modifyBy != ''">
                modify_by = #{modifyBy},
            </if>
            modify_time = CURRENT_TIMESTAMP
        </trim>
        <where>
            id = #{id}
        </where>
    </update>
    <!--删除-->
    <delete id="deleteFaultMeasure">
        DELETE FROM ekb_fault_measure
        <where>
            id IN
            <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </delete>
    <select id="getEkbFaultMeasure" resultType="cc.crrc.manage.pojo.ekb.EkbFaultMeasurePO">
        select
        <include refid="ekbFaultMeasureAlias"></include>
        from
        ekb_fault_measure t1
        LEFT JOIN ekb_reason_measure t2 ON t1.measure_key = t2.fault_measure_key
        WHERE
        t2.fault_type_key = #{faultTypeKey}
        and
        t2.fault_reason_key = #{faultReasonKey}
        LIMIT 1
    </select>
    <select id="getFaultMeasureByContent" resultType="cc.crrc.manage.pojo.ekb.EkbFaultMeasurePO">
        select
        <include refid="ekbFaultMeasureAlias"></include>
        from
        ekb_fault_measure t1
        WHERE
        t1.content = #{content}
    </select>

    <insert id="addFaultMeasureList">
        insert into ekb_fault_measure
        (
        id,
        vehicle_type_id,
        measure_key,
        measure_code,
        content,
        category
        )
        values
        <foreach collection="list" item="item" index= "index" separator =",">
            (
            #{item.id},
            #{item.vehicleTypeId},
            #{item.measureKey},
            #{item.measureCode},
            #{item.content},
            #{item.category}
            )
        </foreach>
    </insert>

    <select id="getEkbFaultMeasureForExcel"
            resultType="cc.crrc.manage.pojo.excel.EkbFaultMeasureForExcelPO">
        SELECT
        <include refid="ekbFaultMeasureAlias"/>,
        t1.create_by as createBy,
        t1.create_time as createTime,
        t1.modify_by as modifyBy,
        t1.modify_time as modifyTime
        FROM ekb_fault_measure t1
        order by t1.id
        desc
        <if test="countNum != null and countNum != '' and countNum == 1">
            limit 1
        </if>

    </select>

    <select id="getMeasureKeyList" resultType="java.lang.String">
        select measure_key from ekb_fault_measure
    </select>

    <select id="getMeasureKeyAndMeasureCodeList" resultType="cc.crrc.manage.pojo.excel.EkbFaultMeasureForExcelPO">
        select distinct
        measure_key as measureKey,
        measure_code as measureCode
        from ekb_fault_measure
    </select>

    <select id="findMeasureKeyByIds" resultType="java.lang.String">
        SELECT
        measure_key
        FROM
        ekb_fault_measure
        <where>
            id IN
            <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>
</mapper>