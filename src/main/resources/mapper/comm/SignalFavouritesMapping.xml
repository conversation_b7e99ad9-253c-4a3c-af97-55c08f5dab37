<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.comm.SignalFavouritesMapping">
    <select id="signalFavouritesGroupList" resultType="cc.crrc.manage.pojo.comm.signalfavourites.SignalFavouritesGroupVO">
        SELECT main.id,
               main.name            as groupName,
               main.vehicle_type_id as vehicleTypeId,
               vehicle_type.name    as vehicleTypeName,
               main.vehicle_id      as vehicleId,
               main.vehicle_code    as vehicleCode,
               vehicle.name_cn      as nameCn,
               main."enable",
               main.create_by       as createBy,
               main.create_time     as createTime,
               main.modify_by       as modifyBy,
               main.modify_time     as modifyTime,
               main.remark
        FROM comm_signal_favourites_group main
                 LEFT JOIN mtr_vehicle_type vehicle_type
                           ON main.vehicle_type_id = vehicle_type.ID
                 LEFT JOIN mtr_vehicle vehicle
                           ON main.vehicle_id = vehicle.ID
        WHERE main.create_by = #{createBy}
          and main."enable" = 1
        ORDER BY main.create_time DESC
    </select>

    <select id="signalsFavouritesList" resultType="cc.crrc.manage.pojo.comm.signalfavourites.SignalsFavouritesVO">
        SELECT sub.ID,
               sub.group_id   AS groupId,
               main.NAME      AS groupName,
               sub.signal_id  AS signalId,
               CONCAT(signal.name_cn,'(',signal.name_en,')') AS signalName,
               CASE WHEN signal.data_type = 'BOOLEAN1' THEN 1 ELSE 0 END AS digitalFlag,
               sub.node_id as nodeId
        FROM comm_signal_favourites sub
             JOIN comm_signal_favourites_group main ON sub.group_id = main.ID
             LEFT JOIN comm_original_signal signal ON sub.signal_id = signal.ID
        WHERE sub.group_id = #{groupId}
        ORDER BY sub.signal_id
    </select>

    <insert id="saveSignalFavouritesGroup">
        INSERT INTO comm_signal_favourites_group(
            id,
            name,
            vehicle_type_id,
            vehicle_id,
            vehicle_code,
            create_by,
            create_time,
            remark
        )
        VALUES(
            #{id},
            #{name},
            #{vehicleTypeId},
            #{vehicleId},
            #{vehicleCode},
            #{createBy},
            CURRENT_TIMESTAMP,
            #{remark}
        )
    </insert>

    <insert id="saveSignalsFavourites">
        insert into comm_signal_favourites(id,group_id, signal_id, node_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.id},#{item.groupId}, #{item.signalId}, #{item.nodeId})
        </foreach>
    </insert>

    <delete id="deleteSignalFavouritesGroup">
        delete
        from comm_signal_favourites_group
        where id = #{id}
    </delete>

    <delete id="deleteSignalsFavourites">
        delete
        from comm_signal_favourites
        where group_id = #{id}
    </delete>

    <select id="signalsFavouritesGroupNameIsExists" resultType="java.lang.Integer">
        SELECT count(id) as num
        FROM comm_signal_favourites_group
        WHERE create_by = #{createBy}
          and name = #{groupName}
    </select>
</mapper>