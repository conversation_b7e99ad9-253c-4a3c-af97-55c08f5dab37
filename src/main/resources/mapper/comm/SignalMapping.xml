<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.comm.SignalMapping">
    <select id="list" resultType="cc.crrc.manage.pojo.comm.signal.SignalVO">
        SELECT
        id,
        signal_type signalType,
        protocol_id protocolId,
        name_cn nameCN,
        name_en nameEN,
        byte_offset byteOffset,
        bit_offset bitOffset,
        data_type dataType,
        unit,
        location,
        subsystem,
        parse_script parseScript,
        trigger_value triggerValue,
        max_value maxVal,
        min_value minVal,
        create_by createBy,
        remark,
        <!--        redis_flag redisFlag,-->
        vehicle_type_id vehicleTypeId,
        fault_code faultCode,
        fault_name_cn faultNameCN,
        fault_name_en faultNameEN
        FROM
        v_comm_signal
        WHERE
        protocol_id=#{protocolId}
        <if test="signalType!=null">
            and signal_type=#{signalType}
        </if>
        <if test="name!=null and name!=''">
            <bind name="pattern" value="'%'+_parameter.name + '%'"/>
            and (name_cn like #{pattern} or name_en like #{pattern})
        </if>
        <if test="byteOffset!=null">
            and byte_offset=#{byteOffset}
        </if>
        <if test="subSystem!=null and subSystem!=''">
            and subsystem=#{subSystem}
        </if>
        <if test="location!=null and location!=''">
            and location=#{location}
        </if>
        <if test="faultCode!=null and faultCode!=''">
            and fault_code=#{faultCode}
        </if>
        order by create_time desc
    </select>
    <insert id="saveOriginalSignal">
        INSERT INTO comm_original_signal(id,
                                         protocol_id,
                                         name_cn,
                                         name_en,
                                         byte_offset,
                                         bit_offset,
                                         data_type,
                                         unit,
                                         location,
                                         subsystem,
                                         parse_script,
                                         fault_type_key,
                                         trigger_value,
                                         max_value,
                                         min_value,
                                         create_by,
                                         create_time,
                                         remark,
                                         redis_flag)
        VALUES (#{id},
                #{protocolId},
                #{nameCN},
                #{nameEN},
                #{byteOffset},
                #{bitOffset},
                #{dataType},
                #{unit},
                #{location},
                #{subSystem},
                #{parseScript},
                #{faultTypeKey},
                #{triggerValue},
                #{maxVal},
                #{minVal},
                #{createBy},
                CURRENT_TIMESTAMP,
                #{remark},
                #{redisFlag})
    </insert>

    <insert id="saveExtendSignal">
        INSERT INTO comm_extend_signal(id,
                                       protocol_id,
                                       name_cn,
                                       name_en,
                                       data_type,
                                       unit,
                                       location,
                                       subsystem,
                                       parse_script,
                                       max_value,
                                       min_value,
                                       create_by,
                                       create_time,
                                       remark,
                                       redis_flag)
        VALUES (#{id},
                #{protocolId},
                #{nameCN},
                #{nameEN},
                #{dataType},
                #{unit},
                #{location},
                #{subSystem},
                #{parseScript},
                #{maxVal},
                #{minVal},
                #{createBy},
                CURRENT_TIMESTAMP,
                #{remark},
                #{redisFlag})
    </insert>
    <select id="queryDuplicate" resultType="int">
        SELECT count(1) FROM
        <if test='signalType=="0"'>
            comm_original_signal
        </if>
        <if test='signalType=="1"'>
            comm_extend_signal
        </if>
        WHERE
        protocol_id=#{protocolId}
        AND name_en=#{nameEN}
        <if test="id!=null">
            AND id!=#{id}
        </if>
    </select>

    <delete id="deleteSignal">
        DELETE FROM
        <if test='signalType=="0"'>
            comm_original_signal
        </if>
        <if test='signalType=="1"'>
            comm_extend_signal
        </if>
        WHERE id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <update id="updateSignal">
        UPDATE
        <if test='signalType=="0"'>
            comm_original_signal
        </if>
        <if test='signalType=="1"'>
            comm_extend_signal
        </if>
        SET
        name_cn=#{nameCN},
        name_en=#{nameEN},
        data_type=#{dataType},
        unit=#{unit},
        location=#{location},
        subsystem=#{subSystem},
        parse_script=#{parseScript},
        <if test='signalType=="0"'>
            byte_offset=#{byteOffset},
            bit_offset=#{bitOffset},
            fault_type_key=#{faultTypeKey},
            trigger_value=#{triggerValue},
        </if>
        max_value=#{maxVal},
        min_value=#{minVal},
        remark=#{remark},
        modify_by=#{modifyBy},
        modify_time=CURRENT_TIMESTAMP,
        redis_flag=#{redisFlag}
        WHERE id=#{id}
    </update>
    <update id="updateSignalForImportEkbFaultType">
        UPDATE
            comm_original_signal
        SET fault_type_key = #{faultTypeKey},
            location       = #{location},
            trigger_value  = #{triggerValue}
        WHERE name_en = #{nameEN}
--           AND location IS NULL
          AND trigger_value IS NULL
    </update>

    <insert id="copyOriginalSignal">
        INSERT INTO comm_original_signal(protocol_id,
                                         name_cn,
                                         name_en,
                                         byte_offset,
                                         bit_offset,
                                         data_type,
                                         unit,
                                         location,
                                         subsystem,
                                         parse_script,
                                         fault_type_key,
                                         trigger_value,
                                         max_value,
                                         min_value,
                                         create_by,
                                         create_time,
                                         remark,
                                         redis_flag)
        SELECT #{newProtocolId},
               name_cn,
               name_en,
               byte_offset,
               bit_offset,
               data_type,
               unit,
               location,
               subsystem,
               parse_script,
               fault_type_key,
               trigger_value,
               max_value,
               min_value,
               #{createBy},
               create_time,
               remark,
               redis_flag
        FROM comm_original_signal
        WHERE protocol_id = #{oldProtocolId}
    </insert>
    <insert id="copyExtendSignal">
        INSERT INTO comm_extend_signal(protocol_id,
                                       name_cn,
                                       name_en,
                                       data_type,
                                       unit,
                                       location,
                                       subsystem,
                                       parse_script,
                                       max_value,
                                       min_value,
                                       create_by,
                                       create_time,
                                       remark,
                                       redis_flag)
        SELECT #{newProtocolId},
               name_cn,
               name_en,
               data_type,
               unit,
               location,
               subsystem,
               parse_script,
               max_value,
               min_value,
               #{createBy},
               create_time,
               remark,
               redis_flag
        FROM comm_extend_signal
        WHERE protocol_id = #{oldProtocolId}
    </insert>

    <select id="validateSignal" resultType="cc.crrc.manage.pojo.comm.signal.SignalVO">
        SELECT id, name_en nameEN
        FROM comm_original_signal
        WHERE protocol_id = #{protocolId}
          and fault_type_key is not null
          and fault_type_key!='' and trigger_value is null
    </select>

    <select id="listSignalByVehicleTypeId4Tree" resultType="cc.crrc.manage.pojo.comm.signal.SignalTreeVO">
        SELECT DISTINCT
        cos.id AS id,
        cos.name_cn||'('||cos.name_en||')' AS name,
        cos.location AS location,
        cos.subsystem AS subsystem,
        vcs.label AS subsystemName,
        svts.sort_number AS sortNumber,
        concat(cos.location,'-',cos.subsystem,'-',cos.id) AS nodeId,
        CASE WHEN cos.data_type = 'BOOLEAN1' THEN 1 ELSE 0 END AS digitalFlag
        FROM comm_original_signal cos
        LEFT JOIN comm_protocol cp ON cos.protocol_id = cp.id
        LEFT JOIN v_car_system vcs ON cos.subsystem = vcs.value
        LEFT JOIN stru_vehicle_type_structure svts on cos."location" = svts.name_en AND
        svts.vehicle_type_id=#{vehicleTypeId}
        WHERE cp.vehicle_type_id = #{vehicleTypeId}
        <if test="location != null and location != ''">
            AND cos.location = #{location}
        </if>
        <if test="subsystem != null and subsystem != ''">
            AND cos.subsystem = #{subsystem}
        </if>
        <if test="searchWord != null and searchWord != ''">
            AND (cos.name_cn LIKE '%'||#{searchWord}||'%' OR cos.name_en LIKE '%'||#{searchWord}||'%')
        </if>
        AND cos.result_type!='String'
        AND cp.del_flag = '0'
        AND cos.location is not null
        -- AND sdt.type = 'vehicle_location_shenzhen6'
        ORDER BY svts.sort_number, id DESC
    </select>
    <select id="getRIOMSignals" resultType="cc.crrc.manage.pojo.comm.signal.SignalVO">
        SELECT rb.number,
               t.id,
               t.protocol_id   protocolId,
               t.name_cn       nameCN,
               t.name_en       nameEN,
               t.byte_offset   byteOffset,
               t.bit_offset    bitOffset,
               t.data_type     dataType,
               t.unit,
               t.location,
               t.subsystem,
               t.parse_script  parseScript,
               t.trigger_value triggerValue,
               t.max_value     maxVal,
               t.min_value     minVal,
               t.create_by     createBy,
               t.remark
        FROM riom_board rb
                 left join
             comm_original_signal t on
                 rb.name_en = t.name_en
        WHERE t.package_order = '0'
          AND t.subsystem LIKE '%RIOM%'
          AND rb.name_en LIKE '%' || #{riomName} || '%'
        ORDER BY rb.sort
    </select>
    <select id="selectSignalByNameEn" resultType="java.lang.Integer">
        select count(1)
        from comm_original_signal
        where name_en = #{signalNameEn}
    </select>
    <select id="getNameCnByIds" resultType="java.util.Map">
        select
        name_cn ||'('||name_en||')' AS nameCn,
        id
        from
        comm_original_signal
        where
        id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getSysListForSignalSearch" resultType="java.util.HashMap">
        SELECT DISTINCT
        t1.subsystem,
        t3.label
        FROM
        comm_original_signal t1
        LEFT JOIN comm_protocol t2 ON t1.protocol_id = t2."id"
        LEFT JOIN sys_dict t3 ON t1.subsystem = t3."value"
        AND t2.vehicle_type_id = t3.vehicle_type_id
        AND t3.type_code = 'ass_car_system'
        LEFT JOIN mtr_vehicle_type_relation t4 ON t4.vehicle_type_id = t3.vehicle_type_id
        LEFT JOIN mtr_vehicle t5 ON t4.vehicle_id = t5.ID
        WHERE
        t5.vehicle_code = #{vehicleCode}
        AND t1.LOCATION IN
        <foreach collection="cars" item="car" index="index" open="(" close=")" separator=",">
            #{car}
        </foreach>
        ORDER BY
        t1.subsystem
    </select>

    <select id="signalListBySysAndLocation" resultType="cc.crrc.manage.pojo.comm.signal.SignalVO">
        SELECT DISTINCT
        t1.id,
        t3.label||'-'||t1.location||'-'||t1.name_cn||'('||t1.name_en||')' AS nameCn,
        t1.name_en AS nameEn,
        concat(t1.location,'-',t1.subsystem,'-',t1.id) AS nodeId,
        t1.subsystem,
        CASE WHEN t1.data_type = 'BOOLEAN1' THEN 1 ELSE 0 END AS digitalFlag,
        t1.location
        FROM
        comm_original_signal t1
        LEFT JOIN comm_protocol t2 ON t1.protocol_id = t2."id"
        LEFT JOIN sys_dict t3 ON t1.subsystem = t3."value"
        AND t2.vehicle_type_id = t3.vehicle_type_id
        AND t3.type_code = 'ass_car_system'
        LEFT JOIN mtr_vehicle_type_relation t4 ON t4.vehicle_type_id = t3.vehicle_type_id
        LEFT JOIN mtr_vehicle t5 ON t4.vehicle_id = t5.ID
        WHERE
        t5.vehicle_code = #{vehicleCode}
        AND t1.LOCATION IN
        <foreach collection="cars" item="car" index="index" open="(" close=")" separator=",">
            #{car}
        </foreach>
        <if test="inputName != null and inputName != ''">
            AND (t1.name_cn LIKE '%'||#{inputName}||'%'
            OR t1.name_en LIKE '%'||#{inputName}||'%')
        </if>
        <if test="subsystem != null and subsystem != ''">
            AND t1.subsystem=#{subsystem}
        </if>
        ORDER BY
        nameCn
    </select>
    <select id="structureList" resultType="java.lang.String">
        SELECT split_part(T.structure_position, '/', 2)
        FROM stru_vehicle_structure_component T
                 LEFT JOIN mtr_vehicle t1 ON t1.id = T.vehicle_id
        WHERE t1.vehicle_code = #{vehicleCode}
          AND T.structure_type = '车厢'
          AND T.structure_position !~ '[\u2e80-\ua4cf]|[\uf900-\ufaff]|[\ufe30-\ufe4f]'
          AND T.del_flag = 0
        ORDER BY
            T.sort_number
    </select>
    <select id="selectProtocolIdByVehicleCode" resultType="java.lang.String">
        SELECT t1.protocol_id AS protocolId
        FROM comm_vehicle_tcp_protocol t1
                 INNER JOIN mtr_vehicle t2 ON t1.vehicle_id = t2.ID
            AND t2.del_flag = 0
                 JOIN comm_protocol t3 ON t3."id" = t1.protocol_id
            AND t3.del_flag = 0
            AND t3."enable" = 1
        WHERE t2.vehicle_code = #{vehicleCode}
          AND t1.VALID = 1
    </select>
    <select id="findLikeWtdSignalByInputName"
            resultType="cc.crrc.manage.monitoringConfig.entity.MonitorConfigWtdSignalPO">
        SELECT
        nws.id AS id,
        nws.protocol_id AS protocolId,
        t3.label||'-'||nws.location||'-'||nws.name_cn||'('||nws.name_en||')' AS nameCn,
        nws.name_en AS nameEn,
        nws.byte_offset AS byteOffset,
        nws.bit_offset AS bitOffset,
        nws.data_type AS dataType,
        nws.unit AS unit,
        nws.location AS carLocation,
        nws.subSystem AS subSystem,
        nws.parse_script AS parseFunction,
        nws.fault_type_key AS triggerFaultId,
        nws.trigger_value AS triggerValue,
        nws.max_value AS maxValue,
        nws.min_value AS minValue,
        nws.modify_time AS modifyTime,
        nws.remark AS remark,
        nws.redis_flag AS redisFlag,
        nws.frames_type AS framesType,
        nws.package_order AS packageOrder,
        nws.result_type AS resultType
        FROM
        comm_original_signal nws
        LEFT JOIN comm_protocol t2 ON nws.protocol_id = t2."id"
        LEFT JOIN sys_dict t3 ON nws.subsystem = t3."value"
        AND t2.vehicle_type_id = t3.vehicle_type_id
        AND t3.type_code = 'ass_car_system'
        WHERE
        nws.protocol_id = #{protocolId}
        <if test="inputName != null and inputName != ''">
            AND (nws.name_cn LIKE '%'|| #{inputName} ||'%'
            OR nws.name_en LIKE '%'|| #{inputName} ||'%')
        </if>
        and nws.redis_flag = '1'
        ORDER BY
        nws.name_cn,nws.modify_time DESC
        LIMIT 20
    </select>
</mapper>