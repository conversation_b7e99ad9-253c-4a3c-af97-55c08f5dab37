<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.comm.ProtocolMapping">
    <select id="list" resultType="cc.crrc.manage.pojo.comm.protocol.ProtocolVO">
        SELECT
        id,
        vehicle_type_id vehicleTypeId,
        vehicle_type_name vehicleTypeName,
        <!--  comm_type,
          packet_type_id packetTypeId,
          packet_type_name packetTypeName,-->
        name,
        version,
        size,
        case endian when 'BigEndian' then '大开端' when '<PERSON><PERSON><PERSON><PERSON>' then '小开端' end endian,
        enable,
        create_by createBy,
        line_id lineId,
        vehicle_count vehicleCount
        FROM
        v_protocol_vehicletype_packet
        WHERE 1=1
        and enable = 1
        <if test="vehicleTypeId!=null">
            and vehicle_type_id=#{vehicleTypeId}
        </if>
        order by create_time desc
    </select>

    <!-- 房明宽 查询条件 增加lineId -->
    <select id="getProtocolList" resultType="cc.crrc.manage.pojo.comm.protocol.ProtocolVO">
        SELECT
        v_protocol_vehicletype_packet.ID,
        v_protocol_vehicletype_packet.vehicle_type_id vehicleTypeId,
        v_protocol_vehicletype_packet.vehicle_type_name vehicleTypeName,
        <!-- v_protocol_vehicletype_packet.comm_type,
         v_protocol_vehicletype_packet.packet_type_id packetTypeId,
         v_protocol_vehicletype_packet.packet_type_name packetTypeName,-->
        v_protocol_vehicletype_packet.NAME,
        v_protocol_vehicletype_packet.VERSION,
        v_protocol_vehicletype_packet.SIZE,
        CASE endian WHEN 'BigEndian' THEN '大开端' WHEN 'LittleEndian' THEN '小开端' END endian,
        v_protocol_vehicletype_packet.ENABLE,
        v_protocol_vehicletype_packet.create_by createBy,
        v_protocol_vehicletype_packet.vehicle_count vehicleCount,
        v_protocol_vehicletype_packet.line_id AS lineId,
        mtr_line.name AS lineName
        FROM
        v_protocol_vehicletype_packet
        <!-- LEFT JOIN mtr_vehicle_type ON v_protocol_vehicletype_packet.vehicle_type_id = mtr_vehicle_type.id-->
        LEFT JOIN mtr_line ON v_protocol_vehicletype_packet.line_id = mtr_line.ID
        WHERE
        1 = 1
        <if test="vehicleTypeId != null and vehicleTypeId !=''">
            and v_protocol_vehicletype_packet.vehicle_type_id = #{vehicleTypeId}
        </if>
        <!-- 查询条件增加 线路ID -->
        <if test="lineId != null and lineId !=''">
            and v_protocol_vehicletype_packet.line_id = #{lineId}
        </if>
        ORDER BY
        v_protocol_vehicletype_packet.create_time DESC
    </select>

    <insert id="saveProtocol">
        INSERT INTO comm_protocol(id,
                                  vehicle_type_id,
                                  name,
                                  version,
                                  size,
                                  endian,
                                  enable,
                                  del_flag,
                                  create_by,
                                  create_time,
                                  remark,
                                  line_id)
        values (#{id},
                #{vehicleTypeId},
                #{name},
                #{version},
                #{size},
                #{endian},
                0,
                0,
                #{createBy},
                current_timestamp,
                #{remark},
                #{lineId})
    </insert>

    <select id="queryDetail" resultType="cc.crrc.manage.pojo.comm.protocol.ProtocolVO">
        SELECT id,
               vehicle_type_id vehicleTypeId,
               name,
               version,
            size,
            endian,
            enable
        FROM v_protocol_vehicletype_packet
        WHERE id=#{id}
    </select>

    <insert id="insertTcpProtocolPacketRelation">
        INSERT INTO comm_vehicle_tcp_protocol(vehicle_id,
                                              protocol_id,
                                              valid,
                                              create_by,
                                              create_time)
        SELECT vehicle_id,
               #{protocolId},
               1,
               #{createBy},
               CURRENT_TIMESTAMP
        FROM mtr_vehicle_type_relation
        WHERE vehicle_type_id = #{vehicleTypeId}
          and valid = 1
    </insert>
    <update id="deleteProtocol">
        UPDATE
            comm_protocol
        SET del_flag=1
        WHERE id = #{protocolId}
          AND del_flag = 0
    </update>

    <select id="queryProtocolByTcp" resultType="int">
        SELECT count(1)
        FROM comm_vehicle_tcp_protocol
        WHERE protocol_id = #{protocolId}
          AND valid = 1
    </select>

    <select id="queryProtocolByMqtt" resultType="int">
        SELECT count(1)
        FROM comm_mqtt_protocol
        WHERE protocol_id = #{protocolId}
          AND valid = 1
    </select>

    <update id="updateStatus">
        UPDATE
            comm_protocol
        SET enable=#{status},
            modify_by=#{modifyBy},
            modify_time=CURRENT_TIMESTAMP
        WHERE id = #{protocolId}
          AND del_flag = 0
    </update>

    <insert id="upgradeProtocol">
        <selectKey resultType="long" keyProperty="id" order="AFTER">
            SELECT currval('comm_protocol_id_seq'::regclass) AS id
        </selectKey>
        INSERT INTO comm_protocol(
        vehicle_type_id,
        name,
        version,
        size,
        endian,
        enable,
        del_flag,
        create_by,
        create_time,
        remark,
        tcp_packet_type_id,
        mqtt_topic_type_id)
        SELECT
        vehicle_type_id,
        name,
        #{version},
        size,
        endian,
        0,
        0,
        #{createBy},
        CURRENT_TIMESTAMP,
        remark,
        tcp_packet_type_id,
        mqtt_topic_type_id
        FROM comm_protocol
        WHERE id=#{protocolId} AND del_flag=0
    </insert>
    <select id="queryMatchVehicle" resultType="cc.crrc.manage.pojo.comm.protocol.ProtocolMatchVO">
        SELECT tmp.vehicleId, tmp.checked, mv.name_cn vehicleName
        FROM mtr_vehicle mv,
             (SELECT vtr.vehicle_id          vehicleId,
                     coalesce(cp."valid", 0) checked
              FROM mtr_vehicle_type_relation vtr
                       LEFT JOIN
                   comm_vehicle_tcp_protocol cp
                   ON vtr.vehicle_id = cp.vehicle_id
                       AND cp."valid" = 1
                       AND cp.protocol_id = #{protocolId}
              WHERE vtr.vehicle_type_id = #{vehicleTypeId}
                AND vtr."valid" = 1) tmp
        where mv.id = tmp.vehicleId
          AND mv.del_flag = 0
    </select>
    <update id="invalidVehicleProtocol">
        UPDATE
        comm_vehicle_tcp_protocol
        SET valid=0,
        modify_by=#{modifyBy},
        modify_time=CURRENT_TIMESTAMP
        WHERE
        protocol_id=#{protocolId}
        AND valid=1
        AND vehicle_id IN
        <foreach collection="invaidVehicleList" open="(" close=")" separator="," item="item">
            #{item.vehicleId}
        </foreach>
    </update>

    <insert id="matchTcpVehicle">
        INSERT INTO comm_vehicle_tcp_protocol(
        vehicle_id,
        protocol_id,
        valid,
        create_by,
        create_time
        )
        VALUES
        <foreach collection="matchList" separator="," item="item">
            (#{item.vehicleId},#{protocolId},1,#{createBy},CURRENT_TIMESTAMP)
        </foreach>
    </insert>


    <update id="updateInvaidByPacket">
        UPDATE
        comm_vehicle_tcp_protocol
        SET valid=0,
        modify_by=#{modifyBy},
        modify_time=CURRENT_TIMESTAMP
        WHERE
        valid=1
        AND vehicle_id in
        <foreach collection="matchList" open="(" separator="," close=")" item="item">
            #{item.vehicleId}
        </foreach>
    </update>

    <select id="getLatestProtocolByVehicleType" resultType="cc.crrc.manage.pojo.comm.protocol.ProtocolVO">
        SELECT *
        FROM (
                 SELECT cp.vehicle_type_id vehicleTypeId,
                        cp.name,
                        cp.version,
                        cp.size,
                        cp.endian,
                        cp.enable,
                        cp.id,
                        ROW_NUMBER()       OVER (PARTITION by cp.name order by cp.create_time desc) AS rowNumber
                 FROM comm_protocol cp
                          LEFT JOIN mtr_vehicle_type mvt ON mvt.id = cp.vehicle_type_id
                 WHERE cp.vehicle_type_id = #{vehicleTypeId}
                   AND cp.del_flag = 0
             ) a
        WHERE a.rowNumber = 1
    </select>

    <select id="getValidProtocolByVehicleId" resultType="cc.crrc.manage.pojo.comm.protocol.ProtocolVO">
        SELECT vehicle_type_id vehicleTypeId,
               name,
               version,
            size,
            endian,
            enable,
            id
        FROM comm_protocol cp
        WHERE id IN (
            SELECT protocol_id AS id FROM comm_vehicle_tcp_protocol
            WHERE vehicle_id = #{vehicleId}
          AND valid =1
            )
          AND del_flag = 0
    </select>
    <select id="selectEnableProNum" resultType="integer">
        SELECT
            COUNT( * )
        FROM
            comm_protocol
        WHERE
                vehicle_type_id = ( SELECT vehicle_type_id FROM comm_protocol WHERE ID = #{protocolId} )
          AND ENABLE = 1
    </select>

</mapper>