<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.comm.CommOriginalSignalMapping">
    <select id="listOriginalSignalByVehicleTypeId" resultType="cc.crrc.manage.pojo.comm.signal.CommSignalVO">
        SELECT DISTINCT
        cos.id,
        cos.protocol_id protocolId,
        cp.name protocolName,
        cos.name_cn nameCN,
        cos.name_en nameEN,
        cos.data_type dataType,
        cos.unit,
        cos.location,
        cos.subsystem,
        vcs.label subsystemName
        FROM comm_original_signal cos
        LEFT JOIN comm_protocol cp ON cos.protocol_id = cp.id
        LEFT JOIN v_car_system vcs ON cos.subsystem = vcs.value
        WHERE cp.vehicle_type_id = #{vehicleTypeId}
        <if test="location != null and location != ''">
            AND cos.location = #{location}
        </if>
        <if test="subsystem != null and subsystem != ''">
            AND cos.subsystem = #{subsystem}
        </if>
        AND cp.del_flag = '0'
    </select>
    <select id="getSignalInterval" resultType="cc.crrc.manage.pojo.comm.signal.CommSignalIntervalPO">
        SELECT
            train_code trainCode,
            day,
            EXTRACT ( epoch FROM CAST ( start_time AS TIMESTAMPTZ ) )*1000 startTime,
            EXTRACT ( epoch FROM CAST ( coalesce (end_time,now()) AS TIMESTAMPTZ ) )*1000 endTime,
            EXTRACT ( epoch FROM CAST ( create_time AS TIMESTAMPTZ ) )*1000 createTime
        FROM
            comm_signal_interval
        WHERE
            train_code = #{vehicleCode}
          AND EXTRACT ( epoch FROM CAST ( start_time AS TIMESTAMPTZ ) ) <![CDATA[<=]]> #{endTime}/1000
          AND EXTRACT ( epoch FROM CAST ( coalesce (end_time,now())  AS TIMESTAMPTZ ) ) >= #{startTime}/1000
        ORDER BY startTime ASC , endTime ASC
    </select>

</mapper>