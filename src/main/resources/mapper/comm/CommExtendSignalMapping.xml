<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.comm.CommExtendSignalMapping">
    <select id="listExtendSignalByVehicleTypeId" resultType="cc.crrc.manage.pojo.comm.signal.CommSignalVO">
        SELECT DISTINCT
            cos.id,
            cos.protocol_id protocolId,
            cp.name protocolName,
            cos.name_cn nameCN,
            cos.name_en nameEN,
            cos.data_type dataType,
            cos.unit,
            cos.location,
            cos.subsystem,
            vcs.label subsystemName
        FROM comm_extend_signal cos
        LEFT JOIN comm_protocol cp ON cos.protocol_id = cp.id
        LEFT JOIN v_car_system vcs ON cos.subsystem = vcs.value
        WHERE cp.vehicle_type_id = #{vehicleTypeId}
        AND cp.del_flag = '0'
    </select>

</mapper>