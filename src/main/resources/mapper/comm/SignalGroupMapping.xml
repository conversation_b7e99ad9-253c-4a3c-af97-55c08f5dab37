<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.comm.SignalGroupMapping">
    <select id="list" resultType="cc.crrc.manage.pojo.comm.signalgroup.SignalGroupVO">
        SELECT
        csg.ID,
        csg.NAME,
        csg.vehicle_type_id vehicleTypeId,
        mvt."name" vehicleTypeName,
        csg.create_by,
        csg.remark,
        ml.id as lineId,
        ml.name as lineName
        FROM
        comm_signal_group csg,
        mtr_vehicle_type mvt
        left JOIN mtr_line ml on ml.id=mvt.line_id
        WHERE
        csg.vehicle_type_id = mvt."id"
        AND mvt.del_flag =0
        <if test="vehicleTypeId!=null and vehicleTypeId !=''">
            and csg.vehicle_type_id=#{vehicleTypeId}
        </if>
        <if test="lineId!=null and lineId !=''">
            and ml.id=#{lineId}
        </if>
        <if test="name!=null and name!=''">
            <bind name="pattern" value="'%'+_parameter.name + '%'"/>
            and csg.name like #{pattern}
        </if>
        order by csg.create_time desc
    </select>

    <insert id="saveSignalGroup">
        INSERT INTO comm_signal_group(
        id,
        vehicle_type_id,
        name,
        remark,
        create_by,
        create_time
        )
        VALUES(
        #{id},
        #{vehicleTypeId},
        #{name},
        #{remark},
        #{createBy},
        CURRENT_TIMESTAMP
        )
    </insert>
    <update id="updateSignalGroup">
        UPDATE comm_signal_group SET name=#{name},remark=#{remark},modify_by=#{modifyBy},modify_time=CURRENT_TIMESTAMP
        WHERE id=#{id}
    </update>
    <delete id="deleteSignalGroup">
        DELETE FROM comm_signal_group WHERE id=#{id}
    </delete>
    <delete id="deleteSignalGroupOriginal">
        DELETE FROM comm_original_signal_group WHERE group_id=#{groupId}
    </delete>
    <delete id="deleteSignalGroupExtend">
        DELETE FROM comm_extend_signal_group WHERE group_id=#{groupId}
    </delete>

    <select id="listSignal" resultType="cc.crrc.manage.pojo.comm.signal.SignalVO">
        SELECT
        protocol_id protocolId,
        name_cn nameCN,
        name_en nameEN,
        byte_offset byteOffset,
        bit_offset bitOffset,
        data_type dataTType,
        unit,
        location,
        sd.label subsystem,
        parse_script parseScript,
        trigger_value triggerValue,
        max_value maxVal,
        min_value minVal,
        vcs.create_by createBy,
        remark,
        vcs.vehicle_type_id vehicleTypeId,
        fault_code faultCode,
        fault_name_cn faultNameCN,
        fault_name_en faultNameEN
        FROM
        v_comm_signal vcs
        Left Join sys_dict sd on sd.value = vcs.subsystem and sd.vehicle_type_id = vcs.vehicle_type_id
        <!--Left Join sys_dict_type sdt on sd.type_id = sdt.id-->
        <if test="signalType==0">
            ,comm_original_signal_group sgr
        </if>
        <if test="signalType==1">
            ,comm_extend_signal_group sgr
        </if>
        WHERE
        vcs.protocol_id IN (select id from (select id,ROW_NUMBER() OVER(PARTITION by name order by create_time desc)
        rowNumber from comm_protocol where vehicle_type_id=#{vehicleTypeId} and del_flag=0) a where a.rowNumber=1)
        and vcs.signal_type=#{signalType}
        <if test="signalType==0">
            and vcs.name_en=sgr.original_signal_name_en
        </if>
        <if test="signalType==1">
            and vcs.name_en=sgr.extend_signal_name_en
        </if>
        <if test="name!=null and name!=''">
            <bind name="pattern" value="'%'+_parameter.name + '%'"/>
            and (vcs.name_cn like #{pattern} or vcs.name_en like #{pattern})
        </if>
        and sgr.group_id=#{groupId}
        <!--and sdt.type = 'ass_car_system'-->
        and sd.type_code = 'ass_car_system'
        <if test="subSystem!=null and subSystem!=''">
            and vcs.subsystem=#{subSystem}
        </if>
        <if test="location!=null and location!=''">
            and vcs.location=#{location}
        </if>
        order by vcs.name_en asc
    </select>

    <select id="listAllSignal" resultType="cc.crrc.manage.pojo.comm.signal.SignalVO">
        SELECT
        protocol_id protocolId,
        name_cn nameCN,
        name_en nameEN,
        byte_offset byteOffset,
        bit_offset bitOffset,
        data_type dataTType,
        unit,
        location,
        sd.label subsystem,
        parse_script parseScript,
        trigger_value triggerValue,
        max_value maxVal,
        min_value minVal,
        vcs.create_by createBy,
        remark,
        vcs.vehicle_type_id vehicleTypeId,
        fault_code faultCode,
        fault_name_cn faultNameCN,
        fault_name_en faultNameEN
        FROM
        v_comm_signal vcs
        Left Join sys_dict sd on sd.value = vcs.subsystem and sd.vehicle_type_id = vcs.vehicle_type_id
        <!--Left Join sys_dict_type sdt on sd.type_id = sdt.id-->
        WHERE
        vcs.protocol_id IN (select id from (select id,ROW_NUMBER() OVER(PARTITION by name order by create_time desc)
        rowNumber from comm_protocol where vehicle_type_id=#{vehicleTypeId} and del_flag=0) a where a.rowNumber=1)
        and vcs.signal_type=#{signalType}
        <!--and sdt.type = 'ass_car_system'-->
        and sd.type_code = 'ass_car_system'
        <if test="signalType==0">
            and vcs.name_en NOT IN (SELECT original_signal_name_en FROM comm_original_signal_group WHERE
            group_id=#{groupId} )
        </if>
        <if test="signalType==1">
            and vcs.name_en NOT IN (SELECT extend_signal_name_en FROM comm_extend_signal_group WHERE
            group_id=#{groupId} )
        </if>
        <if test="name!=null and name!=''">
            <bind name="pattern" value="'%'+_parameter.name + '%'"/>
            and (vcs.name_cn like #{pattern} or vcs.name_en like #{pattern})
        </if>
        <if test="subSystem!=null and subSystem!=''">
            and vcs.subsystem=#{subSystem}
        </if>
        <if test="location!=null and location!=''">
            and vcs.location=#{location}
        </if>
        order by vcs.name_en asc
    </select>
    <insert id="saveSignalGroupRelation">
        INSERT INTO
        <if test="signalType==0">
            comm_original_signal_group
        </if>
        <if test="signalType==1">
            comm_extend_signal_group
        </if>
        (
        group_id,
        <if test="signalType==0">
            original_signal_name_en
        </if>
        <if test="signalType==1">
            extend_signal_name_en
        </if>
        )
        VALUES
        <foreach collection="signalENNames" separator="," item="item">
            (#{groupId},#{item})
        </foreach>
    </insert>

    <delete id="deleteRelation">
        DELETE FROM
        <if test="signalType==0">
            comm_original_signal_group
        </if>
        <if test="signalType==1">
            comm_extend_signal_group
        </if>
        WHERE group_id=#{groupId}
        <if test="signalType==0">
            AND original_signal_name_en IN
        </if>
        <if test="signalType==1">
            AND extend_signal_name_en IN
        </if>
        <foreach collection="signalENNames" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
    </delete>

    <select id="listFault" resultType="cc.crrc.manage.pojo.comm.signalgroup.SignalGroupFaultVO">
        select
        t1.fault_code faultCode,
        t1.name_cn faultTypeNameCn,
        t1.fault_level faultLevel,
        t1.location,
        t1.subsystem,
        t1.vehicle_structure_code vehicleStructureCode,
        t1.fault_type_key faultTypeKey,
        t1.vehicle_type_id vehicleTypeId,
        t2.signal_group_id signalGroupId
        from
        ekb_fault_type t1 left join
        ekb_fault_type_signal_group t2
        on t1.fault_type_key = t2.fault_type_key
        and t2.signal_group_id = #{signalGroupId}
        where
        del_flag = 0
        <if test="vehicleTypeId != null and vehicleTypeId !=''">
            and vehicle_type_id = #{vehicleTypeId}
        </if>
        <if test="location != null and location!=''">
            and location = #{location}
        </if>
        <if test="subsystem != null and subsystem!=''">
            and subsystem = #{subsystem}
        </if>
        <if test="faultCode!=null and faultCode!=''">
            and fault_Code=#{faultCode}
        </if>
        <if test="faultTypeName!=null and faultTypeName!=''">
            and (name_cn LIKE '%'||#{faultTypeName}||'%'
            or name_en LIKE '%'||#{faultTypeName}||'%')
        </if>



    </select>


    <select id="deleteRelatedBeforeInsert">
        delete from
        ekb_fault_type_signal_group t2
        WHERE
        t2.signal_group_id = #{signalGroupId}
    </select>
    <select id="listRelated" resultType="cc.crrc.manage.pojo.comm.signalgroup.SignalGroupFaultVO">
        select
        t1.fault_code faultCode,
        t1.name_cn faultTypeNameCn,
        t1.fault_level faultLevel,
        t1.location,
        t1.subsystem,
        t1.vehicle_structure_code vehicleStructureCode,
        t1.fault_type_key faultTypeKey,
        t1.vehicle_type_id vehicleTypeId,
        t2.signal_group_id signalGroupId
        from
        ekb_fault_type t1 inner join
        ekb_fault_type_signal_group t2
        on t1.fault_type_key = t2.fault_type_key
        and t2.signal_group_id = #{signalGroupId}
		where t1.del_flag = 0
    </select>

    <insert id="insert">
        insert into ekb_fault_type_signal_group(signal_group_id, fault_type_key) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{signalGroupId},#{item})
        </foreach>

    </insert>

</mapper>