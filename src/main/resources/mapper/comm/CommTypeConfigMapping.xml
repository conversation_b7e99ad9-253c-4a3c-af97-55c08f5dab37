<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.comm.CommTypeConfigMapping">
  	<select id="getTcpPacketType" resultType="cc.crrc.manage.pojo.comm.protocolTypeConfig.TcpPacketTypeDTO">
  		SELECT
			id,
			vehicle_type_id as vehicleTypeId,
			name,
			alias,
			valid_script as validScript
		FROM
			comm_tcp_packet_type
		WHERE
			vehicle_type_id = #{vehicleTypeId}
  	</select>
  	
  	<select id="getMqttTopicType" resultType="cc.crrc.manage.pojo.comm.protocolTypeConfig.MqttTopicTypeDTO">
  		SELECT
			id,
			vehicle_type_id as vehicleTypeId,
			name,
			alias,
			type,
			qos,
			remark
		FROM
			comm_mqtt_topic_type
		WHERE
			vehicle_type_id = #{vehicleTypeId}
  	</select>
  	
  	<insert id="addTcpPacketType">
  		INSERT INTO comm_tcp_packet_type
  		(
  			id,
  			vehicle_type_id,
  			name,
			alias,
			valid_script,
			create_by,
			create_time
  		)VALUES(
  			#{id},
  			#{vehicleTypeId},
  			#{name},
  			#{alias},
  			#{validScript},
  			#{createBy},
  			CURRENT_TIMESTAMP
  		)
  	</insert>

  	<insert id="addMqttTopicType">
  		INSERT INTO comm_mqtt_topic_type
  		(
  			id,
  			vehicle_type_id,
  			name,
			alias,
			type,
			qos,
			remark,
			create_by,
			create_time
  		)VALUES(
  			#{id},
  			#{vehicleTypeId},
  			#{name},
  			#{alias},
  			#{type},
  			#{qos},
  			#{remark},
  			#{createBy},
  			CURRENT_TIMESTAMP
  		)
		  		
  	</insert>
  	
  	<update id="updateTcpPacketType">
  		UPDATE comm_tcp_packet_type SET
  			vehicle_type_id = #{vehicleTypeId},
  			name = #{name},
			alias = #{alias},
			valid_script = #{validScript},
			modify_by = #{modifyBy},
			modify_time = CURRENT_TIMESTAMP
		WHERE
			id = #{id}
  	</update>
  	
  	<update id="updateMqttTopicType">
  		UPDATE comm_mqtt_topic_type SET
  			vehicle_type_id = #{vehicleTypeId},
  			name = #{name},
			alias = #{alias},
			type = #{type},
			qos = #{qos},
			remark = #{remark},
			modify_by = #{modifyBy},
			modify_time = CURRENT_TIMESTAMP
		WHERE
			id = #{id}
  	</update>
  	
  	<delete id="delTcpPacketType">
  		DELETE FROM comm_tcp_packet_type
  		WHERE id = #{id}
  	</delete>
  	
  	<delete id="delMqttTopicType">
  		DELETE FROM comm_mqtt_topic_type
  		WHERE id = #{id}
  	</delete>
</mapper>