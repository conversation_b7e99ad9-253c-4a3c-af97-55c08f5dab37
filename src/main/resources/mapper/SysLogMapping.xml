<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.SysLogMapping">

    <insert id="insertLog">
        insert into sys_log
        (
        id,
        opt,
        uri,
        user_id,
        date,
        name,
        host,
        status,
        user_name,
        params,
        user_agent,
        exception,
        opt_type,
        response_time,
        menu_path
        )
        values
        (#{id},
        #{opt},
        #{uri},
        #{userId},
        #{requestTime},
        #{name},
        #{ip},
        #{status},
        #{userName},
        #{params},
        #{userAgent},
        #{exception},
        #{optType},
        #{responseTime},
        #{menuPath}
        )
    </insert>
    <select id="getMenuByPath" resultType="cc.crrc.manage.pojo.SysLogDTO">
        SELECT
        title as menu,
        id as menuId,
        code as menuCode
        FROM
        sys_menu
        where
        del_flag='0'
        <if test="path != null and path != ''">
            and path = #{path}
        </if>

    </select>

    <select id="getLogs" resultType="cc.crrc.manage.pojo.SysLogDTO">
    SELECT
        opt,
        uri,
        date,
        name,
        host as ip,
        status,
        user_name as userName,
        user_agent as userAgent
        FROM
        sys_log
        WHERE
        $DATASCOPE$
        <if test="uri != null and uri != ''">
            and uri like '%\'||#{uri}||'%'
        </if>
        <if test="name != null and name != ''">
            and name like '%\'||#{name}||'%'
        </if>
        <if test="startTime != null and startTime != ''">
            and date &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and #{endTime} >= date
        </if>
        ORDER BY date DESC
    </select>
    <select id="getOptCount" resultType="java.util.Map">
        SELECT
        SUM(CASE WHEN opt like'增加%' THEN 1 ELSE 0 END ) as "insert",
        SUM(CASE WHEN opt like'修改%' THEN 1 ELSE 0 END ) as "update",
        SUM(CASE WHEN opt like'删除%' THEN 1 ELSE 0 END ) as "delete",
        SUM(CASE WHEN opt not like ALL (ARRAY['增加%','删除%','修改%','查询%','%登录%']) THEN 1 ELSE 0 END ) as "others"
        FROM sys_log
        WHERE
        $DATASCOPE$
        <if test="startTime != null and startTime != ''">
            and date &gt; #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and date &lt; #{endTime}
        </if>
    </select>
    <select id="getMenuCount" resultType="cc.crrc.manage.pojo.SysLogDTO">
        SELECT a.menu,a.menuCode,count(1)
        FROM
        (SELECT
        m.title as menu,
        m.code as menuCode
        FROM
        sys_log l
        JOIN
        sys_menu m ON m.path = l.menu_path
        WHERE
        $DATASCOPE$
        <if test="startTime != null and startTime != ''">
            and l.date &gt; #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and l.date &lt; #{endTime}
        </if>) a
        GROUP BY a.menu,a.menuCode
    </select>
    <select id="getAccessCount" resultType="java.util.Map">
        SELECT COUNT( 1 )
        FROM
        sys_log
        WHERE
        $DATASCOPE$
        AND opt = '用户登录'
        <if test="startTime != null and startTime != ''">
            and date &gt; #{startTime}
        </if>
    </select>
    <select id="getAgent" resultType="cc.crrc.manage.pojo.SysLogDTO">
        SELECT
        user_agent as userAgent,
        count(1) as count
        FROM
        sys_log
        WHERE
        $DATASCOPE$
        <if test="startTime != null and startTime != ''">
            and date &gt; #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and date &lt; #{endTime}
        </if>
        GROUP BY
        user_agent
    </select>
    <select id="getVisitors" resultType="cc.crrc.manage.pojo.SysLogDTO">
        SELECT
        user_agent as userAgent,
        host as ip,
        date
        FROM
        sys_log
        WHERE
        $DATASCOPE$
        <if test="startTime != null and startTime != ''">
            and date &gt; #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and date &lt; #{endTime}
        </if>
        ORDER BY date DESC
    </select>
</mapper>