<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.component.ComponentMapping">

    <sql id="ComponentAlias">
        c.id,
        c.component_type_id as componentTypeId,
        c.serial_number as serialNumber,
        c.production_date as productionDate,
        c.del_flag AS delFlag,
        c.remark
    </sql>

    <select id="getComponents" resultType="cc.crrc.manage.pojo.component.ComponentDTO">
        SELECT
            <include refid="ComponentAlias"/>,
            ct.name_cn AS componentTypeName,
            VT.ID AS vehicleTypeId,
            VT.NAME AS vehicleTypeName,
            L.id as lineId,
            L.name AS lineName
        FROM
            stru_component C
            JOIN stru_component_type ct ON ct.ID = C.component_type_id
            LEFT JOIN mtr_vehicle_type VT ON ct.vehicle_type_id = VT.ID
            left join mtr_line L on VT.line_id = L.ID
        WHERE
            ct.del_flag = 0
            AND c.del_flag = 0
            <if test="componentTypeId != null and componentTypeId !=''">
                AND c.component_type_id = #{componentTypeId}
            </if>
            <if test="serialNumber != null and serialNumber != ''">
                AND c.serial_number LIKE '%'||#{serialNumber}||'%'
            </if>
            <!-- 增加筛选条件：车型 -->
            <if test="vehicleTypeId != null and vehicleTypeId != ''">
                and VT.id = #{vehicleTypeId}
            </if>
            <!-- 增加筛选条件：线路 -->
            <if test="lineId != null and lineId != ''">
                and L.id = #{lineId}
            </if>
        ORDER BY coalesce(c.modify_time, c.create_time) DESC NULLS LAST
    </select>

    <select id="getComponentDetailById" resultType="cc.crrc.manage.pojo.component.ComponentDetailVO">
        SELECT
            <include refid="ComponentAlias"/>,
            ct.name_cn as componentTypeName,
            ct.name_en as componentTypeNameEn,
            ct.product_number as productNumber,
            ct.catalog as catalog,
            s.vehicle_id        AS vehicleId,
            s.name              AS softwareName,
            s.version           AS softwareVersion,
            s.update_time       AS softwareUpdateTime,
            <if test="fileType != null and fileType != ''">
                file.id             AS fileId,
                file.url            AS pictureURL,
            </if>
            mv.name_cn          AS nameCn
        FROM stru_component c
        LEFT JOIN stru_component_type ct ON ct.id = c.component_type_id
        LEFT JOIN stru_vehicle_structure_component svsc ON svsc.component_id = c.id
        LEFT JOIN mtr_vehicle mv ON mv.id = svsc.vehicle_id
        LEFT JOIN
            (SELECT component_id, vehicle_id, name, version,update_time
            FROM mtr_component_software mcs
            LEFT JOIN mtr_software ms ON ms.id = mcs.software_id
            WHERE mcs.valid = 't'
--             AND mcs.update_time IS NOT NULL
            ) s ON s.component_id = c.id AND svsc.vehicle_id = s.vehicle_id
        <if test="fileType != null and fileType != ''">
            LEFT JOIN
                (SELECT id,url,component_type_id
                FROM stru_component_type_file sctf
                LEFT JOIN sys_file sf ON sf.id = sctf.file_id
                WHERE sf.type = #{fileType}
                AND sf.del_flag = 0
                ) file ON file.component_type_id = ct.id
        </if>
        WHERE ct.del_flag = 0
            AND c.del_flag = 0
            AND c.id = #{id}
        ORDER BY coalesce(c.modify_time, c.create_time) DESC NULLS LAST
        LIMIT 1
    </select>

    <insert id="insertComponents">
        INSERT INTO stru_component
        (
            id,
            component_type_id,
            serial_number,
            production_date,
            remark,
            create_by,
            create_time
        )VALUES(
            #{id},
            #{componentTypeId},
            #{serialNumber},
            #{productionDate},
            #{remark},
            #{createBy},
            CURRENT_TIMESTAMP
        )
    </insert>

    <update id="updateComponents">
        UPDATE stru_component SET
            component_type_id = #{componentTypeId},
            serial_number = #{serialNumber},
            production_date = #{productionDate},
            remark = #{remark},
            modify_by = #{modifyBy},
            modify_time = CURRENT_TIMESTAMP
        WHERE
            id = #{id}
    </update>

    <update id="delComponents">
        UPDATE stru_component SET
            del_flag = 1
        WHERE
            id = #{id}
    </update>

    <insert id="saveFaultTypeFile">
        INSERT INTO stru_component_file
        (
            component_id,
            file_id,
            start_date
        )VALUES(
            #{id},
            #{fileId},
            CURRENT_TIMESTAMP
        )
    </insert>

    <delete id="delFile">
        UPDATE  stru_component_file
        SET
        del_flag = 1,
        end_date = CURRENT_TIMESTAMP
        WHERE file_id = #{id}
    </delete>

    <select id="selectlFile" resultType="cc.crrc.manage.pojo.SysFilePO">
    SELECT
        t.id,
        t.name,
        t.url,
        t.type,
        t.format,
        t.size,
        t.hash_code as hashCode,
        t.del_flag as delFlag,
        t.create_by as createBy,
        t.create_time as createTime,
        t.modify_by as modifyBy,
        t.modify_time as modifyTime,
        t.remark,
        t.group,
        t.file_location as fileLocation
    FROM
        sys_file t
    JOIN
        stru_component_file ct
    ON
        ct.file_id = t.id
    WHERE
        ct.component_id = #{componentId}
        AND ct.del_flag = 0
    </select>

    <select id="getComponentsByTypeId"  resultType="cc.crrc.manage.pojo.component.ComponentDTO">
        SELECT
            <include refid="ComponentAlias"/>
        FROM stru_component c
        JOIN
            stru_component_type ct
        ON
            ct.id = c.component_type_id
        WHERE
            ct.del_flag = 0
            AND c.del_flag = 0
            AND c.id not in (SELECT component_id FROM stru_vehicle_structure_component WHERE component_id IS NOT NULL)
            AND c.component_type_id = #{componentTypeId}
    </select>
    <!--根据部件序列号筛选唯一的部件-->
    <select id="getComponentBySerialNumber" resultType="cc.crrc.manage.pojo.component.ComponentDTO">
        SELECT
            <include refid="ComponentAlias"/>
        FROM stru_component c
        WHERE
            c.del_flag = 0 AND
            c.serial_number = #{serialNumber}
    </select>
</mapper>