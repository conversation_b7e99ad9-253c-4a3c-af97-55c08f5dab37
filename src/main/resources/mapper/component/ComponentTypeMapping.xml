<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.component.ComponentTypeMapping">
    <!--根据id查询部件类型信息-->
    <select id="getComponentTypeById" resultType="cc.crrc.manage.pojo.component.ComponentTypeDetailVO">
        SELECT
            c.id,
            c.name_cn AS nameCn,
            c.name_en AS nameEn,
            c.product_number AS productNumber,
            c.manufacturer_id AS manufacturerId,
            c.catalog,
            c.remark,
            <if test="fileType != null and fileType != ''">
                file.id AS fileId,
                file.url AS pictureURL,
            </if>
            m.name AS manufacturerName
        FROM stru_component_type c
        LEFT JOIN mtr_manufacturer m ON c.manufacturer_id = m.id
        <if test="fileType != null and fileType != ''">
            LEFT JOIN
                 (SELECT id,url,component_type_id
                  FROM stru_component_type_file sctf
                           LEFT JOIN sys_file sf ON sf.id = sctf.file_id
                  WHERE sf.type = #{fileType}
                    AND sf.del_flag = 0
                 ) file ON file.component_type_id = c.id
        </if>
        WHERE c.id = #{id}
        ORDER BY c.create_time DESC
        LIMIT 1
    </select>

    <!-- 2020年8月14日 房明宽 增加 线路id和车型id 查询条件 -->
    <select id="getComponentTypes" resultType="cc.crrc.manage.pojo.component.ComponentTypeDTO">
        SELECT
        c.id,
        c.name_cn AS nameCn,
        c.name_en AS nameEn,
        c.product_number AS productNumber,
        c.manufacturer_id AS manufacturerId,
        m.name as manufacturerName,
        c.catalog,
        c.del_flag AS delFlag,
        c.create_by AS createBy,
        c.create_time AS createTime,
        c.modify_by AS modifyBy,
        c.modify_time AS modifyTime,
        c.remark,
        VT.id as vehicleTypeId,
        L.id as lineId
        FROM
        stru_component_type c
        LEFT JOIN mtr_manufacturer m ON c.manufacturer_id = m.id
        left join mtr_vehicle_type VT on C.vehicle_type_id = VT.id
        left join mtr_line L on VT.line_id = L.id
        WHERE
        c.del_flag = 0
        <if test="productNumber != null and productNumber != ''">
            AND c.product_number LIKE '%'||#{productNumber}||'%'
        </if>
        <if test="catalog != null and catalog != ''">
            AND c.catalog LIKE '%'||#{catalog}||'%'
        </if>
        <if test="nameCn != null and nameCn != ''">
            AND c.name_cn LIKE '%'||#{nameCn}||'%'
        </if>
        <if test="nameEn != null and nameEn != ''">
            AND c.name_en LIKE '%'||#{nameEn}||'%'
        </if>
        <!-- 查询部件型号列表，增加查询条件：线路id、车型id -->
        <if test="lineId != null and lineId != ''">
            AND c.line_id = #{lineId}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId != ''">
            AND c.vehicle_type_id = #{vehicleTypeId}
        </if>
        ORDER BY
        coalesce(c.modify_time, c.create_time)
        DESC NULLS LAST
    </select>

    <!-- 2020年8月14日 房明宽 增加部件类型  相比之前多了车型id和线路id -->
    <insert id="insertComType">
        INSERT INTO stru_component_type
        (
            id,
            name_cn,
            name_en,
            product_number,
            catalog,
            manufacturer_id,
            remark,
            create_by,
            create_time,
            vehicle_type_id,
            line_id
        ) VALUES (
            #{id},
            #{nameCn},
            #{nameEn},
            #{productNumber},
            #{catalog},
            #{manufacturerId},
            #{remark},
            #{createBy},
            CURRENT_TIMESTAMP,
            #{vehicleTypeId},
            #{lineId}
        )
    </insert>

    <!-- 2020年8月14日 房明宽 相比之前多了车型id和线路id -->
    <update id="updateComType">
        UPDATE stru_component_type SET
            name_cn = #{nameCn},
            name_en = #{nameEn},
            product_number = #{productNumber},
            catalog = #{catalog},
            manufacturer_id = #{manufacturerId},
            remark = #{remark},
            modify_by = #{modifyBy},
            modify_time = CURRENT_TIMESTAMP,
            vehicle_type_id = #{vehicleTypeId},
            line_id = #{lineId}
        WHERE
            id = #{id}
    </update>

    <update id="delComType">
        UPDATE stru_component_type SET
            del_flag = 1
        WHERE
            id = #{id}
    </update>

    <update id="clearManufacturerFromComponentType">
        UPDATE stru_component_type
        <set>
            manufacturer_id = null,
            <if test="modifyBy != null and modifyBy !=''">
                modify_by = #{modifyBy},
            </if>
            modify_time = now()
        </set>
        WHERE manufacturer_id = #{manufacturerId}
    </update>

    <delete id="delEmployee">
        DELETE FROM mtr_component_type_contacts
        WHERE component_type_id = #{id}
    </delete>

    <delete id="deleteComponentTypeContactsByEmployeeId">
        DELETE FROM mtr_component_type_contacts
        WHERE manufacturer_employee_id = #{id}
    </delete>

    <select id="valEmployee" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM mtr_component_type_contacts
        WHERE manufacturer_employee_id = #{empId}
        AND component_type_id = #{id}
    </select>

    <insert id="assEmployee">
        INSERT INTO mtr_component_type_contacts
        (
            manufacturer_employee_id,
            component_type_id
        )VALUES
        (
            #{empId},
            #{id}
        )
    </insert>

    <select id="getComponentTypeIdByEmployeeId" resultType="java.lang.Long">
        select component_type_id
        FROM mtr_component_type_contacts
        WHERE manufacturer_employee_id = #{id}
    </select>

    <select id="getEmployees" resultType="cc.crrc.manage.pojo.mtr.ManufacturerEmployeePO">
        SELECT
            me.id,
            me.name,
            me.phone,
            me.email,
            me.type,
            me.gender,
            me.profile,
            me.manufacturer_id AS manufacturerId
        FROM
            mtr_manufacturer_employee me
        JOIN
            mtr_component_type_contacts ct
        ON
            me.id = ct.manufacturer_employee_id
        JOIN
            stru_component_type C
        ON
            c.id = ct.component_type_id
        WHERE
            c.del_flag = 0
            AND ct.component_type_id = #{id}
            AND me.manufacturer_id = #{manufacturerId}
    </select>

    <insert id="saveFaultTypeFile">
        INSERT INTO stru_component_type_file
        (
            component_type_id,
            file_id,
            start_date
        )VALUES(
            #{id},
            #{fileId},
            CURRENT_TIMESTAMP
        )
    </insert>

    <delete id="delFile">
        UPDATE  stru_component_type_file
        SET
        del_flag = 1,
        end_date = CURRENT_TIMESTAMP
        WHERE file_id = #{id}
    </delete>

    <select id="selectlFile" resultType="cc.crrc.manage.pojo.SysFilePO">
    SELECT
        t.id,
        t.name,
        t.url,
        t.type,
        t.format,
        t.size,
        t.hash_code as hashCode,
        t.del_flag as delFlag,
        t.create_by as createBy,
        t.create_time as createTime,
        t.modify_by as modifyBy,
        t.modify_time as modifyTime,
        t.remark,
        t.group,
        t.file_location as fileLocation
    FROM
        sys_file t
    JOIN
        stru_component_type_file ct
    ON
        ct.file_id = t.id
    WHERE
        ct.component_type_id = #{componentTypeId}
        AND ct.del_flag = 0
        AND (t.type IS NULL
            OR t.type != 'structurePic')
    </select>

    <select id="getSysFileByComponentTypeId" resultType="cc.crrc.manage.pojo.SysFilePO">
        SELECT
            sf.url
        FROM
            stru_component_type_file tf,
            sys_file sf
        WHERE
            sf.TYPE = '1' AND
            sf.ID = tf.file_id AND
            tf.component_type_id = #{componentTypeId}
            AND tf.del_flag = 0
    </select>

    <delete id="deleteMtrComponentTypeContacts">
        DELETE FROM mtr_component_type_contacts
        WHERE manufacturer_employee_id = #{manufacturerEmployeeId} and component_type_id =#{componentTypeId}
    </delete>

    <select id="validCount" resultType="java.lang.Integer">
    	SELECT COUNT(1)
    	FROM mtr_component_type_contacts
    	WHERE manufacturer_employee_id = #{manufacturerEmployeeId} and component_type_id =#{componentTypeId}
    </select>

</mapper>