<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.SysRoleMapping">
    <select id="getSysRoleByRoleType" resultType="cc.crrc.manage.pojo.SysRoleVO">
        select
        sr.id,
        sr.role_code as roleCode,
        sr.role_name as roleName,
        sr.parent_id as parentId,
        sr.role_path as rolePath,
        sr.role_type as roleType,
        sr.remarks,
        sr.create_by as createBy,
        sr.create_date as createDate,
        sr.update_by as updateBy,
        sr.update_date as updateDate,
        sr.tenant_id as tenatId,
        sr.data_permission as dataPermission,
        sr.del_flag as delFlag,
        sr.selected_menu_ids as selectedMenuIds,
        srt.role_type_name as roleTypeName
        from sys_role sr left join sys_role_type srt on
        sr. role_type = srt.id
        <where>
            1=1
            <if test="roleType != null and roleType != ''">
                and sr.role_type= #{roleType}
            </if>
            and sr.del_flag='0'
        </where>
    </select>
    <select id="getSysRoleByDataPermission" resultType="cc.crrc.manage.pojo.SysRoleVO">
        select
        sr.id,
        sr.role_code as roleCode,
        sr.role_name as roleName,
        sr.parent_id as parentId,
        sr.role_path as rolePath,
        sr.role_type as roleType,
        sr.remarks,
        sr.create_by as createBy,
        sr.create_date as createDate,
        sr.update_by as updateBy,
        sr.update_date as updateDate,
        sr.tenant_id as tenatId,
        sr.data_permission as dataPermission,
        sr.del_flag as delFlag,
        sr.selected_menu_ids as selectedMenuIds,
        sr.home_menu_id as homeMenuId,
        sd.label,
        sm.title as homeMenuTitle
        from sys_role sr
        left join sys_dict sd on sr.data_permission = sd.value
        left join sys_menu sm on sm.id = sr.home_menu_id
        <where>
            $DATASCOPE$
            <if test="dataPermission != null and dataPermission != ''">
                and sr.data_permission = #{dataPermission}
            </if>
            and sr.del_flag='0'
            and sr.role_code != 'superadmin'
            and sd.type_code='data_permission'
        </where>

    </select>

    <select id="getSysRole" resultType="cc.crrc.manage.pojo.SysRoleVO">
        select
        sr.id,
        sr.role_code as roleCode,
        sr.role_name as roleName,
        sr.parent_id as parentId,
        sr.role_path as rolePath,
        sr.role_type as roleType,
        sr.remarks,
        sr.create_by as createBy,
        sr.create_date as createDate,
        sr.update_by as updateBy,
        sr.update_date as updateDate,
        sr.tenant_id as tenatId,
        sr.del_flag as delFlag,
        sr.data_permission as dataPermission,
        sr.selected_menu_ids as selectedMenuIds
        from sys_role sr
        <where>
            1=1
            <if test="roleCode != null and roleCode != ''">
                and sr.role_code = #{roleCode}
            </if>
            <if test="roleName != null and roleName != ''">
                and sr.role_name = #{roleName}
            </if>
            and sr.del_flag='0'
        </where>

    </select>
    <!--1-->
    <insert id="saveSysRoleSonInfo">
     <!--  <selectKey resultType="long" order="AFTER" keyProperty="id">
            SELECT currval('sys_role_id_seq'::regclass) AS id
        </selectKey>  -->  
        insert into sys_role
        (id,role_code,role_name,parent_id,role_path,role_type,remarks,create_by,create_date,tenant_id,del_flag,selected_menu_ids,data_permission,home_menu_id)
        values
        (#{id},#{roleCode},#{roleName},#{parentId},#{rolePath},#{roleType},#{remarks},#{createBy},now(),#{tenantId},#{delFlag},#{selectedMenuIds},#{dataPermission},#{homeMenuId})
    </insert>
    <!--2-->
    <update id="updateSysRoleSonInfo">
        update sys_role
        <trim prefix="set" suffixOverrides=",">
            <if test="roleCode != null and roleCode != ''">
                role_code = #{roleCode},
            </if>
            <if test="roleType != null and roleType != ''">
                role_type = #{roleType},
            </if>
            <if test="roleName != null and roleName != ''">
                role_name = #{roleName},
            </if>
            <if test="rolePath != null and rolePath != ''">
                role_path = #{rolePath},
            </if>
            <if test="remarks != null and remarks != ''">
                remarks = #{remarks},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="dataPermission != null and dataPermission != ''">
                data_permission = #{dataPermission},
            </if>
            home_menu_id = #{homeMenuId},
            selected_menu_ids = #{selectedMenuIds},
            update_date=now()
        </trim>
        <where>
            id = #{id}
        </where>
    </update>
    <!--3-->
    <update id="deleteSysRoleInfo">
        UPDATE sys_role SET del_flag = 1 WHERE id = #{id}
    </update>
    <!--4   删除角色对应的菜单-->
    <delete id="deleteRoleMenuByRoleId">
        delete from sys_role_menu where role_id=#{id}
    </delete>
    <!--5-->
    <insert id="batchRoleMenu">
        insert into sys_role_menu(role_id, menu_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.roleId},#{item.menuId})
        </foreach>
    </insert>
    <!--6-->
    <select id="getSysRoleInfoById" resultType="cc.crrc.manage.pojo.SysRoleVO">
        select
        sr.id,
        sr.role_code as roleCode,
        sr.role_name as roleName,
        sr.parent_id as parentId,
        sr.role_path as rolePath,
        sr.role_type as roleType,
        sr.remarks,
        sr.create_by as createBy,
        sr.create_date as createDate,
        sr.update_by as updateBy,
        sr.update_date as updateDate,
        sr.tenant_id as tenatId,
        sr.selected_menu_ids as selectedMenuIds,
        sr.data_permission as dataPermission,
        sr.del_flag as delFlag,
        sd.label
        from sys_role sr left join sys_dict sd on
        sr.data_permission = sd.value
        WHERE
            sd.type_code='data_permission'
        AND sr.del_flag='0'
        <if test="id != null and id != ''">
            AND sr.id = #{id}
        </if>
    </select>
    <!--7-->
    <select id="getSysRoleInfoByParentId" resultType="cc.crrc.manage.pojo.SysRoleVO">
        select
        id,
        role_code as roleCode,
        role_name as roleName,
        parent_id as parentId,
        role_path as rolePath,
        role_type as roleType,
        remarks,
        create_by as createBy,
        create_date as createDate,
        update_by as updateBy,
        update_date as updateDate,
        tenant_id as tenatId,
        selected_menu_ids as selectedMenuIds,
        data_permission as dataPermission,
        del_flag as delFlag
        from sys_role
        <where>
            parent_id = #{parentId} and del_flag='0'
        </where>
    </select>
    <!--8 getSysRoleMenuById-->
    <select id="getSysRoleMenuById" resultType="cc.crrc.manage.pojo.SysRoleMenu">
        select
        role_id as roleId,
        menu_id as menuId
        from sys_role_menu
        <where>
            role_id = #{id}
        </where>
    </select>

    <!-- 9 selectExistSysRole-->
    <select id="selectExistSysRole" resultType="cc.crrc.manage.pojo.SysRoleVO">
        select
        id as id,
        role_code as roleCode,
        role_name as roleName,
        parent_id as parentId,
        role_path as rolePath,
        role_type as roleType,
        remarks,
        create_by as createBy,
        create_date as createDate,
        update_by as updateBy,
        update_date as updateDate,
        tenant_id as tenatId,
        selected_menu_ids as selectedMenuIds,
        data_permission as dataPermission,
        del_flag as delFlag
        from sys_role
        where
            del_flag = '0'
        and (role_code = #{roleCode} or role_name = #{roleName})
    </select>
    <!--用户角色关系多对多 新建用户时调用的维护角色和用户关系的方法-->
    <insert id="addRoleUserRelation">
        INSERT INTO sys_role_user (
        user_id,
        role_id
        )
        VALUES (
        #{userId},
        #{roleId}
        )
    </insert>
    <!--用户角色关系多对多 查询已经存在的用户角色关系-->
    <select id="listRoleUserRelationsByUserId" resultType="cc.crrc.manage.pojo.SysRoleUserVO">
        SELECT
        user_id AS userId,
        role_id AS roleId
        FROM
        sys_role_user
        WHERE
        user_id = #{userId}
    </select>
    <!--删除已经存在的用户角色关系-->
    <delete id="deleteRoleUserRelation">
        DELETE FROM
        sys_role_user
        WHERE
        user_id = #{userId} AND
        role_id = #{roleId}
    </delete>
    <!--13 查询当前菜单是否有子菜单-->
    <select id="findCountByHomeMenuId" resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM
        sys_menu
        WHERE
        del_flag = '0'
        AND
        parent_id = #{homeMenuId}
    </select>
    
    
    
     <!--获取当前用户的datapermission最大值-->
    <select id="getMaxDataPermissionByUsername" resultType="java.lang.Integer">
    SELECT MAX
	( data_permission ) dataPermission 
FROM
	(
	SELECT
		sr.data_permission 
	FROM
		sys_user su,
		sys_role sr,
		sys_role_user sru 
	WHERE
		su.ID = sru.user_id 
		AND sru.role_id = sr.ID 
	AND su.id = #{userId} 
	) AS A 
    </select>
    
    
    
</mapper>
