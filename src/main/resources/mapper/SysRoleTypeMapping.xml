<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.SysRoleTypeMapping">

    <insert id="saveSysRoleType">
        INSERT INTO sys_role_type (
        id,
        role_type_code,
        role_type_name,
        remarks,
        create_by,
        create_date,
        update_by
        )
        VALUES
        (
        #{id},#{roleTypeCode},#{roleTypeName},#{remarks},#{createBy},CURRENT_TIMESTAMP,#{updateBy})
    </insert>

    <select id="getSysRoleType" resultType="String">
        SELECT role_type_name FROM sys_role_type
        <where>
            <if test="roleTypeCode != null and roleTypeCode != ''">
                role_type_code = #{roleTypeCode}
            </if>
            <if test="roleTypeName != null and roleTypeName != ''">
               or role_type_name = #{roleTypeName}
            </if>
        </where>
    </select>

    <delete id="deleteSysRoleTypeById">
        DELETE FROM sys_role_type
        <where>
        id = #{id}
        </where>
    </delete>

    <update id="updateSysRoleTypeById">
        update
        sys_role_type
        <trim prefix="set" suffixOverrides=",">
            <if test="roleTypeCode != null and roleTypeCode != ''">
                role_type_code = #{roleTypeCode},
            </if>
            <if test="roleTypeName != null and roleTypeName != ''">
                role_type_name = #{roleTypeName},
            </if>
            <if test="remarks != null and remarks != ''">
                remarks = #{remarks},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
        </trim>
        <where>
            id = #{id}
        </where>

    </update>

    <select id="selectSysRoleType" resultType="cc.crrc.manage.pojo.SysRoleTypeVO">
        SELECT
        id,
        role_type_code as roleTypeCode,
        role_type_name as roleTypeName,
        remarks,
        create_by as createBy,
        create_date as createDate,
        update_by as updateBy
        from
        sys_role_type
        <where>
            1=1
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="roleTypeCode != null and roleTypeCode != ''">
                and role_type_code = #{roleTypeCode}
            </if>
            <if test="roleTypeName != null and roleTypeName != ''">
                and role_type_name = #{roleTypeName}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createDate != null and createDate != ''">
                and create_date = #{createDate}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
        </where>
    </select>

    <select id="selectSysRoleTypeById" resultType="cc.crrc.manage.pojo.SysRoleTypeVO">
        SELECT
        id,
        role_type_code as roleTypeCode,
        role_type_name as roleTypeName,
        remarks,
        create_by as createBy,
        create_date as createDate,
        update_by as updateBy
        from
        sys_role_type
        <where>
            1=1
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="roleTypeCode != null and roleTypeCode != ''">
                and role_type_code = #{roleTypeCode}
            </if>
            <if test="roleTypeName != null and roleTypeName != ''">
                and role_type_name = #{roleTypeName}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy}
            </if>
            <if test="createDate != null and createDate != ''">
                and create_date = #{createDate}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy}
            </if>
        </where>
    </select>
</mapper>