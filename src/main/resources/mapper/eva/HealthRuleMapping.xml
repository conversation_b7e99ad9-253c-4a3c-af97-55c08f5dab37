<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.eva.HealthRuleMapping">
    <sql id="healthRuleAlias">
        ehru.id AS id,
        ehru.vehicle_type_id AS vehicleTypeId,
        ehru.item_name AS itemName,
        ehru.item_value AS itemValue,
        ehru.unit AS unit,
        ehru.category AS category,
        ehru.create_by AS createBy,
        ehru.create_time AS createTime,
        ehru.modify_by AS modifyBy,
        ehru.modify_time AS modifyTime,
        ehru.remark AS remark
    </sql>

    <select id="listHealthRule" resultType="cc.crrc.manage.pojo.eva.EvaHealthRulePO">
        SELECT
        <include refid="healthRuleAlias"/>
        FROM
        eva_health_rule ehru
        <where>
            1=1
            <if test="vehicleTypeId != null and vehicleTypeId != ''">
                AND vehicle_type_id = #{vehicleTypeId}
            </if>
            <if test="lineId != null and lineId != ''">
                AND vehicle_type_id in (SELECT id  from mtr_vehicle_type where line_id= #{lineId} and del_flag=0)
            </if>
            <if test="category != null and category != ''">
                AND category = #{category}
            </if>
        </where>
        ORDER BY remark
    </select>

    <insert id="addHealthRule">
        INSERT INTO eva_health_rule
        (
        vehicle_type_id,
        item_name,
        item_value,
        unit,
        category,
        create_by,
        create_time,
        modify_by,
        modify_time,
        remark
        )VALUES (
        #{vehicleTypeId},
        #{itemName},
        #{itemValue},
        #{unit},
        #{category},
        #{createBy},
        now(),
        #{modifyBy},
        now(),
        #{remark}
        )
    </insert>

    <update id="updateHealthRule">
        UPDATE eva_health_rule
        <set>
            <if test="vehicleTypeId != null and vehicleTypeId != ''">
                vehicle_type_id = #{vehicleTypeId},
            </if>
            <if test="itemName != null and itemName != ''">
                item_name = #{itemName},
            </if>
            <if test="itemValue != null and itemValue != ''">
                item_value = #{itemValue},
            </if>
            <if test="unit != null and unit != ''">
                unit = #{unit},
            </if>
            <if test="category != null and category != ''">
                category = #{category},
            </if>
            <if test="modifyBy != null and modifyBy !=''">
                modify_by = #{modifyBy},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            modify_time = now()
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteHealthRuleById">
        DELETE FROM eva_health_rule
        WHERE id = #{id}
    </delete>

</mapper>