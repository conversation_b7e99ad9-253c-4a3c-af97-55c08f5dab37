<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.eva.HealthResultMapping">
    <sql id="healthResultAlias">
        ehr.id AS id,
        ehr.vehicle_id AS vehicleId,
        ehr.vehicle_structure_code AS vehicleStructureCode,
        ehr.item_name AS itemName,
        ehr.item_value AS itemValue,
        ehr.type AS type,
        ehr.instruction AS instruction,
        ehr.create_by AS createBy,
        ehr.create_time AS createTime,
        ehr.line_id AS lineId
    </sql>

    <select id="getLatestScopeByVehicleCode" resultType="cc.crrc.manage.pojo.eva.EvaHealthResultPO">
        SELECT
        <include refid="healthResultAlias"/>
        FROM
        eva_health_result ehr
        LEFT JOIN mtr_vehicle mv ON mv.id = ehr.vehicle_id
        WHERE
        ehr.create_time >= now() - INTERVAL '30 DAY'
        AND mv.vehicle_code = #{vehicleCode}
        AND ehr.item_name = '整车'
        ORDER BY ehr.create_time
    </select>

    <select id="getScopeHistoryByVehicleCode" resultType="cc.crrc.manage.pojo.eva.EvaHealthResultPO">
        SELECT
        ehr.id AS id,
        ehr.vehicle_id AS vehicleId,
        ehr.vehicle_structure_code AS vehicleStructureCode,
        mv.vehicle_code AS itemName,
        ehr.item_value AS itemValue,
        ehr.type AS type,
        ehr.instruction AS instruction,
        ehr.create_by AS createBy,
        ehr.create_time AS createTime,
        ehr.line_id AS lineId
        FROM
        eva_health_result ehr
        LEFT JOIN mtr_vehicle mv ON mv.id = ehr.vehicle_id
        WHERE
--         ehr.create_time >= now() - INTERVAL '30 DAY'
        ehr.create_time  BETWEEN #{startTime} AND #{endTime}
        AND mv.vehicle_code = #{vehicleCode}
        AND mv.del_flag = 0
        AND ehr.item_name = '整车'
        ORDER BY ehr.create_time
    </select>

    <select id="getSystemScopeByVehicleCodeAndSysName" resultType="cc.crrc.manage.pojo.eva.EvaHealthResultPO">
        SELECT
        <include refid="healthResultAlias"/>
        FROM
        eva_health_result ehr
        LEFT JOIN mtr_vehicle mv ON mv.id = ehr.vehicle_id
        WHERE
--         ehr.create_time >= now() - INTERVAL '30 DAY'
        ehr.create_time  BETWEEN #{startTime} AND #{endTime}
        AND mv.vehicle_code = #{vehicleCode}
        AND mv.del_flag = 0
        AND ehr.item_name = #{sysName}
        ORDER BY ehr.create_time
    </select>

    <select id="getScopeHistoryByLineId" resultType="cc.crrc.manage.pojo.eva.EvaHealthResultPO">
        SELECT
        <include refid="healthResultAlias"/>
        FROM
        eva_health_result ehr
        WHERE
        create_time >= now() - INTERVAL '30 DAY'
        AND
        line_id = #{lineId}
        ORDER BY create_time
    </select>
    <select id="getScopeHistoryByLine" resultType="cc.crrc.manage.pojo.eva.EvaHealthResultPO">
        SELECT
        <include refid="healthResultAlias"/>
        FROM
        eva_health_result ehr
        WHERE
--         create_time >= now() - INTERVAL '30 DAY'
        ehr.create_time BETWEEN #{startTime} AND #{endTime}
        AND ehr.type = '线路'
        AND line_id = #{lineId}
        ORDER BY create_time
    </select>
    <select id="trainScoreList" resultType="cc.crrc.manage.pojo.excel.healthyScore.HealthyScoreDTO">
            SELECT
                t1.create_time :: DATE AS time,
-- 	        t2.vehicle_code AS "vehicleCode",
	        MAX ( CASE t1.item_name WHEN 'ATC系统' THEN t1.item_value END ) AS "ATC",
	        MAX ( CASE t1.item_name WHEN 'LCU系统' THEN t1.item_value END ) AS "LCU",
	        MAX ( CASE t1.item_name WHEN 'RIOM系统' THEN t1.item_value END ) AS "RIOM",
	        MAX ( CASE t1.item_name WHEN 'VCU-DDU' THEN t1.item_value END ) AS "VCU",
	        MAX ( CASE t1.item_name WHEN '乘客信息系统' THEN t1.item_value END ) AS "PIS",
	        MAX ( CASE t1.item_name WHEN '制动系统' THEN t1.item_value END ) AS "BCU",
	        MAX ( CASE t1.item_name WHEN '弓网系统' THEN t1.item_value END ) AS "PCMU",
	        MAX ( CASE t1.item_name WHEN '火灾系统' THEN t1.item_value END ) AS "FAU",
	        MAX ( CASE t1.item_name WHEN '牵引系统' THEN t1.item_value END ) AS "DCU",
	        MAX ( CASE t1.item_name WHEN '空调系统' THEN t1.item_value END ) AS "HVAC",
	        MAX ( CASE t1.item_name WHEN '能耗记录仪系统' THEN t1.item_value END ) AS "ECR",
	        MAX ( CASE t1.item_name WHEN '蓄电池检测系统' THEN t1.item_value END ) AS "BMS",
	        MAX ( CASE t1.item_name WHEN '走行部系统' THEN t1.item_value END ) AS "BDS",
	        MAX ( CASE t1.item_name WHEN '车门系统' THEN t1.item_value END ) AS "EDCU",
	        MAX ( CASE t1.item_name WHEN '轨道几何&amp;线路巡检系统' THEN t1.item_value END ) AS "TLDS",
	        MAX ( CASE t1.item_name WHEN '辅助系统' THEN t1.item_value END ) AS "SIV",
            MAX ( CASE t1.item_name WHEN '障碍物检测系统' THEN t1.item_value END ) AS "ODS",
            MAX ( CASE t1.item_name WHEN '驾驶系统' THEN t1.item_value END ) AS "DRV"
            FROM
                eva_health_result t1
                LEFT JOIN
                mtr_vehicle t2
            ON
                t1.vehicle_id = t2.id
            WHERE
                t1.create_time BETWEEN #{startTime} AND #{endTime}
              AND
                (
                t1.item_name = 'ATC系统'
               OR t1.item_name = 'LCU系统'
               OR t1.item_name = 'RIOM系统'
               OR t1.item_name = 'VCU-DDU'
               OR t1.item_name = '乘客信息系统'
               OR t1.item_name = '制动系统'
               OR t1.item_name = '弓网系统'
               OR t1.item_name = '火灾系统'
               OR t1.item_name = '牵引系统'
               OR t1.item_name = '空调系统'
               OR t1.item_name = '能耗记录仪系统'
               OR t1.item_name = '蓄电池检测系统'
               OR t1.item_name = '走行部系统'
               OR t1.item_name = '车门系统'
               OR t1.item_name = '轨道几何&amp;线路巡检系统'
               OR t1.item_name = '辅助系统'
               OR t1.item_name = '障碍物检测系统'
               OR t1.item_name = '驾驶系统'
                )
              AND t2.vehicle_code = #{vehicleCode}
            GROUP BY
                time
--             t2.vehicle_code
            ORDER BY
--             t2.vehicle_code,
                time
    </select>
    <select id="lineScoreList" resultType="cc.crrc.manage.pojo.excel.healthyScore.LineHealthyScoreDTO">
        SELECT
            t1.create_time :: DATE AS time,
            t2.name AS "lineName",
	        MAX ( CASE t1.item_name WHEN '线路名称' THEN t1.item_value END ) AS "lineScore"
        FROM
            eva_health_result t1
        LEFT JOIN
            mtr_line t2
        ON
            t1.line_id = t2.id
        WHERE
            t1.create_time BETWEEN #{startTime} AND #{endTime}
            AND
            (
            t1.item_name = '线路名称'
            )
          AND t1.line_id = #{lineId}
        GROUP BY
            time,
            t2.name
        ORDER BY
            time,
            t2.name
    </select>

</mapper>