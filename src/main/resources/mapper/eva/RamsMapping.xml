<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.eva.RamsMapping">
	<resultMap id="selectAll" type="cc.crrc.manage.pojo.eva.RamsLineVo">
		<id property="lineId" column="lineid"/>
		<result property="lineName" column="linename"/>
		<result property="totalDistance" column="totaldistance"/>
		<result property="totalDrivingTime" column="totaldrivingtime"/>
		<collection property="ramsLineVehicleTypeVOS" ofType="cc.crrc.manage.pojo.eva.RamsLineVehicleTypeVO">
			<result property="vehicleTypeName" column="vehicletypename"/>
			<result property="sameVehicleTypeNumber" column="vnum"/>
		</collection>
		<collection property="ramsfKindList" ofType="cc.crrc.manage.pojo.eva.RamsfKindVO">
			<id property="itemCategory" column="item_category"/>
			<collection property="ramsParameterDTOList" ofType="cc.crrc.manage.pojo.component.RamsParameterDTO" column="item_category">
				<result property="itemName" column="item_name"/>
				<result property="itemValue" column="item_value"/>
				<result property="unit" column="unit"/>
			</collection>
		</collection>
	</resultMap>

	<select id="getRamsLineList" resultMap="selectAll">
       SELECT
	ttt1.name  linename ,
	ttt1.id  lineid,
	ttt1.totaldistance totaldistance,
	ttt1.totaldrivingtime totaldrivingtime,
	ttt1.vehicletypename vehicletypename,
	ttt1.vnum vnum,
	ttt2.item_category item_category,
	ttt2.item_name item_name,
	ttt2.item_value item_value,
	ttt2.unit unit
FROM
	(
	(
SELECT
	*
FROM
	(
	(
SELECT DISTINCT
	( d.NAME ),
	c.id,
	c.totalDrivingTime,
	c.totalDistance
FROM
	(
	(
SELECT
	a.id,
	b.NAME,
	a.totalDrivingTime,
	a.totalDistance
FROM
	(
	(
SELECT
	t3.id,
	sum( CASE WHEN t1.item_name_en = 'Total Driving Time' THEN to_number ( t1.item_value, '999999999.99' ) ELSE 0 END ) AS totalDrivingTime,
	sum( CASE WHEN t1.item_name_en = 'Total Distance' THEN to_number ( t1.item_value, '999999999.99' ) ELSE 0 END ) AS totalDistance
FROM
	mtr_operating_param t1
	RIGHT JOIN mtr_vehicle t2 ON t1.vehicle_id = t2.id
	LEFT JOIN mtr_line t3 ON t2.metro_line_id = t3.id
WHERE
	t1.vehicle_id IS NOT NULL
	AND t1.vehicle_id IS NOT NULL
GROUP BY
	t3.id
	) a
	LEFT JOIN mtr_line b ON a.id = b.id
	)
	) c
	LEFT JOIN mtr_line d ON c.id = d.id
	)
	) tt1
	LEFT JOIN (
SELECT DISTINCT
	( mvtr1.vehicle_type_id ) vehicle_type_id,
	metro_line_id,
	mvt.NAME vehicleTypeName,
	ttt1.vnum
FROM
	(
	(
SELECT
	mvtr.vehicle_type_id,
	count( 1 ) Vnum
FROM
	mtr_vehicle mv
	INNER JOIN mtr_vehicle_type_relation mvtr ON mv.ID = mvtr.vehicle_id
	AND mvtr.valid = 1
GROUP BY
	mvtr.vehicle_type_id
	) ttt1
	LEFT JOIN mtr_vehicle_type mvt ON ttt1.vehicle_type_id = mvt.ID
	AND mvt.del_flag = 0
	LEFT JOIN mtr_vehicle_type_relation mvtr1 ON ttt1.vehicle_type_id = mvtr1.vehicle_type_id
	AND mvtr1.valid = 1
	LEFT JOIN mtr_vehicle mv1 ON mvtr1.vehicle_id = mv1.id
	)
	) tt2 ON tt1.id = tt2.metro_line_id
	)
	) ttt1
	LEFT JOIN (
SELECT DISTINCT
	( ers.id ),
	ers.line_id,
	mvtr3.vehicle_type_id,
	ers.item_category,
	ers.item_name,
	ers.item_value,
	ers.unit
FROM
	eva_rams_result ers
	LEFT JOIN mtr_vehicle_type_relation mvtr3 ON ers.vehicle_type_id = mvtr3.vehicle_type_id
	AND mvtr3.valid = 1
WHERE
	ers.line_id IS NOT NULL
	) ttt2 ON ttt1.id = ttt2.line_id
	AND ttt1.vehicle_type_id = ttt2.vehicle_type_id
	)
	ORDER BY
case
when ttt2.item_category like '%服务故障%' then 1
 when ttt2.item_category like '%R%' then 2
 when ttt2.item_category like '%A%' then 3
 when ttt2.item_category like '%M%' then 4
 when ttt2.item_category like '%S%' then 5

end
    </select>

	<select id="getLineDTOList" resultType="cc.crrc.manage.pojo.line.LineDTO">
        SELECT
        id,
        name
        FROM mtr_line
    </select>
	<select id="getDrivingDistanceAndTime" resultType="cc.crrc.manage.pojo.eva.RamsLineVo">
        select
        sum( CASE WHEN mop.item_name_en = 'Total Driving Time' THEN to_number ( mop.item_value, '999999999.99' ) ELSE 0 END ) AS totalDrivingTime,
	    sum( CASE WHEN mop.item_name_en = 'Total Distance' THEN to_number ( mop.item_value, '999999999.99' ) ELSE 0 END ) AS totalDistance
        from
        mtr_operating_param mop join mtr_vehicle mv on mop.vehicle_id=mv.id and mv.del_flag = 0
        where mv.metro_line_id=#{lineId}
    </select>
	<select id="getRamsVehicleTypeVOs" resultType="cc.crrc.manage.pojo.eva.RamsLineVehicleTypeVO">
    SELECT
    DISTINCT
    mvt.id,
	mvt.NAME,
	COUNT( 1 ) OVER ( PARTITION BY mvtr.vehicle_type_id ) AS sameVehicleTypeNumber
    FROM
	mtr_line ml
	INNER JOIN mtr_vehicle mv ON ml.ID = mv.metro_line_id
	AND MV.del_flag = 0
	INNER JOIN mtr_vehicle_type_relation mvtr ON mv.ID = mvtr.vehicle_id
	and mvtr.valid = 1
	LEFT JOIN mtr_vehicle_type mvt ON mvtr.vehicle_type_id = mvt.ID
	and mvt.del_flag = 0
	where ml.id = #{lineId}
    </select>
	<select id="getItems" resultType="cc.crrc.manage.pojo.component.RamsParameterDTO">
	SELECT
	item_category itemCategory,
	item_name itemName,
	CASE WHEN item_category = '可靠性A' THEN
	avg( to_number ( ( split_part ( item_value, '%', 1 ) ), '999999999999999999.999' ) ) ELSE avg( to_number ( item_value, '9999999999999999999999.999' ) )
	END itemValue,
	max(unit) unit
    FROM
	eva_rams_result ers
    WHERE
	line_id = #{lineId}
	group by
	itemCategory,
	itemName
    </select>

	<select id="count" resultType="java.lang.Integer">
        SELECT
            count( 1 )
        FROM
            mtr_vehicle_type_relation
        WHERE
            vehicle_type_id = #{id}
        AND valid = 1
    </select>

	<select id="getParamList" resultType="cc.crrc.manage.pojo.component.OperatingParameterDTO">
        select
        item_name_cn as itemNameCn,
        item_name_en as itemNameEn,
        item_value as itemValue,
        unit
        from mtr_operating_param mop join mtr_vehicle_type_relation mvtr on mop.vehicle_id=mvtr.vehicle_id
        where mvtr.vehicle_type_id = #{id}
    </select>

	<select id="getRamsParamList" resultType="cc.crrc.manage.pojo.component.RamsParameterDTO">
		SELECT
		DISTINCT
		eva_rams_result.id,
		mtr_rams_param.id,
		mtr_rams_param.component_type_id as comTypeId,
		mtr_rams_param.vehicle_type_id as vehicleTypeId,
		mtr_rams_param.item_name as itemName,
		CONCAT(mtr_rams_param.item_value,' / ',eva_rams_result.item_value) as itemValue,
		mtr_rams_param.unit
		FROM
		mtr_rams_param
		JOIN
		mtr_vehicle_type
		on
		mtr_rams_param.vehicle_type_id = mtr_vehicle_type.id
		join
		mtr_vehicle_type_relation t1
		on
		t1.vehicle_type_id = mtr_rams_param.vehicle_type_id
		JOIN
		mtr_vehicle t2
		on
		t2.id =t1.vehicle_id
		JOIN
		eva_rams_result
		ON
		mtr_vehicle_type.id = eva_rams_result.vehicle_type_id
		WHERE
		1=1
		AND mtr_rams_param.item_name = eva_rams_result.item_name
		<if test="comTypeId != null and comTypeId !=''">
			AND mtr_vehicle_type.id = #{comTypeId}
		</if>
		<if test="vehicleTypeId != null and vehicleTypeId != ''">
			AND mtr_vehicle_type.id = #{vehicleTypeId}
		</if>
		AND eva_rams_result.line_id =t2.metro_line_id
	</select>

	<resultMap id="RamsEntity" type="cc.crrc.manage.pojo.eva.ComponentRamsVO">
		<result property="nameCn" column="nameCn"></result>
		<result property="productNumber" column="productNumber"></result>
		<result property="name" column="name"></result>
		<collection property="categoryList" ofType="cc.crrc.manage.pojo.eva.ComponentRamsCategoryVO">
			<result property="itemCategory" column="itemCategory"></result>
			<collection property="categoryItemList" ofType="cc.crrc.manage.pojo.eva.ComponentRamsCategoryItemVO">
				<result property="itemName" column="itemName"></result>
				<result property="resultValue" column="resultValue"></result>
				<result property="paramValue" column="paramValue"></result>
				<result property="unit" column="unit"></result>
			</collection>
		</collection>
	</resultMap>

	<select id="selectRams" resultMap="RamsEntity">
		select
		t1.item_name AS itemName,
		t1.item_value AS resultValue,
		t2.item_value AS paramValue,
		t2.unit,
		t1.item_category AS itemCategory,
		t3.name_cn AS nameCn,
		t3.product_number AS productNumber,
		t4.name,
		t2.component_type_id AS componentTypeId
		FROM
		eva_rams_result t1
		JOIN mtr_rams_param t2 ON t1.component_type_id=t2.component_type_id
		and t1.item_name = t2.item_name
		JOIN stru_component_type t3 ON t1.component_type_id =t3.id
		JOIN mtr_manufacturer t4 ON t3.manufacturer_id = t4.id
		WHERE
		1=1
		<if test="nameCn != null and nameCn != ''">
			AND  t3.name_cn LIKE '%'||#{nameCn}||'%'
		</if>
		<if test="productNumber != null and productNumber != ''">
			AND t3.product_number  LIKE '%'||#{productNumber}||'%'
		</if>
		AND t1.component_type_id IS NOT NULL
		AND t1.item_category IS NOT NULL
		--         ORDER BY t1.item_name
		ORDER BY t2.component_type_id
	</select>



</mapper>