<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.BusDictMapping">
    <resultMap type="cc.crrc.manage.pojo.BusDictVO" id="BusDictVOResult">
        <result property="id"    column="id"    />
        <result property="sortNumber"    column="sort_number"    />
        <result property="label"    column="label"    />
        <result property="value"    column="value"    />
        <result property="description"    column="description"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="typeCode"    column="type_code"    />
        <result property="lineId"    column="line_id"    />
        <result property="code"    column="code"    />
        <result property="vehicleTypeId"    column="vehicle_type_id"    />
        <result property="vehicleTypeName"    column="vehicle_type_name"    />
    </resultMap>

    <sql id="selectBusDictVo">
        select id, sort_number, label, value, description, del_flag, type_code, line_id,code, vehicle_type_id, vehicle_type_name from bus_dict
    </sql>

    <select id="selectBusDictList" parameterType="cc.crrc.manage.pojo.BusDictQuery" resultMap="BusDictVOResult">
        <include refid="selectBusDictVo"/>
        <where>
            del_flag = '0'
            <if test="sortNumber != null "> and sort_number = #{sortNumber}</if>
            <if test="label != null  and label != ''"> and label like concat('%', #{label}::text, '%')</if>
            <if test="value != null  and value != ''"> and value = #{value}</if>
            <if test="description != null  and description != ''"> and description like concat('%', #{description}::text, '%')</if>
            <if test="typeCode != null  and typeCode != ''"> and type_code = #{typeCode}</if>
            <if test="lineId != null  and lineId != ''"> and line_id = #{lineId}</if>
            <if test="vehicleTypeId != null and vehicleTypeId != ''"> and vehicle_type_id = #{vehicleTypeId}</if>
        </where>
        ORDER BY
        type_code, sort_number, bus_dict.last_modify_by ASC
    </select>

    <select id="selectBusDictByCode" parameterType="String" resultMap="BusDictVOResult">
        <include refid="selectBusDictVo"/>
        where type_code = #{typeCode}
        and del_flag = '0'
        and line_id = #{lineId}
        order by sort_number
    </select>

    <insert id="insertBusDict" parameterType="cc.crrc.manage.pojo.BusDict">
        insert into bus_dict
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="sortNumber != null">sort_number,</if>
            <if test="label != null and label != ''">label,</if>
            <if test="value != null and value != ''">value,</if>
            <if test="description != null">description,</if>
            <if test="lastModifyTime != null">last_modify_time,</if>
            <if test="lastModifyBy != null">last_modify_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="typeCode != null and typeCode != ''">type_code,</if>
            <if test="lineId != null">line_id,</if>
            <if test="code != null">code,</if>
            <if test="vehicleTypeId != null and vehicleTypeId != ''">vehicle_type_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="sortNumber != null">#{sortNumber},</if>
            <if test="label != null and label != ''">#{label},</if>
            <if test="value != null and value != ''">#{value},</if>
            <if test="description != null">#{description},</if>
            <if test="lastModifyTime != null">#{lastModifyTime},</if>
            <if test="lastModifyBy != null">#{lastModifyBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="typeCode != null and typeCode != ''">#{typeCode},</if>
            <if test="lineId != null">#{lineId},</if>
            <if test="code != null">#{code},</if>
            <if test="vehicleTypeId != null and vehicleTypeId != ''">#{vehicleTypeId},</if>
        </trim>
    </insert>

    <update id="updateBusDict" parameterType="cc.crrc.manage.pojo.BusDict">
        update bus_dict
        <trim prefix="SET" suffixOverrides=",">
            <if test="sortNumber != null">sort_number = #{sortNumber},</if>
            <if test="label != null and label != ''">label = #{label},</if>
            <if test="value != null and value != ''">value = #{value},</if>
            <if test="description != null">description = #{description},</if>
            <if test="lastModifyTime != null">last_modify_time = #{lastModifyTime},</if>
            <if test="lastModifyBy != null">last_modify_by = #{lastModifyBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="typeCode != null and typeCode != ''">type_code = #{typeCode},</if>
            <if test="lineId != null">line_id = #{lineId},</if>
            <if test="code != null">code = #{code},</if>
            <if test="vehicleTypeId != null and vehicleTypeId != ''">vehicle_type_id = #{vehicleTypeId},</if>
            <if test="vehicleTypeName != null and vehicleTypeName != ''">vehicle_type_name = #{vehicleTypeName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBusDictById" parameterType="String">
        update bus_dict
        set
            del_flag = '1',
            last_modify_by = #{lastModifyBy},
            last_modify_time = #{lastModifyTime}
        where id =#{id}
    </delete>

    <select id="getTypeLevel" resultType="java.lang.String">
        select
            type_level AS typeLevel
        from
            bus_dict_type
        WHERE
            del_flag = '0'
          AND
            code =#{typeCode}
    </select>

    <select id="listDictUniversal" resultType="cc.crrc.manage.pojo.BusDictVO">
        SELECT
        DISTINCT
        t.id,
        t.sort_number AS sortNumber,
        t.code,
        t.label,
        t.value,
        t.description,
        t.line_id AS lineId,
        t.type_code AS typeCode,
        t.vehicle_type_id AS vehicleTypeId,
        t.vehicle_type_name AS vehicleTypeName
        FROM
        bus_dict t
        WHERE
        t.del_flag = '0'
        <if test="typeCode != null and typeCode != ''">
            AND t.type_code = #{typeCode}
        </if>
        <if test="lineId != null and lineId != ''">
            AND t.line_id = #{lineId}
        </if>
        ORDER BY
        t.sort_number ASC
    </select>
    <select id="listByType" resultType="cc.crrc.manage.pojo.BusDictVO">
        SELECT
        t.id,
        t.sort_number AS sortNumber,
        t.code,
        t.label,
        t.value,
        t.description,
        t.line_id AS lineId,
        t.type_code AS typeCode
        FROM
        bus_dict t
        WHERE
        t.del_flag = '0'
        <if test="typeCode != null">
            AND t.type_code = #{typeCode}
        </if>
        ORDER BY
        t.sort_number,t.last_modify_time DESC

    </select>

    <update id="deleteBusDictByTypeCode" parameterType="String">
        update bus_dict
        set
        del_flag = '1'
        where type_code = #{typeCode}
    </update>

</mapper>