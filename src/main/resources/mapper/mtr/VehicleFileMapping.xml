<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtr.VehicleFileMapping">
    <sql id="SysFileAlias">
        sf.id AS id,
        sf.name AS name,
        sf.url AS url,
        sf.type AS type,
        sf.hash_code AS hashCode,
        sf.format AS format,
        sf.size AS size,
        sf.del_flag AS delFlag,
        sf.group AS group,
        sf.file_location AS fileLocation
    </sql>


    <select id="getFilesByVehicleId" resultType="cc.crrc.manage.pojo.SysFilePO">
        SELECT
        <include refid="SysFileAlias"/>
        FROM mtr_vehicle_file mvf
        LEFT JOIN sys_file sf ON sf.id = mvf.file_id
        WHERE
        mvf.vehicle_id = #{vehicleId}
        AND mvf.del_flag = 0
    </select>

    <insert id="addVehicleFile">
        INSERT INTO mtr_vehicle_file
        (vehicle_id,
         file_id,start_date)
        VALUES (#{vehicleId},
                #{fileId},CURRENT_TIMESTAMP)
    </insert>

    <delete id="deleteVehicleIdFileByFileId">
        UPDATE  mtr_vehicle_file
        SET
        del_flag = 1,
        end_date = CURRENT_TIMESTAMP
        WHERE
        file_id = #{fileId}
        <if test="vehicleId != null and vehicleId !=''">
            AND vehicle_id = #{vehicleId}
        </if>
    </delete>

</mapper>