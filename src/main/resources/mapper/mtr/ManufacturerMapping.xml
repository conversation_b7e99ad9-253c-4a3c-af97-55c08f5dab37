<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtr.ManufacturerMapping">
    <sql id="manufacturerAlias">
        mm.id AS id,
        mm.name AS name,
        mm.email AS email,
        mm.phone AS phone,
        mm.website AS website,
        mm.profile AS profile,
        mm.address AS address,
        mm.create_by AS createBy,
        mm.create_time AS createTime,
        mm.modify_by AS modifyBy,
        mm.modify_time AS modifyTime,
        mm.remark AS remark
    </sql>

    <select id="listManufacturer" resultType="cc.crrc.manage.pojo.mtr.ManufacturerPO">
        SELECT 
        <include refid="manufacturerAlias"/>
        FROM
        mtr_manufacturer mm
        <where>
            <if test="name != null and name != ''">
                AND name LIKE '%'||#{name}||'%'
            </if>
            <if test="address != null and address != ''">
                AND address LIKE '%'||#{address}||'%'
            </if>
        </where>
        ORDER BY createTime DESC
    </select>

    <select id="getManufacturerById" resultType="cc.crrc.manage.pojo.mtr.ManufacturerPO">
        SELECT
        <include refid="manufacturerAlias"/>
        FROM
        mtr_manufacturer mm
        WHERE
        id = #{id}
    </select>

    <insert id="addManufacturer">
        INSERT INTO mtr_manufacturer
        (
        id,
        name,
        email,
        phone,
        website,
        profile,
        address,
        create_by,
        create_time,
        modify_by,
        modify_time
        )VALUES (
        #{id},
        #{name},
        #{email},
        #{phone},
        #{website},
        #{profile},
        #{address},
        #{createBy},
        now(),
        #{modifyBy},
        now()
        )
    </insert>

    <update id="updateManufacturer">
        UPDATE mtr_manufacturer
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="website != null and website != ''">
                website = #{website},
            </if>
            <if test="profile != null and profile != ''">
                profile = #{profile},
            </if>
            <if test="address != null and address != ''">
                address = #{address},
            </if>
            <if test="modifyBy != null and modifyBy !=''">
                modify_by = #{modifyBy},
            </if>
            modify_time = now()
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteManufacturerById">
        DELETE FROM mtr_manufacturer
        WHERE id = #{id}
    </delete>

</mapper>