<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtr.MtrManufacturerParameterMapping">
    <select id="getManufParam" resultType="cc.crrc.manage.pojo.component.ManufacturerParameterDTO">
        SELECT
        id,
        vehicle_id as vehicleId,
        component_id as componentId,
        item_name as itemName,
        item_value as itemValue
        FROM
        mtr_manufacturing_param
        WHERE
        1=1
        <if test="vehicleId != null and vehicleId !=''">
            AND vehicle_id = #{vehicleId}
        </if>
        <if test="componentId != null and componentId !=''">
            AND component_id = #{componentId}
        </if>
    </select>
    <insert id="insertManufParam">
        INSERT INTO mtr_manufacturing_param
        (
            id,
            component_id,
            vehicle_id,
            item_name,
            item_value,
            create_by,
            create_time)
        VALUES (
                #{id},
                #{componentId},
                #{vehicleId},
                #{itemName},
                #{itemValue},
                #{createBy},
                CURRENT_TIMESTAMP)
    </insert>
    <update id="updateManufParam">
        UPDATE mtr_manufacturing_param SET
        <if test="vehicleId != null and vehicleId !=''">
            vehicle_id = #{vehicleId},
        </if>
        <if test="componentId != null and componentId !=''">
            component_id = #{componentId},
        </if>
        item_name = #{itemName},
        item_value = #{itemValue},
        modify_by = #{modifyBy},
        modify_time = CURRENT_TIMESTAMP
        WHERE
        id = #{id}
    </update>
    <delete id="deleteManufParam">
        DELETE
        FROM mtr_manufacturing_param
        WHERE id = #{id}
    </delete>
</mapper>