<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtr.MtrSubsystemDictMapping">
    <sql id="subsystemDictAlias">
        t1.id,
        t1.vehicle_id AS vehicleId,
        t1.code,
        t1.value,
        t1.label,
        t1.type,
        t1.description
    </sql>

	<select id="listSubsystemDictByVehicleId" resultType="cc.crrc.manage.pojo.mtr.MtrSubsystemDictPO">
		SELECT
	        t1.id,
	        t2.id AS vehicleId,
	        t1.code,
	        t1.value,
	        t1.label,
	
	        t1.description
        FROM
            sys_dict t1
		JOIN mtr_vehicle t2 ON t2.metro_line_id = t1.line_id
		JOIN mtr_vehicle_type_relation t3 ON t3.vehicle_id = t2.id AND t3.vehicle_type_id = t1.vehicle_type_id
        WHERE
			t1.type_code = 'ass_car_system'
		AND
            t2.id = #{vehicleId}
		AND t1.del_flag = '0'
        ORDER BY
            t1.value,t1.label
	</select>
    
    <!--根据车型id删除子系统字典-->
    <delete id="deleteSubsystemDict">
        DELETE FROM
        mtr_subsystem_dict
        WHERE
        vehicle_id = #{vehicleId}
        <if test="nameCn != null and nameCn != ''">
            AND label = #{nameCn}
        </if>
    </delete>
    <!--插入子系统字典-->
    <insert id="insertSubsystemDict">
        INSERT INTO mtr_subsystem_dict(
            vehicle_id,
            code,
            value,
            label,
            type,
            description,
            create_by,
            create_time,
            modify_by,
            modify_time
        )
        VALUES(
            #{vehicleId},
            #{code},
            #{value},
            #{label},
            #{type},
            #{description},
            #{createBy},
            CURRENT_TIMESTAMP,
            #{modifyBy},
            CURRENT_TIMESTAMP
        )
    </insert>

    <insert id="addSystemFromAllDict">
        INSERT INTO mtr_subsystem_dict (
            id,
            vehicle_id,
            code,
            value,
            label,
            create_by,
            create_time,
            modify_by,
            modify_time
        )
        SELECT
            #{id},
            #{vehicleId},
            sd.code,
            sd.value,
            sd.label,
            #{userId},
            CURRENT_TIMESTAMP,
            #{userId},
            CURRENT_TIMESTAMP
        FROM sys_dict sd
        WHERE
            sd.id IN
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </insert>
</mapper>