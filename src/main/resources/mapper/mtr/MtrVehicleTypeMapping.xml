<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtr.MtrVehicleTypeMapping">
    <sql id="vehicleTypeAlias">
        mvt.id AS id,
        mvt.name AS name,
        mvt.type AS type,
        mvt.marshalling_number AS marshallingNumber,
        mvt.manufacturer_id AS manufacturerId,
        mvt.comm_type AS commType,
        mvt.remark AS remark,
        mvt.line_id AS lineId,
        mvt.vehicle_type_code AS vehicleTypeCode
    </sql>
    <!--通过id筛选车型-->
    <select id="getVehicleTypeById" resultType="cc.crrc.manage.pojo.mtr.MtrVehicleTypeVO">
        SELECT
            <include refid="vehicleTypeAlias"/>,
            mm.name
        FROM
            mtr_vehicle_type mvt
        LEFT JOIN mtr_manufacturer mm ON mvt.manufacturer_id = mm.id
        WHERE mvt.id = #{id} AND mvt.del_flag = '0'
    </select>

    <!--通过id筛选车型,并返回构型图片信息-->
    <select id="getVehicleTypeWithPictureById" resultType="cc.crrc.manage.pojo.mtr.MtrVehicleTypeDetailVO">
        SELECT
            <include refid="vehicleTypeAlias"/>,
            <if test="fileType != null and fileType != ''">
                file.id AS fileId,
                file.url AS pictureURL,
            </if>
            mm.name AS manufacturerName
        FROM
            mtr_vehicle_type mvt
        LEFT JOIN mtr_manufacturer mm ON mvt.manufacturer_id = mm.id
        <if test="fileType != null and fileType != ''">
        LEFT JOIN
            (SELECT id,"group"||'/'||file_location AS url,vehicle_type_id
            FROM mtr_vehicle_type_file mvtf
            LEFT JOIN sys_file sf ON sf.id = mvtf.file_id
            WHERE sf.type = #{fileType}
            AND sf.del_flag = 0
            ) file ON file.vehicle_type_id = mvt.id
        </if>
        WHERE
            mvt.id = #{id}
            AND mvt.del_flag = '0'
        ORDER BY mvt.create_time DESC
        LIMIT 1
    </select>

    <!--车型列表-->
    <select id="listVehicleType" resultType="cc.crrc.manage.pojo.mtr.MtrVehicleTypeVO">
        SELECT
            <include refid="vehicleTypeAlias"/>,
            mm.name AS manufacturerName
        FROM
            mtr_vehicle_type mvt
        LEFT JOIN
            mtr_manufacturer mm
        ON
            mvt.manufacturer_id = mm.id
        WHERE del_flag = 0
        ORDER BY mvt.name
    </select>
    <!--更新-->
    <update id="updateVehicleType">
        UPDATE mtr_vehicle_type
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="marshallingNumber != null">
                marshalling_number = #{marshallingNumber},
            </if>
            <if test="manufacturerId != null and manufacturerId !=''">
                manufacturer_id = #{manufacturerId},
            </if>
            <if test="commType != null and commType != ''">
                comm_type = #{commType},
            </if>
            <if test="modifyBy != null and modifyBy!=''">
                modify_by = #{modifyBy},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="lineId != null and lineId !=''">
                line_id = #{lineId},
            </if>
            <if test="vehicleTypeCode != null and vehicleTypeCode != ''">
                vehicle_type_code = #{vehicleTypeCode},
            </if>
            remark = #{remark},
            modify_time = CURRENT_TIMESTAMP
        </set>
        WHERE id = #{id}
    </update>
    <!--逻辑删除-->
    <update id="deleteVehicleType">
        UPDATE mtr_vehicle_type
        SET del_flag = 1,modify_by = #{userId},modify_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>
    <!--新增车型-->
    <insert id="insertVehicleType">
        INSERT INTO mtr_vehicle_type (
            id,
            name,
            type,
            marshalling_number,
            manufacturer_id,
            comm_type,
            create_by,
            create_time,
            modify_by,
            modify_time,
            remark,
            del_flag,
            vehicle_type_code,
            line_id
        )
        VALUES (
            #{id},
            #{name},
            #{type},
            #{marshallingNumber},
            #{manufacturerId},
            #{commType},
            #{createBy},
            CURRENT_TIMESTAMP,
            #{modifyBy},
            CURRENT_TIMESTAMP,
            #{remark},
            0,
            #{vehicleTypeCode},
            #{lineId}
        )
    </insert>
    <!--新增车型文件关系-->
    <insert id="insertVehicleTypeFileRelation">
        INSERT INTO mtr_vehicle_type_file (
            vehicle_type_id,
            file_id,
            start_date
        )
        VALUES (
            #{vehicleTypeId},
            #{fileId},
            CURRENT_TIMESTAMP
        )
    </insert>
    <!--删除车型文件关系-->
    <delete id="deleteVehicleTypeFileRelation">
        UPDATE  mtr_vehicle_type_file
        SET
        del_flag = 1,
        end_date = CURRENT_TIMESTAMP
        WHERE
            vehicle_type_id = #{vehicleTypeId} AND
            file_id = #{fileId}
    </delete>
    <!--根据车型id获取车型文件列表-->
    <select id="listVehicleTypeFile" resultType="cc.crrc.manage.pojo.SysFilePO">
        SELECT
            t.id,
            t.name,
            t.url,
            t.type,
            t.format,
            t.size,
            t.hash_code AS hashCode,
            t.del_flag AS delFlag,
            t.create_by AS createBy,
            t.create_time AS createTime,
            t.modify_by AS modifyBy,
            t.modify_time AS modifyTime,
            t.remark,
            t.group,
            t.file_location AS fileLocation
        FROM
            sys_file t
        LEFT JOIN
            mtr_vehicle_type_file mvtf
        ON
            mvtf.file_id = t.id
        WHERE
            mvtf.vehicle_type_id = #{vehicleTypeId}
            AND mvtf.del_flag = 0
            AND (t.type IS NULL
                OR t.type != 'structurePic')
    </select>

    <update id="clearManufacturerFromVehicleType">
        UPDATE mtr_vehicle_type
        <set>
            manufacturer_id = null,
            <if test="modifyBy != null and modifyBy !=''">
                modify_by = #{modifyBy},
            </if>
            modify_time = now()
        </set>
        WHERE manufacturer_id = #{manufacturerId}
    </update>

    <select id="getVehicleTypeIdByEmployeeId" resultType="java.lang.Long">
        SELECT
            vehicle_type_id
        FROM
            mtr_vehicle_type_contacts
        WHERE
            manufacturer_employee_id = #{id}
    </select>

    <delete id="deleteVehicleTypeContactsByEmployeeId">
        DELETE
        FROM mtr_vehicle_type_contacts
        WHERE
            manufacturer_employee_id = #{id}
    </delete>
    
    <insert id="insertMtrVehicleTypeContacts">
        INSERT INTO mtr_vehicle_type_contacts (
            manufacturer_employee_id,
            vehicle_type_id
        )
        VALUES (
            #{manufacturerEmployeeId},
            #{vehicleTypeId}
        )
    </insert>
    
     <delete id="deleteMtrVehicleTypeContacts">
        DELETE
        FROM mtr_vehicle_type_contacts
        WHERE
         vehicle_type_id= #{vehicleTypeId}
         <if test="manufacturerEmployeeId != null and manufacturerEmployeeId !=''">
             AND manufacturer_employee_id = #{manufacturerEmployeeId}
         </if>
    </delete>
    
    <select id="selectEmployees" resultType="cc.crrc.manage.pojo.mtr.ManufacturerEmployeePO">
        SELECT
          m.id,
          m.name,
          m.gender,
          m.manufacturer_id AS manufacturerId,
          m.email,
          m.phone,
          m.profile,
          m.type,
          m.create_by AS createBy,
          m.create_time AS createTime,
          m.modify_by AS modifyBy,
          m.modify_time AS modifyTime,
          m.remark
        FROM
            mtr_manufacturer_employee m
        JOIN mtr_vehicle_type_contacts mctc
        ON m.id=mctc.manufacturer_employee_id
        WHERE mctc.vehicle_type_id=#{vehicleTypeId}
    </select>
    <select id="validCount" resultType="java.lang.Integer">
    	SELECT COUNT(1)
    	FROM mtr_vehicle_type_contacts
    	WHERE
    		vehicle_type_id=#{vehicleTypeId}
    	AND
    		manufacturer_employee_id=#{manufacturerEmployeeId}
    </select>
    <select id="getVehicleTypeIdByVehicleCode" resultType="java.lang.String">
        SELECT
            t1.vehicle_type_id
        FROM
            mtr_vehicle_type_relation t1,
            mtr_vehicle t2
        WHERE
            t1.vehicle_id = t2.ID
          AND t2.vehicle_code = #{vehicleCode}
    </select>
</mapper>