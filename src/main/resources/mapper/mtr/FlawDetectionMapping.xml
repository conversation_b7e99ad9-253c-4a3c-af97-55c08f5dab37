<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtr.FlawDetectionMapping">

<select id="queryFlawDetectionList" parameterType="cc.crrc.manage.pojo.mtr.FlawDetectionPO"
        resultType="cc.crrc.manage.pojo.mtr.FlawDetectionVO">
    SELECT
    id,
    original,
    present,
    product,
    check_date AS checkDate
    FROM
    mtr_flaw_detection_record
    <where>
    del_flag = '0'
    AND type = #{type}
    <if test="original != null and original != ''">
        AND original = #{original}
    </if>
    <if test="present != null and present != ''">
        AND present = #{present}
    </if>
    <if test="product != null and product != ''">
        AND product LIKE '%'||#{product}||'%'
    </if>
    <if test="startTime != null">
        AND check_date > #{startTime}
    </if>
    <if test="endTime != null">
        AND #{endTime} > check_date
    </if>
    </where>
    </select>

    <select id="queryFlawDetectionInfo" parameterType="string" resultType="cc.crrc.manage.pojo.mtr.FlawDetectionVO">
        SELECT
        id,
        original,
        present,
        type,
        basic_info_json AS basicInfo,
        situation,
        self_check AS selfCheck,
        self_check_date AS selfCheckDate,
        mutual_check AS mutualCheck,
        mutual_check_date AS mutualCheckDate,
        special_check AS specialCheck,
        special_check_date AS specialCheckDate,
        director,
        check_date AS checkDate,
        check_result AS checkResult
        FROM
        mtr_flaw_detection_record
        WHERE
        id = #{id}
        AND
        del_flag = '0'
    </select>

    <select id="queryFlawDetectionProduct" resultType="cc.crrc.manage.pojo.mtr.FlawDetectionProductVO">
        SELECT
        id,
        code AS number,
        crack,
        amount AS quantity,
        check_date AS date,
        evaluation_result AS result
        FROM
        mtr_flaw_detection_product
        WHERE
        flaw_detection_id = #{flawDetectionId}
    </select>

    <insert id="saveFlawDetection" parameterType="cc.crrc.manage.pojo.mtr.FlawDetectionPO" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO
        mtr_flaw_detection_record
        (
        id,
        original,
        present,
        type,
        product,
        basic_info_json,
        situation,
        self_check,
        self_check_date,
        mutual_check,
        mutual_check_date,
        special_check,
        special_check_date,
        director,
        check_date,
        check_result,
        del_flag,
        create_by,
        create_time
        )VALUES (
        #{id},
        #{original},
        #{present},
        #{type},
        #{product},
        #{basicInfo},
        #{situation},
        #{selfCheck},
        #{selfCheckDate},
        #{mutualCheck},
        #{mutualCheckDate},
        #{specialCheck},
        #{specialCheckDate},
        #{director},
        #{checkDate},
        #{checkResult},
        0,
        #{createBy},
        CURRENT_TIMESTAMP
        )
    </insert>

    <insert id="saveFlawDetectionProduct" parameterType="cc.crrc.manage.pojo.mtr.FlawDetectionProductPO">
        INSERT INTO
        mtr_flaw_detection_product
        (
        id,
        flaw_detection_id,
        type,
        code,
        crack,
        amount,
        check_date,
        evaluation_result
        )VALUES (
        #{id},
        #{flawDetectionId},
        #{type},
        #{number},
        #{crack},
        #{quantity},
        #{date},
        #{result}
        )
    </insert>

    <update id="changeFlawDetection" parameterType="cc.crrc.manage.pojo.mtr.FlawDetectionPO">
        UPDATE mtr_flaw_detection_record
        <set>
        <if test="original != null">
            original = #{original},
        </if>
        <if test="present != null and present != ''">
            present = #{present},
        </if>
        <if test="product != null and product != ''">
            product = #{product},
        </if>
        <if test="basicInfo != null and basicInfo != ''">
            basic_info_json = #{basicInfo},
        </if>
        <if test="situation != null and situation != ''">
            situation = #{situation},
        </if>
        <if test="selfCheck != null and selfCheck != ''">
            self_check = #{selfCheck},
        </if>
        <if test="selfCheckDate != null and selfCheckDate != ''">
            self_check_date = #{selfCheckDate},
        </if>
        <if test="mutualCheck != null and mutualCheck != ''">
            mutual_check = #{mutualCheck},
        </if>
        <if test="mutualCheckDate != null and mutualCheckDate != ''">
            mutual_check_date = #{mutualCheckDate},
        </if>
        <if test="specialCheck != null and specialCheck != ''">
            special_check = #{specialCheck},
        </if>
        <if test="specialCheckDate != null and specialCheckDate != ''">
            special_check_date = #{specialCheckDate},
        </if>
        <if test="director != null and director != ''">
            director = #{director},
        </if>
        <if test="checkDate != null and checkDate != ''">
            check_date = #{checkDate},
        </if>
        <if test="checkResult != null and checkResult != ''">
            check_result = #{checkResult},
        </if>
            modifty_by = #{modifyBy},
            moddify_time = CURRENT_TIMESTAMP
        </set>
        WHERE
        id = #{id}
    </update>

    <delete id="deleteFlawDetectionProduct" parameterType="string">
        DELETE FROM
        mtr_flaw_detection_product
        WHERE
        flaw_detection_id = #{flawDetectionId}
    </delete>
    
    <update id="changeFlawDetectionDelFlag" parameterType="string">
        UPDATE mtr_flaw_detection_record
        SET
        del_flag = '1'
        WHERE
        id=#{id}
    </update>
</mapper>