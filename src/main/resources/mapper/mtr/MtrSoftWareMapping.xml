<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtr.MtrSoftWareMapping">
    <sql id="manufacturerAlias">
        ms.id,
        ms.name,
        ms.version,
        ms.description,
        ms.create_by AS createBy,
        ms.create_time AS createTime,
        ms.modify_by AS modifyBy,
        ms.modify_time AS modifyTime,
        ms.vehicle_structure_code AS vehicleStructureCode,
        ms.vehicle_code AS vehicleCode,
        ms.end_time AS endTime,
        ms.signal_name_en AS signalNameEn,
        ms.operator,
        ms.effective_time AS effectiveTime,
        ms.line_id AS lineId
    </sql>


    <insert id="insertSoftWareInfo" parameterType="cc.crrc.manage.pojo.mtr.MtrSoftWarePO">
        INSERT INTO mtr_software
        (id,
         name,
         version,
         description,
         create_by,
         create_time,
         modify_by,
         modify_time,
         vehicle_structure_code,
         vehicle_code,
         end_time,
         signal_name_en,
         operator,
         effective_time,
         line_id,
         subsystem,
         location)
        VALUES (#{id},
                #{name},
                #{version},
                #{description},
                #{createBy},
                CURRENT_TIMESTAMP,
                #{modifyBy},
                CURRENT_TIMESTAMP,
                #{vehicleStructureCode},
                #{vehicleCode},
                #{endTime},
                #{signalNameEn},
                #{operator},
                #{effectiveTime},
                #{lineId},
                #{subsystem},
                #{location})
    </insert>
    <insert id="insertSoftwareManualHistory" parameterType="cc.crrc.manage.pojo.mtr.MtrSoftWarePO">
        INSERT INTO mtr_software_manual_history
        (id,
         name,
         version,
         description,
         create_by,
         create_time,
         modify_by,
         modify_time,
         vehicle_structure_code,
         vehicle_code,
         end_time,
         signal_name_en,
         operator,
         effective_time,
         line_id,
         subsystem,
         location
         )VALUES (
            #{id},
            #{name},
            #{version},
            #{description},
            #{createBy},
            CURRENT_TIMESTAMP,
            #{modifyBy},
            CURRENT_TIMESTAMP,
            #{vehicleStructureCode},
            #{vehicleCode},
            #{endTime},
            #{signalNameEn},
            #{operator},
            #{effectiveTime},
            #{lineId},
            #{subsystem},
            #{location}
        )
    </insert>

    <update id="updateSoftWareForEndTime">
        UPDATE mtr_software
        SET end_time = #{effectiveTime},
            modify_time = CURRENT_TIMESTAMP
        WHERE
            signal_name_en = #{signalNameEn}
          AND end_time IS NULL
          AND vehicle_code = #{vehicleCode}
    </update>

    <update id="updateUpdaterInfo" parameterType="java.lang.String">
        UPDATE mtr_software
        SET operator = #{updater},
            modify_by = #{userId},
            modify_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <select id="softWareInfoList" resultType="cc.crrc.manage.pojo.mtr.MtrSoftWareVO">
        SELECT
        dad.id,
        dad.name,
        dad.version,
        dad.description,
        dad.create_by AS createBy,
        dad.create_time AS createTime,
        dad.modify_by AS modifyBy,
        dad.modify_time AS modifyTime,
        msm.structure_code AS vehicleStructureCode,
        dad.vehicle_code AS vehicleCode,
        dad.effective_time AS effectiveTime,
        dad.end_time AS endTime,
        dad.signal_name_en AS signalNameEn,
        dad.operator,
        dad.line_id AS lineId,
        ml.name AS lineName,
        mvtr.vehicle_type_id AS vehicleTypeId,
        mvt.name AS vehicleTypeName
        FROM mtr_software dad
        LEFT JOIN mtr_software_mapping msm ON dad.signal_name_en = msm.signal_name_en
        LEFT JOIN mtr_vehicle mv ON mv.vehicle_code = dad.vehicle_code
        LEFT JOIN mtr_vehicle_type_relation mvtr ON mv.id = mvtr.vehicle_id
        LEFT JOIN mtr_vehicle_type mvt ON mvtr.vehicle_type_id = mvt.id AND msm.vehicle_type_id = mvt.id
        LEFT JOIN mtr_line ml ON ml.id = mv.metro_line_id
        WHERE
        mv.del_flag = 0
        AND mvt.del_flag = 0
        AND ml.del_flag = 0
        AND msm.del_flag = 0
        AND dad.end_time is null
        <if test="lineId != null and lineId != ''">
            AND ml.id = #{lineId}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId != ''">
            AND mvt.id = #{vehicleTypeId}
        </if>
        <if test="vehicleCode != null and vehicleCode != ''">
            AND dad.vehicle_code = #{vehicleCode}
        </if>
        <if test="name != null and name != ''">
            AND dad.name LIKE '%'||#{name}||'%'
        </if>
        <if test="signalNameEn != null and signalNameEn != ''">
            AND dad.signal_name_en LIKE '%'||#{signalNameEn}||'%'
        </if>
        <if test="version != null and version != ''">
            AND dad.version LIKE '%'||#{version}||'%'
        </if>
        <if test="operator != null and operator != ''">
            AND dad.operator LIKE '%'||#{operator}||'%'
        </if>
        <if test="effectiveTimeStart != null and effectiveTimeStart != '' and effectiveTimeStart != null and effectiveTimeEnd != ''">
            AND dad.effective_time BETWEEN #{effectiveTimeStart} AND #{effectiveTimeEnd}
        </if>
        ORDER BY dad.vehicle_code ASC, LENGTH(dad.name) ASC, dad.name ASC, dad.effective_time DESC
    </select>

    <select id="getVersionResumeForExcel"
            resultType="cc.crrc.manage.pojo.excel.software.MtrSoftWareForExcelVO">
        SELECT ms.id,
        ms.name,
        ms.version,
        ms.description,
        msm.structure_code AS vehicleStructureCode,
        ms.vehicle_code           AS vehicleCode,
        ms.modify_time               AS modifyTime,
        ms.signal_name_en         AS signalNameEn,
        ms.operator,
        ms.effective_time         AS effectiveTime,
        ms.line_id                AS lineId,
        mvt.name                  AS vehicleTypeName,
        ml.name                   AS lineName,
        ms.end_time AS endTime
        FROM mtr_software ms
        LEFT JOIN mtr_software_mapping msm ON ms.signal_name_en = msm.signal_name_en
        LEFT JOIN mtr_vehicle mv ON mv.vehicle_code = ms.vehicle_code
        LEFT JOIN mtr_vehicle_type_relation mvtr ON mv.id = mvtr.vehicle_id
        LEFT JOIN mtr_vehicle_type mvt ON mvtr.vehicle_type_id = mvt.id AND msm.vehicle_type_id = mvt.id
        LEFT JOIN mtr_line ml ON ml.id = mv.metro_line_id
        WHERE mv.del_flag = 0
        AND mvt.del_flag = 0
        AND ml.del_flag = 0
        AND msm.del_flag = 0
        AND ms.end_time is null
        <if test="lineId != null and lineId !=''">
            AND ml.id = #{lineId}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId !=''">
            AND mvt.id = #{vehicleTypeId}
        </if>
        <if test="name != null and name !=''">
            AND ms.name LIKE '%'||#{name}||'%'
        </if>
        <if test="signalNameEn != null and signalNameEn !=''">
            AND ms.signal_name_en LIKE '%'||#{signalNameEn}||'%'
        </if>
        <if test="version != null and version !=''">
            AND ms.version LIKE '%'||#{version}||'%'
        </if>
        <if test="operator != null and operator !=''">
            AND ms.operator LIKE '%'||#{operator}||'%'
        </if>
        <if test="effectiveTimeStart != null and effectiveTimeStart !='' and effectiveTimeEnd != null and effectiveTimeEnd !=''">
            AND ms.effective_time BETWEEN #{effectiveTimeStart} AND #{effectiveTimeEnd}
        </if>
        <if test="vehicleCode != null and vehicleCode !=''">
            AND ms.vehicle_code = #{vehicleCode}
        </if>
        ORDER BY ms.vehicle_code ASC, LENGTH(ms.name) ASC, ms.name ASC, ms.effective_time DESC
    </select>

    <select id="duplicationChecking" resultType="integer">
        SELECT
        count(*)
        FROM mtr_software ms
        <where>
            ms.end_time is NULL
            AND ms.line_id = #{lineId}
            AND ms.signal_name_en = #{signalNameEn}
            AND ms.vehicle_code = #{vehicleCode}
            AND ms.version = #{version}
        </where>
    </select>

    <select id="countVersions" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM mtr_software ms
        <where>
            ms.end_time is NOT NULL
            AND ms.signal_name_en = #{nameEn}
            AND ms.vehicle_code = #{code}
        </where>
    </select>

    <select id="listResume" resultType="cc.crrc.manage.pojo.excel.software.MtrSoftWareForExcelVO">
        SELECT ms.id,
        ms.name,
        ms.version,
        ms.description,
        msm.structure_code AS vehicleStructureCode,
        ms.vehicle_code           AS vehicleCode,
        ms.modify_time               AS modifyTime,
        ms.signal_name_en         AS signalNameEn,
        ms.operator,
        ms.effective_time         AS effectiveTime,
        ms.line_id                AS lineId,
        mvt.name                  AS vehicleTypeName,
        ml.name                   AS lineName,
        ms.end_time AS endTime
        FROM mtr_software ms
        LEFT JOIN mtr_software_mapping msm ON ms.signal_name_en = msm.signal_name_en
        LEFT JOIN mtr_vehicle mv ON mv.vehicle_code = ms.vehicle_code
        LEFT JOIN mtr_vehicle_type_relation mvtr ON mv.id = mvtr.vehicle_id
        LEFT JOIN mtr_vehicle_type mvt ON mvtr.vehicle_type_id = mvt.id AND msm.vehicle_type_id = mvt.id
        LEFT JOIN mtr_line ml ON ml.id = mv.metro_line_id
        WHERE mv.del_flag = 0
        AND mvt.del_flag = 0
        AND ml.del_flag = 0
        AND msm.del_flag = 0
        AND ms.end_time is not null
        <if test="signalName != null and signalName !=''">
            AND ms.signal_name_en = #{signalName}
        </if>
        <if test="code != null and code !=''">
            AND ms.vehicle_code = #{code}
        </if>
        ORDER BY ms.vehicle_code ASC, LENGTH(ms.name) ASC, ms.name ASC, ms.effective_time DESC, ms.end_time DESC
    </select>
    <select id="listHistoryVersion" resultType="cc.crrc.manage.pojo.mtr.MtrSoftWareResumeVO">
        SELECT
        son.id AS sonId,
        son.name AS sonName,
        son.version AS sonVersion,
        son.description AS sonDescription,
        son.create_by AS sonCreateBy,
        son.create_time AS sonCreateTime,
        son.modify_by AS sonModifyBy,
        son.modify_time AS sonModifyTime,
        (CASE WHEN son.end_time is not null THEN msm.structure_code ELSE null END) AS sonVehicleStructureCode,
        son.vehicle_code AS sonVehicleCode,
        son.effective_time AS sonEffectiveTime,
        son.end_time AS sonEndTime,
        son.signal_name_en AS sonSignalNameEn,
        son.operator AS sonOperator,
        son.line_id AS sonLineId
        FROM mtr_software son
        LEFT JOIN mtr_software_mapping msm ON son.signal_name_en = msm.signal_name_en
        LEFT JOIN mtr_vehicle mv ON mv.vehicle_code = son.vehicle_code
        LEFT JOIN mtr_vehicle_type_relation mvtr ON mv.id = mvtr.vehicle_id
        LEFT JOIN mtr_vehicle_type mvt ON mvtr.vehicle_type_id = mvt.id AND msm.vehicle_type_id = mvt.id
        LEFT JOIN mtr_line ml ON ml.id = mv.metro_line_id
        WHERE
        mv.del_flag = 0
        AND son.end_time is not null
        AND mvt.del_flag = 0
        AND ml.del_flag = 0
        AND msm.del_flag = 0
        AND son.vehicle_code = #{vehicleCode}
        AND son.signal_name_en = #{signalNameEn}
        ORDER BY son.effective_time DESC
    </select>

    <select id="ListAllSoftwareVersion" resultType="cc.crrc.manage.pojo.mtr.MtrSoftwareVersionVO">
        select name,
        "version" AS maxversion,
        vehicle_code AS vehicleCode,
        subsystem,
        location
        from mtr_software
        where end_time is null
        order by subsystem
    </select>

    <select id="ListSoftwareVersionByLocation" resultType="cc.crrc.manage.pojo.mtr.MtrSoftwareVersionVO">
        select name,
        "version" AS maxversion,
        vehicle_code AS vehicleCode,
        subsystem,
        location,
        signal_name_en AS signalNameEn
        from mtr_software
        where end_time is null
        <if test="softwareName != null and softwareName!=''">
            and name = #{softwareName}
        </if>
        and subsystem = #{subsystem}
        and vehicle_code = #{vehicleCode}
        order by name
    </select>

    <select id="listVehicleHistoryVersion" resultType="cc.crrc.manage.pojo.mtr.MtrSoftwareVersionVO">
        select
            ms.create_time,
            ms.version as maxversion,
            ms.location,
            ms.vehicle_code,
            ms.name,
            ms.effective_time as effectiveTime,
            ms.end_time as endTime
        from mtr_software ms
        where ms.vehicle_code like #{vehicleCode}
          and ms.subsystem like #{subsystem}
          and ms.name like #{softwareName}
          and ms.location like #{location}
          and ms.end_time is not null
        order by ms.end_time asc
    </select>

    <select id="listManualSoftware" resultType="cc.crrc.manage.pojo.mtr.MtrSoftwareVersionVO">
        SELECT DISTINCT ON (msmh.signal_name_en)
            msmh.id,
            msmh.name,
            msmh.version AS maxversion,
            msmh.vehicle_code AS vehicleCode,
            msmh.subsystem,
            msmh.location,
            msmh.signal_name_en AS signalNameEn,
            msmh.line_id AS lineId,
            ms.version AS oldVersion
        FROM
            mtr_software_manual_history msmh
        LEFT JOIN mtr_software ms ON ms.signal_name_en = msmh.signal_name_en
        WHERE
            msmh.status = 0
        AND
            ms.end_time IS NULL
        ORDER BY
            msmh.signal_name_en,msmh.create_time ASC
    </select>

    <update id="updateManualSoftware">
		UPDATE mtr_software_manual_history SET status = #{status} WHERE id = #{id}
	</update>
</mapper>