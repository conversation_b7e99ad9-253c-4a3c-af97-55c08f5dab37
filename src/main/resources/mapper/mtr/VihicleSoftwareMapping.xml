<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtr.VihicleSoftwareMapping">


    <select id="VehicleSoftwareList" resultType="cc.crrc.manage.pojo.mtr.VehicleSoftwarePO">
        SELECT
        ms.id,
        ms. NAME as softwareName,
        ms. VERSION as version,
        ms.description as description,
        ms.modify_time as modifyTime,
        mvt.id AS vehicleTypeId,
        mvt.line_id AS lineId,
        line.name AS lineName,
        mvt. NAME AS vehicleTypeName,
        sct.id AS componentTypeId,
        sct.name_cn AS componentTypeName,
        su. NAME AS createByName,
        mm. NAME AS manufacturerName,
        mm.id AS manufacturerId
        FROM
        mtr_software ms
        LEFT JOIN mtr_component_type_software mcts ON ms. ID = mcts.software_id
        LEFT JOIN stru_component_type sct ON sct. ID = mcts.component_type_id
        LEFT JOIN mtr_vehicle_type mvt ON mcts.vehicle_type_id = mvt. ID
        LEFT JOIN mtr_line line ON mvt.line_id = line. ID
        LEFT JOIN mtr_manufacturer mm ON mm. ID = ms.manufacturer_id
        LEFT JOIN sys_user su ON ms.modify_by = su. ID
        <where>
        	mvt.del_flag = 0
            <if test="id != null and id != ''">
                AND ms.id = #{id}
            </if>
            <if test="softwareName != null and softwareName != ''">
                AND ms.name LIKE '%'||#{softwareName}||'%'
            </if>
            <if test="version != null and version != ''">
                AND ms.version LIKE '%'||#{version}||'%'
            </if>
            <if test="manufacturerId != null and manufacturerId != ''">
                AND ms.manufacturer_id = #{manufacturerId}
            </if>
            <if test="createByName != null and createByName != ''">
                AND su.name LIKE '%'||#{createByName}||'%'
            </if>
            <if test="vehicleTypeId != null and vehicleTypeId != ''">
                AND mvt.id = #{vehicleTypeId}
            </if>
            <if test="lineId != null and lineId != ''">
                AND mvt.line_id = #{lineId}
            </if>
            <if test="componentTypeId != null and componentTypeId != ''">
                AND sct.id = #{componentTypeId}
            </if>
        </where>
        ORDER BY ms.modify_time DESC
    </select>
	<select id="getSoftware" resultType="cc.crrc.manage.pojo.mtr.VehicleSoftwarePO">
		SELECT
	        ms.id,
	        ms. NAME as softwareName,
	        ms. VERSION as version,
	        ms.description as description,
	        ms.modify_time as modifyTime,
			su. NAME AS createByName,
			mm. NAME AS manufacturerName,
	        mm.id AS manufacturerId
		FROM
		        mtr_software ms
		JOIN mtr_component_software cs on cs.software_id = ms.id
		LEFT JOIN mtr_manufacturer mm ON mm. ID = ms.manufacturer_id
		LEFT JOIN sys_user su ON ms.modify_by = su. ID
		WHERE cs.component_id = #{componentId}
		AND cs.valid = true
	</select>
	<select id="getVehicleSoftwareList" resultType="cc.crrc.manage.pojo.mtr.VehicleSoftwarePO">
		SELECT
	        ms.id,
	        ms. NAME as softwareName,
	        ms. VERSION as version,
	        ms.description as description,
	        ms.modify_time as modifyTime,
			su. NAME AS createByName,
			mm. NAME AS manufacturerName,
	        mm.id AS manufacturerId
		FROM
		    mtr_software ms
		LEFT JOIN mtr_manufacturer mm ON mm. ID = ms.manufacturer_id
		LEFT JOIN sys_user su ON ms.modify_by = su. ID
		JOIN mtr_component_type_software mts ON mts.software_id = ms.id
		WHERE mts.vehicle_type_id = #{vehicleTypeId}
		AND mts.component_type_id = #{componentTypeId}
		<if test="softwareName != null and softwareName !=''">
			AND ms.name = #{softwareName}
		</if>
	
	</select>

     <insert id="addVehicleSoftware"  parameterType="cc.crrc.manage.pojo.mtr.VehicleSoftwarePO" >
        INSERT INTO mtr_software
        (
        id,
        name,
        version,
        manufacturer_id,
        description,
        create_by,
        create_time,
        modify_by,
        modify_time
        )VALUES (
        #{id},
        #{softwareName},
        #{version},
        #{manufacturerId},
        #{description},
        #{createById},
        now(),
        #{modifyById},
        now()
        )
    </insert>


    <update id="updateVehicleSoftware">
        UPDATE mtr_software SET
                name = #{softwareName},
                version = #{version},
                manufacturer_id = #{manufacturerId},
                description = #{description},
                create_by = #{createById},
                create_time = now(),
                modify_by = #{modifyById},
            modify_time = now()
        WHERE id = #{id}
    </update>

<!--    <update id="updateComponentTypeSoftware">-->
<!--        UPDATE mtr_component_type_software SET-->
<!--                name = #{name},-->
<!--                version = #{version},-->
<!--                manufacturer_id = #{manufacturerId},-->
<!--                description = #{description},-->
<!--                create_by = #{createById},-->
<!--                create_time = now(),-->
<!--                modify_by = #{modifyById},-->
<!--            modify_time = now()-->
<!--        WHERE id = #{id}-->
<!--    </update>-->

    <insert id="instcomponentTypeSoftware">
        INSERT INTO mtr_component_type_software
        (
        component_type_id,
        software_id,
        vehicle_type_id
        )VALUES (
        #{componentTypeId},
        #{id},
        #{vehicleTypeId}
        )
    </insert>

    <insert id="configVehicleSoftware">
        INSERT INTO mtr_component_software
        (
        id,
        vehicle_id,
        component_id,
        software_id,
        update_time,
        plan_time
        )VALUES (
        #{id},
        #{vehicleId},
        #{componentId},
        #{softwareId},
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP
        )
    </insert>


    <insert id="saveVehicleSoftwareFile">
        INSERT INTO mtr_software_file
        (
        software_id,
        file_id,
        start_date
        )VALUES (
        #{id},
        #{fileId},
        CURRENT_TIMESTAMP
        )
    </insert>


    <select id="getVehicleSoftwareFile" resultType="cc.crrc.manage.pojo.SysFilePO">
        SELECT
        sf.id,
        sf.name,
        sf.url,
        sf.type,
        sf.format,
        sf.size,
        sf.hash_code AS hashCode,
        sf.group,
        sf.file_location AS fileLocation
        FROM
        sys_file sf
        LEFT JOIN mtr_software_file msf ON msf.file_id = sf.id
        WHERE msf.software_id=#{id}
        AND msf.del_flag = 0
        ORDER BY sf.create_time DESC
    </select>


    <delete id="deleteVehicleSoftwareFile">
        UPDATE
        mtr_software_file
        SET
        del_flag = 1,
        end_date = CURRENT_TIMESTAMP
        WHERE
        file_id = #{fileId}
    </delete>

    <select id="getComponentSoftware" resultType="java.util.HashMap">
        SELECT
        id
        FROM
        mtr_component_software
        WHERE software_id=#{softwareId}
    </select>
    <select id="getComponentSoftwareListByComponentID" resultType="java.util.HashMap">
        SELECT
        id,
        vehicle_id AS vehicleId,
        component_id AS componentId,
        software_id AS softwareId
        FROM
        mtr_component_software
        WHERE software_id=#{softwareId} AND component_id=#{componentId}
    </select>

    <select id="getComponentTypeSoftware" resultType="java.util.HashMap">
        SELECT
        *
        FROM
        mtr_component_type_software
        WHERE software_id=#{softwareId} AND vehicle_type_id=#{vehicleTypeId} AND component_type_id=#{componentTypeId}
    </select>


    <select id="getComponentListByTypeID" resultType="cc.crrc.manage.pojo.mtr.ConfigSoftwareComponentPO">
        SELECT
        sc.id AS componentId,
        sc.component_type_id AS componentTypeId,
        sc.serial_number AS serialNumber,
        sct.name_cn AS componentTypeName,
        mv.name_cn AS vehicleName,
        mv.id AS vehicleId,
        svsc.name_cn AS vehicleTypeStructureName,
        svsc.structure_type AS structureType,
        sct.catalog AS catalog
        FROM
        stru_component sc
        LEFT JOIN stru_component_type sct ON sct.id = sc.component_type_id
        LEFT JOIN  stru_vehicle_structure_component svsc ON svsc.component_id = sc.id
        LEFT JOIN  mtr_vehicle mv ON mv.id = svsc.vehicle_id
        WHERE sc.component_type_id = #{componentTypeId} AND sc.del_flag = 0
        ORDER BY mv.ID ASC
    </select>

    <delete id="delSoftware">
        DELETE
        FROM mtr_software
        WHERE
        id = #{softwareId}
    </delete>

<!--    <delete id="delComponentTypeSoftware">-->
<!--        DELETE-->
<!--        FROM mtr_component_type_software-->
<!--        WHERE-->
<!--        software_id = #{softwareId} AND component_type_id =#{componentTypeId}-->
<!--    </delete>-->

    <delete id="delComponentSoftware">
        DELETE FROM mtr_component_software
        WHERE id IN (SELECT mcs.id
        FROM mtr_component_software mcs
        LEFT JOIN stru_component sc ON sc.id = mcs.component_id
        LEFT JOIN stru_component_type sct ON sct.id = sc.component_type_id
        WHERE sct.id = #{componentTypeId})
    </delete>

	<update id="changeValid">
		UPDATE mtr_component_software SET
		valid = false
		WHERE component_id = #{componentId}
		AND software_id in (SELECT id FROM mtr_software WHERE name = #{softwareName})
	</update>
</mapper>