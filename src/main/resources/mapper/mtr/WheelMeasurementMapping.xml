<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtr.WheelMeasurementMapping">

    <select id="queryVehicle" resultType="cc.crrc.manage.pojo.mtr.WheelMeasurementVO">
        SELECT
        id,
        vehicle_code AS vehicleCode,
        mileage,
        status,
        checkdate,
        recorder
        FROM
        mtr_wheel_measurement_vehicle
        WHERE
        del_flag = '0'
        <if test="vehicleCode != null and vehicleCode != ''">
            AND vehicle_code = #{vehicleCode}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="startTime != null ">
            AND checkdate > #{startTime}
        </if>
        <if test="endTime != null ">
            AND #{endTime} > checkdate
        </if>
    </select>

    <select id="queryData" resultType="cc.crrc.manage.pojo.mtr.WheelMeasurementDataVO">
        SELECT
        name_cn as nameCn,
        frame,
        axle,
        location,
        diameters as wheelDiameter,
        thickness as flangeThickness,
        height as flangeHeight,
        qr as QR,
        runout as diameterRunout,
        innerside_distance as insideDistance,
        case when
        max(diameters) over(partition by name_cn,axle) - min(diameters) over(partition by name_cn,axle)>2
        then 1 else 0 end labelLocation,
        case when
        max(diameters) over(partition by name_cn,frame) - min(diameters) over(partition by name_cn,frame)>4
        then 1 else 0 end labelAxle,
        case when
        max(diameters) over(partition by name_cn) - min(diameters) over(partition by name_cn)>6
        then 1 else 0 end labelVehicle,
        case when diameters >= 770 then '0' when diameters &lt; 770 then '1' else null end diametersRange,
        case when thickness >= 22 then '0' when thickness &lt; 22 then '1' else null end thicknessRange,
        case when height &lt;= 31 then '0' when height > 31 then '1' else null end heightRange,
        case when qr >= 6.5 and qr &lt;= 12.7 then '0' when qr &lt; 6.5 or qr > 12.7 then '1'  else null end qrRange,
        case when runout &lt;= 0.5 then '0' when runout > 0.5 then '1' else null end runoutRange,
        case when innerside_distance >= 1351 and innerside_distance &lt;= 1357  then '0'
        when innerside_distance &lt; 1351 or innerside_distance > 1357  then '1' else null end insideRange
        from mtr_wheel_measurement_data mwmd
        where measurement_vehicle_id = #{id}
        order by id
    </select>

    <insert id="saveVehicle" parameterType="cc.crrc.manage.pojo.mtr.WheelMeasurementPO" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into mtr_wheel_measurement_vehicle
        (
        id,
        vehicle_code,
        mileage,
        status,
        recorder,
        checkdate,
        del_flag,
        create_by,
        create_time
        )values(
        #{id},
        #{vehicleCode},
        #{mileage},
        #{status},
        #{recorder},
        #{checkDate},
        0,
        #{createBy},
        CURRENT_TIMESTAMP
        )
    </insert>
    <update id="updateVehicle" parameterType="cc.crrc.manage.pojo.mtr.WheelMeasurementPO">
        update mtr_wheel_measurement_vehicle
        <trim prefix="SET" suffixOverrides=",">
            <if test="vehicleCode != null and vehicleCode != ''">
                vehicle_code = #{vehicleCode},
            </if>
            <if test="mileage != null">
                mileage = #{mileage},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="recorder != null and recorder != ''">
                recorder = #{recorder},
            </if>
            <if test="checkDate != null and checkDate != ''">
                checkdate = #{checkDate},
            </if>
            modify_by = #{modifyBy},
            modify_time = CURRENT_TIMESTAMP
        </trim>
            where
            id = #{id}
    </update>

    <insert id="saveData" parameterType="cc.crrc.manage.pojo.mtr.WheelMeasurementDataPO">
        insert into mtr_wheel_measurement_data
        (
        id,
        measurement_vehicle_id,
        name_cn,
        frame,
        axle,
        location,
        diameters,
        thickness,
        height,
        qr,
        runout,
        innerside_distance
        )values(
        #{id},
        #{measurementVehicleId},
        #{nameCn},
        #{frame},
        #{axle},
        #{location},
        #{wheelDiameter},
        #{flangeThickness},
        #{flangeHeight},
        #{qR},
        #{diameterRunout},
        #{insideDistance}
        )
    </insert>


    <update id="deleteVehicle" parameterType="string">
        update mtr_wheel_measurement_vehicle
        set del_flag = '1'
        where id=#{id}
    </update>

    <delete id="deleteData">
        delete from
        mtr_wheel_measurement_data
        where
        measurement_vehicle_id =#{id}
    </delete>
</mapper>