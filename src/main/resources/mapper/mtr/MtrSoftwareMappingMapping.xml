<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtr.MtrSoftwareVersionMappingMapping">
    <resultMap id="BaseResultMap" type="cc.crrc.manage.pojo.mtr.MtrSoftwareVersionMappingVO">
        <!--@mbg.generated-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="signal_name_en" jdbcType="VARCHAR" property="signalNameEn"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="signal_list" jdbcType="VARCHAR" property="signalList"/>
        <result column="line_id" jdbcType="VARCHAR" property="lineId"/>
        <result column="vehicle_type_id" jdbcType="VARCHAR" property="vehicleTypeId"/>
        <result column="structure_code" jdbcType="VARCHAR" property="structureCode"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modify_by" jdbcType="VARCHAR" property="modifyBy"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="del_flag" jdbcType="SMALLINT" property="delFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, signal_name_en, "name", signal_list, line_id, vehicle_type_id, structure_code,
        create_by, create_time, modify_by, modify_time, del_flag
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from "mtr_software_mapping"
        where del_flag='0'
        and id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="selectByStructureCode" resultType="cc.crrc.manage.pojo.mtr.MtrSoftwareVersionMappingVO"
            parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from "mtr_software_mapping"
        where del_flag='0'
        and structure_code = #{structureCode,jdbcType=VARCHAR}
    </select>
    <select id="selectByMultiCondition" resultType="cc.crrc.manage.pojo.mtr.MtrSoftwareVersionMappingVO"
            parameterType="cc.crrc.manage.pojo.mtr.MtrSoftwareVersionMappingVO">
        select
        mtr_software_mapping.*,mvt.name as vehicle_type_name,svts.name_cn as structure_name,ml.name as line_name
        from "mtr_software_mapping"
        left join mtr_vehicle_type mvt
        on mvt.id = mtr_software_mapping.vehicle_type_id
        left join stru_vehicle_type_structure svts
        on svts.vehicle_type_id = mtr_software_mapping.vehicle_type_id and svts.structure_code =
        mtr_software_mapping.structure_code
        left join mtr_line ml
        on ml.id = mtr_software_mapping.line_id
        where mtr_software_mapping.del_flag='0'
        <trim prefix="and" suffix="" suffixOverrides="and">
            <if test="id != null">
                mtr_software_mapping.id like '%'|| #{id,jdbcType=VARCHAR} ||'%' and
            </if>
            <if test="signalNameEn != null">
                mtr_software_mapping.signal_name_en like '%'|| #{signalNameEn,jdbcType=VARCHAR} ||'%' and
            </if>
            <if test="name != null">
                mtr_software_mapping."name" like '%'|| #{name,jdbcType=VARCHAR} ||'%' and
            </if>
            <if test="signalList != null">
                mtr_software_mapping.signal_list like '%'|| #{signalList,jdbcType=VARCHAR} ||'%' and
            </if>
            <if test="lineId != null">
                mtr_software_mapping.line_id like '%'|| #{lineId,jdbcType=VARCHAR} ||'%' and
            </if>
            <if test="vehicleTypeId != null">
                mtr_software_mapping.vehicle_type_id like '%'|| #{vehicleTypeId,jdbcType=VARCHAR} ||'%' and
            </if>
            <if test="structureCode != null">
                mtr_software_mapping.structure_code like '%'|| #{structureCode,jdbcType=VARCHAR} ||'%' and
            </if>
            <if test="createBy != null">
                mtr_software_mapping.create_by like '%'|| #{createBy,jdbcType=VARCHAR} ||'%' and
            </if>
            <if test="createTime != null">
                mtr_software_mapping.create_time = #{createTime,jdbcType=TIMESTAMP} and
            </if>
            <if test="modifyBy != null">
                mtr_software_mapping.modify_by like '%'|| #{modifyBy,jdbcType=VARCHAR} ||'%' and
            </if>
            <if test="modifyTime != null">
                mtr_software_mapping.modify_time = #{modifyTime,jdbcType=TIMESTAMP} and
            </if>
        </trim>
        order by mtr_software_mapping.create_time desc
    </select>
    <select id="getIdByEnName" resultType="java.lang.String">
        select id
        from mtr_software_mapping
        where signal_name_en = #{signalNameEn}
          and del_flag = 0 limit 1
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <!--@mbg.generated-->
        delete from "mtr_software_mapping"
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="cc.crrc.manage.pojo.mtr.MtrSoftwareVersionMappingVO" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into "mtr_software_mapping"
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            <if test="signalNameEn != null">
                signal_name_en,
            </if>
            <if test="name != null">
                "name",
            </if>
            <if test="signalList != null">
                signal_list,
            </if>
            <if test="lineId != null">
                line_id,
            </if>
            <if test="vehicleTypeId != null">
                vehicle_type_id,
            </if>
            <if test="structureCode != null">
                structure_code,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyBy != null">
                modify_by,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="subsystem != null">
                subsystem,
            </if>
            <if test="location != null">
                location,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id,jdbcType=VARCHAR},
            <if test="signalNameEn != null">
                #{signalNameEn,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="signalList != null">
                #{signalList,jdbcType=VARCHAR},
            </if>
            <if test="lineId != null">
                #{lineId,jdbcType=VARCHAR},
            </if>
            <if test="vehicleTypeId != null">
                #{vehicleTypeId,jdbcType=VARCHAR},
            </if>
            <if test="structureCode != null">
                #{structureCode,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyBy != null">
                #{modifyBy,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                #{delFlag,jdbcType=SMALLINT},
            </if>
            <if test="subsystem != null">
                #{subsystem,jdbcType=VARCHAR},
            </if>
            <if test="location != null">
                #{location,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="cc.crrc.manage.pojo.mtr.MtrSoftwareVersionMappingVO">
        <!--@mbg.generated-->
        update "mtr_software_mapping"
        <set>
            <if test="signalNameEn != null">
                signal_name_en = #{signalNameEn,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                "name" = #{name,jdbcType=VARCHAR},
            </if>
            <if test="signalList != null">
                signal_list = #{signalList,jdbcType=VARCHAR},
            </if>
            <if test="lineId != null">
                line_id = #{lineId,jdbcType=VARCHAR},
            </if>
            <if test="vehicleTypeId != null">
                vehicle_type_id = #{vehicleTypeId,jdbcType=VARCHAR},
            </if>
            <if test="structureCode != null">
                structure_code = #{structureCode,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyBy != null">
                modify_by = #{modifyBy,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag,jdbcType=SMALLINT},
            </if>
            <if test="subsystem != null">
                subsystem = #{subsystem,jdbcType=VARCHAR},
            </if>
            <if test="location != null">
                location = #{location,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

</mapper>