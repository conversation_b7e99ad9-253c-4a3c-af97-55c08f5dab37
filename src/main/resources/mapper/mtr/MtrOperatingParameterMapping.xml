<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtr.MtrOperatingParameterMapping">

    <select id="getOperatingParam" resultType="cc.crrc.manage.pojo.component.OperatingParameterDTO">
        SELECT
        id,
        component_id as componentId,
        vehicle_id as vehicleId,
        item_name_cn as itemNameCn,
        item_name_en as itemNameEn,
        item_value as itemValue,
        unit
        FROM
        mtr_operating_param
        WHERE
        1=1
        <if test="componentId != null and componentId !=''">
            AND component_id = #{componentId}
        </if>
        <if test="vehicleId != null and vehicleId !=''">
            AND vehicle_id = #{vehicleId}
        </if>
    </select>
    <insert id="insertOperatingParam">
        INSERT INTO mtr_operating_param
        (id,
        <if test="componentId != null and componentId !=''">
            component_id,
        </if>
        <if test="vehicleId != null and vehicleId !=''">
            vehicle_id,
        </if>
        item_name_cn,
        item_name_en,
        item_value,
        unit,
        create_by,
        create_time
        )VALUES(
        #{id},
        <if test="componentId != null and componentId !=''">
            #{componentId},
        </if>
        <if test="vehicleId != null and vehicleId !=''">
            #{vehicleId},
        </if>
        #{itemNameCn},
        #{itemNameEn},
        #{itemValue},
        #{unit},
        #{createBy},
        CURRENT_TIMESTAMP
        )
    </insert>
    <update id="updateOperatingParam">
        UPDATE mtr_operating_param SET
        <if test="componentId != null and componentId !=''">
            component_id = #{componentId},
        </if>
        <if test="vehicleId != null and vehicleId !=''">
            vehicle_id = #{vehicleId},
        </if>
        item_name_cn = #{itemNameCn},
        item_name_en = #{itemNameEn},
        item_value = #{itemValue},
        unit = #{unit},
        modify_by = #{modifyBy},
        modify_time = CURRENT_TIMESTAMP
        WHERE
        id = #{id}
    </update>
    <delete id="deleteOperatingParam">
        DELETE
        FROM mtr_operating_param
        WHERE id = #{id}
    </delete>
</mapper>