<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtr.ManufacturerFileMapping">
    <sql id="SysFileAlias">
        sf.id AS id,
        sf.name AS name,
        sf.url AS url,
        sf.type AS type,
        sf.hash_code AS hashCode,
        sf.format AS format,
        sf.size AS size,
        sf.del_flag AS delFlag,
        sf.group AS group,
        sf.file_location AS fileLocation
    </sql>


    <select id="getFilesByManufacturerId" resultType="cc.crrc.manage.pojo.SysFilePO">
        SELECT
        <include refid="SysFileAlias"/>
        FROM mtr_manufacturer_file mmf
        LEFT JOIN sys_file sf ON sf.id = mmf.file_id
        WHERE
        mmf.manufacturer_id = #{manufacturerId}
        AND mmf.del_flag = 0
    </select>

    <insert id="addManufacturerFile">
        INSERT INTO mtr_manufacturer_file
        (
        manufacturer_id,
        file_id,
        start_date
        )VALUES (
        #{manufacturerId},
        #{fileId},
        CURRENT_TIMESTAMP
        )
    </insert>

    <delete id="deleteManufacturerFileByFileId">
        UPDATE  mtr_manufacturer_file
        SET
        del_flag = 1,
        end_date = CURRENT_TIMESTAMP
        WHERE
        file_id = #{fileId}
        <if test="manufacturerId != null and manufacturerId !=''">
            AND manufacturer_id = #{manufacturerId}
        </if>
    </delete>
    
</mapper>