<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtr.VihicleSoftwareVersionMapping">


    <select id="VehicleSoftwareVersionList" resultType="cc.crrc.manage.pojo.mtr.VehicleSoftwareVersionPO">
        SELECT
        ms.id,
        ms.name AS softwareName,
        ms.version AS version,
        ms.description AS description,
        ms.modify_by AS userId,
        su.name AS modifyByName,
        mvt.id AS vehicleTypeId,
        mvt.name AS vehicleTypeName,
        mv.id AS vehicleId,
        mvt.line_id AS lineId,
        line.name As lineName,
        mv.name_cn AS vehicleName,
        sct.id AS componentTypeId,
        sct.name_cn AS componentTypeName,
        mcs.plan_time AS planTime,
        mcs.update_time AS realUpdateTime,
        ms.modify_time AS modifyTime

        FROM
        mtr_software ms
        LEFT JOIN mtr_component_software mcs ON mcs.software_id = ms.id
        LEFT JOIN stru_component sc ON sc.id = mcs.component_id
        left JOIN stru_component_type sct ON sct.id = sc.component_type_id
        LEFT JOIN sys_user su ON ms.modify_by = su. ID
        LEFT JOIN stru_vehicle_structure_component svsc ON svsc.component_id = sc.id
        LEFT JOIN mtr_vehicle mv ON mv.id = svsc.vehicle_id
        LEFT JOIN mtr_vehicle_type_relation mtr ON mtr.vehicle_id = mv.id
		LEFT JOIN mtr_vehicle_type mvt ON mvt.id = mtr.vehicle_type_id
        LEFT JOIN mtr_line line ON mvt.line_id = line. ID
        <where>
        	mv.del_flag = 0
            <if test="modifyByName != null and modifyByName != ''">
                AND su.name LIKE '%'||#{modifyByName}||'%'
            </if>
            <if test="softwareName != null and softwareName != ''">
                AND ms.name LIKE '%'||#{softwareName}||'%'
            </if>
            <if test="version != null and version != ''">
                AND ms.version LIKE '%'||#{version}||'%'
            </if>
            <if test="vehicleTypeId != null and vehicleTypeId != ''">
                AND mvt.id = #{vehicleTypeId}
            </if>
            <if test="lineId != null and lineId != ''">
                AND mvt.line_id = #{lineId}
            </if>
            <if test="vehicleId != null and vehicleId != ''">
                AND mv.id = #{vehicleId}
            </if>
            <if test="startTime != null and startTime != ''">
                AND mcs.update_time &gt;= #{startTime}
            </if> 
            <if test="endTime != null and endTime != ''">
                AND mcs.update_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY ms.create_time DESC
    </select>
    <select id="getVersionResume" resultType="cc.crrc.manage.pojo.mtr.VehicleSoftwareVersionPO">
        SELECT
    	ms. NAME AS softwareName,
    	ms. VERSION AS VERSION,
	    mcs.plan_time AS planTime,
	    mcs.update_time AS realUpdateTime
        FROM
	    stru_component sc
        LEFT JOIN mtr_component_software mcs ON mcs.component_id = sc. ID
        LEFT JOIN mtr_software ms ON ms. ID = mcs.software_id
        WHERE
	    mcs.software_id=#{softwareId} AND mcs.component_id=#{componentId}
    </select>
</mapper>