<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtr.MtrDesignParameterMapping">
    <select id="getDesignParam" resultType="cc.crrc.manage.pojo.component.DesignParameterDTO">
        SELECT
        id,
        component_type_id as comTypeId,
        vehicle_type_id as vehicleTypeId,
        item_name as itemName,
        item_value as itemValue
        FROM
        mtr_design_parameter
        WHERE
        1=1
        <if test="comTypeId != null and comTypeId !=''">
            AND component_type_id = #{comTypeId}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId !=''">
            AND vehicle_type_id = #{vehicleTypeId}
        </if>
    </select>

    <insert id="insertDesignParam">
        INSERT INTO mtr_design_parameter
        (id,
        <if test="comTypeId != null and comTypeId !=''">
            component_type_id,
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId !=''">
            vehicle_type_id,
        </if>
        item_name,
        item_value,
        create_by,
        create_time
        )VALUES(
        #{id},
        <if test="comTypeId != null and comTypeId !=''">
            #{comTypeId},
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId !=''">
            #{vehicleTypeId},
        </if>
        #{itemName},
        #{itemValue},
        #{createBy},
        CURRENT_TIMESTAMP
        )
    </insert>

    <update id="updateDesignParam">
        UPDATE mtr_design_parameter
        SET
        <if test="comTypeId != null and comTypeId !=''">
            component_type_id = #{comTypeId},
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId !=''">
            vehicle_type_id = #{vehicleTypeId},
        </if>
        item_name = #{itemName},
        item_value = #{itemValue},
        modify_by = #{modifyBy},
        modify_time = CURRENT_TIMESTAMP
        WHERE
        id = #{id}
    </update>

    <delete id="deleteDesignParam">
        DELETE
        FROM mtr_design_parameter
        WHERE id = #{id}
    </delete>
</mapper>