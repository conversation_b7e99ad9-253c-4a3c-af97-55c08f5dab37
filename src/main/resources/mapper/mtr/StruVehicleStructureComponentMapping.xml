<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtr.StruVehicleStructureComponentMapping">
    <sql id="StruVehicleStructureComponentColumnAlias">
        t.id AS id,
        t.component_id AS componentId,
        t.vehicle_id AS vehicleId,
        t.structure_code AS structureCode,
        t.assembly_time AS assemblyTime,
        t.disassembly_time AS disassemblyTime,
        t.valid AS valid,
        t.name_cn AS nameCn,
        t.name_en AS nameEn,
        t.short_name_en AS shortNameEn,
        t.component_type_id AS componentTypeId,
        t.parent_structure_code AS parentStructureCode,
        t.structure_position AS structurePosition,
        t.structure_type AS structureType,
        t.sort_number AS sortNumber,
        t.del_flag AS delFlag,
        t.start_date AS startDate,
        t.end_date AS endDate,
        t.create_by AS createBy,
        t.create_time AS createTime,
        t.modify_by AS modifyBy,
        t.modify_time AS modifyTime,
        t.remark AS remark
    </sql>

    <insert id="addStruVehicleStructureComponent" parameterType="java.util.List">
        insert into stru_vehicle_structure_component
        (
        id,
        component_id,
        vehicle_id,
        structure_code,
        assembly_time,
        disassembly_time,
        valid,
        name_cn,
        name_en,
        short_name_en,
        component_type_id,
        parent_structure_code,
        structure_position,
        structure_type,
        sort_number,
        del_flag,
        start_date,
        create_by,
        create_time,
        modify_by,
        modify_time,
        remark
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.componentId},
            #{item.vehicleId},
            #{item.structureCode},
            #{item.assemblyTime},
            #{item.disassemblyTime},
            #{item.valid},
            #{item.nameCn},
            #{item.nameEn},
            #{item.shortNameEn},
            #{item.componentTypeId},
            #{item.parentStructureCode},
            #{item.structurePosition},
            #{item.structureType},
            #{item.sortNumber},
            0,
            CURRENT_TIMESTAMP,
            #{item.createBy},
            CURRENT_TIMESTAMP,
            #{item.modifyBy},
            CURRENT_TIMESTAMP,
            #{item.remark}
            )
        </foreach>
    </insert>

    <delete id="deleteStruVehicleStructureComponent">
        UPDATE stru_vehicle_structure_component
        SET del_flag    = 1,
            modify_by   = #{currentId},
            modify_time = CURRENT_TIMESTAMP
        WHERE vehicle_id = #{vehicleId}
    </delete>

    <select id="selectVehicleStructureTreeList" resultType="cc.crrc.manage.pojo.stru.VehicleStructureTreeVO">
        SELECT
        <include refid="StruVehicleStructureComponentColumnAlias"/>,
        mvt.name as vehicleTypeName,
        sf.url as fbxUrl,
        svts.threed_code AS threedCode,
        CONCAT(#{lineId}::VARCHAR, '-', #{vehicleId}::VARCHAR, '-', t.id) AS uniqueFlag
        FROM
        stru_vehicle_structure_component t
        LEFT JOIN mtr_vehicle_type mvt ON mvt.id = #{vehicleTypeId}
        LEFT JOIN stru_vehicle_type_structure svts ON svts.structure_code = t.structure_code
        LEFT JOIN sys_file sf on sf.id =  svts.fbx_file_id
        WHERE
        t.del_flag = 0
        AND t.parent_structure_code != 'root'
        AND t.vehicle_id = #{vehicleId}
        AND svts.structure_code = t.structure_code
        AND svts.vehicle_type_id = #{vehicleTypeId}
    </select>

    <select id="selectVehicleStructureTreeHistoryList" resultType="cc.crrc.manage.pojo.stru.VehicleStructureTreeVO">
        SELECT
        <include refid="StruVehicleStructureComponentColumnAlias"/>,
        mvt.name AS vehicleTypeName,
        sf.url as fbxUrl,
        svts.threed_code AS threedCode,
        CONCAT(#{lineId}::VARCHAR, '-', #{vehicleId}::VARCHAR, '-', t.id) AS uniqueFlag
        FROM
        stru_vehicle_structure_component t
        LEFT JOIN mtr_vehicle_type mvt ON mvt.id = #{vehicleTypeId}
        LEFT JOIN stru_vehicle_type_structure svts ON svts.structure_code = t.structure_code
        LEFT JOIN sys_file sf on sf.id =  svts.fbx_file_id
        WHERE
        t.parent_structure_code != 'root'
        AND t.vehicle_id = #{vehicleId}
        AND t.start_date &lt;= #{time}
        AND (t.end_date &gt; #{time} OR (t.end_date IS NULL and t.del_flag = 0))
        AND svts.vehicle_type_id = #{vehicleTypeId}
    </select>

    <select id="valid" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM stru_vehicle_structure_component
        WHERE vehicle_id = #{vehicleId}
          AND structure_code = #{structureCode}
          AND del_flag = 0
    </select>

    <update id="updateVehicleInfo">
        UPDATE stru_vehicle_structure_component
        SET
        <if test="nameCn != null and nameCn != ''">
            name_cn = #{nameCn},
        </if>
        <if test="nameEn != null and nameEn != ''">
            name_en = #{nameEn},
        </if>
        <if test="shortNameEn != null and shortNameEn != ''">
            short_name_en = #{shortNameEn},
        </if>
        <if test="sortNumber != null">
            sort_number = #{sortNumber},
        </if>
        <if test="componentTypeId != null and componentTypeId !=''">
            component_type_id = #{componentTypeId},
        </if>
        <if test="componentId != null and componentId !=''">
            component_id = #{componentId},
        </if>
        <if test="delFlag != null">
            del_flag = #{delFlag},
        </if>
        <if test="assemblyTime != null">
            assembly_time = #{assemblyTime},
        </if>
        <if test="disassemblyTime != null">
            disassembly_time = #{disassemblyTime},
        </if>
        <if test="modifyBy != null and modifyBy !=''">
            modify_by = #{modifyBy},
        </if>
        modify_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <update id="updateVehicleStructureBasicInfo">
        UPDATE stru_vehicle_structure_component
        SET
        <if test="nameCn != null and nameCn != ''">
            name_cn = #{nameCn},
        </if>
        <if test="nameEn != null and nameEn != ''">
            name_en = #{nameEn},
        </if>
        <if test="shortNameEn != null and shortNameEn != ''">
            short_name_en = #{shortNameEn},
        </if>
        <if test="modifyBy != null and modifyBy !=''">
            modify_by = #{modifyBy},
        </if>
        sort_number = #{sortNumber},
        modify_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <update id="deleteVehicleInfo">
        UPDATE stru_vehicle_structure_component
        SET del_flag    = 1,
            modify_by   = #{userId},
            modify_time = CURRENT_TIMESTAMP,
            end_date    = CURRENT_TIMESTAMP,
            disassembly_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>


    <select id="getComponentId" resultType="java.lang.String">
        SELECT component_id AS componentId
        FROM stru_vehicle_structure_component
        WHERE
            valid = 1
          AND disassembly_time IS NULL
          AND vehicle_id = #{vehicleId}
          AND structure_code = #{structureCode}
    </select>

    <update id="bindComponent">
    	UPDATE stru_vehicle_structure_component SET
	    	component_type_id = #{componentTypeId},
	    	<if test="componentId != null and componentId !=''">
	    		assembly_time = CURRENT_TIMESTAMP,
	    	</if>
	    	component_id = #{componentId}
    	WHERE
    		vehicle_id = #{vehicleId}
    	AND
    		structure_code = #{structureCode}
    	AND
    		del_flag = 0
    </update>

    <!--根据id查询子节点-->
    <select id="getSubStructureById" resultType="cc.crrc.manage.pojo.mtr.StruVehicleStructureComponentPO">
        SELECT
        <include refid="StruVehicleStructureComponentColumnAlias"/>
        FROM
        stru_vehicle_structure_component t
        WHERE
        t.del_flag = '0' AND
        t.parent_structure_code = (
        SELECT structure_code FROM stru_vehicle_structure_component WHERE id = #{id} AND del_flag = '0'
        )
    </select>

    <select id="getStruVehicleStructureById" resultType="cc.crrc.manage.pojo.mtr.StruVehicleStructureComponentPO">
        SELECT
        <include refid="StruVehicleStructureComponentColumnAlias"/>
        FROM
        stru_vehicle_structure_component t
        WHERE
        t.id = #{id}
        AND t.del_flag = '0'
    </select>

    <select id="getStructureSystem" resultType="java.util.HashMap">
        SELECT
        t.name_cn AS name,
        sd.value AS key
        FROM
        stru_vehicle_structure_component t
        LEFT JOIN
        sys_dict sd ON sd.label = t.name_cn
        LEFT JOIN
        sys_dict_type sdt ON sdt.id = sd.type_id
        LEFT JOIN
        mtr_vehicle mv ON t.vehicle_id =mv.id and  mv.metro_line_id = sd.line_id
        WHERE
        t.structure_type = '系统'
        AND t.structure_code IN
        <foreach collection="structureCodeList" item="structureCode" index="index" open="(" close=")" separator=",">
            #{structureCode}
        </foreach>
        AND t.vehicle_id = #{vehicleId}
        AND t.del_flag = '0'
        AND sdt.type = 'ass_car_system'
        ORDER BY t.structure_code DESC
        LIMIT 1
    </select>

    <select id="countSubSystemInVehicle" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        stru_vehicle_structure_component t
        WHERE
        t.structure_type = '系统'
        AND t.name_cn = #{name}
        AND t.vehicle_id = #{vehicleId}
        AND t.del_flag = '0'
    </select>

    <!--根据车辆id和构型编码筛选唯一一条数据  21-11-18 lixin调试发现该方法暂时无用-->
    <select id="getVehicleStructureComponentByVehicleIdAndStructureCode" resultType="cc.crrc.manage.pojo.mtr.StruVehicleStructureComponentPO">
        SELECT
            <include refid="StruVehicleStructureComponentColumnAlias"/>
        FROM
            stru_vehicle_structure_component t
        WHERE
            t.vehicle_id = #{vehicleId} AND
            t.structure_code = #{structureCode} AND
            t.del_flag = 0
    </select>

    <select id="listUniqueFlag" resultType="java.lang.String">
        SELECT
            CONCAT(#{lineId}::VARCHAR, '-', #{vehicleId}::VARCHAR, '-', id)
        FROM stru_vehicle_structure_component
        WHERE structure_code in (
        SELECT
        t.parent_structure_code
        FROM
        stru_vehicle_structure_component t
        WHERE
        t.del_flag = 0
        AND t.parent_structure_code NOT IN ('root','-1')
        AND t.vehicle_id = #{vehicleId}
        <if test="nameCn != null and nameCn != ''">
            AND t.name_cn ~ #{nameCn}
        </if>
        )
        AND vehicle_id= #{vehicleId}
    </select>

    <select id="selectStruVehicleStructureComponentDif" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        stru_vehicle_structure_component t
        WHERE
        t.vehicle_id = #{vehicleId}
        AND
        t.structure_code = #{structureCode}
        AND
        t.del_flag = 0
    </select>

    <select id="getLocationByVehicleId" resultType="java.lang.String">
        SELECT
            t.name_cn
        FROM stru_vehicle_structure_component t
        WHERE
            t.vehicle_id =  #{vehicleId}
          AND t.structure_type = '车厢'
          AND t.del_flag = '0'
          AND t.name_cn != #{nameCn}
    </select>
</mapper>