<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtr.MtrVehicleMapping">
    <sql id="vehicleAlias">
        mv.id AS id,
        mv.name_cn AS nameCn,
        mv.name_en AS nameEn,
        mv.vehicle_code AS vehicleCode,
        mv.metro_line_id AS metroLineId,
        mv.production_date AS productionDate,
        mv.delivery_date AS deliveryDate,
        mv.guarantee_period AS guaranteePeriod,
        mv.remark,
        mv.vehicle_storage_group AS vehicleStorageGroup
    </sql>

    <select id="listVehicleInfo" resultType="cc.crrc.manage.pojo.mtr.MtrVehicleVO">
        SELECT
            mvtr.vehicle_type_id AS vehicleTypeId,
            mvt.name AS vehicleTypeName,
            ml.name AS lineName,
            <include refid="vehicleAlias"/>
        FROM mtr_vehicle mv
        LEFT JOIN mtr_vehicle_type_relation mvtr ON mv.id = mvtr.vehicle_id
        LEFT JOIN mtr_vehicle_type mvt ON mvtr.vehicle_type_id = mvt.id
        LEFT JOIN mtr_line ml ON ml.id = mv.metro_line_id
        <where>
            <if test="name != null and name != ''">
                AND (name_cn LIKE '%'||#{name}||'%'
                OR name_en LIKE '%'||#{name}||'%')
            </if>
            <if test="vehicleCode != null and vehicleCode != ''">
                AND vehicle_code = #{vehicleCode}
            </if>
            <if test="vehicleTypeId != null and vehicleTypeId !=''">
                AND mvtr.vehicle_type_id = #{vehicleTypeId}
            </if>
            <if test="metroLineId != null and metroLineId !=''">
                AND mv.metro_line_id = #{metroLineId}
            </if>
            AND mvtr.valid = 1
            AND mvtr.end_date IS NULL
            AND mv.del_flag = 0
        </where>
        ORDER BY mv.create_time DESC
    </select>

    <select id="getVehicleByCode" resultType="cc.crrc.manage.pojo.mtr.MtrVehiclePO">
        SELECT
        <include refid="vehicleAlias"/>
        FROM mtr_vehicle mv
        <where>
            mv.vehicle_code = #{vehicleCode}
            AND mv.del_flag = 0
        </where>
    </select>

    <select id="getVehicleById" resultType="cc.crrc.manage.pojo.mtr.MtrVehicleDetailVO">
        SELECT
            mvtr.vehicle_type_id AS vehicleTypeId,
            mvt.name AS vehicleTypeName,
            ml.name AS lineName,
            mvt.comm_type AS commType,
            mvt.type AS vehicleTypeCategory,
            mvt.marshalling_number AS marshallingNumber,
            <if test="fileType != null and fileType != ''">
                file.id AS fileId,
                file.url AS pictureURL,
            </if>
            mm.name AS manufacturerName,
            <include refid="vehicleAlias"/>
        FROM mtr_vehicle mv
        LEFT JOIN mtr_vehicle_type_relation mvtr ON mv.id = mvtr.vehicle_id
        LEFT JOIN mtr_vehicle_type mvt ON mvtr.vehicle_type_id = mvt.id
        LEFT JOIN mtr_line ml ON ml.id = mv.metro_line_id
        LEFT JOIN mtr_manufacturer mm ON mm.id = mvt.manufacturer_id
        <if test="fileType != null and fileType != ''">
            LEFT JOIN
                (SELECT id,"group"||'/'||file_location AS url,vehicle_type_id
                FROM mtr_vehicle_type_file mvtf
                LEFT JOIN sys_file sf ON sf.id = mvtf.file_id
                WHERE sf.type = #{fileType}
                AND sf.del_flag = 0
                ) file ON file.vehicle_type_id = mvt.id
        </if>
        <where>
            mv.id = #{vehicleId}
            AND mvtr.valid = 1
            AND mvtr.end_date IS NULL
            AND mv.del_flag = 0
        </where>
        ORDER BY mv.create_time DESC
        LIMIT 1
    </select>

    <select id="listVehicle" resultType="cc.crrc.manage.pojo.mtr.MtrVehiclePO">
        SELECT
        <include refid="vehicleAlias"/>,
        substring (mv.vehicle_code from 4 for 5) AS vehicleCodeAbbr,
        mvt.id AS vehicleTypeId,
        mvt.name AS vehicleTypeName
        FROM
        mtr_vehicle mv
        JOIN mtr_vehicle_type mvt ON mvt.line_id = mv.metro_line_id
        WHERE mv.del_flag = 0
            AND mvt.del_flag = 0
        <if test="metroLineId != null and metroLineId !=''">
            AND mv.metro_line_id = #{metroLineId}
        </if>
        ORDER BY mv.vehicle_code,mv.create_time
    </select>

    <select id="listVehicleExcel" resultType="java.util.Map">
        SELECT
        <include refid="vehicleAlias"/>,
        substring (mv.vehicle_code from 3 for 5) AS vehicleCodeAbbr
        FROM
        mtr_vehicle mv
        WHERE
        del_flag = 0
        <if test="metroLineId != null and metroLineId !=''">
            AND mv.metro_line_id = #{metroLineId}
        </if>
        ORDER BY  vehicle_code,create_time
    </select>



    <!--通过车型id筛选车辆列表-->
    <select id="listVehicleByTypeId" resultType="cc.crrc.manage.pojo.mtr.MtrVehicleVO">
        SELECT
        <include refid="vehicleAlias"/>
        FROM mtr_vehicle mv
        LEFT JOIN mtr_vehicle_type_relation mvtr ON mv.id = mvtr.vehicle_id
        WHERE mvtr.vehicle_type_id = #{id}
        AND mvtr.valid = '1'
        AND mv.del_flag = '0'
        ORDER BY mv.vehicle_code
    </select>

    <insert id="addVehicle" parameterType="cc.crrc.manage.pojo.mtr.MtrVehiclePO">
        INSERT INTO mtr_vehicle(
        id,
        name_cn,
        name_en,
        vehicle_code,
        metro_line_id,
        del_flag,
        create_by,
        create_time,
        modify_by,
        modify_time,
        remark,
        production_date,
        delivery_date,
        guarantee_period
        ) VALUES (
        #{id},
        #{nameCn},
        #{nameEn},
        #{vehicleCode},
        #{metroLineId},
        0,
        #{createBy},
        now(),
        #{modifyBy},
        now(),
        #{remark},
        #{productionDate},
        #{deliveryDate},
        #{guaranteePeriod}
        )
    </insert>

    <update id="deleteVehicle">
        UPDATE mtr_vehicle
        SET del_flag = 1,
            modify_by = #{modifyBy},
            modify_time = CURRENT_TIMESTAMP
        WHERE id = #{id}
    </update>

    <update id="updateVehicle">
        UPDATE mtr_vehicle
        <set>
            <if test="nameCn != null and nameCn != ''">
                name_cn = #{nameCn},
            </if>
            <if test="nameEn != null and nameEn != ''">
                name_en = #{nameEn},
            </if>
            <if test="vehicleCode != null and vehicleCode != ''">
                vehicle_code = #{vehicleCode},
            </if>
            <if test="metroLineId != null and metroLineId">
                metro_line_id = #{metroLineId},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark},
            </if>
            <if test="productionDate != null">
                production_date = #{productionDate},
            </if>
            <if test="deliveryDate != null">
                delivery_date = #{deliveryDate},
            </if>
            <if test="guaranteePeriod != null">
                guarantee_period = #{guaranteePeriod},
            </if>
            modify_time = now(),
            modify_by = #{modifyBy}
        </set>
        WHERE id = #{id}
    </update>

    <!--2020-0302 for 故障预警查询车辆  数字从小到大排序 -->
    <select id="listVehicleForPrognostic" resultType="cc.crrc.manage.pojo.mtr.MtrVehiclePO">
        SELECT
        <include refid="vehicleAlias"/>
        FROM
        mtr_vehicle mv
        LEFT JOIN mtr_vehicle_type_relation t2 ON
        mv.id= t2.vehicle_id
        LEFT JOIN mtr_vehicle_type t3 ON
        t2.vehicle_type_id =t3.id
        WHERE
        mv.del_flag = 0
        AND t3.del_flag = 0
        <if test="metroLineId != null and metroLineId !=''">
            AND mv.metro_line_id = #{metroLineId}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId !=''">
            AND t3.id = #{vehicleTypeId}
        </if>
        ORDER BY mv.vehicle_code ASC
    </select>

    <select id="getVehicleByIdForHistory" resultType="cc.crrc.manage.pojo.mtr.MtrVehicleDetailVO">
        SELECT
        mvtr.vehicle_type_id AS vehicleTypeId,
        mvt.name AS vehicleTypeName,
        ml.name AS lineName,
        mvt.comm_type AS commType,
        mvt.type AS vehicleTypeCategory,
        mvt.marshalling_number AS marshallingNumber,
        <if test="fileType != null and fileType != ''">
            file.id AS fileId,
            file.url AS pictureURL,
        </if>
        mm.name AS manufacturerName,
        <include refid="vehicleAlias"/>
        FROM mtr_vehicle mv
        LEFT JOIN mtr_vehicle_type_relation mvtr ON mv.id = mvtr.vehicle_id
        LEFT JOIN mtr_vehicle_type mvt ON mvtr.vehicle_type_id = mvt.id
        LEFT JOIN mtr_line ml ON ml.id = mv.metro_line_id
        LEFT JOIN mtr_manufacturer mm ON mm.id = mvt.manufacturer_id
        <if test="fileType != null and fileType != ''">
            LEFT JOIN
            (SELECT id,url,vehicle_type_id
            FROM mtr_vehicle_type_file mvtf
            LEFT JOIN sys_file sf ON sf.id = mvtf.file_id
            WHERE sf.type = #{fileType}
            AND sf.del_flag = 0
            ) file ON file.vehicle_type_id = mvt.id
        </if>
        where
            mv.id = #{vehicleId}
            AND mv.del_flag = 0
            AND mvtr.start_date &lt;= #{time}
            AND (mvtr.end_date &gt; #{time} or mvtr.end_date is null)
        ORDER BY mv.create_time DESC
        LIMIT 1
    </select>
    <select id="selectVehicleStartDate" resultType="java.util.Date">
        SELECT
        	t.start_date
        FROM
        	stru_vehicle_structure_component t
        WHERE
        	vehicle_id = #{vehicleId}
        ORDER BY
        	start_date ASC
        LIMIT 1
    </select>
    <select id="listVehicleByLineIdAndVehicleTypeId" resultType="cc.crrc.manage.pojo.mtr.MtrVehiclePO">
        SELECT
        <include refid="vehicleAlias"/>
        FROM
        mtr_vehicle mv
        LEFT JOIN mtr_vehicle_type_relation t2 ON
        mv.id= t2.vehicle_id
        LEFT JOIN mtr_vehicle_type t3 ON
        t2.vehicle_type_id =t3.id
        WHERE
        mv.del_flag = 0
        AND t3.del_flag = 0
        AND mv.metro_line_id = #{metroLineId}
        <if test="vehicleTypeId != null">
            AND t3.id = #{vehicleTypeId}
        </if>
        ORDER BY mv.vehicle_code ASC
    </select>
    <select id="getVehicleTypeAndLineIdById" resultType="java.util.Map">
        SELECT
            t1.metro_line_id AS "lineId",
            t2.vehicle_type_id AS "vehicleTypeId"
        FROM
            mtr_vehicle t1
                LEFT JOIN mtr_vehicle_type_relation t2 ON t1.ID = t2.vehicle_id
        WHERE
            t1.ID =  #{vehicleId}
    </select>
    <select id="getVehicleTypeAndLineIdByVehicleCode" resultType="java.util.Map">
        SELECT
            t1.metro_line_id AS "lineId",
            t2.vehicle_type_id AS "vehicleTypeId",
            t3.name AS "lineName",
            t4.name AS "vehicleTypeName"
        FROM
            mtr_vehicle t1
            LEFT JOIN mtr_vehicle_type_relation t2 ON t1.ID = t2.vehicle_id
            LEFT JOIN mtr_line t3 ON t3.ID = t1.metro_line_id
            LEFT JOIN mtr_vehicle_type t4 ON t4.ID = t2.vehicle_type_id

        WHERE
            t1.vehicle_code =  #{vehicleCode}
    </select>
    <select id="listVehicleForRelayContactor" resultType="cc.crrc.manage.pojo.mtr.MtrVehiclePO">
        SELECT
        <include refid="vehicleAlias"/>,
        t3.id AS vehicleTypeCode
        FROM
        mtr_vehicle mv
        LEFT JOIN mtr_vehicle_type_relation t2 ON
        mv.id= t2.vehicle_id
        LEFT JOIN mtr_vehicle_type t3 ON
        t2.vehicle_type_id =t3.id
        WHERE
        mv.del_flag = 0
        AND t3.del_flag = 0
        <if test="lineId != null and lineId !=''">
            AND mv.metro_line_id = #{lineId}
        </if>
        ORDER BY mv.vehicle_code ASC
    </select>

</mapper>