<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtr.ManufacturerEmployeeMapping">
    <sql id="manufacturerEmployeeAlias">
        mme.id AS id,
        mme.name AS name,
        mme.gender AS gender,
        mme.manufacturer_id AS manufacturerId,
        mme.email AS email,
        mme.phone AS phone,
        mme.profile AS profile,
        mme.type AS type,
        mme.create_by AS createBy,
        mme.create_time AS createTime,
        mme.modify_by AS modifyBy,
        mme.modify_time AS modifyTime,
        mme.remark AS remark,
        mm.name AS manufacturerName,
        mm.address AS manufacturerAddress,
        mm.phone AS manufacturerPhone,
        mm.email AS manufacturerEmail,
        mm.website AS manufacturerWebsite
    </sql>

    <select id="listManufacturerEmployee" resultType="cc.crrc.manage.pojo.mtr.ManufacturerEmployeeVO">
        SELECT
        <include refid="manufacturerEmployeeAlias"/>
        FROM
        mtr_manufacturer_employee mme
        LEFT JOIN mtr_manufacturer mm ON mm.id = mme.manufacturer_id
        <where>
            <if test="searchKey != null and searchKey != ''">
                AND (mme.name LIKE '%'||#{searchKey}||'%'
                OR mm.name LIKE '%'||#{searchKey}||'%'
                OR mme.phone LIKE '%'||#{searchKey}||'%')
            </if>
        </where>
    </select>

    <select id="listManufacturerEmployeeByManufacturerId" resultType="cc.crrc.manage.pojo.mtr.ManufacturerEmployeeVO">
        SELECT
        <include refid="manufacturerEmployeeAlias"/>
        FROM
        mtr_manufacturer_employee mme
        LEFT JOIN mtr_manufacturer mm ON mm.id = mme.manufacturer_id
        <where>
            <if test="manufacturerId != null and manufacturerId !=''">
                AND mme.manufacturer_id = #{manufacturerId}
            </if>
        </where>
    </select>

    <select id="getManufacturerEmployeeById" resultType="cc.crrc.manage.pojo.mtr.ManufacturerEmployeeVO">
        SELECT
        <include refid="manufacturerEmployeeAlias"/>
        FROM
        mtr_manufacturer_employee mme
        LEFT JOIN mtr_manufacturer mm ON mme.manufacturer_id = mm.id
        WHERE
        mme.id = #{id}
    </select>

    <insert id="addManufacturerEmployee">
        INSERT INTO mtr_manufacturer_employee
        (
        id,
        name,
        gender,
        manufacturer_id,
        email,
        phone,
        profile,
        type,
        create_by,
        create_time,
        modify_by,
        modify_time
        )VALUES (
        #{id},
        #{name},
        #{gender},
        #{manufacturerId},
        #{email},
        #{phone},
        #{profile},
        #{type},
        #{createBy},
        now(),
        #{modifyBy},
        now()
        )
    </insert>

    <update id="updateManufacturerEmployee">
        UPDATE mtr_manufacturer_employee
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="gender != null and gender != ''">
                gender = #{gender},
            </if>
            <if test="manufacturerId != null and manufacturerId != ''">
                manufacturer_id = #{manufacturerId},
            </if>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="profile != null">
                profile = #{profile},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="modifyBy != null">
                modify_by = #{modifyBy},
            </if>
                modify_time = now()
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteManufacturerEmployeeById">
        DELETE FROM mtr_manufacturer_employee
        WHERE id = #{id}
    </delete>

    <delete id="deleteManufacturerEmployeeByManufacturerId">
        DELETE FROM mtr_manufacturer_employee
        WHERE manufacturer_id = #{manufacturerId}
    </delete>

</mapper>
