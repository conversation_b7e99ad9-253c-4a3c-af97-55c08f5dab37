<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtr.MtrVehicleTypeRelationMapping">
    <sql id="MtrVehicleTypeRelationAlias">
        mvtr.vehicle_id AS vehicleId,
        mvtr.vehicle_type_id AS vehicleTypeId,
        mvtr.start_date AS startDate,
        mvtr.end_date AS endDate,
        mvtr.valid AS valid
    </sql>

    <select id="getVehicleTypeById" resultType="cc.crrc.manage.pojo.mtr.MtrVehicleTypeRelationPO">
        SELECT
        <include refid="MtrVehicleTypeRelationAlias"/>
        FROM
        mtr_vehicle_type_relation mvtr
        <where>
            <if test="vehicleId != null and vehicleId !=''">
                AND mvtr.vehicle_id = #{vehicleId}
            </if>
            AND mvtr.end_date is null
            AND mvtr.valid = 1
        </where>
    </select>

    <select id="listVehicleByVehicleTypeId" resultType="cc.crrc.manage.pojo.mtr.MtrVehicleTypeRelationPO">
        SELECT
        <include refid="MtrVehicleTypeRelationAlias"/>
        FROM
        mtr_vehicle_type_relation mvtr
        <where>
            <if test="vehicleTypeId != null and vehicleTypeId !=''">
                AND mvtr.vehicle_type_id = #{vehicleTypeId}
            </if>
            AND mvtr.end_date is null
            AND mvtr.valid = 1
        </where>
    </select>

    <select id="listVehicleHistory" resultType="cc.crrc.manage.pojo.mtr.MtrVehicleTypeRelationPO">
        SELECT
        <include refid="MtrVehicleTypeRelationAlias"/>
        FROM
        mtr_vehicle_type_relation mvtr
        WHERE
        mvtr.vehicle_id = #{vehicleId}
    </select>


    <update id="invalidRelationByVehicleId">
        UPDATE mtr_vehicle_type_relation
        SET end_date = now(),
            valid    = 0
        WHERE valid = 1
          AND end_date IS NULL
          AND vehicle_id = #{vehicleId}
    </update>

    <insert id="addRelation">
        INSERT INTO mtr_vehicle_type_relation(
        vehicle_id,
        vehicle_type_id,
        start_date,
        valid
        )VALUES (
        #{vehicleId},
        #{vehicleTypeId},
        now(),
        1
        )
    </insert>

</mapper>