<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.mtr.LifeCycleMapping">

    <select id="getComponentLiftCycle" resultType="cc.crrc.manage.pojo.mtr.ComponentLifeCycleVo">
		SELECT 
			ct.name_cn as nameCn,
			ct.name_en as nameEn,
			ct.product_number as productNumber,
			ct.catalog,
			c.id,
			c.serial_number as serialNumber,
			c.production_date as productionDate,
			vsc.assembly_time as assemblyTime,
			vsc.disassembly_time as disassemblyTime,
			m.name as manufacturerName
		FROM
			stru_component c
		JOIN stru_component_type ct ON ct.id = c.component_type_id
		JOIN mtr_manufacturer m ON m.id = ct.manufacturer_id
		JOIN stru_vehicle_structure_component vsc ON vsc.component_id = c.id
		WHERE
			vsc.structure_code = #{structureCode}
		AND
			vsc.vehicle_id = #{vehicleId}
		AND
			(vsc.del_flag = 0
		or
			(vsc.del_flag = 1 AND vsc.disassembly_time IS NOT NULL))
		ORDER BY vsc.assembly_time ASC
    </select>

    <select id="getSoftwareLiftCycle" resultType="cc.crrc.manage.pojo.mtr.SoftwareLifeCycleVo">
       	SELECT
			s.name,
			s.version,
			cs.update_time as updateTime,
			cs.valid,
			m.name as manufacturerName
		FROM
			mtr_software s
		JOIN mtr_component_software cs on cs.software_id = s.id
		JOIN mtr_manufacturer m ON m.id = s.manufacturer_id
		WHERE cs.component_id = #{componentId}
		ORDER BY cs.update_time ASC
    </select>


    <!--<select id="getFraMaintanenceComponentList" resultType="cc.crrc.manage.pojo.fra.FraMaintainVO">
		select fm.create_time createTime  from  stru_vehicle_structure_component svsc
		left join  fra_maintenance fm on svsc.component_id = fm.component_id and svsc.component_id =#{componentId}
		where  maintenance_mode !='0'
		and
		((svsc.disassembly_time is  null) or
		(fm.create_time &lt;= svsc.disassembly_time))
		order by fm.create_time desc
	</select>-->
</mapper>