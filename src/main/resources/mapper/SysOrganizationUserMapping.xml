<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.mapper.SysOrganizationUserMapping">
    <!--新增-->
    <insert id="addOrganizationUser">
        INSERT INTO sys_organization_user (
            user_id,
            organization_id
        )
        VALUES (
            #{userId},
            #{organizationId}
        )
    </insert>
    <!--根据用户id查询关系数据-->
    <select id="listOrganizationUserRelationsByUserId" resultType="cc.crrc.manage.pojo.SysOrganizationUserPO">
        SELECT
            user_id AS userId,
            organization_id AS organizationId
        FROM
            sys_organization_user
        WHERE
            user_id = #{userId}
    </select>

    <select id="UserIdByOrganizationId" resultType="cc.crrc.manage.pojo.SysOrganizationUserPO">
        SELECT
            user_id AS userId,
            organization_id AS organizationId
        FROM
            sys_organization_user
        WHERE
            organization_id = #{organizationId}
    </select>
    <!--删除关系数据-->
    <delete id="deleteOrganizationUserRelation">
        DELETE FROM
            sys_organization_user
        WHERE
            user_id =  #{userId} AND
            organization_id = #{organizationId}
    </delete>
    <!--根据组织id查询组织下的用户数量-->
    <select id="countUserByOrganizationId" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM
            sys_organization_user
        WHERE
            organization_id = #{organizationId}
    </select>
</mapper>