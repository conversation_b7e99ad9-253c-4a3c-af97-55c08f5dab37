<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.monitoringConfig.dao.LineVehiclesListDao">


    <select id="getLine" resultType="cc.crrc.manage.pojo.line.LineDTO">
        SELECT DISTINCT
        line.id,
        line.name,
        line.location_id as locationId,
        location.name_cn as locationName,
        line.mileage,
        line.longitude,
        line.latitude,
        line.create_by as createBy,
        line.create_time as createTime,
        line.modify_by as modifyBy,
        line.modify_time as modifyTime,
        line.remark,
        line.color
        FROM mtr_line line
        LEFT JOIN sys_location location ON line.location_id=location.id
        WHERE
        line.del_flag = '0'
    </select>

    <select id="getTrains" resultType="cc.crrc.manage.monitoringConfig.entity.TraTrains" parameterType="String">
        select
            pk_id AS pkId,
            tenant_id AS tenantId,
            line_id AS lineId,
            asset_id AS assetId,
            tra_code AS traCode,
            tra_name AS traName,
            create_by AS createBy,
            create_date AS createDate,
            update_by AS updateBy,
            update_date AS updateDate,
            tra_comments AS traComments,
            flag_status AS flagStatus,
            p_id AS pId,
            vehicle_storage_group AS vehicleStorageGroup
        from tra_trains
        where
            line_id =#{lineId}
        <if test="traName != null">
            AND tra_name LIKE '%'||#{traName}||'%'
        </if>
        ORDER BY length(tra_name) asc,tra_name asc
    </select>

    <select id="queryStationByLineId" resultType="java.util.HashMap">
		select 	pk_id 			as 	"pkId",
		 		tenant_id		as 	"tenantId",
		 		p_id			as 	"pId",
		 		line_id			as 	"lineId",
		 		sta_code		as 	"staCode",
		 		sta_name		as 	"staName",
		 		sta_sort		as 	"staSort",
		 		sta_change		as 	"staChange",
		 		create_by		as 	"createBy",
		 		create_date		as 	"createDate",
		 		update_by		as 	"updateBy",
		 		update_date		as 	"updateDate",
		 		sta_comments	as 	"staComments",
		 		flag_status		as 	"flagStatus"
		from 	tra_station
		where 	1 = 1
				and line_id = #{lineId}
		order by sta_sort asc
	</select>
    <select id="getCity" resultType="java.util.HashMap">
        select
        DISTINCT
        sa.id AS areaId,
        sa.name AS areaName
        from  sys_area sa
        JOIN
        tra_line t
        ON
        t.location = sa.id
        JOIN
        sys_tenant s
        ON
        s.pk_id = t.tenant_id
        WHERE 1=1
        AND t.flag_status = 1
        <if test="tenantId != null and tenantId != ''">
            AND t.tenant_id in (
            WITH RECURSIVE tab AS (
            SELECT
            pk_id
            FROM
            sys_tenant
            WHERE
            pk_id =#{tenantId} UNION ALL
            SELECT A.pk_id
            FROM
            sys_tenant
            A JOIN tab b ON A.parent_tenant = b.pk_id
            ) SELECT pk_id FROM tab )
        </if>
    </select>
    <sql id="vehicleAlias">
        mv.id AS id,
        mv.name_cn AS nameCn,
        mv.name_en AS nameEn,
        mv.vehicle_code AS vehicleCode,
        mv.metro_line_id AS metroLineId,
        mv.production_date AS productionDate,
        mv.delivery_date AS deliveryDate,
        mv.guarantee_period AS guaranteePeriod,
        mv.remark
    </sql>
    <select id="getTrainsForMonitor" resultType="cc.crrc.manage.pojo.mtr.MtrVehiclePO">
        SELECT
        <include refid="vehicleAlias"/>,
        (case
        when count(mm.id)>0
        then true
        else false
        end
        ) as trainsDeploy
        FROM
        mtr_vehicle mv
        LEFT JOIN
        monitor_menu mm ON mv.vehicle_code = mm.tra_code and mm.del_flag = false
        WHERE
        mv.del_flag = '0'
        <if test="metroLineId != null and metroLineId != ''">
            AND mv.metro_line_id = #{metroLineId}
        </if>
        GROUP BY mv.id
        ORDER BY mv.create_time
    </select>

</mapper>