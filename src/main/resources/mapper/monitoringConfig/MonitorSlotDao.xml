<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.monitoringConfig.dao.MonitorSlotDao">


    <sql id="monitorSlotAlias">
        t.id,
		t.table_format_id as tableFormatId,
		t.sort,
		t.location,
		t.slot_type as slotType,
		t.create_time AS createTime,
		t.create_by AS createBy,
		t.modify_time as modifyTime,
		t.modify_by as modifyBy,
		t.del_flag as delFlag
    </sql>
    <insert id="addSlots" >
        INSERT into monitor_slot
        (
        id,
        table_format_id,
        sort,
        location,
        slot_type,
        create_time,
        create_by,
        modify_time,
        modify_by,
        del_flag
        )values
        <foreach collection="slotList" item="item"  index="index" separator=",">
        (
        #{item.id},
        #{item.tableFormatId},
        #{item.sort},
        #{item.location},
        #{item.slotType},
        CURRENT_TIMESTAMP,
        #{item.createBy},
        CURRENT_TIMESTAMP,
        #{item.modifyBy},
        #{item.delFlag}
        )
        </foreach>
    </insert>
    <update id="deleteSlotByItemId">
        UPDATE monitor_slot
        SET
        del_flag = TRUE,
        modify_time = CURRENT_TIMESTAMP,
        slot_type = 0
        WHERE
	    table_format_id in
	    (
	        SELECT mtf.id
	        from monitor_table_format mtf
	        where mtf.del_flag=false
	        and mtf.item_id = #{itemId}
	    )
    </update>
    <update id="editSlotType">
        UPDATE monitor_slot
        SET
        slot_type = #{slotType},
        modify_time = CURRENT_TIMESTAMP
        WHERE
	    id = #{slotId}
    </update>

    <!--废弃暂时无用2020-08-01-->
    <select id="getSlotList" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorSlotEntity">
        SELECT
			t1.id,
			t1.table_format_id as tableFormatId,
			t1.sort,
			t1.location,
			t1.create_time AS createTime,
			t1.create_by AS createBy,
			t1.modify_time as modifyTime,
			t1.modify_by as modifyBy,
			t1.del_flag as delFlag,
			t1.slot_type as slotType
		FROM
			monitor_slot t1
			LEFT JOIN monitor_table_format t2 ON t2.id = t1.table_format_id
			LEFT JOIN monitor_table_item t3 ON t3.id = t2.item_id
		WHERE
			t3.id = #{itemId}
			AND t1.del_flag = FALSE
			AND t2.del_flag = FALSE
			AND t3.del_flag = FALSE
		order by t1.sort
    </select>

    <!--废弃暂时无用2020-08-01-->
    <select id="getAllBasicItem" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorTableItemEntity">
        select
            id,
            name
        from
            monitor_table_item
        where
            menu_id = #{menuId}
        and
            type = 'basic'
        and
            del_flag = false
        order by sort
    </select>

    <!--废弃暂时无用2020-08-01-->
    <select id="getFormat" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorTableFormatEntity">
        select
            id,
            item_id as itemId,
            n_row as nRow,
            n_column as nColumn,
            vehicle_location as vehicleLocation,
            create_time as createTime,
            create_by as createBy,
            modify_time as modifyTime,
            modify_by as modifyBy,
            del_flag as delFlag,
            sort
        from
            monitor_table_format
        where
            item_id = #{itemId}
        and
            del_flag = false
        order by
            sort
    </select>

<!--    <select id="getSlotByFormatId" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorSlotEntity">-->
<!--        SELECT-->
<!--			id,-->
<!--			table_format_id as tableFormatId,-->
<!--			sort,-->
<!--			location,-->
<!--			create_time AS createTime,-->
<!--			create_by AS createBy,-->
<!--			modify_time as modifyTime,-->
<!--			modify_by as modifyBy,-->
<!--			del_flag as delFlag,-->
<!--			slot_type as slotType-->
<!--		FROM-->
<!--			monitor_slot-->
<!--		WHERE-->
<!--            table_format_id = #{formatId}-->
<!--        AND-->
<!--            del_flag = false-->
<!--		order by sort-->
<!--    </select>-->
    <select id="listBoard" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorSlotEntity">
        SELECT
        <include refid="monitorSlotAlias"></include>
        FROM monitor_slot t
        LEFT JOIN monitor_table_format t1 ON t.table_format_id = t1.id
        and t1.del_flag =FALSE
        LEFT JOIN monitor_table_item t2 ON t1.item_id = t2.id
        and t2.del_flag =FALSE
        WHERE
        t2.menu_id = #{menuId}
        and
        t2.type = 'car'
        and
        t.del_flag =FALSE
        ORDER BY
        t.sort,t.location
    </select>
    <select id="getSlotByFormatId" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorSlotEntity">
        select
        id,
        table_format_id tableFormatId,
        sort,
        location,
        slot_type slotType,
        create_time createTime,
        create_by createBy,
        modify_time modifyTime,
        modify_by modifyBy,
        del_flag delFlag

        from monitor_slot
        where
        del_flag = false
        and
        table_format_id = #{formatId}
        order by sort asc
    </select>

    <select id="getSlotIdListForCopy" resultType="java.lang.String">
        SELECT
        t.id
        FROM monitor_slot t
        WHERE
        t.table_format_id in
        <foreach collection="formatIdsToDel" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        and
        t.del_flag =FALSE
    </select>
</mapper>
