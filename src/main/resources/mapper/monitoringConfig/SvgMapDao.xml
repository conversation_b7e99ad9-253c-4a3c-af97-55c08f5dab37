<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.monitoringConfig.dao.SvgMapDao">
    <sql id="svgMapAlias">
        t.id,
		t.menu_id as menuId,
		t.vehicle_type as vehicleType,
		t.key,
		t.signal_function_id as signalFunctionId,
		t.svg_code as svgCode,
		t.svg_function as svgFunction,
		t.line_id as lineId,
		t.create_time AS createTime,
		t.create_by AS createBy,
		t.modify_time as modifyTime,
		t.modify_by as modifyBy,
		t.del_flag as delFlag
    </sql>
    <insert id="addSvgMap">
        INSERT into svg_map
         (
        id,
        menu_id,
        vehicle_type,
        key,
        signal_function_id,
        svg_code,
        svg_function,
        line_id,
        create_time,
        create_by,
        modify_time,
        modify_by,
        del_flag
        )values
        (
        #{id},
        #{menuId},
        #{vehicleType},
        #{key},
        #{signalFunctionId},
        #{svgCode},
        #{svgFunction},
        #{lineId},
        CURRENT_TIMESTAMP,
        #{createBy},
        CURRENT_TIMESTAMP,
        #{modifyBy},
        false
        )
    </insert>
    <select id="getSvgMapInfoByLineId" resultType="cc.crrc.manage.monitoringConfig.entity.SvgMapEntity">
        select
        <include refid="svgMapAlias"></include>
        FROM svg_map t
        <where>
            1=1
            <if test="lineId != null and lineId != ''">
                AND t.line_id = #{lineId}
            </if>
            and
            t.del_flag = false
        </where>
    </select>
    <update id="editSvgMapByLineId">
        UPDATE svg_map
        set
        menu_id = #{menuId},
        vehicle_type = #{vehicleType},
        key = #{key},
        signal_function_id = #{signalFunctionId},
        svg_code = #{svgCode},
        svg_function = #{svgFunction},
        line_id = #{lineId},
        modify_time = CURRENT_TIMESTAMP,
        modify_by = #{modifyBy}
        WHERE
        line_id =#{lineId}
    </update>
    <update id="deleteSvgMapByLineId">
        UPDATE svg_map
        set
        del_flag = true
        WHERE
        line_id =#{lineId}
    </update>


</mapper>