<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.monitoringConfig.dao.MonitorMenuDao">
    <!--初始化表字段-->
    <sql id="MonitorMenuColumnAlias">
        id,
        menu_code as menuCode,
        parent_id as parentId,
        name,
        sort,
        board_status as boardStatus,
        menu_type as menuType,
        url,
        create_time as createTime,
        create_by as createBy,
        modify_time as modifyTime,
        modify_by as modifyBy,
        show_status as showStatus,
        tra_code as traCode,
        components_url as componentsUrl,
        default_active as defaultActive,
        del_flag as delFlag
   </sql>
    <!--查询有效菜单信息-->
    <select id="getMonitorMenuList" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorMenuEntityPO">
        select
        <include refid="MonitorMenuColumnAlias"/>
        from monitor_menu
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="traCode != null and traCode != ''">
                and tra_code = #{traCode}
            </if>
            and del_flag = false
            order by cast(sort as int)
        </trim>
    </select>
    <!--修改菜单信息-->
    <update id="updateMonitorMenu">
        update monitor_menu
        <trim prefix="set" suffixOverrides=",">
            <if test="menuCode != null and menuCode!='' ">
                menu_code =#{menuCode},
            </if>
            <if test="parentId != null and parentId!='' ">
                parent_id =#{parentId},
            </if>
            <if test="name != null and name!='' ">
                name =#{name},
            </if>
            <if test="sort != null and sort!='' ">
                sort =#{sort},
            </if>
            <if test="boardStatus != null ">
                board_status =#{boardStatus},
            </if>
            <if test="menuType != null and menuType!='' ">
                menu_type =#{menuType},
            </if>
            <if test="url != null and url!=''">
                url =#{url},
            </if>
            <if test="createTime != null">
                create_time =#{createTime},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by =#{createBy},
            </if>
            <if test="modifyTime != null ">
                modify_time =#{modifyTime},
            </if>
            <if test="modifyBy != null and modifyBy != ''">
                modify_by =#{modifyBy},
            </if>
            <if test="delFlag != null ">
                del_flag =#{delFlag},
            </if>
            <if test="showStatus != null ">
                show_status =#{showStatus},
            </if>
            <if test="traCode != null and traCode != ''">
                tra_code =#{traCode},
            </if>
            <if test="componentsUrl != null and componentsUrl != ''">
                components_url =#{componentsUrl},
            </if>
            <if test="defaultActive != null">
                default_active =#{defaultActive},
            </if>
        </trim>
        <where>
            id =#{id}
        </where>
    </update>
    <!--新增菜单-->
    <insert id="insertMonitorMenu">
        insert into monitor_menu
        (
          id,
          menu_code,
          parent_id,
          name,
          sort,
          board_status,
          menu_type,
          url,
          create_time,
          create_by,
          modify_time,
          modify_by,
          del_flag,
          tra_code,
          components_url,
          default_active,
          show_status
        )
        values
        (
            #{id},
            #{menuCode},
            #{parentId},
            #{name},
            #{sort},
            #{boardStatus},
            #{menuType},
            #{url},
            #{createTime},
            #{createBy},
            #{modifyTime},
            #{modifyBy},
            #{delFlag},
            #{traCode},
            #{componentsUrl},
            #{defaultActive},
            #{showStatus}
        )
    </insert>
    <!--校验 菜单名称 菜单编码 -->
    <select id="findMonitorMenuByNameOrCode"
            resultType="cc.crrc.manage.monitoringConfig.entity.MonitorMenuEntity">
        select
        <include refid="MonitorMenuColumnAlias"/>
        from monitor_menu
        <trim prefix="where" prefixOverrides="and|or">
            <if test="name != null and name != ''">
                or name = #{name}
            </if>
            <if test="menuCode != null and menuCode != ''">
                or menu_code = #{menuCode}
            </if>
        </trim>
    </select>
    <!--查询当前线路下所有父级菜单名称-->
    <select id="findMonitorMenuByParentName"
            resultType="cc.crrc.manage.monitoringConfig.entity.MonitorMenuEntityPO">
        SELECT
        id,
        parent_id,
        "name"
        FROM
        monitor_menu
        <where>
            parent_id = #{parentId}
            and
            tra_code = #{traCode}
            and
            del_flag = false
        </where>
    </select>
    <!--查询父子结构形 菜单信息-->
    <select id="getMenuList" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorMenuEntityPO">
        select
        <include refid="MonitorMenuColumnAlias"/>
        from monitor_menu
        <trim prefix="where" prefixOverrides="and|or">
            <if test="traCode != null and traCode != ''">
                and tra_code = #{traCode}
            </if>
            and del_flag = false
            order by cast(sort as int)
        </trim>
    </select>

    <select id="findMonitorMenuByTraCode"
            resultType="cc.crrc.manage.monitoringConfig.entity.MonitorMenuEntity">
        select
        <include refid="MonitorMenuColumnAlias"/>
        from monitor_menu
        where
        del_flag = false
        and tra_code = #{traCode}
        and del_flag = false
        ORDER BY parent_id DESC
    </select>

    <select id="getMenuById" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorMenuEntity">
        select
        <include refid="MonitorMenuColumnAlias"/>
        from monitor_menu
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            and del_flag = false
            order by create_time desc
        </trim>
    </select>

    <select id="getMonitorMenuIdListForCopy" resultType="java.lang.String">
        select
        id
        from monitor_menu
        WHERE
            tra_code in
        <foreach collection="targetTraCodes" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
            and del_flag = false
            order by create_time desc
    </select>
    <select id="getMenuBySlotId" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorMenuEntity">
        select
        <include refid="MonitorMenuColumnAlias"/>
        from monitor_menu
        WHERE
        id = (select t1.menu_id from monitor_table_item t1
        WHERE
        t1.id = (SELECT t2.item_id from monitor_table_format t2
        WHERE
        t2.id = (SELECT  t3.table_format_id from monitor_slot t3
        WHERE
        t3.id = #{slotId})))
    </select>

</mapper>