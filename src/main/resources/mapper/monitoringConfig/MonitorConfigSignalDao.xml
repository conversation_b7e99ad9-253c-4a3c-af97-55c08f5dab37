<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.monitoringConfig.dao.MonitorConfigSignalDao">
	<sql id="wtdSignalColumnAlias">
		nws.id AS id,
		nws.protocol_id AS protocolId,
		nws.name_cn AS nameCn,
		nws.name_en AS nameEn,
		nws.byte_offset AS byteOffset,
		nws.bit_offset AS bitOffset,
		nws.data_type AS dataType,
		nws.unit AS unit,
		nws.location AS carLocation,
		nws.subSystem AS subSystem,
		nws.parse_script AS parseFunction,
		nws.fault_type_key AS triggerFaultId,
		nws.trigger_value AS triggerValue,
		nws.max_value AS maxValue,
		nws.min_value AS minValue,
	    nws.modify_time AS modifyTime,
	    nws.remark AS remark,
	    nws.redis_flag AS redisFlag,
	    nws.frames_type AS framesType,
		nws.package_order AS packageOrder,
		nws.result_type AS resultType
	</sql>

	<!--1、根据项点名称或者输入变量名模糊查询-->
	<select id="findLikeWtdSignalByInputName"
			resultType="cc.crrc.manage.monitoringConfig.entity.MonitorConfigWtdSignalPO">
		SELECT
		nws.id AS id,
		nws.protocol_id AS protocolId,
		nws.name_cn||'('||nws.name_en||')' AS nameCn,
		nws.name_en AS nameEn,
		nws.byte_offset AS byteOffset,
		nws.bit_offset AS bitOffset,
		nws.data_type AS dataType,
		nws.unit AS unit,
		nws.location AS carLocation,
		nws.subSystem AS subSystem,
		nws.parse_script AS parseFunction,
		/*nws.fault_type_key AS triggerFaultId,
		nws.trigger_value AS triggerValue,
		nws.max_value AS maxValue,
		nws.min_value AS minValue,
		nws.modify_time AS modifyTime,
		nws.remark AS remark,*/
		nws.redis_flag AS redisFlag,
		nws.frames_type AS framesType,
		nws.package_order AS packageOrder,
		nws.result_type AS resultType
		FROM
		comm_original_signal nws
		WHERE
			nws.protocol_id = #{protocolId}
			<if test="inputName != null and inputName != ''">
			AND (nws.name_cn LIKE '%'||#{inputName}||'%'
			OR nws.name_en LIKE '%'||#{inputName}||'%')
			</if>
			/*and nws.redis_flag = '1'*/
		ORDER BY
			nws.name_cn,nws.modify_time DESC
		LIMIT 20
	</select>
	<select id="selectProtocolIdByVehicleCode" resultType="java.lang.String">
		SELECT
	t1.protocol_id AS protocolId
FROM comm_vehicle_tcp_protocol t1
		 INNER JOIN mtr_vehicle t2 ON t1.vehicle_id = t2.ID
	AND t2.del_flag = 0
		 JOIN comm_protocol t3 ON t3."id" = t1.protocol_id
	AND t3.del_flag = 0
	AND t3."enable" = 1
		WHERE t2.vehicle_code = #{vehicleCode}
		  AND t1.VALID = 1
	</select>
	<select id="selectProtocolIdByVehicleType" resultType="java.lang.String" parameterType="java.lang.String">
		SELECT t1.protocol_id AS protocolId
		FROM comm_vehicle_tcp_protocol t1
				 INNER JOIN mtr_vehicle t2 ON t1.vehicle_id = t2.ID
			AND t2.del_flag = 0
				 JOIN comm_protocol t3 ON t3."id" = t1.protocol_id
			AND t3.del_flag = 0
			AND t3."enable" = 1
		WHERE t3.vehicle_type_id = #{vehicleType}
		  AND t1.VALID = 1 limit 1
	</select>


</mapper>