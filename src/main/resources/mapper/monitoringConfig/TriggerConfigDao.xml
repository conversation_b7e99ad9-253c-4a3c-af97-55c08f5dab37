<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.monitoringConfig.dao.TriggerConfigDao">
	<sql id="triggerConfigColumnAlias">
		mt.id AS id,
		mt.slot_id AS slotId,
		mt.label AS label,
		mt.sort AS sort,
		mt.signal_id AS signalId,
		mt.signal_name_en AS signalNameEn,
		mt.trigger_value AS triggerValue,
		mt.data_display_point AS dataDisplayPoint,
		mt.unit_status AS unitStatus,
		mt.svg_url AS svgUrl,
        mt.image_type AS imageType,
        mt.image_path AS imagePath,
		mt.ext_properties AS extProperties,
		mt.create_time AS createTime,
		mt.create_by AS createBy,
		mt.modify_time AS modifyTime,
		mt.modify_by AS modifyBy,
		mt.del_flag AS delFlag
	</sql>

	<!--1、根据卡槽id查询trigger列表数据-->
	<select id="findTriggerBySlotId" resultType="cc.crrc.manage.monitoringConfig.entity.TriggerPO">
		SELECT
		<include refid="triggerConfigColumnAlias"/>,
		ms.slot_type AS slotType,
		nws.name_cn||'('||nws.name_en||')'  AS signalNameCn,
		nws.result_type AS resultType
		FROM
		monitor_trigger mt
		LEFT JOIN monitor_slot ms ON ms.id = mt.slot_id
		LEFT JOIN comm_original_signal nws ON nws.name_en = mt.signal_name_en
		WHERE
		mt.slot_id = #{slotId}
		AND
		mt.del_flag = false
		ORDER BY
		mt.sort ASC,mt.create_by DESC
	</select>

	<!--2、插入trigger数据-->
	<insert id="insertTriggers">
		INSERT INTO
		monitor_trigger(
		id,slot_id,label,sort,
		signal_id,signal_name_en,trigger_value,data_display_point,unit_status,
		svg_url,ext_properties,create_time,create_by,
		modify_time,modify_by,del_flag,image_type,image_path
		)
		VALUES
		<foreach collection="insertTriggerPOList" item="item" index="index" separator=",">
			(
			#{item.id},#{item.slotId},#{item.label},#{item.sort},
			#{item.signalId},#{item.signalNameEn},#{item.triggerValue},#{item.dataDisplayPoint},#{item.unitStatus},
			#{item.svgUrl},#{item.extProperties},#{item.createTime},#{item.createBy},
			#{item.modifyTime},#{item.modifyBy},#{item.delFlag},#{item.imageType},#{item.imagePath}
			)
		</foreach>
	</insert>

	<!--3、更新trigger数据-->
	<update id="updateTriggers">
		UPDATE
			monitor_trigger
		SET
			label = #{triggerPO.label},
			sort = #{triggerPO.sort},
			signal_id = #{triggerPO.signalId},
			signal_name_en = #{triggerPO.signalNameEn},
			trigger_value = #{triggerPO.triggerValue},
			data_display_point = #{triggerPO.dataDisplayPoint},
			unit_status = #{triggerPO.unitStatus},
			svg_url = #{triggerPO.svgUrl},
			image_type = #{triggerPO.imageType},
			image_path = #{triggerPO.imagePath},
			ext_properties = #{triggerPO.extProperties},
			modify_time = #{triggerPO.modifyTime},
			modify_by = #{triggerPO.modifyBy}
		WHERE
			id = #{triggerPO.id}
	</update>

	<!--4、根据triggerId删除对应的数据-->
	<update id="deleteTrigger">
		UPDATE
			monitor_trigger
		SET
			del_flag = true,
			modify_by = #{modifyBy},
			modify_time = now()
		WHERE
			id = #{id}
		AND
			del_flag = false
	</update>

	<!--5、根据项点id查询所有关联的triggerId-->
	<select id="findTriggerListByItemId" resultType="java.lang.String">
		SELECT
			mt.id
		FROM
			monitor_trigger mt
		LEFT JOIN monitor_slot ms ON ms.id = mt.slot_id
		LEFT JOIN monitor_table_format mtf ON mtf.id = ms.table_format_id
		LEFT JOIN monitor_table_item mti ON mti.id = mtf.item_id
		WHERE
			mt.del_flag = false
		AND
			ms.del_flag = false
		AND
			mti.del_flag = false
		AND
			mtf.del_flag = false
		AND
			mti.id = #{itemId}
	</select>

	<!--6、根据trigger Id集合批量逻辑删除-->
	<update id="deleteTriggersByItemId">
		UPDATE
			monitor_trigger
		SET
			del_flag = true
		WHERE
			id IN
			<foreach collection="triggerIdList" index="index" item="itemId" separator="," open="(" close=")">
				#{itemId}
			</foreach>
		AND
			del_flag = false
	</update>

	<!--7、根据slotId查询trigger数量-->
	<select id="findCountById" resultType="java.lang.Integer">
		SELECT
			COUNT(1)
		FROM
			monitor_trigger
		WHERE
			del_flag = false
		AND
			slot_id = #{slotId}
	</select>

	<!--8、根据slotId删除trigger-->
	<update id="deleteTriggersBySlotId">
        UPDATE
			monitor_trigger
		SET
			del_flag = true,
			modify_time = now()
		WHERE
			slot_id = #{slotId}
		AND
			del_flag = false
	</update>

	<select id="getTriggerIdListForCopy" resultType="java.lang.String">
        SELECT
			id
		FROM
			monitor_trigger
		WHERE
			del_flag = false
		AND
			slot_id in
		<foreach collection="slotIdsToDel" open="(" close=")" separator="," item="item">
			#{item}
		</foreach>
	</select>

</mapper>