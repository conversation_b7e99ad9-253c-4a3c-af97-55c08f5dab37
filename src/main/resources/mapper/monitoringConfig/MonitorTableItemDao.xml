<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.monitoringConfig.dao.MonitorTableItemDao">
    <sql id="monitorTableItemAlias">
        t.id,
        t.menu_id AS menuId,
        t.name,
        t.type,
        t.sort,
        t.relation_key AS relationKey,
        t.car_type AS carType,
        t.signal_function_id AS signalFunctionId,
        t.create_time AS createTime,
        t.create_by AS createBy,
        t.modify_time AS modifyTime,
        t.modify_by AS modifyBy,
        t.del_flag AS delFlag,
        t.pub_and_pri AS pubAndPri,
        t.slot_board_id AS slotBoardId
    </sql>

    <select id="structureList" resultType="java.lang.String">
        SELECT
          split_part(T.structure_position,'/',2)
        FROM
           stru_vehicle_structure_component T
        LEFT JOIN mtr_vehicle t1 ON t1.id = T.vehicle_id
        WHERE
          t1.vehicle_code = #{traCode}
          AND T.structure_type = '车厢'
          AND T.structure_position !~ '[\u2e80-\ua4cf]|[\uf900-\ufaff]|[\ufe30-\ufe4f]'
          AND T.del_flag = 0
        ORDER BY
          T.sort_number
    </select>
    <insert id="addItem">
        INSERT into monitor_table_item
        (
        id,
        menu_id,
        name,
        type,
        sort,
        relation_key,
        car_type,
        signal_function_id,
        create_time,
        create_by,
        modify_time,
        modify_by,
        del_flag,
        slot_board_id
        )values
        (
        #{id},
        #{menuId},
        #{name},
        #{type},
        #{sort},
        #{relationKey},
        #{carType},
        #{signalFunctionId},
        CURRENT_TIMESTAMP,
        #{createBy},
        CURRENT_TIMESTAMP,
        #{modifyBy},
        #{delFlag},
        #{slotBoardId}
        )
    </insert>
    <update id="deleteItem">
        UPDATE monitor_table_item
        SET
        del_flag = TRUE,
        modify_time = CURRENT_TIMESTAMP
        WHERE
        	ID = #{itemId}
    </update>
    <update id="editItem">
        UPDATE monitor_table_item
        <trim prefix="set" suffixOverrides=",">
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="relationKey != null and relationKey != ''">
                relation_key = #{relationKey},
            </if>
            <if test="sort != null and sort != ''">
                sort = #{sort},
            </if>
            <if test="carType != null and carType != ''">
                car_type = #{carType},
            </if>
          <!--  <if test="signalFunctionId != null and signalFunctionId != ''">-->
                signal_function_id = #{signalFunctionId},
            <!--</if>-->
            <if test="pubAndPri != null and pubAndPri != ''">
                pub_and_pri = #{pubAndPri},
            </if>
            <if test="slotBoardId != null and slotBoardId != ''">
                slot_board_id = #{slotBoardId},
            </if>
            modify_time = CURRENT_TIMESTAMP
        </trim>
        <where>
            id = #{id}
        </where>
    </update>
    <select id="list" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorTableItemEntity">
        select
        DISTINCT
        <include refid="monitorTableItemAlias"></include>,
        mt.label AS slotBoardName,
        sd1.label AS carTypeName,
        mtf.n_row AS row,
        mtf.n_column AS column,
        length(t.sort),
        mtf.n_row * mtf.n_column AS slotNumber
        from monitor_table_item t
        LEFT JOIN monitor_table_format mtf
        ON t.id = mtf.item_id and
        mtf.del_flag = false
        LEFT JOIN monitor_menu mm
        ON t.menu_id = mm.id and
        mm.del_flag = false
        LEFT JOIN sys_dict sd1
        ON sd1.value = t.car_type
        LEFT JOIN sys_dict_type sdt1
        ON
        sd1.type_id = sdt1.id and
        sdt1.type = 'car_type'
        and sd1.del_flag = '0'
        LEFT JOIN monitor_trigger mt
        ON t.slot_board_id =mt.slot_id
        and mt.del_flag = false
        <where>
            t.menu_id = #{menuId}
            <if test="type != null and type != ''">
                AND t.type = #{type}
            </if>
            <if test="slotBoardId != null and slotBoardId != ''">
                AND t.slot_board_id = #{slotBoardId}
            </if>
            and
            t.del_flag = false
        </where>
        ORDER BY length(t.sort) asc,t.sort asc

    </select>
    <select id="getItemInfoById" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorTableItemEntity">
        select
        <include refid="monitorTableItemAlias"></include>
        from monitor_table_item t
        <where>
            t.id = #{itemId}
            and
            t.del_flag = false
        </where>
    </select>
    <select id="listForPub" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorTableItemEntity">
        select
        <include refid="monitorTableItemAlias"></include>,
        mm.name||'-'||t.name as menuItemName
        from monitor_table_item t
        LEFT JOIN monitor_menu mm
        ON t.menu_id = mm.id
        <where>
            t.menu_id in (SELECT id from monitor_menu where tra_code = #{traCode})
            and
            t.pub_and_pri = 1
            and
            t.del_flag = false
            and
            mm.del_flag = false
        </where>
        ORDER BY t.sort
    </select>
    <select id="selectItemForDuplicateCheck"
            resultType="cc.crrc.manage.monitoringConfig.entity.MonitorTableItemEntity">
        select
        <include refid="monitorTableItemAlias"></include>
        from monitor_table_item t
        WHERE
        1=1
        <if test="id != null and id != ''">
            AND t.id != #{id}
        </if>
        <if test="menuId != null and menuId != ''">
            AND t.menu_id = #{menuId}
        </if>
        <if test="name != null and name != ''">
            AND t.name = #{name}
        </if>
        <if test="type != null and type != ''">
            AND t.type = #{type}
        </if>
        <if test="sort != null and sort != ''">
            AND t.sort = #{sort}
        </if>
        <if test="carType != null and carType != ''">
            AND t.car_type = #{carType}
        </if>
        <if test="signalFunctionId != null and signalFunctionId != ''">
            AND t.signal_function_id = #{signalFunctionId}
        </if>
        <if test="pubAndPri != null and pubAndPri != ''">
            AND t.pub_and_pri = #{pubAndPri}
        </if>
        <if test="relationKey != null and relationKey != ''">
            AND t.relation_key LIKE '%'||#{relationKey}||'%'
        </if>
        and
        t.del_flag = false
        ORDER BY t.sort
    </select>
    <select id="selectItemByRelationKey"
            resultType="cc.crrc.manage.monitoringConfig.entity.MonitorTableItemEntity">
        select
        <include refid="monitorTableItemAlias"></include>
        from monitor_table_item t
        WHERE
        t.menu_id = #{menuId}
        AND
        t.relation_key = #{relationKey}
        AND
        t.del_flag = false
    </select>
    <select id="listLabelAndLogo" resultType="cc.crrc.manage.monitoringConfig.entity.TriggerPO">
    SELECT DISTINCT
    regexp_replace( mt.label, '[0-9]', '', 'g' ) as label,
    mt.svg_url  as svgUrl
    FROM
	monitor_trigger mt
	JOIN monitor_slot ms ON mt.slot_id = ms.ID
	AND ms.del_flag =
	FALSE JOIN monitor_table_format mf ON mf.ID = ms.table_format_id
	AND mf.del_flag = false
    WHERE
	mt.del_flag = false
	AND mf.item_id =#{itemId}
    </select>

    <select id="selectItemForSvg" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorTableItemEntity">
        select
        <include refid="monitorTableItemAlias"></include>
        from monitor_table_item t
        WHERE
        1=1
        <if test="menuId != null and menuId != ''">
            AND t.menu_id = #{menuId}
        </if>
        and
        t.del_flag = false
        ORDER BY t.sort
    </select>

    <select id="getItemIdListForCopy" resultType="java.lang.String">
        select
        t.id
        from monitor_table_item t
        <where>
            t.menu_id in
            <foreach collection="menuIdsToDel" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
            and
            t.del_flag = false
        </where>
    </select>
    <insert id="addItems">
        insert into monitor_table_item
        (
        id,
        menu_id,
        name,
        type,
        sort,
        relation_key,
        car_type,
        signal_function_id,
        create_time,
        create_by,
        modify_time,
        modify_by,
        del_flag,
        pub_and_pri,
        slot_board_id
        )
        values
        <foreach collection="allItem" item="item" index="index" separator=",">
            (#{item.id},
            #{item.menuId},
            #{item.name},
            #{item.type},
            #{item.sort},
            #{item.relationKey},
            #{item.carType},
            #{item.signalFunctionId},
            current_timestamp,
            #{item.createBy},
            #{item.modifyTime},
            #{item.modifyBy},
            #{item.delFlag},
            #{item.pubAndPri},
            #{item.slotBoardId})
        </foreach>
    </insert>
    <select id="listForCopy" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorTableItemEntity">
        select
        <include refid="monitorTableItemAlias"/>
        from monitor_table_item t
        where t.del_flag = false
        and t.menu_id = #{menuId}
        ORDER BY
        t.type desc
    </select>
    <update id="updateSlotBoardId">
        UPDATE monitor_table_item mti
        SET slot_board_id = #{newSlotBoradId}
        FROM monitor_menu mm
        WHERE
	    mti.slot_board_id = #{oldSlotBoradId}
	    AND mti.menu_id = mm.ID
	    AND mm.tra_code = #{targetTraCode}
	    AND mti.del_flag = false
	    AND mm.del_flag = false
    </update>
</mapper>