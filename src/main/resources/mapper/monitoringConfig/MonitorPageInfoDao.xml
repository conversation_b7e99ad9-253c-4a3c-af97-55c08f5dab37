<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.monitoringConfig.dao.MonitorPageInfoDao">
    <sql id="MonitorPageInfoAlias">
        t.id,
		t.menu_code as menuCode,
		t.vehicle_type as vehicleType,
		t.html_content as htmlContent,
		t.css_content as cssContent,
		t.line_id as lineId,
		t.create_time AS createTime,
		t.create_by AS createBy,
		t.modify_time as modifyTime,
		t.modify_by as modifyBy,
		t.del_flag as delFlag,
        t.type
    </sql>
    <insert id="addMonitorPageInfo">
        INSERT into monitor_page_info
        (
            id,
            menu_code,
            vehicle_type,
            html_content,
            css_content,
            line_id,
            create_time,
            create_by,
            modify_time,
            modify_by,
            del_flag,
            type
        )values
        (
            #{id},
            #{menuCode},
            #{vehicleType},
            #{htmlContent},
            #{cssContent},
            #{lineId},
            CURRENT_TIMESTAMP,
            #{createBy},
            CURRENT_TIMESTAMP,
            #{modifyBy},
            false,
            #{type}
        )
    </insert>
    <select id="getCssByLineId" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorPageInfoPO">
        select
        <include refid="MonitorPageInfoAlias"></include>
        FROM monitor_page_info t
        <where>
            1=1
            <if test="lineId != null and lineId != ''">
                AND t.line_id = #{lineId}
            </if>
            and
            t.del_flag = false
            and
            t.type = 'css'
        </where>
    </select>
    <select id="getHtmlByMenuCode" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorPageInfoPO">
        select
        <include refid="MonitorPageInfoAlias"></include>
        FROM monitor_page_info t
        <where>
            1=1
            <if test="menuCode != null and menuCode != ''">
                AND t.menu_code = #{menuCode}
            </if>
            and
            t.del_flag = false
            and
            t.type = 'html'
        </where>
    </select>
    <update id="editMonitorPageInfo">
        UPDATE monitor_page_info
        <trim prefix="set" suffixOverrides=",">
            <if test="menuCode != null and menuCode != ''">
                menu_code = #{menuCode},
            </if>
            <if test="vehicleType != null and vehicleType != ''">
                vehicle_type = #{vehicleType},
            </if>
            <if test="htmlContent != null">
                html_content = #{htmlContent},
            </if>
            <if test="cssContent != null">
                css_content = #{cssContent},
            </if>
            <if test="modifyBy != null and modifyBy != ''">
                modify_by = #{modifyBy},
            </if>
            modify_time = CURRENT_TIMESTAMP
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy},
            </if>
            <if test="delFlag != null and delFlag != ''">
                del_flag = #{delFlag},
            </if>
        </trim>
        WHERE
            del_flag = false
        <if test="lineId != null and lineId != ''">
            AND line_id = #{lineId}
        </if>
        <if test="menuCode != null and menuCode != ''">
            AND menu_code = #{menuCode}
        </if>
        <if test="id != null and id != ''">
            AND id = #{id}
        </if>
        <if test="type != null and type != ''">
            AND type = #{type}
        </if>
    </update>

    <update id="deleteMonitorPageInfo">
        UPDATE monitor_page_info
        set
            del_flag = true
        WHERE
            id =#{id}
    </update>
    <select id="monitorPageInfoList" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorPageInfoPO">
        select
        <include refid="MonitorPageInfoAlias"></include>
        FROM monitor_page_info t
        <where>
            1=1
            <if test="menuCode != null and menuCode != ''">
                AND t.menu_code = #{menuCode}
            </if>
            <if test="vehicleType != null and vehicleType != ''">
                AND vehicle_type = #{vehicleType}
            </if>
            <if test="lineId != null and lineId != ''">
                AND line_id = #{lineId}
            </if>
            <if test="id != null and id != ''">
                AND id = #{id}
            </if>
            <if test="type != null and type != ''">
                AND type = #{type}
            </if>
            <if test="htmlContent != null">
                html_content LIKE '%'||#{htmlContent}||'%'
            </if>
            <if test="cssContent != null">
                css_content LIKE '%'||#{cssContent}||'%'
            </if>
            and
            t.del_flag = false
        </where>
    </select>


</mapper>