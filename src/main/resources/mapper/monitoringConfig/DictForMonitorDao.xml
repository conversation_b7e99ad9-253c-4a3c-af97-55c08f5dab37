<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.monitoringConfig.dao.DictForMonitorDao">

    <sql id="sysDictColumnAlias">
        t.id,
        t.sort_number AS sortNumber,
        t.code,
        t.label,
        t.value,
        t.type_id AS typeId,
        t.description
    </sql>
    <select id="getDictByType" resultType="cc.crrc.manage.monitoringConfig.entity.SysDictEntity">
        SELECT
        <include refid="sysDictColumnAlias"></include>
        FROM
        sys_dict t
        LEFT JOIN
        sys_dict_type t1
        ON
        t.type_id = t1.id
        WHERE
        t.del_flag = '0' AND
        t1.del_flag = '0'
        <if test="type != null">
            AND t1.type = #{type}
        </if>
        ORDER BY
        t.sort_number,t.modify_time DESC
    </select>
    <select id="getRelationKeyByMenuId" resultType="cc.crrc.manage.monitoringConfig.entity.SysDictEntity">
        select
        <include refid="sysDictColumnAlias"></include>
        from sys_dict t
        where
        t.del_flag = '0'
        <if test="lineId != null and lineId != ''">
            AND t.line_id = #{lineId}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId != ''">
            AND t.vehicle_type_id = #{vehicleTypeId}
        </if>
        AND t.type_code = 'relation_key'
        AND
        t.value not in (
        SELECT t1.relation_key FROM monitor_table_item t1 where t1.menu_id=#{menuId}
        AND t1.del_flag = '0'
        )
        ORDER BY
        t.sort_number ASC,
        t.label ASC
    </select>

    <select id="listDictUniversal" resultType="cc.crrc.manage.pojo.SysDictVO">
        SELECT
        DISTINCT
        t.id,
        t.sort_number AS sortNumber,
        t.code,
        t.label,
        t.value,
        t.type_id AS typeId,
        t.description,
        t.line_id AS lineId,
        t.vehicle_type_id AS vehicleTypeId,
        t.line_name AS lineName,
        t.vehicle_type_name AS vehicleTypeName,
        t.type_code AS typeCode
        FROM
        sys_dict t
        WHERE
        t.del_flag = '0'
        <if test="typeId != null and typeId != ''">
            AND t.type_id = #{typeId}
        </if>
        <if test="typeCode != null and typeCode != ''">
            AND t.type_code = #{typeCode}
        </if>
        <if test="lineId != null and lineId != ''">
            AND t.line_id = #{lineId}
        </if>
        <if test="vehicleTypeId != null and vehicleTypeId != ''">
            AND t.vehicle_type_id = #{vehicleTypeId}
        </if>
        ORDER BY
        t.sort_number ASC
    </select>
    <select id="getVehicleTypeAndLineIdById" resultType="java.util.Map">
        SELECT
            t1.metro_line_id AS "lineId",
            t2.vehicle_type_id AS "vehicleTypeId"
        FROM
            mtr_vehicle t1
                LEFT JOIN mtr_vehicle_type_relation t2 ON t1.ID = t2.vehicle_id
        WHERE
            t1.ID =  #{vehicleId}
    </select>
    <select id="getTypeLevel" resultType="java.lang.String">
        select
            type_level AS typeLevel
        from
            sys_dict_type
        WHERE
            del_flag = '0'
          AND
            type =#{typeCode}
    </select>
</mapper>