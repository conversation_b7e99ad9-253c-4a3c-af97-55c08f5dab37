<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.monitoringConfig.dao.MonitorSignalFunctionDao">


    <sql id="monitorSignalFunctionAlias">
        t.id,
		t.type,
		t.name,
		t.function_str as functionStr,
		t.description,
		t.function_name as functionName
    </sql>
    <select id="list" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorSignalFunctionPO">
        select
        <include refid="monitorSignalFunctionAlias"/>
        from monitor_signal_function t
        <where>
            del_flag = false
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
        </where>
    </select>
    <select id="listUsedFunctionKey" resultType="java.lang.String">
        select distinct
         redis_key
        from monitor_signal_function mf
        join monitor_table_item mi
        on mf.id = mi.signal_function_id
        and mi.del_flag = false
		left join monitor_menu mm
		on mm.id= mi.menu_id
		and mm.del_flag = false
        where mf.del_flag = false
		AND
		mm.tra_code = #{traCode}
    </select>


</mapper>