<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.monitoringConfig.dao.MonitorTableFormatDao">


    <sql id="monitorTableFormatAlias">
        t.id,
        t.item_id AS itemId,
        t.n_row AS NRow,
        t.n_column AS NColumn,
        t.vehicle_location AS vehicleLocation,
        t.create_time AS createTime,
        t.create_by AS createBy,
        t.modify_time AS modifyTime,
        t.modify_by AS modifyBy,
        t.del_flag AS delFlag,
        t.sort
    </sql>
    <insert id="addFormats">
        INSERT into monitor_table_format
        (
        id,
        item_id,
        n_row,
        n_column,
        vehicle_location,
        create_time,
        create_by,
        modify_time,
        modify_by,
        del_flag,
        sort
        )values
        <foreach collection="formatList" item="item" separator=",">
        (
        #{item.id},
        #{item.itemId},
        #{item.NRow},
        #{item.NColumn},
        #{item.vehicleLocation},
        CURRENT_TIMESTAMP,
        #{item.createBy},
        CURRENT_TIMESTAMP,
        #{item.modifyBy},
        #{item.delFlag},
        #{item.sort}
        )
        </foreach>
    </insert>
    <update id="deleteFormatByItemId">
        UPDATE monitor_table_format
        SET
        del_flag = TRUE,
        modify_time = CURRENT_TIMESTAMP
        WHERE
        	item_id = #{itemId}
    </update>
    <select id="selectFormatByItemId" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorTableFormatEntity">
        SELECT
        <include refid="monitorTableFormatAlias"></include>
        from monitor_table_format t
        WHERE t.item_id = #{itemId}
        AND t.del_flag = false
        ORDER BY t.sort
    </select>
    <select id="getFormatByItemId" resultType="cc.crrc.manage.monitoringConfig.entity.MonitorTableFormatEntity">
        select id , n_row as NRow, n_column as NColumn,vehicle_location as vehicleLocation
        from
        monitor_table_format
        where item_id = #{itemId}  and del_flag= false
        order by sort
    </select>
    <select id="getFormatIdListForCopy" resultType="java.lang.String">
        SELECT
        t.id
        from monitor_table_format t
        WHERE t.item_id in
        <foreach collection="itemIdsToDel" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        AND t.del_flag = false
        ORDER BY t.sort
    </select>
</mapper>