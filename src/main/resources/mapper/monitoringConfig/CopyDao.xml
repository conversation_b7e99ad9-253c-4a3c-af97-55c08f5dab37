<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cc.crrc.manage.monitoringConfig.dao.CopyDao">
    <sql id="MonitorMenuColumnAlias">
        id,
        menu_code as menuCode,
        parent_id as parentId,
        name,
        sort,
        board_status as boardStatus,
        menu_type as menuType,
        url,
        create_time as createTime,
        create_by as createBy,
        modify_time as modifyTime,
        modify_by as modifyBy,
        show_status as showStatus,
        del_flag as delFlag
   </sql>
    <sql id="monitorTableItemAlias">
        t.id,
        t.menu_id AS menuId,
        t.name,
        t.type,
        t.sort,
        t.relation_key AS relationKey,
        t.car_type AS carType,
        t.signal_function_id AS signalFunctionId,
        t.create_time AS createTime,
        t.create_by AS createBy,
        t.modify_time AS modifyTime,
        t.modify_by AS modifyBy,
        t.del_flag AS delFlag,
        t.pub_and_pri AS pubAndPri,
        t.slot_board_id as slotBoardId
    </sql>

    <update id="updateParentId">
        update monitor_menu set parent_id = #{newMenuId} where parent_id = #{oldMenuId} and tra_code=#{targetTraCode}
    </update>

    <delete id="delMenu">
        UPDATE monitor_menu
        SET
        del_flag = true,
        modify_time = CURRENT_TIMESTAMP
        WHERE
        id in
        <foreach collection="menuIds" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>

    </delete>
    <delete id="delItem">
        UPDATE monitor_table_item
        SET
        del_flag = true,
        modify_time = CURRENT_TIMESTAMP
        WHERE
        id in
        <foreach collection="itemIdsToDel" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>
    <delete id="delFormat">
        UPDATE monitor_table_format
        SET
        del_flag = true,
        modify_time = CURRENT_TIMESTAMP
        WHERE
        id in
        <foreach collection="formatIdsToDel" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>
    <delete id="delSlot">
        UPDATE monitor_slot
        SET
        del_flag = true,
        modify_time = CURRENT_TIMESTAMP
        WHERE
        id in
        <foreach collection="slotIdsToDel" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>
    <delete id="delTrigger">
        UPDATE monitor_trigger
        SET
        del_flag = true,
        modify_time = CURRENT_TIMESTAMP
        WHERE
        id in
        <foreach collection="triggerIdsToDel" index="index" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>

    <resultMap id="WebsocketVO" type="cc.crrc.manage.monitoringConfig.entity.WebsocketVO">
        <result property="id" column="menuId"></result>
        <collection property="websocketItemEntity"
                    ofType="cc.crrc.manage.monitoringConfig.entity.WebsocketItemEntity">
            <result property="id" column="itemId"></result>
            <result property="type" column="type"></result>
            <result property="relationKey" column="relationKey"></result>
            <result property="monitorSignalFunctionPO.id" column="signalFunctionId"></result>
            <result property="name" column="name"></result>
            <result property="carType" column="carType"></result>
            <result property="slotBoardId" column="slotBoardId"></result>
            <result property="monitorSignalFunctionPO.redisKey" column="functionStr"></result>
            <result property="monitorSignalFunctionPO.functionName" column="functionName"></result>
            <result property="monitorSignalFunctionPO.redisKey" column="redisKey"></result>
            <result property="monitorSignalFunctionPO.functionStr" column="functionStr"></result>

            <collection property="websocketFormatEntity"
                        ofType="cc.crrc.manage.monitoringConfig.entity.WebsocketFormatEntity">
                <result property="id" column="formatId"></result>
                <result property="vehicleLocation" column="vehicleLocation"></result>
                <result property="NRow" column="NRow"></result>
                <result property="NColumn" column="NColumn"></result>
                <collection property="websocketSlotEntity"
                            ofType="cc.crrc.manage.monitoringConfig.entity.WebsocketSlotEntity">
                    <result property="id" column="slotId"></result>
                    <result property="slotType" column="slotType"></result>
                    <collection property="websocketTriggerEntity"
                                ofType="cc.crrc.manage.monitoringConfig.entity.WebsocketTriggerEntity">
                        <result property="resultType" column="resultType"></result>
                        <result property="id" column="triggerId"></result>
                        <result property="signalId" column="signalId"></result>
                        <result property="signalNameEn" column="nameEn"></result>
                        <result property="dataDisplayPoint" column="dataDisplayPoint"></result>
                        <result property="svgUrl" column="svgUrl"></result>
                        <result property="imageType" column="imageType"></result>
                        <result property="imagePath" column="imagePath"></result>
                        <result property="label" column="label"></result>
                        <result property="triggerValue" column="triggerValue"></result>
                        <result property="extProperties" column="extProperties"></result>
                        <result property="sort" column="sort"></result>
                    </collection>
                </collection>
            </collection>
        </collection>
    </resultMap>


    <select id="getWebsocketVO" resultMap="WebsocketVO">
    SELECT
	mm.ID menuId,
	mi.type as type,
	mi.ID itemId,
	mi.relation_key relationKey,
	mi.signal_function_id signalFunctionId,
	mi.NAME AS NAME,
	mi.car_type carType,
	mi.slot_board_id slotBoardId,
	msf.function_str functionStr,
	msf.function_name functionName,
	msf.redis_key redisKey,
	mf.ID formatId,
	mf.vehicle_location vehicleLocation,
	mf.n_row NRow,
	mf.n_column NColumn,
	ms.ID slotId,
	ms.slot_type slotType,
	mt.ID triggerId,
	mt.signal_id signalId,
	mt.data_display_point dataDisplayPoint,
	mt.svg_url svgUrl,
    mt.image_type imageType,
    mt.image_path imagePath,
	mt.label AS label,
	mt.trigger_value triggerValue,
	mt.ext_properties extProperties,
	mt.sort sort,
	ns.name_en nameEn,
	ns.result_type resultType
    FROM
	monitor_menu mm
	LEFT JOIN monitor_table_item mi ON mi.menu_id = mm.ID
	AND mi.del_flag = false
	LEFT JOIN monitor_signal_function MSF
    ON MSF.ID = MI.signal_function_id
	 AND MSF.del_flag = false
	LEFT JOIN monitor_table_format mf ON mf.item_id = mi.ID
	AND mf.del_flag = false LEFT JOIN monitor_slot ms ON ms.table_format_id = mf.ID
	AND ms.del_flag = false LEFT JOIN monitor_trigger mt ON mt.slot_id = ms.ID
-- 	AND mt.del_flag = false LEFT JOIN comm_original_signal ns ON ns.ID = mt.signal_id  2021-08-25 根据杭州s1号线 张志坚、史磊修改的 根据信号英文名做关联 修改人：李鑫
    AND mt.del_flag = false LEFT JOIN comm_original_signal ns ON ns.name_en = mt.signal_name_en
    WHERE
	mm.del_flag = false
	AND mm.ID = #{menuId}
	order by
    length(mi.sort),
	mi.sort,
	length(mf.sort),
	mf.sort,
	ms.sort,
	mt.sort
	asc

    </select>


</mapper>


