delete  from  comm_original_signal_new where name_en  in(
'HMI_CxOneSHDFlt_1',
'HMI_CxOneSHDFlt_2',
'HMI_CxOneSHDFlt_3',
'HMI_CxOneSHDFlt_4',
'HMI_CxOneLHDFlt_1',
'HMI_CxOneLHDFlt_2',
'HMI_CxOneLHDFlt_3',
'HMI_CxOneLHDFlt_4'
);

INSERT INTO comm_original_signal_new (id,protocol_id,name_cn,name_en,byte_offset,bit_offset,data_type,unit,"location",subsystem,parse_script,create_by,create_time,modify_by,modify_time,remark,result_type,target_id,target_name,port_no,"ifRepeat",fault_flag,signal_type,frames_type,package_order) VALUES
	 ('2813255421','88','客室右侧（第三个座椅下电气柜）烟火故障','HMI_CxSHD1_Flt_1',777,0,'BOOLEAN1','','TMc1','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255421','HMI_CxSHD1_Flt_1','097',NULL,NULL,NULL,2,6),
	 ('2813255422','88','客室右侧（第二个座椅下电气柜）烟火故障','HMI_CxSHD2_Flt_1',777,1,'BOOLEAN1','','TMc1','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255422','HMI_CxSHD2_Flt_1','097',NULL,NULL,NULL,2,6),
	 ('2813255423','88','客室右侧（第一个座椅下电气柜）烟火故障','HMI_CxSHD3_Flt_1',777,2,'BOOLEAN1','','TMc1','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255423','HMI_CxSHD3_Flt_1','097',NULL,NULL,NULL,2,6),
	 ('2813255424','88','一位侧一位端侧顶烟火故障                       ','HMI_CxSHD4_Flt_1',777,3,'BOOLEAN1','','TMc1','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255424','HMI_CxSHD4_Flt_1','097',NULL,NULL,NULL,2,6),
	 ('2813255425','88','客室左侧（第一个座椅下电气柜）烟火故障','HMI_CxSHD5_Flt_1',777,4,'BOOLEAN1','','TMc1','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255425','HMI_CxSHD5_Flt_1','097',NULL,NULL,NULL,2,6),
	 ('2813255426','88','客室左侧（第三个座椅下电气柜）烟火故障','HMI_CxSHD6_Flt_1',777,5,'BOOLEAN1','','TMc1','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255426','HMI_CxSHD6_Flt_1','097',NULL,NULL,NULL,2,6),
	 ('2813255427','88','二位侧二位端侧顶烟火故障                     ','HMI_CxSHD7_Flt_1',777,6,'BOOLEAN1','','TMc1','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255427','HMI_CxSHD7_Flt_1','097',NULL,NULL,NULL,2,6),
	 ('2813255428','88','客室二位端左侧电气柜烟火故障','HMI_CxSHD8_Flt_1',777,7,'BOOLEAN1','','TMc1','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255428','HMI_CxSHD8_Flt_1','097',NULL,NULL,NULL,2,6),
	 ('2813255429','88','客室二位端右侧电气柜烟火故障','HMI_CxSHD9_Flt_1',778,0,'BOOLEAN1','','TMc1','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255429','HMI_CxSHD9_Flt_1','097',NULL,NULL,NULL,2,6),
	 ('2813255430','88','二位侧二位端侧顶烟火故障                     ','HMI_CxSHD1_Flt_2',778,1,'BOOLEAN1','','Mp1','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255430','HMI_CxSHD1_Flt_2','097',NULL,NULL,NULL,2,6);
INSERT INTO comm_original_signal_new (id,protocol_id,name_cn,name_en,byte_offset,bit_offset,data_type,unit,"location",subsystem,parse_script,create_by,create_time,modify_by,modify_time,remark,result_type,target_id,target_name,port_no,"ifRepeat",fault_flag,signal_type,frames_type,package_order) VALUES
	 ('2813255431','88','一位侧一位端侧顶烟火故障                       ','HMI_CxSHD2_Flt_2',778,2,'BOOLEAN1','','Mp1','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255431','HMI_CxSHD2_Flt_2','097',NULL,NULL,NULL,2,6),
	 ('2813255432','88','客室二位端右侧电气柜烟火故障','HMI_CxSHD3_Flt_2',778,3,'BOOLEAN1','','Mp1','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255432','HMI_CxSHD3_Flt_2','097',NULL,NULL,NULL,2,6),
	 ('2813255433','88','客室二位端左侧电气柜烟火故障','HMI_CxSHD4_Flt_2',778,4,'BOOLEAN1','','Mp1','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255433','HMI_CxSHD4_Flt_2','097',NULL,NULL,NULL,2,6),
	 ('2813255434','88','二位侧二位端侧顶烟火故障                     ','HMI_CxSHD1_Flt_3',778,5,'BOOLEAN1','','Mp2','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255434','HMI_CxSHD1_Flt_3','097',NULL,NULL,NULL,2,6),
	 ('2813255435','88','一位侧一位端侧顶烟火故障                       ','HMI_CxSHD2_Flt_3',778,6,'BOOLEAN1','','Mp2','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255435','HMI_CxSHD2_Flt_3','097',NULL,NULL,NULL,2,6),
	 ('2813255436','88','客室二位端右侧电气柜烟火故障','HMI_CxSHD3_Flt_3',778,7,'BOOLEAN1','','Mp2','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255436','HMI_CxSHD3_Flt_3','097',NULL,NULL,NULL,2,6),
	 ('2813255437','88','客室二位端左侧电气柜烟火故障','HMI_CxSHD4_Flt_3',779,0,'BOOLEAN1','','Mp2','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255437','HMI_CxSHD4_Flt_3','097',NULL,NULL,NULL,2,6),
	 ('2813255438','88','客室右侧（第三个座椅下电气柜）烟火故障','HMI_CxSHD1_Flt_4',779,1,'BOOLEAN1','','TMc2','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255438','HMI_CxSHD1_Flt_4','097',NULL,NULL,NULL,2,6),
	 ('2813255439','88','客室右侧（第二个座椅下电气柜）烟火故障','HMI_CxSHD2_Flt_4',779,2,'BOOLEAN1','','TMc2','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255439','HMI_CxSHD2_Flt_4','097',NULL,NULL,NULL,2,6),
	 ('2813255440','88','客室右侧（第一个座椅下电气柜）烟火故障','HMI_CxSHD3_Flt_4',779,3,'BOOLEAN1','','TMc2','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255440','HMI_CxSHD3_Flt_4','097',NULL,NULL,NULL,2,6);
INSERT INTO comm_original_signal_new (id,protocol_id,name_cn,name_en,byte_offset,bit_offset,data_type,unit,"location",subsystem,parse_script,create_by,create_time,modify_by,modify_time,remark,result_type,target_id,target_name,port_no,"ifRepeat",fault_flag,signal_type,frames_type,package_order) VALUES
	 ('2813255441','88','一位侧一位端侧顶烟火故障                       ','HMI_CxSHD4_Flt_4',779,4,'BOOLEAN1','','TMc2','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255441','HMI_CxSHD4_Flt_4','097',NULL,NULL,NULL,2,6),
	 ('2813255442','88','客室左侧（第一个座椅下电气柜）烟火故障','HMI_CxSHD5_Flt_4',779,5,'BOOLEAN1','','TMc2','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255442','HMI_CxSHD5_Flt_4','097',NULL,NULL,NULL,2,6),
	 ('2813255443','88','客室左侧（第三个座椅下电气柜）烟火故障','HMI_CxSHD6_Flt_4',779,6,'BOOLEAN1','','TMc2','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255443','HMI_CxSHD6_Flt_4','097',NULL,NULL,NULL,2,6),
	 ('2813255444','88','二位侧二位端侧顶烟火故障                     ','HMI_CxSHD7_Flt_4',779,7,'BOOLEAN1','','TMc2','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255444','HMI_CxSHD7_Flt_4','097',NULL,NULL,NULL,2,6),
	 ('2813255445','88','客室二位端左侧电气柜烟火故障','HMI_CxSHD8_Flt_4',780,0,'BOOLEAN1','','TMc2','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255445','HMI_CxSHD8_Flt_4','097',NULL,NULL,NULL,2,6),
	 ('2813255446','88','客室二位端右侧电气柜烟火故障','HMI_CxSHD9_Flt_4',780,1,'BOOLEAN1','','TMc2','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255446','HMI_CxSHD9_Flt_4','097',NULL,NULL,NULL,2,6),
	 ('2813255447','88','牵引箱感温电缆1故障','HMI_CxLHD1_Flt_1',780,2,'BOOLEAN1','','TMc1','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255447','HMI_CxLHD1_Flt_1','097',NULL,NULL,NULL,2,6),
	 ('2813255448','88','辅助箱感温电缆2故障','HMI_CxLHD2_Flt_1',780,3,'BOOLEAN1','','TMc1','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255448','HMI_CxLHD2_Flt_1','097',NULL,NULL,NULL,2,6),
	 ('2813255449','88','牵引箱1感温电缆1故障','HMI_CxLHD1_Flt_2',780,4,'BOOLEAN1','','Mp1','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255449','HMI_CxLHD1_Flt_2','097',NULL,NULL,NULL,2,6),
	 ('2813255450','88','牵引箱2感温电缆2故障','HMI_CxLHD2_Flt_2',780,5,'BOOLEAN1','','Mp1','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255450','HMI_CxLHD2_Flt_2','097',NULL,NULL,NULL,2,6);
INSERT INTO comm_original_signal_new (id,protocol_id,name_cn,name_en,byte_offset,bit_offset,data_type,unit,"location",subsystem,parse_script,create_by,create_time,modify_by,modify_time,remark,result_type,target_id,target_name,port_no,"ifRepeat",fault_flag,signal_type,frames_type,package_order) VALUES
	 ('2813255451','88','牵引箱1感温电缆1故障','HMI_CxLHD1_Flt_3',780,6,'BOOLEAN1','','Mp2','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255451','HMI_CxLHD1_Flt_3','097',NULL,NULL,NULL,2,6),
	 ('2813255452','88','牵引箱2感温电缆2故障','HMI_CxLHD2_Flt_3',780,7,'BOOLEAN1','','Mp2','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255452','HMI_CxLHD2_Flt_3','097',NULL,NULL,NULL,2,6),
	 ('2813255453','88','牵引箱感温电缆1故障','HMI_CxLHD1_Flt_4',781,0,'BOOLEAN1','','TMc2','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255453','HMI_CxLHD1_Flt_4','097',NULL,NULL,NULL,2,6),
	 ('2813255454','88','辅助箱感温电缆2故障','HMI_CxLHD2_Flt_4',781,1,'BOOLEAN1','','TMc2','VCU',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=故障','Integer','2813255454','HMI_CxLHD2_Flt_4','097',NULL,NULL,NULL,2,6),
	 ('2813255455','88','空调设定温度','ATC1_IusTargTemp_1',750,0,'UNSIGNED8','℃','TMc1','ATC',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=1℃,20-28℃','Integer','2813255455','ATC1_IusTargTemp_1','096',NULL,NULL,NULL,2,4),
	 ('2813255456','88','空调设定温度','ATC1_IusTargTemp_2',751,0,'UNSIGNED8','℃','Mp1','ATC',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=1℃,20-28℃','Integer','2813255456','ATC1_IusTargTemp_2','096',NULL,NULL,NULL,2,4),
	 ('2813255457','88','空调设定温度','ATC1_IusTargTemp_3',752,0,'UNSIGNED8','℃','Mp2','ATC',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=1℃,20-28℃','Integer','2813255457','ATC1_IusTargTemp_3','097',NULL,NULL,NULL,2,4),
	 ('2813255458','88','空调设定温度','ATC1_IusTargTemp_4',753,0,'UNSIGNED8','℃','TMc2','ATC',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=1℃,20-28℃','Integer','2813255458','ATC1_IusTargTemp_4','097',NULL,NULL,NULL,2,4),
	 ('2813255459','88','空调设定温度','ATC2_IusTargTemp_1',846,0,'UNSIGNED8','℃','TMc1','ATC',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=1℃,20-28℃','Integer','2813255459','ATC2_IusTargTemp_1','2A1',NULL,NULL,NULL,2,4),
	 ('2813255460','88','空调设定温度','ATC2_IusTargTemp_2',847,0,'UNSIGNED8','℃','Mp1','ATC',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=1℃,20-28℃','Integer','2813255460','ATC2_IusTargTemp_2','2A1',NULL,NULL,NULL,2,4);
INSERT INTO comm_original_signal_new (id,protocol_id,name_cn,name_en,byte_offset,bit_offset,data_type,unit,"location",subsystem,parse_script,create_by,create_time,modify_by,modify_time,remark,result_type,target_id,target_name,port_no,"ifRepeat",fault_flag,signal_type,frames_type,package_order) VALUES
	 ('2813255461','88','空调设定温度','ATC2_IusTargTemp_3',848,0,'UNSIGNED8','℃','Mp2','ATC',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=1℃,20-28℃','Integer','2813255461','ATC2_IusTargTemp_3','2AE',NULL,NULL,NULL,2,4),
	 ('2813255462','88','空调设定温度','ATC2_IusTargTemp_4',849,0,'UNSIGNED8','℃','TMc2','ATC',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=1℃,20-28℃','Integer','2813255462','ATC2_IusTargTemp_4','2AE',NULL,NULL,NULL,2,4),
	 ('2813255463','88','空调设定温度','ATC3_IusTargTemp_1',942,0,'UNSIGNED8','℃','TMc1','ATC',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=1℃,20-28℃','Integer','2813255463','ATC3_IusTargTemp_1','099',NULL,NULL,NULL,2,4),
	 ('2813255464','88','空调设定温度','ATC3_IusTargTemp_2',943,0,'UNSIGNED8','℃','Mp1','ATC',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=1℃,20-28℃','Integer','2813255464','ATC3_IusTargTemp_2','099',NULL,NULL,NULL,2,4),
	 ('2813255465','88','空调设定温度','ATC3_IusTargTemp_3',944,0,'UNSIGNED8','℃','Mp2','ATC',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=1℃,20-28℃','Integer','2813255465','ATC3_IusTargTemp_3','09A',NULL,NULL,NULL,2,4),
	 ('2813255466','88','空调设定温度','ATC3_IusTargTemp_4',945,0,'UNSIGNED8','℃','TMc2','ATC',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=1℃,20-28℃','Integer','2813255466','ATC3_IusTargTemp_4','09A',NULL,NULL,NULL,2,4),
	 ('2813255467','88','空调设定温度','ATC4_IusTargTemp_1',58,0,'UNSIGNED8','℃','TMc1','ATC',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=1℃,20-28℃','Integer','2813255467','ATC4_IusTargTemp_1','081',NULL,NULL,NULL,2,5),
	 ('2813255468','88','空调设定温度','ATC4_IusTargTemp_2',59,0,'UNSIGNED8','℃','Mp1','ATC',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=1℃,20-28℃','Integer','2813255468','ATC4_IusTargTemp_2','081',NULL,NULL,NULL,2,5),
	 ('2813255469','88','空调设定温度','ATC4_IusTargTemp_3',60,0,'UNSIGNED8','℃','Mp2','ATC',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=1℃,20-28℃','Integer','2813255469','ATC4_IusTargTemp_3','081',NULL,NULL,NULL,2,5),
	 ('2813255470','88','空调设定温度','ATC4_IusTargTemp_4',61,0,'UNSIGNED8','℃','TMc2','ATC',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=1℃,20-28℃','Integer','2813255470','ATC4_IusTargTemp_4','081',NULL,NULL,NULL,2,5);
INSERT INTO comm_original_signal_new (id,protocol_id,name_cn,name_en,byte_offset,bit_offset,data_type,unit,"location",subsystem,parse_script,create_by,create_time,modify_by,modify_time,remark,result_type,target_id,target_name,port_no,"ifRepeat",fault_flag,signal_type,frames_type,package_order) VALUES
	 ('2813255471','88','空调设定温度反馈','ATC_CusTargeTemp_1',886,0,'UNSIGNED8','℃','TMc1','ATC',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=1℃,20-28℃','Integer','2813255471','ATC_CusTargeTemp_1','3AE',NULL,NULL,NULL,2,5),
	 ('2813255472','88','空调设定温度反馈','ATC_CusTargeTemp_2',887,0,'UNSIGNED8','℃','Mp1','ATC',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=1℃,20-28℃','Integer','2813255472','ATC_CusTargeTemp_2','3AE',NULL,NULL,NULL,2,5),
	 ('2813255473','88','空调设定温度反馈','ATC_CusTargeTemp_3',888,0,'UNSIGNED8','℃','Mp2','ATC',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=1℃,20-28℃','Integer','2813255473','ATC_CusTargeTemp_3','3AE',NULL,NULL,NULL,2,5),
	 ('2813255474','88','空调设定温度反馈','ATC_CusTargeTemp_4',889,0,'UNSIGNED8','℃','TMc2','ATC',NULL,NULL,'2023-12-21 10:08:00',NULL,'2023-12-21 10:08:00','1=1℃,20-28℃','Integer','2813255474','ATC_CusTargeTemp_4','3AE',NULL,NULL,NULL,2,5);
