delete  from comm_original_signal where name_en in(
'HMI_CxOneSHDFlt_1',
'HMI_CxOneSHDFlt_2',
'HMI_CxOneSHDFlt_3',
'HMI_CxOneSHDFlt_4',
'HMI_CxOneLHDFlt_1',
'HMI_CxOneLHDFlt_2',
'HMI_CxOneLHDFlt_3',
'HMI_CxOneLHDFlt_4');

delete  from ekb_fault_type where fault_type_key in
('VCU_C006_64655',
'VCU_C005_64647',
'VCU_C005_64652',
'VCU_C006_64660',
'VCU_C006_64656',
'VCU_C005_64650',
'VCU_C006_64657',
'VCU_C005_64651');

INSERT INTO comm_original_signal (id,protocol_id,name_cn,name_en,byte_offset,bit_offset,data_type,unit,"location",subsystem,parse_script,fault_type_key,trigger_value,max_value,min_value,create_by,create_time,modify_by,modify_time,remark,redis_flag,result_type,frames_type,package_order) VALUES
	 ('2813255421','88','客室右侧（第三个座椅下电气柜）烟火故障','HMI_CxSHD1_Flt_1',777,0,'BOOLEAN1','','TMc1','VCU','','VCU_C100_69745',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255422','88','客室右侧（第二个座椅下电气柜）烟火故障','HMI_CxSHD2_Flt_1',777,1,'BOOLEAN1','','TMc1','VCU','','VCU_C100_69746',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255423','88','客室右侧（第一个座椅下电气柜）烟火故障','HMI_CxSHD3_Flt_1',777,2,'BOOLEAN1','','TMc1','VCU','','VCU_C100_69747',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255424','88','一位侧一位端侧顶烟火故障                       ','HMI_CxSHD4_Flt_1',777,3,'BOOLEAN1','','TMc1','VCU','','VCU_C100_69748',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255425','88','客室左侧（第一个座椅下电气柜）烟火故障','HMI_CxSHD5_Flt_1',777,4,'BOOLEAN1','','TMc1','VCU','','VCU_C100_69749',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255426','88','客室左侧（第三个座椅下电气柜）烟火故障','HMI_CxSHD6_Flt_1',777,5,'BOOLEAN1','','TMc1','VCU','','VCU_C100_69750',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255427','88','二位侧二位端侧顶烟火故障                     ','HMI_CxSHD7_Flt_1',777,6,'BOOLEAN1','','TMc1','VCU','','VCU_C100_69751',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255428','88','客室二位端左侧电气柜烟火故障','HMI_CxSHD8_Flt_1',777,7,'BOOLEAN1','','TMc1','VCU','','VCU_C100_69752',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255429','88','客室二位端右侧电气柜烟火故障','HMI_CxSHD9_Flt_1',778,0,'BOOLEAN1','','TMc1','VCU','','VCU_C100_69753',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255430','88','二位侧二位端侧顶烟火故障                     ','HMI_CxSHD1_Flt_2',778,1,'BOOLEAN1','','Mp1','VCU','','VCU_C100_69754',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6);
INSERT INTO comm_original_signal (id,protocol_id,name_cn,name_en,byte_offset,bit_offset,data_type,unit,"location",subsystem,parse_script,fault_type_key,trigger_value,max_value,min_value,create_by,create_time,modify_by,modify_time,remark,redis_flag,result_type,frames_type,package_order) VALUES
	 ('2813255431','88','一位侧一位端侧顶烟火故障                       ','HMI_CxSHD2_Flt_2',778,2,'BOOLEAN1','','Mp1','VCU','','VCU_C100_69755',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255432','88','客室二位端右侧电气柜烟火故障','HMI_CxSHD3_Flt_2',778,3,'BOOLEAN1','','Mp1','VCU','','VCU_C100_69756',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255433','88','客室二位端左侧电气柜烟火故障','HMI_CxSHD4_Flt_2',778,4,'BOOLEAN1','','Mp1','VCU','','VCU_C100_69757',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255434','88','二位侧二位端侧顶烟火故障                     ','HMI_CxSHD1_Flt_3',778,5,'BOOLEAN1','','Mp2','VCU','','VCU_C100_69758',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255435','88','一位侧一位端侧顶烟火故障                       ','HMI_CxSHD2_Flt_3',778,6,'BOOLEAN1','','Mp2','VCU','','VCU_C100_69759',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255436','88','客室二位端右侧电气柜烟火故障','HMI_CxSHD3_Flt_3',778,7,'BOOLEAN1','','Mp2','VCU','','VCU_C100_69760',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255437','88','客室二位端左侧电气柜烟火故障','HMI_CxSHD4_Flt_3',779,0,'BOOLEAN1','','Mp2','VCU','','VCU_C100_69761',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255438','88','客室右侧（第三个座椅下电气柜）烟火故障','HMI_CxSHD1_Flt_4',779,1,'BOOLEAN1','','TMc2','VCU','','VCU_C100_69762',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255439','88','客室右侧（第二个座椅下电气柜）烟火故障','HMI_CxSHD2_Flt_4',779,2,'BOOLEAN1','','TMc2','VCU','','VCU_C100_69763',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255440','88','客室右侧（第一个座椅下电气柜）烟火故障','HMI_CxSHD3_Flt_4',779,3,'BOOLEAN1','','TMc2','VCU','','VCU_C100_69764',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6);
INSERT INTO comm_original_signal (id,protocol_id,name_cn,name_en,byte_offset,bit_offset,data_type,unit,"location",subsystem,parse_script,fault_type_key,trigger_value,max_value,min_value,create_by,create_time,modify_by,modify_time,remark,redis_flag,result_type,frames_type,package_order) VALUES
	 ('2813255441','88','一位侧一位端侧顶烟火故障                       ','HMI_CxSHD4_Flt_4',779,4,'BOOLEAN1','','TMc2','VCU','','VCU_C100_69765',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255442','88','客室左侧（第一个座椅下电气柜）烟火故障','HMI_CxSHD5_Flt_4',779,5,'BOOLEAN1','','TMc2','VCU','','VCU_C100_69766',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255443','88','客室左侧（第三个座椅下电气柜）烟火故障','HMI_CxSHD6_Flt_4',779,6,'BOOLEAN1','','TMc2','VCU','','VCU_C100_69767',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255444','88','二位侧二位端侧顶烟火故障                     ','HMI_CxSHD7_Flt_4',779,7,'BOOLEAN1','','TMc2','VCU','','VCU_C100_69768',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255445','88','客室二位端左侧电气柜烟火故障','HMI_CxSHD8_Flt_4',780,0,'BOOLEAN1','','TMc2','VCU','','VCU_C100_69769',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255446','88','客室二位端右侧电气柜烟火故障','HMI_CxSHD9_Flt_4',780,1,'BOOLEAN1','','TMc2','VCU','','VCU_C100_69770',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255447','88','牵引箱感温电缆1故障','HMI_CxLHD1_Flt_1',780,2,'BOOLEAN1','','TMc1','VCU','','VCU_C101_69771',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255448','88','辅助箱感温电缆2故障','HMI_CxLHD2_Flt_1',780,3,'BOOLEAN1','','TMc1','VCU','','VCU_C101_69772',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255449','88','牵引箱1感温电缆1故障','HMI_CxLHD1_Flt_2',780,4,'BOOLEAN1','','Mp1','VCU','','VCU_C101_69773',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255450','88','牵引箱2感温电缆2故障','HMI_CxLHD2_Flt_2',780,5,'BOOLEAN1','','Mp1','VCU','','VCU_C101_69774',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6);
INSERT INTO comm_original_signal (id,protocol_id,name_cn,name_en,byte_offset,bit_offset,data_type,unit,"location",subsystem,parse_script,fault_type_key,trigger_value,max_value,min_value,create_by,create_time,modify_by,modify_time,remark,redis_flag,result_type,frames_type,package_order) VALUES
	 ('2813255451','88','牵引箱1感温电缆1故障','HMI_CxLHD1_Flt_3',780,6,'BOOLEAN1','','Mp2','VCU','','VCU_C101_69775',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255452','88','牵引箱2感温电缆2故障','HMI_CxLHD2_Flt_3',780,7,'BOOLEAN1','','Mp2','VCU','','VCU_C101_69776',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255453','88','牵引箱感温电缆1故障','HMI_CxLHD1_Flt_4',781,0,'BOOLEAN1','','TMc2','VCU','','VCU_C101_69777',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255454','88','辅助箱感温电缆2故障','HMI_CxLHD2_Flt_4',781,1,'BOOLEAN1','','TMc2','VCU','','VCU_C101_69778',1,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','1','Integer',2,6),
	 ('2813255455','88','空调设定温度','ATC1_IusTargTemp_1',750,0,'UNSIGNED8','℃','TMc1','ATC','','',NULL,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=1℃,20-28℃','1','Integer',2,4),
	 ('2813255456','88','空调设定温度','ATC1_IusTargTemp_2',751,0,'UNSIGNED8','℃','Mp1','ATC','','',NULL,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=1℃,20-28℃','1','Integer',2,4),
	 ('2813255457','88','空调设定温度','ATC1_IusTargTemp_3',752,0,'UNSIGNED8','℃','Mp2','ATC','','',NULL,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=1℃,20-28℃','1','Integer',2,4),
	 ('2813255458','88','空调设定温度','ATC1_IusTargTemp_4',753,0,'UNSIGNED8','℃','TMc2','ATC','','',NULL,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=1℃,20-28℃','1','Integer',2,4),
	 ('2813255459','88','空调设定温度','ATC2_IusTargTemp_1',846,0,'UNSIGNED8','℃','TMc1','ATC','','',NULL,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=1℃,20-28℃','1','Integer',2,4),
	 ('2813255460','88','空调设定温度','ATC2_IusTargTemp_2',847,0,'UNSIGNED8','℃','Mp1','ATC','','',NULL,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=1℃,20-28℃','1','Integer',2,4);
INSERT INTO comm_original_signal (id,protocol_id,name_cn,name_en,byte_offset,bit_offset,data_type,unit,"location",subsystem,parse_script,fault_type_key,trigger_value,max_value,min_value,create_by,create_time,modify_by,modify_time,remark,redis_flag,result_type,frames_type,package_order) VALUES
	 ('2813255461','88','空调设定温度','ATC2_IusTargTemp_3',848,0,'UNSIGNED8','℃','Mp2','ATC','','',NULL,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=1℃,20-28℃','1','Integer',2,4),
	 ('2813255462','88','空调设定温度','ATC2_IusTargTemp_4',849,0,'UNSIGNED8','℃','TMc2','ATC','','',NULL,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=1℃,20-28℃','1','Integer',2,4),
	 ('2813255463','88','空调设定温度','ATC3_IusTargTemp_1',942,0,'UNSIGNED8','℃','TMc1','ATC','','',NULL,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=1℃,20-28℃','1','Integer',2,4),
	 ('2813255464','88','空调设定温度','ATC3_IusTargTemp_2',943,0,'UNSIGNED8','℃','Mp1','ATC','','',NULL,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=1℃,20-28℃','1','Integer',2,4),
	 ('2813255465','88','空调设定温度','ATC3_IusTargTemp_3',944,0,'UNSIGNED8','℃','Mp2','ATC','','',NULL,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=1℃,20-28℃','1','Integer',2,4),
	 ('2813255466','88','空调设定温度','ATC3_IusTargTemp_4',945,0,'UNSIGNED8','℃','TMc2','ATC','','',NULL,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=1℃,20-28℃','1','Integer',2,4),
	 ('2813255467','88','空调设定温度','ATC4_IusTargTemp_1',58,0,'UNSIGNED8','℃','TMc1','ATC','','',NULL,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=1℃,20-28℃','1','Integer',2,5),
	 ('2813255468','88','空调设定温度','ATC4_IusTargTemp_2',59,0,'UNSIGNED8','℃','Mp1','ATC','','',NULL,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=1℃,20-28℃','1','Integer',2,5),
	 ('2813255469','88','空调设定温度','ATC4_IusTargTemp_3',60,0,'UNSIGNED8','℃','Mp2','ATC','','',NULL,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=1℃,20-28℃','1','Integer',2,5),
	 ('2813255470','88','空调设定温度','ATC4_IusTargTemp_4',61,0,'UNSIGNED8','℃','TMc2','ATC','','',NULL,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=1℃,20-28℃','1','Integer',2,5);
INSERT INTO comm_original_signal (id,protocol_id,name_cn,name_en,byte_offset,bit_offset,data_type,unit,"location",subsystem,parse_script,fault_type_key,trigger_value,max_value,min_value,create_by,create_time,modify_by,modify_time,remark,redis_flag,result_type,frames_type,package_order) VALUES
	 ('2813255471','88','空调设定温度反馈','ATC_CusTargeTemp_1',886,0,'UNSIGNED8','℃','TMc1','ATC','','',NULL,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=1℃,20-28℃','1','Integer',2,5),
	 ('2813255472','88','空调设定温度反馈','ATC_CusTargeTemp_2',887,0,'UNSIGNED8','℃','Mp1','ATC','','',NULL,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=1℃,20-28℃','1','Integer',2,5),
	 ('2813255473','88','空调设定温度反馈','ATC_CusTargeTemp_3',888,0,'UNSIGNED8','℃','Mp2','ATC','','',NULL,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=1℃,20-28℃','1','Integer',2,5),
	 ('2813255474','88','空调设定温度反馈','ATC_CusTargeTemp_4',889,0,'UNSIGNED8','℃','TMc2','ATC','','',NULL,NULL,NULL,'','2023-12-21 10:08:00','','2023-12-21 10:08:00','1=1℃,20-28℃','1','Integer',2,5);





INSERT INTO ekb_fault_type (id,vehicle_type_id,fault_type_key,name_cn,name_en,fault_code,fault_level,subsystem,del_flag,create_time,modify_by,modify_time,description,"location","enable",fault_mode,frontline_disposal_recommendations,overhaul_suggestions,fault_reason,model_code,line_id,fault_category,vehicle_structure_code,create_by) VALUES
	 ('2751','129','VCU_C100_69745','客室右侧（第三个座椅下电气柜）烟火故障','HMI_CxSHD1_Flt_1','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','TMc1',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2752','129','VCU_C100_69746','客室右侧（第二个座椅下电气柜）烟火故障','HMI_CxSHD2_Flt_1','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','TMc1',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2753','129','VCU_C100_69747','客室右侧（第一个座椅下电气柜）烟火故障','HMI_CxSHD3_Flt_1','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','TMc1',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2754','129','VCU_C100_69748','一位侧一位端侧顶烟火故障                       ','HMI_CxSHD4_Flt_1','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','TMc1',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2755','129','VCU_C100_69749','客室左侧（第一个座椅下电气柜）烟火故障','HMI_CxSHD5_Flt_1','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','TMc1',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2756','129','VCU_C100_69750','客室左侧（第三个座椅下电气柜）烟火故障','HMI_CxSHD6_Flt_1','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','TMc1',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2757','129','VCU_C100_69751','二位侧二位端侧顶烟火故障                     ','HMI_CxSHD7_Flt_1','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','TMc1',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2758','129','VCU_C100_69752','客室二位端左侧电气柜烟火故障','HMI_CxSHD8_Flt_1','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','TMc1',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2759','129','VCU_C100_69753','客室二位端右侧电气柜烟火故障','HMI_CxSHD9_Flt_1','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','TMc1',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2760','129','VCU_C100_69754','二位侧二位端侧顶烟火故障                     ','HMI_CxSHD1_Flt_2','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','Mp1',false,'0','','结束当天运营后回库由维修处理','','','24',1,'','');
INSERT INTO ekb_fault_type (id,vehicle_type_id,fault_type_key,name_cn,name_en,fault_code,fault_level,subsystem,del_flag,create_time,modify_by,modify_time,description,"location","enable",fault_mode,frontline_disposal_recommendations,overhaul_suggestions,fault_reason,model_code,line_id,fault_category,vehicle_structure_code,create_by) VALUES
	 ('2761','129','VCU_C100_69755','一位侧一位端侧顶烟火故障                       ','HMI_CxSHD2_Flt_2','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','Mp1',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2762','129','VCU_C100_69756','客室二位端右侧电气柜烟火故障','HMI_CxSHD3_Flt_2','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','Mp1',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2763','129','VCU_C100_69757','客室二位端左侧电气柜烟火故障','HMI_CxSHD4_Flt_2','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','Mp1',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2764','129','VCU_C100_69758','二位侧二位端侧顶烟火故障                     ','HMI_CxSHD1_Flt_3','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','Mp2',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2765','129','VCU_C100_69759','一位侧一位端侧顶烟火故障                       ','HMI_CxSHD2_Flt_3','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','Mp2',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2766','129','VCU_C100_69760','客室二位端右侧电气柜烟火故障','HMI_CxSHD3_Flt_3','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','Mp2',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2767','129','VCU_C100_69761','客室二位端左侧电气柜烟火故障','HMI_CxSHD4_Flt_3','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','Mp2',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2768','129','VCU_C100_69762','客室右侧（第三个座椅下电气柜）烟火故障','HMI_CxSHD1_Flt_4','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','TMc2',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2769','129','VCU_C100_69763','客室右侧（第二个座椅下电气柜）烟火故障','HMI_CxSHD2_Flt_4','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','TMc2',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2770','129','VCU_C100_69764','客室右侧（第一个座椅下电气柜）烟火故障','HMI_CxSHD3_Flt_4','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','TMc2',false,'0','','结束当天运营后回库由维修处理','','','24',1,'','');
INSERT INTO ekb_fault_type (id,vehicle_type_id,fault_type_key,name_cn,name_en,fault_code,fault_level,subsystem,del_flag,create_time,modify_by,modify_time,description,"location","enable",fault_mode,frontline_disposal_recommendations,overhaul_suggestions,fault_reason,model_code,line_id,fault_category,vehicle_structure_code,create_by) VALUES
	 ('2771','129','VCU_C100_69765','一位侧一位端侧顶烟火故障                       ','HMI_CxSHD4_Flt_4','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','TMc2',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2772','129','VCU_C100_69766','客室左侧（第一个座椅下电气柜）烟火故障','HMI_CxSHD5_Flt_4','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','TMc2',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2773','129','VCU_C100_69767','客室左侧（第三个座椅下电气柜）烟火故障','HMI_CxSHD6_Flt_4','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','TMc2',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2774','129','VCU_C100_69768','二位侧二位端侧顶烟火故障                     ','HMI_CxSHD7_Flt_4','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','TMc2',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2775','129','VCU_C100_69769','客室二位端左侧电气柜烟火故障','HMI_CxSHD8_Flt_4','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','TMc2',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2776','129','VCU_C100_69770','客室二位端右侧电气柜烟火故障','HMI_CxSHD9_Flt_4','C100',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','TMc2',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2777','129','VCU_C101_69771','牵引箱感温电缆1故障','HMI_CxLHD1_Flt_1','C101',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','TMc1',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2778','129','VCU_C101_69772','辅助箱感温电缆2故障','HMI_CxLHD2_Flt_1','C101',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','TMc1',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2779','129','VCU_C101_69773','牵引箱1感温电缆1故障','HMI_CxLHD1_Flt_2','C101',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','Mp1',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2780','129','VCU_C101_69774','牵引箱2感温电缆2故障','HMI_CxLHD2_Flt_2','C101',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','Mp1',false,'0','','结束当天运营后回库由维修处理','','','24',1,'','');
INSERT INTO ekb_fault_type (id,vehicle_type_id,fault_type_key,name_cn,name_en,fault_code,fault_level,subsystem,del_flag,create_time,modify_by,modify_time,description,"location","enable",fault_mode,frontline_disposal_recommendations,overhaul_suggestions,fault_reason,model_code,line_id,fault_category,vehicle_structure_code,create_by) VALUES
	 ('2781','129','VCU_C101_69775','牵引箱1感温电缆1故障','HMI_CxLHD1_Flt_3','C101',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','Mp2',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2782','129','VCU_C101_69776','牵引箱2感温电缆2故障','HMI_CxLHD2_Flt_3','C101',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','Mp2',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2783','129','VCU_C101_69777','牵引箱感温电缆1故障','HMI_CxLHD1_Flt_4','C101',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','TMc2',false,'0','','结束当天运营后回库由维修处理','','','24',1,'',''),
	 ('2784','129','VCU_C101_69778','辅助箱感温电缆2故障','HMI_CxLHD2_Flt_4','C101',1,'VCU',0,'2023-12-21 10:08:00','','2023-12-21 10:08:00','1=故障','TMc2',false,'0','','结束当天运营后回库由维修处理','','','24',1,'','');

