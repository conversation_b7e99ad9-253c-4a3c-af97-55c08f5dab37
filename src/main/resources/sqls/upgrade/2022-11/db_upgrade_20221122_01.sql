-- 软件版本 LCU项配置数据处理
DELETE FROM monitor_table_item WHERE menu_id = '134f18cb101b4a2abfd909663c517f43';
INSERT INTO public.monitor_table_item (id, menu_id, name, type, sort, car_type, signal_function_id, create_time, create_by, modify_time, modify_by, del_flag, pub_and_pri, relation_key, slot_board_id) VALUES ('9c3d035079d74c9aad314b3876657087', '134f18cb101b4a2abfd909663c517f43', 'LCU应用程序版本', 'basic', '7', 'no-type', '', '2022-11-25 15:01:41', null, '2022-11-25 15:01:41', null, false, 0, null, null);
INSERT INTO public.monitor_table_item (id, menu_id, name, type, sort, car_type, signal_function_id, create_time, create_by, modify_time, modify_by, del_flag, pub_and_pri, relation_key, slot_board_id) VALUES ('63ad443dd7ac4c1b818778bf90b59de9', '134f18cb101b4a2abfd909663c517f43', '4车线路巡检系统', 'basic', '4', 'no-type', '', '2022-05-31 11:39:15', null, '2022-05-31 11:39:15', null, false, 0, null, null);
INSERT INTO public.monitor_table_item (id, menu_id, name, type, sort, car_type, signal_function_id, create_time, create_by, modify_time, modify_by, del_flag, pub_and_pri, relation_key, slot_board_id) VALUES ('aec662bddae047e59fcc69544b02aaf8', '134f18cb101b4a2abfd909663c517f43', 'LCU通信版本', 'basic', '6', 'no-type', '', '2022-05-31 14:15:34', null, '2022-11-25 15:01:13', null, false, 0, null, null);
INSERT INTO public.monitor_table_item (id, menu_id, name, type, sort, car_type, signal_function_id, create_time, create_by, modify_time, modify_by, del_flag, pub_and_pri, relation_key, slot_board_id) VALUES ('4a987b17f5ce40b9ab11f4a8f265bf6d', '134f18cb101b4a2abfd909663c517f43', 'RIOMx1软件版本', 'basic', '14', 'no-type', '', '2022-05-31 14:24:22', null, '2022-11-25 15:02:13', null, false, 0, null, null);
INSERT INTO public.monitor_table_item (id, menu_id, name, type, sort, car_type, signal_function_id, create_time, create_by, modify_time, modify_by, del_flag, pub_and_pri, relation_key, slot_board_id) VALUES ('a01da9ead2414bb492692b8a5744678b', '134f18cb101b4a2abfd909663c517f43', 'RIOM10软件版本', 'basic', '13', 'no-type', '', '2022-05-31 14:23:15', null, '2022-11-25 15:02:16', null, false, 0, null, null);
INSERT INTO public.monitor_table_item (id, menu_id, name, type, sort, car_type, signal_function_id, create_time, create_by, modify_time, modify_by, del_flag, pub_and_pri, relation_key, slot_board_id) VALUES ('39bddcb44d3c466c9a103795c1f5eaef', '134f18cb101b4a2abfd909663c517f43', 'HMI软件版本', 'basic', '12', 'no-type', '', '2022-05-31 14:22:22', null, '2022-11-25 15:02:19', null, false, 0, null, null);
INSERT INTO public.monitor_table_item (id, menu_id, name, type, sort, car_type, signal_function_id, create_time, create_by, modify_time, modify_by, del_flag, pub_and_pri, relation_key, slot_board_id) VALUES ('434cdc3b0bf64c96a6f1085d50959c22', '134f18cb101b4a2abfd909663c517f43', 'VCU软件版本', 'basic', '11', 'no-type', '', '2022-05-31 14:21:36', null, '2022-11-25 15:02:22', null, false, 0, null, null);
INSERT INTO public.monitor_table_item (id, menu_id, name, type, sort, car_type, signal_function_id, create_time, create_by, modify_time, modify_by, del_flag, pub_and_pri, relation_key, slot_board_id) VALUES ('ecea14bd8d814d21a70f63bd96a99d2a', '134f18cb101b4a2abfd909663c517f43', '能耗记录仪软件版本', 'basic', '10', 'no-type', '', '2022-05-31 14:20:49', null, '2022-11-25 15:02:25', null, false, 0, null, null);
INSERT INTO public.monitor_table_item (id, menu_id, name, type, sort, car_type, signal_function_id, create_time, create_by, modify_time, modify_by, del_flag, pub_and_pri, relation_key, slot_board_id) VALUES ('0e11751c96ef4814b40ab9fc66f0c73c', '134f18cb101b4a2abfd909663c517f43', '被动障碍物版本号', 'basic', '9', 'no-type', '', '2022-05-31 14:19:45', null, '2022-11-25 15:02:29', null, false, 0, null, null);
INSERT INTO public.monitor_table_item (id, menu_id, name, type, sort, car_type, signal_function_id, create_time, create_by, modify_time, modify_by, del_flag, pub_and_pri, relation_key, slot_board_id) VALUES ('50e6f34b7a654f49bd1dd7eee6ca092d', '134f18cb101b4a2abfd909663c517f43', '主动障碍物版本号', 'basic', '8', 'no-type', '', '2022-05-31 14:16:19', null, '2022-11-25 15:02:39', null, false, 0, null, null);
INSERT INTO public.monitor_table_item (id, menu_id, name, type, sort, car_type, signal_function_id, create_time, create_by, modify_time, modify_by, del_flag, pub_and_pri, relation_key, slot_board_id) VALUES ('69a793d4bb5c472db59df2aab6f6c7a6', '134f18cb101b4a2abfd909663c517f43', 'RFID主版本号', 'basic', '1', 'no-type', '', '2022-05-30 15:05:31', null, '2022-05-30 15:05:31', null, false, 0, null, null);
INSERT INTO public.monitor_table_item (id, menu_id, name, type, sort, car_type, signal_function_id, create_time, create_by, modify_time, modify_by, del_flag, pub_and_pri, relation_key, slot_board_id) VALUES ('c2e4fc827a934761a0f49da9056c5afe', '134f18cb101b4a2abfd909663c517f43', 'BDS系统软件版本', 'basic', '2', 'no-type', '', '2022-05-30 15:09:43', null, '2022-05-30 15:09:43', null, false, 0, null, null);
INSERT INTO public.monitor_table_item (id, menu_id, name, type, sort, car_type, signal_function_id, create_time, create_by, modify_time, modify_by, del_flag, pub_and_pri, relation_key, slot_board_id) VALUES ('c595dc083cf346f4a0cf3527e1eeadee', '134f18cb101b4a2abfd909663c517f43', '1车轨道几何系统', 'basic', '3', 'no-type', '', '2022-05-31 11:19:53', null, '2022-05-31 11:39:19', null, false, 0, null, null);
INSERT INTO public.monitor_table_item (id, menu_id, name, type, sort, car_type, signal_function_id, create_time, create_by, modify_time, modify_by, del_flag, pub_and_pri, relation_key, slot_board_id) VALUES ('6e48e68a2c0249e49a1c65bda03a0b8d', '134f18cb101b4a2abfd909663c517f43', 'FCU软件版本', 'basic', '5', 'no-type', '', '2022-05-31 14:14:48', null, '2022-05-31 14:14:48', null, false, 0, null, null);

DELETE FROM monitor_table_format WHERE item_id IN ('aec662bddae047e59fcc69544b02aaf8','9c3d035079d74c9aad314b3876657087') ;
INSERT INTO public.monitor_table_format (id, item_id, n_row, n_column, vehicle_location, create_time, create_by, modify_time, modify_by, del_flag, sort) VALUES ('e6a88c8e19174f6d8270c72101bdb78c', 'aec662bddae047e59fcc69544b02aaf8', 1, 1, 'C1', '2022-05-31 14:15:34', null, '2022-05-31 14:15:34', null, false, '0');
INSERT INTO public.monitor_table_format (id, item_id, n_row, n_column, vehicle_location, create_time, create_by, modify_time, modify_by, del_flag, sort) VALUES ('163f1fcde54d4417a35b863b7d0ecea6', 'aec662bddae047e59fcc69544b02aaf8', 1, 1, 'C2', '2022-05-31 14:15:34', null, '2022-05-31 14:15:34', null, false, '1');
INSERT INTO public.monitor_table_format (id, item_id, n_row, n_column, vehicle_location, create_time, create_by, modify_time, modify_by, del_flag, sort) VALUES ('46205614a8374eddaee02841efc5363d', 'aec662bddae047e59fcc69544b02aaf8', 1, 1, 'C3', '2022-05-31 14:15:34', null, '2022-05-31 14:15:34', null, false, '2');
INSERT INTO public.monitor_table_format (id, item_id, n_row, n_column, vehicle_location, create_time, create_by, modify_time, modify_by, del_flag, sort) VALUES ('dfd053ccdc644e61a17c5af101951160', 'aec662bddae047e59fcc69544b02aaf8', 1, 1, 'C4', '2022-05-31 14:15:34', null, '2022-05-31 14:15:34', null, false, '3');
INSERT INTO public.monitor_table_format (id, item_id, n_row, n_column, vehicle_location, create_time, create_by, modify_time, modify_by, del_flag, sort) VALUES ('e5d1d1800c624b56b6ade4e986d3578e', '9c3d035079d74c9aad314b3876657087', 1, 1, 'C1', '2022-11-25 15:01:41', null, '2022-11-25 15:01:41', null, false, '0');
INSERT INTO public.monitor_table_format (id, item_id, n_row, n_column, vehicle_location, create_time, create_by, modify_time, modify_by, del_flag, sort) VALUES ('67524439e74a488087d4dda0d5a614ef', '9c3d035079d74c9aad314b3876657087', 1, 1, 'C2', '2022-11-25 15:01:41', null, '2022-11-25 15:01:41', null, false, '1');
INSERT INTO public.monitor_table_format (id, item_id, n_row, n_column, vehicle_location, create_time, create_by, modify_time, modify_by, del_flag, sort) VALUES ('afe3d17277564f798983c2267bb32b18', '9c3d035079d74c9aad314b3876657087', 1, 1, 'C3', '2022-11-25 15:01:41', null, '2022-11-25 15:01:41', null, false, '2');
INSERT INTO public.monitor_table_format (id, item_id, n_row, n_column, vehicle_location, create_time, create_by, modify_time, modify_by, del_flag, sort) VALUES ('176d7dcdf5404a43acf110cd5d6f503f', '9c3d035079d74c9aad314b3876657087', 1, 1, 'C4', '2022-11-25 15:01:41', null, '2022-11-25 15:01:41', null, false, '3');

DELETE FROM monitor_slot WHERE table_format_id IN(
      'e6a88c8e19174f6d8270c72101bdb78c',
      'f20c8959de1244e595662c568726faf5',
      '46205614a8374eddaee02841efc5363d',
     'dfd053ccdc644e61a17c5af101951160',
      '67524439e74a488087d4dda0d5a614ef',
      'afe3d17277564f798983c2267bb32b18',
      'e5d1d1800c624b56b6ade4e986d3578e',
      '176d7dcdf5404a43acf110cd5d6f503f',
      '163f1fcde54d4417a35b863b7d0ecea6',
      '46205614a8374eddaee02841efc5363d',
      'e6a88c8e19174f6d8270c72101bdb78c',
      'dfd053ccdc644e61a17c5af101951160');
INSERT INTO public.monitor_slot (id, table_format_id, sort, location, slot_type, create_time, create_by, modify_time, modify_by, del_flag) VALUES ('93a470b421d74470999a5399c8c63847', '67524439e74a488087d4dda0d5a614ef', 0, 'C2', 0, '2022-11-25 15:01:41', null, '2022-11-25 15:01:41', null, false);
INSERT INTO public.monitor_slot (id, table_format_id, sort, location, slot_type, create_time, create_by, modify_time, modify_by, del_flag) VALUES ('be5466d1be0442fcb5ff4014d778976b', 'afe3d17277564f798983c2267bb32b18', 0, 'C3', 0, '2022-11-25 15:01:41', null, '2022-11-25 15:01:41', null, false);
INSERT INTO public.monitor_slot (id, table_format_id, sort, location, slot_type, create_time, create_by, modify_time, modify_by, del_flag) VALUES ('a288b112f1924fbb87b9cddd99d6c87b', 'e5d1d1800c624b56b6ade4e986d3578e', 0, 'C1', 1, '2022-11-25 15:01:41', null, '2022-11-28 10:45:49', null, false);
INSERT INTO public.monitor_slot (id, table_format_id, sort, location, slot_type, create_time, create_by, modify_time, modify_by, del_flag) VALUES ('8a268ff10bf2442986322f5e72d74299', '176d7dcdf5404a43acf110cd5d6f503f', 0, 'C4', 1, '2022-11-25 15:01:41', null, '2022-11-28 10:45:54', null, false);
INSERT INTO public.monitor_slot (id, table_format_id, sort, location, slot_type, create_time, create_by, modify_time, modify_by, del_flag) VALUES ('25d508fdc0ee4baeb2d86c936511964d', '163f1fcde54d4417a35b863b7d0ecea6', 0, 'C2', 0, '2022-05-31 14:15:34', null, '2022-05-31 14:15:34', null, false);
INSERT INTO public.monitor_slot (id, table_format_id, sort, location, slot_type, create_time, create_by, modify_time, modify_by, del_flag) VALUES ('400a65fdc9584c3aac7e24f07c960a73', '46205614a8374eddaee02841efc5363d', 0, 'C3', 0, '2022-05-31 14:15:34', null, '2022-05-31 14:15:34', null, false);
INSERT INTO public.monitor_slot (id, table_format_id, sort, location, slot_type, create_time, create_by, modify_time, modify_by, del_flag) VALUES ('7adec27b704245159c511b532f5f8917', 'e6a88c8e19174f6d8270c72101bdb78c', 0, 'C1', 1, '2022-05-31 14:15:34', null, '2022-05-31 14:15:53', null, false);
INSERT INTO public.monitor_slot (id, table_format_id, sort, location, slot_type, create_time, create_by, modify_time, modify_by, del_flag) VALUES ('9f296d02e306467f90834406f2beea4d', 'dfd053ccdc644e61a17c5af101951160', 0, 'C4', 1, '2022-05-31 14:15:34', null, '2022-05-31 14:15:56', null, false);


DELETE FROM monitor_trigger WHERE slot_id IN(
    'cb306a360a734bbdb8ad8131c11494fe',
    'f20c8959de1244e595662c568726faf5',
    '7adec27b704245159c511b532f5f8917',
    'a288b112f1924fbb87b9cddd99d6c87b',
    '8a268ff10bf2442986322f5e72d74299',
    '9f296d02e306467f90834406f2beea4d');
INSERT INTO public.monitor_trigger (id, slot_id, label, sort, signal_id, trigger_value, data_display_point, unit_status, svg_url, ext_properties, create_time, create_by, modify_time, modify_by, del_flag, image_type, image_path, signal_name_en, mark) VALUES ('cb306a360a734bbdb8ad8131c11494fe', '7adec27b704245159c511b532f5f8917', '通信', 1, '2029226160', null, '1', null, '', null, '2022-05-31 14:15:53', null, '2022-11-28 10:46:03', null, false, null, null, 'HMI_uiSoftVer_com_1', '1');
INSERT INTO public.monitor_trigger (id, slot_id, label, sort, signal_id, trigger_value, data_display_point, unit_status, svg_url, ext_properties, create_time, create_by, modify_time, modify_by, del_flag, image_type, image_path, signal_name_en, mark) VALUES ('7f9a5c9aef28491abcc17d7d06833bc9', 'a288b112f1924fbb87b9cddd99d6c87b', '应用程序', 1, '2029226170', null, '1', null, '', null, '2022-11-28 10:45:49', null, '2022-11-28 10:45:49', null, false, null, null, 'HMI_uiSoftVer_app_1', null);
INSERT INTO public.monitor_trigger (id, slot_id, label, sort, signal_id, trigger_value, data_display_point, unit_status, svg_url, ext_properties, create_time, create_by, modify_time, modify_by, del_flag, image_type, image_path, signal_name_en, mark) VALUES ('2b6e53a49faa4fe1a4a1cddbf3ff83c9', '8a268ff10bf2442986322f5e72d74299', '应用程序', 1, '2029226190', null, '1', null, '', null, '2022-11-28 10:45:54', null, '2022-11-28 10:46:21', null, false, null, null, 'HMI_uiSoftVer_app_4', null);
INSERT INTO public.monitor_trigger (id, slot_id, label, sort, signal_id, trigger_value, data_display_point, unit_status, svg_url, ext_properties, create_time, create_by, modify_time, modify_by, del_flag, image_type, image_path, signal_name_en, mark) VALUES ('f20c8959de1244e595662c568726faf5', '9f296d02e306467f90834406f2beea4d', '通信', 1, '2029226180', null, '1', null, '', null, '2022-05-31 14:15:56', null, '2022-11-28 10:46:11', null, false, null, null, 'HMI_uiSoftVer_com_4', '1');

-- 子系统 障碍物检测系统 删除主动障碍物设备探测到障碍物 重复配置
DELETE FROM monitor_slot WHERE id IN('1ca953cd9c9e4120a780aa6e35a2973d','446180b06d5c4ae1b0b025dddd0f7763','0ce414543ca247e1b52f71a931558ff5','10429bf90a3d4ff7bbe60be1aba15485');
DELETE FROM monitor_table_item WHERE id = '6997712b18034a83a48319f3424192d4';
DELETE FROM monitor_table_format WHERE item_id = '6997712b18034a83a48319f3424192d4';
DELETE FROM monitor_trigger WHERE slot_id IN('1ca953cd9c9e4120a780aa6e35a2973d','446180b06d5c4ae1b0b025dddd0f7763','0ce414543ca247e1b52f71a931558ff5','10429bf90a3d4ff7bbe60be1aba15485') ;

-- 健康评分 权重配置 子系统数据处理
DELETE FROM eva_health_rule WHERE category = 'b';
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('40', '129', '车门系统', '0.05', null, 'b', '0', '2020-03-03 09:39:48.954401', '124', '2022-11-23 15:23:09', '1', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('92', '129', '弓网系统', '0.1', null, 'b', '128', '2020-08-18 10:34:29.422198', '128', '2022-11-23 15:23:09', '10', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('91', '129', 'RIOM系统', '0.1', null, 'b', '128', '2020-08-18 10:34:21.080818', '162', '2022-11-23 15:23:09', '11', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('90', '129', '轨道几何&线路巡检系统', '0.1', null, 'b', '128', '2020-08-18 10:34:12.645169', '128', '2022-11-23 15:23:09', '12', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('89', '129', '牵引系统', '0.1', null, 'b', '128', '2020-08-18 10:33:50.167109', '162', '2022-11-23 15:23:09', '13', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('88', '129', '走行部系统', '0.1', null, 'b', '128', '2020-08-18 10:33:40.973272', '128', '2022-11-23 15:23:09', '14', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('61', '129', '能耗记录仪系统', '0.05', null, 'b', '124', '2020-03-31 11:17:06.853020', '152', '2022-11-23 15:23:09', '2', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('48', '129', 'LCU系统', '0.05', null, 'b', '124', '2020-03-31 11:12:01.934191', '124', '2022-11-23 15:23:09', '3', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('31', '129', '乘客信息系统', '0.05', null, 'b', '128', '2020-08-18 10:35:40.027707', '162', '2022-11-23 15:23:09', '4', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('95', '129', '蓄电池检测系统', '0.1', null, 'b', '128', '2020-08-18 10:35:40.027707', '162', '2022-11-23 15:23:09', '5', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('32', '129', '驾驶系统', '0.05', null, 'b', '128', '2020-08-18 10:35:40.027707', '162', '2022-11-23 15:23:09', '6', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('34', '129', '障碍物检测系统', '0.05', null, 'b', '128', '2020-08-18 10:35:40.027707', '162', '2022-11-23 15:23:09', '7', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('33', '129', '制动系统', '0.05', null, 'b', '128', '2020-08-18 10:35:40.027707', '162', '2022-11-23 15:23:09', '8', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('94', '129', 'ATC系统', '0.05', null, 'b', '128', '2020-08-18 10:35:32.232714', '128', '2022-11-23 15:23:09', '9', '24');

-- 信号配置 车门故障关 图标修改
UPDATE monitor_trigger SET svg_url = '1_door_faultClose',modify_time = '2022-11-23 15:55:00' WHERE label = '故障关' and signal_name_en like 'DOR_usDor%';

