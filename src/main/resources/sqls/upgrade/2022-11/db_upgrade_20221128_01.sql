-- 更新mtc_signal_params_relation表中的信号id
UPDATE mtc_signal_params_relation mspr SET signal_id = cos.id FROM comm_original_signal cos WHERE cos.name_en = mspr.signal_name;

-- 更新软件版本RFID信号配置
DELETE FROM monitor_trigger WHERE slot_id in ('d55fe29f0922447e9e3db8ebcf0c22c6','5172611ceab848c08856422b0db109d7');
INSERT INTO public.monitor_trigger (id, slot_id, label, sort, signal_id, trigger_value, data_display_point, unit_status, svg_url, ext_properties, create_time, create_by, modify_time, modify_by, del_flag, image_type, image_path, signal_name_en, mark) VALUES ('daf019c21c6f48a094a9aa14fa1d1e3e', 'd55fe29f0922447e9e3db8ebcf0c22c6', 'RFID', 1, '108124140', null, '1', null, '', null, '2022-05-30 15:06:20', null, '2022-11-28 11:40:07', null, false, null, null, 'HMI_CusMajorVersionNum_4', '1');
INSERT INTO public.monitor_trigger (id, slot_id, label, sort, signal_id, trigger_value, data_display_point, unit_status, svg_url, ext_properties, create_time, create_by, modify_time, modify_by, del_flag, image_type, image_path, signal_name_en, mark) VALUES ('a5e2f1e282ed4df9aa23518901084617', '5172611ceab848c08856422b0db109d7', 'RFID', 1, '108124120', null, '1', null, '', null, '2022-05-30 15:06:15', null, '2022-11-28 11:40:01', null, false, null, null, 'HMI_CusMajorVersionNum_1', '1');

-- 更新信号表 comm_original_signal xxxx.xxxx.xxxxxxxx解析脚本
UPDATE comm_original_signal SET parse_script = 'result=String.format("%d.%d.%d",(value>>12) & 0x000F,(value>>8) & 0x000F,value & 0x00FF);'
WHERE parse_script = 'result=String.format("%d.%d.%d",(value>>4) & 0x000F,(value>>8) & 0x000F,value & 0x00FF);';

-- 删除软件版本 唯一键
alter table mtr_software
    drop constraint mtr_software_name_version_key;

-- 重新导入 mtr_software_mapping 数据

DELETE FROM mtr_software_mapping where 1 = 1;
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10001', 'EDCU1_IuiDor1SWVersion', '1车1号门软件版本', 'EDCU1_IuiDor1SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10002', 'EDCU1_IuiDor2SWVersion', '1车2号门软件版本', 'EDCU1_IuiDor2SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10003', 'EDCU1_IuiDor3SWVersion', '1车3号门软件版本', 'EDCU1_IuiDor3SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10004', 'EDCU1_IuiDor4SWVersion', '1车4号门软件版本', 'EDCU1_IuiDor4SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10005', 'EDCU1_IuiDor5SWVersion', '1车5号门软件版本', 'EDCU1_IuiDor5SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10006', 'EDCU1_IuiDor6SWVersion', '1车6号门软件版本', 'EDCU1_IuiDor6SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10007', 'EDCU1_IuiDor7SWVersion', '1车7号门软件版本', 'EDCU1_IuiDor7SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10008', 'EDCU1_IuiDor8SWVersion', '1车8号门软件版本', 'EDCU1_IuiDor8SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10083', 'AODS1_IuiAODSSW', '1车AODS版本', 'AODS1_IuiAODSSW', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'ODS', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10155', 'HMI_CuiBDSSWVersion_1', '1车BDS系统软件版本', 'HMI_CuiBDSSWVersion_1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10075', 'BMS1_Iusedtion', '1车BMS1软件版本', 'BMS1_Iusedtion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'BMS', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10159', 'HMI_CusSWRev_1', '1车FCU软件版本', 'HMI_CusSWRev_1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10078', 'TLDS1_IuiTGPDSWVer', '1车GPDS软件版本', 'TLDS1_IuiTGPDSWVer', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'TLD', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10143', 'TMS_uiHMIVersion_1', '1车HMI软件版本', 'TMS_uiHMIVersion_1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10162', 'HMI_uiSoftVer_app_1', '1车LCU应用程序版本', 'HMI_uiSoftVer_app_1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10161', 'HMI_uiSoftVer_com_1', '1车LCU通信版本', 'HMI_uiSoftVer_com_1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10151', 'PIS_uiPICSWVersion_1', '1车PIC软件版本', 'PIS_uiPICSWVersion_1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10085', 'PODS1_IuiPODSSW', '1车PODS版本', 'PODS1_IuiPODSSW', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'ODS', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10149', 'HMI_CusMajorVersionNum_1', '1车RFID主版本', 'HMI_CusMajorVersionNum_1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10087', 'RIOM101_IuiSWRer', '1车RIOM101软件版本', 'RIOM101_IuiSWRer', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'RIOM', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10145', 'TMS_uiRIOM10SWVersion_1', '1车RIOM10软件版本', 'TMS_uiRIOM10SWVersion_1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10090', 'RIOM111_IuiSWRer', '1车RIOM111软件版本', 'RIOM111_IuiSWRer', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'RIOM', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10147', 'TMS_uiRIOM11SWVersion_1', '1车RIOM11软件版本', 'TMS_uiRIOM11SWVersion_1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10141', 'TMS_uiVCUSWVersion_1', '1车VCU软件版本', 'TMS_uiVCUSWVersion_1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10125', 'ODS_uiAODSSW_1', '1车主动障碍物版本', 'ODS_uiAODSSW_1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10091', 'RFID1_IusMajorVersionNum', '1车主版本', 'RFID1_IusMajorVersionNum', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'RIOM', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10101', 'DIS_IuiBCGDSPVer_1', '1车充电机DSP软件版本', 'DIS_IuiBCGDSPVer_1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10047', 'SIV1_IuiBCGDSPVer', '1车充电机DSP软件版本', 'SIV1_IuiBCGDSPVer', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'SIV', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10102', 'DIS_IuiBCGLGCVer_1', '1车充电机LGC软件版本', 'DIS_IuiBCGLGCVer_1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10048', 'SIV1_IuiBCGLGCVer', '1车充电机LGC软件版本', 'SIV1_IuiBCGLGCVer', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'SIV', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10089', 'FAU1_IusHWRev', '1车本机硬件版本', 'FAU1_IusHWRev', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'RIOM', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10074', 'BMS1_IuiBatVer', '1车版本', 'BMS1_IuiBatVer', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'BMS', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10153', 'HVAC_uiHVACSWVersion_1', '1车空调软件版本', 'HVAC_uiHVACSWVersion_1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10057', 'DCU12_IuiAdhSWRevision', '1车粘着软件版本', 'DCU12_IuiAdhSWRevision', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10135', 'HMI_CuiSWVersion_C1', '1车网关BCU软件版本', 'HMI_CuiSWVersion_C1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10127', 'ODS_uiPODSSW_1', '1车被动障碍物版本', 'ODS_uiPODSSW_1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10157', 'HMI_CuiTLDSSWVer_1', '1车轨道几何系统软件版本', 'HMI_CuiTLDSSWVer_1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10109', 'DCU12_IuiAdhSWRevision_1', '1车转向架2牵引粘着版本', 'DCU12_IuiAdhSWRevision_1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10108', 'DCU12_IuiInvSWRevision_1', '1车转向架2牵引逆变版本', 'DCU12_IuiInvSWRevision_1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10107', 'DCU12_IuiLglSWRevision_1', '1车转向架2牵引逻辑版本', 'DCU12_IuiLglSWRevision_1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10099', 'DIS_IuiINVDSPVer_1', '1车逆变器DSP软件版本', 'DIS_IuiINVDSPVer_1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10100', 'DIS_IuiINVLGCVer_1', '1车逆变器LGC软件版本', 'DIS_IuiINVLGCVer_1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10056', 'DCU12_IuiInvSWRevision', '1车逆变软件版本', 'DCU12_IuiInvSWRevision', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10055', 'DCU12_IuiLglSWRevision', '1车逻辑软件版本', 'DCU12_IuiLglSWRevision', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10129', 'EDCU_uiDorSWVersion_1', '1车门系统软件版本', 'EDCU_uiDorSWVersion_1', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10009', 'EDCU2_IuiDor1SWVersion', '2车1号门软件版本', 'EDCU2_IuiDor1SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10010', 'EDCU2_IuiDor2SWVersion', '2车2号门软件版本', 'EDCU2_IuiDor2SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10011', 'EDCU2_IuiDor3SWVersion', '2车3号门软件版本', 'EDCU2_IuiDor3SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10012', 'EDCU2_IuiDor4SWVersion', '2车4号门软件版本', 'EDCU2_IuiDor4SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10013', 'EDCU2_IuiDor5SWVersion', '2车5号门软件版本', 'EDCU2_IuiDor5SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10014', 'EDCU2_IuiDor6SWVersion', '2车6号门软件版本', 'EDCU2_IuiDor6SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10015', 'EDCU2_IuiDor7SWVersion', '2车7号门软件版本', 'EDCU2_IuiDor7SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10016', 'EDCU2_IuiDor8SWVersion', '2车8号门软件版本', 'EDCU2_IuiDor8SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10060', 'DCU21_IuiAdhSWRevision', '2车DCU21粘着软件版本', 'DCU21_IuiAdhSWRevision', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10059', 'DCU21_IuiInvSWRevision', '2车DCU21逆变软件版本', 'DCU21_IuiInvSWRevision', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10058', 'DCU21_IuiLglSWRevision', '2车DCU21逻辑软件版本', 'DCU21_IuiLglSWRevision', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10063', 'DCU22_IuiAdhSWRevision', '2车DCU22粘着软件版本', 'DCU22_IuiAdhSWRevision', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10062', 'DCU22_IuiInvSWRevision', '2车DCU22逆变软件版本', 'DCU22_IuiInvSWRevision', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10061', 'DCU22_IuiLglSWRevision', '2车DCU22逻辑软件版本', 'DCU22_IuiLglSWRevision', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10139', 'TMS_uiRIOM21SWVersion_2', '2车RIOM21软件版本', 'TMS_uiRIOM21SWVersion_2', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10154', 'HVAC_uiHVACSWVersion_2', '2车空调软件版本', 'HVAC_uiHVACSWVersion_2', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10136', 'HMI_CuiSWVersion_C2', '2车网关BCU软件版本', 'HMI_CuiSWVersion_C2', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10133', 'ECR_usSoftVersion_2', '2车能耗记录仪软件版本', 'ECR_usSoftVersion_2', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10112', 'DCU21_IuiAdhSWRevision_2', '2车转向架1牵引粘着版本', 'DCU21_IuiAdhSWRevision_2', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10111', 'DCU21_IuiInvSWRevision_2', '2车转向架1牵引逆变版本', 'DCU21_IuiInvSWRevision_2', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10110', 'DCU21_IuiLglSWRevision_2', '2车转向架1牵引逻辑版本', 'DCU21_IuiLglSWRevision_2', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10115', 'DCU22_IuiAdhSWRevision_2', '2车转向架2牵引粘着版本', 'DCU22_IuiAdhSWRevision_2', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10114', 'DCU22_IuiInvSWRevision_2', '2车转向架2牵引逆变版本', 'DCU22_IuiInvSWRevision_2', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10113', 'DCU22_IuiLglSWRevision_2', '2车转向架2牵引逻辑版本', 'DCU22_IuiLglSWRevision_2', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10081', 'EGM2_Ius_SoftVersion', '2车软件版本', 'EGM2_Ius_SoftVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'ECR', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10130', 'EDCU_uiDorSWVersion_2', '2车门系统软件版本', 'EDCU_uiDorSWVersion_2', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10017', 'EDCU3_IuiDor1SWVersion', '3车1号门软件版本', 'EDCU3_IuiDor1SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10018', 'EDCU3_IuiDor2SWVersion', '3车2号门软件版本', 'EDCU3_IuiDor2SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10019', 'EDCU3_IuiDor3SWVersion', '3车3号门软件版本', 'EDCU3_IuiDor3SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10020', 'EDCU3_IuiDor4SWVersion', '3车4号门软件版本', 'EDCU3_IuiDor4SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10021', 'EDCU3_IuiDor5SWVersion', '3车5号门软件版本', 'EDCU3_IuiDor5SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10022', 'EDCU3_IuiDor6SWVersion', '3车6号门软件版本', 'EDCU3_IuiDor6SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10023', 'EDCU3_IuiDor7SWVersion', '3车7号门软件版本', 'EDCU3_IuiDor7SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10024', 'EDCU3_IuiDor8SWVersion', '3车8号门软件版本', 'EDCU3_IuiDor8SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10066', 'DCU31_IuiAdhSWRevision', '3车DCU31粘着版本', 'DCU31_IuiAdhSWRevision', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10065', 'DCU31_IuiInvSWRevision', '3车DCU31逆变版本', 'DCU31_IuiInvSWRevision', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10064', 'DCU31_IuiLglSWRevision', '3车DCU31逻辑版本', 'DCU31_IuiLglSWRevision', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10069', 'DCU32_IuiAdhSWRevision', '3车DCU32粘着版本', 'DCU32_IuiAdhSWRevision', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10068', 'DCU32_IuiInvSWRevision', '3车DCU32逆变版本', 'DCU32_IuiInvSWRevision', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10067', 'DCU32_IuiLglSWRevision', '3车DCU32逻辑版本', 'DCU32_IuiLglSWRevision', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10140', 'TMS_uiRIOM21SWVersion_3', '3车RIOM21软件版本', 'TMS_uiRIOM21SWVersion_3', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10165', 'HVAC_uiHVACSWVersion_3', '3车空调软件版本', 'HVAC_uiHVACSWVersion_3', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10137', 'HMI_CuiSWVersion_C3', '3车网关BCU软件版本', 'HMI_CuiSWVersion_C3', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10134', 'ECR_usSoftVersion_3', '3车能耗记录仪软件版本', 'ECR_usSoftVersion_3', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10118', 'DCU31_IuiAdhSWRevision_3', '3车转向架1牵引粘着版本', 'DCU31_IuiAdhSWRevision_3', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10117', 'DCU31_IuiInvSWRevision_3', '3车转向架1牵引逆变版本', 'DCU31_IuiInvSWRevision_3', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10116', 'DCU31_IuiLglSWRevision_3', '3车转向架1牵引逻辑版本', 'DCU31_IuiLglSWRevision_3', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10121', 'DCU32_IuiAdhSWRevision_3', '3车转向架2牵引粘着版本', 'DCU32_IuiAdhSWRevision_3', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10120', 'DCU32_IuiInvSWRevision_3', '3车转向架2牵引逆变版本', 'DCU32_IuiInvSWRevision_3', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10119', 'DCU32_IuiLglSWRevision_3', '3车转向架2牵引逻辑版本', 'DCU32_IuiLglSWRevision_3', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10082', 'EGM3_Ius_SoftVersion', '3车软件版本', 'EGM3_Ius_SoftVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'ECR', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10131', 'EDCU_uiDorSWVersion_3', '3车门系统软件版本', 'EDCU_uiDorSWVersion_3', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10025', 'EDCU4_IuiDor1SWVersion', '4车1号门软件版本', 'EDCU4_IuiDor1SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10026', 'EDCU4_IuiDor2SWVersion', '4车2号门软件版本', 'EDCU4_IuiDor2SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10027', 'EDCU4_IuiDor3SWVersion', '4车3号门软件版本', 'EDCU4_IuiDor3SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10028', 'EDCU4_IuiDor4SWVersion', '4车4号门软件版本', 'EDCU4_IuiDor4SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10029', 'EDCU4_IuiDor5SWVersion', '4车5号门软件版本', 'EDCU4_IuiDor5SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10030', 'EDCU4_IuiDor6SWVersion', '4车6号门软件版本', 'EDCU4_IuiDor6SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10031', 'EDCU4_IuiDor7SWVersion', '4车7号门软件版本', 'EDCU4_IuiDor7SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10032', 'EDCU4_IuiDor8SWVersion', '4车8号门软件版本', 'EDCU4_IuiDor8SWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'EDCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10084', 'AODS4_IuiAODSSW', '4车AODS版本号', 'AODS4_IuiAODSSW', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'ODS', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10156', 'HMI_CuiBDSSWVersion_4', '4车BDS系统软件版本', 'HMI_CuiBDSSWVersion_4', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10077', 'BMS4_Iusedtion', '4车BMS4软件版本', 'BMS4_Iusedtion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'BMS', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10160', 'HMI_CusSWRev_4', '4车FCU软件版本', 'HMI_CusSWRev_4', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10144', 'TMS_uiHMIVersion_4', '4车HMI软件版本', 'TMS_uiHMIVersion_4', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10164', 'HMI_uiSoftVer_app_4', '4车LCU应用程序版本', 'HMI_uiSoftVer_app_4', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10163', 'HMI_uiSoftVer_com_4', '4车LCU通信版本', 'HMI_uiSoftVer_com_4', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10034', 'PIS4_uiPICSWVersion', '4车PIS软件版本', 'PIS4_uiPICSWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'PIS', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10086', 'PODS4_IuiPODSSW', '4车PODS版本号', 'PODS4_IuiPODSSW', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'ODS', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10150', 'HMI_CusMajorVersionNum_4', '4车RFID主版本', 'HMI_CusMajorVersionNum_4', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10094', 'RIOM104_IuiSWRer', '4车RIOM104软件版本', 'RIOM104_IuiSWRer', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'RIOM', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10146', 'TMS_uiRIOM10SWVersion_4', '4车RIOM10软件版本', 'TMS_uiRIOM10SWVersion_4', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10092', 'RIOM114_IuiSWRer', '4车RIOM114软件版本', 'RIOM114_IuiSWRer', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'RIOM', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10148', 'TMS_uiRIOM11SWVersion_4', '4车RIOM11软件版本', 'TMS_uiRIOM11SWVersion_4', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10079', 'TLD4_IuiTLDSWVer', '4车TLDS软件版本', 'TLD4_IuiTLDSWVer', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'TLD', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10142', 'TMS_uiVCUSWVersion_4', '4车VCU软件版本', 'TMS_uiVCUSWVersion_4', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10126', 'ODS_uiAODSSW_4', '4车主动障碍物版本', 'ODS_uiAODSSW_4', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10105', 'DIS_IuiBCGDSPVer_4', '4车充电机DSP版本', 'DIS_IuiBCGDSPVer_4', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10106', 'DIS_IuiBCGLGCVer_4', '4车充电机LGC版本', 'DIS_IuiBCGLGCVer_4', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10096', 'FAU4_IusHWRev', '4车本机硬件版本', 'FAU4_IusHWRev', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'RIOM', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10076', 'BMS4_IuiBatVer', '4车版本号', 'BMS4_IuiBatVer', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'BMS', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10166', 'HVAC_uiHVACSWVersion_4', '4车空调软件版本', 'HVAC_uiHVACSWVersion_4', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10072', 'DCU42_IuiAdhSWRevision', '4车粘着软件版本号', 'DCU42_IuiAdhSWRevision', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10158', 'HMI_CuiTLDWVer_4', '4车线路巡检系统软件版本', 'HMI_CuiTLDWVer_4', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10138', 'HMI_CuiSWVersion_C4', '4车网关BCU软件版本', 'HMI_CuiSWVersion_C4', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10128', 'ODS_uiPODSSW_4', '4车被动障碍物版本号', 'ODS_uiPODSSW_4', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10073', 'BCU4_IuiSWVersion', '4车设备软件版本', 'BCU4_IuiSWVersion', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'BCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10124', 'DCU42_IuiAdhSWRevision_4', '4车转向架2牵引粘着版本', 'DCU42_IuiAdhSWRevision_4', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10123', 'DCU42_IuiInvSWRevision_4', '4车转向架2牵引逆变版本', 'DCU42_IuiInvSWRevision_4', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10122', 'DCU42_IuiLglSWRevision_4', '4车转向架2牵引逻辑版本', 'DCU42_IuiLglSWRevision_4', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10103', 'DIS_IuiINVDSPVer_4', '4车逆变器DSP软件版本', 'DIS_IuiINVDSPVer_4', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10053', 'SIV4_IuiINVDSPVer', '4车逆变器DSP软件版本', 'SIV4_IuiINVDSPVer', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'SIV', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10054', 'SIV4_IuiINVLGCVer', '4车逆变器LGC软件版本', 'SIV4_IuiINVLGCVer', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'SIV', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10104', 'DIS_IuiINVLGCVer_4', '4车逆变器LGC软件版本', 'DIS_IuiINVLGCVer_4', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10071', 'DCU42_IuiInvSWRevision', '4车逆变软件版本', 'DCU42_IuiInvSWRevision', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10070', 'DCU42_IuiLglSWRevision', '4车逻辑软件版本', 'DCU42_IuiLglSWRevision', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'DCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX202001N10132', 'EDCU_uiDorSWVersion_4', '4车门系统软件版本', 'EDCU_uiDorSWVersion_4', '24', '129', null, '0', '2022-11-30 10:28:07', '0', '2022-11-30 10:28:07', 0, 'VCU', 'A2');
