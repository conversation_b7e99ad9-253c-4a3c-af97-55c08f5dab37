-- 处理信号子系统
UPDATE comm_original_signal SET subsystem = 'BDS' where name_en = 'HMI_CuiBDSSWVersion_1';
UPDATE comm_original_signal_new SET subsystem = 'BDS' where name_en = 'HMI_CuiBDSSWVersion_1';
UPDATE comm_original_signal SET subsystem = 'BDS' where name_en = 'HMI_CuiBDSSWVersion_4';
UPDATE comm_original_signal_new SET subsystem = 'BDS' where name_en = 'HMI_CuiBDSSWVersion_4';
-- 重新导入 mtr_software_mapping 数据
DELETE FROM mtr_software_mapping WHERE 1 = 1;
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10001', 'EDCU1_IuiDor1SWVersion', '1号车门软件版本', 'EDCU1Dor1SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10002', 'EDCU2_IuiDor1SWVersion', '1号车门软件版本', 'EDCU2Dor1SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10003', 'EDCU3_IuiDor1SWVersion', '1号车门软件版本', 'EDCU3Dor1SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10004', 'EDCU4_IuiDor1SWVersion', '1号车门软件版本', 'EDCU4Dor1SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10005', 'EDCU1_IuiDor2SWVersion', '2号车门软件版本', 'EDCU1Dor2SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10006', 'EDCU2_IuiDor2SWVersion', '2号车门软件版本', 'EDCU2Dor2SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10007', 'EDCU3_IuiDor2SWVersion', '2号车门软件版本', 'EDCU3Dor2SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10008', 'EDCU4_IuiDor2SWVersion', '2号车门软件版本', 'EDCU4Dor2SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10009', 'EDCU1_IuiDor3SWVersion', '3号车门软件版本', 'EDCU1Dor3SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10010', 'EDCU2_IuiDor3SWVersion', '3号车门软件版本', 'EDCU2Dor3SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10011', 'EDCU3_IuiDor3SWVersion', '3号车门软件版本', 'EDCU3Dor3SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10012', 'EDCU4_IuiDor3SWVersion', '3号车门软件版本', 'EDCU4Dor3SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10013', 'EDCU1_IuiDor4SWVersion', '4号车门软件版本', 'EDCU1Dor4SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10014', 'EDCU2_IuiDor4SWVersion', '4号车门软件版本', 'EDCU2Dor4SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10015', 'EDCU3_IuiDor4SWVersion', '4号车门软件版本', 'EDCU3Dor4SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10016', 'EDCU4_IuiDor4SWVersion', '4号车门软件版本', 'EDCU4Dor4SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10017', 'EDCU1_IuiDor5SWVersion', '5号车门软件版本', 'EDCU1Dor5SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10018', 'EDCU2_IuiDor5SWVersion', '5号车门软件版本', 'EDCU2Dor5SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10019', 'EDCU3_IuiDor5SWVersion', '5号车门软件版本', 'EDCU3Dor5SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10020', 'EDCU4_IuiDor5SWVersion', '5号车门软件版本', 'EDCU4Dor5SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10021', 'EDCU1_IuiDor6SWVersion', '6号车门软件版本', 'EDCU1Dor6SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10022', 'EDCU2_IuiDor6SWVersion', '6号车门软件版本', 'EDCU2Dor6SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10023', 'EDCU3_IuiDor6SWVersion', '6号车门软件版本', 'EDCU3Dor6SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10024', 'EDCU4_IuiDor6SWVersion', '6号车门软件版本', 'EDCU4Dor6SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10025', 'EDCU1_IuiDor7SWVersion', '7号车门软件版本', 'EDCU1Dor7SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10026', 'EDCU2_IuiDor7SWVersion', '7号车门软件版本', 'EDCU2Dor7SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10027', 'EDCU3_IuiDor7SWVersion', '7号车门软件版本', 'EDCU3Dor7SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10028', 'EDCU4_IuiDor7SWVersion', '7号车门软件版本', 'EDCU4Dor7SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10029', 'EDCU1_IuiDor8SWVersion', '8号车门软件版本', 'EDCU1Dor8SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10030', 'EDCU2_IuiDor8SWVersion', '8号车门软件版本', 'EDCU2Dor8SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10031', 'EDCU3_IuiDor8SWVersion', '8号车门软件版本', 'EDCU3Dor8SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10032', 'EDCU4_IuiDor8SWVersion', '8号车门软件版本', 'EDCU4Dor8SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'EDCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10033', 'AODS1_IuiAODSSW', 'AODS版本', 'AODS1AODSSWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'ODS', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10034', 'AODS4_IuiAODSSW', 'AODS版本', 'AODS4AODSSWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'ODS', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10035', 'HMI_CuiBDSSWVersion_1', 'BDS系统软件版本', 'BDS1SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'BDS', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10036', 'HMI_CuiBDSSWVersion_4', 'BDS系统软件版本', 'BDS4SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'BDS', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10037', 'BMS1_IuiBatVer', 'BMSBat版本号', 'BMS1BatSWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'BMS', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10038', 'BMS4_IuiBatVer', 'BMSBat版本号', 'BMS4BatSWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'BMS', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10039', 'BMS1_Iusedtion', 'BMS软件版本', 'BMS1SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'BMS', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10040', 'BMS4_Iusedtion', 'BMS软件版本', 'BMS4SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'BMS', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10041', 'DCU12_IuiInvSWRevision', 'DCU逆变软件版本', 'DCU12InvSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'DCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10042', 'DCU42_IuiInvSWRevision', 'DCU逆变软件版本', 'DCU42InvSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'DCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10043', 'DCU12_IuiAdhSWRevision', 'DCU粘着软件版本', 'DCU12AdhSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'DCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10044', 'DCU42_IuiAdhSWRevision', 'DCU粘着软件版本', 'DCU42AdhSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'DCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10045', 'EGM2_Ius_SoftVersion', 'EGM软件版本', 'EGM2SWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'ECR', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10046', 'EGM3_Ius_SoftVersion', 'EGM软件版本', 'EGM3SWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'ECR', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10047', 'HMI_CusSWRev_1', 'FCU软件版本', 'FCU1SWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10048', 'HMI_CusSWRev_4', 'FCU软件版本', 'FCU4SWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10049', 'TLDS1_IuiTGPDSWVer', 'GPDS软件版本', 'TLDS1TGPDSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'TLD', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10050', 'HMI_uiSoftVer_com_1', 'LCU通信版本', 'LCU1COMSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10051', 'HMI_uiSoftVer_com_4', 'LCU通信版本', 'LCU4COMSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10052', 'HMI_uiSoftVer_app_1', 'LCU应用程序版本', 'LCU1APPSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10053', 'HMI_uiSoftVer_app_4', 'LCU应用程序版本', 'LCU4APPSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10054', 'PIS_uiPICSWVersion_1', 'PIS软件版本', 'PIS1SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10055', 'PIS_uiPICSWVersion_4', 'PIS软件版本', 'PIS4SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'PIS', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10056', 'PODS1_IuiPODSSW', 'PODS软件版本', 'PODS1SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'ODS', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10057', 'PODS4_IuiPODSSW', 'PODS软件版本', 'PODS4SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'ODS', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10058', 'HMI_CusMajorVersionNum_1', 'RFID主版本', 'Major1VersionNum', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10059', 'HMI_CusMajorVersionNum_4', 'RFID主版本', 'Major4VersionNum', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10060', 'TMS_uiRIOM10SWVersion_1', 'RIOM10软件版本', 'RIOM101SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10061', 'TMS_uiRIOM10SWVersion_4', 'RIOM10软件版本', 'RIOM104SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10062', 'TMS_uiRIOM11SWVersion_1', 'RIOM11软件版本', 'RIOM111SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10063', 'TMS_uiRIOM11SWVersion_4', 'RIOM11软件版本', 'RIOM114SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10064', 'TMS_uiRIOM21SWVersion_2', 'RIOM21软件版本', 'RIOM212SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10065', 'TMS_uiRIOM21SWVersion_3', 'RIOM21软件版本', 'RIOM213SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10066', 'TMS_uiHMIVersion_1', 'TMS软件版本', 'TMS1SWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10067', 'TMS_uiHMIVersion_4', 'TMS软件版本', 'TMS4SWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10068', 'TMS_uiVCUSWVersion_1', 'VCU软件版本', 'VCU1SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10069', 'TMS_uiVCUSWVersion_4', 'VCU软件版本', 'VCU4SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10070', 'ODS_uiPODSSW_1', '被动障碍物版本号', 'PODS1SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10071', 'ODS_uiPODSSW_4', '被动障碍物版本号', 'PODS4SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10072', 'FAU1_IusHWRev', '本机硬件版本', 'FAU1HWRevSWVer', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'RIOM', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10073', 'FAU4_IusHWRev', '本机硬件版本', 'FAU4HWRevSWVer', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'RIOM', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10074', 'DIS_IuiBCGDSPVer_4', '充电机DSP版本', 'DISBCGDSP4SWVer', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10075', 'DIS_IuiBCGDSPVer_1', '充电机DSP版本', 'DISBCGDSP1SWVer', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10076', 'DIS_IuiBCGLGCVer_4', '充电机LGC版本', 'DISBCGLGC4SWVer', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10077', 'DIS_IuiBCGLGCVer_1', '充电机LGC版本', 'DISBCGLGC1SWVer', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10078', 'HMI_CuiTLDSSWVer_1', '轨道几何系统版本', 'TLDS1SWVer', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10079', 'HVAC_uiHVACSWVersion_1', '空调软件版本', 'HVAC1SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10080', 'HVAC_uiHVACSWVersion_2', '空调软件版本', 'HVAC2SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10081', 'HVAC_uiHVACSWVersion_3', '空调软件版本', 'HVAC3SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10082', 'HVAC_uiHVACSWVersion_4', '空调软件版本', 'HVAC4SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10083', 'EDCU_uiDorSWVersion_1', '车门系统软件版本', 'Dor1SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10084', 'EDCU_uiDorSWVersion_2', '车门系统软件版本', 'Dor2SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10085', 'EDCU_uiDorSWVersion_3', '车门系统软件版本', 'Dor3SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10086', 'EDCU_uiDorSWVersion_4', '车门系统软件版本', 'Dor4SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10087', 'ECR_usSoftVersion_2', '能耗记录仪软件版本', 'ECR2SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10088', 'ECR_usSoftVersion_3', '能耗记录仪软件版本', 'ECR3SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10089', 'DIS_IuiINVDSPVer_1', '逆变器DSP软件版本', 'DISINVDSP1SWVer', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10090', 'DIS_IuiINVDSPVer_4', '逆变器DSP软件版本', 'DISINVDSP4SWVer', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10091', 'DIS_IuiINVLGCVer_1', '逆变器LGC软件版本', 'DISINVLGC1SWVer', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10092', 'DIS_IuiINVLGCVer_4', '逆变器LGC软件版本', 'DISINVLGC4SWVer', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10093', 'DCU21_IuiLglSWRevision_2', '牵引逻辑版本', 'DCU212LglSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'DCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10094', 'DCU31_IuiLglSWRevision_3', '牵引逻辑版本', 'DCU313LglSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'DCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10095', 'DCU12_IuiLglSWRevision_1', '牵引逻辑版本', 'DCU121LglSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'DCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10096', 'DCU22_IuiLglSWRevision_2', '牵引逻辑版本', 'DCU222LglSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'DCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10097', 'DCU32_IuiLglSWRevision_3', '牵引逻辑版本', 'DCU323LglSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'DCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10098', 'DCU42_IuiLglSWRevision_4', '牵引逻辑版本', 'DCU424LglSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'DCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10099', 'DCU21_IuiInvSWRevision_2', '牵引逆变版本', 'DCU212InvSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'DCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10100', 'DCU31_IuiInvSWRevision_3', '牵引逆变版本', 'DCU313InvSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'DCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10101', 'DCU12_IuiInvSWRevision_1', '牵引逆变版本', 'DCU121InvSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'DCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10102', 'DCU22_IuiInvSWRevision_2', '牵引逆变版本', 'DCU222InvSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'DCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10103', 'DCU32_IuiInvSWRevision_3', '牵引逆变版本', 'DCU323InvSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'DCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10104', 'DCU42_IuiInvSWRevision_4', '牵引逆变版本', 'DCU424InvSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'DCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10105', 'DCU21_IuiAdhSWRevision_2', '牵引粘着版本', 'DCU212AdhSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'DCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10106', 'DCU31_IuiAdhSWRevision_3', '牵引粘着版本', 'DCU313AdhSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'DCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10107', 'DCU12_IuiAdhSWRevision_1', '牵引粘着版本', 'DCU121AdhSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'DCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10108', 'DCU22_IuiAdhSWRevision_2', '牵引粘着版本', 'DCU222AdhSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'DCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10109', 'DCU32_IuiAdhSWRevision_3', '牵引粘着版本', 'DCU323AdhSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'DCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10110', 'DCU42_IuiAdhSWRevision_4', '牵引粘着版本', 'DCU424AdhSWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'DCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10111', 'HMI_CuiSWVersion_C1', '网关BCU软件版本', 'BCU1SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10112', 'HMI_CuiSWVersion_C2', '网关BCU软件版本', 'BCU2SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'B1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10113', 'HMI_CuiSWVersion_C3', '网关BCU软件版本', 'BCU3SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'B2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10114', 'HMI_CuiSWVersion_C4', '网关BCU软件版本', 'BCU4SWVersion', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10115', 'HMI_CuiTLDWVer_4', '线路巡检软件版本', 'TLD4SWVer', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A2');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10116', 'ODS_uiAODSSW_1', '主动障碍物版本', 'ODS1SWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A1');
INSERT INTO public.mtr_software_mapping (id, signal_name_en, name, signal_list, line_id, vehicle_type_id, structure_code, create_by, create_time, modify_by, modify_time, del_flag, subsystem, location) VALUES ('SX22022N10117', 'ODS_uiAODSSW_4', '主动障碍物版本', 'ODS4SWRevision', '24', '129', null, '0', '2022-12-12 16:53:07', '0', '2022-12-12 16:53:07', 0, 'VCU', 'A2');

