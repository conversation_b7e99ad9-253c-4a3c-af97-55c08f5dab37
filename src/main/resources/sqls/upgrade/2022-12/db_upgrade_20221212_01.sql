-- 编写车辆监控 逃生门紧急解锁请求脚本和仪表盘脚本选择
INSERT INTO public.monitor_signal_function (id, type, name, function_str, create_time, create_by, modify_time, modify_by, del_flag, description, function_name, redis_key) VALUES ('func8', 'escapeDoorStat', '仪表盘-紧急逃生门解锁请求', e'function escapeDoorStat(){
    var item= {};
    var triggers = arguments[2];
    var signals = arguments[0].signals;
    if(triggers != null && triggers.length != 0){
		for (var i = 0; i <triggers.length; i++) {
            var trigger = triggers[i];
            var nameEn = trigger.signalNameEn;
           	value = signals[nameEn];
           	if(value == \'2\'){
           		item[\'slotValue\'] = \'1\';
        		return JSON.stringify(item);
           	}
        }
    }
    item[\'slotValue\'] = false;
    return JSON.stringify(item);
}', '2022-12-12 09:44:36', '0', '2022-12-12 09:44:36', '0', false, '仪表盘-紧急逃生门解锁请求', 'escapeDoorStat', 'trainCode_shadow');

DELETE FROM monitor_table_item WHERE id = 'd167fade4473485fa80d35620b9f3129';
INSERT INTO public.monitor_table_item (id, menu_id, name, type, sort, car_type, signal_function_id, create_time, create_by, modify_time, modify_by, del_flag, pub_and_pri, relation_key, slot_board_id) VALUES ('d167fade4473485fa80d35620b9f3129', 'bdf7b3f7737b4449a7ba6ec629d61441', '逃生门紧急解锁请求', 'board', '30', null, 'func8', '2022-07-13 19:02:58', null, '2022-12-12 11:04:49', null, false, 0, 'dorEscapeStat', null);


-- 健康评分配置子系统 修改成全部子系统 统一雷达图和趋势统计图的子系统数量
DELETE FROM eva_health_rule WHERE category = 'b';
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('201', '129', '车门系统', '0.05', null, 'b', '0', '2022-11-23 15:23:09.000000', '0', '2022-11-23 15:23:09', '1', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('210', '129', '弓网系统', '0.05', null, 'b', '0', '2022-11-23 15:23:09.000000', '0', '2022-11-23 15:23:09', '10', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('211', '129', 'RIOM系统', '0.05', null, 'b', '0', '2022-11-23 15:23:09.000000', '0', '2022-11-23 15:23:09', '11', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('212', '129', '轨道几何&线路巡检系统', '0.05', null, 'b', '0', '2022-11-23 15:23:09.000000', '0', '2022-11-23 15:23:09', '12', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('213', '129', '牵引系统', '0.05', null, 'b', '0', '2022-11-23 15:23:09.000000', '0', '2022-11-23 15:23:09', '13', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('214', '129', '走行部系统', '0.05', null, 'b', '0', '2022-11-23 15:23:09.000000', '0', '2022-11-23 15:23:09', '14', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('215', '129', '火灾系统', '0.05', null, 'b', '0', '2022-11-23 15:23:09.000000', '0', '2022-11-23 15:23:09', '15', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('216', '129', '空调系统', '0.05', null, 'b', '0', '2022-11-23 15:23:09.000000', '0', '2022-11-23 15:23:09', '16', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('217', '129', '辅助系统', '0.05', null, 'b', '0', '2022-11-23 15:23:09.000000', '0', '2022-11-23 15:23:09', '17', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('218', '129', 'VCU-DDU', '0.1', null, 'b', '0', '2022-11-23 15:23:09.000000', '0', '2022-11-23 15:23:09', '18', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('202', '129', '能耗记录仪系统', '0.05', null, 'b', '0', '2022-11-23 15:23:09.000000', '0', '2022-11-23 15:23:09', '2', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('203', '129', 'LCU系统', '0.05', null, 'b', '0', '2022-11-23 15:23:09.000000', '0', '2022-11-23 15:23:09', '3', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('204', '129', '乘客信息系统', '0.05', null, 'b', '0', '2022-11-23 15:23:09.000000', '0', '2022-11-23 15:23:09', '4', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('205', '129', '蓄电池检测系统', '0.05', null, 'b', '0', '2022-11-23 15:23:09.000000', '0', '2022-11-23 15:23:09', '5', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('206', '129', '驾驶系统', '0.05', null, 'b', '0', '2022-11-23 15:23:09.000000', '0', '2022-11-23 15:23:09', '6', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('207', '129', '障碍物检测系统', '0.05', null, 'b', '0', '2022-11-23 15:23:09.000000', '0', '2022-11-23 15:23:09', '7', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('208', '129', '制动系统', '0.1', null, 'b', '0', '2022-11-23 15:23:09.000000', '0', '2022-11-23 15:23:09', '8', '24');
INSERT INTO public.eva_health_rule (id, vehicle_type_id, item_name, item_value, unit, category, create_by, create_time, modify_by, modify_time, remark, line_id) VALUES ('209', '129', 'ATC系统', '0.05', null, 'b', '0', '2022-11-23 15:23:09.000000', '0', '2022-11-23 15:23:09', '9', '24');
