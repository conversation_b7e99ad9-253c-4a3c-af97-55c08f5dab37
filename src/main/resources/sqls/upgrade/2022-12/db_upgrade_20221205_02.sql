-- 区域数据死循环处理 sys_organization
DELETE FROM sys_organization WHERE 1 = 1;
INSERT INTO public.sys_organization (id, parent_id, node_type, name, name_en, code, type, location_id, remark, create_time, create_by, modify_time, modify_by, del_flag) VALUES ('136', '135', '子节点', '绍兴地铁2号线', 'ShaoXing metroTwo', '00202', ' 部门', '15', null, '2022-05-11 15:05:47', '0', '2022-11-25 10:33:36', '0', '0');
INSERT INTO public.sys_organization (id, parent_id, node_type, name, name_en, code, type, location_id, remark, create_time, create_by, modify_time, modify_by, del_flag) VALUES ('135', '-1', '子节点', '绍兴地铁', 'ShaoXing metro', '002', '公司', '15', null, '2022-05-11 15:05:47', '0', '2022-11-25 10:30:49', '0', '0');
INSERT INTO public.sys_organization (id, parent_id, node_type, name, name_en, code, type, location_id, remark, create_time, create_by, modify_time, modify_by, del_flag) VALUES ('33412622615322624', '135', '子节点', 'SX2运维部', 'SXtwoYW', '00202YW', '部门', '15', null, '2022-05-11 15:05:47', '0', '2022-05-11 15:05:47', '0', '0');

-- 初始化表 dw_realtime_train_status_by_day
DELETE FROM dw_realtime_train_status_by_day WHERE 1 = 1;
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02008', 'ECR_udHistFDB', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02008', 'TMS_udTotalRunTime', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02008', 'ECR_udHistTTL', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02008', 'ECR_udHistMotor', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02008', 'ECR_udRunDistance', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02008', 'ECR_udHistBCH', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02008', 'ECR_udHistAUX', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02001', 'TMS_udTotalRunTime', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02001', 'ECR_udRunDistance', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02001', 'ECR_udHistFDB', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02001', 'ECR_udHistMotor', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02001', 'ECR_udHistAUX', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02001', 'ECR_udHistBCH', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02001', 'ECR_udHistTTL', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02002', 'ECR_udHistTTL', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02002', 'TMS_udTotalRunTime', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02002', 'ECR_udRunDistance', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02002', 'ECR_udHistFDB', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02002', 'ECR_udHistMotor', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02002', 'ECR_udHistAUX', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02002', 'ECR_udHistBCH', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02003', 'ECR_udHistTTL', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02003', 'TMS_udTotalRunTime', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02003', 'ECR_udRunDistance', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02003', 'ECR_udHistFDB', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02003', 'ECR_udHistMotor', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02003', 'ECR_udHistAUX', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02003', 'ECR_udHistBCH', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02004', 'ECR_udHistTTL', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02004', 'TMS_udTotalRunTime', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02004', 'ECR_udRunDistance', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02004', 'ECR_udHistFDB', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02004', 'ECR_udHistMotor', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02004', 'ECR_udHistAUX', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02004', 'ECR_udHistBCH', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02005', 'ECR_udHistTTL', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02005', 'TMS_udTotalRunTime', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02005', 'ECR_udRunDistance', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02005', 'ECR_udHistFDB', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02005', 'ECR_udHistMotor', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02005', 'ECR_udHistAUX', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02005', 'ECR_udHistBCH', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02006', 'ECR_udHistTTL', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02006', 'TMS_udTotalRunTime', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02006', 'ECR_udRunDistance', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02006', 'ECR_udHistFDB', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02006', 'ECR_udHistMotor', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02006', 'ECR_udHistAUX', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02006', 'ECR_udHistBCH', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02007', 'ECR_udHistTTL', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02007', 'TMS_udTotalRunTime', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02007', 'ECR_udRunDistance', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02007', 'ECR_udHistFDB', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02007', 'ECR_udHistMotor', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02007', 'ECR_udHistAUX', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02007', 'ECR_udHistBCH', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02009', 'TMS_udTotalRunTime', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02009', 'ECR_udHistAUX', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02009', 'ECR_udHistBCH', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02009', 'ECR_udRunDistance', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02009', 'ECR_udHistMotor', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02009', 'ECR_udHistTTL', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02009', 'ECR_udHistFDB', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02010', 'TMS_udTotalRunTime', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02010', 'ECR_udHistAUX', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02010', 'ECR_udHistBCH', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02010', 'ECR_udRunDistance', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02010', 'ECR_udHistMotor', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02010', 'ECR_udHistTTL', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02010', 'ECR_udHistFDB', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02011', 'TMS_udTotalRunTime', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02011', 'ECR_udHistAUX', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02011', 'ECR_udHistBCH', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02011', 'ECR_udRunDistance', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02011', 'ECR_udHistMotor', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02011', 'ECR_udHistTTL', 0, 0);
INSERT INTO public.dw_realtime_train_status_by_day (time, train_id, signal_name_en, accumlation, increament) VALUES ('20221118', '02011', 'ECR_udHistFDB', 0, 0);

--
delete from ekb_fault_type where id > '3000';
alter table ekb_fault_type
    add constraint code_name_location_category_unique
        unique (fault_code, name_cn, fault_category, location);
comment on constraint code_name_location_category_unique on ekb_fault_type is '故障唯一键 通过类型、名称、编码、位置';

-- 更新车辆监控 子系统顺序和子系统父子关系
DELETE FROM monitor_menu WHERE 1 = 1;
INSERT INTO public.monitor_menu (id, menu_code, parent_id, name, sort, board_status, menu_type, url, create_time, create_by, modify_time, modify_by, del_flag, show_status, tra_code, components_url, default_active) VALUES ('36f962c87ab144e1a89c186ab32c7e16', 'status-overview', '-1', '状态总览', '1', true, 'menuBaseType', null, '2022-05-24 15:05:33', '', '2022-08-19 09:26:45', null, false, true, '02001', '@/views/monitoring/components/status-overview', true);
INSERT INTO public.monitor_menu (id, menu_code, parent_id, name, sort, board_status, menu_type, url, create_time, create_by, modify_time, modify_by, del_flag, show_status, tra_code, components_url, default_active) VALUES ('bbebc723520b4c13ad0100f15ff3d022', 'train-aux-sys', '59392961d1654ca895515efd1d4e4a60', '辅助系统', '10', false, 'menuBaseType', null, '2022-06-14 10:39:38', '', '2022-12-05 17:14:49', null, false, false, '02001', '@/views/monitoring/components/train-aux-sys', false);
INSERT INTO public.monitor_menu (id, menu_code, parent_id, name, sort, board_status, menu_type, url, create_time, create_by, modify_time, modify_by, del_flag, show_status, tra_code, components_url, default_active) VALUES ('8509449c841d4f379f7c35f19d11aad1', 'train-air-sys', '59392961d1654ca895515efd1d4e4a60', '空调系统', '11', false, 'menuBaseType', null, '2022-05-30 10:04:00', '', '2022-12-05 17:04:59', null, false, false, '02001', '@/views/monitoring/components/train-air-sys', false);
INSERT INTO public.monitor_menu (id, menu_code, parent_id, name, sort, board_status, menu_type, url, create_time, create_by, modify_time, modify_by, del_flag, show_status, tra_code, components_url, default_active) VALUES ('8ec38c42240d4f2689d912e8a8c6b8bc', 'train-door-sys', '59392961d1654ca895515efd1d4e4a60', '车门系统', '12', false, 'menuBaseType', null, '2022-08-15 11:25:47', '', '2022-12-05 17:14:57', null, false, true, '02001', '@/views/monitoring/components/train-door-sys', false);
INSERT INTO public.monitor_menu (id, menu_code, parent_id, name, sort, board_status, menu_type, url, create_time, create_by, modify_time, modify_by, del_flag, show_status, tra_code, components_url, default_active) VALUES ('bd7e24e1e781451c8653b7009b62d625', 'train-energy-sys', '59392961d1654ca895515efd1d4e4a60', '能耗系统', '13', false, 'menuBaseType', null, '2022-08-15 16:02:40', '', '2022-12-05 17:08:09', null, false, true, '02001', '@/views/monitoring/components/train-energy-sys', false);
INSERT INTO public.monitor_menu (id, menu_code, parent_id, name, sort, board_status, menu_type, url, create_time, create_by, modify_time, modify_by, del_flag, show_status, tra_code, components_url, default_active) VALUES ('29685b424f734824bb895d6383664247', 'train-pis-sys', '59392961d1654ca895515efd1d4e4a60', '乘客信息系统', '14', false, 'menuBaseType', null, '2022-08-15 14:44:12', '', '2022-12-05 17:15:09', null, false, true, '02001', '@/views/monitoring/components/train-pis-sys', false);
INSERT INTO public.monitor_menu (id, menu_code, parent_id, name, sort, board_status, menu_type, url, create_time, create_by, modify_time, modify_by, del_flag, show_status, tra_code, components_url, default_active) VALUES ('648dffd19bfc448b915e89ef863f314b', 'train-no-bat-sys', '59392961d1654ca895515efd1d4e4a60', '蓄电池检测系统', '15', false, 'menuBaseType', null, '2022-08-29 14:24:03', '', '2022-12-05 17:15:17', null, false, true, '02001', '@/views/monitoring/components/train-no-bat-sys', false);
INSERT INTO public.monitor_menu (id, menu_code, parent_id, name, sort, board_status, menu_type, url, create_time, create_by, modify_time, modify_by, del_flag, show_status, tra_code, components_url, default_active) VALUES ('589348d29f20438a8d5e93fdc89a288d', 'train-trackInspection-sys', '59392961d1654ca895515efd1d4e4a60', '障碍物检测系统', '16', false, 'menuBaseType', null, '2022-08-29 14:48:53', '', '2022-12-05 17:15:26', null, false, true, '02001', '@/views/monitoring/components/train-trackInspection-sys', false);
INSERT INTO public.monitor_menu (id, menu_code, parent_id, name, sort, board_status, menu_type, url, create_time, create_by, modify_time, modify_by, del_flag, show_status, tra_code, components_url, default_active) VALUES ('b61fb5da92bb4e6a8fa63a95a6965777', 'travel-department', '59392961d1654ca895515efd1d4e4a60', '走行部', '17', false, 'menuBaseType', null, '2022-08-30 09:44:16', '', '2022-12-05 17:15:39', null, false, true, '02001', '@/views/monitoring/components/travel-department', false);
INSERT INTO public.monitor_menu (id, menu_code, parent_id, name, sort, board_status, menu_type, url, create_time, create_by, modify_time, modify_by, del_flag, show_status, tra_code, components_url, default_active) VALUES ('25f2f489630749e9b783819fa1ebbf5d', 'train-fire-sys', '59392961d1654ca895515efd1d4e4a60', '火灾系统', '18', false, 'menuBaseType', null, '2022-08-15 15:26:51', '', '2022-12-05 17:08:04', null, false, true, '02001', '@/views/monitoring/components/train-fire-sys', false);
INSERT INTO public.monitor_menu (id, menu_code, parent_id, name, sort, board_status, menu_type, url, create_time, create_by, modify_time, modify_by, del_flag, show_status, tra_code, components_url, default_active) VALUES ('bdf7b3f7737b4449a7ba6ec629d61441', 'dashboard', '-1', '仪表盘', '2', null, 'dashBoard', null, '2022-05-30 10:04:28', '', null, null, false, null, '02001', null, false);
INSERT INTO public.monitor_menu (id, menu_code, parent_id, name, sort, board_status, menu_type, url, create_time, create_by, modify_time, modify_by, del_flag, show_status, tra_code, components_url, default_active) VALUES ('59392961d1654ca895515efd1d4e4a60', 'subsystems', '-1', '各子系统', '3', false, 'menuBaseType', null, '2022-05-27 11:10:03', '', '2022-08-15 11:24:24', null, false, false, '02001', '1', false);
INSERT INTO public.monitor_menu (id, menu_code, parent_id, name, sort, board_status, menu_type, url, create_time, create_by, modify_time, modify_by, del_flag, show_status, tra_code, components_url, default_active) VALUES ('3f7ac5862801415592b75ba1e8a704e1', 'train-bypass', '-1', '旁路', '4', false, 'menuBaseType', null, '2022-05-30 13:39:14', '', '2022-12-05 17:13:58', null, false, false, '02001', '@/views/monitoring/components/train-bypass', false);
INSERT INTO public.monitor_menu (id, menu_code, parent_id, name, sort, board_status, menu_type, url, create_time, create_by, modify_time, modify_by, del_flag, show_status, tra_code, components_url, default_active) VALUES ('134f18cb101b4a2abfd909663c517f43', 'train-version', '-1', '软件版本', '5', false, 'menuBaseType', null, '2022-05-30 14:19:47', '', '2022-12-05 17:14:07', null, false, false, '02001', '@/views/monitoring/components/train-version', false);
INSERT INTO public.monitor_menu (id, menu_code, parent_id, name, sort, board_status, menu_type, url, create_time, create_by, modify_time, modify_by, del_flag, show_status, tra_code, components_url, default_active) VALUES ('66155fb2618d451ab7b36bf40846e60b', 'online-status', '-1', '通信状态', '6', false, 'menuSvgType', null, '2022-06-14 16:35:28', '', '2022-12-05 17:14:19', null, false, false, '02001', '@/views/monitoring/components/online-status', false);
INSERT INTO public.monitor_menu (id, menu_code, parent_id, name, sort, board_status, menu_type, url, create_time, create_by, modify_time, modify_by, del_flag, show_status, tra_code, components_url, default_active) VALUES ('d44f2a8053cf4cd5ada1db3aca082f00', 'io', '-1', 'IO', '7', true, 'menuRiomType', null, '2022-08-19 10:28:04', '', '2022-12-05 17:14:23', null, false, true, '02001', '@/views/monitoring/components/io', false);
INSERT INTO public.monitor_menu (id, menu_code, parent_id, name, sort, board_status, menu_type, url, create_time, create_by, modify_time, modify_by, del_flag, show_status, tra_code, components_url, default_active) VALUES ('7b5508237c304c61a907d0fa86d2c81e', 'train-traction-sys', '59392961d1654ca895515efd1d4e4a60', '牵引系统', '8', false, 'menuBaseType', null, '2022-06-13 11:03:23', '', '2022-12-05 17:14:39', null, false, false, '02001', '@/views/monitoring/components/train-traction-sys', false);
INSERT INTO public.monitor_menu (id, menu_code, parent_id, name, sort, board_status, menu_type, url, create_time, create_by, modify_time, modify_by, del_flag, show_status, tra_code, components_url, default_active) VALUES ('819c0b8913e34911add5a1d01d60825e', 'train-brk-sys', '59392961d1654ca895515efd1d4e4a60', '制动系统', '9', false, 'menuBaseType', null, '2022-06-13 11:39:02', '', '2022-12-05 17:14:44', null, false, false, '02001', '@/views/monitoring/components/train-brk-sys', false);


-- 信号表 有部分信号 redis_flag 值为 ' '
UPDATE comm_original_signal SET redis_flag = '1' WHERE redis_flag = ' ';

-- 修改紧急逃生门信号触发值 2和3颠倒了
DELETE FROM monitor_trigger where id IN('e10a4014cc1f40b284e6dc059023f493',
                                        '356eec2055794ee28b03d38961437ad5',
                                        '169b351b80604ac4b2258638a152b245',
                                        '60b772ae66e045a381bd9d9cae7d2b7b');
INSERT INTO public.monitor_trigger (id, slot_id, label, sort, signal_id, trigger_value, data_display_point, unit_status, svg_url, ext_properties, create_time, create_by, modify_time, modify_by, del_flag, image_type, image_path, signal_name_en, mark) VALUES ('e10a4014cc1f40b284e6dc059023f493', 'c749836c1a274087981a194b5f960399', '未关好', 3, '10967490', '3', '0', null, '', null, '2022-08-15 13:38:39', null, '2022-12-06 16:46:53', null, false, null, null, 'DOR_usEscapeStat_1', null);
INSERT INTO public.monitor_trigger (id, slot_id, label, sort, signal_id, trigger_value, data_display_point, unit_status, svg_url, ext_properties, create_time, create_by, modify_time, modify_by, del_flag, image_type, image_path, signal_name_en, mark) VALUES ('356eec2055794ee28b03d38961437ad5', '4f277ab8b11243da89f20da23dc224a1', '未关好', 3, '10967500', '3', '0', null, '', null, '2022-08-15 13:38:44', null, '2022-12-06 16:46:45', null, false, null, null, 'DOR_usEscapeStat_4', null);
INSERT INTO public.monitor_trigger (id, slot_id, label, sort, signal_id, trigger_value, data_display_point, unit_status, svg_url, ext_properties, create_time, create_by, modify_time, modify_by, del_flag, image_type, image_path, signal_name_en, mark) VALUES ('169b351b80604ac4b2258638a152b245', '4f277ab8b11243da89f20da23dc224a1', '紧急解锁请求', 4, '10967500', '2', '0', null, '', null, '2022-08-15 13:38:44', null, '2022-12-06 16:46:45', null, false, null, null, 'DOR_usEscapeStat_4', null);
INSERT INTO public.monitor_trigger (id, slot_id, label, sort, signal_id, trigger_value, data_display_point, unit_status, svg_url, ext_properties, create_time, create_by, modify_time, modify_by, del_flag, image_type, image_path, signal_name_en, mark) VALUES ('60b772ae66e045a381bd9d9cae7d2b7b', 'c749836c1a274087981a194b5f960399', '紧急解锁请求', 4, '10967490', '2', '0', null, '', null, '2022-08-15 13:38:39', null, '2022-12-06 16:46:53', null, false, null, null, 'DOR_usEscapeStat_1', null);

