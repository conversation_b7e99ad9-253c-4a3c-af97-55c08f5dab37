UPDATE monitor_signal_function
SET "type"='getRideRateInfo', "name"='仪表盘-乘车率', function_str='function getRideRateInfo() {
    var returnObj = {};
    var sum = 0;
    var value;
    var eValue = arguments[0].signals;
    var nameEn ;
    for (var i = 0; i < arguments[2].length; i++) {
        nameEn = arguments[2][i].signalNameEn;	
		value = eValue[nameEn];
		if (value == undefined || value == null || isNaN(value)) {
			value = 0;
		}else{
			value = value;
		} 
		sum += Number(value);
	}
    returnObj[''slotValue''] = Math.round(sum * 1000/(60 * 240 * 4) * 100);
    return JSON.stringify(returnObj);
}', create_time='2022-06-07 14:44:36.000', create_by='0', modify_time = CURRENT_TIMESTAMP, modify_by='0', del_flag=false, description='计算车厢乘车率', function_name='getRideRateInfo', redis_key='trainCode_shadow'
WHERE id='func3';
