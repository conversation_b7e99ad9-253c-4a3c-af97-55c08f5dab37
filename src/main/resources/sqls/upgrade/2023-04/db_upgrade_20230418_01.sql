-- 轮对测量修改车厢为A1 B1 B2 A2
UPDATE mtr_wheel_measurement_data SET name_cn = 'B1' WHERE name_cn = 'MP1';
UPDATE mtr_wheel_measurement_data SET name_cn = 'B2' WHERE name_cn = 'MP2';
UPDATE mtr_wheel_measurement_data SET name_cn = 'A1' WHERE name_cn = 'TC1';
UPDATE mtr_wheel_measurement_data SET name_cn = 'A2' WHERE name_cn = 'TC2';
DELETE FROM  mtr_wheel_measurement_data WHERE name_cn = 'M1';
DELETE FROM  mtr_wheel_measurement_data WHERE name_cn = 'M2';

alter table dw_realtime_train_status_by_day
    drop constraint if exists dw_realtime_train_status_by_day_unique_key;

ALTER TABLE dw_realtime_train_status_by_day
    ADD CONSTRAINT dw_realtime_train_status_by_day_unique_key UNIQUE (time, train_id, signal_name_en);