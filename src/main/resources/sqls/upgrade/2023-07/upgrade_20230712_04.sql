-- 信号v0.07版本字典新增2个数据
DELETE FROM ekb_fault_type WHERE id in('2749','2750');

INSERT INTO ekb_fault_type (id, vehicle_type_id, fault_type_key, name_cn, name_en, fault_code, fault_level, subsystem, del_flag, create_time, modify_by, modify_time, description, location, enable, fault_mode, frontline_disposal_recommendations, overhaul_suggestions, fault_reason, model_code, line_id, fault_category, vehicle_structure_code, create_by) VALUES ('2749', '129', 'VCU_5027_62654', '警惕按钮松开', 'HMI_CxEBDMS_C1', '5027', 1, 'VCU', 0, '2023-07-12 14:16:03.963850', null, '2023-07-12 14:16:04', '1=松开', 'TMc1', false, '0', null, '警惕按钮松开', null, null, '24', 1, null, null);
INSERT INTO ekb_fault_type (id, vehicle_type_id, fault_type_key, name_cn, name_en, fault_code, fault_level, subsystem, del_flag, create_time, modify_by, modify_time, description, location, enable, fault_mode, frontline_disposal_recommendations, overhaul_suggestions, fault_reason, model_code, line_id, fault_category, vehicle_structure_code, create_by) VALUES ('2750', '129', 'VCU_5027_62655', '警惕按钮松开', 'HMI_CxEBDMS_C4', '5027', 1, 'VCU', 0, '2023-07-12 14:16:03.963850', null, '2023-07-12 14:16:04', '1=松开', 'TMc2', false, '0', null, '警惕按钮松开', null, null, '24', 1, null, null);
