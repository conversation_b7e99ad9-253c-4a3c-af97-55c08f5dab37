-- 绍兴2号线小表更新到v0.07版本

DELETE FROM comm_original_signal WHERE create_time >= '2023-07-12 00:00:00' and create_time <= '2023-07-12 23:00:00';

UPDATE comm_original_signal SET name_cn = '网关BCU通信故障'               ,modify_time = '2023-07-12 12:12:12'              WHERE name_en = 'HMI_CxGVMVBComFlt_C1';
UPDATE comm_original_signal SET name_cn = '网关BCU通信故障'               ,modify_time = '2023-07-12 12:12:12'              WHERE name_en = 'HMI_CxGVMVBComFlt_C2';
UPDATE comm_original_signal SET name_cn = '网关BCU通信故障'               ,modify_time = '2023-07-12 12:12:12'              WHERE name_en = 'HMI_CxGVMVBComFlt_C3';
UPDATE comm_original_signal SET name_cn = '网关BCU通信故障'               ,modify_time = '2023-07-12 12:12:12'              WHERE name_en = 'HMI_CxGVMVBComFlt_C4';
UPDATE comm_original_signal SET name_cn = '转向架1BCU通信故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBCUComFltB1_C1';
UPDATE comm_original_signal SET name_cn = '转向架1BCU通信故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBCUComFltB1_C2';
UPDATE comm_original_signal SET name_cn = '转向架1BCU通信故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBCUComFltB1_C3';
UPDATE comm_original_signal SET name_cn = '转向架1BCU通信故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBCUComFltB1_C4';
UPDATE comm_original_signal SET name_cn = '转向架2BCU通信故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBCUComFltB2_C1';
UPDATE comm_original_signal SET name_cn = '转向架2BCU通信故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBCUComFltB2_C2';
UPDATE comm_original_signal SET name_cn = '转向架2BCU通信故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBCUComFltB2_C3';
UPDATE comm_original_signal SET name_cn = '转向架2BCU通信故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBCUComFltB2_C4';
UPDATE comm_original_signal SET name_cn = '所有制动缓解继电器故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'BRK_xABRRFlt_C1';
UPDATE comm_original_signal SET name_cn = '所有制动缓解继电器故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'BRK_xABRRFlt_C4';
UPDATE comm_original_signal SET name_cn = '转向架1制动阀严重故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMajorFltB1_C1';
UPDATE comm_original_signal SET name_cn = '转向架1制动阀严重故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMajorFltB1_C2';
UPDATE comm_original_signal SET name_cn = '转向架1制动阀严重故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMajorFltB1_C3';
UPDATE comm_original_signal SET name_cn = '转向架1制动阀严重故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMajorFltB1_C4';
UPDATE comm_original_signal SET name_cn = '转向架2制动阀严重故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMajorFltB2_C1';
UPDATE comm_original_signal SET name_cn = '转向架2制动阀严重故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMajorFltB2_C2';
UPDATE comm_original_signal SET name_cn = '转向架2制动阀严重故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMajorFltB2_C3';
UPDATE comm_original_signal SET name_cn = '转向架2制动阀严重故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMajorFltB2_C4';
UPDATE comm_original_signal SET name_cn = '转向架1制动阀中等故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMediumFltB1_C1';
UPDATE comm_original_signal SET name_cn = '转向架1制动阀中等故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMediumFltB1_C2';
UPDATE comm_original_signal SET name_cn = '转向架1制动阀中等故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMediumFltB1_C3';
UPDATE comm_original_signal SET name_cn = '转向架1制动阀中等故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMediumFltB1_C4';
UPDATE comm_original_signal SET name_cn = '转向架2制动阀中等故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMediumFltB2_C1';
UPDATE comm_original_signal SET name_cn = '转向架2制动阀中等故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMediumFltB2_C2';
UPDATE comm_original_signal SET name_cn = '转向架2制动阀中等故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMediumFltB2_C3';
UPDATE comm_original_signal SET name_cn = '转向架2制动阀中等故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMediumFltB2_C4';
UPDATE comm_original_signal SET name_cn = '转向架1制动阀轻微故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMinorFltB1_C1';
UPDATE comm_original_signal SET name_cn = '转向架1制动阀轻微故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMinorFltB1_C2';
UPDATE comm_original_signal SET name_cn = '转向架1制动阀轻微故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMinorFltB1_C3';
UPDATE comm_original_signal SET name_cn = '转向架1制动阀轻微故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMinorFltB1_C4';
UPDATE comm_original_signal SET name_cn = '转向架2制动阀轻微故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMinorFltB2_C1';
UPDATE comm_original_signal SET name_cn = '转向架2制动阀轻微故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMinorFltB2_C2';
UPDATE comm_original_signal SET name_cn = '转向架2制动阀轻微故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMinorFltB2_C3';
UPDATE comm_original_signal SET name_cn = '转向架2制动阀轻微故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBVMinorFltB2_C4';
UPDATE comm_original_signal SET name_cn = '转向架1制动风缸低压'              ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxLBSRB1_C1';
UPDATE comm_original_signal SET name_cn = '转向架1制动风缸低压'              ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxLBSRB1_C2';
UPDATE comm_original_signal SET name_cn = '转向架1制动风缸低压'              ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxLBSRB1_C3';
UPDATE comm_original_signal SET name_cn = '转向架1制动风缸低压'              ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxLBSRB1_C4';
UPDATE comm_original_signal SET name_cn = '转向架2制动风缸低压'              ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxLBSRB2_C1';
UPDATE comm_original_signal SET name_cn = '转向架2制动风缸低压'              ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxLBSRB2_C2';
UPDATE comm_original_signal SET name_cn = '转向架2制动风缸低压'              ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxLBSRB2_C3';
UPDATE comm_original_signal SET name_cn = '转向架2制动风缸低压'              ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxLBSRB2_C4';
UPDATE comm_original_signal SET name_cn = '转向架1制动不缓解故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBrkNotRelFltB1_C1';
UPDATE comm_original_signal SET name_cn = '转向架1制动不缓解故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBrkNotRelFltB1_C2';
UPDATE comm_original_signal SET name_cn = '转向架1制动不缓解故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBrkNotRelFltB1_C3';
UPDATE comm_original_signal SET name_cn = '转向架1制动不缓解故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBrkNotRelFltB1_C4';
UPDATE comm_original_signal SET name_cn = '转向架2制动不缓解故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBrkNotRelFltB2_C1';
UPDATE comm_original_signal SET name_cn = '转向架2制动不缓解故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBrkNotRelFltB2_C2';
UPDATE comm_original_signal SET name_cn = '转向架2制动不缓解故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBrkNotRelFltB2_C3';
UPDATE comm_original_signal SET name_cn = '转向架2制动不缓解故障'             ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxBrkNotRelFltB2_C4';
UPDATE comm_original_signal SET name_cn = '转向架1轴1速度传感器故障'           ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxSpdSensFltA11_C1';
UPDATE comm_original_signal SET name_cn = '转向架1轴1速度传感器故障'           ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxSpdSensFltA11_C2';
UPDATE comm_original_signal SET name_cn = '转向架1轴1速度传感器故障'           ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxSpdSensFltA11_C3';
UPDATE comm_original_signal SET name_cn = '转向架1轴1速度传感器故障'           ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxSpdSensFltA11_C4';
UPDATE comm_original_signal SET name_cn = '转向架1轴2速度传感器故障'           ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxSpdSensFltA12_C1';
UPDATE comm_original_signal SET name_cn = '转向架1轴2速度传感器故障'           ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxSpdSensFltA12_C2';
UPDATE comm_original_signal SET name_cn = '转向架1轴2速度传感器故障'           ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxSpdSensFltA12_C3';
UPDATE comm_original_signal SET name_cn = '转向架1轴2速度传感器故障'           ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxSpdSensFltA12_C4';
UPDATE comm_original_signal SET name_cn = '转向架2轴1速度传感器故障'           ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxSpdSensFltA21_C1';
UPDATE comm_original_signal SET name_cn = '转向架2轴1速度传感器故障'           ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxSpdSensFltA21_C2';
UPDATE comm_original_signal SET name_cn = '转向架2轴1速度传感器故障'           ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxSpdSensFltA21_C3';
UPDATE comm_original_signal SET name_cn = '转向架2轴1速度传感器故障'           ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxSpdSensFltA21_C4';
UPDATE comm_original_signal SET name_cn = '转向架2轴2速度传感器故障'           ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxSpdSensFltA22_C1';
UPDATE comm_original_signal SET name_cn = '转向架2轴2速度传感器故障'           ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxSpdSensFltA22_C2';
UPDATE comm_original_signal SET name_cn = '转向架2轴2速度传感器故障'           ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxSpdSensFltA22_C3';
UPDATE comm_original_signal SET name_cn = '转向架2轴2速度传感器故障'           ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxSpdSensFltA22_C4';
UPDATE comm_original_signal SET name_cn = '转向架1空气悬挂数值超出范围'          ,modify_time = '2023-07-12 12:12:12'            WHERE name_en = 'HMI_CxAirSuspFltB1_C1';
UPDATE comm_original_signal SET name_cn = '转向架1空气悬挂数值超出范围'          ,modify_time = '2023-07-12 12:12:12'            WHERE name_en = 'HMI_CxAirSuspFltB1_C2';
UPDATE comm_original_signal SET name_cn = '转向架1空气悬挂数值超出范围'          ,modify_time = '2023-07-12 12:12:12'            WHERE name_en = 'HMI_CxAirSuspFltB1_C3';
UPDATE comm_original_signal SET name_cn = '转向架1空气悬挂数值超出范围'          ,modify_time = '2023-07-12 12:12:12'            WHERE name_en = 'HMI_CxAirSuspFltB1_C4';
UPDATE comm_original_signal SET name_cn = '转向架2空气悬挂数值超出范围'          ,modify_time = '2023-07-12 12:12:12'            WHERE name_en = 'HMI_CxAirSuspFltB2_C1';
UPDATE comm_original_signal SET name_cn = '转向架2空气悬挂数值超出范围'          ,modify_time = '2023-07-12 12:12:12'            WHERE name_en = 'HMI_CxAirSuspFltB2_C2';
UPDATE comm_original_signal SET name_cn = '转向架2空气悬挂数值超出范围'          ,modify_time = '2023-07-12 12:12:12'            WHERE name_en = 'HMI_CxAirSuspFltB2_C3';
UPDATE comm_original_signal SET name_cn = '转向架2空气悬挂数值超出范围'          ,modify_time = '2023-07-12 12:12:12'            WHERE name_en = 'HMI_CxAirSuspFltB2_C4';
UPDATE comm_original_signal SET name_cn = '转向架1架号异常'                ,modify_time = '2023-07-12 12:12:12'                WHERE name_en = 'BRK_xCodingPlugFltB1_C1';
UPDATE comm_original_signal SET name_cn = '转向架1架号异常'                ,modify_time = '2023-07-12 12:12:12'                WHERE name_en = 'BRK_xCodingPlugFltB1_C2';
UPDATE comm_original_signal SET name_cn = '转向架1架号异常'                ,modify_time = '2023-07-12 12:12:12'                WHERE name_en = 'BRK_xCodingPlugFltB1_C3';
UPDATE comm_original_signal SET name_cn = '转向架1架号异常'                ,modify_time = '2023-07-12 12:12:12'                WHERE name_en = 'BRK_xCodingPlugFltB1_C4';
UPDATE comm_original_signal SET name_cn = '转向架2架号异常'                ,modify_time = '2023-07-12 12:12:12'                WHERE name_en = 'BRK_xCodingPlugFltB2_C1';
UPDATE comm_original_signal SET name_cn = '转向架2架号异常'                ,modify_time = '2023-07-12 12:12:12'                WHERE name_en = 'BRK_xCodingPlugFltB2_C2';
UPDATE comm_original_signal SET name_cn = '转向架2架号异常'                ,modify_time = '2023-07-12 12:12:12'                WHERE name_en = 'BRK_xCodingPlugFltB2_C3';
UPDATE comm_original_signal SET name_cn = '转向架2架号异常'                ,modify_time = '2023-07-12 12:12:12'                WHERE name_en = 'BRK_xCodingPlugFltB2_C4';
UPDATE comm_original_signal SET name_cn = '转向架1内部传感器故障'             ,modify_time = '2023-07-12 12:12:12'               WHERE name_en = 'BRK_xInSensFltB1_C1';
UPDATE comm_original_signal SET name_cn = '转向架1内部传感器故障'             ,modify_time = '2023-07-12 12:12:12'               WHERE name_en = 'BRK_xInSensFltB1_C2';
UPDATE comm_original_signal SET name_cn = '转向架1内部传感器故障'             ,modify_time = '2023-07-12 12:12:12'               WHERE name_en = 'BRK_xInSensFltB1_C3';
UPDATE comm_original_signal SET name_cn = '转向架1内部传感器故障'             ,modify_time = '2023-07-12 12:12:12'               WHERE name_en = 'BRK_xInSensFltB1_C4'          ;
UPDATE comm_original_signal SET name_cn = '转向架2内部传感器故障'             ,modify_time = '2023-07-12 12:12:12'               WHERE name_en = 'BRK_xInSensFltB2_C1'         ;
UPDATE comm_original_signal SET name_cn = '转向架2内部传感器故障'             ,modify_time = '2023-07-12 12:12:12'               WHERE name_en = 'BRK_xInSensFltB2_C2'         ;
UPDATE comm_original_signal SET name_cn = '转向架2内部传感器故障'             ,modify_time = '2023-07-12 12:12:12'               WHERE name_en = 'BRK_xInSensFltB2_C3'         ;
UPDATE comm_original_signal SET name_cn = '转向架2内部传感器故障'             ,modify_time = '2023-07-12 12:12:12'               WHERE name_en = 'BRK_xInSensFltB2_C4'         ;
UPDATE comm_original_signal SET name_cn = '转向架1制动力不足'               ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLackPowerB1_C1'         ;
UPDATE comm_original_signal SET name_cn = '转向架1制动力不足'               ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLackPowerB1_C2'         ;
UPDATE comm_original_signal SET name_cn = '转向架1制动力不足'               ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLackPowerB1_C3'         ;
UPDATE comm_original_signal SET name_cn = '转向架1制动力不足'               ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLackPowerB1_C4'         ;
UPDATE comm_original_signal SET name_cn = '转向架2制动力不足'               ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLackPowerB2_C1'         ;
UPDATE comm_original_signal SET name_cn = '转向架2制动力不足'               ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLackPowerB2_C2'         ;
UPDATE comm_original_signal SET name_cn = '转向架2制动力不足'               ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLackPowerB2_C3'         ;
UPDATE comm_original_signal SET name_cn = '转向架2制动力不足'               ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLackPowerB2_C4'         ;
UPDATE comm_original_signal SET name_cn = '转向架1连通阀故障'               ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xCVFltB1_C1'             ;
UPDATE comm_original_signal SET name_cn = '转向架1连通阀故障'               ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xCVFltB1_C2'             ;
UPDATE comm_original_signal SET name_cn = '转向架1连通阀故障'               ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xCVFltB1_C3'             ;
UPDATE comm_original_signal SET name_cn = '转向架1连通阀故障'               ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xCVFltB1_C4'             ;
UPDATE comm_original_signal SET name_cn = '转向架2连通阀故障'               ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xCVFltB2_C1'             ;
UPDATE comm_original_signal SET name_cn = '转向架2连通阀故障'               ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xCVFltB2_C2'             ;
UPDATE comm_original_signal SET name_cn = '转向架2连通阀故障'               ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xCVFltB2_C3'             ;
UPDATE comm_original_signal SET name_cn = '转向架2连通阀故障'               ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xCVFltB2_C4'             ;
UPDATE comm_original_signal SET name_cn = '转向架1防滑系统故障'              ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xAntiSlipB1_C1'          ;
UPDATE comm_original_signal SET name_cn = '转向架1防滑系统故障'              ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xAntiSlipB1_C2'          ;
UPDATE comm_original_signal SET name_cn = '转向架1防滑系统故障'              ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xAntiSlipB1_C3'          ;
UPDATE comm_original_signal SET name_cn = '转向架1防滑系统故障'              ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xAntiSlipB1_C4'          ;
UPDATE comm_original_signal SET name_cn = '转向架2防滑系统故障'              ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xAntiSlipB2_C1'          ;
UPDATE comm_original_signal SET name_cn = '转向架2防滑系统故障'              ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xAntiSlipB2_C2'          ;
UPDATE comm_original_signal SET name_cn = '转向架2防滑系统故障'              ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xAntiSlipB2_C3'          ;
UPDATE comm_original_signal SET name_cn = '转向架2防滑系统故障'              ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xAntiSlipB2_C4'          ;
UPDATE comm_original_signal SET name_cn = '转向架1防滑超时'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xAntiSlipOvertimeB1_C1'  ;
UPDATE comm_original_signal SET name_cn = '转向架1防滑超时'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xAntiSlipOvertimeB1_C2'  ;
UPDATE comm_original_signal SET name_cn = '转向架1防滑超时'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xAntiSlipOvertimeB1_C3'  ;
UPDATE comm_original_signal SET name_cn = '转向架1防滑超时'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xAntiSlipOvertimeB1_C4'  ;
UPDATE comm_original_signal SET name_cn = '转向架2防滑超时'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xAntiSlipOvertimeB2_C1'  ;
UPDATE comm_original_signal SET name_cn = '转向架2防滑超时'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xAntiSlipOvertimeB2_C2'  ;
UPDATE comm_original_signal SET name_cn = '转向架2防滑超时'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xAntiSlipOvertimeB2_C3'  ;
UPDATE comm_original_signal SET name_cn = '转向架2防滑超时'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xAntiSlipOvertimeB2_C4'  ;
UPDATE comm_original_signal SET name_cn = '转向架1轴1抱死'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLockAxle1B1_C1'         ;
UPDATE comm_original_signal SET name_cn = '转向架1轴1抱死'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLockAxle1B1_C2'         ;
UPDATE comm_original_signal SET name_cn = '转向架1轴1抱死'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLockAxle1B1_C3'         ;
UPDATE comm_original_signal SET name_cn = '转向架1轴1抱死'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLockAxle1B1_C4'         ;
UPDATE comm_original_signal SET name_cn = '转向架2轴1抱死'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLockAxle1B2_C1'         ;
UPDATE comm_original_signal SET name_cn = '转向架2轴1抱死'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLockAxle1B2_C2'         ;
UPDATE comm_original_signal SET name_cn = '转向架2轴1抱死'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLockAxle1B2_C3'         ;
UPDATE comm_original_signal SET name_cn = '转向架2轴1抱死'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLockAxle1B2_C4'         ;
UPDATE comm_original_signal SET name_cn = '转向架1轴2抱死'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLockAxle2B1_C1'         ;
UPDATE comm_original_signal SET name_cn = '转向架1轴2抱死'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLockAxle2B1_C2'         ;
UPDATE comm_original_signal SET name_cn = '转向架1轴2抱死'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLockAxle2B1_C3'         ;
UPDATE comm_original_signal SET name_cn = '转向架1轴2抱死'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLockAxle2B1_C4'         ;
UPDATE comm_original_signal SET name_cn = '转向架2轴2抱死'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLockAxle2B2_C1'         ;
UPDATE comm_original_signal SET name_cn = '转向架2轴2抱死'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLockAxle2B2_C2'         ;
UPDATE comm_original_signal SET name_cn = '转向架2轴2抱死'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLockAxle2B2_C3'         ;
UPDATE comm_original_signal SET name_cn = '转向架2轴2抱死'                ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'BRK_xLockAxle2B2_C4'         ;
UPDATE comm_original_signal SET name_cn = '由司机警惕触发的紧急制动'             ,modify_time = '2023-07-12 12:12:12'            WHERE name_en = 'HMI_CxEBDMYR_C1'             ;
UPDATE comm_original_signal SET name_cn = '由司机警惕触发的紧急制动'             ,modify_time = '2023-07-12 12:12:12'            WHERE name_en = 'HMI_CxEBDMYR_C4'             ;
UPDATE comm_original_signal SET name_cn = '由紧急制动控制回路断路器触发的紧急制动'      ,modify_time = '2023-07-12 12:12:12'        WHERE name_en = 'BRK_xEBEBCB_C1'              ;
UPDATE comm_original_signal SET name_cn = '由紧急制动控制回路断路器触发的紧急制动'      ,modify_time = '2023-07-12 12:12:12'        WHERE name_en = 'BRK_xEBEBCB_C4'              ;
UPDATE comm_original_signal SET name_cn = '由紧急制动列车线断路器触发的紧急制动'       ,modify_time = '2023-07-12 12:12:12'        WHERE name_en = 'BRK_xEBEBTLCB_C1'            ;
UPDATE comm_original_signal SET name_cn = '由紧急制动列车线断路器触发的紧急制动'       ,modify_time = '2023-07-12 12:12:12'        WHERE name_en = 'BRK_xEBEBTLCB_C4'            ;
UPDATE comm_original_signal SET name_cn = '轨道几何主机自检故障'               ,modify_time = '2023-07-12 12:12:12'             WHERE name_en = 'HMI_CxSelfTestFlt_1'        ;
UPDATE comm_original_signal SET name_cn = '线路巡检主机自检故障'               ,modify_time = '2023-07-12 12:12:12'              WHERE name_en = 'HMI_CxSelfTestFlt_4'         ;
UPDATE comm_original_signal SET name_cn = 'LCU自检故障'                  ,modify_time = '2023-07-12 12:12:12'                 WHERE name_en = 'HMI_xLCUDrvSelfTestRes'      ;
UPDATE comm_original_signal SET name_cn = '方向向后，限速10'                ,modify_time = '2023-07-12 12:12:12'              WHERE name_en = 'PRO_x10_1'                   ;

INSERT INTO public.comm_original_signal (id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, location, subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order) VALUES ('2028272654', '88', '警惕按钮松开', 'HMI_CxEBDMS_C1', 265, 4, 'BOOLEAN1', null, 'TMc1', 'VCU', null, 'VCU_5027_62654', 1, null, null, null, '2023-07-12 14:46:00.000000', null, '2023-07-12 14:46:04.000000', '1=松开', '1', 'Integer', 2, 6);
INSERT INTO public.comm_original_signal (id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, location, subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order) VALUES ('2028272655', '88', '警惕按钮松开', 'HMI_CxEBDMS_C4', 265, 5, 'BOOLEAN1', null, 'TMc2', 'VCU', null, 'VCU_5027_62655', 1, null, null, null, '2023-07-12 14:46:02.000000', null, '2023-07-12 14:46:07.000000', '1=松开', '1', 'Integer', 2, 6);
