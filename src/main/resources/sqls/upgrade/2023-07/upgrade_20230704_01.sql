-- 创建业主字典表
create table if not exists bus_dict
(
    id               varchar(128)         not null
        primary key,
    sort_number      bigint               not null,
    label            varchar(128)         not null,
    value            varchar(32)          not null,
    description      varchar(128),
    last_modify_time timestamp(0),
    last_modify_by   varchar(128),
    create_time      timestamp(0)         not null,
    create_by        varchar(128)         not null,
    del_flag         varchar(1) default 0 not null,
    type_code        varchar(64)          not null,
    line_id          varchar(128),
    code             varchar(255)
);

comment on table bus_dict is '业务字典表';

comment on column bus_dict.id is '主键id';

comment on column bus_dict.sort_number is '排序';

comment on column bus_dict.label is '字典标签';

comment on column bus_dict.value is '字典值';

comment on column bus_dict.description is '描述';

comment on column bus_dict.last_modify_time is '最后修改时间';

comment on column bus_dict.last_modify_by is '最后修改人';

comment on column bus_dict.create_time is '创建时间';

comment on column bus_dict.create_by is '创建人';

comment on column bus_dict.del_flag is '删除标识 0-未删除  1-删除';

comment on column bus_dict.type_code is '字典类型code';

comment on column bus_dict.line_id is '线路id';

comment on column bus_dict.code is '标签编码';


-- 创建业务字典类型表
create table if not exists bus_dict_type
(
    id               varchar(128)         not null
        primary key,
    description      varchar(512),
    create_time      timestamp(6)         not null,
    create_by        varchar(128)         not null,
    last_modify_time timestamp(6),
    last_modify_by   varchar(128),
    del_flag         varchar(1) default 0 not null,
    code             varchar(64)          not null,
    name             varchar(256),
    type_level       char
);

comment on table bus_dict_type is '业务字典类型表';

comment on column bus_dict_type.description is '字典描述';

comment on column bus_dict_type.create_time is '创建时间';

comment on column bus_dict_type.create_by is '创建人';

comment on column bus_dict_type.last_modify_time is '最后修改时间';

comment on column bus_dict_type.last_modify_by is '最后修改人';

comment on column bus_dict_type.del_flag is '删除标识 1-删除 0-未删除';

comment on column bus_dict_type.code is '字典类型编码';

comment on column bus_dict_type.name is '字典类型名称';

comment on column bus_dict_type.type_level is '1-common  2-line';

