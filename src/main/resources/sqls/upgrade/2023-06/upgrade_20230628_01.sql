-- 备份表数据
DROP TABLE IF EXISTS "comm_original_signal_back_v0.05";
DROP TABLE IF EXISTS "comm_original_signal_new_back_v0.05";


CREATE TABLE if not exists "comm_original_signal_back_v0.05" AS SELECT * FROM comm_original_signal;
CREATE TABLE if not exists "comm_original_signal_new_back_v0.05" AS SELECT * FROM comm_original_signal_new;

DELETE FROM comm_original_signal_new WHERE package_order = '2' and byte_offset >= 224 and byte_offset <= 239;
DELETE FROM comm_original_signal WHERE package_order = '2' and byte_offset >= 224 and byte_offset <= 239;

update comm_original_signal set create_time = '2023-06-08 08:08:08',modify_time = '2023-06-08 08:08:08' where create_time isnull;
update comm_original_signal_new set create_time = '2023-06-08 08:08:08',modify_time = '2023-06-08 08:08:08' where create_time isnull;
