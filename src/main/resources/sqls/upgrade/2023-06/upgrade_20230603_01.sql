-- 修改数据字典车厢
UPDATE sys_dict SET code = 'TMc1', value = 'TMc1', modify_time = now() WHERE id = '291';
UPDATE sys_dict SET code = 'Mp1', value = 'Mp1', modify_time = now() WHERE id = '292';
UPDATE sys_dict SET code = 'Mp2', value = 'Mp2', modify_time = now() WHERE id = '293';
UPDATE sys_dict SET code = 'TMc2', value = 'TMc2', modify_time = now() WHERE id = '294';

-- 修改信号点表中的车厢
UPDATE comm_original_signal SET location = 'TMc1' WHERE location = 'A1';
UPDATE comm_original_signal SET location = 'TMc2' WHERE location = 'A2';
UPDATE comm_original_signal SET location = 'Mp1' WHERE location = 'B1';
UPDATE comm_original_signal SET location = 'Mp2' WHERE location = 'B2';

UPDATE comm_original_signal_new SET location = 'TMc1' WHERE location = 'A1';
UPDATE comm_original_signal_new SET location = 'TMc2' WHERE location = 'A2';
UPDATE comm_original_signal_new SET location = 'Mp1' WHERE location = 'B1';
UPDATE comm_original_signal_new SET location = 'Mp2' WHERE location = 'B2';

UPDATE ekb_fault_type SET location = 'TMc1' WHERE location = 'A1';
UPDATE ekb_fault_type SET location = 'TMc2' WHERE location = 'A2';
UPDATE ekb_fault_type SET location = 'Mp1' WHERE location = 'B1';
UPDATE ekb_fault_type SET location = 'Mp2' WHERE location = 'B2';

UPDATE monitor_slot SET location = 'TMc1' WHERE location = 'A1';
UPDATE monitor_slot SET location = 'TMc2' WHERE location = 'A2';
UPDATE monitor_slot SET location = 'Mp1' WHERE location = 'B1';
UPDATE monitor_slot SET location = 'Mp2' WHERE location = 'B2';

UPDATE monitor_slot SET location = 'TMc1' WHERE location = 'C1';
UPDATE monitor_slot SET location = 'TMc2' WHERE location = 'C4';
UPDATE monitor_slot SET location = 'Mp1' WHERE location = 'C2';
UPDATE monitor_slot SET location = 'Mp2' WHERE location = 'C3';

UPDATE monitor_table_format SET vehicle_location = 'TMc1' WHERE vehicle_location = 'A1';
UPDATE monitor_table_format SET vehicle_location = 'TMc2' WHERE vehicle_location = 'A2';
UPDATE monitor_table_format SET vehicle_location = 'Mp1' WHERE vehicle_location = 'B1';
UPDATE monitor_table_format SET vehicle_location = 'Mp2' WHERE vehicle_location = 'B2';

UPDATE monitor_table_format SET vehicle_location = 'TMc1' WHERE vehicle_location = 'C1';
UPDATE monitor_table_format SET vehicle_location = 'TMc2' WHERE vehicle_location = 'C4';
UPDATE monitor_table_format SET vehicle_location = 'Mp1' WHERE vehicle_location = 'C2';
UPDATE monitor_table_format SET vehicle_location = 'Mp2' WHERE vehicle_location = 'C3';

UPDATE mtc_alarm_warning SET location = 'TMc1' WHERE location = 'A1';
UPDATE mtc_alarm_warning SET location = 'TMc2' WHERE location = 'A2';
UPDATE mtc_alarm_warning SET location = 'Mp1' WHERE location = 'B1';
UPDATE mtc_alarm_warning SET location = 'Mp2' WHERE location = 'B2';

UPDATE mtr_software SET location = 'TMc1' WHERE location = 'A1';
UPDATE mtr_software SET location = 'TMc2' WHERE location = 'A2';
UPDATE mtr_software SET location = 'Mp1' WHERE location = 'B1';
UPDATE mtr_software SET location = 'Mp2' WHERE location = 'B2';

UPDATE mtr_software SET location = 'TMc1' WHERE location = 'A1';
UPDATE mtr_software SET location = 'TMc2' WHERE location = 'A2';
UPDATE mtr_software SET location = 'Mp1' WHERE location = 'B1';
UPDATE mtr_software SET location = 'Mp2' WHERE location = 'B2';

UPDATE mtr_software_manual_history SET location = 'TMc1' WHERE location = 'A1';
UPDATE mtr_software_manual_history SET location = 'TMc2' WHERE location = 'A2';
UPDATE mtr_software_manual_history SET location = 'Mp1' WHERE location = 'B1';
UPDATE mtr_software_manual_history SET location = 'Mp2' WHERE location = 'B2';

UPDATE mtr_software_mapping SET location = 'TMc1' WHERE location = 'A1';
UPDATE mtr_software_mapping SET location = 'TMc2' WHERE location = 'A2';
UPDATE mtr_software_mapping SET location = 'Mp1' WHERE location = 'B1';
UPDATE mtr_software_mapping SET location = 'Mp2' WHERE location = 'B2';

UPDATE stru_vehicle_type_structure SET structure_code = 'Metro/TMc1', name_cn = 'TMc1', name_en = 'TMc1', structure_position = 'Metro/TMc1' WHERE id = '1';
UPDATE stru_vehicle_type_structure SET structure_code = 'Metro/Mp1', name_cn = 'Mp1', name_en = 'Mp1', structure_position = 'Metro/Mp1' WHERE id = '2';
UPDATE stru_vehicle_type_structure SET structure_code = 'Metro/Mp2', name_cn = 'Mp2', name_en = 'Mp2', structure_position = 'Metro/Mp2' WHERE id = '3';
UPDATE stru_vehicle_type_structure SET structure_code = 'Metro/TMc2', name_cn = 'TMc2', name_en = 'TMc2', structure_position = 'Metro/TMc2' WHERE id = '4';
-- 修改车辆监控菜单文字 防止串行
UPDATE monitor_menu SET name = '蓄电池检测', modify_time = now() WHERE name = '蓄电池检测系统';
UPDATE monitor_menu SET name = '障碍物检测', modify_time = now() WHERE name = '障碍物检测系统';
