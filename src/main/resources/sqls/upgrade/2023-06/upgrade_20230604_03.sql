-- 处理信号中信号名称有？的问题
UPDATE comm_original_signal SET name_en = 'DRV_xDCCB_4', modify_time = now() WHERE id = '2028210601';
UPDATE comm_original_signal_new SET name_en = 'DRV_xDCCB_4', target_name = 'DRV_xDCCB_4', modify_time = now() WHERE id = '2028210601';

-- V0.04版本遗漏信号
DELETE FROM comm_original_signal_new WHERE id IN('2028155166','2028155167');

INSERT INTO public.comm_original_signal_new (id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type,
                                             unit, location, subsystem, parse_script, create_by, create_time, modify_by,
                                             modify_time, remark, result_type, target_id, target_name, port_no,
                                             "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES ('2028155166', '88', '蓄电池主接触器断开', 'BMS_xKM1Open_1', 516, 6, 'BOOLEAN1', null, 'TMc1',
        'VCU', null, null, now(), null, now(), '1=断开', 'Integer', '2028155166', 'BMS_xKM1Open_1', '08F',
        null, null, '2', 2, 6);

INSERT INTO public.comm_original_signal_new (id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type,
                                             unit, location, subsystem, parse_script, create_by, create_time, modify_by,
                                             modify_time, remark, result_type, target_id, target_name, port_no,
                                             "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES ('2028155167', '88', '蓄电池主接触器断开', 'BMS_xKM1Open_4', 516, 7, 'BOOLEAN1', null, 'TMc2',
        'VCU', null, null, now(), null, now(), '1=断开', 'Integer', '2028155167', 'BMS_xKM1Open_4', '08F',
        null, null, '2', 2, 6);


DELETE FROM comm_original_signal WHERE id IN('2028155166','2028155167');

INSERT INTO public.comm_original_signal (id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit,
                                         location, subsystem, parse_script, fault_type_key, trigger_value, max_value,
                                         min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag,
                                         result_type, frames_type, package_order)
VALUES ('2028155166', '88', '蓄电池主接触器断开', 'BMS_xKM1Open_1', 516, 6, 'BOOLEAN1', '', 'TMc1', 'VCU', '', null, 1,
        null, null, '', now(), '', now(), '1=断开', '1', 'Integer', 2, 6);

INSERT INTO public.comm_original_signal (id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit,
                                         location, subsystem, parse_script, fault_type_key, trigger_value, max_value,
                                         min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag,
                                         result_type, frames_type, package_order)
VALUES ('2028155167', '88', '蓄电池主接触器断开', 'BMS_xKM1Open_4', 516, 7, 'BOOLEAN1', '', 'TMc2', 'VCU', '', null, 1,
        null, null, '', now(), '', now(), '1=断开', '1', 'Integer', 2, 6);