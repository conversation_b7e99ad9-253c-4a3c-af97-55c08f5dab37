-- 修改4车厢构型名称
UPDATE stru_vehicle_structure_component SET structure_code = 'Metro/TMc2', name_en = 'TMc2',name_cn = 'TMc2',
short_name_en = 'TMc2',structure_position = 'Metro/TMc2',modify_time = now()
WHERE id = '4';

-- 插入车辆构型数据

DELETE FROM stru_vehicle_structure_component WHERE vehicle_id in ('02002','02003','02004','02005','02006','02007','02008','02009','02010','02011');
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('6', null, '02002', 'Metro/TMc1', null, null, 0, 'TMc1', 'TMc1', 'TMc1', null, 'Metro', 'Metro/TMc1', '车厢', 2, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('7', null, '02002', 'Metro', null, null, 0, '绍兴2号线车型', 'Metro', 'Metro', null, 'root', 'Metro', '车型', 1, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('8', null, '02002', 'Metro/Mp1', null, null, 0, 'Mp1', 'Mp1', 'Mp1', null, 'Metro', 'Metro/Mp1', '车厢', 3, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('9', null, '02002', 'Metro/Mp2', null, null, 0, 'Mp2', 'Mp2', 'Mp2', null, 'Metro', 'Metro/Mp2', '车厢', 4, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('10', null, '02002', 'Metro/TMc2', null, null, 0, 'TMc2', 'TMc2', 'TMc2', null, 'Metro', 'Metro/TMc2', '车厢', 5, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);

INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('11', null, '02003', 'Metro/TMc1', null, null, 0, 'TMc1', 'TMc1', 'TMc1', null, 'Metro', 'Metro/TMc1', '车厢', 2, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('12', null, '02003', 'Metro', null, null, 0, '绍兴2号线车型', 'Metro', 'Metro', null, 'root', 'Metro', '车型', 1, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('13', null, '02003', 'Metro/Mp1', null, null, 0, 'Mp1', 'Mp1', 'Mp1', null, 'Metro', 'Metro/Mp1', '车厢', 3, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('14', null, '02003', 'Metro/Mp2', null, null, 0, 'Mp2', 'Mp2', 'Mp2', null, 'Metro', 'Metro/Mp2', '车厢', 4, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('15', null, '02003', 'Metro/TMc2', null, null, 0, 'TMc2', 'TMc2', 'TMc2', null, 'Metro', 'Metro/TMc2', '车厢', 5, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);

INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('16', null, '02004', 'Metro/TMc1', null, null, 0, 'TMc1', 'TMc1', 'TMc1', null, 'Metro', 'Metro/TMc1', '车厢', 2, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('17', null, '02004', 'Metro', null, null, 0, '绍兴2号线车型', 'Metro', 'Metro', null, 'root', 'Metro', '车型', 1, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('18', null, '02004', 'Metro/Mp1', null, null, 0, 'Mp1', 'Mp1', 'Mp1', null, 'Metro', 'Metro/Mp1', '车厢', 3, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('19', null, '02004', 'Metro/Mp2', null, null, 0, 'Mp2', 'Mp2', 'Mp2', null, 'Metro', 'Metro/Mp2', '车厢', 4, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('20', null, '02004', 'Metro/TMc2', null, null, 0, 'TMc2', 'TMc2', 'TMc2', null, 'Metro', 'Metro/TMc2', '车厢', 5, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);

INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('21', null, '02005', 'Metro/TMc1', null, null, 0, 'TMc1', 'TMc1', 'TMc1', null, 'Metro', 'Metro/TMc1', '车厢', 2, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('22', null, '02005', 'Metro', null, null, 0, '绍兴2号线车型', 'Metro', 'Metro', null, 'root', 'Metro', '车型', 1, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('23', null, '02005', 'Metro/Mp1', null, null, 0, 'Mp1', 'Mp1', 'Mp1', null, 'Metro', 'Metro/Mp1', '车厢', 3, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('24', null, '02005', 'Metro/Mp2', null, null, 0, 'Mp2', 'Mp2', 'Mp2', null, 'Metro', 'Metro/Mp2', '车厢', 4, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('25', null, '02005', 'Metro/TMc2', null, null, 0, 'TMc2', 'TMc2', 'TMc2', null, 'Metro', 'Metro/TMc2', '车厢', 5, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);

INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('26', null, '02006', 'Metro/TMc1', null, null, 0, 'TMc1', 'TMc1', 'TMc1', null, 'Metro', 'Metro/TMc1', '车厢', 2, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('27', null, '02006', 'Metro', null, null, 0, '绍兴2号线车型', 'Metro', 'Metro', null, 'root', 'Metro', '车型', 1, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('28', null, '02006', 'Metro/Mp1', null, null, 0, 'Mp1', 'Mp1', 'Mp1', null, 'Metro', 'Metro/Mp1', '车厢', 3, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('29', null, '02006', 'Metro/Mp2', null, null, 0, 'Mp2', 'Mp2', 'Mp2', null, 'Metro', 'Metro/Mp2', '车厢', 4, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('30', null, '02006', 'Metro/TMc2', null, null, 0, 'TMc2', 'TMc2', 'TMc2', null, 'Metro', 'Metro/TMc2', '车厢', 5, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);

INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('31', null, '02007', 'Metro/TMc1', null, null, 0, 'TMc1', 'TMc1', 'TMc1', null, 'Metro', 'Metro/TMc1', '车厢', 2, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('32', null, '02007', 'Metro', null, null, 0, '绍兴2号线车型', 'Metro', 'Metro', null, 'root', 'Metro', '车型', 1, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('33', null, '02007', 'Metro/Mp1', null, null, 0, 'Mp1', 'Mp1', 'Mp1', null, 'Metro', 'Metro/Mp1', '车厢', 3, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('34', null, '02007', 'Metro/Mp2', null, null, 0, 'Mp2', 'Mp2', 'Mp2', null, 'Metro', 'Metro/Mp2', '车厢', 4, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('35', null, '02007', 'Metro/TMc2', null, null, 0, 'TMc2', 'TMc2', 'TMc2', null, 'Metro', 'Metro/TMc2', '车厢', 5, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);

INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('36', null, '02008', 'Metro/TMc1', null, null, 0, 'TMc1', 'TMc1', 'TMc1', null, 'Metro', 'Metro/TMc1', '车厢', 2, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('37', null, '02008', 'Metro', null, null, 0, '绍兴2号线车型', 'Metro', 'Metro', null, 'root', 'Metro', '车型', 1, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('38', null, '02008', 'Metro/Mp1', null, null, 0, 'Mp1', 'Mp1', 'Mp1', null, 'Metro', 'Metro/Mp1', '车厢', 3, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('39', null, '02008', 'Metro/Mp2', null, null, 0, 'Mp2', 'Mp2', 'Mp2', null, 'Metro', 'Metro/Mp2', '车厢', 4, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('40', null, '02008', 'Metro/TMc2', null, null, 0, 'TMc2', 'TMc2', 'TMc2', null, 'Metro', 'Metro/TMc2', '车厢', 5, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);

INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('41', null, '02009', 'Metro/TMc1', null, null, 0, 'TMc1', 'TMc1', 'TMc1', null, 'Metro', 'Metro/TMc1', '车厢', 2, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('42', null, '02009', 'Metro', null, null, 0, '绍兴2号线车型', 'Metro', 'Metro', null, 'root', 'Metro', '车型', 1, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('43', null, '02009', 'Metro/Mp1', null, null, 0, 'Mp1', 'Mp1', 'Mp1', null, 'Metro', 'Metro/Mp1', '车厢', 3, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('44', null, '02009', 'Metro/Mp2', null, null, 0, 'Mp2', 'Mp2', 'Mp2', null, 'Metro', 'Metro/Mp2', '车厢', 4, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('45', null, '02009', 'Metro/TMc2', null, null, 0, 'TMc2', 'TMc2', 'TMc2', null, 'Metro', 'Metro/TMc2', '车厢', 5, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);

INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('46', null, '02010', 'Metro/TMc1', null, null, 0, 'TMc1', 'TMc1', 'TMc1', null, 'Metro', 'Metro/TMc1', '车厢', 2, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('47', null, '02010', 'Metro', null, null, 0, '绍兴2号线车型', 'Metro', 'Metro', null, 'root', 'Metro', '车型', 1, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('48', null, '02010', 'Metro/Mp1', null, null, 0, 'Mp1', 'Mp1', 'Mp1', null, 'Metro', 'Metro/Mp1', '车厢', 3, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('49', null, '02010', 'Metro/Mp2', null, null, 0, 'Mp2', 'Mp2', 'Mp2', null, 'Metro', 'Metro/Mp2', '车厢', 4, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('50', null, '02010', 'Metro/TMc2', null, null, 0, 'TMc2', 'TMc2', 'TMc2', null, 'Metro', 'Metro/TMc2', '车厢', 5, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);

INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('51', null, '02011', 'Metro/TMc1', null, null, 0, 'TMc1', 'TMc1', 'TMc1', null, 'Metro', 'Metro/TMc1', '车厢', 2, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('52', null, '02011', 'Metro', null, null, 0, '绍兴2号线车型', 'Metro', 'Metro', null, 'root', 'Metro', '车型', 1, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('53', null, '02011', 'Metro/Mp1', null, null, 0, 'Mp1', 'Mp1', 'Mp1', null, 'Metro', 'Metro/Mp1', '车厢', 3, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('54', null, '02011', 'Metro/Mp2', null, null, 0, 'Mp2', 'Mp2', 'Mp2', null, 'Metro', 'Metro/Mp2', '车厢', 4, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('55', null, '02011', 'Metro/TMc2', null, null, 0, 'TMc2', 'TMc2', 'TMc2', null, 'Metro', 'Metro/TMc2', '车厢', 5, 0, '2022-05-13 09:53:14', null, '0', now(), '0', now(), null);

-- 信号解析脚本修改 大表10个信号解析脚本不对 x.y.z格式的
UPDATE comm_original_signal_new SET parse_script = 'result=String.format("%d.%d.%d",(value>>12) & 0x000F,(value>>8) & 0x000F,value & 0x00FF);'
WHERE id IN('2028293200','2028293280', '2028293320', '2028134480', '2028293220', '2028293300', '2028293340', '2028134500', '2028293160' ,'2028293180');

