-- 修改大表中的target_id和target_name与小表不一致的问题

UPDATE comm_original_signal_new SET target_name = 'PRO_x80_4', modify_time = now() WHERE id = '2029277722';

UPDATE comm_original_signal_new SET target_id = '2429266667', modify_time = now() WHERE id = '2429266667';
UPDATE comm_original_signal_new SET target_id = '2429266687', modify_time = now() WHERE id = '2429266687';
UPDATE comm_original_signal_new SET target_id = '2429266697', modify_time = now() WHERE id = '2429266697';

