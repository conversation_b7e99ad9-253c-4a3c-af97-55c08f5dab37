-- 在车门上添加PECU激活图标 触发值写-1 排序最小
DELETE FROM monitor_trigger WHERE id = 'cfc2659dc55842149de3b7230277a6cf';
INSERT INTO public.monitor_trigger (id, slot_id, label, sort, signal_id, trigger_value, data_display_point, unit_status, svg_url, ext_properties, create_time, create_by, modify_time, modify_by, del_flag, image_type, image_path, signal_name_en, mark) VALUES ('cfc2659dc55842149de3b7230277a6cf', '1f929718f0ce44809b039d03f7af4ed6', 'PECU激活', 1, '2029246570', '-1', '3', null, '2_PECU_OnG', null, '2023-06-05 20:11:19', null, '2023-06-05 20:11:19', null, false, 'gif', 'shaoxing2', 'DOR_usDor2Stat_1', null);

UPDATE monitor_trigger SET sort = 10, modify_time = now() WHERE id = 'ae1628c2d8ac4b71b0023a60d4f2bc58';