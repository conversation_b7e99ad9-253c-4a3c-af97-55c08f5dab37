-- 修改数据字典车厢

DELETE FROM stru_vehicle_structure_component WHERE id in ('A1', '1', 'B1', 'A2', 'B2');
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('2', null, '02001', 'Metro/TMc1', null, null, 0, 'TMc1', 'TMc1', 'TMc1', null, 'Metro', 'Metro/TMc1', '车厢', 2, 0, '2022-05-13 09:53:14', null, '0', '2022-05-13 09:53:14', '0', '2022-05-13 09:53:14', null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('1', null, '02001', 'Metro', null, null, 0, '绍兴2号线车型', 'Metro', 'Metro', null, 'root', 'Metro', '车型', 1, 0, '2022-05-13 09:53:14', null, '0', '2022-05-13 09:53:14', '0', '2022-05-13 09:53:14', null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('3', null, '02001', 'Metro/Mp1', null, null, 0, 'Mp1', 'Mp1', 'Mp1', null, 'Metro', 'Metro/Mp1', '车厢', 3, 0, '2022-05-13 09:53:14', null, '0', '2022-05-13 09:53:14', '0', '2022-05-13 09:53:14', null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('4', null, '02001', 'Metro/TMc1', null, null, 0, 'TMc1', 'TMc1', 'TMc1', null, 'Metro', 'Metro/TMc1', '车厢', 5, 0, '2022-05-13 09:53:14', null, '0', '2022-05-13 09:53:14', '0', '2022-05-13 09:53:14', null);
INSERT INTO public.stru_vehicle_structure_component (id, component_id, vehicle_id, structure_code, assembly_time, disassembly_time, valid, name_cn, name_en, short_name_en, component_type_id, parent_structure_code, structure_position, structure_type, sort_number, del_flag, start_date, end_date, create_by, create_time, modify_by, modify_time, remark) VALUES ('5', null, '02001', 'Metro/Mp2', null, null, 0, 'Mp2', 'Mp2', 'Mp2', null, 'Metro', 'Metro/Mp2', '车厢', 4, 0, '2022-05-13 09:53:14', null, '0', '2022-05-13 09:53:14', '0', '2022-05-13 09:53:14', null);
