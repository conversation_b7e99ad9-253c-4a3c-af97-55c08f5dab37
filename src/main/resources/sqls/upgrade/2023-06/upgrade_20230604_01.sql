-- 信号大表添加信号数据

DELETE FROM comm_original_signal_new WHERE id IN('2028210716','2028210717','2028272632','2028272633');

INSERT INTO public.comm_original_signal_new (id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type,
                                             unit, location, subsystem, parse_script, create_by, create_time, modify_by,
                                             modify_time, remark, result_type, target_id, target_name, port_no,
                                             "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES ('2028210716', '88', '列车供电接触器未激活', 'DRV_xTSKNOAct_1', 71, 6, 'BOOLEAN1', null, 'TMc1',
        'VCU', null, null, now(), null, now(), '1=未激活', 'Integer', '2028210716', 'DRV_xTSKNOAct_1', '81',
        null, null, '2', 2, 6);

INSERT INTO public.comm_original_signal_new (id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type,
                                             unit, location, subsystem, parse_script, create_by, create_time, modify_by,
                                             modify_time, remark, result_type, target_id, target_name, port_no,
                                             "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES ('2028210717', '88', '列车供电接触器未激活', 'DRV_xTSKNOAct_4', 71, 7, 'BOOLEAN1', null, 'TMc2',
        'VCU', null, null, now(), null, now(), '1=未激活', 'Integer', '2028210717', 'DRV_xTSKNOAct_4', '81',
        null, null, '2', 2, 6);

INSERT INTO public.comm_original_signal_new (id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type,
                                             unit, location, subsystem, parse_script, create_by, create_time, modify_by,
                                             modify_time, remark, result_type, target_id, target_name, port_no,
                                             "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES ('2028272632', '88', 'LCU输出所有停放制动缓解状态异常', 'HMI_CxAPBRRFlt_C1', 263, 2, 'BOOLEAN1', null, 'TMc1',
        'VCU', null, null, now(), null, now(), '1=异常', 'Integer', '2028272632', 'HMI_CxAPBRRFlt_C1', '87',
        null, null, '2', 2, 6);

INSERT INTO public.comm_original_signal_new (id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type,
                                             unit, location, subsystem, parse_script, create_by, create_time, modify_by,
                                             modify_time, remark, result_type, target_id, target_name, port_no,
                                             "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES ('2028272633', '88', 'LCU输出所有停放制动缓解状态异常', 'HMI_CxAPBRRFlt_C4', 263, 3, 'BOOLEAN1', null, 'TMc2',
        'VCU', null, null, now(), null, now(), '1=异常', 'Integer', '2028272633', 'HMI_CxAPBRRFlt_C4', '87',
        null, null, '2', 2, 6);

-- 信号小表添加信号数据
DELETE FROM comm_original_signal WHERE id IN('2028210716','2028210717','2028272632','2028272633');

INSERT INTO public.comm_original_signal (id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit,
                                         location, subsystem, parse_script, fault_type_key, trigger_value, max_value,
                                         min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag,
                                         result_type, frames_type, package_order)
VALUES ('2028210716', '88', '列车供电接触器未激活', 'DRV_xTSKNOAct_1', 71, 6, 'BOOLEAN1', '', 'TMc1', 'VCU', '', null, 1,
        null, null, '', now(), '', now(), '1=未激活', '1', 'Integer', 2, 6);

INSERT INTO public.comm_original_signal (id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit,
                                         location, subsystem, parse_script, fault_type_key, trigger_value, max_value,
                                         min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag,
                                         result_type, frames_type, package_order)
VALUES ('2028210717', '88', '列车供电接触器未激活', 'DRV_xTSKNOAct_4', 71, 7, 'BOOLEAN1', '', 'TMc2', 'VCU', '', null, 1,
        null, null, '', now(), '', now(), '1=未激活', '1', 'Integer', 2, 6);

INSERT INTO public.comm_original_signal (id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit,
                                         location, subsystem, parse_script, fault_type_key, trigger_value, max_value,
                                         min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag,
                                         result_type, frames_type, package_order)
VALUES ('2028272632', '88', 'LCU输出所有停放制动缓解状态异常', 'HMI_CxAPBRRFlt_C1', 263, 2, 'BOOLEAN1', '', 'TMc1', 'VCU', '', null, 1,
        null, null, '', now(), '', now(), '1=未激活', '1', 'Integer', 2, 6);

INSERT INTO public.comm_original_signal (id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit,
                                         location, subsystem, parse_script, fault_type_key, trigger_value, max_value,
                                         min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag,
                                         result_type, frames_type, package_order)
VALUES ('2028272633', '88', 'LCU输出所有停放制动缓解状态异常', 'HMI_CxAPBRRFlt_C4', 263, 3, 'BOOLEAN1', '', 'TMc2', 'VCU', '', null, 1,
        null, null, '', now(), '', now(), '1=未激活', '1', 'Integer', 2, 6);