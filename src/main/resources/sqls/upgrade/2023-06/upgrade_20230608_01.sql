-- 脚本处理
UPDATE monitor_signal_function SET name = '仪表盘-蓄电池电压', modify_time = now() WHERE id = 'func2';


DELETE FROM monitor_signal_function WHERE id = 'func9';
INSERT INTO public.monitor_signal_function (id, type, name, function_str, create_time, create_by, modify_time, modify_by, del_flag, description, function_name, redis_key) VALUES ('func9', 'mainResPress', '仪表盘-主风压力和制动缸压力脚本', e'function mainResPress(){
    var item = {};
    var formats = new Array();
    var triggers = arguments[2];
    var signals = arguments[0].signals;
    var formatu1 = {};
	var formatu2 = {};
	if(signals != null || signals != undefined){
		// 司机室激活端信号：
		var VCU_CxCabAct1 = signals[\'VCU_CxCabAct1\']
		var VCU_CxCabAct4 = signals[\'VCU_CxCabAct4\']
		if (VCU_CxCabAct4 == \'1\' && VCU_CxCabAct1 != \'1\') {
			formatu1[\'vehicleLocation\'] = \'TMc2\';
			formatu2[\'vehicleLocation\'] = \'TMc2\';
			var mainResPress = parseFloat(signals[\'HMI_CusMainResPress_U2\']);
			var cusSBPressB1 = parseFloat(signals[\'HMI_CusSBPressB1_C4\']);
		}else{
			formatu1[\'vehicleLocation\'] = \'TMc1\';
			formatu2[\'vehicleLocation\'] = \'TMc1\';
			var mainResPress = parseFloat(signals[\'HMI_CusMainResPress_U1\']);
			var cusSBPressB1 = parseFloat(signals[\'HMI_CusSBPressB1_C1\']);
		}
	}
	formatu1[\'value\'] = mainResPress;
	formatu2[\'value\'] = cusSBPressB1;
    formats.push(formatu1);
    formats.push(formatu2);
    item[\'slotValue\'] = formats;
    return JSON.stringify(item);
}', '2023-06-08 09:08:46', '0', '2023-06-08 09:08:46', '0', false, '仪表盘-主风压力和制动缸压力脚本', 'mainResPress', 'trainCode_shadow');





