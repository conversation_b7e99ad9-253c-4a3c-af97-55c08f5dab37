-- 统计表dw_realtime_train_status_by_day添加创建时间和修改时间 用于记录数据落库时间便于查看
alter table if exists dw_realtime_train_status_by_day
    add if not exists create_time timestamp;

alter table if exists dw_realtime_train_status_by_day
    add if not exists modify_time timestamp;

comment on column dw_realtime_train_status_by_day.create_time is '创建时间';
comment on column dw_realtime_train_status_by_day.modify_time is '修改时间';



