-- select count(*) from comm_original_signal_new where package_order = '4' and byte_offset >= 667 and byte_offset <= 671;
delete from comm_original_signal_new where package_order = '4' and byte_offset >= 667 and byte_offset <= 671;
delete from comm_original_signal where package_order = '4' and byte_offset >= 667 and byte_offset <= 671;

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266667', '88', '驱动左侧开门列车线', 'LCU4_IxDOCL', 666, 7, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=左侧开门列车线被激活
0=左侧开门列车线未被激活', 'Integer', '2429266677', 'LCU4_IxDOCL', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266667', '88', '驱动左侧开门列车线', 'LCU4_IxDOCL', 666, 7, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=左侧开门列车线被激活
0=左侧开门列车线未被激活', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266670', '88', '驱动右侧开门列车线', 'LCU4_IxDOCR', 667, 0, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=右侧开门列车线被激活
0=右侧开门列车线未被激活', 'Integer', '2429266670', 'LCU4_IxDOCR', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266670', '88', '驱动右侧开门列车线', 'LCU4_IxDOCR', 667, 0, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=右侧开门列车线被激活
0=右侧开门列车线未被激活', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266671', '88', '驱动左侧关门列车线', 'LCU4_IxDCCL', 667, 1, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=左侧关门列车线被激活
0=左侧关门列车线未被激活', 'Integer', '2429266671', 'LCU4_IxDCCL', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266671', '88', '驱动左侧关门列车线', 'LCU4_IxDCCL', 667, 1, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=左侧关门列车线被激活
0=左侧关门列车线未被激活', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266672', '88', '驱动右侧关门列车线', 'LCU4_IxDCCR', 667, 2, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=右侧关门列车线被激活
0=右侧关门列车线未被激活', 'Integer', '2429266672', 'LCU4_IxDCCR', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266672', '88', '驱动右侧关门列车线', 'LCU4_IxDCCR', 667, 2, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=右侧关门列车线被激活
0=右侧关门列车线未被激活', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266673', '88', '所有门关好信号', 'LCU4_IxDIR', 667, 3, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=所有门关好
0=有车门未关好', 'Integer', '2429266673', 'LCU4_IxDIR', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266673', '88', '所有门关好信号', 'LCU4_IxDIR', 667, 3, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=所有门关好
0=有车门未关好', '1', 'Integer', 2, 4);


INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266676', '88', '所有气制动缓解', 'LCU4_IxABRR1', 667, 6, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=所有气制动缓解
0=有气制动未缓解', 'Integer', '2429266676', 'LCU4_IxABRR1', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266676', '88', '所有气制动缓解', 'LCU4_IxABRR1', 667, 6, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=所有气制动缓解
0=有气制动未缓解', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266677', '88', '所有停放制动缓解', 'LCU4_IxAPBRR', 667, 7, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=所有停放制动缓解
0=有停放制动未缓解', 'Integer', '2429266677', 'LCU4_IxAPBRR', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266677', '88', '所有停放制动缓解', 'LCU4_IxAPBRR', 667, 7, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=所有停放制动缓解
0=有停放制动未缓解', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266680', '88', '零速信号', 'LCU4_IxZVR', 668, 0, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=零速
0=非零速', 'Integer', '2429266680', 'LCU4_IxZVR', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266680', '88', '零速信号', 'LCU4_IxZVR', 668, 0, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=零速
0=非零速', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266683', '88', '驱动正常照明列车线', 'LCU4_IxNLCR', 668, 3, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=正常照明列车线被激活
0=正常照明列车线未被激活', 'Integer', '2429266683', 'LCU4_IxNLCR', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266683', '88', '驱动正常照明列车线', 'LCU4_IxNLCR', 668, 3, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=正常照明列车线被激活
0=正常照明列车线未被激活', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266684', '88', '驱动紧急照明列车线', 'LCU4_IxELCR', 668, 4, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=紧急照明列车线被激活
0=紧急照明列车线未被激活', 'Integer', '2429266684', 'LCU4_IxELCR', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266684', '88', '驱动紧急照明列车线', 'LCU4_IxELCR', 668, 4, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=紧急照明列车线被激活
0=紧急照明列车线未被激活', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266685', '88', '驱动整车照明关断列车线', 'LCU4_IxPLCR', 668, 5, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=整车照明关断列车线被激活
0=整车照明关断列车线未被激活', 'Integer', '2429266685', 'LCU4_IxPLCR', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266685', '88', '驱动整车照明关断列车线', 'LCU4_IxPLCR', 668, 5, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=整车照明关断列车线被激活
0=整车照明关断列车线未被激活', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266686', '88', '驱动HLK接触器', 'LCU4_IxHLK', 668, 6, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=驱动HLK1/2接触器
0=不驱动HLK1/2接触器', 'Integer', '2429266686', 'LCU4_IxHLK', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266686', '88', '驱动HLK接触器', 'LCU4_IxHLK', 668, 6, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=驱动HLK1/2接触器
0=不驱动HLK1/2接触器', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266687', '88', '驱动红色标志灯', 'LCU4_IxSL', 668, 7, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=驱动红色标志灯
0=不驱动红色标志灯', 'Integer', '2429266697', 'LCU4_IxSL', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266687', '88', '驱动红色标志灯', 'LCU4_IxSL', 668, 7, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=驱动红色标志灯
0=不驱动红色标志灯', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266690', '88', '驱动尾灯', 'LCU4_IxTLI', 669, 0, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=驱动尾灯
0=不驱动尾灯', 'Integer', '2429266690', 'LCU4_IxTLI', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266690', '88', '驱动尾灯', 'LCU4_IxTLI', 669, 0, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=驱动尾灯
0=不驱动尾灯', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266691', '88', '所有停放制动缓解指示灯', 'LCU4_IxPBRI', 669, 1, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=驱动所有停放制动缓解指示灯
0=不驱动所有停放制动缓解指示灯', 'Integer', '2429266691', 'LCU4_IxPBRI', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266691', '88', '所有停放制动缓解指示灯', 'LCU4_IxPBRI', 669, 1, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=驱动所有停放制动缓解指示灯
0=不驱动所有停放制动缓解指示灯', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266692', '88', '停放制动施加指示灯', 'LCU4_IxPBAI', 669, 2, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=驱动停放制动施加指示灯
0=不驱动停放制动施加指示灯', 'Integer', '2429266692', 'LCU4_IxPBAI', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266692', '88', '停放制动施加指示灯', 'LCU4_IxPBAI', 669, 2, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=驱动停放制动施加指示灯
0=不驱动停放制动施加指示灯', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266693', '88', '所有气制动缓解指示灯', 'LCU4_IxABRI', 669, 3, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=驱动所有制动缓解指示灯
0=不驱动所有制动缓解指示灯', 'Integer', '2429266693', 'LCU4_IxABRI', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266693', '88', '所有气制动缓解指示灯', 'LCU4_IxABRI', 669, 3, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=驱动所有制动缓解指示灯
0=不驱动所有制动缓解指示灯', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266695', '88', '关左门按钮指示灯', 'LCU4_IxDCPBLD', 669, 5, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=驱动关左门按钮指示灯
0=不驱动关左门按钮指示灯', 'Integer', '2429266695', 'LCU4_IxDCPBLD', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266695', '88', '关左门按钮指示灯', 'LCU4_IxDCPBLD', 669, 5, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=驱动关左门按钮指示灯
0=不驱动关左门按钮指示灯', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266696', '88', '关右门按钮指示灯', 'LCU4_IxDCPBRD', 669, 6, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=驱动关右门按钮指示灯
0=不驱动关右门按钮指示灯', 'Integer', '2429266696', 'LCU4_IxDCPBRD', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266696', '88', '关右门按钮指示灯', 'LCU4_IxDCPBRD', 669, 6, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=驱动关右门按钮指示灯
0=不驱动关右门按钮指示灯', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266697', '88', '解钩按钮指示灯', 'LCU4_IxUNPBD', 669, 7, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=驱动解钩按钮指示灯
0=不驱动解钩按钮指示灯', 'Integer', '2429266700', 'LCU4_IxUNPBD', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266697', '88', '解钩按钮指示灯', 'LCU4_IxUNPBD', 669, 7, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=驱动解钩按钮指示灯
0=不驱动解钩按钮指示灯', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266703', '88', '司机警惕信号', 'LCU4_IxDMYR', 670, 3, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=警惕未触发紧急制动
0=警惕触发紧急制动', 'Integer', '2429266703', 'LCU4_IxDMYR', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266703', '88', '司机警惕信号', 'LCU4_IxDMYR', 670, 3, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=警惕未触发紧急制动
0=警惕触发紧急制动', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266706', '88', '客室门（左）禁止解锁', 'LCU4_IxMDCRL', 670, 6, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=客室门（左）禁止解锁
0=客室门（左）允许解锁', 'Integer', '2429266706', 'LCU4_IxMDCRL', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266706', '88', '客室门（左）禁止解锁', 'LCU4_IxMDCRL', 670, 6, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=客室门（左）禁止解锁
0=客室门（左）允许解锁', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266707', '88', '客室门（右）禁止解锁', 'LCU4_IxMDCRR', 670, 7, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=客室门（右）禁止解锁
0=客室门（右）允许解锁', 'Integer', '2429266707', 'LCU4_IxMDCRR', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266707', '88', '客室门（右）禁止解锁', 'LCU4_IxMDCRR', 670, 7, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=客室门（右）禁止解锁
0=客室门（右）允许解锁', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266711', '88', '走行轨回流确认指令', 'LCU4_IxWTSTenable', 671, 1, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=走行轨回流确认指令
0=未发出走行轨回流确认指令', 'Integer', '2429266711', 'LCU4_IxWTSTenable', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266711', '88', '走行轨回流确认指令', 'LCU4_IxWTSTenable', 671, 1, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=走行轨回流确认指令
0=未发出走行轨回流确认指令', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266712', '88', '专用轨回流确认指令', 'LCU4_IxSTWTenable', 671, 2, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=专用轨回流确认指令
0=未发出专用轨回流确认指令', 'Integer', '2429266712', 'LCU4_IxSTWTenable', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266712', '88', '专用轨回流确认指令', 'LCU4_IxSTWTenable', 671, 2, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=专用轨回流确认指令
0=未发出专用轨回流确认指令', '1', 'Integer', 2, 4);









