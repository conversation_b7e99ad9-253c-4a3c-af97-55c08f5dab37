-- 修改信号表
INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2029277685', '88', '列车速度大于103KM/H，牵引封锁', 'PRO_xSpdOver103', 768, 5, 'BOOLEAN1', NULL, 'ALL', 'VCU', NULL, NULL, NULL, NULL, NULL, '1=激活', 'Integer', '2029277685', 'PRO_xSpdOver103', '97', NULL, NULL, NULL, 2, 6);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2029277685', '88', '列车速度大于103KM/H，牵引封锁', 'PRO_xSpdOver103', 768, 5, 'BOOLEAN1', '', 'ALL', 'VCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=激活', '1', 'Integer', 2, 6);


UPDATE public.comm_original_signal_new
SET protocol_id='88', name_cn='升单弓，限速80', name_en='PRO_x80_4', byte_offset=772, bit_offset=2, data_type='BOOLEAN1', unit=NULL, "location"='ALL', subsystem='VCU', parse_script=NULL, create_by=NULL, create_time=NULL, modify_by=NULL, modify_time=NULL, remark='1=激活', result_type='Integer', target_id='2029277722', target_name='PRO_x85_3', port_no='97', "ifRepeat"=NULL, fault_flag=NULL, signal_type=NULL, frames_type=2, package_order=6
WHERE id='2029277722';
UPDATE public.comm_original_signal
SET id='2029277722', protocol_id='88', name_cn='升单弓，限速80', name_en='PRO_x80_4', byte_offset=772, bit_offset=2, data_type='BOOLEAN1', unit='', "location"='ALL', subsystem='VCU', parse_script='', fault_type_key='BCU_0000_1623', trigger_value=1, max_value=NULL, min_value=NULL, create_by='', create_time=NULL, modify_by='', modify_time=NULL, remark='1=激活', redis_flag='1', result_type='Integer', frames_type=2, package_order=6
WHERE id='2029277722';

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266675', '88', '解钩指令', 'LCU4_IxEUMV', 667, 5, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=LCU激活解钩电磁阀
0=LCU未激活解钩电磁阀', 'Integer', '2429266675', 'LCU4_IxEUMV', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266675', '88', '解钩指令', 'LCU4_IxEUMV', 667, 5, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=LCU激活解钩电磁阀
0=LCU未激活解钩电磁阀', '1', 'Integer', 2, 4);

INSERT INTO public.comm_original_signal_new
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, create_by, create_time, modify_by, modify_time, remark, result_type, target_id, target_name, port_no, "ifRepeat", fault_flag, signal_type, frames_type, package_order)
VALUES('2429266694', '88', '所有门关好指示灯', 'LCU4_IxDCLI', 669, 4, 'BOOLEAN1', NULL, 'A2', 'LCU', NULL, NULL, NULL, NULL, NULL, '1=驱动所有门关好指示灯
0=不驱动所有门关好指示灯', 'Integer', '2429266694', 'LCU4_IxDCLI', '496', NULL, NULL, NULL, 2, 4);
INSERT INTO public.comm_original_signal
(id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, "location", subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time, modify_by, modify_time, remark, redis_flag, result_type, frames_type, package_order)
VALUES('2429266694', '88', '所有门关好指示灯', 'LCU4_IxDCLI', 669, 4, 'BOOLEAN1', '', 'A2', 'LCU', '', NULL, NULL, NULL, NULL, '', NULL, '', NULL, '1=驱动所有门关好指示灯
0=不驱动所有门关好指示灯', '1', 'Integer', 2, 4);


-- 修改字典表
INSERT INTO public.ekb_fault_type (id, vehicle_type_id, fault_type_key, vehicle_structure_code, name_cn, name_en,
                                   fault_code, fault_level, subsystem, del_flag, create_by, create_time, modify_by,
                                   modify_time, description, location, enable, fault_mode, frontline_disposal_recommendations,
                                   overhaul_suggestions, fault_reason, model_code, line_id, fault_category) VALUES
('1840', '129', 'BCU_1018_1653', null, '列车速度大于103KM/H，牵引封锁', 'PRO_xSpdOver103', '1018', 0, 'BCU', 0, '0', null, '0', null, '1=激活', 'ALL', false, '0', null, null, null, null, '24', 1);


UPDATE public.ekb_fault_type SET name_cn = '升单弓，限速80', name_en = 'PRO_x80_4',location = 'A2', overhaul_suggestions = '升单弓，限速80'
                             WHERE fault_type_key = 'BCU_0000_1623';