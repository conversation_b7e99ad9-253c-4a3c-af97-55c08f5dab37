-- 创建轨旁监测列车过车信息表
create table if not exists public.train_pass_info
(
    id varchar(128) NOT NULL,
	vehicle_code varchar(128) NOT NULL,
	trace_time timestamp NOT NULL,
	trace_file varchar(255) NOT NULL,
	station_code int4 NOT NULL,
	direction int2 NOT NULL,
	CONSTRAINT train_pass_info_pk PRIMARY KEY (id)
);

COMMENT ON COLUMN public.train_pass_info.vehicle_code IS '列车号';
COMMENT ON COLUMN public.train_pass_info.trace_time IS '过车时间';
COMMENT ON COLUMN public.train_pass_info.trace_file IS '过车图片或者视频目录';
COMMENT ON COLUMN public.train_pass_info.station_code IS '站点编码';
COMMENT ON COLUMN public.train_pass_info.direction IS '行车方向，0：正向，1：反向';


-- 创建轨旁监测列车检出数据表
create table if not exists public.check_out_data
(
    id varchar(128) NOT NULL,
	train_pass_info_id varchar(128) NOT NULL,
	point_type_code int4 NOT NULL,
	part_code int4 NOT NULL,
	device_type_code int4 NOT NULL,
	data_value numeric(10, 2) NULL,
	alarm_id int8 NULL,
	alarm_level int2 NULL,
	status int2 NULL,
	part_type varchar(128) NULL,
	CONSTRAINT check_out_data_pk PRIMARY KEY (id)
);

COMMENT ON COLUMN public.check_out_data.train_pass_info_id IS '过车数据id';
COMMENT ON COLUMN public.check_out_data.point_type_code IS '检测项编码';
COMMENT ON COLUMN public.check_out_data.part_code IS '列车被检部位编码';
COMMENT ON COLUMN public.check_out_data.device_type_code IS '监测类型（设备）编码';
COMMENT ON COLUMN public.check_out_data.data_value IS '检出值';
COMMENT ON COLUMN public.check_out_data.alarm_id IS '告警id';
COMMENT ON COLUMN public.check_out_data.alarm_level IS '告警等级，1：轻微，2：中等，3：严重';
COMMENT ON COLUMN public.check_out_data.status IS '告警处理状态，0：待处理（机器报警），1：确认报警（告警有效），2：无需处理（告警无效），3：持续追踪，4：疑似故障，5：已换部件。';
COMMENT ON COLUMN public.check_out_data.part_type IS '部件类型';


-- 创建轨旁监测设备类型、检测项、被检部位的编码与名称映射表
create table if not exists public.monitor_name_code_mapping
(
    "name" varchar(128) NOT NULL,
	code int4 NOT NULL,
	display_name varchar(128) NULL,
	"type" varchar(128) NULL,
	sort int4 NULL,
	unique_code varchar(128) NULL
);

COMMENT ON COLUMN public.monitor_name_code_mapping."name" IS '名称';
COMMENT ON COLUMN public.monitor_name_code_mapping.code IS '软件编码定义';
COMMENT ON COLUMN public.monitor_name_code_mapping.display_name IS 'web显示名称';
COMMENT ON COLUMN public.monitor_name_code_mapping."type" IS '类型';
COMMENT ON COLUMN public.monitor_name_code_mapping.sort IS '排序';
COMMENT ON COLUMN public.monitor_name_code_mapping.unique_code IS '唯一编码';


-- 插入轨旁监测设备类型、检测项、被检部位的编码与名称映射表 基础数据
INSERT INTO public.monitor_name_code_mapping ("name",code,display_name,"type",sort,unique_code) VALUES
	 ('受电弓',101,'受电弓','device_type_code',1,NULL),
	 ('轮对',302,'轮对','device_type_code',2,NULL),
	 ('MC1-1轴',1,'MC1-1轴','part_code_axle',1,'33'),
	 ('MC1-2轴',2,'MC1-2轴','part_code_axle',2,'34'),
	 ('MC1-3轴',3,'MC1-3轴','part_code_axle',3,'35'),
	 ('MC1-4轴',4,'MC1-4轴','part_code_axle',4,'36'),
	 ('MP1-1轴',5,'MP1-1轴','part_code_axle',5,'37'),
	 ('MP1-2轴',6,'MP1-2轴','part_code_axle',6,'38'),
	 ('MP1-3轴',7,'MP1-3轴','part_code_axle',7,'39'),
	 ('MP1-4轴',8,'MP1-4轴','part_code_axle',8,'40');
INSERT INTO public.monitor_name_code_mapping ("name",code,display_name,"type",sort,unique_code) VALUES
	 ('MP2-1轴',9,'MP2-1轴','part_code_axle',9,'41'),
	 ('MP2-2轴',10,'MP2-2轴','part_code_axle',10,'42'),
	 ('MP2-3轴',11,'MP2-3轴','part_code_axle',11,'43'),
	 ('MP2-4轴',12,'MP2-4轴','part_code_axle',12,'44'),
	 ('MC2-1轴',16,'MC2-1轴','part_code_axle',13,'45'),
	 ('MC2-2轴',15,'MC2-2轴','part_code_axle',14,'46'),
	 ('MC2-3轴',14,'MC2-3轴','part_code_axle',15,'47'),
	 ('MC2-4轴',13,'MC2-4轴','part_code_axle',16,'48'),
	 ('MC1-1转向架',1,'MC1-1转向架','part_code_bogie',1,'49'),
	 ('MC1-2转向架',2,'MC1-2转向架','part_code_bogie',2,'50');
INSERT INTO public.monitor_name_code_mapping ("name",code,display_name,"type",sort,unique_code) VALUES
	 ('MP1-1转向架',3,'MP1-1转向架','part_code_bogie',3,'51'),
	 ('MP1-2转向架',4,'MP1-2转向架','part_code_bogie',4,'52'),
	 ('MP2-1转向架',5,'MP2-1转向架','part_code_bogie',5,'53'),
	 ('MP2-2转向架',6,'MP2-2转向架','part_code_bogie',6,'54'),
	 ('MC2-1转向架',8,'MC2-1转向架','part_code_bogie',7,'55'),
	 ('MC2-2转向架',7,'MC2-2转向架','part_code_bogie',8,'56'),
	 ('MC1',1,'MC1','part_code_carriage',1,'57'),
	 ('MP1',2,'MP1','part_code_carriage',2,'58'),
	 ('MP2',3,'MP2','part_code_carriage',3,'59'),
	 ('MC2',4,'MC2','part_code_carriage',4,'60');
INSERT INTO public.monitor_name_code_mapping ("name",code,display_name,"type",sort,unique_code) VALUES
	 ('MP1',1,'MP1','part_code_pan',1,'61'),
	 ('MP2',2,'MP2','part_code_pan',2,'62'),
	 ('1',12,'MC1-1','part_code_wheel',1,'1'),
	 ('2',11,'MC1-2','part_code_wheel',2,'2'),
	 ('3',22,'MC1-3','part_code_wheel',3,'3'),
	 ('4',21,'MC1-4','part_code_wheel',4,'4'),
	 ('5',32,'MC1-5','part_code_wheel',5,'5'),
	 ('6',31,'MC1-6','part_code_wheel',6,'6'),
	 ('7',42,'MC1-7','part_code_wheel',7,'7'),
	 ('8',41,'MC1-8','part_code_wheel',8,'8');
INSERT INTO public.monitor_name_code_mapping ("name",code,display_name,"type",sort,unique_code) VALUES
	 ('1',52,'MP1-1','part_code_wheel',9,'9'),
	 ('2',51,'MP1-2','part_code_wheel',10,'10'),
	 ('3',62,'MP1-3','part_code_wheel',11,'11'),
	 ('4',61,'MP1-4','part_code_wheel',12,'12'),
	 ('5',72,'MP1-5','part_code_wheel',13,'13'),
	 ('6',71,'MP1-6','part_code_wheel',14,'14'),
	 ('7',82,'MP1-7','part_code_wheel',15,'15'),
	 ('8',81,'MP1-8','part_code_wheel',16,'16'),
	 ('1',121,'MP2-8','part_code_wheel',17,'17'),
	 ('2',122,'MP2-7','part_code_wheel',18,'18');
INSERT INTO public.monitor_name_code_mapping ("name",code,display_name,"type",sort,unique_code) VALUES
	 ('3',111,'MP2-6','part_code_wheel',19,'19'),
	 ('4',112,'MP2-5','part_code_wheel',20,'20'),
	 ('5',101,'MP2-4','part_code_wheel',21,'21'),
	 ('6',102,'MP2-3','part_code_wheel',22,'22'),
	 ('7',91,'MP2-2','part_code_wheel',23,'23'),
	 ('8',92,'MP2-1','part_code_wheel',24,'24'),
	 ('1',161,'MC2-1','part_code_wheel',25,'25'),
	 ('2',162,'MC2-2','part_code_wheel',26,'26'),
	 ('3',151,'MC2-3','part_code_wheel',27,'27'),
	 ('4',152,'MC2-4','part_code_wheel',28,'28');
INSERT INTO public.monitor_name_code_mapping ("name",code,display_name,"type",sort,unique_code) VALUES
	 ('5',141,'MC2-5','part_code_wheel',29,'29'),
	 ('6',142,'MC2-6','part_code_wheel',30,'30'),
	 ('7',131,'MC2-7','part_code_wheel',31,'31'),
	 ('8',132,'MC2-8','part_code_wheel',32,'32'),
	 ('前滑板磨耗',0,'前滑板磨耗','point_type_code_pan',1,NULL),
	 ('后滑板磨耗',2,'后滑板磨耗','point_type_code_pan',2,NULL),
	 ('前滑板缺口',4,'前滑板缺口','point_type_code_pan',3,NULL),
	 ('后滑板缺口',6,'后滑板缺口','point_type_code_pan',4,NULL),
	 ('前滑板中心偏移',8,'前滑板中心偏移','point_type_code_pan',5,NULL),
	 ('后滑板中心偏移',9,'后滑板中心偏移','point_type_code_pan',6,NULL);
INSERT INTO public.monitor_name_code_mapping ("name",code,display_name,"type",sort,unique_code) VALUES
	 ('受电弓中心偏移',10,'受电弓中心偏移','point_type_code_pan',7,NULL),
	 ('前滑板上下倾斜',11,'前滑板上下倾斜','point_type_code_pan',8,NULL),
	 ('后滑板上下倾斜',12,'后滑板上下倾斜','point_type_code_pan',9,NULL),
	 ('受电弓上下倾斜',13,'受电弓上下倾斜','point_type_code_pan',10,NULL),
	 ('前滑板羊角状态',31,'前滑板羊角状态','point_type_code_pan',11,NULL),
	 ('后滑板羊角状态',32,'后滑板羊角状态','point_type_code_pan',12,NULL),
	 ('前滑板剩余厚度',37,'前滑板剩余厚度','point_type_code_pan',13,NULL),
	 ('后滑板剩余厚度',38,'后滑板剩余厚度','point_type_code_pan',14,NULL),
	 ('轮缘厚',1,'轮缘厚','point_type_code_wheel',1,NULL),
	 ('轮缘高度',2,'轮缘高度','point_type_code_wheel',2,NULL);
INSERT INTO public.monitor_name_code_mapping ("name",code,display_name,"type",sort,unique_code) VALUES
	 ('QR 轮缘综合值',3,'QR 轮缘综合值','point_type_code_wheel',3,NULL),
	 ('不圆度',7,'不圆度','point_type_code_wheel',4,NULL),
	 ('轮直径',8,'轮直径','point_type_code_wheel',5,NULL),
	 ('擦伤面积(图像踏面)',61,'擦伤面积(图像踏面)','point_type_code_wheel',6,NULL),
	 ('轮对内侧距',6,'轮对内侧距','point_type_code_wheel',7,NULL),
	 ('同轴最大轮径差',9,'同轴最大轮径差','point_type_code_wheel',8,NULL),
	 ('同转向架最大轮径差',10,'同转向架最大轮径差','point_type_code_wheel',9,NULL),
	 ('同辆最大轮径差',11,'同辆最大轮径差','point_type_code_wheel',10,NULL);


-- 字典表新建轨旁监测所需字典
INSERT INTO public.sys_dict_type
(id, "type", description, create_time, create_by, modify_time, modify_by, del_flag, type_level)
VALUES('56', 'alarm_status', '诺丽告警数据处理状态', '2023-08-30 09:54:40.731', '0', '2023-08-30 09:54:40.731', '0', '0', '1');

INSERT INTO public.sys_dict
(id, sort_number, code, "label", value, type_id, description, modify_time, modify_by, create_time, create_by, del_flag, line_id, vehicle_type_id, line_name, vehicle_type_name, type_code)
VALUES('4204', 1, '0', '待处理', '0', '56', '诺丽告警数据处理状态', '2023-08-30 09:54:41.000', '0', '2023-08-30 09:54:41.000', '0', '0', '24', '129', 'SX2', '绍兴2号线车型', 'alarm_status');
INSERT INTO public.sys_dict
(id, sort_number, code, "label", value, type_id, description, modify_time, modify_by, create_time, create_by, del_flag, line_id, vehicle_type_id, line_name, vehicle_type_name, type_code)
VALUES('4205', 2, '1', '确认报警', '1', '56', '诺丽告警数据处理状态', '2023-08-30 09:54:41.000', '0', '2023-08-30 09:54:41.000', '0', '0', '24', '129', 'SX2', '绍兴2号线车型', 'alarm_status');
INSERT INTO public.sys_dict
(id, sort_number, code, "label", value, type_id, description, modify_time, modify_by, create_time, create_by, del_flag, line_id, vehicle_type_id, line_name, vehicle_type_name, type_code)
VALUES('4206', 3, '2', '无需处理', '2', '56', '诺丽告警数据处理状态', '2023-08-30 09:54:41.000', '0', '2023-08-30 09:54:41.000', '0', '0', '24', '129', 'SX2', '绍兴2号线车型', 'alarm_status');
INSERT INTO public.sys_dict
(id, sort_number, code, "label", value, type_id, description, modify_time, modify_by, create_time, create_by, del_flag, line_id, vehicle_type_id, line_name, vehicle_type_name, type_code)
VALUES('4207', 4, '3', '持续追踪', '3', '56', '诺丽告警数据处理状态', '2023-08-30 09:54:41.000', '0', '2023-08-30 09:54:41.000', '0', '0', '24', '129', 'SX2', '绍兴2号线车型', 'alarm_status');
INSERT INTO public.sys_dict
(id, sort_number, code, "label", value, type_id, description, modify_time, modify_by, create_time, create_by, del_flag, line_id, vehicle_type_id, line_name, vehicle_type_name, type_code)
VALUES('4208', 5, '4', '疑似故障', '4', '56', '诺丽告警数据处理状态', '2023-08-30 09:54:41.000', '0', '2023-08-30 09:54:41.000', '0', '0', '24', '129', 'SX2', '绍兴2号线车型', 'alarm_status');
INSERT INTO public.sys_dict
(id, sort_number, code, "label", value, type_id, description, modify_time, modify_by, create_time, create_by, del_flag, line_id, vehicle_type_id, line_name, vehicle_type_name, type_code)
VALUES('4209', 6, '5', '已换部件', '5', '56', '诺丽告警数据处理状态', '2023-08-30 09:54:41.000', '0', '2023-08-30 09:54:41.000', '0', '0', '24', '129', 'SX2', '绍兴2号线车型', 'alarm_status');


