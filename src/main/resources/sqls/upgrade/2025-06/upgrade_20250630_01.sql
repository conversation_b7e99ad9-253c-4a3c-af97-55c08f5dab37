
UPDATE public.comm_original_signal
	SET parse_script='result=String.format("%d.%02d",(value>>8) & 0xFF,value & 0xFF);'
	WHERE id='2112237340';


UPDATE public.comm_original_signal
	SET parse_script='result=String.format("%d.%02d",(value>>8) & 0xFF,value & 0xFF);'
	WHERE id='2212238540';


UPDATE public.comm_original_signal
	SET parse_script='result=String.format("%d.%02d",(value>>8) & 0xFF,value & 0xFF);'
	WHERE id='2312239740';


UPDATE public.comm_original_signal_new
SET parse_script='result=String.format("%d.%02d",(value>>8) & 0xFF,value & 0xFF);'
WHERE id='2112237340';

UPDATE public.comm_original_signal_new
SET parse_script='result=String.format("%d.%02d",(value>>8) & 0xFF,value & 0xFF);'
WHERE id='2212238540';


UPDATE public.comm_original_signal_new
SET parse_script='result=String.format("%d.%02d",(value>>8) & 0xFF,value & 0xFF);'
WHERE id='2312239740';



UPDATE public.comm_original_signal
SET parse_script='result=String.format("%d.%02d",(value>>5) & 0x07,value & 0x1F);'
WHERE id='2429266730';


UPDATE public.comm_original_signal_new
SET parse_script='result=String.format("%d.%02d",(value>>5) & 0x07,value & 0x1F);'
WHERE id='2429266730';


UPDATE public.comm_original_signal
SET parse_script='result=String.format("%d.%02d",(value>>13) & 0x07,value(value>>8) & 0x1F);'
WHERE id='2429266720';

UPDATE public.comm_original_signal_new
SET parse_script='result=String.format("%d.%02d",(value>>13) & 0x07,value(value>>8) & 0x1F);'
WHERE id='2429266720';


UPDATE public.comm_original_signal
SET parse_script='result=String.format("%d.%02d",(value>>8) & 0xFF,value & 0xFF);'
WHERE id='2029121440';

UPDATE public.comm_original_signal_new
SET parse_script='result=String.format("%d.%02d",(value>>8) & 0xFF,value & 0xFF);'
WHERE id='2029121440';


UPDATE monitor_trigger
SET slot_id='07b86e2d3f53449a9f23cc02ea679b50', "label"='', sort=1, signal_id='2028155240', trigger_value=NULL, data_display_point='1', unit_status=NULL, svg_url='', ext_properties=NULL, create_time='2025-06-25 15:50:40.000', create_by=NULL, modify_time='2025-06-30 14:56:14.000', modify_by=NULL, del_flag=false, image_type=NULL, image_path=NULL, signal_name_en='BatVer_4', mark=NULL
WHERE id='45d2cae8862d4be9a96188ffb1ab8ac8';

UPDATE monitor_trigger
SET slot_id='ec70c7dbcd38430ca9bc9ff3d6f6f784', "label"='', sort=1, signal_id='2028155220', trigger_value=NULL, data_display_point='1', unit_status=NULL, svg_url='', ext_properties=NULL, create_time='2025-06-25 15:50:27.000', create_by=NULL, modify_time='2025-06-30 14:56:05.000', modify_by=NULL, del_flag=false, image_type=NULL, image_path=NULL, signal_name_en='BatVer_1', mark=NULL
WHERE id='cfc5bc8045b64c8a893c7ce367aa29ba';



ALTER TABLE public.mtc_alarm_warning ADD close_statue bool DEFAULT false NULL;
ALTER TABLE public.mtc_alarm_warning ALTER COLUMN close_statue SET STORAGE PLAIN;
COMMENT ON COLUMN public.mtc_alarm_warning.close_statue IS '是否弹窗';
