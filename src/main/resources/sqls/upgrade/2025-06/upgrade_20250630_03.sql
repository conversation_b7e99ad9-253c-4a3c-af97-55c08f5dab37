-- Auto-generated SQL script #202506301440
UPDATE public.mtr_software_mapping
	SET "name"='VCU'
	WHERE id='SX22022N10068';
UPDATE public.mtr_software_mapping
	SET "name"='VCU'
	WHERE id='SX22022N10069';
-- Auto-generated SQL script #202506301443
UPDATE public.mtr_software_mapping
	SET "name"='HMI'
	WHERE id='SX22022N10066';
-- Auto-generated SQL script #202506301443
UPDATE public.mtr_software_mapping
	SET "name"='HMI'
	WHERE id='SX22022N10067';
-- Auto-generated SQL script #202506301444
UPDATE public.mtr_software_mapping
	SET "name"='RIOM10'
	WHERE id='SX22022N10060';
-- Auto-generated SQL script #202506301444
UPDATE public.mtr_software_mapping
	SET "name"='RIOM10'
	WHERE id='SX22022N10061';
-- Auto-generated SQL script #202506301446
UPDATE public.mtr_software_mapping
	SET "name"='RIOMx1'
	WHERE id='SX22022N10062';
-- Auto-generated SQL script #202506301447
UPDATE public.mtr_software_mapping
	SET "name"='RIOMx1'
	WHERE id='SX22022N10064';
-- Auto-generated SQL script #202506301448
UPDATE public.mtr_software_mapping
	SET "name"='RIOMx1'
	WHERE id='SX22022N10065';
-- Auto-generated SQL script #202506301449
UPDATE public.mtr_software_mapping
	SET "name"='RIOMx1'
	WHERE id='SX22022N10063';
-- Auto-generated SQL script #202506301457
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10118','BCU1_IuiSWVersion','制动系统','BCU1_IuiSWVersion','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'BCU','TMc1');
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10119','BCU2_IuiSWVersion','制动系统','BCU2_IuiSWVersion','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'BCU','Mp1');
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10120','BCU3_IuiSWVersion','制动系统','BCU3_IuiSWVersion','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'BCU','Mp2');
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10121','BCU4_IuiSWVersion','制动系统','BCU4_IuiSWVersion','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'BCU','TMc2');
-- Auto-generated SQL script #202506301502
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10122','DCU12_IuiLglSWRevision','牵引系统逻辑1/2','DCU12_IuiLglSWRevision','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'DCU','TMc1');
-- Auto-generated SQL script #202506301504
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10123','DCU21_IuiLglSWRevision','牵引系统逻辑1/2','DCU21_IuiLglSWRevision','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'DCU','Mp1');
-- Auto-generated SQL script #202506301506
UPDATE public.mtr_software_mapping
	SET "name"='牵引系统逆变1/2'
	WHERE id='SX22022N10041';
-- Auto-generated SQL script #202506301513
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10124','DCU21_IuiInvSWRevision','牵引系统逆变1/2','DCU21_IuiInvSWRevision','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'DCU','Mp1');
-- Auto-generated SQL script #202506301523
UPDATE public.mtr_software_mapping
	SET "name"='牵引系统粘着1/2'
	WHERE id='SX22022N10043';
-- Auto-generated SQL script #202506301526
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10125','DCU21_IuiAdhSWRevision','牵引系统粘着1/2','DCU21_IuiAdhSWRevision','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'DCU','Mp1');
-- Auto-generated SQL script #202506301528
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10126','DCU22_IuiLglSWRevision','牵引系统逻辑1/2','DCU22_IuiLglSWRevision','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'DCU','Mp1');
-- Auto-generated SQL script #202506301530
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10127','DCU31_IuiLglSWRevision','牵引系统逻辑1/2','DCU31_IuiLglSWRevision','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'DCU','Mp2');
-- Auto-generated SQL script #202506301531
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10128','DCU32_IuiLglSWRevision','牵引系统逻辑1/2','DCU32_IuiLglSWRevision','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'DCU','Mp2');
-- Auto-generated SQL script #202506301532
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10129','DCU42_IuiLglSWRevision','牵引系统逻辑1/2','DCU42_IuiLglSWRevision','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'DCU','TMc2');
-- Auto-generated SQL script #202506301534
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10130','DCU22_IuiInvSWRevision','牵引系统逆变1/2','DCU22_IuiInvSWRevision','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'DCU','Mp1');
-- Auto-generated SQL script #202506301535
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10131','DCU31_IuiInvSWRevision','牵引系统逆变1/2','DCU31_IuiInvSWRevision','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'DCU','Mp2');
-- Auto-generated SQL script #202506301536
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10132','DCU32_IuiInvSWRevision','牵引系统逆变1/2','DCU32_IuiInvSWRevision','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'DCU','Mp2');
-- Auto-generated SQL script #202506301537
UPDATE public.mtr_software_mapping
	SET "name"='牵引系统逆变1/2'
	WHERE id='SX22022N10042';
-- Auto-generated SQL script #202506301539
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10133','DCU22_IuiAdhSWRevision','牵引系统粘着1/2','DCU22_IuiAdhSWRevision','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'DCU','Mp1');
-- Auto-generated SQL script #202506301540
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10134','DCU31_IuiAdhSWRevision','牵引系统粘着1/2','DCU31_IuiAdhSWRevision','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'DCU','Mp2');
-- Auto-generated SQL script #202506301541
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10135','DCU32_IuiAdhSWRevision','牵引系统粘着1/2','DCU32_IuiAdhSWRevision','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'DCU','Mp2');
-- Auto-generated SQL script #202506301541
UPDATE public.mtr_software_mapping
	SET "name"='牵引系统粘着1/2'
	WHERE id='SX22022N10044';
-- Auto-generated SQL script #202506301547
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10136','SIV1_IuiINVDSPVer','辅助系统DSP','SIV1_IuiINVDSPVer','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'SIV','TMc1');
-- Auto-generated SQL script #202506301548
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10137','SIV4_IuiINVDSPVer','辅助系统DSP','SIV4_IuiINVDSPVer','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'SIV','TMc2');
-- Auto-generated SQL script #202506301550
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10138','SIV1_IuiINVLGCVer','辅助系统PPC','SIV1_IuiINVLGCVer','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'SIV','TMc1');
-- Auto-generated SQL script #202506301551
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10139','SIV4_IuiINVLGCVer','辅助系统PPC','SIV4_IuiINVLGCVer','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'SIV','TMc2');
-- Auto-generated SQL script #202506301552
UPDATE public.mtr_software_mapping
	SET "name"='烟火系统'
	WHERE id='SX22022N10072';
-- Auto-generated SQL script #202506301553
UPDATE public.mtr_software_mapping
	SET "name"='烟火系统'
	WHERE id='SX22022N10073';
-- Auto-generated SQL script #202506301554
UPDATE public.mtr_software_mapping
	SET "name"='乘客信息系统PIC'
	WHERE id='SX22022N10054';
-- Auto-generated SQL script #202506301555
UPDATE public.mtr_software_mapping
	SET "name"='乘客信息系统PIC'
	WHERE id='SX22022N10055';
-- Auto-generated SQL script #202506301557
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10140','HVAC1_uiHVACSWVersion','空调系统','HVAC1_uiHVACSWVersion','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'HVAC','TMc1');
-- Auto-generated SQL script #202506301600
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10141','HVAC2_uiHVACSWVersion','空调系统','HVAC2_uiHVACSWVersion','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'HVAC','Mp1');
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10142','HVAC3_uiHVACSWVersion','空调系统','HVAC3_uiHVACSWVersion','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'HVAC','Mp2');
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10143','HVAC4_uiHVACSWVersion','空调系统','HVAC4_uiHVACSWVersion','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'HVAC','TMc2');
-- Auto-generated SQL script #202506301601
UPDATE public.mtr_software_mapping
	SET "name"='车门系统'
	WHERE id='SX22022N10083';
-- Auto-generated SQL script #202506301601
UPDATE public.mtr_software_mapping
	SET "name"='车门系统'
	WHERE id='SX22022N10084';
-- Auto-generated SQL script #202506301602
UPDATE public.mtr_software_mapping
	SET "name"='车门系统'
	WHERE id='SX22022N10085';
-- Auto-generated SQL script #202506301602
UPDATE public.mtr_software_mapping
	SET "name"='车门系统'
	WHERE id='SX22022N10086';
-- Auto-generated SQL script #202506301603
UPDATE public.mtr_software_mapping
	SET "name"='主动障碍物'
	WHERE id='SX22022N10116';
-- Auto-generated SQL script #202506301603
UPDATE public.mtr_software_mapping
	SET "name"='主动障碍物'
	WHERE id='SX22022N10117';
-- Auto-generated SQL script #202506301604
UPDATE public.mtr_software_mapping
	SET "name"='被动障碍物'
	WHERE id='SX22022N10070';
-- Auto-generated SQL script #202506301604
UPDATE public.mtr_software_mapping
	SET "name"='被动障碍物'
	WHERE id='SX22022N10071';
-- Auto-generated SQL script #202506301605
UPDATE public.mtr_software_mapping
	SET "name"='能耗记录仪'
	WHERE id='SX22022N10087';
-- Auto-generated SQL script #202506301606
UPDATE public.mtr_software_mapping
	SET "name"='能耗记录仪'
	WHERE id='SX22022N10088';
-- Auto-generated SQL script #202506301606
UPDATE public.mtr_software_mapping
	SET "name"='BDS系统'
	WHERE id='SX22022N10035';
-- Auto-generated SQL script #202506301607
UPDATE public.mtr_software_mapping
	SET "name"='BDS系统'
	WHERE id='SX22022N10036';
-- Auto-generated SQL script #202506301607
UPDATE public.mtr_software_mapping
	SET "name"='轨道几何系统'
	WHERE id='SX22022N10078';
-- Auto-generated SQL script #202506301608
UPDATE public.mtr_software_mapping
	SET "name"='线路巡检系统'
	WHERE id='SX22022N10115';
-- Auto-generated SQL script #202506301610
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10144','LCU1_IuiSoftVer_com','LCU通信板','LCU1_IuiSoftVer_com','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'LCU','TMc1');
-- Auto-generated SQL script #202506301611
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10145','LCU4_IuiSoftVer_com','LCU通信板','LCU4_IuiSoftVer_com','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'LCU','TMc2');
-- Auto-generated SQL script #202506301612
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10146','LCU1_IuiSoftVer_app','LCU应用程序','LCU1_IuiSoftVer_app','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'LCU','TMc1');
-- Auto-generated SQL script #202506301613
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10147','LCU4_IuiSoftVer_app','LCU应用程序','LCU4_IuiSoftVer_app','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'LCU','TMc2');
-- Auto-generated SQL script #202506301614
UPDATE public.mtr_software_mapping
	SET "name"='RFID'
	WHERE id='SX22022N10058';
-- Auto-generated SQL script #202506301614
UPDATE public.mtr_software_mapping
	SET "name"='RFID'
	WHERE id='SX22022N10059';
-- Auto-generated SQL script #202506301619
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10148','BatVer_1','BMS系统','BatVer_1','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'VCU','TMc1');
-- Auto-generated SQL script #202506301622
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10149','BatVer_4','BMS系统','BatVer_4','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'VCU','TMc2');
-- Auto-generated SQL script #202506301625
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10150','SIV1_IuiBCGDSPVer','充电机DSP','SIV1_IuiBCGDSPVer','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'SIV','TMc1');
-- Auto-generated SQL script #202506301626
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10151','SIV4_IuiBCGDSPVer','充电机DSP','SIV4_IuiBCGDSPVer','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'SIV','TMc2');
-- Auto-generated SQL script #202506301627
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10152','SIV1_IuiBCGLGCVer','充电机LGC','SIV1_IuiBCGLGCVer','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'SIV','TMc1');
-- Auto-generated SQL script #202506301628
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10153','SIV4_IuiBCGLGCVer','充电机LGC','SIV4_IuiBCGLGCVer','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'SIV','TMc2');
-- Auto-generated SQL script #202506301631
INSERT INTO public.mtr_software_mapping (id,signal_name_en,"name",signal_list,line_id,vehicle_type_id,create_by,create_time,modify_by,modify_time,del_flag,subsystem,"location")
	VALUES ('SX22022N10154','PCMU_IuiSWRevision_2','弓网检测','PCMU_IuiSWRevision_2','24','129','0','2022-12-12 16:53:07.000','0','2022-12-12 16:53:07.000',0,'VCU','TMc1');


-- Auto-generated SQL script #202506301644
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10001';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10002';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10003';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10004';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10005';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10006';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10007';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10008';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10009';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10010';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10011';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10012';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10013';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10014';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10015';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10016';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10017';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10018';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10019';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10020';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10021';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10022';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10023';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10024';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10025';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10026';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10027';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10028';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10029';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10030';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10031';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10032';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10033';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10034';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10037';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10038';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10039';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10040';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10045';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10046';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10047';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10048';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10049';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10050';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10051';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10052';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10053';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10056';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10057';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10074';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10075';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10076';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10077';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10079';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10080';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10081';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10082';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10089';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10090';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10091';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10092';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10093';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10094';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10095';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10096';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10097';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10098';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10099';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10100';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10101';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10102';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10103';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10104';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10105';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10106';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10107';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10108';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10109';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10110';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10111';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10112';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10113';
UPDATE public.mtr_software_mapping
	SET del_flag=1
	WHERE id='SX22022N10114';



DELETE FROM mtr_software;


