create table if not exists comm_extend_signal
(
    id           varchar(128) not null
        primary key,
    protocol_id  varchar(128) not null,
    name_cn      varchar(128) not null,
    name_en      varchar(128) not null,
    data_type    varchar(32)  not null,
    parse_script text,
    location     varchar(16),
    subsystem    varchar(32)  not null,
    unit         varchar(16),
    max_value    double precision,
    min_value    double precision,
    create_by    varchar(128),
    create_time  timestamp(6),
    modify_time  timestamp(6),
    modify_by    varchar(128),
    remark       text
);

comment on table comm_extend_signal is '扩展信号';

comment on column comm_extend_signal.id is '主键';

comment on column comm_extend_signal.protocol_id is '外键，引用协议表的主键（id）';

comment on column comm_extend_signal.name_cn is '扩展信号中文名称';

comment on column comm_extend_signal.name_en is '扩展信号英文名称';

comment on column comm_extend_signal.data_type is '数据类型';

comment on column comm_extend_signal.parse_script is '解析脚本，基于基础信号计算';

comment on column comm_extend_signal.location is '信号所属车辆，A1/B1/B2/A2';

comment on column comm_extend_signal.subsystem is '信号所属子系统';

comment on column comm_extend_signal.unit is '单位';

comment on column comm_extend_signal.modify_time is '修改时间';

comment on column comm_extend_signal.modify_by is '用户编号';

comment on column comm_extend_signal.remark is '备注';

alter table comm_extend_signal
    owner to phmdbadmin;

create table if not exists comm_extend_signal_group
(
    extend_signal_name_en varchar(128) not null,
    group_id              varchar(128) not null
);

comment on table comm_extend_signal_group is '扩展信号分组';

comment on column comm_extend_signal_group.extend_signal_name_en is '外键，引用扩展信号表的英文名称（name_en）';

comment on column comm_extend_signal_group.group_id is '外键，引用信号分组表的主键（id）';

alter table comm_extend_signal_group
    owner to phmdbadmin;

create index if not exists comm_extend_signal_group_extend_signal_name_en_idx
    on comm_extend_signal_group (extend_signal_name_en);

create index if not exists comm_extend_signal_group_group_id_idx
    on comm_extend_signal_group (group_id);

create table if not exists comm_mqtt_acl
(
    id       varchar(128) not null
        primary key,
    allow    integer,
    ipaddr   varchar(64),
    username varchar(128),
    clientid varchar(255),
    access   integer,
    topic    varchar(255)
);

comment on table comm_mqtt_acl is '通信权限控制';

alter table comm_mqtt_acl
    owner to phmdbadmin;

create table if not exists comm_mqtt_protocol
(
    comm_topic_id varchar(128)       not null,
    protocol_id   varchar(128)       not null,
    update_time   timestamp(0),
    valid         smallint default 1 not null,
    create_by     varchar(128),
    create_time   timestamp(0),
    modify_by     varchar(128),
    modify_time   timestamp(0),
    id            varchar(128)       not null,
    vehicle_id    varchar(128)       not null
);

comment on table comm_mqtt_protocol is 'MQTT主题协议，更新协议时，并不删除此记录，仅把有效标志置做False';

comment on column comm_mqtt_protocol.comm_topic_id is '外键，引用通讯主题表的主键（id）';

comment on column comm_mqtt_protocol.protocol_id is '外键，引用协议表的主键（id）';

comment on column comm_mqtt_protocol.update_time is '主题解析协议更新时间';

comment on column comm_mqtt_protocol.valid is '有效标志，1：有效，0：无效';

comment on column comm_mqtt_protocol.create_by is '创建用户编号';

comment on column comm_mqtt_protocol.create_time is '创建时间';

comment on column comm_mqtt_protocol.modify_by is '修改用户编号';

comment on column comm_mqtt_protocol.modify_time is '修改时间';

comment on column comm_mqtt_protocol.id is '主键';

comment on column comm_mqtt_protocol.vehicle_id is '外键，引用车辆表的主键（id）';

alter table comm_mqtt_protocol
    owner to phmdbadmin;

create table if not exists comm_mqtt_topic_type
(
    id              varchar(128) not null
        primary key,
    vehicle_type_id varchar(128) not null,
    name            varchar(255) not null,
    type            varchar(32)  not null,
    qos             smallint     not null,
    modify_time     timestamp(6),
    remark          text,
    alias           varchar(128),
    create_by       varchar(128),
    create_time     timestamp(0),
    modify_by       varchar(128),
    constraint comm_mqtt_topic_type_vehicle_type_id_name_key1
        unique (vehicle_type_id, name)
);

comment on table comm_mqtt_topic_type is '通信主题类，相当于车辆上传数据包的种类。';

comment on column comm_mqtt_topic_type.vehicle_type_id is '外键，引用车辆型号表的主键（id）';

comment on column comm_mqtt_topic_type.name is '主题名称，也相当于数据包类型';

comment on column comm_mqtt_topic_type.type is '主题类型，订阅/发布（发送和接收）';

comment on column comm_mqtt_topic_type.qos is '消息服务质量';

comment on column comm_mqtt_topic_type.modify_time is '修改时间';

comment on column comm_mqtt_topic_type.alias is '主题别名，说明主题的用途';

comment on column comm_mqtt_topic_type.create_by is '创建用户编号';

comment on column comm_mqtt_topic_type.create_time is '创建时间';

comment on column comm_mqtt_topic_type.modify_by is '修改用户编号';

alter table comm_mqtt_topic_type
    owner to phmdbadmin;

create index if not exists comm_mqtt_topic_type_vehicle_type_id_idx
    on comm_mqtt_topic_type (vehicle_type_id);

create table if not exists comm_original_signal
(
    id             varchar(128) not null
        primary key,
    protocol_id    varchar(128) not null,
    name_cn        varchar(128) not null,
    name_en        varchar(128) not null,
    byte_offset    integer      not null,
    bit_offset     smallint     not null,
    data_type      varchar(32)  not null,
    unit           varchar(16),
    location       varchar(16),
    subsystem      varchar(32),
    parse_script   text,
    fault_type_key varchar(64),
    trigger_value  smallint,
    max_value      double precision,
    min_value      double precision,
    create_by      varchar(128),
    create_time    timestamp(6) default CURRENT_TIMESTAMP,
    modify_by      varchar(128),
    modify_time    timestamp(6) default CURRENT_TIMESTAMP,
    remark         text,
    redis_flag     char,
    result_type    varchar(32),
    frames_type    smallint,
    package_order  smallint
);

comment on table comm_original_signal is '信号';

comment on column comm_original_signal.id is '主键';

comment on column comm_original_signal.protocol_id is '外键，引用协议表的主键（id）';

comment on column comm_original_signal.name_cn is '信号中文名';

comment on column comm_original_signal.name_en is '信号英文名，作为统一版本下的唯一标识';

comment on column comm_original_signal.byte_offset is '字节偏移量';

comment on column comm_original_signal.bit_offset is '位偏移量';

comment on column comm_original_signal.data_type is '数据类型，U8/U16/U32/S8/S16/S32/Bool';

comment on column comm_original_signal.unit is '信号单位';

comment on column comm_original_signal.location is '信号位置，A1/B1/C1/D1';

comment on column comm_original_signal.subsystem is '信号所属子系统';

comment on column comm_original_signal.parse_script is '解析公式脚本';

comment on column comm_original_signal.fault_type_key is '外键，引用故障类型表的业务主键（fault_type_key）';

comment on column comm_original_signal.trigger_value is '布尔型信号故障触发值，0：低电平触发，1：高电平触发';

comment on column comm_original_signal.max_value is '信号最大值';

comment on column comm_original_signal.min_value is '信号最小值';

comment on column comm_original_signal.create_by is '创建用户编号';

comment on column comm_original_signal.create_time is '创建时间';

comment on column comm_original_signal.modify_by is '修改用户编号';

comment on column comm_original_signal.modify_time is '修改时间';

comment on column comm_original_signal.remark is '备注';

comment on column comm_original_signal.redis_flag is '1-redis数据，0-非redis数据';

comment on column comm_original_signal.result_type is '解析结果数据类型';

alter table comm_original_signal
    owner to phmdbadmin;

create table if not exists comm_original_signal_group
(
    original_signal_name_en varchar(128) not null,
    group_id                varchar(64)  not null
);

comment on column comm_original_signal_group.original_signal_name_en is '外键，引用原始信号表的英文名称（name_en）';

comment on column comm_original_signal_group.group_id is '外键，引用信号分组表的主键（id）';

alter table comm_original_signal_group
    owner to phmdbadmin;

create index if not exists comm_original_signal_group_group_id_idx
    on comm_original_signal_group (group_id);

create index if not exists comm_original_signal_group_original_signal_name_en_idx
    on comm_original_signal_group (original_signal_name_en);

create table if not exists comm_original_signal_new
(
    id            varchar(128) not null
        primary key,
    protocol_id   varchar(128) not null,
    name_cn       varchar(128) not null,
    name_en       varchar(128) not null,
    byte_offset   integer      not null,
    bit_offset    smallint     not null,
    data_type     varchar(32)  not null,
    unit          varchar(16),
    location      varchar(16),
    subsystem     varchar(32)  not null,
    parse_script  text,
    create_by     varchar(128),
    create_time   timestamp(6),
    modify_by     varchar(128),
    modify_time   timestamp(0),
    remark        text,
    result_type   varchar(32),
    target_id     varchar(128),
    target_name   varchar(128),
    port_no       varchar(20),
    "ifRepeat"    varchar(2),
    fault_flag    varchar(2),
    signal_type   varchar(2),
    frames_type   integer,
    package_order integer
);

comment on column comm_original_signal_new.id is '主键';

comment on column comm_original_signal_new.protocol_id is '外键，引用协议表的主键（id）';

comment on column comm_original_signal_new.name_cn is '信号中文名';

comment on column comm_original_signal_new.name_en is '信号英文名，作为统一版本下的唯一标识';

comment on column comm_original_signal_new.byte_offset is '字节偏移量';

comment on column comm_original_signal_new.bit_offset is '位偏移量';

comment on column comm_original_signal_new.data_type is '数据类型，U8/U16/U32/S8/S16/S32/Bool';

comment on column comm_original_signal_new.unit is '信号单位';

comment on column comm_original_signal_new.location is '信号位置，A1/B1/C1/D1';

comment on column comm_original_signal_new.subsystem is '信号所属子系统';

comment on column comm_original_signal_new.parse_script is '解析公式脚本';

comment on column comm_original_signal_new.create_by is '创建用户编号';

comment on column comm_original_signal_new.create_time is '创建时间';

comment on column comm_original_signal_new.modify_by is '修改用户编号';

comment on column comm_original_signal_new.modify_time is '修改时间';

comment on column comm_original_signal_new.remark is '备注';

comment on column comm_original_signal_new.result_type is '解析结果数据类型';

comment on column comm_original_signal_new.target_id is '目标id';

comment on column comm_original_signal_new.port_no is '端口号';

comment on column comm_original_signal_new."ifRepeat" is '是否重复';

comment on column comm_original_signal_new.signal_type is '1-整车级，2-单元级，3-车辆级，4-转向架级，
4-车轴级';

comment on column comm_original_signal_new.frames_type is '报文类型 1=128ms 2=512ms';

comment on column comm_original_signal_new.package_order is '报文id 0-6  代表package1-7';

alter table comm_original_signal_new
    owner to phmdbadmin;

create table if not exists comm_protocol
(
    id                 varchar(128)                                          not null
        primary key,
    vehicle_type_id    varchar(128)                                          not null,
    name               varchar(128)                                          not null,
    version            varchar(32)                                           not null,
    size               integer,
    endian             varchar(32) default 'LittleEndian'::character varying not null,
    enable             smallint    default 1                                 not null,
    del_flag           smallint    default 0                                 not null,
    create_by          varchar(128),
    create_time        timestamp(6),
    modify_by          varchar(128),
    modify_time        timestamp(0),
    remark             text,
    tcp_packet_type_id varchar(128),
    mqtt_topic_type_id varchar(128),
    line_id            varchar(128)
);

comment on table comm_protocol is '通讯协议';

comment on column comm_protocol.id is '主键，数据解析协议编号';

comment on column comm_protocol.vehicle_type_id is '外键，引用车辆型号表的主键（id）';

comment on column comm_protocol.name is '协议名称';

comment on column comm_protocol.version is '协议版本号';

comment on column comm_protocol.size is '数据包大小';

comment on column comm_protocol.endian is '开端模式，BigEndian/LittleEndian';

comment on column comm_protocol.enable is '使能状态，1：激活，0：未激活';

comment on column comm_protocol.del_flag is '逻辑删除标志，1：逻辑删除，0：未删除';

comment on column comm_protocol.create_by is '创建用户编号';

comment on column comm_protocol.create_time is '创建时间';

comment on column comm_protocol.modify_by is '修改用户编号';

comment on column comm_protocol.modify_time is '修改时间';

comment on column comm_protocol.remark is '备注';

comment on column comm_protocol.tcp_packet_type_id is '外键，引用数据包表的主键（id）';

comment on column comm_protocol.mqtt_topic_type_id is '外键，引用主题表的主键（id）';

comment on column comm_protocol.line_id is '线路id';

alter table comm_protocol
    owner to phmdbadmin;

create index if not exists comm_protocol_vehicle_type_id_idx
    on comm_protocol (vehicle_type_id);

create table if not exists comm_record
(
    id              varchar(128) not null
        primary key,
    vehicle_id      varchar(128) not null,
    type            varchar(32),
    packet_counter  bigint,
    success_counter bigint,
    failure_counter bigint,
    modity_date     date
);

comment on table comm_record is '通讯记录，记录车辆每日通讯情况';

comment on column comm_record.id is '主键';

comment on column comm_record.vehicle_id is '外键，引用车辆表的主键（id）';

comment on column comm_record.type is '数据类型，实时数据/故障数据';

comment on column comm_record.packet_counter is '数据包数量';

comment on column comm_record.success_counter is '成功解析数量';

comment on column comm_record.failure_counter is '解析失败数量';

comment on column comm_record.modity_date is '修改日期';

alter table comm_record
    owner to phmdbadmin;

create table if not exists comm_signal_favourites
(
    id        varchar(128) not null
        constraint comm_signal_favourites_sub_pkey
            primary key,
    group_id  varchar(128) not null,
    signal_id varchar(128) not null,
    node_id   varchar(128)
);

comment on table comm_signal_favourites is '信号条件收藏子表';

comment on column comm_signal_favourites.id is '主键';

comment on column comm_signal_favourites.group_id is '外键，收藏id（分组id）';

comment on column comm_signal_favourites.signal_id is '信号主键';

comment on column comm_signal_favourites.node_id is '节点id（用于前端判断信号的位置）';

alter table comm_signal_favourites
    owner to phmdbadmin;

create table if not exists comm_signal_favourites_group
(
    id              varchar(128)       not null
        constraint comm_signal_favourites_pkey
            primary key,
    name            varchar(128)       not null,
    vehicle_type_id varchar(128)       not null,
    vehicle_id      varchar(128)       not null,
    create_by       varchar(128)       not null,
    create_time     timestamp(6)       not null,
    enable          smallint default 1 not null,
    modify_by       varchar(128),
    modify_time     timestamp(6),
    remark          text,
    vehicle_code    varchar(64)
);

comment on table comm_signal_favourites_group is '信号条件收藏主表';

comment on column comm_signal_favourites_group.id is '主键id';

comment on column comm_signal_favourites_group.name is '收藏名称';

comment on column comm_signal_favourites_group.vehicle_type_id is '外键，车辆型号表的主键（id）';

comment on column comm_signal_favourites_group.vehicle_id is '外键，车辆id';

comment on column comm_signal_favourites_group.create_by is '创建收藏的当前用户';

comment on column comm_signal_favourites_group.create_time is '创建收藏的当前时间';

comment on column comm_signal_favourites_group.enable is '使能状态，1：激活，0：未激活';

comment on column comm_signal_favourites_group.modify_by is '修改收藏的当前用户';

comment on column comm_signal_favourites_group.modify_time is '修改收藏的当前时间';

comment on column comm_signal_favourites_group.remark is '备注';

comment on column comm_signal_favourites_group.vehicle_code is '外键，车辆型号表的车辆编号';

alter table comm_signal_favourites_group
    owner to phmdbadmin;

create table if not exists comm_signal_group
(
    id              varchar(128) not null
        primary key,
    vehicle_type_id varchar(128) not null,
    name            varchar(64)  not null,
    create_by       varchar(128),
    create_time     timestamp(0),
    modify_by       varchar(128),
    modify_time     timestamp(0),
    remark          text
);

comment on table comm_signal_group is '信号分组，因信号众多，所以在其上建立组的概念，方便信号管理和关联';

comment on column comm_signal_group.id is '主键';

comment on column comm_signal_group.vehicle_type_id is '外键，引用车辆型号表的主键（id）';

comment on column comm_signal_group.name is '信号分组名称';

comment on column comm_signal_group.create_by is '创建用户编号';

comment on column comm_signal_group.create_time is '创建时间';

comment on column comm_signal_group.modify_by is '修改用户编号';

comment on column comm_signal_group.modify_time is '修改时间';

comment on column comm_signal_group.remark is '备注';

alter table comm_signal_group
    owner to phmdbadmin;

create table if not exists comm_signal_interval
(
    train_code  varchar(20),
    day         varchar(8),
    start_time  timestamp(6),
    end_time    timestamp(6),
    create_time timestamp(6)
);

alter table comm_signal_interval
    owner to phmdbadmin;

create table if not exists comm_tcp_packet_type
(
    id              varchar(128) not null
        primary key,
    vehicle_type_id varchar(128) not null,
    name            varchar(255) not null
        unique,
    alias           varchar(128),
    valid_script    text,
    create_by       varchar(128),
    create_time     timestamp(6),
    modify_by       varchar(128),
    modify_time     timestamp(0)
);

comment on table comm_tcp_packet_type is 'TCP数据包类型';

comment on column comm_tcp_packet_type.id is '主键，主题编号';

comment on column comm_tcp_packet_type.name is '数据包名称';

comment on column comm_tcp_packet_type.alias is '数据包别名，可包含用途';

comment on column comm_tcp_packet_type.valid_script is '有效脚本';

comment on column comm_tcp_packet_type.create_by is '创建用户编号';

comment on column comm_tcp_packet_type.create_time is '创建时间';

comment on column comm_tcp_packet_type.modify_by is '修改用户编号';

comment on column comm_tcp_packet_type.modify_time is '修改时间';

alter table comm_tcp_packet_type
    owner to phmdbadmin;

create index if not exists comm_tcp_packet_type_vehicle_type_id_idx
    on comm_tcp_packet_type (vehicle_type_id);

create table if not exists comm_tcp_user
(
    id              varchar(128) not null
        primary key,
    vehicle_type_id varchar(128),
    username        varchar(128) not null,
    password        varchar(128),
    enable          smallint default 1,
    create_by       varchar(128),
    create_time     timestamp(6),
    modify_by       varchar(128),
    modify_time     timestamp(6)
);

comment on table comm_tcp_user is 'TCP用户表';

comment on column comm_tcp_user.id is '主键';

comment on column comm_tcp_user.vehicle_type_id is '外键，引用车辆型号表的主键（id）';

comment on column comm_tcp_user.username is '用户名';

comment on column comm_tcp_user.password is '密码';

comment on column comm_tcp_user.enable is '使能状态，1：激活，0：未激活';

comment on column comm_tcp_user.create_by is '创建用户编号';

comment on column comm_tcp_user.create_time is '创建时间';

comment on column comm_tcp_user.modify_by is '修改用户编号';

comment on column comm_tcp_user.modify_time is '修改时间';

alter table comm_tcp_user
    owner to phmdbadmin;

create table if not exists comm_vehicle_tcp_protocol
(
    vehicle_id     varchar(128),
    protocol_id    varchar(128),
    packet_type_id varchar(128),
    valid          smallint default 1,
    create_by      varchar(128),
    create_time    timestamp(0),
    modify_by      varchar(128),
    modify_time    timestamp(0)
);

comment on column comm_vehicle_tcp_protocol.vehicle_id is '外键，引用车辆表的主键（id）';

comment on column comm_vehicle_tcp_protocol.protocol_id is '外键，引用协议表的主键（id）';

comment on column comm_vehicle_tcp_protocol.packet_type_id is '外键，引用数据包类型表的主键（id）';

comment on column comm_vehicle_tcp_protocol.valid is '有效标识符，1：有效，0：无效';

comment on column comm_vehicle_tcp_protocol.create_by is '创建用户编号';

comment on column comm_vehicle_tcp_protocol.create_time is '创建时间';

comment on column comm_vehicle_tcp_protocol.modify_by is '修改用户编号';

comment on column comm_vehicle_tcp_protocol.modify_time is '修改时间';

alter table comm_vehicle_tcp_protocol
    owner to phmdbadmin;

create table if not exists draw_line_config
(
    id          varchar(128)         not null
        primary key,
    segment_id  varchar(128),
    current_sta varchar(128),
    coordinate  varchar(255),
    json        varchar(255),
    path        varchar(4096),
    direction   varchar(64)          not null,
    line_id     varchar(128),
    type        varchar(255)         not null,
    sort        integer,
    create_by   varchar(128),
    create_time timestamp(0),
    modify_by   varchar(128),
    modify_time timestamp(0),
    del_flag    varchar(2) default 0 not null
);

comment on column draw_line_config.segment_id is '区段id';

comment on column draw_line_config.current_sta is '当前站sta_id';

comment on column draw_line_config.coordinate is '当前站坐标信息';

comment on column draw_line_config.json is '前端需要';

comment on column draw_line_config.path is '前端需要';

comment on column draw_line_config.direction is '上下行方向：up上行、down下行';

comment on column draw_line_config.line_id is '线路id';

comment on column draw_line_config.type is '类型：1车辆监控、2线路监控、3线网监控';

comment on column draw_line_config.sort is '排序';

comment on column draw_line_config.create_by is '创建用户';

comment on column draw_line_config.create_time is '创建时间';

comment on column draw_line_config.modify_by is '修改用户';

comment on column draw_line_config.modify_time is '修改时间';

comment on column draw_line_config.del_flag is '删除标识';

alter table draw_line_config
    owner to phmdbadmin;

create index if not exists draw_line_info
    on draw_line_config (line_id, type, del_flag);

create table if not exists dw_realtime_train_status_by_day
(
    time           text,
    train_id       text,
    signal_name_en text,
    accumlation    double precision,
    increament     double precision
);

comment on column dw_realtime_train_status_by_day.time is '统计日期';

comment on column dw_realtime_train_status_by_day.train_id is '车辆编码';

comment on column dw_realtime_train_status_by_day.signal_name_en is '信号英文名';

comment on column dw_realtime_train_status_by_day.accumlation is '累加值';

comment on column dw_realtime_train_status_by_day.increament is '日值';

alter table dw_realtime_train_status_by_day
    owner to phmdbadmin;

create table if not exists ekb_fault_avalanche
(
    id             varchar(128) not null
        primary key,
    enable         boolean,
    fault_type_key varchar(64),
    avalanche_info text
);

comment on column ekb_fault_avalanche.id is '主键id';

comment on column ekb_fault_avalanche.enable is '使能状态（t/f）';

comment on column ekb_fault_avalanche.fault_type_key is '故障类型业务主键，唯一标识一个故障类型(外键关联ekb_fault_type表)';

comment on column ekb_fault_avalanche.avalanche_info is '雪崩详情json';

alter table ekb_fault_avalanche
    owner to phmdbadmin;

create table if not exists ekb_fault_measure
(
    id              varchar(128) not null
        primary key,
    vehicle_type_id varchar(128) not null,
    measure_key     varchar(64)  not null,
    measure_code    varchar(64),
    content         text,
    category        varchar(32),
    create_by       varchar(128),
    create_time     timestamp(6),
    modify_by       varchar(128),
    modify_time     timestamp(0),
    line_id         varchar(128)
);

comment on table ekb_fault_measure is '故障处理措施';

comment on column ekb_fault_measure.id is '主键';

comment on column ekb_fault_measure.measure_key is '故障解决措施业务主键';

comment on column ekb_fault_measure.measure_code is '故障解决措施编码';

comment on column ekb_fault_measure.content is '故障措施内容';

comment on column ekb_fault_measure.category is '类别';

comment on column ekb_fault_measure.create_by is '创建用户编号';

comment on column ekb_fault_measure.create_time is '创建时间';

comment on column ekb_fault_measure.modify_by is '修改用户编号';

comment on column ekb_fault_measure.modify_time is '修改时间';

comment on column ekb_fault_measure.line_id is '线路id';

alter table ekb_fault_measure
    owner to phmdbadmin;

create table if not exists ekb_fault_reason
(
    id              varchar(128) not null
        primary key,
    vehicle_type_id varchar(128) not null,
    reason_key      varchar(64)  not null,
    reason_code     varchar(64)  not null,
    content         text,
    category        varchar(32),
    create_by       varchar(128),
    create_time     timestamp(6),
    modify_by       varchar(128),
    modify_time     timestamp(0),
    line_id         varchar(128)
);

comment on table ekb_fault_reason is '故障原因';

comment on column ekb_fault_reason.id is '主键';

comment on column ekb_fault_reason.vehicle_type_id is '外键，引用故障';

comment on column ekb_fault_reason.reason_key is '故障原因业务主键，通过车辆型号和原因编码生成';

comment on column ekb_fault_reason.reason_code is '故障原因编码';

comment on column ekb_fault_reason.content is '原因内容';

comment on column ekb_fault_reason.category is '类别';

comment on column ekb_fault_reason.create_by is '创建用户编号';

comment on column ekb_fault_reason.create_time is '创建时间';

comment on column ekb_fault_reason.modify_by is '修改用户编号';

comment on column ekb_fault_reason.modify_time is '修改时间';

comment on column ekb_fault_reason.line_id is '线路id';

alter table ekb_fault_reason
    owner to phmdbadmin;

create table if not exists ekb_fault_type
(
    id                                 varchar(128)         not null
        primary key,
    vehicle_type_id                    varchar(128)         not null,
    fault_type_key                     varchar(64)          not null,
    vehicle_structure_code             varchar(64),
    name_cn                            varchar(128)         not null,
    name_en                            varchar(128)         not null,
    fault_code                         varchar(64),
    fault_level                        smallint   default 3 not null,
    subsystem                          varchar(32),
    del_flag                           smallint   default 0 not null,
    create_by                          varchar(128),
    create_time                        timestamp(6),
    modify_by                          varchar(128),
    modify_time                        timestamp(0),
    description                        text,
    location                           varchar(256),
    enable                             boolean    default false,
    fault_mode                         varchar(2) default 0,
    frontline_disposal_recommendations text,
    overhaul_suggestions               text,
    fault_reason                       text,
    model_code                         varchar(255),
    line_id                            varchar(128),
    fault_category                     smallint
);

comment on table ekb_fault_type is '故障类型，包括车辆故障、子系统故障和部件故障，相当于故障字典，车辆经过安全完整性分析后的所有潜在故障。';

comment on column ekb_fault_type.vehicle_type_id is '外键，引用车辆型号表的主键（id）';

comment on column ekb_fault_type.fault_type_key is '故障类型业务主键，唯一标识一个故障类型';

comment on column ekb_fault_type.vehicle_structure_code is '外键，引用车辆构型表的构型编码（structure_code）';

comment on column ekb_fault_type.name_cn is '故障中文名称';

comment on column ekb_fault_type.name_en is '故障英文名称';

comment on column ekb_fault_type.fault_code is '故障编码';

comment on column ekb_fault_type.fault_level is '故障级别，0：事件 1：轻微 2：中等 3：严重';

comment on column ekb_fault_type.subsystem is '故障所属子系统';

comment on column ekb_fault_type.del_flag is '逻辑删除标识符，1：逻辑删除，0：未删除';

comment on column ekb_fault_type.create_by is '创建用户编号';

comment on column ekb_fault_type.create_time is '创建时间';

comment on column ekb_fault_type.modify_by is '修改用户编号';

comment on column ekb_fault_type.description is '故障描述';

comment on column ekb_fault_type.location is '故障所属车厢，A1/B1/C1/D1';

comment on column ekb_fault_type.enable is '使能状态(雪崩使能状态原先不为空 现在无相关业务去掉不为空)';

comment on column ekb_fault_type.fault_mode is '故障模式：0-告警, 1-预警';

comment on column ekb_fault_type.frontline_disposal_recommendations is '正线状态建议';

comment on column ekb_fault_type.overhaul_suggestions is '检修建议';

comment on column ekb_fault_type.fault_reason is '故障原因';

comment on column ekb_fault_type.model_code is '模型编码';

comment on column ekb_fault_type.line_id is '线路id';

comment on column ekb_fault_type.fault_category is '故障种类，1：自动上报，2：手动上报.....';

alter table ekb_fault_type
    owner to phmdbadmin;

create index if not exists ekb_fault_type_fault_type_key_idx
    on ekb_fault_type (fault_type_key);

create index if not exists ekb_fault_type_vehicle_type_id_fault_code_idx
    on ekb_fault_type (vehicle_type_id, fault_code);

create table if not exists ekb_fault_type_file
(
    fault_type_key varchar(64)  not null,
    file_id        varchar(128) not null,
    del_flag       smallint default 0,
    start_date     timestamp(6),
    end_date       timestamp(6)
);

comment on table ekb_fault_type_file is '故障类型文件';

comment on column ekb_fault_type_file.fault_type_key is '外键，引用故障类型表的主键（id）';

comment on column ekb_fault_type_file.start_date is '记录生效日期';

comment on column ekb_fault_type_file.end_date is '记录失效日期';

alter table ekb_fault_type_file
    owner to phmdbadmin;

create index if not exists ekb_fault_type_file_fault_type_key_idx
    on ekb_fault_type_file (fault_type_key);

create index if not exists ekb_fault_type_file_file_id_idx
    on ekb_fault_type_file (file_id);

create table if not exists ekb_fault_type_reason
(
    fault_reason_key varchar(64) not null,
    fault_type_key   varchar(64) not null,
    init_counter     bigint,
    real_counter     bigint
);

comment on table ekb_fault_type_reason is '故障类型原因';

comment on column ekb_fault_type_reason.fault_reason_key is '外键，引用故障原因表的业务主键（reason_key）';

comment on column ekb_fault_type_reason.fault_type_key is '外键，引用故障类型表的业务主键（fault_type_key）';

comment on column ekb_fault_type_reason.init_counter is '初始计数';

comment on column ekb_fault_type_reason.real_counter is '实际计数，每次发生故障计数+1';

alter table ekb_fault_type_reason
    owner to phmdbadmin;

create index if not exists ekb_fault_type_reason_fault_reason_key_idx
    on ekb_fault_type_reason (fault_reason_key);

create index if not exists ekb_fault_type_reason_fault_type_key_idx
    on ekb_fault_type_reason (fault_type_key);

create table if not exists ekb_fault_type_signal_group
(
    signal_group_id varchar(128) not null,
    fault_type_key  varchar(64)  not null
);

comment on column ekb_fault_type_signal_group.signal_group_id is '外键，引用信号分组表的主键（id）';

comment on column ekb_fault_type_signal_group.fault_type_key is '外键，引用故障类型表的业务主键（fault_type_key）';

alter table ekb_fault_type_signal_group
    owner to phmdbadmin;

create index if not exists ekb_fault_type_signal_group_signal_group_id_idx
    on ekb_fault_type_signal_group (signal_group_id);

create table if not exists ekb_reason_measure
(
    fault_type_key    varchar(64) not null,
    fault_reason_key  varchar(64) not null,
    init_counter      bigint,
    real_counter      bigint,
    fault_measure_key varchar(64) not null
);

comment on table ekb_reason_measure is '故障原因和故障措施关系';

comment on column ekb_reason_measure.fault_reason_key is '外键，引用故障原因表的业务主键';

comment on column ekb_reason_measure.init_counter is '相关概率';

comment on column ekb_reason_measure.fault_measure_key is '外键，引用故障处理措施表的主键（id）';

alter table ekb_reason_measure
    owner to phmdbadmin;

create index if not exists ekb_reason_measure_fault_measure_key_idx
    on ekb_reason_measure (fault_measure_key);

create index if not exists ekb_reason_measure_fault_reason_key_idx
    on ekb_reason_measure (fault_reason_key);

create table if not exists eva_health_result
(
    id                     varchar(128) not null,
    vehicle_id             varchar(128),
    vehicle_structure_code varchar(64),
    item_name              varchar(128) not null,
    item_value             varchar(32)  not null,
    type                   varchar(32),
    instruction            text,
    create_by              varchar(64),
    create_time            timestamp(6),
    line_id                varchar(128) default 1,
    date                   varchar(16)
);

comment on table eva_health_result is '健康评价结果';

comment on column eva_health_result.id is '主键';

comment on column eva_health_result.vehicle_id is '车辆编号';

comment on column eva_health_result.vehicle_structure_code is '外键，引用车辆构型表的业务主键（structure_code）';

comment on column eva_health_result.item_name is '项点名称';

comment on column eva_health_result.item_value is '项点值';

comment on column eva_health_result.type is '评分结果类型，车辆/子系统';

comment on column eva_health_result.instruction is '指导意见';

comment on column eva_health_result.create_by is '创建用户编号';

comment on column eva_health_result.create_time is '创建时间';

comment on column eva_health_result.line_id is '外键，引用线路表的主键（id）';

comment on column eva_health_result.date is '创建时间字符串，大数据用于去重';

alter table eva_health_result
    owner to phmdbadmin;

create table if not exists eva_health_rule
(
    id              varchar(128) not null
        primary key,
    vehicle_type_id varchar(128) not null,
    item_name       varchar(128) not null,
    item_value      varchar(32)  not null,
    unit            varchar(16),
    category        varchar(64),
    create_by       varchar(64),
    create_time     timestamp(6),
    modify_by       varchar(64),
    modify_time     timestamp(0),
    remark          text,
    line_id         varchar(128)
);

comment on table eva_health_rule is '健康评价规则';

comment on column eva_health_rule.id is '主键，健康评价规则编号';

comment on column eva_health_rule.vehicle_type_id is '外键，引用车辆类型表的主键（id）';

comment on column eva_health_rule.item_name is '规则名称';

comment on column eva_health_rule.item_value is '规则项点值';

comment on column eva_health_rule.unit is '项点单位';

comment on column eva_health_rule.category is '项点类别';

comment on column eva_health_rule.create_by is '创建用户编号';

comment on column eva_health_rule.create_time is '创建时间';

comment on column eva_health_rule.modify_by is '修改用户编号';

comment on column eva_health_rule.modify_time is '修改时间';

comment on column eva_health_rule.remark is '备注';

comment on column eva_health_rule.line_id is '线路id';

alter table eva_health_rule
    owner to phmdbadmin;

create index if not exists eva_health_rule_vehicle_type_id_idx
    on eva_health_rule (vehicle_type_id);

create table if not exists eva_rams_result
(
    id                varchar(128) not null
        primary key,
    vehicle_type_id   varchar(128),
    component_type_id varchar(128),
    item_name         varchar(128) not null,
    item_value        varchar(32)  not null,
    create_by         varchar(128),
    create_time       timestamp(6),
    item_category     varchar(64),
    line_id           varchar(128),
    unit              varchar(16)
);

comment on table eva_rams_result is 'RAMS评价结果';

comment on column eva_rams_result.id is '主键';

comment on column eva_rams_result.item_name is '结果项点名称';

comment on column eva_rams_result.item_value is '结果项点值';

comment on column eva_rams_result.create_by is '创建用户编号';

comment on column eva_rams_result.create_time is '创建时间';

comment on column eva_rams_result.unit is '项点单位';

alter table eva_rams_result
    owner to phmdbadmin;

create index if not exists eva_rams_result_component_type_id_idx
    on eva_rams_result (component_type_id);

create index if not exists eva_rams_result_vehicle_type_id_idx
    on eva_rams_result (vehicle_type_id);

create table if not exists eva_rams_rule
(
    id                varchar(128) not null
        primary key,
    vehicle_type_id   varchar(128),
    component_type_id varchar(128),
    item_name         varchar(128) not null,
    item_value        varchar(32)  not null,
    category          varchar(64),
    unit              varchar(16),
    create_by         varchar(128),
    create_time       timestamp(0),
    modify_by         varchar(128),
    modify_time       timestamp(0),
    remark            text
);

comment on table eva_rams_rule is 'RAMS评价参数';

comment on column eva_rams_rule.item_name is '规则项点名称';

comment on column eva_rams_rule.item_value is '规则项点值';

comment on column eva_rams_rule.category is '类别';

comment on column eva_rams_rule.unit is '单位';

comment on column eva_rams_rule.create_time is '创建时间';

comment on column eva_rams_rule.modify_by is '修改用户编号';

comment on column eva_rams_rule.modify_time is '修改时间';

comment on column eva_rams_rule.remark is '备注';

alter table eva_rams_rule
    owner to phmdbadmin;

create table if not exists fault_snow_slide
(
    id                    varchar(128)          not null
        primary key,
    vehicle_type_id       varchar(128)          not null,
    parent_fault_type_key varchar(64)           not null,
    fault_type_key        varchar(64)           not null,
    correlation_time      integer default 1000  not null,
    enable                boolean default false not null,
    create_by             varchar(128),
    create_time           timestamp(0),
    modify_by             varchar(128),
    modify_time           timestamp(0),
    remark                text,
    line_id               varchar(128),
    constraint fault_snow_slide_vehicle_type_id_parent_fault_type_key_faul_key
        unique (vehicle_type_id, parent_fault_type_key, fault_type_key)
);

comment on table fault_snow_slide is '故障雪崩';

comment on column fault_snow_slide.id is '主键';

comment on column fault_snow_slide.vehicle_type_id is '外键，引用车辆型号表的主键（id）';

comment on column fault_snow_slide.parent_fault_type_key is '父节点故障类型业务主键，外键，引用故障类型表的业务主键（fault_type_key）';

comment on column fault_snow_slide.fault_type_key is '当前节点故障类型业务主键，外键，引用故障类型表的业务主键（fault_type_key）';

comment on column fault_snow_slide.correlation_time is '相关时间，单位毫秒';

comment on column fault_snow_slide.enable is '使能状态';

comment on column fault_snow_slide.create_by is '创建用户编号';

comment on column fault_snow_slide.create_time is '创建时间';

comment on column fault_snow_slide.modify_by is '修改用户编号';

comment on column fault_snow_slide.modify_time is '修改时间';

comment on column fault_snow_slide.remark is '备注';

comment on column fault_snow_slide.line_id is '线路id';

alter table fault_snow_slide
    owner to phmdbadmin;

create table if not exists maintenance_period
(
    id              varchar(128) not null
        primary key,
    unique_id       varchar(128) not null,
    line_id         varchar(128),
    vehicle_type_id varchar(128),
    vehicle_code    varchar(128) not null,
    location        varchar(128),
    component_type  varchar(64),
    name_cn         varchar(255),
    period          varchar(64),
    mode            varchar(64),
    operator        varchar(64),
    operate_time    timestamp(0),
    warning_time    timestamp(0),
    change_time     timestamp(0),
    work_type       varchar(64),
    remark          varchar(255),
    create_by       varchar(64),
    create_time     timestamp(0),
    modify_by       varchar(64),
    modify_time     timestamp(0),
    carriage        varchar(255),
    del_flag        boolean          default false,
    initial_mileage double precision default 0,
    running_mileage double precision default 0,
    warning_mileage double precision default 0
);

comment on table maintenance_period is '维护周期';

comment on column maintenance_period.id is '主键id';

comment on column maintenance_period.unique_id is '唯一id';

comment on column maintenance_period.line_id is '线路id';

comment on column maintenance_period.vehicle_type_id is '车型id';

comment on column maintenance_period.vehicle_code is '车辆编码';

comment on column maintenance_period.location is '位置';

comment on column maintenance_period.component_type is '所属部件类型';

comment on column maintenance_period.name_cn is '中文名';

comment on column maintenance_period.period is '换油周期';

comment on column maintenance_period.mode is '洗车模式';

comment on column maintenance_period.operator is '操作者';

comment on column maintenance_period.operate_time is '操作时间';

comment on column maintenance_period.warning_time is '预警时间';

comment on column maintenance_period.change_time is '变更时间';

comment on column maintenance_period.work_type is '工作类型';

comment on column maintenance_period.remark is '备注';

comment on column maintenance_period.create_by is '创建人';

comment on column maintenance_period.create_time is '创建时间';

comment on column maintenance_period.modify_by is '修改人';

comment on column maintenance_period.modify_time is '修改时间';

comment on column maintenance_period.carriage is '车厢';

comment on column maintenance_period.del_flag is '删除状态true为已删除 false 为未删除';

comment on column maintenance_period.initial_mileage is '初始里程';

comment on column maintenance_period.running_mileage is '部件更换—期间的运行里程（只有更换部件的时候才更新更换日期的车辆里程数）';

comment on column maintenance_period.warning_mileage is '预警里程';

alter table maintenance_period
    owner to phmdbadmin;

create table if not exists monitor_menu
(
    id             varchar(128)          not null
        primary key,
    menu_code      varchar(128),
    parent_id      varchar(128),
    name           varchar(100),
    sort           varchar(20),
    board_status   boolean,
    menu_type      varchar(124),
    url            varchar(255),
    create_time    timestamp(0),
    create_by      varchar(64),
    modify_time    timestamp(0),
    modify_by      varchar(64),
    del_flag       boolean default false not null,
    show_status    boolean,
    tra_code       varchar(255),
    components_url varchar(512),
    default_active boolean
);

comment on table monitor_menu is '菜单表';

comment on column monitor_menu.menu_code is '菜单code';

comment on column monitor_menu.parent_id is '父级菜单id';

comment on column monitor_menu.name is '菜单名称';

comment on column monitor_menu.sort is '菜单排序数';

comment on column monitor_menu.board_status is '下方仪表盘启用状态';

comment on column monitor_menu.menu_type is '1.基础类型  2.url跳转  3.svg  4.(等等)';

comment on column monitor_menu.url is 'url跳转地址';

comment on column monitor_menu.modify_time is '修改时间';

comment on column monitor_menu.del_flag is '删除标识符';

comment on column monitor_menu.show_status is '菜单显示隐藏状态 t显示f隐藏';

comment on column monitor_menu.tra_code is '所属车辆';

comment on column monitor_menu.components_url is '前端组件地址';

comment on column monitor_menu.default_active is '默认激活（前端展示默认首页）';

alter table monitor_menu
    owner to phmdbadmin;

create index if not exists "monitor_menu_trainCode_idx"
    on monitor_menu (tra_code varchar_ops);

create table if not exists monitor_page_info
(
    id           varchar(128)          not null
        primary key,
    menu_code    varchar(64),
    vehicle_type varchar(64),
    html_content text,
    css_content  text,
    line_id      varchar(64),
    create_time  timestamp(0),
    create_by    varchar(40),
    modify_time  timestamp(0),
    modify_by    varchar(40),
    del_flag     boolean default false not null,
    type         varchar(255)          not null
);

comment on column monitor_page_info.id is '主键id';

comment on column monitor_page_info.menu_code is '所属菜单id';

comment on column monitor_page_info.html_content is 'html内容';

comment on column monitor_page_info.css_content is 'css内容';

comment on column monitor_page_info.line_id is '线路id';

comment on column monitor_page_info.modify_time is '修改时间';

comment on column monitor_page_info.del_flag is '删除标识符';

comment on column monitor_page_info.type is 'css/html 两个类型';

alter table monitor_page_info
    owner to phmdbadmin;

create table if not exists monitor_signal_function
(
    id            varchar(128)          not null
        primary key,
    type          varchar(64),
    name          varchar(64),
    function_str  text,
    create_time   timestamp(0),
    create_by     varchar(64),
    modify_time   timestamp(0),
    modify_by     varchar(64),
    del_flag      boolean default false not null,
    description   varchar(255),
    function_name varchar(255),
    redis_key     varchar(255)
);

comment on table monitor_signal_function is 'js处理函数表';

comment on column monitor_signal_function.type is '车型id外键';

comment on column monitor_signal_function.name is '函数名称';

comment on column monitor_signal_function.function_str is 'js函数字符串';

comment on column monitor_signal_function.modify_time is '修改时间';

comment on column monitor_signal_function.del_flag is '删除标识符';

comment on column monitor_signal_function.description is '脚本描述';

comment on column monitor_signal_function.function_name is '脚本函数名';

comment on column monitor_signal_function.redis_key is '数据所在的key值';

alter table monitor_signal_function
    owner to phmdbadmin;

create table if not exists monitor_slot
(
    id              varchar(128)           not null
        primary key,
    table_format_id varchar(64)            not null,
    sort            smallint default 0,
    location        varchar(32),
    slot_type       smallint default 0,
    create_time     timestamp(0),
    create_by       varchar(64),
    modify_time     timestamp(0),
    modify_by       varchar(64),
    del_flag        boolean  default false not null
);

comment on table monitor_slot is '监控配置的最小单元 卡槽';

comment on column monitor_slot.table_format_id is '外键（table_format主键）';

comment on column monitor_slot.sort is '修改为判断slot排序字段/*卡槽对应的trigger数量 （用于查询卡槽是否被配置，当更新卡槽对应的trigger时需要进行一次维护例如新增对应卡槽的trigger时（一条或多条）+x 删除时(单条操作)－1 更新和查询无需修改）*/';

comment on column monitor_slot.location is '卡槽所在位置';

comment on column monitor_slot.slot_type is '0：代表未配置  1：代表信号  2：代表文本';

comment on column monitor_slot.modify_time is '修改时间';

comment on column monitor_slot.del_flag is '删除标识符';

alter table monitor_slot
    owner to phmdbadmin;

create table if not exists monitor_table_format
(
    id               varchar(128)           not null
        primary key,
    item_id          varchar(64)            not null,
    n_row            smallint default 0,
    n_column         smallint default 0,
    vehicle_location varchar(64)            not null,
    create_time      timestamp(0),
    create_by        varchar(64),
    modify_time      timestamp(0),
    modify_by        varchar(64),
    del_flag         boolean  default false not null,
    sort             varchar(64)
);

comment on table monitor_table_format is '单元格表';

comment on column monitor_table_format.item_id is '外键';

comment on column monitor_table_format.n_row is '行';

comment on column monitor_table_format.n_column is '列';

comment on column monitor_table_format.vehicle_location is '车厢';

comment on column monitor_table_format.modify_time is '修改时间';

comment on column monitor_table_format.del_flag is '删除标识符';

comment on column monitor_table_format.sort is '排序';

alter table monitor_table_format
    owner to phmdbadmin;

create table if not exists monitor_table_item
(
    id                 varchar(128)           not null
        primary key,
    menu_id            varchar(64),
    name               varchar(100),
    type               varchar(64),
    sort               varchar(64),
    car_type           varchar(100),
    signal_function_id varchar(64),
    create_time        timestamp(0),
    create_by          varchar(64),
    modify_time        timestamp(0),
    modify_by          varchar(64),
    del_flag           boolean  default false not null,
    pub_and_pri        smallint default 0,
    relation_key       varchar(124),
    slot_board_id      varchar(64)
);

comment on table monitor_table_item is '每辆车的每个菜单实际对应的项点';

comment on column monitor_table_item.menu_id is '菜单id';

comment on column monitor_table_item.name is '项点name';

comment on column monitor_table_item.type is 'car车厢项点，basic基本项点,board仪表盘项点customTemplate自定义模板项点';

comment on column monitor_table_item.sort is '排序';

comment on column monitor_table_item.car_type is '车厢类型（当项点类型为车厢项点时 此字段不能为空）';

comment on column monitor_table_item.signal_function_id is '项点对应的业务处理脚本';

comment on column monitor_table_item.modify_time is '修改时间';

comment on column monitor_table_item.del_flag is '删除标识符';

comment on column monitor_table_item.pub_and_pri is '1公共项点 0私有项点';

comment on column monitor_table_item.relation_key is '关联键值（websocket返回数据时的固定key值）';

comment on column monitor_table_item.slot_board_id is '所属板卡id（对应的板卡slot的主键）';

alter table monitor_table_item
    owner to phmdbadmin;

create table if not exists monitor_trigger
(
    id                 varchar(128)          not null
        primary key,
    slot_id            varchar(64),
    label              varchar(100),
    sort               integer default 1,
    signal_id          varchar(128),
    trigger_value      varchar(64),
    data_display_point varchar(100),
    unit_status        boolean,
    svg_url            varchar(255),
    ext_properties     varchar(1024),
    create_time        timestamp(0),
    create_by          varchar(64),
    modify_time        timestamp(0),
    modify_by          varchar(64),
    del_flag           boolean default false not null,
    image_type         varchar(255),
    image_path         varchar(255),
    signal_name_en     varchar(64),
    mark               varchar(8)
);

comment on table monitor_trigger is '触发值与样式表';

comment on column monitor_trigger.label is '名称（多做描述或者文字类型trigger的显示部分）';

comment on column monitor_trigger.sort is '排序';

comment on column monitor_trigger.signal_id is '绑定的信号id';

comment on column monitor_trigger.trigger_value is '触发值';

comment on column monitor_trigger.data_display_point is '展示样式的类型（文字、图片）';

comment on column monitor_trigger.unit_status is 't为有单位 f无单位 单位可以从redis中获取';

comment on column monitor_trigger.svg_url is '图片地址';

comment on column monitor_trigger.ext_properties is '保存信号的扩展属性（json串保存的key value）';

comment on column monitor_trigger.modify_time is '修改时间';

comment on column monitor_trigger.del_flag is '删除标识符';

comment on column monitor_trigger.image_type is '图片类型';

comment on column monitor_trigger.image_path is '图片地址';

comment on column monitor_trigger.signal_name_en is '绑定的信号英文名唯一标识';

comment on column monitor_trigger.mark is '前期配置信号标记使用';

alter table monitor_trigger
    owner to phmdbadmin;

create index if not exists index_signal_id
    on monitor_trigger (signal_id);

create index if not exists index_slot_id
    on monitor_trigger (slot_id);

create table if not exists mtc_alarm_warning
(
    id                     varchar(128) not null
        constraint mtc_alarm_warning_pk
            primary key,
    line_id                varchar(128),
    vehicle_id             varchar(128) not null,
    vehicle_type_id        varchar(128),
    vehicle_structure_code varchar(200),
    fault_type_key         varchar(64),
    component_id           varchar(128),
    subsystem              varchar(200),
    location               varchar(64),
    fault_name_cn          varchar(128),
    fault_name_en          varchar(128),
    fault_code             varchar(128),
    fault_level            smallint,
    alarm_rule_id          varchar(128),
    alarm_snapshot         text,
    start_time             timestamp(0) not null,
    end_time               timestamp(0),
    status                 varchar(2)   default 0,
    running_status         boolean      default true,
    suppressed_by          varchar(64),
    suppressed_status      boolean      default false,
    fault_source           varchar(2),
    create_time            timestamp(6) default now(),
    modify_time            timestamp(6),
    modify_by              varchar(128),
    end_status             integer      default 0,
    params                 text
);

comment on table mtc_alarm_warning is '告警预警表';

comment on column mtc_alarm_warning.id is '主键，故障编号';

comment on column mtc_alarm_warning.line_id is '线路编号';

comment on column mtc_alarm_warning.vehicle_id is '车辆主键';

comment on column mtc_alarm_warning.vehicle_type_id is '车型代码';

comment on column mtc_alarm_warning.vehicle_structure_code is '车辆构型代码';

comment on column mtc_alarm_warning.fault_type_key is '外键，引用故障类型表的业务主键（fault_type_key）';

comment on column mtc_alarm_warning.component_id is '外键，引用部件表的主键（id）';

comment on column mtc_alarm_warning.subsystem is '子系统';

comment on column mtc_alarm_warning.location is '故障位置';

comment on column mtc_alarm_warning.fault_name_cn is '故障中文名';

comment on column mtc_alarm_warning.fault_name_en is '故障英文名';

comment on column mtc_alarm_warning.fault_code is '故障编码';

comment on column mtc_alarm_warning.fault_level is '故障等级';

comment on column mtc_alarm_warning.alarm_rule_id is '外键，引用预警规则表的主键（id）';

comment on column mtc_alarm_warning.alarm_snapshot is '机理故障快照，保存故障时刻的参数';

comment on column mtc_alarm_warning.start_time is '故障发生时间';

comment on column mtc_alarm_warning.end_time is '故障结束时间';

comment on column mtc_alarm_warning.status is '处理状态：0未提报，1已提报，2已处理';

comment on column mtc_alarm_warning.running_status is '正线状态：正线true，非正线false';

comment on column mtc_alarm_warning.suppressed_by is '外键，抑制故障父节点，故障类型表的业务主键（fault_type_key）';

comment on column mtc_alarm_warning.suppressed_status is '雪崩抑制状态 ：抑制false，被抑制true';

comment on column mtc_alarm_warning.fault_source is '故障来源：0系统信号，1机理规则';

comment on column mtc_alarm_warning.modify_time is '修改时间';

comment on column mtc_alarm_warning.modify_by is '修改人id';

comment on column mtc_alarm_warning.end_status is '解除状态 0未解除 1解除';

comment on column mtc_alarm_warning.params is 'JSON数据(mtc_signal_params_relation配置的信号)';

alter table mtc_alarm_warning
    owner to phmdbadmin;

create index if not exists mtc_alarm_warning_start_time_index
    on mtc_alarm_warning (start_time);

comment on index mtc_alarm_warning_start_time_index is '发生时间索引';

create index if not exists mtc_alarm_warning_vehicle_id_index
    on mtc_alarm_warning (vehicle_id);

comment on index mtc_alarm_warning_vehicle_id_index is '车辆索引';

create table if not exists mtc_mechanism_alarm_record
(
    id             varchar(128) not null
        primary key,
    vehicle_id     varchar(128) not null,
    alarm_rule_id  varchar(128) not null,
    alarm_snapshot text,
    start_time     timestamp(0) not null,
    end_time       timestamp(0),
    status         varchar(16),
    modify_time    timestamp(6),
    modify_by      varchar(128)
);

comment on table mtc_mechanism_alarm_record is '机理故障记录';

comment on column mtc_mechanism_alarm_record.id is '主键';

comment on column mtc_mechanism_alarm_record.vehicle_id is '外键，引用车辆表的主键（id）';

comment on column mtc_mechanism_alarm_record.alarm_rule_id is '外键，引用预警规则表的主键（id）';

comment on column mtc_mechanism_alarm_record.alarm_snapshot is '机理故障快照，保存故障时刻的参数';

comment on column mtc_mechanism_alarm_record.start_time is '开始时间';

comment on column mtc_mechanism_alarm_record.end_time is '结束时间';

comment on column mtc_mechanism_alarm_record.status is '处理状态';

comment on column mtc_mechanism_alarm_record.modify_time is '修改时间';

comment on column mtc_mechanism_alarm_record.modify_by is '修改用户编号';

alter table mtc_mechanism_alarm_record
    owner to phmdbadmin;

create index if not exists mtc_mechanism_alarm_record_alarm_rule_id_idx
    on mtc_mechanism_alarm_record (alarm_rule_id);

create table if not exists mtc_mechanism_alarm_rule
(
    id                varchar(128)               not null
        primary key,
    vehicle_type_id   varchar(128)               not null,
    fault_type_key    varchar(64)                not null,
    name              varchar(64)                not null,
    content           text,
    enable            boolean      default false not null,
    image_file        varchar(128),
    description       text,
    modify_by         varchar(128),
    modify_time       timestamp(6),
    create_by         varchar(128),
    create_time       timestamp(6),
    line_id           varchar(128),
    signal_cycle      varchar(256) default '512ms'::character varying,
    test_status       boolean      default false,
    encryption_status boolean      default false,
    constraint vehicle_name_unique
        unique (vehicle_type_id, name)
);

comment on table mtc_mechanism_alarm_rule is '机理故障规则';

comment on column mtc_mechanism_alarm_rule.id is '主键';

comment on column mtc_mechanism_alarm_rule.vehicle_type_id is '外键，引用车辆型号表的主键（id）';

comment on column mtc_mechanism_alarm_rule.fault_type_key is '外键，引用故障类型表的业务主键（fault_type_key）';

comment on column mtc_mechanism_alarm_rule.name is '规则名称';

comment on column mtc_mechanism_alarm_rule.content is '规则内容，JSON格式';

comment on column mtc_mechanism_alarm_rule.enable is '使能标志位';

comment on column mtc_mechanism_alarm_rule.image_file is '树图文件';

comment on column mtc_mechanism_alarm_rule.description is '规则描述';

comment on column mtc_mechanism_alarm_rule.modify_by is '修改用户编号';

comment on column mtc_mechanism_alarm_rule.modify_time is '修改时间';

comment on column mtc_mechanism_alarm_rule.create_by is '创建用户';

comment on column mtc_mechanism_alarm_rule.create_time is '创建时间';

comment on column mtc_mechanism_alarm_rule.line_id is '线路id';

comment on column mtc_mechanism_alarm_rule.signal_cycle is '64ms、512ms ';

comment on column mtc_mechanism_alarm_rule.test_status is '机理规则加密状态';

comment on column mtc_mechanism_alarm_rule.encryption_status is '机理规则加密状态 true:加密 false:未加密';

comment on constraint vehicle_name_unique on mtc_mechanism_alarm_rule is '一个车型下的机理模型名称互不相同';

alter table mtc_mechanism_alarm_rule
    owner to phmdbadmin;

create table if not exists mtc_mechanism_variable_rule
(
    id              varchar(128)          not null
        primary key,
    vehicle_type_id varchar(128)          not null,
    name            varchar(64)           not null,
    content         text,
    enable          boolean default false not null,
    image_file      varchar(128),
    description     text,
    modify_by       varchar(128),
    modify_time     timestamp(6),
    create_by       varchar(128),
    create_time     timestamp(6),
    location        varchar(32)           not null,
    subsystem       varchar(32)           not null,
    line_id         varchar(128),
    constraint variable_name_unique
        unique (vehicle_type_id, name)
);

comment on column mtc_mechanism_variable_rule.line_id is '线路id';

alter table mtc_mechanism_variable_rule
    owner to phmdbadmin;

create table if not exists mtr_component_software
(
    id           varchar(128)         not null
        primary key,
    vehicle_id   varchar(128),
    component_id varchar(128)         not null,
    software_id  varchar(128)         not null,
    plan_time    timestamp(0),
    update_time  timestamp(0),
    valid        boolean default true not null
);

comment on table mtr_component_software is '部件软件，更新部件软件时，并不删除关系记录，仅把有效标志置成False';

comment on column mtr_component_software.id is '主键';

comment on column mtr_component_software.component_id is '外键，引用部件表的主键（id）';

comment on column mtr_component_software.software_id is '外键，引用软件表的主键（id）';

comment on column mtr_component_software.plan_time is '计划更新软件时间';

comment on column mtr_component_software.update_time is '软件实际更新时间';

comment on column mtr_component_software.valid is '有效标志';

alter table mtr_component_software
    owner to phmdbadmin;

create index if not exists mtr_component_software_component_id_idx
    on mtr_component_software (component_id);

create index if not exists mtr_component_software_software_id_idx
    on mtr_component_software (software_id);

create table if not exists mtr_component_type_contacts
(
    manufacturer_employee_id varchar(128) not null,
    component_type_id        varchar(128) not null
);

comment on table mtr_component_type_contacts is '联系人，负责部件的技术支持、商务和采购等。';

comment on column mtr_component_type_contacts.manufacturer_employee_id is '外键，引用制造商雇员表的主键（id）';

comment on column mtr_component_type_contacts.component_type_id is '外键，引用部件类型表主键（id）';

alter table mtr_component_type_contacts
    owner to phmdbadmin;

create index if not exists mtr_component_type_contacts_component_type_id_idx
    on mtr_component_type_contacts (component_type_id);

create index if not exists mtr_component_type_contacts_manufacturer_employee_id_idx
    on mtr_component_type_contacts (manufacturer_employee_id);

create table if not exists mtr_component_type_software
(
    component_type_id varchar(128) not null,
    software_id       varchar(128) not null,
    vehicle_type_id   varchar(128) not null
);

comment on column mtr_component_type_software.vehicle_type_id is '外键，引用车辆型号表的主键（id）';

alter table mtr_component_type_software
    owner to phmdbadmin;

create index if not exists mtr_component_type_software_component_type_id_idx
    on mtr_component_type_software (component_type_id);

create index if not exists mtr_component_type_software_software_id_idx
    on mtr_component_type_software (software_id);

create index if not exists mtr_component_type_software_vehicle_type_id_idx
    on mtr_component_type_software (vehicle_type_id);

create table if not exists mtr_design_parameter
(
    id                varchar(128) not null
        primary key,
    component_type_id varchar(128),
    vehicle_type_id   varchar(128),
    item_name         varchar(128) not null,
    item_value        varchar(32)  not null,
    create_by         varchar(128),
    create_time       timestamp(0),
    modify_by         varchar(128),
    modify_time       timestamp(0)
);

comment on table mtr_design_parameter is '部件（车辆）设计参数';

comment on column mtr_design_parameter.id is '主键';

comment on column mtr_design_parameter.item_name is '设计参数名称';

comment on column mtr_design_parameter.item_value is '设计参数值';

comment on column mtr_design_parameter.create_by is '创建用户编号';

comment on column mtr_design_parameter.create_time is '创建时间';

comment on column mtr_design_parameter.modify_by is '修改用户编号';

comment on column mtr_design_parameter.modify_time is '修改时间';

alter table mtr_design_parameter
    owner to phmdbadmin;

create index if not exists mtr_design_parameter_component_type_id_idx
    on mtr_design_parameter (component_type_id);

create index if not exists mtr_design_parameter_vehicle_type_id_idx
    on mtr_design_parameter (vehicle_type_id);

create table if not exists mtr_line
(
    id               varchar(128) not null
        primary key,
    name             varchar(128) not null,
    location_id      varchar(128),
    mileage          integer      not null,
    longitude        double precision,
    latitude         double precision,
    create_by        varchar(128),
    create_time      timestamp(6),
    modify_by        varchar(128),
    modify_time      timestamp(0),
    remark           text,
    color            varchar(16),
    del_flag         smallint     not null,
    project_asc_code varchar(255),
    direction_status varchar(8)
);

comment on table mtr_line is '地铁线路';

comment on column mtr_line.id is '地铁线路编号';

comment on column mtr_line.name is '线路名称';

comment on column mtr_line.location_id is '外键，引用区域表的主键（id）';

comment on column mtr_line.mileage is '线路里程，单位：km';

comment on column mtr_line.longitude is '经度';

comment on column mtr_line.latitude is '纬度';

comment on column mtr_line.create_by is '用户编号';

comment on column mtr_line.create_time is '创建时间';

comment on column mtr_line.modify_by is '修改用户编号';

comment on column mtr_line.modify_time is '修改时间';

comment on column mtr_line.remark is '备注';

comment on column mtr_line.color is '线路颜色值';

comment on column mtr_line.del_flag is '删除标识';

comment on column mtr_line.direction_status is '判断是否分上下行：1：分上下行，2：不分';

alter table mtr_line
    owner to phmdbadmin;

create index if not exists mtr_line_location_id_idx
    on mtr_line (location_id);

create table if not exists mtr_manufacturer
(
    id          varchar(128) not null
        primary key,
    name        varchar(128) not null,
    email       varchar(128),
    phone       varchar(32),
    website     varchar(255),
    profile     text,
    address     varchar(255),
    create_by   varchar(128),
    create_time timestamp(0),
    modify_by   varchar(128),
    modify_time timestamp(6),
    remark      text
);

comment on table mtr_manufacturer is '制造商';

comment on column mtr_manufacturer.id is '主键，制造商编号';

comment on column mtr_manufacturer.name is '制造商名称';

comment on column mtr_manufacturer.email is '制造商电子邮件';

comment on column mtr_manufacturer.phone is '制造商联系电话';

comment on column mtr_manufacturer.website is '制造商网址';

comment on column mtr_manufacturer.profile is '制造商简介';

comment on column mtr_manufacturer.address is '地址';

comment on column mtr_manufacturer.create_by is '创建用户编号';

comment on column mtr_manufacturer.create_time is '创建时间';

comment on column mtr_manufacturer.modify_by is '修改用户编号';

comment on column mtr_manufacturer.modify_time is '修改时间';

comment on column mtr_manufacturer.remark is '备注';

alter table mtr_manufacturer
    owner to phmdbadmin;

create table if not exists mtr_manufacturer_employee
(
    id              varchar(128)                                  not null
        primary key,
    name            varchar(128)                                  not null,
    gender          varchar(16) default 'male'::character varying not null,
    manufacturer_id varchar(128)                                  not null,
    email           varchar(128),
    phone           varchar(32),
    profile         text,
    type            varchar(32),
    create_by       varchar(128),
    create_time     timestamp(6),
    modify_by       varchar(128),
    modify_time     timestamp(6),
    remark          text
);

comment on table mtr_manufacturer_employee is '制造商雇员';

comment on column mtr_manufacturer_employee.id is '主键，制造商编号';

comment on column mtr_manufacturer_employee.name is '雇员姓名';

comment on column mtr_manufacturer_employee.gender is '性别';

comment on column mtr_manufacturer_employee.manufacturer_id is '外键，引用制造商表的主键（id）';

comment on column mtr_manufacturer_employee.email is '电子邮件';

comment on column mtr_manufacturer_employee.phone is '联系电话';

comment on column mtr_manufacturer_employee.profile is '雇员基本情况';

comment on column mtr_manufacturer_employee.type is '雇员类型，技术支持/商务/销售/其它';

comment on column mtr_manufacturer_employee.create_by is '创建用户编号';

comment on column mtr_manufacturer_employee.create_time is '创建时间';

comment on column mtr_manufacturer_employee.modify_by is '修改用户编号';

comment on column mtr_manufacturer_employee.modify_time is '修改时间';

comment on column mtr_manufacturer_employee.remark is '备注';

alter table mtr_manufacturer_employee
    owner to phmdbadmin;

create table if not exists mtr_manufacturer_file
(
    manufacturer_id varchar(128) not null,
    file_id         varchar(128) not null,
    del_flag        smallint default 0,
    start_date      timestamp(6),
    end_date        timestamp(6)
);

comment on table mtr_manufacturer_file is '制造商文件，包含包括资质文件、执照文件等。';

comment on column mtr_manufacturer_file.manufacturer_id is '外键，引用制造商表的主键（id）';

comment on column mtr_manufacturer_file.file_id is '外键，引用文件表的主键（id）';

comment on column mtr_manufacturer_file.del_flag is '默认为0';

comment on column mtr_manufacturer_file.start_date is '记录生效时间 ';

comment on column mtr_manufacturer_file.end_date is '记录失效时间';

alter table mtr_manufacturer_file
    owner to phmdbadmin;

create index if not exists mtr_manufacturer_file_file_id_idx
    on mtr_manufacturer_file (file_id);

create index if not exists mtr_manufacturer_file_manufacturer_id_idx
    on mtr_manufacturer_file (manufacturer_id);

create table if not exists mtr_manufacturing_param
(
    id           varchar(128) not null
        primary key,
    vehicle_id   varchar(128),
    component_id varchar(128),
    item_name    varchar(128) not null,
    item_value   varchar(32)  not null,
    create_time  timestamp(6),
    create_by    varchar(128),
    modify_by    varchar(128),
    modify_time  timestamp(0)
);

comment on table mtr_manufacturing_param is '部件（车辆）制造参数';

comment on column mtr_manufacturing_param.id is '主键';

comment on column mtr_manufacturing_param.vehicle_id is '外键，引用车辆表的主键（id）';

comment on column mtr_manufacturing_param.component_id is '外键，引用部件表的主键（id）';

comment on column mtr_manufacturing_param.item_name is '项点名称';

comment on column mtr_manufacturing_param.item_value is '项点值';

comment on column mtr_manufacturing_param.modify_by is '修改用户编号';

comment on column mtr_manufacturing_param.modify_time is '修改时间';

alter table mtr_manufacturing_param
    owner to phmdbadmin;

create index if not exists mtr_manufacturing_param_component_id_idx
    on mtr_manufacturing_param (component_id);

create index if not exists mtr_manufacturing_param_vehicle_id_idx
    on mtr_manufacturing_param (vehicle_id);

create table if not exists mtr_operating_param
(
    id           varchar(128) not null
        primary key,
    vehicle_id   varchar(128),
    component_id varchar(128),
    item_name_cn varchar(128) not null,
    item_name_en varchar(128),
    item_value   varchar(32)  not null,
    unit         varchar(16),
    create_by    varchar(128),
    create_time  timestamp(6),
    modify_by    varchar(128),
    modify_time  timestamp(0)
);

comment on table mtr_operating_param is '部件（车辆）运行参数';

comment on column mtr_operating_param.id is '主键';

comment on column mtr_operating_param.vehicle_id is '外键，引用车辆表的主键（id）';

comment on column mtr_operating_param.component_id is '外键，引用部件表的主键（id）';

comment on column mtr_operating_param.item_name_cn is '项点中文名称';

comment on column mtr_operating_param.item_name_en is '项点英文名称';

comment on column mtr_operating_param.item_value is '项点值';

comment on column mtr_operating_param.unit is '参数单位';

comment on column mtr_operating_param.create_by is '创建用户编号';

comment on column mtr_operating_param.create_time is '创建时间';

comment on column mtr_operating_param.modify_by is '修改用户编号';

comment on column mtr_operating_param.modify_time is '修改时间';

alter table mtr_operating_param
    owner to phmdbadmin;

create index if not exists mtr_operating_param_component_id_idx
    on mtr_operating_param (component_id);

create index if not exists mtr_operating_param_vehicle_id_idx
    on mtr_operating_param (vehicle_id);

create table if not exists mtr_org_metro_line
(
    metro_line_id   varchar(128) not null,
    organization_id varchar(128) not null
);

comment on table mtr_org_metro_line is '组织车辆';

comment on column mtr_org_metro_line.metro_line_id is '外键，引用车辆表的主键（id）';

comment on column mtr_org_metro_line.organization_id is '外键，引用组织表的主键（id）';

alter table mtr_org_metro_line
    owner to phmdbadmin;

create index if not exists mtr_org_metro_line_metro_line_id_idx
    on mtr_org_metro_line (metro_line_id);

create index if not exists mtr_org_metro_line_organization_id_idx
    on mtr_org_metro_line (organization_id);

create table if not exists mtr_rams_param
(
    id                varchar(128) not null
        primary key,
    vehicle_type_id   varchar(128),
    component_type_id varchar(128),
    item_name         varchar(128) not null,
    item_value        varchar(32)  not null,
    unit              varchar(16),
    create_by         varchar(128),
    create_time       timestamp(6),
    modify_by         varchar(128),
    modify_time       timestamp(0),
    item_category     varchar(64)
);

comment on table mtr_rams_param is '设计RAMS参数';

comment on column mtr_rams_param.id is '主键';

comment on column mtr_rams_param.vehicle_type_id is '外键，引用车辆型号表的主键（id）';

comment on column mtr_rams_param.component_type_id is '外键，引用部件型号表的主键（id）';

comment on column mtr_rams_param.item_name is '项点名称';

comment on column mtr_rams_param.item_value is '项点值';

comment on column mtr_rams_param.unit is '项点单位';

comment on column mtr_rams_param.create_by is '创建用户编号';

comment on column mtr_rams_param.create_time is '创建时间';

comment on column mtr_rams_param.modify_by is '修改用户编号';

comment on column mtr_rams_param.modify_time is '修改时间';

alter table mtr_rams_param
    owner to phmdbadmin;

create index if not exists mtr_rams_param_component_type_id_idx
    on mtr_rams_param (component_type_id);

create index if not exists mtr_rams_param_vehicle_type_id_idx
    on mtr_rams_param (vehicle_type_id);

create table if not exists mtr_software
(
    id                     varchar(128) not null
        primary key,
    name                   varchar(128) not null,
    version                varchar(32)  not null,
    manufacturer_id        varchar(128),
    description            text,
    create_by              varchar(128),
    create_time            timestamp(0),
    modify_by              varchar(128),
    modify_time            timestamp(0),
    vehicle_structure_code varchar(64),
    vehicle_code           varchar(255),
    end_time               timestamp,
    signal_name_en         varchar(255),
    operator               varchar(255),
    effective_time         timestamp,
    line_id                varchar(128),
    subsystem              varchar,
    location               varchar,
    constraint mtr_software_name_version_key
        unique (name, version, signal_name_en, vehicle_code)
);

comment on table mtr_software is '软件版本，软件名称和版本号联合唯一。';

comment on column mtr_software.id is '主键';

comment on column mtr_software.name is '软件名称';

comment on column mtr_software.version is '软件版本号';

comment on column mtr_software.manufacturer_id is '外键，引用供应商表的主键（id）';

comment on column mtr_software.description is '版本描述';

comment on column mtr_software.create_by is '创建用户';

comment on column mtr_software.create_time is '创建时间';

comment on column mtr_software.modify_by is '修改用户编号';

comment on column mtr_software.modify_time is '修改时间';

comment on column mtr_software.vehicle_structure_code is '外键，引用车辆构型表的构型编码（structure_code）';

comment on column mtr_software.vehicle_code is '车辆编码';

comment on column mtr_software.end_time is '软件版本的结束时间（若有值则说明为历史记录）';

comment on column mtr_software.signal_name_en is '软件英文名（信号名称）';

comment on column mtr_software.operator is '操作者';

comment on column mtr_software.effective_time is '软件版本生效日期';

comment on column mtr_software.line_id is '线路id';

comment on column mtr_software.subsystem is '软件对应子系统';

comment on column mtr_software.location is '车厢位置';

alter table mtr_software
    owner to phmdbadmin;

create index if not exists mtr_software_name_idx
    on mtr_software (name);

create table if not exists mtr_software_file
(
    software_id varchar(128) not null,
    file_id     varchar(128) not null,
    del_flag    smallint default 0,
    start_date  timestamp(6),
    end_date    timestamp(6)
);

comment on column mtr_software_file.software_id is '外键，引用软件表的主键（id）';

comment on column mtr_software_file.file_id is '外键，引用文件表的主键（id）';

comment on column mtr_software_file.del_flag is '默认为0';

alter table mtr_software_file
    owner to phmdbadmin;

create index if not exists mtr_software_file_file_id_idx
    on mtr_software_file (file_id);

create index if not exists mtr_software_file_software_id_idx
    on mtr_software_file (software_id);

create table if not exists mtr_software_mapping
(
    id              varchar(128)       not null
        primary key,
    signal_name_en  varchar(255)       not null,
    name            varchar(255)       not null,
    signal_list     varchar(255),
    line_id         varchar(128),
    vehicle_type_id varchar(128),
    structure_code  varchar(255),
    create_by       varchar(128),
    create_time     timestamp(0),
    modify_by       varchar(128),
    modify_time     timestamp(0),
    del_flag        smallint default 0 not null,
    subsystem       varchar,
    location        varchar
);

comment on column mtr_software_mapping.id is '主键';

comment on column mtr_software_mapping.signal_name_en is '软件版本英文名（同软件不同版本 此值唯一）';

comment on column mtr_software_mapping.name is '软件中文名称';

comment on column mtr_software_mapping.signal_list is '采集信号（存在一个信号 或者多个信号的情况）';

comment on column mtr_software_mapping.line_id is '关联线路id';

comment on column mtr_software_mapping.vehicle_type_id is '关联车型id';

comment on column mtr_software_mapping.structure_code is '关联车型构型编码';

comment on column mtr_software_mapping.create_by is '创建用户';

comment on column mtr_software_mapping.create_time is '创建时间';

comment on column mtr_software_mapping.modify_by is '修改用户编号';

comment on column mtr_software_mapping.modify_time is '修改时间';

comment on column mtr_software_mapping.del_flag is '逻辑删除标志，1：逻辑删除，0：未删除';

comment on column mtr_software_mapping.subsystem is '软件对应子系统';

comment on column mtr_software_mapping.location is '车厢位置';

alter table mtr_software_mapping
    owner to phmdbadmin;

create table if not exists mtr_station
(
    id                 varchar(128)       not null
        primary key,
    station_code       integer            not null,
    name               varchar(128)       not null,
    sort_number        integer            not null,
    departure_distance integer,
    status             smallint default 1 not null,
    metro_line_id      varchar(128)       not null,
    create_by          varchar(128),
    create_time        timestamp(0),
    modify_by          varchar(128),
    modify_time        timestamp(0),
    remark             text,
    type               varchar(128)       not null,
    direction          varchar(64)        not null,
    sta_id             varchar(128),
    del_flag           smallint           not null
);

comment on table mtr_station is '地铁站点';

comment on column mtr_station.station_code is '站点编号';

comment on column mtr_station.name is '站点名称';

comment on column mtr_station.sort_number is '顺序编号';

comment on column mtr_station.departure_distance is '距上行始发站距离，单位：m';

comment on column mtr_station.status is '站点状态，0：未开通，1：开通';

comment on column mtr_station.metro_line_id is '外键，引用地铁线路表的主键（id）';

comment on column mtr_station.create_by is '创建用户';

comment on column mtr_station.create_time is '创建时间';

comment on column mtr_station.modify_by is '修改用户';

comment on column mtr_station.modify_time is '修改时间';

comment on column mtr_station.remark is '备注';

comment on column mtr_station.type is '站点类型：正常站1、虚拟站2、折返站3';

comment on column mtr_station.direction is '上下行方向：up上行、down下行';

comment on column mtr_station.sta_id is '站id 需区分上下行';

comment on column mtr_station.del_flag is '删除标识';

alter table mtr_station
    owner to phmdbadmin;

create index if not exists mtr_station_metro_line_id_idx
    on mtr_station (metro_line_id);

create table if not exists mtr_subsystem_dict
(
    id          varchar(128) not null
        constraint mtr_subystem_dict_pkey
            primary key,
    vehicle_id  varchar(128),
    value       varchar(32),
    label       varchar(128),
    type        varchar(128),
    description varchar(128),
    create_by   varchar(128),
    create_time timestamp(0),
    modify_by   varchar(128),
    modify_time timestamp(0),
    code        varchar(32)
);

comment on table mtr_subsystem_dict is '车辆子系统字典';

comment on column mtr_subsystem_dict.id is '主键';

comment on column mtr_subsystem_dict.vehicle_id is '外键，引用车辆表的主键（id）';

comment on column mtr_subsystem_dict.value is '编码';

comment on column mtr_subsystem_dict.type is '类型';

comment on column mtr_subsystem_dict.description is '描述';

comment on column mtr_subsystem_dict.create_by is '创建用户编号';

comment on column mtr_subsystem_dict.create_time is '创建时间';

comment on column mtr_subsystem_dict.modify_by is '修改用户编号';

comment on column mtr_subsystem_dict.modify_time is '修改时间';

comment on column mtr_subsystem_dict.code is '缩写';

alter table mtr_subsystem_dict
    owner to phmdbadmin;

create table if not exists mtr_vehicle
(
    id                    varchar(128)       not null
        primary key,
    name_cn               varchar(128),
    name_en               varchar(128)       not null,
    vehicle_code          varchar(64)        not null,
    metro_line_id         varchar(128),
    del_flag              smallint default 0 not null,
    create_by             varchar(128),
    create_time           timestamp(0),
    modify_by             varchar(128),
    modify_time           timestamp(0),
    remark                text,
    production_date       date,
    delivery_date         date,
    guarantee_period      integer,
    vehicle_storage_group smallint,
    vehicle_asc_code      varchar(255),
    vehicle_type_id       varchar(128)
);

comment on table mtr_vehicle is '车辆';

comment on column mtr_vehicle.id is '主键';

comment on column mtr_vehicle.name_cn is '车辆中文名称';

comment on column mtr_vehicle.name_en is '车辆英文名称';

comment on column mtr_vehicle.vehicle_code is '车辆编码';

comment on column mtr_vehicle.metro_line_id is '外键，引用地铁线路表的主键（id）';

comment on column mtr_vehicle.del_flag is '逻辑删除标志，1：逻辑删除，0：未删除';

comment on column mtr_vehicle.create_by is '创建用户';

comment on column mtr_vehicle.create_time is '创建时间';

comment on column mtr_vehicle.modify_by is '修改用户';

comment on column mtr_vehicle.modify_time is '修改时间';

comment on column mtr_vehicle.remark is '备注';

comment on column mtr_vehicle.production_date is '出厂日期';

comment on column mtr_vehicle.delivery_date is '交车日期';

comment on column mtr_vehicle.guarantee_period is '质保期';

comment on column mtr_vehicle.vehicle_storage_group is 'iotdb用';

comment on column mtr_vehicle.vehicle_type_id is '车型id';

alter table mtr_vehicle
    owner to phmdbadmin;

create index if not exists mtr_vehicle_metro_line_id_idx
    on mtr_vehicle (metro_line_id);

create index if not exists mtr_vehicle_vehicle_code_idx
    on mtr_vehicle (vehicle_code);

create table if not exists mtr_vehicle_file
(
    vehicle_id varchar(128) not null,
    file_id    varchar(128) not null,
    del_flag   smallint default 0,
    start_date timestamp(6),
    end_date   timestamp(6)
);

comment on column mtr_vehicle_file.vehicle_id is '外键，引用车辆表的主键（id）';

comment on column mtr_vehicle_file.file_id is '外键，引用文件表的主键（id）';

alter table mtr_vehicle_file
    owner to phmdbadmin;

create index if not exists mtr_vehicle_file_file_id_idx
    on mtr_vehicle_file (file_id);

create index if not exists mtr_vehicle_file_vehicle_id_idx
    on mtr_vehicle_file (vehicle_id);

create table if not exists mtr_vehicle_type
(
    id                 varchar(128)       not null
        primary key,
    name               varchar(128)       not null,
    type               varchar(64)        not null,
    marshalling_number bigint             not null,
    manufacturer_id    varchar(128),
    comm_type          varchar(32),
    del_flag           smallint default 0 not null,
    create_by          varchar(128),
    create_time        timestamp(0),
    modify_by          varchar(128),
    modify_time        timestamp(0),
    remark             text,
    vehicle_type_code  varchar(10),
    line_id            varchar(128)
);

comment on table mtr_vehicle_type is '车辆型号';

comment on column mtr_vehicle_type.id is '主键，车辆型号编号';

comment on column mtr_vehicle_type.name is '车辆型号名称';

comment on column mtr_vehicle_type.type is '车辆类型，地铁/轻轨';

comment on column mtr_vehicle_type.marshalling_number is '编组数量，4/6/8';

comment on column mtr_vehicle_type.manufacturer_id is '外键，引用制造商表的主键（id）';

comment on column mtr_vehicle_type.comm_type is '通信方式，TCP/MQTT';

comment on column mtr_vehicle_type.del_flag is '逻辑删除标志，1：逻辑删除，0：未删除';

comment on column mtr_vehicle_type.create_by is '创建用户编号';

comment on column mtr_vehicle_type.create_time is '创建时间';

comment on column mtr_vehicle_type.modify_by is '修改用户编号';

comment on column mtr_vehicle_type.modify_time is '修改时间';

comment on column mtr_vehicle_type.remark is '备注';

comment on column mtr_vehicle_type.vehicle_type_code is '车型code提供fracas使用';

comment on column mtr_vehicle_type.line_id is '外键，引用线路表的主键';

alter table mtr_vehicle_type
    owner to phmdbadmin;

create table if not exists mtr_vehicle_type_contacts
(
    manufacturer_employee_id varchar(128) not null,
    vehicle_type_id          varchar(128) not null
);

comment on table mtr_vehicle_type_contacts is '联系人，负责车辆技术支持、商务和采购等。';

comment on column mtr_vehicle_type_contacts.manufacturer_employee_id is '外键，引用制造商雇员表的主键（id）';

comment on column mtr_vehicle_type_contacts.vehicle_type_id is '外键，引用车辆型号表的主键（id）';

alter table mtr_vehicle_type_contacts
    owner to phmdbadmin;

create index if not exists mtr_vehicle_type_contacts_manufacturer_employee_id_idx
    on mtr_vehicle_type_contacts (manufacturer_employee_id);

create index if not exists mtr_vehicle_type_contacts_vehicle_type_id_idx
    on mtr_vehicle_type_contacts (vehicle_type_id);

create table if not exists mtr_vehicle_type_cumulant
(
    id              varchar(128) not null
        primary key,
    vehicle_type_id varchar(128) not null,
    signal_name_en  varchar      not null,
    create_time     timestamp(0),
    create_by       varchar(128),
    modify_time     timestamp(0),
    modify_by       varchar(128),
    remark          text
);

comment on column mtr_vehicle_type_cumulant.id is '主键';

comment on column mtr_vehicle_type_cumulant.vehicle_type_id is '外键，引用车辆型号表的主键';

comment on column mtr_vehicle_type_cumulant.signal_name_en is '信号英文名';

comment on column mtr_vehicle_type_cumulant.create_time is '创建时间';

comment on column mtr_vehicle_type_cumulant.create_by is '创建用户编号';

comment on column mtr_vehicle_type_cumulant.modify_time is '修改时间';

comment on column mtr_vehicle_type_cumulant.modify_by is '修改用户编号';

comment on column mtr_vehicle_type_cumulant.remark is '备注';

alter table mtr_vehicle_type_cumulant
    owner to phmdbadmin;

create table if not exists mtr_vehicle_type_file
(
    vehicle_type_id varchar(128) not null,
    file_id         varchar(128) not null,
    del_flag        smallint default 0,
    start_date      timestamp(6),
    end_date        timestamp(6)
);

comment on table mtr_vehicle_type_file is '车辆型号和文件关系';

comment on column mtr_vehicle_type_file.vehicle_type_id is '外键，引用车辆型号表的主键（id）';

comment on column mtr_vehicle_type_file.file_id is '文件编号';

comment on column mtr_vehicle_type_file.del_flag is '默认为0';

alter table mtr_vehicle_type_file
    owner to phmdbadmin;

create table if not exists mtr_vehicle_type_relation
(
    vehicle_id      varchar(128)       not null,
    vehicle_type_id varchar(128)       not null,
    start_date      date               not null,
    end_date        date,
    valid           smallint default 1 not null
);

comment on table mtr_vehicle_type_relation is '车辆与车辆型号关系，用于保持车辆型号变更记录。';

comment on column mtr_vehicle_type_relation.vehicle_id is '外键，引用车辆表的主键（id）';

comment on column mtr_vehicle_type_relation.vehicle_type_id is '外键，引用车辆型号表的主键（id）';

comment on column mtr_vehicle_type_relation.start_date is '记录生效日期';

comment on column mtr_vehicle_type_relation.end_date is '记录失效日期';

comment on column mtr_vehicle_type_relation.valid is '数据记录有效标识符，1：有效，0：无效';

alter table mtr_vehicle_type_relation
    owner to phmdbadmin;

create index if not exists mtr_vehicle_type_relation_vehicle_id_idx
    on mtr_vehicle_type_relation (vehicle_id);

create index if not exists mtr_vehicle_type_relation_vehicle_type_id_idx
    on mtr_vehicle_type_relation (vehicle_type_id);

create table if not exists mx_temp
(
    vehicle_type_id varchar(255),
    mx_mc           varchar(255),
    content         text,
    description     varchar(255),
    line_id         integer,
    fault_type_key  varchar(255),
    name_cn         varchar(255),
    fault_reason    varchar(255)
);

alter table mx_temp
    owner to phmdbadmin;

create table if not exists re_dict
(
    id          varchar(128) not null
        primary key,
    parent_id   varchar(128),
    key         varchar(128),
    value_cn    varchar(256),
    value_en    varchar(256),
    create_time timestamp(0),
    user_id     varchar(128),
    remark      text
);

comment on table re_dict is '设备管理字典表';

comment on column re_dict.id is '主键';

comment on column re_dict.key is '字典键';

comment on column re_dict.value_cn is '中文名称';

comment on column re_dict.value_en is '英文名称';

comment on column re_dict.create_time is '创建时间';

comment on column re_dict.user_id is '用户ID';

comment on column re_dict.remark is '备注';

alter table re_dict
    owner to phmdbadmin;

create table if not exists relay_contactor
(
    id                      varchar(128)           not null
        primary key,
    comm_original_signal_id varchar(128)           not null,
    type                    varchar(32)            not null,
    create_by               bigint,
    create_time             timestamp(0),
    modify_by               bigint,
    modify_time             timestamp(0),
    remark                  text,
    electrical_life         bigint,
    operation_avg           bigint,
    mtr_vehicle_type_id     varchar(128),
    name_cn                 varchar(64),
    structure_code          varchar(255),
    calculation_type        varchar(255) default 0,
    trigger_value           integer      default 0,
    calculation_method      varchar(255) default 0,
    line_id                 varchar(128),
    unit                    varchar(255),
    del_flag                smallint     default 0 not null,
    component_kind          smallint,
    name_en                 varchar(64),
    product_number          varchar(64),
    manufacturer_id         varchar(128)
);

comment on table relay_contactor is '继电器和接触器';

comment on column relay_contactor.id is '主键';

comment on column relay_contactor.comm_original_signal_id is '外键，引用信号表的主键（id）';

comment on column relay_contactor.type is '部件类型，继电器relay/接触器contactor';

comment on column relay_contactor.create_by is '创建用户编号';

comment on column relay_contactor.create_time is '创建时间';

comment on column relay_contactor.modify_by is '修改用户编号';

comment on column relay_contactor.modify_time is '修改时间';

comment on column relay_contactor.remark is '备注';

comment on column relay_contactor.electrical_life is '电气寿命';

comment on column relay_contactor.operation_avg is '继电器或接触器仅30天平均每日开合次数';

comment on column relay_contactor.mtr_vehicle_type_id is '外键，引用车型表的主键（id）';

comment on column relay_contactor.name_cn is '部件中文名称';

comment on column relay_contactor.structure_code is '构型编码';

comment on column relay_contactor.calculation_type is '大数据处理计算类型(time、count、distance)';

comment on column relay_contactor.trigger_value is '符合条件的触发值（非必填）';

comment on column relay_contactor.calculation_method is '符合的计算方式（大于 等于 不等于）';

comment on column relay_contactor.line_id is '线路id';

comment on column relay_contactor.unit is '单位';

comment on column relay_contactor.del_flag is '逻辑删除标志，1：逻辑删除，0：未删除';

comment on column relay_contactor.component_kind is '0：寿命预测部件 1：常规部件(此表新增)（21-12-13新添加）';

comment on column relay_contactor.name_en is '部件英文名称（21-12-13新添加）';

comment on column relay_contactor.product_number is '产品编号（21-12-13新添加）(当component_type类型=1时 必填)';

comment on column relay_contactor.manufacturer_id is '外键，应用制造商表的主键（id）（21-12-13新添加）(当component_type类型=1时 必填)';

alter table relay_contactor
    owner to phmdbadmin;

create table if not exists relay_contactor_history
(
    id                      varchar(128) not null
        primary key,
    relay_contactor_life_id bigint       not null,
    operation_cnt           bigint,
    assembly_time           timestamp(0),
    installer               varchar(64),
    end_time                timestamp(0),
    product_number          varchar(64),
    manufacturer_id         varchar(128)
);

comment on column relay_contactor_history.relay_contactor_life_id is '继电器使用表id';

comment on column relay_contactor_history.operation_cnt is '继电器或接触器开合次数';

comment on column relay_contactor_history.assembly_time is '安装时间';

comment on column relay_contactor_history.installer is '安装人';

comment on column relay_contactor_history.end_time is '拆卸时间';

comment on column relay_contactor_history.product_number is '产品编号（21-12-13新添加）(当component_type类型=1时 必填)';

comment on column relay_contactor_history.manufacturer_id is '外键，应用制造商表的主键（id）（21-12-13新添加）(当component_type类型=1时 必填)';

alter table relay_contactor_history
    owner to phmdbadmin;

create table if not exists relay_contactor_life
(
    id                 varchar(128) not null
        primary key,
    relay_contactor_id varchar(128) not null,
    operation_cnt      bigint,
    remaining_life     bigint,
    vehicle_code       varchar(64)  not null,
    assembly_time      timestamp(0),
    assembly_location  varchar(32),
    valid              smallint     not null,
    remark             text,
    usepercent         varchar(64),
    installer          varchar(64),
    replace_times      bigint default 0,
    product_number     varchar(64),
    manufacturer_id    varchar(128)
);

comment on table relay_contactor_life is '继电器和接触器寿命';

comment on column relay_contactor_life.id is '主键';

comment on column relay_contactor_life.relay_contactor_id is '外键，引用继电器和接触器表的主键（id）';

comment on column relay_contactor_life.operation_cnt is '继电器或接触器开合次数';

comment on column relay_contactor_life.remaining_life is '继电器或接触器剩余寿命，单位：天';

comment on column relay_contactor_life.vehicle_code is '车辆编号';

comment on column relay_contactor_life.assembly_time is '安装时间';

comment on column relay_contactor_life.assembly_location is '安装位置';

comment on column relay_contactor_life.valid is '有效标志，1：有效，0：无效';

comment on column relay_contactor_life.remark is '备注';

comment on column relay_contactor_life.installer is '安装人';

comment on column relay_contactor_life.replace_times is '更换次数';

comment on column relay_contactor_life.product_number is '产品编号（21-12-13新添加）(当component_type类型=1时 必填)';

comment on column relay_contactor_life.manufacturer_id is '外键，应用制造商表的主键（id）（21-12-13新添加）(当component_type类型=1时 必填)';

alter table relay_contactor_life
    owner to phmdbadmin;

create table if not exists riom_board
(
    name_en varchar(255),
    number  varchar(255),
    sort    integer
);

alter table riom_board
    owner to phmdbadmin;

create table if not exists stru_component
(
    id                varchar(128)       not null
        primary key,
    component_type_id varchar(128)       not null,
    serial_number     varchar(64),
    production_date   date,
    del_flag          smallint default 0 not null,
    create_by         varchar(128),
    create_time       timestamp(0),
    modify_by         varchar(128),
    modify_time       timestamp(0),
    remark            text
);

comment on table stru_component is '部件，相当于部件实体。';

comment on column stru_component.id is '主键，部件编号';

comment on column stru_component.component_type_id is '外键，引用车辆部件表的主键（id）';

comment on column stru_component.serial_number is '部件序列号';

comment on column stru_component.production_date is '部件出厂日期';

comment on column stru_component.del_flag is '逻辑删除标识符，1：逻辑删除，0：未删除';

comment on column stru_component.create_by is '创建用户编号';

comment on column stru_component.create_time is '创建时间';

comment on column stru_component.modify_by is '修改用户编号';

comment on column stru_component.modify_time is '修改时间';

comment on column stru_component.remark is '备注';

alter table stru_component
    owner to phmdbadmin;

create index if not exists stru_component_component_type_id_idx
    on stru_component (component_type_id);

create table if not exists stru_component_file
(
    component_id varchar(128) not null,
    file_id      varchar(128) not null,
    del_flag     smallint default 0,
    start_date   timestamp(6),
    end_date     timestamp(6)
);

comment on table stru_component_file is '部件和文件关系';

comment on column stru_component_file.component_id is '部件编号';

comment on column stru_component_file.file_id is '文件编号';

alter table stru_component_file
    owner to phmdbadmin;

create index if not exists stru_component_file_component_id_idx
    on stru_component_file (component_id);

create index if not exists stru_component_file_file_id_idx
    on stru_component_file (file_id);

create table if not exists stru_component_type
(
    id              varchar(128)       not null
        primary key,
    name_cn         varchar(128)       not null,
    name_en         varchar(128),
    product_number  varchar(64),
    manufacturer_id varchar(128),
    catalog         varchar(16)        not null,
    del_flag        smallint default 0 not null,
    create_by       varchar(128),
    create_time     timestamp(0),
    modify_by       varchar(128),
    modify_time     timestamp(0),
    remark          text,
    vehicle_type_id varchar(128),
    line_id         varchar(128)
);

comment on table stru_component_type is '部件型号，保存车辆所有部件的型号。';

comment on column stru_component_type.id is '主键';

comment on column stru_component_type.name_cn is '部件型号中文名称';

comment on column stru_component_type.name_en is '部件型号英文名称';

comment on column stru_component_type.product_number is '产品编号';

comment on column stru_component_type.manufacturer_id is '外键，应用制造商表的主键（id）';

comment on column stru_component_type.catalog is '部件型号分类';

comment on column stru_component_type.del_flag is '逻辑删除标识符，1：逻辑删除，0：未删除';

comment on column stru_component_type.create_by is '创建用户编号';

comment on column stru_component_type.create_time is '创建时间';

comment on column stru_component_type.modify_by is '修改用户编号';

comment on column stru_component_type.modify_time is '修改时间';

comment on column stru_component_type.remark is '备注';

comment on column stru_component_type.vehicle_type_id is '外键，引用车辆型号表的主键';

comment on column stru_component_type.line_id is '外键，引用线路表的主键';

alter table stru_component_type
    owner to phmdbadmin;

create table if not exists stru_component_type_file
(
    component_type_id varchar(128) not null,
    file_id           varchar(128) not null,
    del_flag          smallint default 0,
    start_date        timestamp(6),
    end_date          timestamp(6)
);

comment on table stru_component_type_file is '部件型号和文件关系';

comment on column stru_component_type_file.component_type_id is '外键，引用部件型号表的主键（id）';

comment on column stru_component_type_file.file_id is '外键，引用文件表的主键（id）';

comment on column stru_component_type_file.del_flag is '默认为0';

alter table stru_component_type_file
    owner to phmdbadmin;

create index if not exists stru_component_type_file_component_type_id_idx
    on stru_component_type_file (component_type_id);

create index if not exists stru_component_type_file_file_id_idx
    on stru_component_type_file (file_id);

create table if not exists stru_structure_signal_group
(
    vehicle_type_id        varchar(128) not null,
    vehicle_structure_code varchar(64)  not null,
    signal_group_id        varchar(128) not null
);

comment on table stru_structure_signal_group is '车辆构型信号分组，关联与该构型相关的信号。';

comment on column stru_structure_signal_group.vehicle_type_id is '外键，引用车辆型号表的主键（id）';

alter table stru_structure_signal_group
    owner to phmdbadmin;

create index if not exists stru_structure_signal_group_signal_group_id_idx
    on stru_structure_signal_group (signal_group_id);

create index if not exists stru_structure_signal_group_vehicle_structure_code_idx
    on stru_structure_signal_group (vehicle_structure_code);

create index if not exists stru_structure_signal_group_vehicle_type_id_idx
    on stru_structure_signal_group (vehicle_type_id);

create table if not exists stru_vehicle_type_structure
(
    id                    varchar(128)       not null
        primary key,
    vehicle_type_id       varchar(128)       not null,
    parent_structure_code varchar(64),
    structure_code        varchar(64)        not null,
    name_cn               varchar(128)       not null,
    name_en               varchar(128),
    short_name_en         varchar(64)        not null,
    structure_position    varchar(255),
    structure_type        varchar(32),
    component_type_id     varchar(128),
    sort_number           integer,
    modify_time           timestamp(6),
    remark                text,
    create_by             varchar(128),
    create_time           timestamp(0),
    modify_by             varchar(128),
    del_flag              smallint default 0 not null,
    fbx_file_id           varchar(128),
    threed_code           varchar(255)
);

comment on table stru_vehicle_type_structure is '车辆构型，构型树又分作逻辑节点和实体节点，其中逻辑节点仅表明位置，不包含实际部件类型。';

comment on column stru_vehicle_type_structure.id is '主键';

comment on column stru_vehicle_type_structure.vehicle_type_id is '外键，引用车辆型号表的主键（id）';

comment on column stru_vehicle_type_structure.parent_structure_code is '父结构编码';

comment on column stru_vehicle_type_structure.structure_code is '结构编码';

comment on column stru_vehicle_type_structure.name_cn is '结构中文名称';

comment on column stru_vehicle_type_structure.name_en is '结构英文名称';

comment on column stru_vehicle_type_structure.short_name_en is '结构英文名称缩写';

comment on column stru_vehicle_type_structure.structure_position is '父节点集合，用构型英文缩写';

comment on column stru_vehicle_type_structure.structure_type is '结构类型，车辆/车厢/子系统/部件/位置';

comment on column stru_vehicle_type_structure.component_type_id is '外键，引用部件型号表的主键（id）';

comment on column stru_vehicle_type_structure.sort_number is '顺序编号';

comment on column stru_vehicle_type_structure.modify_time is '修改时间';

comment on column stru_vehicle_type_structure.remark is '备注';

comment on column stru_vehicle_type_structure.create_by is '创建用户编号';

comment on column stru_vehicle_type_structure.create_time is '创建时间';

comment on column stru_vehicle_type_structure.modify_by is '修改用户编号';

comment on column stru_vehicle_type_structure.del_flag is '逻辑删除标识符，1：逻辑删除，0：未删除';

comment on column stru_vehicle_type_structure.fbx_file_id is '（3d效果图）文件表主键';

comment on column stru_vehicle_type_structure.threed_code is '三维模型code值（前端根据这个值绑定模型）';

alter table stru_vehicle_type_structure
    owner to phmdbadmin;

create index if not exists stru_vehicle_type_structure_parent_structure_code_idx
    on stru_vehicle_type_structure (parent_structure_code);

create index if not exists stru_vehicle_type_structure_structure_code_idx
    on stru_vehicle_type_structure (structure_code);

create index if not exists stru_vehicle_type_structure_vehicle_type_id_idx
    on stru_vehicle_type_structure (vehicle_type_id);

create table if not exists stru_vehicle_component_record
(
    id                      varchar(128) not null
        constraint stru_vehicle_component_record_copy1_pkey
            primary key,
    component_position      varchar(64),
    previous_component_code varchar(64),
    current_component_code  varchar(64)  not null,
    executer                varchar(64),
    execute_time            timestamp(0),
    create_by               varchar(128),
    create_time             timestamp(0),
    vehicle_id              varchar(128) not null,
    structure_code          varchar(64)  not null
);

comment on column stru_vehicle_component_record.id is '主键';

comment on column stru_vehicle_component_record.component_position is '部件位置';

comment on column stru_vehicle_component_record.previous_component_code is '更换前的部件编码';

comment on column stru_vehicle_component_record.current_component_code is '更换后的部件编码';

comment on column stru_vehicle_component_record.executer is '更换人';

comment on column stru_vehicle_component_record.execute_time is '更换时间';

comment on column stru_vehicle_component_record.create_by is '创建用户编号';

comment on column stru_vehicle_component_record.create_time is '创建时间';

comment on column stru_vehicle_component_record.vehicle_id is '外键，引用车辆表的主键（id）';

comment on column stru_vehicle_component_record.structure_code is '外键，引用车辆构型表的构型编码（structure_code）';

alter table stru_vehicle_component_record
    owner to phmdbadmin;

create table if not exists stru_vehicle_structure_component
(
    id                    varchar(128)       not null
        constraint stru_vehicle_structure_component_copy1_pkey
            primary key,
    component_id          varchar(128),
    vehicle_id            varchar(128)       not null,
    structure_code        varchar(64)        not null,
    assembly_time         timestamp(0),
    disassembly_time      timestamp(0),
    valid                 smallint default 1 not null,
    name_cn               varchar(128)       not null,
    name_en               varchar(128),
    short_name_en         varchar(64)        not null,
    component_type_id     varchar(128),
    parent_structure_code varchar(64)        not null,
    structure_position    varchar(255),
    structure_type        varchar(32),
    sort_number           integer,
    del_flag              smallint           not null,
    start_date            timestamp(0)       not null,
    end_date              timestamp(0),
    create_by             varchar(128),
    create_time           timestamp(0),
    modify_by             varchar(128),
    modify_time           timestamp(0),
    remark                text
);

comment on table stru_vehicle_structure_component is '车辆构型部件，更换车辆部件时，并不删除此关系记录，仅把有效标志置做False';

comment on column stru_vehicle_structure_component.id is '主键';

comment on column stru_vehicle_structure_component.component_id is '外键，引用部件表的主键（id）';

comment on column stru_vehicle_structure_component.vehicle_id is '外键，引用车辆表的主键（id）';

comment on column stru_vehicle_structure_component.structure_code is '外键，引用车辆构型表的构型编码（structure_code）';

comment on column stru_vehicle_structure_component.assembly_time is '部件安装时间';

comment on column stru_vehicle_structure_component.disassembly_time is '部件拆卸时间';

comment on column stru_vehicle_structure_component.valid is '有效标志，1：有效，0：无效';

comment on column stru_vehicle_structure_component.name_cn is '结构中文名称';

comment on column stru_vehicle_structure_component.name_en is '结构英文名称';

comment on column stru_vehicle_structure_component.short_name_en is '结构英文名称缩写';

comment on column stru_vehicle_structure_component.component_type_id is '外键，引用部件型号表的主键（id）';

comment on column stru_vehicle_structure_component.parent_structure_code is '父结构编码';

comment on column stru_vehicle_structure_component.structure_position is '父节点集合，用构型英文缩写';

comment on column stru_vehicle_structure_component.structure_type is '结构类型，车辆/车厢/子系统/部件/位置';

comment on column stru_vehicle_structure_component.sort_number is '顺序编号';

comment on column stru_vehicle_structure_component.del_flag is '逻辑删除标识符，1：逻辑删除，0：未删除';

comment on column stru_vehicle_structure_component.start_date is '记录生效日期';

comment on column stru_vehicle_structure_component.end_date is '记录失效日期';

comment on column stru_vehicle_structure_component.create_by is '创建用户编号';

comment on column stru_vehicle_structure_component.create_time is '创建时间';

comment on column stru_vehicle_structure_component.modify_by is '修改用户编号';

comment on column stru_vehicle_structure_component.modify_time is '修改时间';

comment on column stru_vehicle_structure_component.remark is '备注';

alter table stru_vehicle_structure_component
    owner to phmdbadmin;

create index if not exists stru_vehicle_structure_component_component_id_idx_copy1
    on stru_vehicle_structure_component (component_id);

create index if not exists stru_vehicle_structure_component_vehicle_id_idx_copy1
    on stru_vehicle_structure_component (vehicle_id);

create index if not exists stru_vehicle_structure_component_vehicle_structure_code_idx_cop
    on stru_vehicle_structure_component (structure_code);

create table if not exists svg_map
(
    id                 varchar(64)           not null
        primary key,
    menu_id            varchar(64),
    vehicle_type       varchar(64),
    key                varchar(255),
    signal_function_id varchar(64),
    svg_code           text,
    svg_function       varchar(65535),
    line_id            varchar(64),
    create_time        timestamp(0),
    create_by          varchar(40),
    modify_time        timestamp(0),
    modify_by          varchar(40),
    del_flag           boolean default false not null
);

comment on table svg_map is 'svg图中需要配置的项点';

comment on column svg_map.menu_id is '所属菜单id';

comment on column svg_map.key is '可配项点的id';

comment on column svg_map.signal_function_id is '处理函数id';

comment on column svg_map.svg_code is 'svg_code代码段';

comment on column svg_map.svg_function is 'svg_code所需function';

comment on column svg_map.line_id is '线路id';

comment on column svg_map.modify_time is '修改时间';

comment on column svg_map.del_flag is '删除标识符';

alter table svg_map
    owner to phmdbadmin;

create table if not exists sys_access_statistics
(
    id               varchar(128) not null,
    menu_name        varchar(255),
    menu_code        varchar(255),
    parent_menu_code varchar(255),
    user_id          varchar(128),
    date             timestamp(0),
    name             varchar(255),
    host             varchar(60),
    username         varchar(255),
    user_agent       varchar(512),
    line_id          varchar(128)
);

comment on table sys_access_statistics is '系统日志表';

comment on column sys_access_statistics.menu_name is '菜单名称';

comment on column sys_access_statistics.menu_code is '菜单code';

comment on column sys_access_statistics.parent_menu_code is '父级菜单code';

comment on column sys_access_statistics.user_id is '操作人ID';

comment on column sys_access_statistics.date is '操作时间';

comment on column sys_access_statistics.name is '操作人真实名字';

comment on column sys_access_statistics.host is '操作主机';

comment on column sys_access_statistics.username is '登录名';

comment on column sys_access_statistics.user_agent is '用户代理信息（系统、浏览器等等）';

comment on column sys_access_statistics.line_id is '线路id';

alter table sys_access_statistics
    owner to phmdbadmin;

create table if not exists sys_area
(
    "areaId"   varchar(32) not null
        primary key,
    "areaCode" varchar(50) not null,
    "areaName" varchar(20) not null,
    level      integer     not null,
    "cityCode" varchar(50),
    center     varchar(50),
    "parentId" varchar(32) not null
);

comment on column sys_area."areaId" is '地区Id';

comment on column sys_area."areaCode" is '地区编码';

comment on column sys_area."areaName" is '地区名';

comment on column sys_area.level is '地区级别（1:省份province,2:市city,3:区县district,4:街道street）';

comment on column sys_area."cityCode" is '城市编码';

comment on column sys_area.center is '城市中心点（即：经纬度坐标）';

comment on column sys_area."parentId" is '地区父节点';

alter table sys_area
    owner to phmdbadmin;

create table if not exists sys_assoc_rules
(
    pk_id            varchar(40) not null,
    ruleid           integer,
    pre              text[],
    post             text[],
    count            integer,
    support          double precision,
    confidence       double precision,
    lift             double precision,
    conviction       double precision,
    sys_id           varchar,
    create_date      timestamp(0) default now(),
    update_date      timestamp(0),
    trigger_fault_id varchar
);

alter table sys_assoc_rules
    owner to phmdbadmin;

create table if not exists sys_data
(
    id          varchar(128) not null
        primary key,
    menu_code   varchar(10),
    menu_name   varchar(50),
    item_code   varchar(4),
    item_name   varchar(50),
    sort        integer,
    create_user varchar(64),
    create_date date,
    validflag   char         not null,
    remark      varchar(100)
);

comment on column sys_data.validflag is '0-有效，1-无效';

alter table sys_data
    owner to phmdbadmin;

create table if not exists sys_dict
(
    id                varchar(128) not null
        primary key,
    sort_number       bigint,
    code              varchar(255),
    label             varchar(255),
    value             varchar(255),
    type_id           varchar(128),
    description       varchar(255),
    modify_time       timestamp(0),
    modify_by         varchar(128),
    create_time       timestamp(0),
    create_by         varchar(128),
    del_flag          varchar(1)   not null,
    line_id           varchar(128),
    vehicle_type_id   varchar(128),
    line_name         varchar(255),
    vehicle_type_name varchar(255),
    type_code         varchar(255)
);

comment on table sys_dict is '系统字典';

comment on column sys_dict.id is '主键id';

comment on column sys_dict.sort_number is '排序';

comment on column sys_dict.code is '标签编码';

comment on column sys_dict.label is '标签';

comment on column sys_dict.value is '键值';

comment on column sys_dict.type_id is '类型id';

comment on column sys_dict.description is '描述';

comment on column sys_dict.modify_time is '修改时间';

comment on column sys_dict.modify_by is '修改人';

comment on column sys_dict.create_time is '创建时间';

comment on column sys_dict.create_by is '创建人';

comment on column sys_dict.del_flag is '删除标识';

comment on column sys_dict.line_id is '线路id';

comment on column sys_dict.vehicle_type_id is '车型id';

comment on column sys_dict.line_name is '线路名称';

comment on column sys_dict.vehicle_type_name is '车型名称';

comment on column sys_dict.type_code is '字典类型code';

alter table sys_dict
    owner to phmdbadmin;

create table if not exists sys_dict_type
(
    id          varchar(128) not null
        primary key,
    type        varchar(255) not null,
    description text,
    create_time timestamp(6),
    create_by   varchar(128),
    modify_time timestamp(6),
    modify_by   varchar(128),
    del_flag    varchar(1)   not null,
    type_level  char
);

comment on column sys_dict_type.id is '主键';

comment on column sys_dict_type.type is '类型';

comment on column sys_dict_type.description is '描述';

comment on column sys_dict_type.create_time is '创建日期';

comment on column sys_dict_type.create_by is '创建人';

comment on column sys_dict_type.modify_time is '修改时间';

comment on column sys_dict_type.modify_by is '修改人';

comment on column sys_dict_type.del_flag is '删除标识';

comment on column sys_dict_type.type_level is '1-common  2-line 3-vehicle_type';

alter table sys_dict_type
    owner to phmdbadmin;

create table if not exists sys_element
(
    id           varchar(128) not null
        primary key,
    element_code varchar(255),
    element_type varchar(255),
    element_name varchar(255),
    element_uri  varchar(255),
    menu_id      varchar(64),
    parent_id    varchar(64),
    path         varchar(255),
    method       varchar(255),
    remarks      varchar(255),
    create_by    varchar(255),
    create_date  date,
    update_by    varchar(255),
    update_date  date,
    tenant_id    varchar(64),
    del_flag     char
);

comment on table sys_element is '资源基本表';

comment on column sys_element.element_code is '资源编码';

comment on column sys_element.element_type is '资源类型';

comment on column sys_element.element_name is '资源名称';

comment on column sys_element.element_uri is '资源路径';

comment on column sys_element.menu_id is '资源关联菜单';

comment on column sys_element.parent_id is '关联组织id';

comment on column sys_element.path is '资源树状检索路径';

comment on column sys_element.method is '资源请求类型';

comment on column sys_element.remarks is '备注';

comment on column sys_element.create_by is '创建人';

comment on column sys_element.create_date is '创建时间';

comment on column sys_element.update_by is '更新人';

comment on column sys_element.update_date is '更新时间';

comment on column sys_element.tenant_id is '租户id';

alter table sys_element
    owner to phmdbadmin;

create table if not exists sys_element_role
(
    id           varchar(64) not null,
    role_id      varchar(128),
    role_type    varchar(255),
    element_id   varchar(128),
    element_type varchar(128),
    parent_id    varchar(64),
    path         varchar(255),
    create_by    varchar(255),
    create_date  date,
    update_by    varchar(255),
    update_date  date,
    tenant_id    varchar(64),
    del_flag     char,
    type         varchar(255)
);

comment on table sys_element_role is '资源和角色关系表';

comment on column sys_element_role.id is '主键id';

comment on column sys_element_role.role_id is '角色ID';

comment on column sys_element_role.role_type is '角色类型';

comment on column sys_element_role.element_id is '资源id';

comment on column sys_element_role.element_type is '资源类型';

comment on column sys_element_role.parent_id is '父节点id';

comment on column sys_element_role.path is '资源树状检索路径';

comment on column sys_element_role.create_by is '创建人';

comment on column sys_element_role.create_date is '创建日期';

comment on column sys_element_role.update_by is '更新人';

comment on column sys_element_role.update_date is '更新日期';

comment on column sys_element_role.tenant_id is '租户Id';

comment on column sys_element_role.del_flag is '删除标识';

comment on column sys_element_role.type is '删除标识（在用的）';

alter table sys_element_role
    owner to phmdbadmin;

create table if not exists sys_file
(
    id            varchar(128)       not null
        primary key,
    name          varchar(128)       not null,
    url           varchar(255)       not null,
    type          varchar(32),
    format        varchar(16),
    size          bigint,
    hash_code     varchar(64),
    del_flag      smallint default 0 not null,
    create_by     varchar(128),
    create_time   timestamp(6),
    modify_by     varchar(128),
    modify_time   timestamp(0),
    remark        text,
    "group"       varchar(24)        not null,
    file_location varchar(255)       not null
);

comment on table sys_file is '文件';

comment on column sys_file.id is '文件编号';

comment on column sys_file.name is '文件名称';

comment on column sys_file.url is '文件地址';

comment on column sys_file.type is '文件类型，图纸/产品手册/检修手册 (1 代表构型关联部件展示专属类型)';

comment on column sys_file.format is '文件格式，docx/png/jpeg';

comment on column sys_file.size is '文件大小，字节单位';

comment on column sys_file.hash_code is '文件哈希值，用于判断文件是否重复上传。';

comment on column sys_file.del_flag is '删除标识符，1：逻辑删除，0：未删除';

comment on column sys_file.create_by is '创建用户编号';

comment on column sys_file.create_time is '创建时间';

comment on column sys_file.modify_by is '修改用户编号';

comment on column sys_file.modify_time is '修改时间';

comment on column sys_file.remark is '备注';

comment on column sys_file."group" is '文件分组（文件删上传服务器后的一个固定属性）';

comment on column sys_file.file_location is '文件远程地址';

alter table sys_file
    owner to phmdbadmin;

create index if not exists sys_file_format_idx
    on sys_file (format);

create index if not exists sys_file_name_idx
    on sys_file (name);

create index if not exists sys_file_type_idx
    on sys_file (type);

create table if not exists sys_location
(
    id          varchar(128) not null
        primary key,
    parent_id   varchar(128),
    parent_ids  varchar,
    node_type   varchar(32),
    name_cn     varchar(128) not null,
    name_en     varchar(128),
    code        varchar(32),
    sort_number integer,
    longitude   numeric(50, 3),
    latitude    numeric(50, 3),
    del_flag    boolean default false,
    create_by   varchar(128),
    create_time timestamp(6),
    modify_by   varchar(128),
    modify_time timestamp(0)
);

comment on column sys_location.id is '主键';

comment on column sys_location.parent_id is '父地域编号';

comment on column sys_location.parent_ids is '父节点集合';

comment on column sys_location.node_type is '节点类型，根节点/子节点/叶节点';

comment on column sys_location.name_cn is '地域中文名称';

comment on column sys_location.name_en is '地域英文名称';

comment on column sys_location.code is '邮政编码';

comment on column sys_location.sort_number is '顺序编号';

comment on column sys_location.longitude is '经度';

comment on column sys_location.latitude is '纬度';

comment on column sys_location.del_flag is '删除状态';

comment on column sys_location.create_by is '创建用户编号';

comment on column sys_location.create_time is '创建时间';

comment on column sys_location.modify_by is '修改用户编号';

comment on column sys_location.modify_time is '修改时间';

alter table sys_location
    owner to phmdbadmin;

create table if not exists sys_menu
(
    id           varchar(64) not null,
    code         varchar(255),
    title        varchar(255),
    parent_id    varchar(64),
    href         varchar(255),
    icon         varchar(255),
    type         varchar(255),
    path         varchar(255),
    enabled      boolean,
    create_by    varchar(255),
    create_date  date,
    update_by    varchar(255),
    update_date  date,
    tenant_id    varchar(64),
    del_flag     char default 0,
    attr1        varchar(255),
    sort         varchar(32),
    redirecting  boolean,
    before_enter varchar(255),
    props        varchar(255),
    regex        varchar(255)
);

comment on table sys_menu is '菜单表';

comment on column sys_menu.id is '主键id';

comment on column sys_menu.code is '路径编码';

comment on column sys_menu.title is '标题';

comment on column sys_menu.parent_id is '父级节点';

comment on column sys_menu.href is '资源路径';

comment on column sys_menu.icon is '图标地址';

comment on column sys_menu.type is '类型';

comment on column sys_menu.path is '菜单上下级关系';

comment on column sys_menu.enabled is '启用禁用';

comment on column sys_menu.create_by is '创建人';

comment on column sys_menu.create_date is '创建时间';

comment on column sys_menu.update_by is '更新人';

comment on column sys_menu.update_date is '更新时间';

comment on column sys_menu.del_flag is '删除标识';

comment on column sys_menu.redirecting is '是否跳转';

comment on column sys_menu.before_enter is '前端需要';

comment on column sys_menu.props is '前端需要';

comment on column sys_menu.regex is '前端需要';

alter table sys_menu
    owner to phmdbadmin;

create table if not exists sys_organization
(
    id          varchar(128) not null
        primary key,
    parent_id   varchar(128) not null,
    node_type   varchar(255),
    name        varchar(255) not null,
    name_en     varchar(255),
    code        varchar(255),
    type        varchar(255),
    location_id varchar(128),
    remark      text,
    create_time timestamp(0),
    create_by   varchar(128),
    modify_time timestamp(0),
    modify_by   varchar(128),
    del_flag    varchar(1)   not null
);

comment on column sys_organization.id is '主键id';

comment on column sys_organization.parent_id is '父节点id，根节点为-1';

comment on column sys_organization.node_type is '节点类型：根节点/子节点/叶节点';

comment on column sys_organization.name is '组织名称';

comment on column sys_organization.name_en is '组织英文名称';

comment on column sys_organization.code is '组织编码';

comment on column sys_organization.type is '组织类型：公司/部门/工作组';

comment on column sys_organization.location_id is '组织归属地id';

comment on column sys_organization.remark is '备注';

comment on column sys_organization.create_time is '创建时间';

comment on column sys_organization.create_by is '创建人';

comment on column sys_organization.modify_time is '修改时间';

comment on column sys_organization.modify_by is '修改人';

comment on column sys_organization.del_flag is '删除标识';

alter table sys_organization
    owner to phmdbadmin;

create table if not exists sys_organization_user
(
    user_id         varchar(128) not null,
    organization_id varchar(128) not null
);

alter table sys_organization_user
    owner to phmdbadmin;

create table if not exists sys_role
(
    id                varchar(128) not null
        primary key,
    role_code         varchar(255),
    role_name         varchar(255),
    parent_id         varchar(128),
    role_path         varchar(255),
    role_type         varchar(255),
    remarks           varchar(255),
    create_by         varchar(64),
    create_date       date,
    update_by         varchar(64),
    update_date       date,
    tenant_id         varchar(64),
    del_flag          char          default 0,
    selected_menu_ids varchar(2048) default NULL::character varying,
    data_permission   varchar(32),
    home_menu_id      varchar(32)
);

comment on table sys_role is '角色表';

comment on column sys_role.id is '主键id';

comment on column sys_role.role_code is '角色编码';

comment on column sys_role.role_name is '角色名称';

comment on column sys_role.parent_id is '上级节点';

comment on column sys_role.role_path is '树状关系';

comment on column sys_role.role_type is '角色类型';

comment on column sys_role.remarks is '备注';

comment on column sys_role.create_by is '创建人';

comment on column sys_role.create_date is '创建时间';

comment on column sys_role.update_by is '更新人';

comment on column sys_role.update_date is '更新时间';

comment on column sys_role.tenant_id is '租户id';

comment on column sys_role.del_flag is '删除标识';

comment on column sys_role.selected_menu_ids is '前端需要单独返回的 选中的menu_id,不带父menu_id';

comment on column sys_role.data_permission is '数据权限（个人，所有，公司等）';

comment on column sys_role.home_menu_id is '默认首页菜单id';

alter table sys_role
    owner to phmdbadmin;

create table if not exists sys_role_menu
(
    role_id varchar(128) not null,
    menu_id varchar(128) not null,
    primary key (role_id, menu_id)
);

comment on column sys_role_menu.role_id is '角色类型id';

comment on column sys_role_menu.menu_id is '菜单id';

alter table sys_role_menu
    owner to phmdbadmin;

create table if not exists sys_role_user
(
    role_id varchar(128),
    user_id varchar(128)
);

comment on table sys_role_user is '角色和用户关系表';

alter table sys_role_user
    owner to phmdbadmin;

create table if not exists sys_user
(
    id              varchar(128) not null
        primary key,
    username        varchar(255),
    password        varchar(255),
    name            varchar(255),
    birthday        varchar(255),
    address         varchar(255),
    mobile_phone    varchar(255),
    tel_phone       varchar(255),
    email           varchar(255),
    sex             char(10),
    type            varchar(255),
    remarks         varchar(255),
    create_by       varchar(128),
    create_date     timestamp(0),
    update_by       varchar(128),
    update_date     timestamp(0),
    del_flag        char,
    tenant_id       varchar(64),
    is_super_admin  varchar(16),
    role_id         varchar(128),
    organization_id varchar(128),
    image           varchar(255),
    user_role_ids   varchar(255)
);

comment on table sys_user is '用户表';

comment on column sys_user.id is '主键id';

comment on column sys_user.username is '用户名';

comment on column sys_user.password is '密码';

comment on column sys_user.name is '名字';

comment on column sys_user.birthday is '生日';

comment on column sys_user.address is '联系地址';

comment on column sys_user.mobile_phone is '手机号码';

comment on column sys_user.tel_phone is '电话号码';

comment on column sys_user.email is '邮箱';

comment on column sys_user.sex is '性别';

comment on column sys_user.type is '用户类型（暂时未用）';

comment on column sys_user.remarks is '备注';

comment on column sys_user.create_by is '创建人';

comment on column sys_user.create_date is '创建日期';

comment on column sys_user.update_by is '更新人';

comment on column sys_user.update_date is '更新日期';

comment on column sys_user.del_flag is '删除标识';

comment on column sys_user.tenant_id is '租户Id';

comment on column sys_user.is_super_admin is '是否是超级管理员标识（暂时未用）';

comment on column sys_user.role_id is '角色id（暂时未用）';

comment on column sys_user.organization_id is '组织id（暂时未用）';

comment on column sys_user.image is '头像地址';

comment on column sys_user.user_role_ids is '用户角色id字符串(有序拼接)';

alter table sys_user
    owner to phmdbadmin;

create table if not exists sys_log
(
    id            varchar(128) not null
        constraint sys_log_copy_pkey
            primary key,
    opt           varchar(255),
    uri           varchar(255),
    user_id       varchar(128),
    date          timestamp(0),
    name          varchar(255),
    host          varchar(60),
    status        char,
    user_name     varchar(255),
    params        text,
    user_agent    varchar(512),
    exception     text,
    opt_type      varchar(8),
    response_time bigint,
    menu_path     varchar(64)
);

comment on table sys_log is '系统日志表';

comment on column sys_log.opt is '操作';

comment on column sys_log.uri is '资源路径';

comment on column sys_log.user_id is '操作人ID';

comment on column sys_log.date is '操作时间';

comment on column sys_log.name is '操作人真实名字';

comment on column sys_log.host is '操作主机';

comment on column sys_log.status is '是否正常，1代表正常，0代表异常';

comment on column sys_log.user_name is '登录名';

comment on column sys_log.params is '参数';

comment on column sys_log.user_agent is '浏览器';

comment on column sys_log.exception is '异常信息';

comment on column sys_log.opt_type is '操作类型';

comment on column sys_log.response_time is '响应时间(ms)';

comment on column sys_log.menu_path is '菜单Path';

alter table sys_log
    owner to phmdbadmin;

create index if not exists sys_log_date_copy_index
    on sys_log (date);

create table if not exists mtr_software_manual_history
(
    id                     varchar(128) not null
        constraint mtr_software_copy1_pkey
            primary key,
    name                   varchar(128) not null,
    version                varchar(32)  not null,
    manufacturer_id        varchar(128),
    description            text,
    create_by              varchar(128),
    create_time            timestamp(0),
    modify_by              varchar(128),
    modify_time            timestamp(0),
    vehicle_structure_code varchar(64),
    vehicle_code           varchar(255),
    end_time               timestamp(6),
    signal_name_en         varchar(255),
    operator               varchar(255),
    effective_time         timestamp(6),
    line_id                varchar(128),
    status                 smallint default 0,
    subsystem              varchar(128),
    location               varchar(128),
    constraint mtr_software_copy1_name_version_signal_name_en_vehicle_code_key
        unique (name, version, signal_name_en, vehicle_code)
);

alter table mtr_software_manual_history
    owner to phmdbadmin;

create index if not exists mtr_software_name_idx_copy1
    on mtr_software_manual_history (name);

create table if not exists mtc_alarm_warning_rule_test
(
    id                     varchar(128) not null,
    line_id                varchar(128),
    vehicle_id             varchar(128) not null,
    vehicle_type_id        varchar(128),
    vehicle_structure_code varchar(200),
    fault_type_key         varchar(64),
    component_id           varchar(128),
    subsystem              varchar(200),
    location               varchar(64),
    fault_name_cn          varchar(128),
    fault_name_en          varchar(128),
    fault_code             varchar(128),
    fault_level            smallint,
    alarm_rule_id          varchar(128),
    alarm_snapshot         text,
    start_time             timestamp(0) not null,
    end_time               timestamp(0),
    status                 varchar(2)   default 0,
    running_status         boolean,
    suppressed_by          varchar(64),
    suppressed_status      boolean,
    fault_source           varchar(2),
    create_time            timestamp(6) default now(),
    modify_time            timestamp(6),
    modify_by              varchar(128),
    params                 text,
    end_status             integer      default 0
);

comment on column mtc_alarm_warning_rule_test.params is 'JSON数据(mtc_signal_params_relation配置的信号)';

comment on column mtc_alarm_warning_rule_test.end_status is '解除状态 0未解除 1解除';

alter table mtc_alarm_warning_rule_test
    owner to phmdbadmin;

create index if not exists mtc_alarm_warning_rule_test_end_time
    on mtc_alarm_warning_rule_test (end_time, fault_source, running_status, line_id);

create index if not exists mtc_alarm_warning_rule_test_fault_source_idx
    on mtc_alarm_warning_rule_test (fault_source varchar_ops);

create table if not exists mtr_flaw_detection_product
(
    id                varchar(128) not null
        constraint mtr_flaw_detection_product_pk
            primary key,
    flaw_detection_id bigint       not null,
    type              char,
    code              varchar(32)  not null,
    crack             char,
    amount            integer,
    check_date        varchar(32),
    evaluation_result char
);

comment on column mtr_flaw_detection_product.id is '主键id';

comment on column mtr_flaw_detection_product.flaw_detection_id is '关联mtr_flaw_detection_record表id';

comment on column mtr_flaw_detection_product.type is '探伤类型(1-涡流 2-磁粉 3-超声波)';

comment on column mtr_flaw_detection_product.code is '产品编号';

comment on column mtr_flaw_detection_product.crack is '有无裂纹';

comment on column mtr_flaw_detection_product.amount is '数量';

comment on column mtr_flaw_detection_product.check_date is '日期';

comment on column mtr_flaw_detection_product.evaluation_result is '评定结果';

alter table mtr_flaw_detection_product
    owner to phmdbadmin;

create table if not exists mtr_flaw_detection_record
(
    id                 varchar(128) not null
        constraint mtr_flaw_detection_record_pk
            primary key,
    original           varchar(16)  not null,
    present            varchar(16)  not null,
    type               char,
    product            varchar(32),
    basic_info_json    text,
    situation          text,
    self_check         varchar(32),
    self_check_date    varchar(32),
    mutual_check       varchar(32),
    mutual_check_date  varchar(32),
    special_check      varchar(32),
    special_check_date varchar(32),
    director           varchar(32),
    check_date         varchar(32),
    check_result       char,
    del_flag           char,
    create_by          bigint       not null,
    create_time        timestamp(0) not null,
    modifty_by         bigint,
    moddify_time       timestamp(0)
);

comment on column mtr_flaw_detection_record.id is '主键id';

comment on column mtr_flaw_detection_record.original is '原车号';

comment on column mtr_flaw_detection_record.present is '现装车号';

comment on column mtr_flaw_detection_record.type is '检测方式(1-涡流 2-磁粉 3-超声波)';

comment on column mtr_flaw_detection_record.product is '产品名称';

comment on column mtr_flaw_detection_record.basic_info_json is '基础信息';

comment on column mtr_flaw_detection_record.situation is '作业情况';

comment on column mtr_flaw_detection_record.self_check is '自检';

comment on column mtr_flaw_detection_record.self_check_date is '自检日期';

comment on column mtr_flaw_detection_record.mutual_check is '互检';

comment on column mtr_flaw_detection_record.mutual_check_date is '互检日期';

comment on column mtr_flaw_detection_record.special_check is '专检';

comment on column mtr_flaw_detection_record.special_check_date is '专检日期';

comment on column mtr_flaw_detection_record.director is '质量负责人';

comment on column mtr_flaw_detection_record.check_date is '检查日期';

comment on column mtr_flaw_detection_record.check_result is '检查结果';

comment on column mtr_flaw_detection_record.del_flag is '1-删除';

comment on column mtr_flaw_detection_record.create_by is '创建人';

comment on column mtr_flaw_detection_record.create_time is '创建时间';

comment on column mtr_flaw_detection_record.modifty_by is '修改人';

comment on column mtr_flaw_detection_record.moddify_time is '修改时间';

alter table mtr_flaw_detection_record
    owner to phmdbadmin;

create table if not exists mtr_wheel_measurement_data
(
    id                     varchar(128) not null
        constraint mtr_wheel_measurement_data_pk
            primary key,
    measurement_vehicle_id bigint       not null,
    name_cn                varchar(16)  not null,
    frame                  varchar(16)  not null,
    axle                   varchar(16)  not null,
    location               varchar(16)  not null,
    diameters              double precision,
    thickness              double precision,
    height                 double precision,
    qr                     double precision,
    runout                 double precision,
    innerside_distance     double precision
);

comment on column mtr_wheel_measurement_data.id is '主键id';

comment on column mtr_wheel_measurement_data.measurement_vehicle_id is '关联mtr_wheel_measurement_vehicle id';

comment on column mtr_wheel_measurement_data.name_cn is '车厢';

comment on column mtr_wheel_measurement_data.frame is '转向架 方便计算';

comment on column mtr_wheel_measurement_data.axle is '轴';

comment on column mtr_wheel_measurement_data.location is '轴位置';

comment on column mtr_wheel_measurement_data.diameters is '轮径';

comment on column mtr_wheel_measurement_data.thickness is '轮缘厚度';

comment on column mtr_wheel_measurement_data.height is '轮缘高度';

comment on column mtr_wheel_measurement_data.qr is 'qR值';

comment on column mtr_wheel_measurement_data.runout is '轮径跳动';

comment on column mtr_wheel_measurement_data.innerside_distance is '内侧距';

alter table mtr_wheel_measurement_data
    owner to phmdbadmin;

create table if not exists mtr_wheel_measurement_vehicle
(
    id           varchar(128) not null
        constraint mtr_wheel_measurement_vehicle_pk
            primary key,
    vehicle_code varchar(16)  not null,
    mileage      varchar(32)  not null,
    status       char,
    recorder     varchar(16),
    checkdate    varchar(16),
    del_flag     char,
    create_by    bigint       not null,
    create_time  timestamp(0) not null,
    modify_by    bigint,
    modify_time  timestamp(0)
);

comment on column mtr_wheel_measurement_vehicle.id is '主键id';

comment on column mtr_wheel_measurement_vehicle.vehicle_code is '车辆编号';

comment on column mtr_wheel_measurement_vehicle.mileage is '里程';

comment on column mtr_wheel_measurement_vehicle.status is '0-暂存  1-提交';

comment on column mtr_wheel_measurement_vehicle.recorder is '记录人';

comment on column mtr_wheel_measurement_vehicle.checkdate is '检查时间';

comment on column mtr_wheel_measurement_vehicle.del_flag is '1-删除';

comment on column mtr_wheel_measurement_vehicle.create_by is '创建人';

comment on column mtr_wheel_measurement_vehicle.create_time is '创建时间';

comment on column mtr_wheel_measurement_vehicle.modify_by is '修改人';

comment on column mtr_wheel_measurement_vehicle.modify_time is '修改时间';

alter table mtr_wheel_measurement_vehicle
    owner to phmdbadmin;

create table if not exists od_operation_decision
(
    id                 varchar(64) not null
        primary key,
    scheduling_code    varchar(64),
    decision_type      varchar(8),
    recommend_decision text,
    decision_basis     text,
    push_time          timestamp(6),
    operate_time       timestamp(6),
    operate_by         bigint,
    status             varchar(8),
    status_flag        smallint,
    decision_substance varchar(64),
    subsystem          varchar(64),
    location           varchar(64),
    decision_id        varchar(64)
);

comment on table od_operation_decision is '运维决策表';

comment on column od_operation_decision.id is '主键';

comment on column od_operation_decision.scheduling_code is '决策编码';

comment on column od_operation_decision.decision_type is '决策类型：车辆检修决策...';

comment on column od_operation_decision.recommend_decision is '决策内容';

comment on column od_operation_decision.decision_basis is '决策依据';

comment on column od_operation_decision.push_time is '推送时间';

comment on column od_operation_decision.operate_time is '操作时间';

comment on column od_operation_decision.operate_by is '操作员';

comment on column od_operation_decision.status is '状态标识 0:待处理  1:延后  2:完成 3:忽略';

comment on column od_operation_decision.status_flag is '状态标识：0未处理，1已处理';

comment on column od_operation_decision.decision_substance is '决策对象';

comment on column od_operation_decision.subsystem is '所属子系统';

comment on column od_operation_decision.location is '所在车厢';

comment on column od_operation_decision.decision_id is '决策id 详细决策表的主键id';

alter table od_operation_decision
    owner to phmdbadmin;

create table if not exists od_operation_plan_detail
(
    id                   varchar(64) not null
        primary key,
    scheduling_code      varchar(32),
    start_time           varchar(16),
    end_time             varchar(16),
    commissioning_count  integer,
    interval_time        integer,
    line_id              varchar(64),
    vehicle_speed        integer,
    start_switching_time integer,
    end_switching_time   integer,
    ride_rate            real,
    history_ride_count   varchar(64),
    remark               varchar(255),
    valid                smallint,
    craete_by            varchar(64),
    create_time          timestamp(0),
    modify_by            varchar(64),
    modify_time          timestamp(0),
    push_status          varchar(16) default 0
);

comment on table od_operation_plan_detail is '运营计划决策 详情表';

comment on column od_operation_plan_detail.id is '主键';

comment on column od_operation_plan_detail.scheduling_code is '决策编码';

comment on column od_operation_plan_detail.start_time is '开始时间  时：分';

comment on column od_operation_plan_detail.end_time is '结束时间  时：分';

comment on column od_operation_plan_detail.commissioning_count is '投运数量 单位：辆';

comment on column od_operation_plan_detail.interval_time is '发车时间间隔 单位：分钟';

comment on column od_operation_plan_detail.line_id is '外键,线路表主键';

comment on column od_operation_plan_detail.vehicle_speed is '平均车速';

comment on column od_operation_plan_detail.start_switching_time is '始发站调头时间：单位分';

comment on column od_operation_plan_detail.end_switching_time is '终点站调头时间：单位分';

comment on column od_operation_plan_detail.ride_rate is '乘车率';

comment on column od_operation_plan_detail.history_ride_count is '历史某时间段平均乘车人数总Z(比值)';

comment on column od_operation_plan_detail.remark is '决策描述';

comment on column od_operation_plan_detail.valid is '有效性 1:有效 0:无效';

comment on column od_operation_plan_detail.craete_by is '创建用户编号';

comment on column od_operation_plan_detail.create_time is '创建时间';

comment on column od_operation_plan_detail.modify_by is '修改用户编号';

comment on column od_operation_plan_detail.modify_time is '修改时间';

comment on column od_operation_plan_detail.push_status is '推送状态:0:不推送  1:推送 ';

alter table od_operation_plan_detail
    owner to phmdbadmin;

create table if not exists od_vehicle_recondition_decision
(
    id                 varchar(64) not null
        primary key,
    substance          varchar(64),
    scheduling_code    varchar(32),
    recommend_decision varchar(255),
    status             varchar(16),
    trigger_time       timestamp(0),
    create_time        timestamp(0),
    remark             varchar(255),
    submit_status      varchar(16) default 0,
    subsystem          varchar(64),
    location           varchar(64)
);

comment on table od_vehicle_recondition_decision is '车辆检修决策表';

comment on column od_vehicle_recondition_decision.id is '主键id';

comment on column od_vehicle_recondition_decision.substance is '决策对象';

comment on column od_vehicle_recondition_decision.scheduling_code is '决策编码';

comment on column od_vehicle_recondition_decision.recommend_decision is '推荐决策(决策内容)';

comment on column od_vehicle_recondition_decision.status is '状态标识 0:待处理  1:延后  2:完成 3:忽略';

comment on column od_vehicle_recondition_decision.trigger_time is '触发时间 初始值为生成时间';

comment on column od_vehicle_recondition_decision.create_time is '创建时间';

comment on column od_vehicle_recondition_decision.remark is '备注';

comment on column od_vehicle_recondition_decision.submit_status is '提交状态 0: 未提交 1:已提交  该字段保留，目前没有使用场景';

comment on column od_vehicle_recondition_decision.subsystem is '所属子系统';

comment on column od_vehicle_recondition_decision.location is '所在车厢';

alter table od_vehicle_recondition_decision
    owner to phmdbadmin;

create table if not exists od_vehicle_service_detail
(
    id                    varchar(64) not null
        primary key,
    operation_decision_id bigint,
    whether_service       varchar(2),
    priority              varchar(8),
    health_degree         smallint,
    decision_basis        text,
    vehicle_id            bigint
);

comment on table od_vehicle_service_detail is '车辆投运决策详情表';

comment on column od_vehicle_service_detail.id is '主键';

comment on column od_vehicle_service_detail.operation_decision_id is '外键，引用运维决策表主键';

comment on column od_vehicle_service_detail.whether_service is '是否投运';

comment on column od_vehicle_service_detail.priority is '优先级：高/中/低优先级';

comment on column od_vehicle_service_detail.health_degree is '车辆健康度';

comment on column od_vehicle_service_detail.decision_basis is '决策依据';

comment on column od_vehicle_service_detail.vehicle_id is '车辆id，mtr_vehicle表主键';

alter table od_vehicle_service_detail
    owner to phmdbadmin;

create table if not exists mtc_fault_record
(
    id                  varchar(128) not null
        constraint mtc_manual_fault_record_pkey
            primary key,
    sys_responsible_by  varchar(64),
    found_by            varchar(64),
    treatment           text,
    fault_info          text,
    treatment_by        varchar(64),
    treatment_company   varchar(64),
    treatment_time      timestamp(0),
    comit_by            varchar(64),
    input_by            varchar(64),
    material_usage      text,
    material_ascription varchar(255),
    fault_location      varchar(255),
    fault_id            varchar(64),
    fault_source        varchar(8),
    line_id             varchar(128),
    vehicle_id          varchar(64),
    modify_by           varchar,
    confirm             integer,
    modify_time         timestamp(0),
    start_time          timestamp(0),
    confirm_time        timestamp(0),
    fault_name_cn       varchar(128)
);

comment on column mtc_fault_record.sys_responsible_by is '系统负责人';

comment on column mtc_fault_record.found_by is '发现人';

comment on column mtc_fault_record.treatment is '处理情况';

comment on column mtc_fault_record.fault_info is '工程师故障描述与确认';

comment on column mtc_fault_record.treatment_by is '处理人';

comment on column mtc_fault_record.treatment_company is '处理单位';

comment on column mtc_fault_record.treatment_time is '处理时间';

comment on column mtc_fault_record.comit_by is '确认人';

comment on column mtc_fault_record.input_by is '录入人';

comment on column mtc_fault_record.material_usage is '用料情况';

comment on column mtc_fault_record.material_ascription is '用料归口';

comment on column mtc_fault_record.fault_location is '故障位置';

comment on column mtc_fault_record.fault_id is '关联故障代码表';

comment on column mtc_fault_record.fault_source is '故障类型 0 自动故障 2 人工故障...';

comment on column mtc_fault_record.line_id is '线路编号';

comment on column mtc_fault_record.vehicle_id is '车辆主键';

comment on column mtc_fault_record.modify_by is '修改人id';

comment on column mtc_fault_record.confirm is '确认状态 0：未确认 1:确认';

comment on column mtc_fault_record.modify_time is '修改时间';

comment on column mtc_fault_record.start_time is '故障发生时间';

comment on column mtc_fault_record.confirm_time is '确认时间';

comment on column mtc_fault_record.fault_name_cn is '故障中文名称';

alter table mtc_fault_record
    owner to phmdbadmin;

create table if not exists monitor_trigger_back20220919
(
    id                 varchar(64),
    slot_id            varchar(64),
    label              varchar(100),
    sort               integer,
    signal_id          varchar(128),
    trigger_value      varchar(64),
    data_display_point varchar(100),
    unit_status        boolean,
    svg_url            varchar(255),
    ext_properties     varchar(1024),
    create_time        timestamp,
    create_by          varchar(64),
    modify_time        timestamp,
    modify_by          varchar(64),
    del_flag           boolean,
    image_type         varchar(255),
    image_path         varchar(255),
    signal_name_en     varchar(64),
    mark               varchar(8)
);

alter table monitor_trigger_back20220919
    owner to phmdbadmin;

create table if not exists mtc_signal_params_relation
(
    id              varchar(128)           not null,
    signal_id       varchar(128)           not null,
    signal_name     varchar(255),
    line_id         varchar(128),
    vehicle_type_id varchar(128),
    protocol_id     varchar(128),
    param_key       varchar(255)           not null,
    type            varchar(255) default 0 not null
);

comment on column mtc_signal_params_relation.id is '主键';

comment on column mtc_signal_params_relation.signal_id is '信号id';

comment on column mtc_signal_params_relation.signal_name is '信号名称';

comment on column mtc_signal_params_relation.line_id is '线路id';

comment on column mtc_signal_params_relation.vehicle_type_id is '车型id';

comment on column mtc_signal_params_relation.protocol_id is '协议id';

comment on column mtc_signal_params_relation.param_key is '业务键值key';

comment on column mtc_signal_params_relation.type is '0(自动上报所需)、1(机理规则所需)';

alter table mtc_signal_params_relation
    owner to phmdbadmin;

create table if not exists mtc_manual_alarm_warning
(
    id                     varchar(128),
    line_id                varchar(128),
    vehicle_id             varchar(128),
    vehicle_type_id        varchar(128),
    vehicle_structure_code varchar(200),
    fault_type_key         varchar(64),
    subsystem              varchar(200),
    location               varchar(64),
    fault_name_cn          varchar(128),
    fault_name_en          varchar(128),
    fault_code             varchar(128),
    fault_level            smallint,
    start_time             timestamp,
    end_time               timestamp,
    status                 varchar(2) default 0,
    running_status         boolean    default true,
    fault_source           varchar(2),
    create_time            timestamp,
    modify_time            timestamp,
    modify_by              varchar(128),
    end_status             integer
);

comment on table mtc_manual_alarm_warning is '人工故障';

comment on column mtc_manual_alarm_warning.id is '主键';

comment on column mtc_manual_alarm_warning.line_id is '线路id';

comment on column mtc_manual_alarm_warning.vehicle_id is '车辆id';

comment on column mtc_manual_alarm_warning.vehicle_type_id is '车型id';

comment on column mtc_manual_alarm_warning.vehicle_structure_code is '构型编码';

comment on column mtc_manual_alarm_warning.fault_type_key is '故障业务主键';

comment on column mtc_manual_alarm_warning.subsystem is '故障系统';

comment on column mtc_manual_alarm_warning.location is '故障位置';

comment on column mtc_manual_alarm_warning.fault_name_cn is '故障中文名';

comment on column mtc_manual_alarm_warning.fault_name_en is '故障英文名';

comment on column mtc_manual_alarm_warning.fault_code is '故障编码';

comment on column mtc_manual_alarm_warning.fault_level is '故障等级';

comment on column mtc_manual_alarm_warning.start_time is '发生时间';

comment on column mtc_manual_alarm_warning.end_time is '解除时间';

comment on column mtc_manual_alarm_warning.status is '状态';

comment on column mtc_manual_alarm_warning.running_status is '正线状态';

comment on column mtc_manual_alarm_warning.fault_source is '故障类型';

comment on column mtc_manual_alarm_warning.create_time is '创建时间';

comment on column mtc_manual_alarm_warning.modify_time is '修改时间';

comment on column mtc_manual_alarm_warning.modify_by is '修改时间';

comment on column mtc_manual_alarm_warning.end_status is '解除状态';

alter table mtc_manual_alarm_warning
    owner to phmdbadmin;

create index if not exists mtc_manual_alarm_warning_start_time_index
    on mtc_manual_alarm_warning (start_time);

comment on index mtc_manual_alarm_warning_start_time_index is '发生时间索引';

create index if not exists mtc_manual_alarm_warning_vehicle_id_index
    on mtc_manual_alarm_warning (vehicle_id);

comment on index mtc_manual_alarm_warning_vehicle_id_index is '车辆索引';

create table if not exists comm_original_signal_new_20221110
(
    id            varchar(128),
    protocol_id   varchar(128),
    name_cn       varchar(128),
    name_en       varchar(128),
    byte_offset   integer,
    bit_offset    smallint,
    data_type     varchar(32),
    unit          varchar(16),
    location      varchar(16),
    subsystem     varchar(32),
    parse_script  text,
    create_by     varchar(128),
    create_time   timestamp,
    modify_by     varchar(128),
    modify_time   timestamp,
    remark        text,
    result_type   varchar(32),
    target_id     varchar(128),
    target_name   varchar(128),
    port_no       varchar(20),
    "ifRepeat"    varchar(2),
    fault_flag    varchar(2),
    signal_type   varchar(2),
    frames_type   integer,
    package_order integer
);

alter table comm_original_signal_new_20221110
    owner to phmdbadmin;

create table if not exists comm_original_signal_20221110
(
    id             varchar(128),
    protocol_id    varchar(128),
    name_cn        varchar(128),
    name_en        varchar(128),
    byte_offset    integer,
    bit_offset     smallint,
    data_type      varchar(32),
    unit           varchar(16),
    location       varchar(16),
    subsystem      varchar(32),
    parse_script   text,
    fault_type_key varchar(64),
    trigger_value  smallint,
    max_value      double precision,
    min_value      double precision,
    create_by      varchar(128),
    create_time    timestamp,
    modify_by      varchar(128),
    modify_time    timestamp,
    remark         text,
    redis_flag     char,
    result_type    varchar(32),
    frames_type    smallint,
    package_order  smallint
);

alter table comm_original_signal_20221110
    owner to phmdbadmin;

create table if not exists ekb_fault_type_20221110
(
    id                                 varchar(128),
    vehicle_type_id                    varchar(128),
    fault_type_key                     varchar(64),
    vehicle_structure_code             varchar(64),
    name_cn                            varchar(128),
    name_en                            varchar(128),
    fault_code                         varchar(64),
    fault_level                        smallint,
    subsystem                          varchar(32),
    del_flag                           smallint,
    create_by                          varchar(128),
    create_time                        timestamp,
    modify_by                          varchar(128),
    modify_time                        timestamp,
    description                        text,
    location                           varchar(256),
    enable                             boolean,
    fault_mode                         varchar(2),
    frontline_disposal_recommendations text,
    overhaul_suggestions               text,
    fault_reason                       text,
    model_code                         varchar(255),
    line_id                            varchar(128),
    fault_category                     smallint
);

alter table ekb_fault_type_20221110
    owner to phmdbadmin;

create table if not exists sys_dict_back20221115
(
    id                varchar(128),
    sort_number       bigint,
    code              varchar(255),
    label             varchar(255),
    value             varchar(255),
    type_id           varchar(128),
    description       varchar(255),
    modify_time       timestamp,
    modify_by         varchar(128),
    create_time       timestamp,
    create_by         varchar(128),
    del_flag          varchar(1),
    line_id           varchar(128),
    vehicle_type_id   varchar(128),
    line_name         varchar(255),
    vehicle_type_name varchar(255),
    type_code         varchar(255)
);

alter table sys_dict_back20221115
    owner to phmdbadmin;

create table if not exists sys_dict_type_back20221115
(
    id          varchar(128),
    type        varchar(255),
    description text,
    create_time timestamp,
    create_by   varchar(128),
    modify_time timestamp,
    modify_by   varchar(128),
    del_flag    varchar(1),
    type_level  char
);

alter table sys_dict_type_back20221115
    owner to phmdbadmin;

create or replace view v_car_system(label, value) as
SELECT dict.label,
       dict.value
FROM sys_dict dict
         LEFT JOIN sys_dict_type ON dict.type_id::text = sys_dict_type.id::text
WHERE sys_dict_type.type::text = 'ass_car_system'::text
  AND sys_dict_type.del_flag::text = '0'::text
  AND dict.del_flag::text = '0'::text;

alter table v_car_system
    owner to phmdbadmin;

create or replace view v_comm_signal
            (signal_type, id, protocol_id, name_cn, name_en, byte_offset, bit_offset, data_type, unit, location,
             subsystem, parse_script, fault_type_key, trigger_value, max_value, min_value, create_by, create_time,
             remark, enable, vehicle_type_id, fault_code, fault_name_cn, fault_name_en)
as
SELECT vos.signal_type,
       vos.id,
       vos.protocol_id,
       vos.name_cn,
       vos.name_en,
       vos.byte_offset,
       vos.bit_offset,
       vos.data_type,
       vos.unit,
       vos.location,
       vos.subsystem,
       vos.parse_script,
       vos.fault_type_key,
       vos.trigger_value,
       vos.max_value,
       vos.min_value,
       vos.create_by,
       vos.create_time,
       vos.remark,
       vos.enable,
       vos.vehicle_type_id,
       eft.fault_code,
       eft.name_cn AS fault_name_cn,
       eft.name_en AS fault_name_en
FROM (SELECT 0 AS signal_type,
             cos.id,
             cos.protocol_id,
             cos.name_cn,
             cos.name_en,
             cos.byte_offset,
             cos.bit_offset,
             cos.data_type,
             cos.unit,
             cos.location,
             cos.subsystem,
             cos.parse_script,
             cos.fault_type_key,
             cos.trigger_value,
             cos.max_value,
             cos.min_value,
             cos.create_by,
             cos.create_time,
             cos.remark,
             cp.enable,
             cp.vehicle_type_id
      FROM comm_original_signal cos,
           comm_protocol cp
      WHERE cos.protocol_id::text = cp.id::text) vos
         LEFT JOIN ekb_fault_type eft ON vos.vehicle_type_id::text = eft.vehicle_type_id::text AND
                                         vos.fault_type_key::text = eft.fault_type_key::text
UNION
SELECT 1                       AS signal_type,
       ces.id,
       ces.protocol_id,
       ces.name_cn,
       ces.name_en,
       NULL::integer           AS byte_offset,
       NULL::smallint          AS bit_offset,
       ces.data_type,
       ces.unit,
       ces.location,
       ces.subsystem,
       ces.parse_script,
       NULL::character varying AS fault_type_key,
       NULL::smallint          AS trigger_value,
       ces.max_value,
       ces.min_value,
       ces.create_by,
       ces.create_time,
       ces.remark,
       cp.enable,
       cp.vehicle_type_id,
       NULL::character varying AS fault_code,
       NULL::character varying AS fault_name_cn,
       NULL::character varying AS fault_name_en
FROM comm_extend_signal ces,
     comm_protocol cp
WHERE ces.protocol_id::text = cp.id::text;

alter table v_comm_signal
    owner to phmdbadmin;

create or replace view v_protocol_vehicletype_packet
            (id, vehicle_type_id, name, version, size, endian, create_by, enable, vehicle_type_name, create_time,
             line_id, vehicle_count)
as
SELECT cp.id,
       cp.vehicle_type_id,
       cp.name,
       cp.version,
       cp.size,
       cp.endian,
       cp.create_by,
       cp.enable,
       mvt.name               AS vehicle_type_name,
       cp.create_time,
       cp.line_id,
       (SELECT count(1) AS count
        FROM comm_vehicle_tcp_protocol cvtp
        WHERE cp.id::text = cvtp.protocol_id::text
          AND cvtp.valid = 1) AS vehicle_count
FROM comm_protocol cp,
     mtr_vehicle_type mvt
WHERE cp.del_flag = 0
  AND cp.vehicle_type_id::text = mvt.id::text;

alter table v_protocol_vehicletype_packet
    owner to phmdbadmin;

create or replace function pgpool_recovery(script_name text, remote_host text, remote_data_directory text, primary_port text, remote_node integer, remote_port text) returns boolean
    strict
    language c
as
$$
begin
-- missing source code
end;
$$;

alter function pgpool_recovery(text, text, text, text, integer, text) owner to postgres;

create or replace function pgpool_recovery(script_name text, remote_host text, remote_data_directory text, primary_port text, remote_node integer) returns boolean
    strict
    language c
as
$$
begin
-- missing source code
end;
$$;

alter function pgpool_recovery(text, text, text, text, integer) owner to postgres;

create or replace function pgpool_recovery(script_name text, remote_host text, remote_data_directory text, primary_port text) returns boolean
    strict
    language c
as
$$
begin
-- missing source code
end;
$$;

alter function pgpool_recovery(text, text, text, text) owner to postgres;

create or replace function pgpool_recovery(script_name text, remote_host text, remote_data_directory text) returns boolean
    strict
    language c
as
$$
begin
-- missing source code
end;
$$;

alter function pgpool_recovery(text, text, text) owner to postgres;

create or replace function pgpool_remote_start(remote_host text, remote_data_directory text) returns boolean
    strict
    language c
as
$$
begin
-- missing source code
end;
$$;

alter function pgpool_remote_start(text, text) owner to postgres;

create or replace function pgpool_pgctl(action text, stop_mode text) returns boolean
    strict
    language c
as
$$
begin
-- missing source code
end;
$$;

alter function pgpool_pgctl(text, text) owner to postgres;

create or replace function pgpool_switch_xlog(arcive_dir text) returns text
    strict
    language c
as
$$
begin
-- missing source code
end;
$$;

alter function pgpool_switch_xlog(text) owner to postgres;

create or replace function decode(VARIADIC p_decode_list text[]) returns text
    language plpgsql
as
$$
declare
    -- 获取数组长度(即入参个数)
    v_len integer := array_length(p_decode_list, 1);
    -- 声明存放返回值的变量
    v_ret text;
begin
    /*
    * 功能说明:模拟Oracle中的DECODE功能(字符串处理, 其它格式可以自行转换返回值)
    * 参数说明:格式同Oracle相同,至少三个参数
    * 实现原理: 1、VARIADIC 允许变参; 2、Oracle中的DECODE是拿第一个数依次和之后的偶数位值进行比较,相同则取偶数位+1的数值,否则取最后一位值(最后一位为偶数为,否则为null)
    */

    -- 同Oracle相同当参数不足三个抛出异常
    if v_len >= 3 then
        -- Oracle中的DECODE是拿第一个数依次和之后的偶数位值进行比较,相同则取偶数位+1的数值
        for i in 2..(v_len - 1) loop
                v_ret := null;
                if mod(i, 2) = 0 then
                    if p_decode_list[1] = p_decode_list[i] then
                        v_ret := p_decode_list[i+1];
                    elsif p_decode_list[1] <> p_decode_list[i] then
                        if v_len = i + 2 and v_len > 3 then
                            v_ret := p_decode_list[v_len];
                        end if;
                    end if;
                end if;
                exit when v_ret is not null;
            end loop;
    else
        raise exception 'UPG-00938: not enough args for function.';
    end if;
    return v_ret;
end;
$$;

alter function decode(text[]) owner to phmdbadmin;

create or replace function duplicate_protocol(op_id integer) returns boolean
    language plpgsql
as
$$
DECLARE
    np_id INTEGER;
    nid INTEGER;
    sql varchar;
    r record;
BEGIN
    -- updated by Deng Ran on 2018.09.27
    -- set the version to original one
    insert into wtd_protocol(name,version ,flag_status,line_id,pro_id) select name|| '_copy', version ,0,line_id,pro_id from wtd_protocol where p_id = op_id RETURNING p_id into np_id;

    sql := 'select id,value,frametype_dic_id from wtd_protocol_frametype where p_id = ' || op_id || '';
    FOR r IN EXECUTE sql LOOP
            insert into wtd_protocol_frametype(p_id,value,frametype_dic_id) select np_id,r.value,r.frametype_dic_id from wtd_protocol_frametype where id = r.id and p_id = op_id RETURNING id into nid;
            insert into wtd_signal(v_id ,
                                   p_id ,
                                   description,
                                   field_position ,
                                   byte_offset ,
                                   bit_len ,
                                   analysis_type ,
                                   name_en ,
                                   bit_offset ,
                                   v_type ,
                                   trigger_value ,
                                   precision ,
                                   comment,
                                   valid_vid ,
                                   valid_value ,
                                   own_car,
                                   own_unit ,
                                   own_system ,
                                   name_cn,
                                   trigger_fault_id ,
                                   unit,
                                   data_type,
                                   default_value,
                                   max_value,
                                   min_value,
                                   frame_type_id ,
                                   monitor_flag ,
                                   by_passby_flag,
                                   structure_position,
                                   basic_value
            ) select
                  v_id ,
                  np_id ,
                  description,
                  field_position ,
                  byte_offset ,
                  bit_len ,
                  analysis_type ,
                  name_en ,
                  bit_offset ,
                  v_type ,
                  trigger_value ,
                  precision ,
                  comment,
                  valid_vid ,
                  valid_value ,
                  own_car,
                  own_unit ,
                  own_system ,
                  name_cn,
                  trigger_fault_id ,
                  unit,
                  data_type,
                  default_value,
                  max_value,
                  min_value,
                  nid ,
                  monitor_flag ,
                  by_passby_flag ,
                  structure_position,
                  basic_value from wtd_signal where p_id = op_id and frame_type_id = r.id;
        END LOOP;
    RETURN TRUE;

EXCEPTION WHEN OTHERS THEN RETURN FALSE;
--RAISE NOTICE EXCEPTION;
end;
$$;

alter function duplicate_protocol(integer) owner to phmdbadmin;

create or replace function duplicate_protocol_new(op_id integer) returns boolean
    language plpgsql
as
$$
DECLARE
    np_id INTEGER;
    nid INTEGER;
    sql varchar;
    r record;
BEGIN
    -- updated by Jin GuoYang on 2019.07.19
    -- set the version to original one
    insert into wtd_protocol(name,version ,flag_status,line_id,pro_id) select name|| '_copy', version ,0,line_id,pro_id from wtd_protocol where p_id = op_id RETURNING p_id into np_id;

    sql := 'select id,value,frametype_dic_id from wtd_protocol_frametype where p_id =  ' || op_id ||'';
    FOR r IN EXECUTE sql LOOP
            RAISE NOTICE 'value %',np_id;
            RAISE NOTICE 'value %',op_id;
            RAISE NOTICE 'value %',r.id;
            insert into wtd_protocol_frametype(p_id,value,frametype_dic_id) select np_id,r.value,r.frametype_dic_id from wtd_protocol_frametype where id = r.id and p_id = op_id RETURNING id into nid;

            INSERT INTO new_wtd_signal(
                protocol_id,
                name_cn,
                name_en,
                byte_offset,
                bit_offset,
                data_type,
                unit,
                modify_time,
                sub_system,
                car_location,
                parse_function,
                max_value,
                min_value,
                trigger_value,
                trigger_fault_id,
                frame_type_id,
                monitor_flag,
                bypass_trigger_value,
                description,
                remark
            ) select
                  np_id,
                  name_cn,
                  name_en,
                  byte_offset,
                  bit_offset,
                  data_type,
                  unit,
                  modify_time,
                  sub_system,
                  car_location,
                  parse_function,
                  max_value,
                  min_value,
                  trigger_value,
                  trigger_fault_id,
                  nid,
                  monitor_flag,
                  bypass_trigger_value,
                  description,
                  remark
            from new_wtd_signal where protocol_id = op_id and frame_type_id = r.id;

        END LOOP;
    RETURN TRUE;


EXCEPTION WHEN OTHERS THEN RETURN FALSE;
--RAISE NOTICE EXCEPTION;
end;
$$;

alter function duplicate_protocol_new(integer) owner to phmdbadmin;

create or replace function insert_signal_group() returns void
    language plpgsql
as
$$
declare
    faultTypeKey RECORD;
begin
    for faultTypeKey in (SELECT fault_type_key FROM "public"."ekb_fault_type" WHERE "vehicle_type_id" = '29' AND "name_cn" LIKE'%ED1CT_车门_Tc1车%' AND "name_cn" LIKE'%车门8%' AND "del_flag" = '0' ) loop
            INSERT INTO "ekb_fault_type_signal_group"("signal_group_id", "fault_type_key") VALUES (67, faultTypeKey.fault_type_key);
        end loop;
end;
$$;

alter function insert_signal_group() owner to phmdbadmin;

create or replace function original_signal_group() returns void
    language plpgsql
as
$$
declare
    faultType RECORD;
begin
    for faultType in (
        SELECT a.id,a.fault_type_key,a.start_time ,string_agg(E.original_signal_name_en,','),string_agg(b.name_cn,',') name_cn
        FROM mtc_auto_fault_record a
                 Left JOIN
             ekb_fault_type b on a.fault_type_key = b. fault_type_key
                 LEFT JOIN
             ekb_fault_type_signal_group C ON a.fault_type_key = c.fault_type_key
                 LEFT JOIN comm_signal_group D ON C.signal_group_id = D.id
                 LEFT JOIN comm_original_signal_group E ON E.group_id = D.ID
        GROUP BY A.ID
        HAVING string_agg(E.original_signal_name_en,',') IS NULL
        Order By a.id
    ) loop
            INSERT INTO "ekb_fault_type_signal_group"(signal_group_id,fault_type_key) VALUES(faultType.fault_type_key,faultType.name_cn);
        end loop;
end;
$$;

alter function original_signal_group() owner to phmdbadmin;

