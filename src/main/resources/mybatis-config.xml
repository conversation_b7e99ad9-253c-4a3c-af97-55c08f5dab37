<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <setting name="cacheEnabled" value="true"/>
        <setting name="logImpl" value="STDOUT_LOGGING"/>
    </settings>
    <plugins>
        <plugin interceptor="cc.crrc.manage.datapermission.PHMPageInterceptor">
            <property name="helperDialect" value="postgresql"/>
        </plugin>
        <plugin interceptor="cc.crrc.manage.datapermission.DataPermissionInterceptor">
        </plugin>
    </plugins>
</configuration>