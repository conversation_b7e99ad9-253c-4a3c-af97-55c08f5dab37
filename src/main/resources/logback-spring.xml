<configuration debug="true">
    <contextName>phm-admin</contextName>
    <property name="logPath"
              value="${log.file.path:-${user.home}/logs/phm}"/>
    <property name="logLevel" value="${log.level:-INFO}"/>
    <property name="maxFileSize" value="${file.maxSize:-10MB}"/>
    <property name="maxHistory" value="${maxhistory:-90}"/>
    <property name="encoderPattern"
              value="${encoder.pattern:-%d{yyyy-MM-dd HH:mm:ss.SSS} |- %-5level %logger{80} - %msg %n}"/>
    <property name="zipExt" value="%d{yyyy-MM-dd}.%i"/>
    <if condition='isDefined("dev")'>
        <then>
            <appender name="systemLogFile"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>${logPath}/system.log</file>
                <rollingPolicy
                        class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                    <fileNamePattern>${logPath}/system.log.${zipExt}.zip</fileNamePattern>
                    <maxHistory>${maxHistory}</maxHistory>
                    <TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                        <MaxFileSize>${maxFileSize}</MaxFileSize>
                    </TimeBasedFileNamingAndTriggeringPolicy>
                </rollingPolicy>
                <encoder>
                    <pattern>${encoderPattern}</pattern>
                </encoder>
            </appender>

            <appender name="sqlLogFile"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>${logPath}/sql.log</file>
                <rollingPolicy
                        class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                    <fileNamePattern>${logPath}/sql.log.${zipExt}.zip</fileNamePattern>
                    <maxHistory>${maxHistory}</maxHistory>
                    <TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                        <MaxFileSize>${maxFileSize}</MaxFileSize>
                    </TimeBasedFileNamingAndTriggeringPolicy>
                </rollingPolicy>
                <encoder>
                    <pattern>${encoderPattern}</pattern>
                </encoder>
            </appender>
            <appender name="serviceLogFile"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>${logPath}/service.log</file>
                <rollingPolicy
                        class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                    <fileNamePattern>${logPath}/service.log.${zipExt}.zip</fileNamePattern>
                    <maxHistory>${maxHistory}</maxHistory>
                    <TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                        <MaxFileSize>${maxFileSize}</MaxFileSize>
                    </TimeBasedFileNamingAndTriggeringPolicy>
                </rollingPolicy>
                <encoder>
                    <pattern>${encoderPattern}</pattern>
                </encoder>
            </appender>

            <appender name="dbLogFile"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>${logPath}/db.log</file>
                <rollingPolicy
                        class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                    <fileNamePattern>${logPath}/db.log.${zipExt}.zip</fileNamePattern>
                    <maxHistory>${maxHistory}</maxHistory>
                    <TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                        <MaxFileSize>${maxFileSize}</MaxFileSize>
                    </TimeBasedFileNamingAndTriggeringPolicy>
                </rollingPolicy>
                <encoder>
                    <pattern>${encoderPattern}</pattern>
                </encoder>
            </appender>

            <appender name="softwareVersionFile"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>${logPath}/softwareVersion.log</file>
                <rollingPolicy
                        class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                    <fileNamePattern>${logPath}/softwareVersion.log.${zipExt}.zip</fileNamePattern>
                    <maxHistory>${maxHistory}</maxHistory>
                    <TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                        <MaxFileSize>${maxFileSize}</MaxFileSize>
                    </TimeBasedFileNamingAndTriggeringPolicy>
                </rollingPolicy>
                <encoder>
                    <pattern>${encoderPattern}</pattern>
                </encoder>
            </appender>

            <appender name="faultFile"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>${logPath}/fault.log</file>
                <rollingPolicy
                        class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                    <fileNamePattern>${logPath}/fault.log.${zipExt}.zip</fileNamePattern>
                    <maxHistory>${maxHistory}</maxHistory>
                    <TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                        <MaxFileSize>${maxFileSize}</MaxFileSize>
                    </TimeBasedFileNamingAndTriggeringPolicy>
                </rollingPolicy>
                <encoder>
                    <pattern>${encoderPattern}</pattern>
                </encoder>
            </appender>
            <appender name="websocketLogFile"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>${logPath}/websocket.log</file>
                <rollingPolicy
                        class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                    <fileNamePattern>${logPath}/websocket.log.${zipExt}.zip</fileNamePattern>
                    <maxHistory>${maxHistory}</maxHistory>
                    <TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                        <MaxFileSize>${maxFileSize}</MaxFileSize>
                    </TimeBasedFileNamingAndTriggeringPolicy>
                </rollingPolicy>
                <encoder>
                    <pattern>${encoderPattern}</pattern>
                </encoder>
            </appender>

            <logger name="cc.crrc.manage.mapper" additivity="false" level="DEBUG">
                <appender-ref ref="sqlLogFile"/>
            </logger>
            <!--kakfa消费日志定位到类上-->
            <logger name="software.version.update" additivity="false" level="INFO">
                <appender-ref ref="softwareVersionFile"/>
            </logger>
            <logger name="cc.crrc.manage.mq.process.KafkaAutoFaultConsumer" additivity="false" level="INFO">
                <appender-ref ref="faultFile"/>
            </logger>
            <!--websocket日志单独打印-->
            <logger name="cc.crrc.manage.websocket" additivity="false" level="DEBUG">
                <appender-ref ref="websocketLogFile"/>
            </logger>
            <logger name="cc.crrc.manage" additivity="false" level="${logLevel}">
                <appender-ref ref="serviceLogFile"/>
            </logger>
            <logger name="liquibase" additivity="false" level="INFO">
                <appender-ref ref="dbLogFile"/>
            </logger>
            <root level="INFO">
                <appender-ref ref="systemLogFile"/>
            </root>
        </then>
        <else>
            <appender name="console"
                      class="ch.qos.logback.core.ConsoleAppender">
                <encoder>
                    <pattern>${encoderPattern}
                    </pattern>
                </encoder>
            </appender>
            <logger name="cc.crrc.manage.mapper" additivity="false" level="DEBUG">
                <appender-ref ref="console"/>
            </logger>
            <logger name="org.activiti" additivity="false" level="DEBUG">
                <appender-ref ref="console"/>
            </logger>
            <logger name="liquibase" additivity="false" level="INFO">
                <appender-ref ref="console"/>
            </logger>
            <root level="INFO">
                <appender-ref ref="console"/>
            </root>
        </else>
    </if>
</configuration>